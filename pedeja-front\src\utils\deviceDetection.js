/**
 * Utilitários para detecção de dispositivos e navegadores
 */

/**
 * Detecta se o dispositivo é iOS (iPhone, iPad, iPod)
 * @returns {boolean} true se for iOS, false caso contrário
 */
export const isIOS = () => {
  // Verifica se estamos no navegador
  if (typeof window === 'undefined' || !window.navigator) {
    return false;
  }

  const userAgent = window.navigator.userAgent;
  const platform = window.navigator.platform;

  // Detecta iOS através do userAgent
  const isIOSUserAgent = /iPad|iPhone|iPod/.test(userAgent);
  
  // Detecta iOS através da platform (mais confiável)
  const isIOSPlatform = /iPad|iPhone|iPod/.test(platform);
  
  // Detecta iOS 13+ no iPad (que pode reportar como Mac)
  const isIPadOS = platform === 'MacIntel' && window.navigator.maxTouchPoints > 1;
  
  return isIOSUserAgent || isIOSPlatform || isIPadOS;
};

/**
 * Detecta se o navegador é Safari
 * @returns {boolean} true se for Safari, false caso contrário
 */
export const isSafari = () => {
  if (typeof window === 'undefined' || !window.navigator) {
    return false;
  }

  const userAgent = window.navigator.userAgent;
  
  // Safari tem 'Safari' no userAgent mas não 'Chrome' nem 'Chromium'
  return /Safari/.test(userAgent) && !/Chrome|Chromium/.test(userAgent);
};

/**
 * Detecta se é um dispositivo móvel iOS
 * @returns {boolean} true se for iPhone ou iPod, false caso contrário
 */
export const isIOSMobile = () => {
  if (typeof window === 'undefined' || !window.navigator) {
    return false;
  }

  const userAgent = window.navigator.userAgent;
  return /iPhone|iPod/.test(userAgent);
};

/**
 * Detecta se é um tablet iOS (iPad)
 * @returns {boolean} true se for iPad, false caso contrário
 */
export const isIOSTablet = () => {
  if (typeof window === 'undefined' || !window.navigator) {
    return false;
  }

  const userAgent = window.navigator.userAgent;
  const platform = window.navigator.platform;
  
  // iPad tradicional
  const isIPadUserAgent = /iPad/.test(userAgent);
  
  // iPad com iOS 13+ (reporta como Mac)
  const isIPadOS = platform === 'MacIntel' && window.navigator.maxTouchPoints > 1;
  
  return isIPadUserAgent || isIPadOS;
};

/**
 * Detecta se o dispositivo suporta notificações push de forma confiável
 * @returns {boolean} true se suporta notificações de forma confiável, false caso contrário
 */
export const supportsReliableNotifications = () => {
  // Verifica se a API de notificações está disponível
  if (!('Notification' in window)) {
    return false;
  }

  // iOS tem suporte limitado e problemático para notificações web
  // Especialmente no Safari, as notificações podem não funcionar corretamente
  if (isIOS()) {
    return false;
  }

  return true;
};

/**
 * Detecta se é um dispositivo móvel (baseado na largura da tela)
 * @param {number} breakpoint - Largura máxima para considerar móvel (padrão: 768px)
 * @returns {boolean} true se for móvel, false caso contrário
 */
export const isMobileScreen = (breakpoint = 768) => {
  if (typeof window === 'undefined') {
    return false;
  }

  return window.innerWidth <= breakpoint;
};

/**
 * Detecta se é um dispositivo móvel (combinando detecção de tela e userAgent)
 * @returns {boolean} true se for móvel, false caso contrário
 */
export const isMobileDevice = () => {
  if (typeof window === 'undefined' || !window.navigator) {
    return false;
  }

  const userAgent = window.navigator.userAgent;
  
  // Detecta através do userAgent
  const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  
  // Detecta através da largura da tela
  const isMobileScreen = window.innerWidth <= 768;
  
  return isMobileUserAgent || isMobileScreen;
};

/**
 * Obtém informações detalhadas sobre o dispositivo
 * @returns {object} Objeto com informações do dispositivo
 */
export const getDeviceInfo = () => {
  return {
    isIOS: isIOS(),
    isSafari: isSafari(),
    isIOSMobile: isIOSMobile(),
    isIOSTablet: isIOSTablet(),
    isMobileDevice: isMobileDevice(),
    isMobileScreen: isMobileScreen(),
    supportsReliableNotifications: supportsReliableNotifications(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : '',
    platform: typeof window !== 'undefined' ? window.navigator.platform : '',
    screenWidth: typeof window !== 'undefined' ? window.innerWidth : 0,
    screenHeight: typeof window !== 'undefined' ? window.innerHeight : 0,
  };
};
