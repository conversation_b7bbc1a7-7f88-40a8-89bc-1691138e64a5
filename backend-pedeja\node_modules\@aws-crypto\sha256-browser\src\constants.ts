export const SHA_256_HASH: { name: "SHA-256" } = { name: "SHA-256" };

export const SHA_256_HMAC_ALGO: { name: "<PERSON><PERSON>"; hash: { name: "SHA-256" } } = {
  name: "HM<PERSON>",
  hash: SHA_256_HASH
};

export const EMPTY_DATA_SHA_256 = new Uint8Array([
  227,
  176,
  196,
  66,
  152,
  252,
  28,
  20,
  154,
  251,
  244,
  200,
  153,
  111,
  185,
  36,
  39,
  174,
  65,
  228,
  100,
  155,
  147,
  76,
  164,
  149,
  153,
  27,
  120,
  82,
  184,
  85
]);
