import { AppConfigContext } from "antd/es/app/context";
import axios from "axios";
import { toast } from 'react-toastify';

const isDevelopment = window.location.hostname === "localhost" ? true : false;
const apiUrl = isDevelopment
  ? process.env.REACT_APP_SERVER_URL_DEV
  : process.env.REACT_APP_SERVER_URL_PROD;
const apiInstancesWppUrl = process.env.REACT_APP_API_INSTANCES_WPP_URL;
export const api = axios.create({
  baseURL: apiUrl,
});

const apiInstancesWpp = axios.create({
  baseURL: apiInstancesWppUrl,
  headers: {
    "Content-Type": "application/json",
  },
});

// Função para configurar o token de autorização
const setAuthToken = (token) => {
  if (token) {
    // Aplica o token de autorização a todas as requisições se o token estiver presente
    api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  } else {
    // Remove o header de autorização se o token não estiver presente
    delete api.defaults.headers.common["Authorization"];
  }
};

export const createSession = async (email, password) => {
  return api.post("/auth/login", { email, password });
};

export const checkLicense = (userId) => {
  return api.get(`/check-license/${userId}`);
};

export const getBotStatusLead = (leadId) => {
  return api.get(`/auth/bot-status-lead/${leadId}`);
};


export const getUsers = async (userID) => {
  return api.get("/list-users/" + userID);
};

export const getUsersByRole = async (role) => {
  return api.get(`/list-users-by-role/${role}`);
};

export const getEntregadores = async (id_empresa) => {
  return api.get("/list-entregadores/" + id_empresa);
};

export const listarCaixas = async (id, page, startDate, endDate) => {
  return api.get("/list-caixas/" + id, {
    params: {
      page,
      per_page: 10,
      startDate,
      endDate,
    },
  });
};

export const getUserRole = async (userID) => {
  return api.get("/user-role/" + userID);
};

export const getPlansAsaas = async (userID) => {
  return api.get("/list-plans/" + userID);
};

export const getFilPlansIugu = async (userID) => {
  return api.get("/list-fil-plans/" + userID);
};


export const getUser = async (userID) => {
  return api.get("/user/" + userID);
};

export const getEmpresaWithObjId = async (empresaObjId) => {
  return api.get("/empresaWithObjId/" + empresaObjId);
};

export const getEmpresa = async (userID, id_empresa) => {
  return api.post("/get-empresa/" + userID, { id_empresa });
};

export const getEmpresasAdmin = async (userID, page, limit) => {
  return api.get(`/get-empresas-admin/${userID}?page=${page}&limit=${limit}`);
};

export const createInstanceAdmin = async (userID, empresaId) => {
  return api.post(`/createInstance-admin/${userID}/${empresaId}`);
};

export const getCaixaById = async (userID, id_caixa) => {
  return api.post("/get-caixa/" + userID, { id_caixa });
};

export const testeImpressao = async (companyId) => {
  return api.post("/print-test", { companyId });
}

export const getPedidos = async (userID, vinculo_empresa) => {
  return api.post("/get-pedidos/" + userID, { vinculo_empresa });
};

export const getPedido = async (
  userID,
  id_empresa,
  vinculo_empresa,
  id_pedido
) => {
  try {
    return await api.post("/get-pedido/" + userID, {
      id_empresa,
      vinculo_empresa,
      id_pedido,
    });
  } catch (error) {
    console.log("Erro ao buscar pedido", error);
    throw error;
  }
};

export const getCategorias = async (
  userID,
  id_empresa,
  vinculo_empresa,
  empresaObjId
) => {
  return api.post("/get-categorias/" + userID, {
    id_empresa,
    vinculo_empresa,
    empresaObjId,
  });
};

export const getAdicionais = async (
  userID,
  id_empresa,
  vinculo_empresa,
  empresaObjId
) => {
  return api.post("/get-adicionais/" + userID, {
    id_empresa,
    vinculo_empresa,
    empresaObjId,
  });
};

export const getAdicionaisCardapio = async (id_empresa, nomeEmpresa) => {
  return api.post("/get-adicionais-cardapio/", { id_empresa, nomeEmpresa });
};

export const getCategoriasCardapio = async (id_empresa, nomeEmpresa) => {
  return api.post("/get-categorias-cardapio/", { id_empresa, nomeEmpresa });
};

export const getEmpresaInfo = async (id_empresa, nomeEmpresa) => {
  return api.post("/get-empresa-info", { id_empresa, nomeEmpresa });
};

export const getItens = async (
  userID,
  id_empresa,
  vinculo_empresa,
  empresaObjId
) => {
  return api.post("/get-itens/" + userID, {
    id_empresa,
    vinculo_empresa,
    empresaObjId,
  });
};

export const getItensCardapio = async (id_empresa, nomeEmpresa) => {
  return api.post("/get-itens-cardapio/", { id_empresa, nomeEmpresa });
};

export const getItensCardapioSalao = async (id_empresa, nomeEmpresa) => {
  return api.post("/get-itens-cardapio-salao/", { id_empresa, nomeEmpresa });
};

export const getItem = async (
  userID,
  id_empresa,
  vinculo_empresa,
  empresaObjId,
  itemObjId
) => {
  return api.post("/get-item/" + userID, {
    id_empresa,
    vinculo_empresa,
    empresaObjId,
    itemObjId,
  });
};

export const getItemCardapio = async (id_empresa, itemObjId) => {
  return api.post("/get-item-cardapio/", { id_empresa, itemObjId });
};

export const getPedidosByStatus = async (
  userID,
  id_empresa,
  vinculo_empresa,
  status_pedido,
  forReport
) => {
  return api.post("/get-pedidosStatus/" + userID, {
    id_empresa,
    vinculo_empresa,
    status_pedido,
    forReport
  });
};

export const getPedidosByStatusSimples = async (
  userID,
  id_empresa,
  vinculo_empresa,
  status_pedido,
  forReport
) => {
  return api.post("/get-pedidosStatusSimples/" + userID, {
    id_empresa,
    vinculo_empresa,
    status_pedido,
    forReport
  });
};

export const getPedidosByStatusFinalizados = async (
  userID,
  id_empresa,
  vinculo_empresa,
  forReport
) => {
  return api.post("/get-pedidosStatusFinalizados/" + userID, {
    id_empresa,
    vinculo_empresa,
    forReport
  });
};

export const getPedidosFinalizadosHistorico = async (
  userID,
  id_empresa,
  vinculo_empresa,
  status_pedido,
  periodo
) => {
  return api.post("/get-pedidosFinalizadosHistorico/" + userID, {
    id_empresa,
    vinculo_empresa,
    status_pedido,
    periodo,
  });
};

export const getPedidosFinalizadosPeriodo = async (
  userID,
  id_empresa,
  vinculo_empresa,
  status_pedido,
  startDate,
  endDate
) => {
  return api.post("/get-pedidosFinalizadosPeriodo/" + userID, {
    id_empresa,
    vinculo_empresa,
    status_pedido,
    startDate,
    endDate,
  });
};

export const getPedidosToPrint = async (
  userID,
  id_empresa,
  vinculo_empresa
) => {
  return api.post("/get-pedidos-toprint/" + userID, {
    id_empresa,
    vinculo_empresa,
  });
};

export const getCliente = async (userID, id_empresa, telefone) => {
  return api.post("/get-cliente/" + userID, { id_empresa, telefone });
};

export const getVendedor = async (userID, id_vendedor) => {
  return api.post("/get-vendedor/" + userID, { id_vendedor });
};

export const getOrcamento = async (userID, id_orcamento) => {
  return api.post("/get-orcamento/" + userID, { id_orcamento });
};

export const getVinculoEmpresa = async (userID) => {
  return api.get("/vinculo-empresa/" + userID);
};

export const getEmpresas = async (userID) => {
  return api.get("/list-empresas/" + userID);
};

export const getRevendas = async (userID) => {
  return api.get("/list-revendas/" + userID);
};

export const getClientes = async (userID) => {
  return api.get("/list-clientes/" + userID);
};

export const getClientesAtivos = async (empresaObjectId, startDate, endDate) => {
  return api.get(`/clientes-ativos/${empresaObjectId}`, {
    params: {
      startDate: startDate.toISOString(), // Converte para formato UTC
      endDate: endDate.toISOString()
    }
  });
};


export const getCompanyResponses = async (companyId) => {
  return api.get("/company-responses/" + companyId);
};

export const getVendedores = async (userID) => {
  return api.get("/list-vendedores/" + userID);
};

export const getOrcamentos = async (userID) => {
  return api.get("/list-orcamentos/" + userID);
};

export const updateUser = async (
  id,
  userID,
  email,
  password,
  name
) => {
  return api.post("/update-user/" + userID, {
    id,
    email,
    password,
    name
  });
};

export const updateEntregador = async (
  idToEdit,
  userID,
  id_empresa,
  name,
  veiculo,
  telefone,
  placa
) => {
  return api.post("/update-entregador/" + userID, {
    idToEdit,
    id_empresa,
    name,
    veiculo,
    telefone,
    placa,
  });
};

export const updateTempoEntregaEmpresa = async (
  userID,
  _id,
  id,
  tempoBalcaoMin,
  tempoBalcaoMax,
  tempoEntregaMin,
  tempoEntregaMax,
  tipo_impressao
) => {
  return api.post("/update-empresaTempoEntrega/" + userID, {
    _id,
    id,
    tempoBalcaoMin,
    tempoBalcaoMax,
    tempoEntregaMin,
    tempoEntregaMax,
    tipo_impressao,
  });
};

export const updateTempoHorarioFuncionamento = async (
  userID,
  _id,
  id,
  status_loja,
  horario_funcionamento,
  timezone
) => {
  return api.post("/update-empresaHorarioFuncionamento/" + userID, {
    _id,
    id,
    status_loja,
    horario_funcionamento,
    timezone,
  });
};

export const updateTypeRegion = async (
  _id,
  id,
  userID,
  region_type_delivery
) => {
  return api.post("/update-empresa/" + userID, {
    _id: _id,
    id: id,
    region_type_delivery: region_type_delivery,
    type_of_region: region_type_delivery,
  });
};

export const updateClienteAddress = async (
  id_empresa,
  telefone,
  endereco,
  enderecoToEdit
) => {
  return api.post("/update-endereco-cliente/", {
    id_empresa,
    telefone,
    endereco,
    enderecoToEdit,
  });
};

export const updateRaioEntregaEmpresa = async (
  _id,
  id,
  userID,
  raio_entrega,
  valor_entrega
) => {
  return api.post("/update-raioEntrega/" + userID, {
    _id: _id,
    id: id,
    raio_entrega: raio_entrega,
    valor_entrega: valor_entrega,
  });
};

export const updateBairroEntregaEmpresa = async (
  _id,
  id,
  userID,
  bairro_entrega,
  valor_entrega
) => {
  return api.post("/update-bairroEntrega/" + userID, {
    _id: _id,
    id: id,
    bairro_entrega: bairro_entrega,
    valor_entrega: valor_entrega,
  });
};

export const updateCategoriasOrder = async (
  userID,
  _id,
  id_categoria,
  id_empresa,
  order
) => {
  return api.post("/update-categoria/" + userID, {
    _id: _id,
    id_categoria: id_categoria,
    id_empresa: id_empresa,
    order: order,
  });
};

export const updateItensOrder = async (
  userID,
  _id,
  id_item,
  id_empresa,
  order
) => {
  return api.post("/update-item/" + userID, {
    _id: _id,
    id_item: id_item,
    id_empresa: id_empresa,
    order: order,
  });
};

export const updateItemOut = async (userID, _id, id_item, id_empresa, out) => {
  return api.post("/update-item/" + userID, {
    _id: _id,
    id_item: id_item,
    id_empresa: id_empresa,
    out: out,
  });
};

export const updateItemOutSalao = async (userID, _id, id_item, id_empresa, out) => {
  return api.post("/update-item/" + userID, {
    _id: _id,
    id_item: id_item,
    id_empresa: id_empresa,
    out_salao: out,
  });
};

export const updateItemAdicionalOut = async (userID, idGrupo, itemID, titulo, price, id_adicional, id_empresa, out) => {
  return api.post("/auth/updateAdicional/" + userID, {
    _id: itemID,
    id_grupo: idGrupo,
    title: titulo,
    price: price,
    id_adicional: id_adicional,
    id_empresa: id_empresa,
    out: out,
  });
};

export const updateItemAdicionalOutSalao = async (userID, idGrupo, itemID, titulo, price, id_adicional, id_empresa, out) => {
  return api.post("/auth/updateAdicional/" + userID, {
    _id: itemID,
    id_grupo: idGrupo,
    title: titulo,
    price: price,
    id_adicional: id_adicional,
    id_empresa: id_empresa,
    out_salao: out,
  });
};

export const updateItemPrice = async (
  userID,
  _id,
  id_item,
  id_empresa,
  price
) => {
  return api.post("/update-item/" + userID, {
    _id: _id,
    id_item: id_item,
    id_empresa: id_empresa,
    price: price,
  });
};

export const updateItemPriceSalao = async (
  userID,
  _id,
  id_item,
  id_empresa,
  price
) => {
  return api.post("/update-item/" + userID, {
    _id: _id,
    id_item: id_item,
    id_empresa: id_empresa,
    price_salao: price,
  });
};

export const updateCategorias = async (
  userID,
  _id,
  id_categoria,
  id_empresa,
  modelo,
  title,
  disponibilidade,
  dia_horario_disponibilidade
) => {
  return api.post("/update-categoria/" + userID, {
    _id: _id,
    id_categoria: id_categoria,
    id_empresa: id_empresa,
    modelo: modelo,
    title: title,
    disponibilidade: disponibilidade,
    dia_horario_disponibilidade: dia_horario_disponibilidade,
  });
};

export const updateAdicionaisGroup = async ({
  userID,
  adicional_objId,
  idEmpresa,
  title,
  minimo,
  maximo,
  mandatory,
  calcularMaiorValor,
  calcularMedia,
  precificacao,
  type
}) => {
  return api.post("/update-grupo-adicionais/" + userID, {
    _id: adicional_objId,
    id_empresa: idEmpresa,
    title: title,
    min: minimo,
    max: maximo,
    mandatory: mandatory,
    calcular_maior_valor: calcularMaiorValor,
    calcular_media: calcularMedia,
    precificacao: precificacao,
    type: type
  });
};

export const updateItens = async (
  userID,
  itemObjId,
  id_item,
  idEmpresa,
  orderItem,
  category_item_id,
  category_item_title,
  title,
  description,
  out,
  images,
  price,
  has_adicional,
  adicionais,
  type
) => {
  return api.post("/update-item/" + userID, {
    _id: itemObjId,
    id_item: id_item,
    id_empresa: idEmpresa,
    order: orderItem,
    category_item_id: category_item_id,
    category_item_title: category_item_title,
    title: title,
    description: description,
    out: out,
    images: images,
    price: price,
    has_adicional: has_adicional,
    adicionais: adicionais,
    type: type,
  });
};

export const removeAdicionalFromItem = async (
  userID,
  itemId,
  grupoAdicionalValue
) => {
  return api.post("/remove-grupoAdicionaisFromItem/" + userID, {
    itemId,
    grupoAdicionalValue
  });
};

export const updateStatusPedido = async (
  userID,
  _id,
  id_pedido,
  status_pedido
) => {
  return api.post("/update-pedido/" + userID, {
    _id: _id,
    id_pedido: id_pedido,
    status_pedido: status_pedido,
    //"status_print":status_print,
  });
};

export const updatePedidoWithEntregador = async (
  userID,
  _id,
  id_pedido,
  entregador
) => {
  return axios.post(`${apiUrl}/update-pedido/${userID}`, {
    _id: _id,
    id_pedido: id_pedido,
    entregador: entregador,
  });
};

export const apiUpdateImportFlag = async (
  idEmpresa,
  importacao_finalizada
) => {
  return api.post(`/update-import-flag/${idEmpresa}`, {
    importacao_finalizada
  });
};

export const ImprimirPedido = async (userID, dadosPedido) => {
  return api.post("/command-print/" + userID, {
    dadosPedido,
  });
};

export const updateStatusPedidoFinalizado = async (
  userID,
  _id,
  id_pedido,
  status_pedido,
  finalizadoAt
) => {
  return api.post("/update-pedido/" + userID, {
    _id: _id,
    id_pedido: id_pedido,
    status_pedido: status_pedido,
    //"status_print":status_print,
    finalizadoAt: finalizadoAt,
  });
};

export const updateStatusPrint = async (userID, _id, id_pedido) => {
  //console.log("caiuNoUpdateStatusPrint>", id_pedido);
  return api.post("/update-pedido/" + userID, {
    _id: _id,
    id_pedido: id_pedido,
    status_pedido: "2",
    status_print: false,
  });
};

export const fecharCaixa = async (
  id_empresa,
  objIdCaixa,
  closedBy,
  saldo_final,
  valor_informado_dinheiro,
  valor_informado_cartao,
  valor_informado_pix
) => {
  return api.post("/closeCaixa/", {
    id_empresa,
    objIdCaixa,
    closedBy,
    saldo_final,
    valor_informado_dinheiro,
    valor_informado_cartao,
    valor_informado_pix,
  });
};
export const addLancamentoCaixa = async (
  id_empresa,
  objIdCaixa,
  descricao,
  tipo_lancamento,
  valor,
  createdBy
) => {
  return api.post("/addLancamentoCaixa/", {
    id_empresa,
    objIdCaixa,
    descricao,
    tipo_lancamento,
    valor,
    createdBy,
  });
};

export const updateEmpresa = async (
  id,
  userID,
  cnpj,
  name,
  razao,
  email,
  cep,
  estado,
  municipio,
  bairro,
  complemento,
  telefone,
  celular
) => {
  console.log("caiu no updateEmpresa Normal");
  return api.post("/update-empresa/" + userID, {
    id,
    cnpj,
    name,
    razao,
    email,
    cep,
    estado,
    municipio,
    bairro,
    complemento,
    telefone,
    celular,
  });
};

export const updateEmpresaAddress = async (
  _id,
  id,
  userID,
  cep,
  estado,
  municipio,
  bairro,
  address_number,
  complemento,
  latitude,
  longitude
) => {
  return api.post("/update-empresa/" + userID, {
    _id,
    id,
    cep,
    estado,
    municipio,
    bairro,
    address_number,
    complemento,
    latitude,
    longitude
  });
};

export const updateCliente = async (
  id,
  userID,
  documento,
  name,
  razao,
  contato,
  email,
  cep,
  estado,
  municipio,
  bairro,
  complemento,
  telefone,
  celular,
  type
) => {
  if (type === "F") {
    return api.post("/update-cliente/" + userID, {
      id,
      documento,
      name,
      contato,
      email,
      cep,
      estado,
      municipio,
      bairro,
      complemento,
      telefone,
      celular,
    });
  } else {
    return api.post("/update-cliente/" + userID, {
      id,
      documento,
      name,
      razao,
      email,
      cep,
      estado,
      municipio,
      bairro,
      complemento,
      telefone,
      celular,
    });
  }
};

export const updateVendedor = async (
  id,
  userID,
  documento,
  name,
  cep,
  estado,
  municipio,
  bairro,
  complemento,
  telefone,
  celular
) => {
  return api.post("/update-vendedor/" + userID, {
    id,
    documento,
    name,
    cep,
    estado,
    municipio,
    bairro,
    complemento,
    telefone,
    celular,
  });
};

export const updateOrcamento = async (
  id,
  userID,
  codigo_cliente,
  nome_cliente,
  codigo_vendedor,
  nome_vendedor,
  total_orc,
  status_orc,
  id_grupo,
  vinculo_empresa,
  items
) => {
  return api.post("/update-orcamento/" + userID, {
    id,
    codigo_cliente,
    nome_cliente,
    codigo_vendedor,
    nome_vendedor,
    total_orc,
    status_orc,
    id_grupo,
    vinculo_empresa,
    items,
  });
};

export const updateStatus = async (id_empresa, userID, status) => {
  return api.post("/update-status/" + userID, { id_empresa, status });
};

export const updateUserImg = async (userID, image) => {
  return api.post("/update-user-img/" + userID, { image });
};

export const deleteUser = async (id, userID) => {
  return api.post("/delete-user/" + userID, { id });
};

export const deleteEntregador = async (id, userID) => {
  return api.post("/delete-entregador/" + userID, { id });
};

export const cancelarPedido = async (id, userID, empresaId, senha) => {
  return api.post("/cancelar-pedido/" + userID, { id, empresaId, senha });
};

export const updateEmpresaCancelamentoPedido = async (payload) => {
  return api.post("/configurar-senha-cancelamento", payload);
};

export const deleteEmpresa = async (userID, id) => {
  return api.post("/delete-empresa/" + userID, { id });
};

export const deleteEmpresaCompleta = async (empresaObjId, usuario, senha) => {
  return api.delete("/delete-empresa-completa/" + empresaObjId, { 
    data: { usuario, senha } 
  });
};

export const deleteCategoria = async (userID, id) => {
  return api.post("/delete-categoria/" + userID, { id });
};

export const deleteGrupoAdicional = async (userID, id) => {
  return api.post("/delete-grupo-adicional/" + userID, { id });
};

export const deleteItem = async (userID, id) => {
  return api.post("/delete-item/" + userID, { id });
};

export const deleteEnderecoCliente = async (
  id_empresa,
  telefone,
  enderecoIdToDelete
) => {
  return api.post("/delete-endereco-cliente/", {
    id_empresa,
    telefone,
    enderecoIdToDelete,
  });
};

export const deleteCliente = async (id, userID) => {
  return api.post("/delete-cliente/" + userID, { id });
};

export const deleteVendedor = async (id, userID) => {
  return api.post("/delete-vendedor/" + userID, { id });
};

export const deleteOrcamento = async (id, userID) => {
  return api.post("/delete-orcamento/" + userID, { id });
};

export const deleteRaioEntrega = async (
  userID,
  id_empresa,
  id_raio_entrega
) => {
  return api.post("/delete-raioEntrega/" + userID, {
    id_empresa,
    id_raio_entrega,
  });
};

export const deleteBairroEntrega = async (
  userID,
  id_empresa,
  id_bairro_entrega
) => {
  return api.post("/delete-bairroEntrega/" + userID, {
    id_empresa,
    id_bairro_entrega,
  });
};

export const register = async (
  createdBy,
  name,
  email,
  password,
  confirmpassword,
  vinculo_empresa,
  role
) => {
  return api.post("/auth/register", {
    createdBy,
    name,
    email,
    password,
    confirmpassword,
    vinculo_empresa,
    role,
  });
};

export const registerGarcom = async (
  name,
  email,
  password,
  number
) => {
  return api.post("/users/garcom", {
    name,
    email,
    password,
    number
  });
};

export const registerEntregador = async (
  createdBy,
  id_empresa,
  name,
  telefone,
  veiculo,
  placa
) => {
  return api.post("/registerEntregador", {
    createdBy,
    id_empresa,
    name,
    telefone,
    veiculo,
    placa,
  });
};

export const registerEmpresa = async (
  createdBy,
  cnpj,
  name,
  razao,
  email,
  cep,
  estado,
  municipio,
  bairro,
  complemento,
  telefone,
  celular,
  id_grupo,
  type
) => {
  return api.post("/auth/registerEmpresa", {
    createdBy,
    cnpj,
    name,
    razao,
    email,
    cep,
    estado,
    municipio,
    bairro,
    complemento,
    telefone,
    celular,
    id_grupo,
    type,
  });
};

export const createEmpresaUser = async (
  cnpj,
  nome_empresa,
  email,
  telefone,
  type,
  nome_pessoa,
  password,
  confirmpassword,
  vinculo_empresa
) => {
  return api.post("/createEmpresaUser", {
    cnpj,
    nome_empresa,
    email,
    telefone,
    type,
    nome_pessoa,
    password,
    confirmpassword,
    vinculo_empresa,
  });
};

// Função para adicionar endereço à empresa
export const addEnderecoEmpresa = async (empresaId, enderecoData) => {
  return api.put(`/addEnderecoEmpresa/${empresaId}`, enderecoData);
};

// Função para criar cliente Asaas
export const createAsaasCustomer = async (empresaId, dadosEndereco) => {
  return api.post(`/createAsaasCustomer/${empresaId}`, dadosEndereco);
};

export const registerPedido = async (
  createdBy,
  id_empresa,
  itens,
  celular_cliente,
  nome_cliente,
  tipo_pagamento,
  entrega,
  desconto,
  cpf_cnpj,
  valor_troco,
  valor_total,
  descricao,
  external_id
) => {
  return api.post("/auth/registerPedido", {
    createdBy,
    id_empresa,
    itens,
    celular_cliente,
    nome_cliente,
    tipo_pagamento,
    entrega,
    desconto,
    cpf_cnpj,
    valor_troco,
    valor_total,
    descricao,
    external_id
  });
};

export const toggleBotStatus = async (leadId) => {
  return api.put(`/auth/toggle-bot/${leadId}`);
};

// Função para buscar e salvar imagem de perfil do WhatsApp
export const getWhatsappProfilePicture = async (leadChannelID, token) => {
  setAuthToken(token);
  return api.post(`/api/v1/whatsapp/get-profile-picture/${leadChannelID}`);
};

// Função para buscar imagem de perfil do próprio usuário (dono da instância)
export const getMyWhatsappProfilePicture = async (empresaID, token) => {
  setAuthToken(token);
  return api.get(`/api/v1/whatsapp/get-my-profile-picture/${empresaID}`);
};

// Função para forçar busca e salvamento do JID
export const fetchWhatsappJID = async (empresaID, token) => {
  setAuthToken(token);
  return api.post(`/api/v1/whatsapp/fetch-jid/${empresaID}`);
};

export const updateFormasPagamento = async (empresaId, formas_pagamento) => {
  return api.put(`/empresa/${empresaId}/formas-pagamento`, { formas_pagamento });
};

export const updateConfiguracoesEntrega = async (empresaId, entrega_disabled, retirada_disabled) => {
  return api.put(`/empresa/${empresaId}/configuracoes-entrega`, { entrega_disabled, retirada_disabled });
};

export const updatePedidoFromPdv = async (
  objIdPedido,
  id_empresa,
  itens,
  celular_cliente,
  nome_cliente,
  tipo_pagamento,
  entrega,
  desconto,
  cpf_cnpj,
  valor_troco,
  valor_total,
  descricao,
  external_id
) => {
  return api.put("/auth/updatePedidoFromPdv", {
    objIdPedido,
    id_empresa,
    itens,
    celular_cliente,
    nome_cliente,
    tipo_pagamento,
    entrega,
    desconto,
    cpf_cnpj,
    valor_troco,
    valor_total,
    descricao,
    external_id
  });
};

export const updatePedidoFromMesas = async (
  objIdPedido,
  id_empresa,
  itens,
  celular_cliente,
  nome_cliente,
  valor_total,
  descricao,
  external_id
) => {
  return api.put("/auth/updatePedidoFromMesas", {
    objIdPedido,
    id_empresa,
    itens,
    celular_cliente,
    nome_cliente,
    valor_total,
    descricao,
    external_id
  });
};

export const registerCategoria = async (
  createdBy,
  id_empresa,
  title,
  disponibilidade,
  dia_horario_disponibilidade,
  only_pdv,
  only_qrcode,
  only_delivery_take_local,
  modelo
) => {
  return api.post("/auth/registerCategoria", {
    createdBy,
    id_empresa,
    title,
    disponibilidade,
    dia_horario_disponibilidade,
    only_pdv,
    only_qrcode,
    only_delivery_take_local,
    modelo
  });
};

export const registerItem = async (
  createdBy,
  id_empresa,
  category_item_id,
  category_item_title,
  title,
  description,
  out,
  image,
  price,
  disponibilidade,
  type
) => {
  return api.post("/auth/registerItem", {
    createdBy,
    id_empresa,
    category_item_id,
    category_item_title,
    title,
    description,
    out,
    image,
    price,
    disponibilidade,
    type
  });
};

export const registerGrupoAdicionais = async (
  createdBy,
  id_empresa,
  title,
  min,
  max,
  mandatory,
  out,
  calcular_maior_valor,
  calcular_media,
  precificacao,
  type
) => {
  return api.post("/auth/registerGrupoAdicionais", {
    createdBy,
    id_empresa,
    title,
    min,
    max,
    mandatory,
    out,
    calcular_maior_valor,
    calcular_media,
    precificacao,
    type
  });
};

export const registerAdicionais = async (
  createdBy,
  id_grupo,
  id_empresa,
  title,
  price,
  out,
  image
) => {
  return api.post("/auth/registerAdicionais/" + createdBy, {
    createdBy,
    id_grupo,
    id_empresa,
    title,
    price,
    out,
    image,
  });
};

export const updateItemAdicional = async (data) => {
  return api.post("/auth/updateAdicional/" + data.userID, data);
};

export const deleteItemAdicional = async (data) => {
  return api.post("/auth/deleteSubadicional/" + data.userID, data);
};

export const registerCaixaAndOpen = async (
  createdBy,
  id_empresa,
  saldo_inicial
) => {
  return api.post("/registerCaixaAndOpen/", {
    createdBy,
    id_empresa,
    saldo_inicial,
  });
};

export const registerPlanoAdmin = async (
  createdBy,
  nome,
  plan_identifier,
  order,
  plan_cycle,
  maxPayments,
  valor_plano
) => {
  return api.post("/auth/registerPlanoAdmin", {
    createdBy,
    nome,
    plan_identifier,
    order,
    plan_cycle,
    maxPayments,
    valor_plano
  });
};

export const searchClienteAsaasAndGetInvoice = async (
  createdBy,
  email,
  name,
  cpf_cnpj,
  zip_code,
  number,
  plan_identifier,
  payment_type,
  isPromotional
) => {
  return api.post("/requestsToAsaas", {
    createdBy,
    email,
    name,
    cpf_cnpj,
    zip_code,
    number,
    plan_identifier,
    payment_type,
    isPromotional
  });
};

export const deleteAsaasPlan = async (
  createdBy,
  planId
) => {
  return api.post("/delete-plan-asaas/" + createdBy, {
    planId
  });
}

export const updateQuestionResponses = async (company_id, responses) => {
  return api.post("/update-company-responses", {
    company_id,
    responses,
  });
};

export const updateQuestionActive = async (companyId, questionIdentifier, active) => {
  return api.put("/update-question-active", {
    companyId,
    questionIdentifier,
    active,
  });
};

// ===== NOVAS FUNÇÕES PARA CUSTOM RESPONSES =====

// Buscar respostas personalizadas da empresa
export const getCustomResponses = async (companyId) => {
  return api.get("/custom-responses/" + companyId);
};

// Adicionar nova resposta personalizada
export const addCustomResponse = async (companyId, question, response) => {
  return api.post("/custom-responses/" + companyId, {
    question,
    response,
  });
};

// Atualizar resposta personalizada
export const updateCustomResponse = async (companyId, responseId, question, response, active = true) => {
  return api.put(`/custom-responses/${companyId}/${responseId}`, {
    question,
    response,
    active,
  });
};

// Deletar resposta personalizada
export const deleteCustomResponse = async (companyId, responseId) => {
  return api.delete(`/custom-responses/${companyId}/${responseId}`);
};

export const registerCliente = async (
  createdBy,
  documento,
  name,
  contato,
  razao,
  email,
  cep,
  estado,
  municipio,
  bairro,
  complemento,
  telefone,
  celular,
  vinculo_empresa,
  type
) => {
  if (type === "F") {
    return api.post("/auth/registerCliente", {
      createdBy,
      documento,
      name,
      contato,
      email,
      cep,
      estado,
      municipio,
      bairro,
      complemento,
      telefone,
      celular,
      vinculo_empresa,
      type,
    });
  } else {
    return api.post("/auth/registerCliente", {
      createdBy,
      documento,
      name,
      razao,
      email,
      cep,
      estado,
      municipio,
      bairro,
      complemento,
      telefone,
      celular,
      vinculo_empresa,
      type,
    });
  }
};

export const registerClienteFromCardapio = async (
  id_empresa,
  nome,
  telefone
) => {
  return api.post("/register-cliente-cardapio", { id_empresa, nome, telefone });
};

export const registerVendedor = async (
  createdBy,
  documento,
  name,
  cep,
  estado,
  municipio,
  bairro,
  complemento,
  telefone,
  celular,
  vinculo_empresa,
  type
) => {
  return api.post("/auth/registerVendedor", {
    createdBy,
    documento,
    name,
    cep,
    estado,
    municipio,
    bairro,
    complemento,
    telefone,
    celular,
    vinculo_empresa,
    type,
  });
};

export const registerOrcamento = async (
  createdBy,
  data_emissao,
  codigo_cliente,
  nome_cliente,
  codigo_vendedor,
  nome_vendedor,
  total_orc,
  status_orc,
  id_grupo,
  vinculo_empresa,
  items
) => {
  return api.post("/auth/registerOrcamento", {
    createdBy,
    data_emissao,
    codigo_cliente,
    nome_cliente,
    codigo_vendedor,
    nome_vendedor,
    total_orc,
    status_orc,
    id_grupo,
    vinculo_empresa,
    items,
  });
};

/*export const retornaClienteAsaas = async (nome_cliente, cpf_cnpj, email_cliente, phone, addressNumber, postalCode) => {   
    return api.post("/retorna-cliente-asaas",{nome_cliente, cpf_cnpj, email_cliente, phone, addressNumber, postalCode}); 
};*/

export const criarAssinaturaAsaasBolPix = async (
  customerID,
  payment_type,
  cycle,
  value,
  nextDueDate
) => {
  return api.post("/criar-assinatura-asaas-bolpix", {
    customerID,
    payment_type,
    cycle,
    value,
    nextDueDate,
  });
};

export const criarAssinaturaAsaasCartao = async (
  customerID,
  nome_cliente,
  cpf_cnpj,
  cycle,
  numero_cartao,
  expiryMonth,
  expiryYear,
  ccv,
  email,
  postalCode,
  addressNumber,
  phone,
  value,
  nextDueDate,
  remoteIp
) => {
  return api.post("/criar-assinatura-asaas-cartao", {
    customerID,
    nome_cliente,
    cpf_cnpj,
    cycle,
    numero_cartao,
    expiryMonth,
    expiryYear,
    ccv,
    email,
    postalCode,
    addressNumber,
    phone,
    value,
    nextDueDate,
    remoteIp,
  });
};

export const criarCobrancaAsaas = async (
  customerID,
  nome_cliente,
  cpf_cnpj,
  numero_cartao,
  expiryMonth,
  expiryYear,
  ccv,
  email,
  postalCode,
  addressNumber,
  phone,
  value,
  nextDueDate,
  remoteIp,
  parcela
) => {
  return api.post("/criar-cobranca-asaas-cartao", {
    customerID,
    nome_cliente,
    cpf_cnpj,
    numero_cartao,
    expiryMonth,
    expiryYear,
    ccv,
    email,
    postalCode,
    addressNumber,
    phone,
    value,
    nextDueDate,
    remoteIp,
    parcela,
  });
};

export const criarCobrancaAsaasBolPix = async (
  customerID,
  payment_type,
  value,
  nextDueDate
) => {
  return api.post("/criar-cobranca-asaas", {
    customerID,
    payment_type,
    value,
    nextDueDate,
  });
};

export const listarAssinaturasAsaas = async (customer_id) => {
  return api.post("/lista-subscription-asaas", { customer_id });
};

/*export const listarAssinaturasIugu = async (empresa_id, customer_id) => {
  return api.post("/lista-subscription-iugu", { empresa_id, customer_id });
}*/

export const getDataCustomerAsaas = async (customer_id) => {
  return api.post("/getData-customer-asaas", { customer_id });
}

export const getLastPendingInvoiceAsaas = async (subscriptionIdAsaas) => {
  return api.post("/lastPendingInvoice", { subscriptionIdAsaas });
}

export const getAllInvoicesAsaas = async (subscriptionIdAsaas) => {
  return api.post("/allInvoices", { subscriptionIdAsaas });
};

export const getInvoiceIugu = async (invoiceId) => {
  return api.post("/getInvoice-iugu", { invoiceId });
}

export const importCardapioIfood = async (url_import, id_empresa) => {
  return api.post("/import-cardapio-ifood", { url_import, id_empresa });
};

export const importCardapioAnotaai = async (url_import, id_empresa) => {
  //console.log("TRECHO API>",url_import)
  return api.post("/import-cardapio-anotaai", { url_import, id_empresa });
};

export const changeStatusLoja = async (id, status_loja) => {
  return api.post("/changeStatusLoja", { id, status_loja });
};

export const getUltimoPedidoID = async (id) => {
  return api.get("/ultimoPedidoID/" + id);
}

export const getDaysToExpireLicense = async (id) => {
  return api.get("/getDaysToExpireLicense/" + id);
}

export const apiCheckImportStatus = async (idEmpresa) => {
  return api.get(`/check-import-status/${idEmpresa}`);
};

export const configurarResetPedido = async (id_empresa, dias_para_reset) => {
  return api.post(`/configurar-reset-pedido`, { id_empresa, dias_para_reset });
};

export const resetPedidoManual = async (id_empresa) => {
  return api.post(`/reset-pedido-counter`, { id_empresa });
};

export const updateStatusBotEmpresa = async (id, status_bot) => {
  return api.patch(`/api/empresa/${id}/status_bot`, { status_bot });
};

export const updateCallAtendenteEmpresa = async (id, call_atendente) => {
  return api.patch(`/api/empresa/${id}/call_atendente`, { call_atendente });
};

export const getQrCodeWhatsapp = async (empresaID, token) => {
  setAuthToken(token);
  return api.get("/api/v1/whatsapp/qrcode/" + empresaID);
};

// **📌 NOVA FUNÇÃO PARA RENOVAR QR CODE VIA API**
export const renewQrCodeWhatsapp = async (empresaID, token) => {
  setAuthToken(token);
  return api.post("/api/v1/whatsapp/qrcode/renew/" + empresaID);
};

export const refreshProfilePicturesWhatsapp = async (empresaID, token) => {
  setAuthToken(token);
  return api.post("/api/v1/whatsapp/refresh-profile-pictures/" + empresaID);
};

export const refreshContactInfoWhatsapp = async (leadChannelID, token) => {
  setAuthToken(token);
  return api.post("/api/v1/whatsapp/refresh-contact-info/" + leadChannelID);
};



// **📌 NOVA FUNÇÃO PARA VERIFICAR STATUS DE CONEXÃO DA EVOLUTION API**
export const getWhatsappConnectionStatus = async (empresaID, token) => {
  setAuthToken(token);
  return api.get("/api/v1/whatsapp/connectionStatus/" + empresaID);
};

export const deleteInstanceAdmin = async (userID, id) => {
  return api.delete(`/deleteInstance-admin/${userID}`, { data: { id } });
};

// 🔥 NOVO: Funções para gerenciar fila de atendimento humano
export const buscarFilaAtendimento = async (empresaId) => {
  return api.get(`/api/v1/whatsapp/fila-atendimento/${empresaId}`);
};

export const iniciarAtendimentoFila = async (empresaId, atendimentoId, userData) => {
  return api.post(`/api/v1/whatsapp/fila-atendimento/${empresaId}/iniciar/${atendimentoId}`, userData);
};

export const finalizarAtendimentoFila = async (empresaId, atendimentoId, observacoes) => {
  return api.post(`/api/v1/whatsapp/fila-atendimento/${empresaId}/finalizar/${atendimentoId}`, { observacoes });
};

export const cancelarAtendimentoFila = async (empresaId, atendimentoId, motivo) => {
  return api.delete(`/api/v1/whatsapp/fila-atendimento/${empresaId}/cancelar/${atendimentoId}`, { data: { motivo } });
};

export const limparAtendimentosFinalizados = async (empresaId) => {
  return api.post(`/api/v1/whatsapp/fila-atendimento/${empresaId}/limpar-finalizados`);
};

export const sendTextMessageWhatsapp = async (empresaID, number, message) => {
  //setAuthToken(token);
  return api.post("/api/v1/whatsapp/sendTextMessage/" + empresaID, { number, message });
};

export const removeWhatsappSession = async (empresaID) => {
  return api.get(`/api/v1/whatsapp/removeSession/${empresaID}`);
};

// Função para configurar webhook
export const configureWhatsappWebhook = async (empresaID) => {
  return api.post(`/api/v1/whatsapp/webhook/configure/${empresaID}`);
};

// Função para verificar status do webhook
export const getWhatsappWebhookStatus = async (empresaID) => {
  return api.get(`/api/v1/whatsapp/webhook/status/${empresaID}`);
};

// Função para obter os chats
export const getWhatsappChats = async (
  empresaID,
  page,
  pageSize,
  query,
  token
) => {
  setAuthToken(token);
  return api.get(`/api/v1/whatsapp/chats/${empresaID}`, {
    params: {
      page,
      pageSize,
      query,
    },
  });
};

// Função para obter os conversa de um chat com LeadId
export const getWhatsappChatLead = async (
  empresaID,
  leadID,
  page,
  pageSize,
  token
) => {
  setAuthToken(token);
  return api.get(`/api/v1/whatsapp/chats/${empresaID}/messages/${leadID}`, {
    params: {
      page,
      pageSize,
    },
  });
};

// Função para obter contagem de mensagens não lidas
export const getWhatsappUnreadCount = async (empresaID, token) => {
  setAuthToken(token);
  return api.get(`/api/v1/whatsapp/chats/${empresaID}/unread-count`);
};

// Função para marcar mensagens como lidas
export const markWhatsappMessagesAsRead = async (empresaID, leadID, token) => {
  setAuthToken(token);
  return api.put(`/api/v1/whatsapp/chats/${empresaID}/messages/${leadID}/mark-as-read`);
};

// 🔥 NOVO: Função para buscar um chat específico por lead_id
export const getWhatsappChatById = async (empresaID, leadID, token) => {
  setAuthToken(token);
  return api.get(`/api/v1/whatsapp/chats/${empresaID}/chat/${leadID}`);
};

// 🔥 NOVO: Função para iniciar nova conversa com um contato
export const createWhatsappChat = async (empresaID, contactNumber, contactName, token) => {
  setAuthToken(token);
  return api.post(`/api/v1/whatsapp/chats/${empresaID}/create`, {
    contactNumber,
    contactName
  });
};

export const getEstados = async () => {
  return api.get(
    "https://servicodados.ibge.gov.br/api/v1/localidades/estados/"
  );
};

export const getMunicipios = async (estadoSelecionado) => {
  return api.get(
    "https://servicodados.ibge.gov.br/api/v1/localidades/estados/" +
    estadoSelecionado +
    "/municipios"
  );
};

export const getBairros = async (city, uf) => {
  console.log("Chegou no getBairros da api:", city, uf)
  return api.get(`/carregar-bairros?city=${city}&uf=${uf}`);
};

export const calcularDistancia = async (coordenadasA, coordenadasB) => {
  const { lat: latA, lng: lngA } = coordenadasA;
  const { lat: latB, lng: lngB } = coordenadasB;
  return api.get(`/calcular-distancia?latA=${latA}&lngA=${lngA}&latB=${latB}&lngB=${lngB}`);
};

// Mesas

export const getMesas = async () => {
  const { data } = await api.get('/mesas');

  if (!data.status) {
    return null;
  }

  return data.data;
}

export const createMesa = async ({ name, qr_code }) => {
  const { data } = await api.post('/mesas', {
    name,
    qr_code
  });

  if (!data.status) {
    return null;
  }

  return data.data;
}

export const createManyMesa = async ({ url, quantity }) => {
  const { data } = await api.post('/many_mesas', {
    url,
    quantity
  });

  if (!data.status) {
    return null;
  }

  return data.data;
}


export const registerPedidoMesa = async (
  {
    mesa_id,
    id_empresa,
    itens,
    celular_cliente,
    nome_cliente,
    valor_total,
    descricao,
  }
) => {
  return api.post("/mesas/registerPedido", {
    id_empresa,
    itens,
    mesa_id,
    celular_cliente,
    nome_cliente,
    valor_total,
    descricao,
  });
};

export const deleteMesa = async ({ mesaId }) => {
  const { data } = await api.delete(`/mesas/${mesaId}`);

  if (!data.status) {
    return null;
  }

  return data.data;
}

export const registerPayment = async (
  {
    mesa_id,
    payment_type,
    total_payed,
    customer_name,
    customer_phone
  }
) => {
  return api.post("/register-payment", {
    mesa_id,
    payment_type,
    total_payed,
    customer_name,
    customer_phone
  });
};

// Funções para gerenciar progresso da configuração inicial
export const getProgressoConfiguracaoInicial = async (empresaId) => {
  return api.get(`/getProgressoConfiguracaoInicial/${empresaId}`);
};

export const atualizarProgressoConfiguracaoInicial = async (empresaId, etapaCompleta, proximaEtapa, finalizada = false) => {
  return api.put(`/atualizarProgressoConfiguracaoInicial/${empresaId}`, {
    etapaCompleta,
    proximaEtapa,
    finalizada
  });
};

// Funções para solicitação de cardápio para equipe
export const enviarCardapioParaEquipe = async (empresaId, idEmpresa, userId, customLink, pdfFile, observacoes, userLookupName) => {
  const formData = new FormData();
  formData.append('empresaId', empresaId);
  formData.append('idEmpresa', idEmpresa);
  formData.append('userId', userId);
  formData.append('customLink', customLink || '');
  formData.append('observacoes', observacoes || '');
  formData.append('userLookupName', userLookupName || '');
  
  if (pdfFile) {
    formData.append('pdfFile', pdfFile);
  }

  return api.post('/enviar-cardapio-para-equipe', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const listarSolicitacoesCardapio = async (userId, status = 'todos', page = 1, limit = 10) => {
  return api.get(`/listar-solicitacoes-cardapio/${userId}`, {
    params: {
      status,
      page,
      limit
    }
  });
};

export const atualizarStatusSolicitacaoCardapio = async (userId, empresaId, solicitacaoId, novoStatus, observacoesEquipe, usuarioResponsavel) => {
  return api.put(`/atualizar-status-solicitacao-cardapio/${userId}`, {
    empresaId,
    solicitacaoId,
    novoStatus,
    observacoesEquipe,
    usuarioResponsavel
  });
};

export const buscarSolicitacoesEmpresa = async (empresaId, userId) => {
  return api.get(`/solicitacoes-cardapio-empresa/${empresaId}/${userId}`);
};

export const getProgressoImportacao = async (idEmpresa) => {
  return api.get(`/progress-import/${idEmpresa}`);
};

export const resetImportStatus = async (idEmpresa) => {
  return api.post(`/reset-import-status/${idEmpresa}`);
};
