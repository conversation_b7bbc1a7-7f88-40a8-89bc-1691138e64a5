const axios = require('axios');

const httpRegex = /^(http|https):\/\/(.*)/gm;

class WhatsappManager {
    endpoint = '';
    token = '';

	constructor(endpoint, token) {

		/*if (!endpoint) {
			throw "ERRO_SET_ENDPOINT"
		}

        if (!token || token.length < 4) {
            throw "ERRO_SET_TOKEN"
        }*/

		this.endpoint = endpoint;
		this.token = token;
	}

    async axioGETRequest (path = '/') {
		const options = {
			method: "GET",
			url: this.endpoint + path,
			headers: {accept: 'application/json', apikey: this.token}
		};

		return axios.request(options);

    }

	async axioPOSTRequest (path = '/' , data) {
		const options = {
			method: "POST",
			url: this.endpoint + path,
			headers: {accept: 'application/json', apikey: this.token, 'Content-Type': 'application/json'},
			data: data
		};

		return axios.request(options);

    }


    // Função para normalizar o nome da instância
    normalizeInstanceName(name) {
        // Remove acentos, converte para lowercase, substitui espaços por hífens
        const normalized = name
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '') // Remove acentos
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '') // Remove caracteres especiais exceto espaços
            .replace(/\s+/g, '-') // Substitui espaços por hífens
            .replace(/-+/g, '-') // Remove hífens duplicados
            .replace(/^-|-$/g, ''); // Remove hífens do início e fim

        // Adiciona timestamp para garantir unicidade
        const timestamp = Date.now();

        return `${normalized}-${timestamp}`;
    }

	async createInstance(custom_name = "", webhook = "") {

        // Definir webhook baseado no ambiente
        if (process.env.NODE_ENV === 'develop' && process.env.NGROK_URL) {
            webhook = `${process.env.NGROK_URL}/api/v1/whatsapp/webhook`;
        } else {
            webhook = process.env.WP_WEBHOOK || "https://api.dev.pedeja.chat/api/v1/whatsapp/webhook";
        }

        // Normalizar o nome da instância para garantir unicidade
        const normalizedInstanceName = this.normalizeInstanceName(custom_name);

        console.log(`📝 Nome original: "${custom_name}"`);
        console.log(`🔄 Nome normalizado: "${normalizedInstanceName}"`);

		const data = {
            instanceName: normalizedInstanceName,
            qrcode: false,
			integration: "WHATSAPP-BAILEYS",
            groupsIgnore: true,
            webhook: {
                url: webhook,
                byEvents: true,  // 🔧 CORRIGIDO: Habilitar byEvents para receber eventos específicos
                base64: true,
                events: [
                    "QRCODE_UPDATED",
                    "CONNECTION_UPDATE",
                    "MESSAGES_UPSERT",
                    "MESSAGES_UPDATE",
                    "SEND_MESSAGE"
                ]
            }
		};

		const request = await this.axioPOSTRequest("/instance/create", data);

        if(request.status !== 201){
            throw "ERRO_INSTANCE_CREATE"
        }

        // Adaptar resposta para formato esperado
        const { instance, hash } = request.data;
        return {
            id: instance.instanceId,
            name: normalizedInstanceName, // Incluir o nome normalizado para futuras operações
            endpoint: this.endpoint,
            token: hash || this.token  // hash é uma string direta, não um objeto
        };
	}

    async axioDELETERequest(path = '/') {
        const options = {
            method: "DELETE",
            url: this.endpoint + path,
            headers: {accept: 'application/json', apikey: this.token}
        };

        return axios.request(options);
    }

    async deleteInstance(instanceIdentifier) {
        // A Evolution API espera o NOME da instância, não o ID
        // Se o identificador já é um nome (contém hífens e timestamp), usar diretamente
        // Senão, buscar o nome pelo ID

        if (instanceIdentifier.includes('-') && /\d{13}$/.test(instanceIdentifier)) {
            // Parece ser um nome normalizado (contém hífens e termina com timestamp)
            console.log(`🎯 Usando identificador como nome da instância: ${instanceIdentifier}`);

            const request = await this.axioDELETERequest(`/instance/delete/${instanceIdentifier}`);

            if(request.status !== 200){
                throw "ERRO_INSTANCE_DELETE"
            }			

            return request.data;
        }

        // Caso contrário, buscar o nome pelo ID
        try {
            const listResult = await this.axioGETRequest("/instance/fetchInstances");

            if (listResult.data && listResult.data.length > 0) {
                // Procurar a instância pelo ID
                const targetInstance = listResult.data.find(inst =>
                    inst.id === instanceIdentifier ||
                    inst.instance?.instanceId === instanceIdentifier
                );

                if (targetInstance) {
                    // Usar o nome da instância para deletar
                    const instanceName = targetInstance.name || targetInstance.instance?.instanceName;

                    if (instanceName) {
                        console.log(`🎯 Encontrado nome da instância: ${instanceName} para ID: ${instanceIdentifier}`);

                        const request = await this.axioDELETERequest(`/instance/delete/${instanceName}`);

                        if(request.status !== 200){
                            throw "ERRO_INSTANCE_DELETE"
                        }

                        return request.data;
                    } else {
                        throw new Error("Nome da instância não encontrado");
                    }
                } else {
                    throw new Error(`Instância com ID ${instanceIdentifier} não encontrada`);
                }
            } else {
                throw new Error("Nenhuma instância encontrada na lista");
            }
        } catch (error) {
            if (error.message && error.message.includes("não encontrada")) {
                throw error;
            }

            // Se falhou ao buscar a lista, tentar deletar diretamente
            console.log(`🎯 Tentando deletar diretamente: ${instanceIdentifier}`);

            const request = await this.axioDELETERequest(`/instance/delete/${instanceIdentifier}`);

            if(request.status !== 200){
                throw "ERRO_INSTANCE_DELETE"
            }

            return request.data;
        }
    }

}

module.exports = WhatsappManager