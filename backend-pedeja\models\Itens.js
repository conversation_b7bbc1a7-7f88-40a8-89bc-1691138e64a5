const mongoose = require('mongoose')

const ItemSchema = new mongoose.Schema({
empresaObjId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Empresa',
    required: true,
},
id_item: Number,
id_empresa: Number,
createdBy: String,

category_item_id: String,
category_item_title: String,
title: String,
description: String,
out: Boolean,
out_salao: Boolean,
image: String, //url da imagem
order: Number,
price: Number,
price_salao: Number,
disponibilidade: String,
dia_horario_disponibilidade: Array,
type: String,
import_category_id: Number,

has_adicional: Boolean,
adicionais: Array,
inativo:Boolean,
bloqueado:Boolean,

createdAt: Date,
updatedAt: Date,
deletedAt: Date

})

const Itens = mongoose.model('Itens', ItemSchema)

module.exports = Itens