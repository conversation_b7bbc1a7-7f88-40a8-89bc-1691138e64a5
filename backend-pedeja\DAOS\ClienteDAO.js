class ClienteDAO {
    constructor(clienteModel){
        this.model = clienteModel;
    }

    async findAndUpdateCounterPedido({ id_empresa, celular_cliente }){
        try {
            return await this.model.findOneAndUpdate(
                {id_empresa:id_empresa, telefone:celular_cliente},
                    { $inc: { counter_qtd_pedido: 1 } },
                    { new: true }
                );
        } catch(error){
            console.log('error', error);
            return {
                status: false,
                message: "Erro ao atualizar contador de pedidos"
            }
        }
    }

    async createCliente({ id_empresa, celular_cliente }){

        const newVal = new this.model({
            id_empresa:id_empresa,
            telefone: celular_cliente,
            counter_qtd_pedido: 1 
        });
        await newVal.save();

    }
}

module.exports = ClienteDAO;