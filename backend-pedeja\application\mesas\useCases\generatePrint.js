const path = require("path");
const fs = require("fs");
const { chromium } = require("playwright");
const { enviarParaImpressao } = require("../../..");
const { Storage } = require("@google-cloud/storage");
const { PubSub } = require("@google-cloud/pubsub"); // GOOGLE PUB/SUB

const storage = new Storage({
    keyFilename: "./projeto-pedeja-28a38562cd77.json",
});
const bucket = storage.bucket("pedeja-static");
// Instancia o cliente do Google Cloud Pub/Sub com as credenciais
const pubSubClient = new PubSub({
    keyFilename: "./projeto-pedeja-28a38562cd77.json",
});
const bucketPrint = "pedeja-print";
const nomeTopico = "fila-impressao";

class GeneratePrintUseCase {
  constructor(mesaDAO, empresaDAO){
      this.mesaDAO = mesaDAO
      this.empresa = empresaDAO;
  }

  async execute({ vinculo_empresa, mesa_id }) {
      try {
          const empresa = await this.empresa.getEmpresaById(vinculo_empresa);

          const currentMesa = await this.mesaDAO.findMesaById({ mesa_id: mesa_id });

          if(!currentMesa){
              return {
                  status: false,
                  message: "Mesa atual não encontrada"
              }
          }

          const dados = {
            tipoPedido: currentMesa.name,
            dataPedido: new Date(),
            nomeLoja: empresa.name,
          }

          this.gerarPDF({ pedidos: currentMesa.pedidos, dados: dados })
                .then((caminhoArquivo) => {
                    console.log("Comanda gerada com sucesso:", caminhoArquivo);
                    // Aqui você pode enviar o arquivo para impressão ou fazer outras operações
                    this.enviarParaImpressao(caminhoArquivo, empresa._id);
                })
                .catch((erro) => {
                    console.error("Erro ao gerar a comanda:", erro);
                });

          return {
              status: true,
              message: "Impressão realizada com sucesso",
          }

      } catch(error){
          console.error(error);
          return {
              status: false,
              msg: "Error ao registrar pedido"
          }
      }
  }

  async gerarPDF({ pedidos, dados }) {
    const isProduction = process.env.NODE_ENV === "production";

    const date = new Date(pedidos[0].id_pedido.createdAt).toLocaleDateString("pt-BR");

    const browser = await chromium.launch({
        headless: true,
        args: [
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage", // Usa memória do disco ao invés de memória compartilhada
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--single-process", // Tenta manter um único processo, o que pode ajudar em ambientes com memória limitada
        ],
        executablePath: isProduction
            ? "/usr/bin/chromium-browser" // Caminho para produção (Fly.io)
            : "/usr/bin/chromium-browser", // Usar o Chromium padrão baixado pelo Playwright no ambiente local
    });

    const total = pedidos.reduce((acc, pedido) => acc + pedido.id_pedido.valor_total, 0);

    const page = await browser.newPage();
    // Defina seu HTML e CSS aqui
    const contentHTML = `
        <html>
          <head>
            <style>
              body {
                width: 80mm;
                font-family: 'Helvetica', 'Arial', sans-serif;
              }
              .header-title {
                display: flex;
                justify-content: center;
                flex-direction: column;
                align-items: center;
              }
              .delivery-type {
                font-size: 16px;
                font-weight: bold;
                text-transform: uppercase;
              }
              .title {
                font-size: 22px;
                font-weight: bold;
                text-align: center;
              }
              .items {
                margin-top: 5mm;
                font-size: 18px;
              }
              .itens-label {
                display: flex;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 2mm;
              }
              .item, .item-complement {
                display: flex;
                justify-content: space-between;
                margin-bottom: 2mm;
                font-size: 12px;
              }
              .item-complement {
                margin-left: 20px;
              }
              .item-name, .complement-name {
                font-size: 12px;
                font-weight: bold;
                max-width: 60mm;
              }
              .item-price, .complement-price {
                font-size: 12px;
                font-weight: bold;
              }
              .observacao-item {
                display: flex;
                border: 1px dashed;
                border-radius: 3px;
                flex-direction: column;
                justify-content: center;
                padding: 3px;
              }
              .observacao-item label {
                font-size: 12px;
                font-weight: bold;
                text-align: center;
              }
              .observacao-item span {
                font-size: 12px;
              }
              .cliente, .pagamento {
                margin-top: 5mm;
                font-size: 18px;
                display: flex;
                flex-direction: column;
              }
              .cliente span, .pagamento span {
                font-size: 12px;
                font-weight: bold;
              }
              .total {
                font-size: 14px;
                font-weight: bold;
                text-align: right;
                margin-top: 5mm;
                display: flex;
                flex-direction: column;
              }
              .total-values {
                display: flex;
                justify-content: space-between;
              }
              .footer {
                text-align: center;
                font-size: 14px;
                font-weight: 700;
              }
            </style>
          </head>
          <body>
            <div class="header-title">
              <span>================================</span>
              <span class="delivery-type">${dados.tipoPedido}</span>
              <span>================================</span>
              <span>${date}</span>
              <span>${dados.nomeLoja}</span>
            </div>
            ${pedidos.map((dadosComanda) => `
              <div class="header-title">
                <span>--------------------------------------------------------</span>
                <span class="title">Pedido ${dadosComanda.id_pedido.id_pedido_counter}</span>
                <span>--------------------------------------------------------</span>
              </div>
              <div class="items">
                <label class="itens-label">Itens:</label>
                ${dadosComanda.id_pedido.itens.map((item) => {
                  const adicionaisHTML = item.grupo_adicionais.map((grupo) => {
                    if (grupo.calcular_maior_valor) {
                      const adicionalMaisCaro = grupo.adicionais.reduce(
                        (prev, current) => (current.price > prev.price ? current : prev)
                      );
                      const adicionalMaisCaroHTML = `
                        <div class="item-complement">
                          <div class="complement-name">
                            ${adicionalMaisCaro.quantity > grupo.adicionais.length
                              ? `${grupo.adicionais.length}/${grupo.adicionais.length}`
                              : `${adicionalMaisCaro.quantity}/${grupo.adicionais.length}`}
                            ${adicionalMaisCaro.title}
                          </div>
                          <div class="complement-price">R$ ${adicionalMaisCaro.price.toFixed(2).replace('.', ',')}</div>
                        </div>
                      `;
                      const adicionaisComPrecoZero = grupo.adicionais
                        .filter((adicional) => adicional.adicionalId !== adicionalMaisCaro.adicionalId)
                        .map((adicional) => `
                          <div class="item-complement">
                            <div class="complement-name">
                              ${adicional.quantity > grupo.adicionais.length
                                ? `${grupo.adicionais.length}/${grupo.adicionais.length}`
                                : `${adicional.quantity}/${grupo.adicionais.length}`}
                              ${adicional.title}
                            </div>
                            <div class="complement-price">R$ 0,00</div>
                          </div>
                        `)
                        .join('');
                      return `${adicionalMaisCaroHTML}${adicionaisComPrecoZero}`;
                    }
                    // Handle other scenarios...
                  }).join('');

                  return `
                    <div class="item">
                      <div class="item-name">(${item.quantidade}) ${item.nomeItem}</div>
                      <div class="item-price">R$ ${item.valor.toFixed(2).replace('.', ',')}</div>
                    </div>
                    ${adicionaisHTML}
                  `;
                }).join('')}
              </div>
              <div class="subtotal">
                <div class="total-values" style="font-size:18px;">
                  <span>Subtotal:</span>
                  <span>R$ ${dadosComanda.id_pedido.valor_total.toFixed(2).replace('.', ',')}</span>
                </div>
              </div>
            `).join('')}
            <div class="total">
              <span>--------------------------------------------------------</span>
              <div class="total-values" style="font-size:18px;">
                <span>Total:</span>
                <span>R$ ${total.toFixed(2).replace('.', ',')}</span>
              </div>
              <span>--------------------------------------------------------</span>
            </div>
            <div class="footer">
              Powered By: Pede Já <br>
              Acesse: https://pedeja.chat
            </div>
          </body>
        </html>
        `;

    console.log("breakpoint 4");

    await page.setContent(contentHTML, { waitUntil: "domcontentloaded" });
    console.log("breakpoint 5");

    const diretorio = path.join(__dirname, "../../../printer_comandas_clientes");
    this.garantirDiretorio(diretorio);
    const caminhoArquivo = path.join(
        diretorio,
        `empresa_${dados.nomeLoja},pedido_${dados.tipoPedido}.pdf`
    );
    console.log("breakpoint 6, caminho arquivo:", caminhoArquivo);

    // Gera um arquivo PDF no tamanho definido
    await page.pdf({
        path: caminhoArquivo,
        width: "80mm",
        margin: {
            top: "0mm",
            right: "2mm",
            bottom: "10mm",
            left: "2mm",
        },
    });
    await browser.close();

    return caminhoArquivo;
  }

  async garantirDiretorio(diretorio) {
    if (!fs.existsSync(diretorio)) {
        fs.mkdirSync(diretorio, { recursive: true });
    }
  }

  async enviarParaImpressao(caminhoArquivo, empresaObjId) {
    const nomeArquivoDestino = path.basename(caminhoArquivo);

    try {
        // Faz o upload do arquivo para o Google Cloud Storage
        await storage.bucket(bucketPrint).upload(caminhoArquivo, {
            destination: nomeArquivoDestino,
        });
        console.log(
            `${caminhoArquivo} uploaded to ${bucketPrint} as ${nomeArquivoDestino}.`
        );

        // Dados que você deseja enviar junto com a notificação
        const dadosMensagem = JSON.stringify({
            bucket: bucketPrint,
            arquivo: nomeArquivoDestino,
            empresaObjId: empresaObjId, // Inclua o empresaObjId na mensagem
        });

        // Cria um buffer a partir dos dados da mensagem
        const dataBuffer = Buffer.from(dadosMensagem);

        // Publica a mensagem no tópico do Pub/Sub com o atributo empresaObjId
        await pubSubClient.topic(nomeTopico).publish(dataBuffer, {
            empresaObjId: String(empresaObjId), // Garante que o valor é uma string
        });

        console.log("Mensagem publicada no Pub/Sub com atributo empresaObjId.");
    } catch (error) {
        console.error("Erro ao enviar para impressão:", error);
    }
  }
}

module.exports = GeneratePrintUseCase;