{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\roboConfig\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport './style.css';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport LeftMenu from \"../../components/LeftMenu\";\nimport ConfirmDialog from \"../../components/ConfirmDialog\";\nimport styled from 'styled-components';\nimport QRCode from 'qrcode.react';\nimport { LuPlusCircle } from \"react-icons/lu\";\nimport * as AiIcons from 'react-icons/ai';\nimport * as SlIcons from 'react-icons/sl';\nimport { FaRegTrashAlt } from \"react-icons/fa\";\nimport CryptoJS from 'crypto-js';\nimport { resetPedidoManual, getEmpresaWithObjId, getUltimoPedidoID, getCompanyResponses, updateQuestionResponses, updateQuestionActive } from \"../../services/api\";\nimport { toast } from \"react-toastify\";\nimport axios from \"axios\";\nimport moment from 'moment-timezone';\nimport backgroundWhatsApp from '../../assets/img/chat-background.png';\nimport debounce from 'lodash.debounce';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Teste = styled.div`\n\n    display: flex;\n    margin-left:  ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'}; \n    height:auto;\n    width:auto;\n    transition: 150ms;\n    background-color:rgb(247,247,247)!important;\n    overflow: initial;\n    z-Index: 9;\n\n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\n_c = Teste;\nconst Message = styled.div`\n  background: ${props => props.fromMe ? '#dcf8c6' : '#ffffff'};\n  padding: 10px;\n  border-radius: 7.5px;\n  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);\n  font-size: 1rem;\n  margin: 1px 0px 1px ${props => props.fromMe ? 'auto' : '0px'};\n  width: fit-content;\n  max-width: 60%;\n  margin-bottom: ${props => props.fromMe ? '3px' : '10px'};\n  white-space: pre-wrap; /* Adiciona suporte para quebras de linha */\n  span {\n    font-size: 0.9rem;\n    color: rgba(0, 0, 0, 0.45);\n    display: block;\n    text-align: right;\n  }\n`;\n_c2 = Message;\nconst RoboCfg = () => {\n  _s();\n  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\n  const userEncrypted = localStorage.getItem('user');\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  const userParse = JSON.parse(user);\n  const userID = userParse._id;\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const idEmpresa = empresaParse.id_empresa;\n  const empresaObjId = empresaParse._id;\n  const [atualSeqId, setAtualSeqId] = useState(0);\n  const [daysToReset, setDaysToReset] = useState(0);\n  const [lastResetedDate, setLastResetedDate] = useState(null);\n  const [refresh, setRefresh] = useState(false);\n  const [confirmOpen, setConfirmOpen] = useState(false);\n  const [tela, setTela] = useState(\"tela1\");\n  const [cancelOption, setCancelOption] = useState(\"noPassword\");\n  const [cancelPassword, setCancelPassword] = useState(\"\");\n  const [showInputNewPassword, setShowInputNewPassword] = useState(false);\n  const [newCancelPassword, setNewCancelPassword] = useState(\"\");\n  const [currentCancelPassword, setCurrentCancelPassword] = useState(\"\");\n  const [hasCancelPassword, setHasCancelPassword] = useState(false);\n  const [definindoNovaSenha, setDefinindoNovaSenha] = useState(true);\n  const {\n    sidebar,\n    setSidebar\n  } = useContext(SidebarContext);\n  ; //TESTANDO CONST NO AppROUTES\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [companyResponses, setCompanyResponses] = useState([]);\n  const [selectedResponse, setSelectedResponse] = useState(null);\n  const navigate = useNavigate();\n  useEffect(() => {\n    // Função para buscar os dados do banco de dados\n    const fetchData = async () => {\n      try {\n        var _empresaTemp$data$emp, _empresaTemp$data$emp2, _empresaTemp$data$emp3, _ultimoPedidoId$data;\n        // Supondo que getEmpresaWithObjId seja uma função que retorna uma Promise\n        const empresaTemp = await getEmpresaWithObjId(empresaObjId);\n        const companyResponsesTemp = await getCompanyResponses(empresaObjId);\n        console.log(\"companyResponses:\", companyResponsesTemp.data.responses);\n        setCompanyResponses(companyResponsesTemp.data.responses);\n        setSelectedResponse(companyResponsesTemp.data.responses[0]);\n        if ((_empresaTemp$data$emp = empresaTemp.data.empresa) !== null && _empresaTemp$data$emp !== void 0 && _empresaTemp$data$emp.dias_para_reset) {\n          setDaysToReset(empresaTemp.data.empresa.dias_para_reset);\n        }\n        if ((_empresaTemp$data$emp2 = empresaTemp.data.empresa) !== null && _empresaTemp$data$emp2 !== void 0 && _empresaTemp$data$emp2.last_reset_date) {\n          const dataFormatada = moment(empresaTemp.data.empresa.last_reset_date).tz(\"America/Sao_Paulo\").format(\"DD/MM/YYYY HH:mm\");\n          setLastResetedDate(dataFormatada);\n        }\n        if ((_empresaTemp$data$emp3 = empresaTemp.data.empresa) !== null && _empresaTemp$data$emp3 !== void 0 && _empresaTemp$data$emp3.has_cancel_password) {\n          setCancelOption(\"standardPassword\");\n          setDefinindoNovaSenha(false);\n        }\n        console.log(\"empresaTemp.data.empresa.has_cancel_password>>\", empresaTemp.data.empresa.has_cancel_password);\n        setHasCancelPassword(empresaTemp.data.empresa.has_cancel_password);\n        const ultimoPedidoId = await getUltimoPedidoID(idEmpresa);\n        if ((_ultimoPedidoId$data = ultimoPedidoId.data) !== null && _ultimoPedidoId$data !== void 0 && _ultimoPedidoId$data.ultimoPedidoId) {\n          setAtualSeqId(ultimoPedidoId.data.ultimoPedidoId);\n        }\n      } catch (error) {\n        console.error(\"Erro ao buscar dados da empresa:\", error);\n        // Lidar com o erro, talvez definindo algum estado de erro na UI\n      }\n    };\n    fetchData();\n  }, [refresh]); // O array vazio [] indica que o useEffect será executado uma vez após a montagem do componente\n\n  const debouncedUpdateQuestionActive = debounce(async (empresaObjId, questionIdentifier, isActive, setCompanyResponses, companyResponses) => {\n    try {\n      // Atualiza no backend\n      await updateQuestionActive(empresaObjId, questionIdentifier, isActive);\n\n      // Atualiza localmente após confirmação do backend\n      const updatedResponses = companyResponses.map(response => response.questionIdentifier === questionIdentifier ? {\n        ...response,\n        active: isActive\n      } : response);\n      setCompanyResponses(updatedResponses);\n      console.log(\"Disponibilidade atualizada com sucesso.\");\n    } catch (error) {\n      console.error(\"Erro ao atualizar disponibilidade:\", error);\n    }\n  }, 300); // Aguarda 300ms após o último evento\n\n  const handleBack = () => {\n    //setSidebar(!sidebar)\n    navigate(\"/\");\n  };\n  const handleSelectQuestionAndAnswer = message => {\n    setSelectedResponse(message);\n  };\n\n  // Função para atualizar o response do selectedResponse diretamente\n  const handleResponseChange = e => {\n    setSelectedResponse(prevSelectedResponse => ({\n      ...prevSelectedResponse,\n      response: e.target.value\n    }));\n  };\n\n  // Função para salvar as alterações no companyResponses\n  const handleSaveResponse = async () => {\n    if (selectedResponse) {\n      // Atualiza a resposta localmente\n      const updatedResponses = companyResponses.map(response => response.questionIdentifier === selectedResponse.questionIdentifier ? {\n        ...response,\n        response: selectedResponse.response\n      } // Atualiza apenas a mensagem de resposta\n      : response);\n      setCompanyResponses(updatedResponses);\n      try {\n        // Chama a API para atualizar o backend\n        await updateQuestionResponses(empresaObjId, updatedResponses);\n        toast(\"Respostas atualizadas com sucesso!\", {\n          autoClose: 2000,\n          type: \"success\"\n        });\n      } catch (error) {\n        console.error(\"Erro ao atualizar respostas:\", error);\n        toast(\"Ocorreu um erro ao salvar as respostas.\", {\n          autoClose: 2000,\n          type: \"error\"\n        });\n      }\n    }\n  };\n\n  // Função genérica para inserir texto no cursor\n  const insertAtCursor = (textarea, text) => {\n    if (textarea) {\n      const startPos = textarea.selectionStart;\n      const endPos = textarea.selectionEnd;\n      const value = textarea.value;\n\n      // Atualiza o texto no campo com o novo valor\n      textarea.value = value.substring(0, startPos) + text + value.substring(endPos);\n\n      // Mantém o cursor após o texto inserido\n      textarea.selectionStart = textarea.selectionEnd = startPos + text.length;\n    }\n  };\n\n  // Funções específicas para cada botão\n  const handleInsertNomeCliente = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"{nome_cliente}\");\n  };\n  const handleInsertLinkCardapio = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"{link}\");\n  };\n  const handleInsertSaudacao = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"Agradecemos a preferência\");\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: /*#__PURE__*/_jsxDEV(Teste, {\n        sidebar: sidebar,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-100 p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-header\",\n              style: {\n                marginBottom: \"0px\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"title\",\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  children: \"Configura\\xE7\\xF5es do Rob\\xF4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                justifyContent: \"right\" /*, height:\"80px\"*/\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contentItemComplete flex-column flex-md-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group inputGroup-etapasItem\",\n                style: {\n                  height: 50\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: tela === \"tela1\" ? \"etapasAddItem etapaAtiva\" : \"etapasAddItem\",\n                  style: {\n                    borderBottom: '1px solid lightgray'\n                  },\n                  onClick: () => setTela(\"tela1\"),\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"1. Personalizar Mensagens\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mh-100\",\n                style: {\n                  maxWidth: \"80%\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"formGroupRow\",\n                  children: tela === \"tela1\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"roboCfg-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"roboCfg-sidebar\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        children: \"Mensagens\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 269,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"roboCfg-message-list\",\n                        children: companyResponses.map((message, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          className: `roboCfg-message-item ${(selectedResponse === null || selectedResponse === void 0 ? void 0 : selectedResponse.questionIdentifier) === message.questionIdentifier ? 'roboCfg-selected' : ''}`,\n                          onClick: () => handleSelectQuestionAndAnswer(message),\n                          children: [message.questionType, /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"checkBoxContentMsg\",\n                            children: [message.active ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"roboCfg-edit-button-ativo\",\n                              children: \"Ativo\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 280,\n                              columnNumber: 73\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"roboCfg-edit-button-inativo\",\n                              children: \"Inativo\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 282,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"wrapper\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"switch_box box_1\",\n                                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                                  type: \"checkbox\",\n                                  className: \"switch_1\",\n                                  checked: message.active || false\n                                  // Dentro do checkbox\n                                  ,\n                                  onChange: e => {\n                                    const isActive = e.target.checked;\n\n                                    // Chamar a função debounce para evitar múltiplas chamadas ao backend\n                                    debouncedUpdateQuestionActive(empresaObjId, message.questionIdentifier, isActive, setCompanyResponses, companyResponses);\n                                  }\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 286,\n                                  columnNumber: 77\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 285,\n                                columnNumber: 73\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 284,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 278,\n                            columnNumber: 65\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 272,\n                          columnNumber: 61\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 270,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"roboCfg-main\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"roboCfg-preview\",\n                        style: {\n                          backgroundImage: `url(${backgroundWhatsApp})`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-chat\",\n                          children: [(selectedResponse === null || selectedResponse === void 0 ? void 0 : selectedResponse.question) && /*#__PURE__*/_jsxDEV(Message, {\n                            fromMe: false,\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              children: selectedResponse === null || selectedResponse === void 0 ? void 0 : selectedResponse.question\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 311,\n                              columnNumber: 69\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"12:00\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 312,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 310,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(Message, {\n                            fromMe: true,\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              children: selectedResponse === null || selectedResponse === void 0 ? void 0 : selectedResponse.response\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 318,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"12:01\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 319,\n                              columnNumber: 65\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 317,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 307,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 306,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"roboCfg-editor\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Personalize a mensagem\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 324,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                          className: \"roboCfg-textarea\",\n                          value: (selectedResponse === null || selectedResponse === void 0 ? void 0 : selectedResponse.response) || \"\",\n                          onChange: handleResponseChange // Altera diretamente o response do selectedResponse\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 325,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-buttons\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-button\",\n                            type: \"button\",\n                            onClick: handleInsertNomeCliente // Chama a função para Nome do cliente\n                            ,\n                            children: \"Nome do cliente\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 331,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-button\",\n                            type: \"button\",\n                            onClick: handleInsertLinkCardapio // Chama a função para Link do cardápio\n                            ,\n                            children: \"Link do card\\xE1pio\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 338,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 330,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-save-cancel\",\n                          children: /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-save-button\",\n                            type: \"button\",\n                            onClick: handleSaveResponse,\n                            children: \"Salvar\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 355,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 354,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 323,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(RoboCfg, \"QM4MGBp7nku14TIAdutFRMHz57Q=\", false, function () {\n  return [useNavigate];\n});\n_c3 = RoboCfg;\nexport default RoboCfg;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Teste\");\n$RefreshReg$(_c2, \"Message\");\n$RefreshReg$(_c3, \"RoboCfg\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "SidebarContext", "PermissionGate", "LeftMenu", "ConfirmDialog", "styled", "QRCode", "LuPlusCircle", "AiIcons", "SlIcons", "FaRegTrashAlt", "CryptoJS", "resetPedidoManual", "getEmpresaWithObjId", "getUltimoPedidoID", "getCompanyResponses", "updateQuestionResponses", "updateQuestionActive", "toast", "axios", "moment", "backgroundWhatsApp", "debounce", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON>e", "div", "sidebar", "_c", "Message", "props", "fromMe", "_c2", "RoboCfg", "_s", "secret<PERSON>ey", "userEncrypted", "localStorage", "getItem", "user", "AES", "decrypt", "toString", "enc", "Utf8", "userParse", "JSON", "parse", "userID", "_id", "empresa", "empresaParse", "idEmpresa", "id_empresa", "empresaObjId", "atualSeqId", "setAtualSeqId", "daysToReset", "setDaysToReset", "lastResetedDate", "setLastResetedDate", "refresh", "setRefresh", "confirmOpen", "setConfirmOpen", "tela", "<PERSON><PERSON><PERSON>", "cancelOption", "setCancelOption", "cancelPassword", "setCancelPassword", "showInputNewPassword", "setShowInputNewPassword", "newCancelPassword", "setNewCancelPassword", "currentCancelPassword", "setCurrentCancelPassword", "hasCancelPassword", "setHasCancelPassword", "definindoNovaSenha", "setDefinindoNovaSenha", "setSidebar", "isSubmitting", "setIsSubmitting", "companyResponses", "setCompanyResponses", "selectedResponse", "setSelectedResponse", "navigate", "fetchData", "_empresaTemp$data$emp", "_empresaTemp$data$emp2", "_empresaTemp$data$emp3", "_ultimoPedidoId$data", "empresaTemp", "companyResponsesTemp", "console", "log", "data", "responses", "dias_para_reset", "last_reset_date", "dataFormatada", "tz", "format", "has_cancel_password", "ultimoPedidoId", "error", "debouncedUpdateQuestionActive", "questionIdentifier", "isActive", "updatedResponses", "map", "response", "active", "handleBack", "handleSelectQuestionAndAnswer", "message", "handleResponseChange", "e", "prevSelectedResponse", "target", "value", "handleSaveResponse", "autoClose", "type", "insertAtCursor", "textarea", "text", "startPos", "selectionStart", "endPos", "selectionEnd", "substring", "length", "handleInsertNomeCliente", "document", "querySelector", "handleInsertLinkCardapio", "handleInsertSaudacao", "children", "permissions", "className", "style", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "height", "borderBottom", "onClick", "max<PERSON><PERSON><PERSON>", "index", "questionType", "checked", "onChange", "backgroundImage", "question", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/roboConfig/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport './style.css'\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\nimport ConfirmDialog from \"../../components/ConfirmDialog\";\r\nimport styled from 'styled-components';\r\nimport QRCode from 'qrcode.react';\r\nimport { LuPlusCircle } from \"react-icons/lu\";\r\nimport * as AiIcons from 'react-icons/ai'\r\nimport * as SlIcons from 'react-icons/sl'\r\nimport { FaRegTrashAlt } from \"react-icons/fa\";\r\nimport CryptoJS from 'crypto-js';\r\nimport { resetPedidoManual, getEmpresaWithObjId, getUltimoPedidoID, getCompanyResponses, updateQuestionResponses, updateQuestionActive } from \"../../services/api\";\r\nimport { toast } from \"react-toastify\";\r\nimport axios from \"axios\";\r\nimport moment from 'moment-timezone';\r\nimport backgroundWhatsApp from '../../assets/img/chat-background.png'\r\nimport debounce from 'lodash.debounce';\r\n\r\nconst Teste = styled.div`\r\n\r\n    display: flex;\r\n    margin-left:  ${({ sidebar }) => (sidebar ? '250px' : '100px')}; \r\n    height:auto;\r\n    width:auto;\r\n    transition: 150ms;\r\n    background-color:rgb(247,247,247)!important;\r\n    overflow: initial;\r\n    z-Index: 9;\r\n\r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\nconst Message = styled.div`\r\n  background: ${(props) => (props.fromMe ? '#dcf8c6' : '#ffffff')};\r\n  padding: 10px;\r\n  border-radius: 7.5px;\r\n  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);\r\n  font-size: 1rem;\r\n  margin: 1px 0px 1px ${(props) => (props.fromMe ? 'auto' : '0px')};\r\n  width: fit-content;\r\n  max-width: 60%;\r\n  margin-bottom: ${(props) => (props.fromMe ? '3px' : '10px')};\r\n  white-space: pre-wrap; /* Adiciona suporte para quebras de linha */\r\n  span {\r\n    font-size: 0.9rem;\r\n    color: rgba(0, 0, 0, 0.45);\r\n    display: block;\r\n    text-align: right;\r\n  }\r\n`;\r\n\r\nconst RoboCfg = () => {\r\n\r\n    const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\r\n    const userEncrypted = localStorage.getItem('user')\r\n    const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\r\n    const userParse = JSON.parse(user)\r\n    const userID = userParse._id;\r\n    const empresa = localStorage.getItem('empresa')\r\n    const empresaParse = JSON.parse(empresa)\r\n    const idEmpresa = empresaParse.id_empresa;\r\n    const empresaObjId = empresaParse._id;\r\n    const [atualSeqId, setAtualSeqId] = useState(0);\r\n    const [daysToReset, setDaysToReset] = useState(0);\r\n    const [lastResetedDate, setLastResetedDate] = useState(null);\r\n    const [refresh, setRefresh] = useState(false);\r\n    const [confirmOpen, setConfirmOpen] = useState(false);\r\n    const [tela, setTela] = useState(\"tela1\")\r\n    const [cancelOption, setCancelOption] = useState(\"noPassword\");\r\n    const [cancelPassword, setCancelPassword] = useState(\"\");\r\n    const [showInputNewPassword, setShowInputNewPassword] = useState(false);\r\n    const [newCancelPassword, setNewCancelPassword] = useState(\"\");\r\n    const [currentCancelPassword, setCurrentCancelPassword] = useState(\"\");\r\n    const [hasCancelPassword, setHasCancelPassword] = useState(false);\r\n    const [definindoNovaSenha, setDefinindoNovaSenha] = useState(true);\r\n    const { sidebar, setSidebar } = useContext(SidebarContext);; //TESTANDO CONST NO AppROUTES\r\n    const [isSubmitting, setIsSubmitting] = useState(false);\r\n    const [companyResponses, setCompanyResponses] = useState([]);\r\n    const [selectedResponse, setSelectedResponse] = useState(null)\r\n    const navigate = useNavigate();\r\n\r\n    useEffect(() => {\r\n        // Função para buscar os dados do banco de dados\r\n        const fetchData = async () => {\r\n            try {\r\n                // Supondo que getEmpresaWithObjId seja uma função que retorna uma Promise\r\n                const empresaTemp = await getEmpresaWithObjId(empresaObjId);\r\n                const companyResponsesTemp = await getCompanyResponses(empresaObjId);\r\n                console.log(\"companyResponses:\", companyResponsesTemp.data.responses);\r\n                setCompanyResponses(companyResponsesTemp.data.responses);\r\n                setSelectedResponse(companyResponsesTemp.data.responses[0]);\r\n                if (empresaTemp.data.empresa?.dias_para_reset) {\r\n                    setDaysToReset(empresaTemp.data.empresa.dias_para_reset)\r\n                }\r\n                if (empresaTemp.data.empresa?.last_reset_date) {\r\n                    const dataFormatada = moment(empresaTemp.data.empresa.last_reset_date)\r\n                        .tz(\"America/Sao_Paulo\")\r\n                        .format(\"DD/MM/YYYY HH:mm\");\r\n                    setLastResetedDate(dataFormatada);\r\n                }\r\n                if (empresaTemp.data.empresa?.has_cancel_password) {\r\n                    setCancelOption(\"standardPassword\");\r\n                    setDefinindoNovaSenha(false);\r\n                }\r\n                console.log(\"empresaTemp.data.empresa.has_cancel_password>>\", empresaTemp.data.empresa.has_cancel_password)\r\n                setHasCancelPassword(empresaTemp.data.empresa.has_cancel_password);\r\n                const ultimoPedidoId = await getUltimoPedidoID(idEmpresa)\r\n                if (ultimoPedidoId.data?.ultimoPedidoId) {\r\n                    setAtualSeqId(ultimoPedidoId.data.ultimoPedidoId)\r\n                }\r\n            } catch (error) {\r\n                console.error(\"Erro ao buscar dados da empresa:\", error);\r\n                // Lidar com o erro, talvez definindo algum estado de erro na UI\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, [refresh]); // O array vazio [] indica que o useEffect será executado uma vez após a montagem do componente\r\n\r\n    const debouncedUpdateQuestionActive = debounce(async (empresaObjId, questionIdentifier, isActive, setCompanyResponses, companyResponses) => {\r\n        try {\r\n            // Atualiza no backend\r\n            await updateQuestionActive(empresaObjId, questionIdentifier, isActive);\r\n    \r\n            // Atualiza localmente após confirmação do backend\r\n            const updatedResponses = companyResponses.map((response) =>\r\n                response.questionIdentifier === questionIdentifier\r\n                    ? { ...response, active: isActive }\r\n                    : response\r\n            );\r\n            setCompanyResponses(updatedResponses);\r\n            console.log(\"Disponibilidade atualizada com sucesso.\");\r\n        } catch (error) {\r\n            console.error(\"Erro ao atualizar disponibilidade:\", error);\r\n        }\r\n    }, 300); // Aguarda 300ms após o último evento\r\n\r\n\r\n    const handleBack = () => {\r\n        //setSidebar(!sidebar)\r\n        navigate(\"/\");\r\n    }\r\n\r\n    const handleSelectQuestionAndAnswer = (message) => {\r\n        setSelectedResponse(message);\r\n    };\r\n\r\n    // Função para atualizar o response do selectedResponse diretamente\r\n    const handleResponseChange = (e) => {\r\n        setSelectedResponse((prevSelectedResponse) => ({\r\n            ...prevSelectedResponse,\r\n            response: e.target.value,\r\n        }));\r\n    };\r\n\r\n    // Função para salvar as alterações no companyResponses\r\n    const handleSaveResponse = async () => {\r\n        if (selectedResponse) {\r\n            // Atualiza a resposta localmente\r\n            const updatedResponses = companyResponses.map((response) =>\r\n                response.questionIdentifier === selectedResponse.questionIdentifier\r\n                    ? { ...response, response: selectedResponse.response } // Atualiza apenas a mensagem de resposta\r\n                    : response\r\n            );\r\n            setCompanyResponses(updatedResponses);\r\n\r\n            try {\r\n                // Chama a API para atualizar o backend\r\n                await updateQuestionResponses(empresaObjId, updatedResponses);\r\n                toast(\"Respostas atualizadas com sucesso!\", { autoClose: 2000, type: \"success\" });\r\n            } catch (error) {\r\n                console.error(\"Erro ao atualizar respostas:\", error);\r\n                toast(\"Ocorreu um erro ao salvar as respostas.\", { autoClose: 2000, type: \"error\" });\r\n            }\r\n        }\r\n    };\r\n\r\n\r\n    // Função genérica para inserir texto no cursor\r\n    const insertAtCursor = (textarea, text) => {\r\n        if (textarea) {\r\n            const startPos = textarea.selectionStart;\r\n            const endPos = textarea.selectionEnd;\r\n            const value = textarea.value;\r\n\r\n            // Atualiza o texto no campo com o novo valor\r\n            textarea.value = value.substring(0, startPos) + text + value.substring(endPos);\r\n\r\n            // Mantém o cursor após o texto inserido\r\n            textarea.selectionStart = textarea.selectionEnd = startPos + text.length;\r\n        }\r\n    };\r\n\r\n    // Funções específicas para cada botão\r\n    const handleInsertNomeCliente = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"{nome_cliente}\");\r\n    };\r\n\r\n    const handleInsertLinkCardapio = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"{link}\");\r\n    };\r\n\r\n    const handleInsertSaudacao = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"Agradecemos a preferência\");\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <PermissionGate permissions={['default']}>\r\n\r\n                <Teste sidebar={sidebar}>\r\n                    <div className=\"w-100 p-4\">\r\n                        {/*<form onSubmit={handleSubmitButton}>*/}\r\n                        <form /*onSubmit={formik.handleSubmit}*/ >\r\n\r\n                            <div className=\"form-header\" style={{ marginBottom: \"0px\" }}>\r\n                                <div className=\"title\">\r\n                                    <h1>Configurações do Robô</h1>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div style={{ display: \"flex\", justifyContent: \"right\"/*, height:\"80px\"*/ }}>\r\n\r\n                                {/*<div className=\"div-buttons\">\r\n                                    <div className=\"continue-button\">\r\n                                        {tela === \"tela1\" ?\r\n                                            <button type=\"button\" onClick={saveChanges} disabled={isSubmitting}>\r\n                                                <AiIcons.AiOutlineSave style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Salvar</a>\r\n                                            </button>\r\n                                            :\r\n                                            <button type=\"button\" disabled={isSubmitting}>\r\n                                                <AiIcons.AiOutlineSave style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Salvar</a>\r\n                                            </button>\r\n                                        }\r\n                                    </div>\r\n\r\n                                    <div className=\"back-button\">\r\n                                        <button onClick={handleBack}>\r\n                                            <SlIcons.SlActionUndo style={{ color: \"#ff4c4c\", marginRight: \"5px\", fontSize: \"18px\", marginBottom: \"2px\" }} /><a >Voltar</a>\r\n                                        </button>\r\n                                    </div>\r\n                                </div>*/}\r\n\r\n                            </div>\r\n\r\n                            <div className=\"contentItemComplete flex-column flex-md-row\">\r\n                                <div className=\"input-group inputGroup-etapasItem\" style={{ height: 50 }}>\r\n                                    <div className={tela === \"tela1\" ? \"etapasAddItem etapaAtiva\" : \"etapasAddItem\"}\r\n                                        style={{ borderBottom: '1px solid lightgray' }} onClick={() => setTela(\"tela1\")}\r\n                                    >\r\n                                        <label>1. Personalizar Mensagens</label>\r\n                                    </div>                                   \r\n                                </div>\r\n\r\n                                <div className=\"input-group mh-100\" style={{ maxWidth: \"80%\" }}>\r\n\r\n                                    <div className=\"formGroupRow\">\r\n                                        {tela === \"tela1\" &&\r\n                                            <div className=\"roboCfg-container\">\r\n                                                <div className=\"roboCfg-sidebar\">\r\n                                                    <h3>Mensagens</h3>\r\n                                                    <ul className=\"roboCfg-message-list\">\r\n                                                        {companyResponses.map((message, index) => (\r\n                                                            <li\r\n                                                                key={index}\r\n                                                                className={`roboCfg-message-item ${selectedResponse?.questionIdentifier === message.questionIdentifier ? 'roboCfg-selected' : ''}`}\r\n                                                                onClick={() => handleSelectQuestionAndAnswer(message)}\r\n                                                            >\r\n                                                                {message.questionType}\r\n                                                                <div className=\"checkBoxContentMsg\">\r\n                                                                    {message.active ? \r\n                                                                        <div className=\"roboCfg-edit-button-ativo\">Ativo</div> \r\n                                                                        : \r\n                                                                        <div className=\"roboCfg-edit-button-inativo\">Inativo</div>\r\n                                                                    }\r\n                                                                    <div className=\"wrapper\">\r\n                                                                        <div className=\"switch_box box_1\">\r\n                                                                            <input\r\n                                                                                type=\"checkbox\"\r\n                                                                                className=\"switch_1\"\r\n                                                                                checked={message.active || false}\r\n                                                                                // Dentro do checkbox\r\n                                                                                onChange={(e) => {\r\n                                                                                    const isActive = e.target.checked;\r\n                                                                                \r\n                                                                                    // Chamar a função debounce para evitar múltiplas chamadas ao backend\r\n                                                                                    debouncedUpdateQuestionActive(empresaObjId, message.questionIdentifier, isActive, setCompanyResponses, companyResponses);\r\n                                                                                }}\r\n                                                                            />\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </li>\r\n                                                        ))}\r\n                                                    </ul>\r\n                                                </div>\r\n                                                <div className=\"roboCfg-main\">\r\n                                                    <div className=\"roboCfg-preview\" style={{ backgroundImage: `url(${backgroundWhatsApp})` }}>\r\n                                                        <div className=\"roboCfg-chat\">\r\n\r\n                                                            {selectedResponse?.question &&\r\n                                                                <Message fromMe={false}>\r\n                                                                    <p>{selectedResponse?.question}</p>\r\n                                                                    <span>12:00</span>\r\n                                                                </Message>\r\n                                                            }\r\n\r\n\r\n                                                            <Message fromMe={true}>\r\n                                                                <p>{selectedResponse?.response}</p>\r\n                                                                <span>12:01</span>\r\n                                                            </Message>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div className=\"roboCfg-editor\">\r\n                                                        <h4>Personalize a mensagem</h4>\r\n                                                        <textarea\r\n                                                            className=\"roboCfg-textarea\"\r\n                                                            value={selectedResponse?.response || \"\"}\r\n                                                            onChange={handleResponseChange} // Altera diretamente o response do selectedResponse\r\n                                                        />\r\n                                                        <div className=\"roboCfg-buttons\">\r\n                                                            <button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertNomeCliente} // Chama a função para Nome do cliente\r\n                                                            >\r\n                                                                Nome do cliente\r\n                                                            </button>\r\n                                                            <button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertLinkCardapio} // Chama a função para Link do cardápio\r\n                                                            >\r\n                                                                Link do cardápio\r\n                                                            </button>\r\n                                                            {/*<button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertSaudacao} // Chama a função para Saudação\r\n                                                            >\r\n                                                                Saudação\r\n                                                            </button>*/}\r\n                                                        </div>\r\n\r\n                                                        <div className=\"roboCfg-save-cancel\">\r\n                                                            <button className=\"roboCfg-save-button\" type=\"button\" onClick={handleSaveResponse}>Salvar</button>\r\n                                                            {/*<button className=\"roboCfg-cancel-button\">Cancelar</button>*/}\r\n                                                        </div>\r\n\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        }\r\n                                        {/*tela === \"tela2\" && (\r\n                                            <div>NADA AQUI POR ENQUANTO</div>\r\n                                        )*/}\r\n                                    </div>\r\n\r\n                                </div>\r\n\r\n\r\n\r\n                            </div>\r\n\r\n                        </form>\r\n                    </div>\r\n                </Teste>\r\n\r\n            </PermissionGate>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default RoboCfg;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,aAAa;AACpB,SAASC,cAAc,QAAQ,iBAAiB;AAChD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAOC,QAAQ,MAAM,WAAW;AAChC,SAASC,iBAAiB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,uBAAuB,EAAEC,oBAAoB,QAAQ,oBAAoB;AAClK,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,kBAAkB,MAAM,sCAAsC;AACrE,OAAOC,QAAQ,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,KAAK,GAAGtB,MAAM,CAACuB,GAAG;AACxB;AACA;AACA,oBAAoB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAdIH,KAAK;AAgBX,MAAMI,OAAO,GAAG1B,MAAM,CAACuB,GAAG;AAC1B,gBAAiBI,KAAK,IAAMA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,SAAU;AACjE;AACA;AACA;AACA;AACA,wBAAyBD,KAAK,IAAMA,KAAK,CAACC,MAAM,GAAG,MAAM,GAAG,KAAM;AAClE;AACA;AACA,mBAAoBD,KAAK,IAAMA,KAAK,CAACC,MAAM,GAAG,KAAK,GAAG,MAAO;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAjBIH,OAAO;AAmBb,MAAMI,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAElB,MAAMC,SAAS,GAAG,uFAAuF;EACzG,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMC,IAAI,GAAG9B,QAAQ,CAAC+B,GAAG,CAACC,OAAO,CAACL,aAAa,EAAED,SAAS,CAAC,CAACO,QAAQ,CAACjC,QAAQ,CAACkC,GAAG,CAACC,IAAI,CAAC;EACvF,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACR,IAAI,CAAC;EAClC,MAAMS,MAAM,GAAGH,SAAS,CAACI,GAAG;EAC5B,MAAMC,OAAO,GAAGb,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMa,YAAY,GAAGL,IAAI,CAACC,KAAK,CAACG,OAAO,CAAC;EACxC,MAAME,SAAS,GAAGD,YAAY,CAACE,UAAU;EACzC,MAAMC,YAAY,GAAGH,YAAY,CAACF,GAAG;EACrC,MAAM,CAACM,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkE,OAAO,EAAEC,UAAU,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsE,IAAI,EAAEC,OAAO,CAAC,GAAGvE,QAAQ,CAAC,OAAO,CAAC;EACzC,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,YAAY,CAAC;EAC9D,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC8E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACgF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACkF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM;IAAEgC,OAAO;IAAEsD;EAAW,CAAC,GAAGrF,UAAU,CAACG,cAAc,CAAC;EAAC,CAAC,CAAC;EAC7D,MAAM,CAACmF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5F,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM6F,QAAQ,GAAG1F,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZ;IACA,MAAM4F,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAI;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,oBAAA;QACA;QACA,MAAMC,WAAW,GAAG,MAAMnF,mBAAmB,CAAC2C,YAAY,CAAC;QAC3D,MAAMyC,oBAAoB,GAAG,MAAMlF,mBAAmB,CAACyC,YAAY,CAAC;QACpE0C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,oBAAoB,CAACG,IAAI,CAACC,SAAS,CAAC;QACrEd,mBAAmB,CAACU,oBAAoB,CAACG,IAAI,CAACC,SAAS,CAAC;QACxDZ,mBAAmB,CAACQ,oBAAoB,CAACG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC3D,KAAAT,qBAAA,GAAII,WAAW,CAACI,IAAI,CAAChD,OAAO,cAAAwC,qBAAA,eAAxBA,qBAAA,CAA0BU,eAAe,EAAE;UAC3C1C,cAAc,CAACoC,WAAW,CAACI,IAAI,CAAChD,OAAO,CAACkD,eAAe,CAAC;QAC5D;QACA,KAAAT,sBAAA,GAAIG,WAAW,CAACI,IAAI,CAAChD,OAAO,cAAAyC,sBAAA,eAAxBA,sBAAA,CAA0BU,eAAe,EAAE;UAC3C,MAAMC,aAAa,GAAGpF,MAAM,CAAC4E,WAAW,CAACI,IAAI,CAAChD,OAAO,CAACmD,eAAe,CAAC,CACjEE,EAAE,CAAC,mBAAmB,CAAC,CACvBC,MAAM,CAAC,kBAAkB,CAAC;UAC/B5C,kBAAkB,CAAC0C,aAAa,CAAC;QACrC;QACA,KAAAV,sBAAA,GAAIE,WAAW,CAACI,IAAI,CAAChD,OAAO,cAAA0C,sBAAA,eAAxBA,sBAAA,CAA0Ba,mBAAmB,EAAE;UAC/CrC,eAAe,CAAC,kBAAkB,CAAC;UACnCY,qBAAqB,CAAC,KAAK,CAAC;QAChC;QACAgB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEH,WAAW,CAACI,IAAI,CAAChD,OAAO,CAACuD,mBAAmB,CAAC;QAC3G3B,oBAAoB,CAACgB,WAAW,CAACI,IAAI,CAAChD,OAAO,CAACuD,mBAAmB,CAAC;QAClE,MAAMC,cAAc,GAAG,MAAM9F,iBAAiB,CAACwC,SAAS,CAAC;QACzD,KAAAyC,oBAAA,GAAIa,cAAc,CAACR,IAAI,cAAAL,oBAAA,eAAnBA,oBAAA,CAAqBa,cAAc,EAAE;UACrClD,aAAa,CAACkD,cAAc,CAACR,IAAI,CAACQ,cAAc,CAAC;QACrD;MACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZX,OAAO,CAACW,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD;MACJ;IACJ,CAAC;IAEDlB,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,CAAC5B,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf,MAAM+C,6BAA6B,GAAGxF,QAAQ,CAAC,OAAOkC,YAAY,EAAEuD,kBAAkB,EAAEC,QAAQ,EAAEzB,mBAAmB,EAAED,gBAAgB,KAAK;IACxI,IAAI;MACA;MACA,MAAMrE,oBAAoB,CAACuC,YAAY,EAAEuD,kBAAkB,EAAEC,QAAQ,CAAC;;MAEtE;MACA,MAAMC,gBAAgB,GAAG3B,gBAAgB,CAAC4B,GAAG,CAAEC,QAAQ,IACnDA,QAAQ,CAACJ,kBAAkB,KAAKA,kBAAkB,GAC5C;QAAE,GAAGI,QAAQ;QAAEC,MAAM,EAAEJ;MAAS,CAAC,GACjCG,QACV,CAAC;MACD5B,mBAAmB,CAAC0B,gBAAgB,CAAC;MACrCf,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAC1D,CAAC,CAAC,OAAOU,KAAK,EAAE;MACZX,OAAO,CAACW,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC9D;EACJ,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;EAGT,MAAMQ,UAAU,GAAGA,CAAA,KAAM;IACrB;IACA3B,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,MAAM4B,6BAA6B,GAAIC,OAAO,IAAK;IAC/C9B,mBAAmB,CAAC8B,OAAO,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIC,CAAC,IAAK;IAChChC,mBAAmB,CAAEiC,oBAAoB,KAAM;MAC3C,GAAGA,oBAAoB;MACvBP,QAAQ,EAAEM,CAAC,CAACE,MAAM,CAACC;IACvB,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIrC,gBAAgB,EAAE;MAClB;MACA,MAAMyB,gBAAgB,GAAG3B,gBAAgB,CAAC4B,GAAG,CAAEC,QAAQ,IACnDA,QAAQ,CAACJ,kBAAkB,KAAKvB,gBAAgB,CAACuB,kBAAkB,GAC7D;QAAE,GAAGI,QAAQ;QAAEA,QAAQ,EAAE3B,gBAAgB,CAAC2B;MAAS,CAAC,CAAC;MAAA,EACrDA,QACV,CAAC;MACD5B,mBAAmB,CAAC0B,gBAAgB,CAAC;MAErC,IAAI;QACA;QACA,MAAMjG,uBAAuB,CAACwC,YAAY,EAAEyD,gBAAgB,CAAC;QAC7D/F,KAAK,CAAC,oCAAoC,EAAE;UAAE4G,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC;MACrF,CAAC,CAAC,OAAOlB,KAAK,EAAE;QACZX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD3F,KAAK,CAAC,yCAAyC,EAAE;UAAE4G,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAQ,CAAC,CAAC;MACxF;IACJ;EACJ,CAAC;;EAGD;EACA,MAAMC,cAAc,GAAGA,CAACC,QAAQ,EAAEC,IAAI,KAAK;IACvC,IAAID,QAAQ,EAAE;MACV,MAAME,QAAQ,GAAGF,QAAQ,CAACG,cAAc;MACxC,MAAMC,MAAM,GAAGJ,QAAQ,CAACK,YAAY;MACpC,MAAMV,KAAK,GAAGK,QAAQ,CAACL,KAAK;;MAE5B;MACAK,QAAQ,CAACL,KAAK,GAAGA,KAAK,CAACW,SAAS,CAAC,CAAC,EAAEJ,QAAQ,CAAC,GAAGD,IAAI,GAAGN,KAAK,CAACW,SAAS,CAACF,MAAM,CAAC;;MAE9E;MACAJ,QAAQ,CAACG,cAAc,GAAGH,QAAQ,CAACK,YAAY,GAAGH,QAAQ,GAAGD,IAAI,CAACM,MAAM;IAC5E;EACJ,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMR,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,gBAAgB,CAAC;EAC9C,CAAC;EAED,MAAMW,wBAAwB,GAAGA,CAAA,KAAM;IACnC,MAAMX,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,QAAQ,CAAC;EACtC,CAAC;EAED,MAAMY,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,MAAMZ,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,2BAA2B,CAAC;EACzD,CAAC;EAED,oBACIzG,OAAA,CAAAE,SAAA;IAAAoH,QAAA,eACItH,OAAA,CAACtB,cAAc;MAAC6I,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAD,QAAA,eAErCtH,OAAA,CAACG,KAAK;QAACE,OAAO,EAAEA,OAAQ;QAAAiH,QAAA,eACpBtH,OAAA;UAAKwH,SAAS,EAAC,WAAW;UAAAF,QAAA,eAEtBtH,OAAA;YAAAsH,QAAA,gBAEItH,OAAA;cAAKwH,SAAS,EAAC,aAAa;cAACC,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAJ,QAAA,eACxDtH,OAAA;gBAAKwH,SAAS,EAAC,OAAO;gBAAAF,QAAA,eAClBtH,OAAA;kBAAAsH,QAAA,EAAI;gBAAqB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN9H,OAAA;cAAKyH,KAAK,EAAE;gBAAEM,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,OAAO;cAAoB;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBvE,CAAC,eAEN9H,OAAA;cAAKwH,SAAS,EAAC,6CAA6C;cAAAF,QAAA,gBACxDtH,OAAA;gBAAKwH,SAAS,EAAC,mCAAmC;gBAACC,KAAK,EAAE;kBAAEQ,MAAM,EAAE;gBAAG,CAAE;gBAAAX,QAAA,eACrEtH,OAAA;kBAAKwH,SAAS,EAAE7E,IAAI,KAAK,OAAO,GAAG,0BAA0B,GAAG,eAAgB;kBAC5E8E,KAAK,EAAE;oBAAES,YAAY,EAAE;kBAAsB,CAAE;kBAACC,OAAO,EAAEA,CAAA,KAAMvF,OAAO,CAAC,OAAO,CAAE;kBAAA0E,QAAA,eAEhFtH,OAAA;oBAAAsH,QAAA,EAAO;kBAAyB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN9H,OAAA;gBAAKwH,SAAS,EAAC,oBAAoB;gBAACC,KAAK,EAAE;kBAAEW,QAAQ,EAAE;gBAAM,CAAE;gBAAAd,QAAA,eAE3DtH,OAAA;kBAAKwH,SAAS,EAAC,cAAc;kBAAAF,QAAA,EACxB3E,IAAI,KAAK,OAAO,iBACb3C,OAAA;oBAAKwH,SAAS,EAAC,mBAAmB;oBAAAF,QAAA,gBAC9BtH,OAAA;sBAAKwH,SAAS,EAAC,iBAAiB;sBAAAF,QAAA,gBAC5BtH,OAAA;wBAAAsH,QAAA,EAAI;sBAAS;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClB9H,OAAA;wBAAIwH,SAAS,EAAC,sBAAsB;wBAAAF,QAAA,EAC/BxD,gBAAgB,CAAC4B,GAAG,CAAC,CAACK,OAAO,EAAEsC,KAAK,kBACjCrI,OAAA;0BAEIwH,SAAS,EAAE,wBAAwB,CAAAxD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuB,kBAAkB,MAAKQ,OAAO,CAACR,kBAAkB,GAAG,kBAAkB,GAAG,EAAE,EAAG;0BACnI4C,OAAO,EAAEA,CAAA,KAAMrC,6BAA6B,CAACC,OAAO,CAAE;0BAAAuB,QAAA,GAErDvB,OAAO,CAACuC,YAAY,eACrBtI,OAAA;4BAAKwH,SAAS,EAAC,oBAAoB;4BAAAF,QAAA,GAC9BvB,OAAO,CAACH,MAAM,gBACX5F,OAAA;8BAAKwH,SAAS,EAAC,2BAA2B;8BAAAF,QAAA,EAAC;4BAAK;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,gBAEtD9H,OAAA;8BAAKwH,SAAS,EAAC,6BAA6B;8BAAAF,QAAA,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAE9D9H,OAAA;8BAAKwH,SAAS,EAAC,SAAS;8BAAAF,QAAA,eACpBtH,OAAA;gCAAKwH,SAAS,EAAC,kBAAkB;gCAAAF,QAAA,eAC7BtH,OAAA;kCACIuG,IAAI,EAAC,UAAU;kCACfiB,SAAS,EAAC,UAAU;kCACpBe,OAAO,EAAExC,OAAO,CAACH,MAAM,IAAI;kCAC3B;kCAAA;kCACA4C,QAAQ,EAAGvC,CAAC,IAAK;oCACb,MAAMT,QAAQ,GAAGS,CAAC,CAACE,MAAM,CAACoC,OAAO;;oCAEjC;oCACAjD,6BAA6B,CAACtD,YAAY,EAAE+D,OAAO,CAACR,kBAAkB,EAAEC,QAAQ,EAAEzB,mBAAmB,EAAED,gBAAgB,CAAC;kCAC5H;gCAAE;kCAAA6D,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACL;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACD;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC;wBAAA,GA3BDO,KAAK;0BAAAV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA4BV,CACP;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN9H,OAAA;sBAAKwH,SAAS,EAAC,cAAc;sBAAAF,QAAA,gBACzBtH,OAAA;wBAAKwH,SAAS,EAAC,iBAAiB;wBAACC,KAAK,EAAE;0BAAEgB,eAAe,EAAE,OAAO5I,kBAAkB;wBAAI,CAAE;wBAAAyH,QAAA,eACtFtH,OAAA;0BAAKwH,SAAS,EAAC,cAAc;0BAAAF,QAAA,GAExB,CAAAtD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE0E,QAAQ,kBACvB1I,OAAA,CAACO,OAAO;4BAACE,MAAM,EAAE,KAAM;4BAAA6G,QAAA,gBACnBtH,OAAA;8BAAAsH,QAAA,EAAItD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE0E;4BAAQ;8BAAAf,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnC9H,OAAA;8BAAAsH,QAAA,EAAM;4BAAK;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC,eAId9H,OAAA,CAACO,OAAO;4BAACE,MAAM,EAAE,IAAK;4BAAA6G,QAAA,gBAClBtH,OAAA;8BAAAsH,QAAA,EAAItD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2B;4BAAQ;8BAAAgC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnC9H,OAAA;8BAAAsH,QAAA,EAAM;4BAAK;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN9H,OAAA;wBAAKwH,SAAS,EAAC,gBAAgB;wBAAAF,QAAA,gBAC3BtH,OAAA;0BAAAsH,QAAA,EAAI;wBAAsB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC/B9H,OAAA;0BACIwH,SAAS,EAAC,kBAAkB;0BAC5BpB,KAAK,EAAE,CAAApC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2B,QAAQ,KAAI,EAAG;0BACxC6C,QAAQ,EAAExC,oBAAqB,CAAC;wBAAA;0BAAA2B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC,CAAC,eACF9H,OAAA;0BAAKwH,SAAS,EAAC,iBAAiB;0BAAAF,QAAA,gBAC5BtH,OAAA;4BACIwH,SAAS,EAAC,gBAAgB;4BAC1BjB,IAAI,EAAC,QAAQ;4BACb4B,OAAO,EAAElB,uBAAwB,CAAC;4BAAA;4BAAAK,QAAA,EACrC;0BAED;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACT9H,OAAA;4BACIwH,SAAS,EAAC,gBAAgB;4BAC1BjB,IAAI,EAAC,QAAQ;4BACb4B,OAAO,EAAEf,wBAAyB,CAAC;4BAAA;4BAAAE,QAAA,EACtC;0BAED;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAQR,CAAC,eAEN9H,OAAA;0BAAKwH,SAAS,EAAC,qBAAqB;0BAAAF,QAAA,eAChCtH,OAAA;4BAAQwH,SAAS,EAAC,qBAAqB;4BAACjB,IAAI,EAAC,QAAQ;4BAAC4B,OAAO,EAAE9B,kBAAmB;4BAAAiB,QAAA,EAAC;0BAAM;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEjG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEI;EAAC,gBACnB,CAAC;AAEX,CAAC;AAAAlH,EAAA,CApUKD,OAAO;EAAA,QA4BQnC,WAAW;AAAA;AAAAmK,GAAA,GA5B1BhI,OAAO;AAsUb,eAAeA,OAAO;AAAC,IAAAL,EAAA,EAAAI,GAAA,EAAAiI,GAAA;AAAAC,YAAA,CAAAtI,EAAA;AAAAsI,YAAA,CAAAlI,GAAA;AAAAkI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}