import React, { useState, useEffect, useContext, useMemo, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import logoPedeJA from '../../img/logoPedeJA.png';
import { addEnderecoEmpresa, createAsaasCustomer, getEmpresa, updateEmpresaAddress, getProgressoConfiguracaoInicial, atualizarProgressoConfiguracaoInicial, updateTypeRegion, updateRaioEntregaEmpresa, updateBairroEntregaEmpresa, deleteRaioEntrega, deleteBairroEntrega, getBairros, updateTempoHorarioFuncionamento, getEmpresaWithObjId, importCardapioIfood, importCardapioAnotaai, apiCheckImportStatus, enviarCardapioParaEquipe, getProgressoImportacao, resetImportStatus, updateConfiguracoesEntrega, getQrCodeWhatsapp, removeWhatsappSession } from '../../services/api';
import { AuthContext } from '../../contexts/auth';
import { useFormik } from "formik";
import * as Yup from "yup";
import InputMask from 'react-input-mask';
import axios from 'axios';
import RegistrationStepper from '../../components/ui/RegistrationStepper';
import { AiOutlineCheck, AiOutlineHome, AiOutlineUser, AiOutlineCheckCircle, AiOutlineSave, AiOutlineQuestionCircle, AiOutlineInfoCircle, AiOutlineFileText } from 'react-icons/ai';
import { FaExclamationCircle, FaEdit, FaTrash, FaRegTrashAlt, FaClock, FaInfoCircle, FaFileUpload, FaHandshake, FaUtensils, FaLink, FaFilePdf, FaCloudUploadAlt, FaTruck, FaWhatsapp, FaCheckCircle } from 'react-icons/fa';
import { MdLocationPin, MdDeliveryDining, MdSchedule, MdHelp, MdRestaurantMenu, MdCreateNewFolder, MdStorefront } from 'react-icons/md';
import { LuPlusCircle } from "react-icons/lu";
import { BsQuestionCircleFill, BsPlusCircle } from 'react-icons/bs';
import Map from '../Endereco/index';
import CryptoJS from 'crypto-js';
import io from 'socket.io-client';
import { ProgressBar } from 'react-bootstrap';
import Loading from "react-loading";
import './styles.css';

const ConfiguracaoInicial = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { authenticated, user } = useContext(AuthContext);
    const { empresaId, isNewUser } = location.state || {};

    const [currentStep, setCurrentStep] = useState(1);
    const [completedSteps, setCompletedSteps] = useState([1]); // Step 1 já concluído
    const [loading, setLoading] = useState(false);
    const [cepLoading, setCepLoading] = useState(false);
    const [isAuthenticated, setIsAuthenticated] = useState(authenticated);
    const [isCheckingAuth, setIsCheckingAuth] = useState(true);

    // Estados para coordenadas do mapa
    const [latitudeEmpresa, setLatitudeEmpresa] = useState(null);
    const [longitudeEmpresa, setLongitudeEmpresa] = useState(null);
    const [empresaData, setEmpresaData] = useState(null);

    // Estados para área de entrega
    const [typeOfRegion, setTypeOfRegion] = useState("");
    const [raio, setRaio] = useState(1);
    const [valorEntrega, setValorEntrega] = useState('');
    const [arrayRaioEntrega, setArrayRaioEntrega] = useState([]);
    const [arrayBairroEntrega, setArrayBairroEntrega] = useState([]);
    const [bairroEntrega, setBairroEntrega] = useState("");
    const [city, setCity] = useState(null);
    const [uf, setUf] = useState(null);
    const [isFocused, setIsFocused] = useState(false);

    // Estados para horários de funcionamento
    const [availability, setAvailability] = useState('especifico');
    const [activeDays, setActiveDays] = useState(new Set());
    const [timeZones, setTimeZones] = useState([]);
    const [selectedTimeZone, setSelectedTimeZone] = useState('America/Sao_Paulo');
    const initialTimeRange = { start: '08:00', end: '18:00' };
    const initialTimeRanges = {
        domingo: [initialTimeRange],
        segunda: [initialTimeRange],
        terca: [initialTimeRange],
        quarta: [initialTimeRange],
        quinta: [initialTimeRange],
        sexta: [initialTimeRange],
        sabado: [initialTimeRange],
    };
    const [timeRanges, setTimeRanges] = useState(initialTimeRanges);
    const daysOfWeek = ['domingo', 'segunda', 'terca', 'quarta', 'quinta', 'sexta', 'sabado'];

    // Estados para tooltips e ajuda
    const [showTooltip, setShowTooltip] = useState(null);
    const [showHelpModal, setShowHelpModal] = useState(false);

    // Estados para cardápio
    const [cardapioOption, setCardapioOption] = useState('');
    const [importProgress, setImportProgress] = useState(null);
    const [importComplete, setImportComplete] = useState(false);
    const [isImporting, setIsImporting] = useState(false);
    const [importMessage, setImportMessage] = useState('');
    const [importStage, setImportStage] = useState('');
    const [importDetails, setImportDetails] = useState({});
    const [cardapioFormData, setCardapioFormData] = useState({
        ifoodLink: '',
        anotaaiLink: '',
        customLink: '',
        pdfFile: null,
        observacoes: ''
    });
    const [showVideo, setShowVideo] = useState(false);
    const [videoType, setVideoType] = useState('');
    const [socket, setSocket] = useState(null);
    
    // Estados para o switch de importação
    const [selectedImportSource, setSelectedImportSource] = useState('ifood'); // 'ifood' ou 'anotaai'
    
    // Estados para formas de entrega
    const [entregaDisabled, setEntregaDisabled] = useState(false);
    const [retiradaDisabled, setRetiradaDisabled] = useState(false);
    
    // Estados para WhatsApp
    const [qrCodeImg, setQrCodeImg] = useState('');
    const [generatingQrCode, setGeneratingQrCode] = useState(false);
    const [isWhatsappLoged, setIsWhatsappLoged] = useState(false);
    const [whatsappConnected, setWhatsappConnected] = useState(false);
    const [userRequestedQrCode, setUserRequestedQrCode] = useState(false); // 🔧 NOVO: Controla se usuário solicitou QR Code
    const timeoutRef = useRef(null);
    const isMountedRef = useRef(true);
    const cancelQrCodeFetchRef = useRef(false);



    // Verificação de autenticação
    useEffect(() => {
        const userEncrypted = localStorage.getItem('user');
        const empresa = localStorage.getItem('empresa');
        
        // Se não há dados no localStorage ou no contexto, redirecionar para login
        if (!userEncrypted || !empresa || !user) {
            console.log('Usuário não autenticado, redirecionando para login');
            navigate('/login');
            return;
        }
        
        // Verificar se os dados podem ser descriptografados
        try {
            const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';
            const userDecrypted = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);
            if (!userDecrypted) {
                console.log('Erro ao descriptografar dados do usuário, redirecionando para login');
                navigate('/login');
                return;
            }
            const userParse = JSON.parse(userDecrypted);
            if (!userParse || !userParse._id) {
                console.log('Dados do usuário inválidos, redirecionando para login');
                navigate('/login');
                return;
            }
            
            // Se chegou até aqui, usuário está autenticado
            setIsAuthenticated(true);
            setIsCheckingAuth(false);
        } catch (error) {
            console.error('Erro ao verificar autenticação:', error);
            navigate('/login');
            return;
        }
    }, [navigate, user]);

    // Estados para dados do usuário processados
    const [userParse, setUserParse] = useState(null);
    const [empresaParse, setEmpresaParse] = useState(null);
    const [userID, setUserID] = useState(null);
    const [objIdEmpresa, setObjIdEmpresa] = useState(null);

    // Processar dados do usuário uma única vez após autenticação
    useEffect(() => {
        if (!isAuthenticated) return;
        
        const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';
        const userEncrypted = localStorage.getItem('user');
        const empresa = localStorage.getItem('empresa');
        
        if (userEncrypted && empresa) {
            try {
                const userDecrypted = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);
                const userParseTemp = JSON.parse(userDecrypted);
                const empresaParseTemp = JSON.parse(empresa);
                
                setUserParse(userParseTemp);
                setEmpresaParse(empresaParseTemp);
                setUserID(userParseTemp._id);
                setObjIdEmpresa(empresaParseTemp._id);
                
                console.log('Dados do usuário processados:', {
                    'user.vinculo_empresa (ObjectId)': user?.vinculo_empresa,
                    'empresaParse._id (pode ser CNPJ)': empresaParseTemp?._id,
                    'empresaParse.id_empresa': empresaParseTemp?.id_empresa
                });
            } catch (error) {
                console.error('Erro ao processar dados do usuário:', error);
            }
        }
    }, [isAuthenticated, user?.vinculo_empresa]); // Dependências específicas e estáveis

    // Configurar socket.io para feedback em tempo real da importação
    useEffect(() => {
        if (!isAuthenticated || !empresaParse?.id_empresa) return;

        const isDevelopment = window.location.hostname === 'localhost';
        const apiUrl = isDevelopment 
            ? process.env.REACT_APP_SERVER_URL_DEV 
            : process.env.REACT_APP_SERVER_URL_PROD;

        // Conectar ao servidor WebSocket
        const newSocket = io(apiUrl, { 
            withCredentials: true, 
            transports: ['websocket'], 
            auth: { token: localStorage.getItem('token') } 
        });

        // Entrar na sala da empresa
        newSocket.emit('joinCompanyRoom', { 
            companyId: empresaParse._id.toString(), 
            clientId: 'configClient' 
        });

        // Escutar eventos de progresso da importação
        newSocket.on('import-progress', (data) => {
            console.log("Progresso da importação recebido:", data);
            setImportProgress(data.progress);
            setImportMessage(data.message || '');
            setImportStage(data.stage || '');
            setImportDetails(data.details || {});
            
            if (data.progress === 100 && data.stage === 'finalizado') {
                setImportComplete(true);
                setIsImporting(false);
                // Não chamar handleFinishCardapioImport aqui para evitar loop
                // A função será chamada quando o usuário clicar no botão
                console.log("Importação finalizada - aguardando ação do usuário");
            }
            
            if (data.stage === 'erro') {
                setIsImporting(false);
                setImportProgress(null);
                toast.error(data.message || 'Erro durante importação');
            }
        });

        setSocket(newSocket);

        return () => {
            newSocket.off('import-progress');
            newSocket.disconnect();
        };
    }, [isAuthenticated, empresaParse?.id_empresa]); // Dependência específica

    // useEffect separado para verificar progresso salvo - EXECUTAR APENAS UMA VEZ
    useEffect(() => {
        let isMounted = true;
        
        const verificarProgressoInicial = async () => {
            if (!isAuthenticated || !empresaParse?.id_empresa) return;
            
            try {
                const response = await getProgressoImportacao(empresaParse.id_empresa);
                const { importacao_finalizada, status_importacao, progresso_importacao } = response.data;
                
                if (!isMounted) return; // Verificar se o componente ainda está montado
                
                if (importacao_finalizada || status_importacao === 'concluida') {
                    setImportComplete(true);
                    setImportProgress(100);
                    setImportMessage('Importação finalizada com sucesso!');
                    setImportStage('finalizado');
                } else if (status_importacao === 'erro') {
                    setIsImporting(false);
                    setImportProgress(0);
                    setImportMessage('Erro durante importação');
                    setImportStage('erro');
                } else if (status_importacao === 'em_andamento' || status_importacao === 'iniciando' || status_importacao === 'finalizando') {
                    setIsImporting(true);
                    setImportProgress(progresso_importacao?.porcentagem || 0);
                    setImportMessage(progresso_importacao?.etapa_atual || 'Processando...');
                    setImportStage('em_andamento');
                }
            } catch (error) {
                console.error("Erro ao verificar progresso da importação:", error);
            }
        };

        verificarProgressoInicial();

        return () => {
            isMounted = false;
        };
    }, [isAuthenticated, empresaParse?.id_empresa]); // Dependências específicas

    // Verificar se os dados necessários estão disponíveis
    const isDataReady = useMemo(() => {
        return isAuthenticated && userParse && empresaParse && userID && objIdEmpresa;
    }, [isAuthenticated, userParse, empresaParse, userID, objIdEmpresa]);

    const steps = [
        { 
            id: 1, 
            title: 'Informações Pessoais', 
            description: 'Dados já cadastrados'
        },
        { 
            id: 2, 
            title: 'Endereço', 
            description: 'Localização do estabelecimento' 
        },
        { 
            id: 3, 
            title: 'Posicionamento no Mapa', 
            description: 'Localização precisa' 
        },
        { 
            id: 4, 
            title: 'Área de Entrega', 
            description: 'Configurar região de entrega' 
        },
        { 
            id: 5, 
            title: 'Horários de Funcionamento', 
            description: 'Configurar horários de atendimento' 
        },
        { 
            id: 6, 
            title: 'Adicionar Cardápio', 
            description: 'Configurar seu cardápio' 
        },
        { 
            id: 7, 
            title: 'Formas de Entrega', 
            description: 'Configurar tipos de serviço' 
        },
        { 
            id: 8, 
            title: 'Sincronizar WhatsApp', 
            description: 'Conectar WhatsApp' 
        },
        { 
            id: 9, 
            title: 'Finalização', 
            description: 'Revisar e finalizar configuração' 
        }
    ];

    const EnderecoSchema = Yup.object().shape({
        cep: Yup.string().required('CEP é obrigatório'),
        estado: Yup.string().required('Estado é obrigatório'),
        municipio: Yup.string().required('Município é obrigatório'),
        bairro: Yup.string().required('Bairro é obrigatório'),
        logradouro: Yup.string().required('Logradouro é obrigatório'),
        address_number: Yup.string().required('Número é obrigatório'),
    });

    const AreaEntregaSchema = Yup.object().shape({
        bairro_entrega: Yup.string().when('typeOfRegion', {
            is: 'bairro',
            then: Yup.string().required('Bairro é obrigatório'),
            otherwise: Yup.string()
        }),
    });

    const formikEndereco = useFormik({
        validationSchema: EnderecoSchema,
        initialValues: {
            cep: '',
            estado: '',
            municipio: '',
            bairro: '',
            logradouro: '',
            address_number: '',
            complemento: ''
        },
        onSubmit: async (values) => {
            await handleSaveEndereco(values);
        }
    });

    const formikAreaEntrega = useFormik({
        validationSchema: AreaEntregaSchema,
        initialValues: {
            bairro_entrega: '',
        },
        onSubmit: async (values) => {
            if (typeOfRegion === 'bairro') {
                await handleAddBairro(values.bairro_entrega);
            }
        }
    });

    // Função para buscar CEP na ViaCEP
    const buscarCEP = async (cep) => {
        if (!isAuthenticated) {
            console.warn('Não é possível buscar CEP - usuário não autenticado');
            return;
        }
        
        setCepLoading(true);
        try {
            const cepLimpo = cep.replace(/\D/g, '');
            
            if (cepLimpo.length === 8) {
                const response = await axios.get(`https://viacep.com.br/ws/${cepLimpo}/json/`);
                
                if (response.data && !response.data.erro) {
                    const { logradouro, bairro, localidade, uf } = response.data;
                    
                    // Preencher os campos automaticamente
                    formikEndereco.setValues({
                        ...formikEndereco.values,
                        cep: cep,
                        estado: uf,
                        municipio: localidade,
                        bairro: bairro,
                        logradouro: logradouro
                    });
                    
                    toast.success('CEP encontrado! Dados preenchidos automaticamente.', { autoClose: 3000 });
                } else {
                    toast.error('CEP não encontrado. Verifique o CEP digitado.', { autoClose: 5000 });
                }
            }
        } catch (error) {
            console.error('Erro ao buscar CEP:', error);
            toast.error('Erro ao buscar CEP. Tente novamente.', { autoClose: 5000 });
        } finally {
            setCepLoading(false);
        }
    };

    // Função para detectar quando o CEP foi completamente digitado
    const handleCepChange = (e) => {
        if (!isAuthenticated) {
            console.warn('Não é possível alterar CEP - usuário não autenticado');
            return;
        }
        
        const cep = e.target.value;
        formikEndereco.setFieldValue('cep', cep);
        
        // Buscar automaticamente quando o CEP estiver completo (8 dígitos)
        const cepLimpo = cep.replace(/\D/g, '');
        if (cepLimpo.length === 8) {
            buscarCEP(cep);
        }
    };

    // Carregar dados da empresa - só executar se estiver autenticado
    useEffect(() => {
        const loadEmpresaData = async () => {
            // Verificar se está autenticado e tem dados necessários
            if (!isAuthenticated || !userID || !empresaParse?.id_empresa || !objIdEmpresa) {
                console.log('Não carregando dados - usuário não autenticado ou dados incompletos');
                return;
            }

            try {
                const response = await getEmpresa(userID, empresaParse.id_empresa);
                const empresa = response.data.empresa;
                setEmpresaData(empresa);
                
                // Só definir coordenadas se a empresa tiver coordenadas válidas cadastradas no banco
                // Não usar coordenadas padrão
                if (empresa.latitude && empresa.longitude && 
                    empresa.latitude !== '0' && empresa.longitude !== '0' &&
                    empresa.latitude !== 0 && empresa.longitude !== 0 &&
                    parseFloat(empresa.latitude) !== -15.7942 && // Não aceitar coordenada padrão de Brasília
                    parseFloat(empresa.longitude) !== -47.8822) {
                    
                    console.log('✅ Carregando coordenadas válidas do banco:', {
                        latitude: empresa.latitude,
                        longitude: empresa.longitude
                    });
                    setLatitudeEmpresa(parseFloat(empresa.latitude));
                    setLongitudeEmpresa(parseFloat(empresa.longitude));
                } else {
                    console.log('❌ Empresa não possui coordenadas válidas cadastradas no banco');
                    // Não definir coordenadas padrão - aguardar que o usuário posicione no mapa
                    setLatitudeEmpresa(null);
                    setLongitudeEmpresa(null);
                }

                // Carregar dados de área de entrega
                setCity(empresa.municipio);
                setUf(empresa.estado);
                
                // Verificar ambos os campos para compatibilidade
                const tipoRegiao = empresa.type_of_region || empresa.region_type_delivery;
                if (tipoRegiao) {
                    console.log('Tipo de região encontrado:', tipoRegiao);
                    setTypeOfRegion(tipoRegiao);
                    
                    if (tipoRegiao === 'raio' && empresa.raios_entrega) {
                        setArrayRaioEntrega(empresa.raios_entrega);
                    } else if (tipoRegiao === 'bairro' && empresa.bairros_entrega) {
                        setArrayBairroEntrega(empresa.bairros_entrega);
                    }
                    // Se type_of_region é 'skip', não precisa carregar arrays
                }

                // Tentar carregar progresso salvo no banco
                try {
                    const progressResponse = await getProgressoConfiguracaoInicial(objIdEmpresa);
                    const progresso = progressResponse.data.progresso;
                    
                    console.log('Progresso carregado do banco:', progresso);
                    
                    // Usar APENAS o progresso salvo no banco
                    // Não fazer validações automáticas baseadas em dados
                    setCurrentStep(progresso.etapa_atual);
                    setCompletedSteps([...progresso.etapas_completas]);
                    
                    console.log('Estado definido baseado apenas no banco:', {
                        etapa_atual: progresso.etapa_atual,
                        etapas_completas: progresso.etapas_completas
                    });
                } catch (progressError) {
                    console.warn('Erro ao carregar progresso salvo, iniciando do começo:', progressError);
                    // Se não conseguir carregar progresso, começar do início
                    setCurrentStep(2);
                    setCompletedSteps([1]); // Apenas etapa 1 (dados pessoais) completa
                }
            } catch (error) {
                console.error('Erro ao carregar dados da empresa:', error);
                // Em caso de erro, não definir coordenadas padrão
                setLatitudeEmpresa(null);
                setLongitudeEmpresa(null);
                // Começar do início
                setCurrentStep(2);
                setCompletedSteps([1]);
            }
        };

        loadEmpresaData();
    }, [isAuthenticated, userID, empresaParse?.id_empresa, objIdEmpresa]); // Dependências específicas

    // Carregar fusos horários
    useEffect(() => {
        const fetchTimeZones = async () => {
            if (!isAuthenticated) return;
            
            try {
                const response = await axios.get('https://api.timezonedb.com/v2.1/list-time-zone', {
                    params: {
                        key: '7UIGZ1GOJE0U',
                        format: 'json',
                        country: 'BR'
                    }
                });
                setTimeZones(response.data.zones);
            } catch (error) {
                console.error('Erro ao buscar fusos horários', error);
                // Fallback com alguns fusos horários brasileiros
                setTimeZones([
                    { zoneName: 'America/Sao_Paulo' },
                    { zoneName: 'America/Manaus' },
                    { zoneName: 'America/Campo_Grande' },
                    { zoneName: 'America/Cuiaba' }
                ]);
            }
        };

        fetchTimeZones();
    }, [isAuthenticated]);

    // Carregar configurações de entrega existentes
    useEffect(() => {
        const fetchConfiguracoesEntrega = async () => {
            if (!isAuthenticated || !objIdEmpresa) return;

            try {
                const empresaTemp = await getEmpresaWithObjId(objIdEmpresa);
                console.log("Configurações de entrega carregadas:", empresaTemp.data);
                setEntregaDisabled(empresaTemp.data.empresa.entrega_disabled || false);
                setRetiradaDisabled(empresaTemp.data.empresa.retirada_disabled || false);
            } catch (error) {
                console.error("Erro ao buscar configurações de entrega:", error);
            }
        };

        fetchConfiguracoesEntrega();
    }, [isAuthenticated, objIdEmpresa]);

    // Cleanup para os timeouts do WhatsApp
    useEffect(() => {
        return () => {
            isMountedRef.current = false;
            cancelQrCodeFetchRef.current = true;
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
                console.log('Timeout WhatsApp cleared on unmount');
            }
        };
    }, []);

    // Inicializar estado quando entrar na etapa 8 (WhatsApp) - SEM gerar QR Code automaticamente
    useEffect(() => {
        if (currentStep === 8 && isAuthenticated && empresaParse?.id_empresa && !whatsappConnected && !isWhatsappLoged) {
            console.log('Entrando na etapa 8 - aguardando comando manual do usuário para gerar QR Code');
            // Resetar estados
            isMountedRef.current = true;
            cancelQrCodeFetchRef.current = false;
            setUserRequestedQrCode(false); // 🔧 NOVO: Resetar flag para mostrar botão
            // 🔧 REMOVIDO: fetchQrCode(); - Não gerar QR Code automaticamente
        }
    }, [currentStep, isAuthenticated, empresaParse?.id_empresa]);

    // Carregar dados de horários existentes
    useEffect(() => {
        const fetchHorarioData = async () => {
            if (!isAuthenticated || !objIdEmpresa) return;

            try {
                const empresaTemp = await getEmpresaWithObjId(objIdEmpresa);
                console.log('Dados da empresa para horários:', empresaTemp);
                
                if (empresaTemp && empresaTemp.data.empresa.horario_funcionamento) {
                    setTimeRanges(empresaTemp.data.empresa.horario_funcionamento);
                    const daysActive = new Set(Object.keys(empresaTemp.data.empresa.horario_funcionamento));
                    setActiveDays(daysActive);
                }
                
                if (empresaTemp && empresaTemp.data.empresa.status_loja) {
                    setAvailability(empresaTemp.data.empresa.status_loja);
                }

                if (empresaTemp && empresaTemp.data.empresa.timezone) {
                    setSelectedTimeZone(empresaTemp.data.empresa.timezone);
                } else {
                    setSelectedTimeZone('America/Sao_Paulo');
                }
            } catch (error) {
                console.error("Erro ao buscar dados de horários da empresa:", error);
            }
        };

        fetchHorarioData();
    }, [isAuthenticated, objIdEmpresa]);



    // Preencher formulário de endereço quando dados da empresa estiverem disponíveis
    useEffect(() => {
        if (isAuthenticated && empresaData && empresaData.cep) {
            console.log('Preenchendo formulário com dados existentes:', empresaData);
            formikEndereco.setValues({
                cep: empresaData.cep || '',
                estado: empresaData.estado || '',
                municipio: empresaData.municipio || '',
                bairro: empresaData.bairro || '',
                logradouro: empresaData.logradouro || '',
                address_number: empresaData.address_number || '',
                complemento: empresaData.complemento || ''
            });
        }
    }, [empresaData, isAuthenticated]); // Removida dependência problemática

    const handleSaveEndereco = async (enderecoData) => {
        // Verificar se está autenticado e tem dados necessários
        if (!isAuthenticated || !objIdEmpresa) {
            console.warn('Não é possível salvar endereço - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }
        
        setLoading(true);
        try {
            const empresaObjId = objIdEmpresa; // ID da empresa do contexto

            if (!empresaObjId) {
                toast('Erro: ID da empresa não encontrado. Faça login novamente.', { type: 'error' });
                return;
            }

            console.log('Salvando endereço para empresa ObjectId:', empresaObjId);

            // Adicionar endereço
            await addEnderecoEmpresa(empresaObjId, enderecoData);
            toast('Endereço salvo com sucesso!', { type: 'success' });

            // Criar cliente Asaas (se ainda não existir)
            try {
                console.log('Tentando criar cliente Asaas para empresa:', empresaObjId);
                console.log('Dados para Asaas:', {
                    address_number: enderecoData.address_number,
                    cep: enderecoData.cep
                });
                
                const asaasResponse = await createAsaasCustomer(empresaObjId, enderecoData.address_number, enderecoData.cep);
                console.log('Cliente Asaas criado/atualizado:', asaasResponse.data);
                
                if (asaasResponse.data.success) {
                    console.log('✅ Cliente Asaas configurado com sucesso');
                } else {
                    console.warn('⚠️ Possível problema com cliente Asaas:', asaasResponse.data.message);
                }
            } catch (asaasError) {
                console.error('Erro ao criar cliente Asaas:', asaasError);
                toast('Endereço salvo, mas houve um problema ao configurar o sistema de pagamento. Você pode continuar.', { 
                    type: 'warning', 
                    autoClose: 5000 
                });
            }

            // AGUARDAR AS COORDENADAS SEREM CARREGADAS ANTES DE AVANÇAR
            console.log('🔄 Recarregando dados da empresa para obter coordenadas atualizadas...');
            
            // Mostrar feedback ao usuário sobre o processo de geocode
            //toast('Obtendo coordenadas do endereço...', { type: 'info', autoClose: 3000 });
            
            // Recarregar dados da empresa para obter as coordenadas do geocode
            const empresaResponse = await getEmpresa(userID, empresaParse.id_empresa);
            const empresaAtualizada = empresaResponse.data.empresa;
            
            console.log('📍 Coordenadas obtidas:', {
                latitude: empresaAtualizada.latitude,
                longitude: empresaAtualizada.longitude
            });

            // Verificar se as coordenadas foram obtidas com sucesso
            if (empresaAtualizada.latitude && empresaAtualizada.longitude && 
                empresaAtualizada.latitude !== '0' && empresaAtualizada.longitude !== '0' &&
                empresaAtualizada.latitude !== 0 && empresaAtualizada.longitude !== 0) {
                
                // Atualizar o state com as novas coordenadas
                setLatitudeEmpresa(parseFloat(empresaAtualizada.latitude));
                setLongitudeEmpresa(parseFloat(empresaAtualizada.longitude));
                setEmpresaData(empresaAtualizada);
                
                console.log('✅ Coordenadas atualizadas no state:', {
                    latitude: parseFloat(empresaAtualizada.latitude),
                    longitude: parseFloat(empresaAtualizada.longitude)
                });
                
                /*toast('✅ Coordenadas obtidas com sucesso! Você pode ajustar a posição no mapa.', { 
                    type: 'success', 
                    autoClose: 4000 
                });*/
            } else {
                console.warn('⚠️ Coordenadas não foram obtidas pelo geocode, usando localização padrão');
                // Definir coordenadas padrão de Brasília para centralizar o mapa
                setLatitudeEmpresa(-15.7942);
                setLongitudeEmpresa(-47.8822);
                
                toast('⚠️ Não foi possível obter coordenadas automáticas. Posicione o marcador no mapa.', { 
                    type: 'warning', 
                    autoClose: 5000 
                });
            }

            // Marcar etapa 2 como completa e avançar para etapa 3
            const novasEtapasCompletas = [...completedSteps];
            if (!novasEtapasCompletas.includes(2)) {
                novasEtapasCompletas.push(2);
            }
            
            setCompletedSteps(novasEtapasCompletas);
            setCurrentStep(3);

            // Salvar progresso no banco de dados
            try {
                if (objIdEmpresa) {
                    await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 2, 3);
                    console.log('Progresso salvo: etapa 2 (endereço) completa, avançando para etapa 3');
                }
            } catch (error) {
                console.warn('Erro ao salvar progresso da configuração inicial:', error);
            }

        } catch (error) {
            console.error('Erro ao salvar endereço:', error);
            toast(error?.response?.data?.msg || 'Erro ao salvar endereço', { type: 'error' });
        } finally {
            setLoading(false);
        }
    };

    const handleNextStep = async () => {
        if (!isAuthenticated) {
            console.warn('Não é possível navegar - usuário não autenticado');
            return;
        }
        
        if (currentStep < steps.length) {
            const nextStep = currentStep + 1;
            
            // Apenas navegar para a próxima etapa
            // NÃO marcar etapas como completas automaticamente
            setCurrentStep(nextStep);
            
            console.log('Navegando para etapa:', nextStep);
            
            // Não salvar progresso aqui - apenas navegação
            // O progresso é salvo apenas quando o usuário clica em "Salvar" em cada etapa
        }
    };

    const handlePreviousStep = () => {
        if (!isAuthenticated) {
            console.warn('Não é possível navegar - usuário não autenticado');
            return;
        }
        
        if (currentStep > 1) {
            const prevStep = currentStep - 1;
            setCurrentStep(prevStep);
        }
    };

    const handleStepChange = (step) => {
        if (!isAuthenticated) {
            console.warn('Não é possível navegar - usuário não autenticado');
            return;
        }
        
        // Permite navegar apenas para steps já completados ou o próximo step
        if (step <= Math.max(...completedSteps, 1) || step === currentStep + 1) {
            setCurrentStep(step);
        }
    };

    // Função para salvar coordenadas do mapa
    const handleSaveMapLocation = async () => {
        // Verificar se está autenticado e tem dados necessários
        if (!isAuthenticated || !userID || !empresaParse?.id_empresa || !objIdEmpresa) {
            console.warn('Não é possível salvar localização - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }
        
        if (!latitudeEmpresa || !longitudeEmpresa) {
            toast('Por favor, posicione o marcador no mapa.', { type: 'warning' });
            return;
        }

        // Verificar se as coordenadas não são as padrão de Brasília
        const isDefaultBrasilia = parseFloat(latitudeEmpresa) === -15.7942 && parseFloat(longitudeEmpresa) === -47.8822;
        
        if (isDefaultBrasilia) {
            toast('Por favor, arraste o marcador para a localização real do seu estabelecimento antes de salvar.', { 
                type: 'warning',
                autoClose: 5000 
            });
            return;
        }

        // Validação adicional: verificar se as coordenadas são válidas
        const lat = parseFloat(latitudeEmpresa);
        const lng = parseFloat(longitudeEmpresa);
        
        if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
            toast('Coordenadas inválidas. Por favor, posicione o marcador no mapa.', { 
                type: 'error',
                autoClose: 5000 
            });
            return;
        }

        setLoading(true);
        try {
            const empresaObjId = objIdEmpresa;
            
            if (!empresaObjId) {
                toast('Erro: ID da empresa não encontrado. Faça login novamente.', { type: 'error' });
                return;
            }

            console.log('Salvando coordenadas para empresa ObjectId:', empresaObjId);
            console.log('Coordenadas a serem salvas:', {
                latitude: latitudeEmpresa,
                longitude: longitudeEmpresa
            });
            
            // Atualizar coordenadas no backend
            await updateEmpresaAddress(
                empresaObjId,
                empresaParse.id_empresa,
                userID,
                formikEndereco.values.cep,
                formikEndereco.values.estado,
                formikEndereco.values.municipio,
                formikEndereco.values.bairro,
                formikEndereco.values.address_number,
                formikEndereco.values.complemento,
                latitudeEmpresa.toString(),
                longitudeEmpresa.toString()
            );

            toast('Localização salva com sucesso!', { type: 'success' });
            
            // Marcar etapa 3 como completa e avançar para etapa 4
            const novasEtapasCompletas = [...completedSteps];
            if (!novasEtapasCompletas.includes(3)) {
                novasEtapasCompletas.push(3);
            }
            
            setCompletedSteps(novasEtapasCompletas);
            setCurrentStep(4);

            // Salvar progresso no banco de dados
            try {
                if (objIdEmpresa) {
                    await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 3, 4);
                    console.log('Progresso salvo: etapa 3 (mapa) completa, avançando para etapa 4 (área de entrega)');
                }
            } catch (error) {
                console.warn('Erro ao salvar progresso da configuração inicial:', error);
            }

        } catch (error) {
            console.error('Erro ao salvar localização:', error);
            toast('Erro ao salvar localização. Tente novamente.', { type: 'error' });
        } finally {
            setLoading(false);
        }
    };

    const handleFinish = async () => {
        // Verificar se está autenticado
        if (!isAuthenticated) {
            console.warn('Não é possível finalizar - usuário não autenticado');
            toast('Erro: usuário não autenticado', { type: 'error' });
            return;
        }
        
        setLoading(true);
        
        try {
            // Marcar etapa 9 como completa e finalizar configuração
            const novasEtapasCompletas = [...completedSteps];
            if (!novasEtapasCompletas.includes(9)) {
                novasEtapasCompletas.push(9);
            }
            setCompletedSteps(novasEtapasCompletas);
            
            // Marcar configuração como finalizada no banco de dados
            if (objIdEmpresa) {
                await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 9, null, true); // true = finalizada
                console.log('Configuração inicial finalizada - todas as 9 etapas completas');
            }
            
            toast('Configuração inicial concluída! Bem-vindo ao PedeJA!', { type: 'success' });
            navigate('/');
        } catch (error) {
            console.warn('Erro ao finalizar configuração inicial:', error);
            toast('Erro ao finalizar configuração. Tente novamente.', { type: 'error' });
        } finally {
            setLoading(false);
        }
    };

    // Funções para área de entrega
    const handleChangeRegionType = async (newType) => {
        console.log('🔄 handleChangeRegionType chamado:', { newType, currentType: typeOfRegion });
        
        // Verificar se está autenticado e tem dados necessários
        if (!isAuthenticated || !userID || !empresaParse?.id_empresa || !objIdEmpresa) {
            console.warn('Não é possível alterar tipo de região - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }
        
        try {
            // Primeiro, limpar os dados do tipo atual no backend se houver mudança
            if (typeOfRegion && typeOfRegion !== newType) {
                console.log('📝 Limpando dados do tipo anterior:', typeOfRegion);
                
                if (typeOfRegion === 'raio' && arrayRaioEntrega.length > 0) {
                    console.log('🗑️ Removendo raios existentes...');
                    for (const raio of arrayRaioEntrega) {
                        try {
                            await deleteRaioEntrega(userID, empresaParse.id_empresa, raio._id);
                            console.log('✅ Raio removido:', raio._id);
                        } catch (error) {
                            console.warn('⚠️ Erro ao remover raio:', raio._id, error);
                        }
                    }
                    setArrayRaioEntrega([]);
                } else if (typeOfRegion === 'bairro' && arrayBairroEntrega.length > 0) {
                    console.log('🗑️ Removendo bairros existentes...');
                    for (const bairro of arrayBairroEntrega) {
                        try {
                            await deleteBairroEntrega(userID, empresaParse.id_empresa, bairro._id);
                            console.log('✅ Bairro removido:', bairro._id);
                        } catch (error) {
                            console.warn('⚠️ Erro ao remover bairro:', bairro._id, error);
                        }
                    }
                    setArrayBairroEntrega([]);
                }
            }
            
            // Agora atualizar o tipo de região
            console.log('📤 Atualizando tipo de região para:', newType);
            const response = await updateTypeRegion(objIdEmpresa, empresaParse.id_empresa, userID, newType);
            console.log('✅ Resposta updateTypeRegion:', response);
            
            if (response.status === 200) {
                setTypeOfRegion(newType);
                
                // Garantir que os arrays estão limpos
                setArrayRaioEntrega([]);
                setArrayBairroEntrega([]);
                
                // Resetar valores
                setValorEntrega('');
                setRaio(1);
                formikAreaEntrega.resetForm();
                
                // Recarregar dados da empresa para garantir sincronização
                console.log('🔄 Recarregando dados da empresa...');
                const empresaResponse = await getEmpresa(userID, empresaParse.id_empresa);
                const empresa = empresaResponse.data.empresa;
                
                // Carregar arrays baseado no novo tipo
                if (newType === 'raio' && empresa.raios_entrega) {
                    setArrayRaioEntrega(empresa.raios_entrega);
                    console.log('📊 Raios carregados:', empresa.raios_entrega.length);
                } else if (newType === 'bairro' && empresa.bairros_entrega) {
                    setArrayBairroEntrega(empresa.bairros_entrega);
                    console.log('📊 Bairros carregados:', empresa.bairros_entrega.length);
                }
                
                toast(`Tipo de região alterado para ${newType === 'raio' ? 'Raio' : 'Bairro'}`, { type: 'success' });
                console.log(`✅ Tipo de região alterado para: ${newType}`);
            } else {
                console.error('❌ Erro na resposta da API:', response);
                toast('Erro ao alterar tipo de região', { type: 'error' });
            }
        } catch (error) {
            console.error('❌ Erro ao alterar tipo de região:', error);
            toast('Erro ao alterar tipo de região', { type: 'error' });
        }
    };

    const handleAddRaio = async () => {
        console.log('handleAddRaio chamado:', { raio, valorEntrega, type: typeof valorEntrega });
        
        // Verificar se está autenticado e tem dados necessários
        if (!isAuthenticated || !userID || !empresaParse?.id_empresa || !objIdEmpresa) {
            console.warn('Não é possível adicionar raio - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }
        
        if (raio === 0 || raio === '' || !raio) {
            return toast("É obrigatório informar o raio!", { type: "error" });
        }

        if (valorEntrega === '' || valorEntrega < 0) {
            return toast("É obrigatório informar um valor válido para a entrega!", { type: "error" });
        }

        const raioToApi = raio * 1000;
        const valorToApi = typeof valorEntrega === 'string' ? parseFloat(valorEntrega) : valorEntrega;

        console.log('Enviando para API:', { raioToApi, valorToApi });

        try {
            const response = await updateRaioEntregaEmpresa(objIdEmpresa, empresaParse.id_empresa, userID, raioToApi, valorToApi);
            console.log('Resposta da API:', response);
            
            if (response.status === 200) {
                toast(response.data.msg, { type: "success" });
                // Recarregar dados da empresa para atualizar o array
                const empresaResponse = await getEmpresa(userID, empresaParse.id_empresa);
                setArrayRaioEntrega(empresaResponse.data.empresa.raios_entrega || []);
                setRaio(1);
                setValorEntrega('');
            } else if (response.status === 201) {
                // Status 201 indica que o raio já existe ou outro erro
                toast(response.data.msg || 'Este raio já está cadastrado!', { type: "warning" });
                console.log('⚠️ Raio já existe:', response.data.msg);
            }
        } catch (error) {
            console.error("Erro ao adicionar raio de entrega:", error);
            // Verificar se o erro tem uma resposta da API
            if (error.response && error.response.status === 201) {
                toast(error.response.data.msg || 'Este raio já está cadastrado!', { type: "warning" });
            } else {
                toast("Erro ao adicionar raio de entrega!", { type: "error" });
            }
        }
    };

    const handleAddBairro = async (bairro) => {
        console.log('handleAddBairro chamado:', { bairro, valorEntrega, type: typeof valorEntrega });
        
        // Verificar se está autenticado e tem dados necessários
        if (!isAuthenticated || !userID || !empresaParse?.id_empresa || !objIdEmpresa) {
            console.warn('Não é possível adicionar bairro - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }
        
        if (!bairro || bairro.trim() === '') {
            return toast("É obrigatório informar o nome do bairro!", { type: "error" });
        }

        if (valorEntrega === '' || valorEntrega < 0) {
            return toast("É obrigatório informar um valor válido para a entrega!", { type: "error" });
        }

        const valorToApi = typeof valorEntrega === 'string' ? parseFloat(valorEntrega) : valorEntrega;

        console.log('Enviando para API:', { bairro, valorToApi });

        try {
            const response = await updateBairroEntregaEmpresa(objIdEmpresa, empresaParse.id_empresa, userID, bairro, valorToApi);
            console.log('Resposta da API:', response);
            
            if (response.status === 200) {
                toast(response.data.msg, { type: "success" });
                // Recarregar dados da empresa para atualizar o array
                const empresaResponse = await getEmpresa(userID, empresaParse.id_empresa);
                setArrayBairroEntrega(empresaResponse.data.empresa.bairros_entrega || []);
                setValorEntrega('');
                formikAreaEntrega.resetForm();
            } else if (response.status === 201) {
                // Status 201 indica que o bairro já existe ou outro erro
                toast(response.data.msg || 'Este bairro já está cadastrado!', { type: "warning" });
                console.log('⚠️ Bairro já existe:', response.data.msg);
            }
        } catch (error) {
            console.error("Erro ao adicionar bairro:", error);
            // Verificar se o erro tem uma resposta da API
            if (error.response && error.response.status === 201) {
                toast(error.response.data.msg || 'Este bairro já está cadastrado!', { type: "warning" });
            } else {
                toast("Erro ao adicionar bairro!", { type: "error" });
            }
        }
    };

    const handleDeleteRaio = async (raio_entrega_id) => {
        // Verificar se está autenticado e tem dados necessários
        if (!isAuthenticated || !userID || !empresaParse?.id_empresa) {
            console.warn('Não é possível excluir raio - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }
        
        try {
            const response = await deleteRaioEntrega(userID, empresaParse.id_empresa, raio_entrega_id);
            if (response.status === 200) {
                toast(response.data.msg, { type: "success" });
                // Recarregar dados da empresa para atualizar o array
                const empresaResponse = await getEmpresa(userID, empresaParse.id_empresa);
                setArrayRaioEntrega(empresaResponse.data.empresa.raios_entrega || []);
            }
        } catch (error) {
            toast("Erro na exclusão do raio de entrega!", { type: "error" });
        }
    };

    const handleDeleteBairro = async (bairro_entrega_id) => {
        // Verificar se está autenticado e tem dados necessários
        if (!isAuthenticated || !userID || !empresaParse?.id_empresa) {
            console.warn('Não é possível excluir bairro - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }
        
        try {
            const response = await deleteBairroEntrega(userID, empresaParse.id_empresa, bairro_entrega_id);
            if (response.status === 200) {
                toast(response.data.msg, { type: "success" });
                // Recarregar dados da empresa para atualizar o array
                const empresaResponse = await getEmpresa(userID, empresaParse.id_empresa);
                setArrayBairroEntrega(empresaResponse.data.empresa.bairros_entrega || []);
            }
        } catch (error) {
            toast("Erro na exclusão do bairro de entrega!", { type: "error" });
        }
    };

    const handleSaveAreaEntrega = async () => {
        console.log('Salvando área de entrega:', { typeOfRegion, arrayRaioEntrega, arrayBairroEntrega });
        
        // Verificar se está autenticado
        if (!isAuthenticated) {
            console.warn('Não é possível salvar área de entrega - usuário não autenticado');
            toast('Erro: usuário não autenticado', { type: 'error' });
            return;
        }
        
        try {
            setLoading(true);
            
            // Validar se há configurações válidas
            if (!typeOfRegion) {
                return toast.error('Selecione o tipo de região de entrega ou pule a etapa se não oferecer entrega!', { type: "error" });
            }
            
            if (typeOfRegion === 'skip') {
                // Se foi pulada, pode prosseguir
                console.log('Área de entrega foi pulada, prosseguindo...');
            } else if (typeOfRegion === 'raio' && arrayRaioEntrega.length === 0) {
                return toast.error('Adicione pelo menos um raio de entrega!', { type: "error" });
            } else if (typeOfRegion === 'bairro' && arrayBairroEntrega.length === 0) {
                return toast.error('Adicione pelo menos um bairro de entrega!', { type: "error" });
            }
            
            // Marcar etapa como concluída
            if (!completedSteps.includes(4)) {
                setCompletedSteps([...completedSteps, 4]);
            }
            
            // Avançar para próxima etapa (horários de funcionamento)
            setCurrentStep(5);
            
            const mensagem = typeOfRegion === 'skip' ? 
                'Configuração salva! Apenas retirada no local.' : 
                'Área de entrega configurada com sucesso!';
            
            toast.success(mensagem, { type: "success" });

            // Salvar progresso no banco de dados
            try {
                if (objIdEmpresa) {
                    await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 4, 5);
                    console.log('Progresso salvo: etapa 4 (área de entrega) completa, avançando para etapa 5 (horários)');
                }
            } catch (error) {
                console.warn('Erro ao salvar progresso da configuração inicial:', error);
            }
            
        } catch (error) {
            console.error('Erro ao salvar área de entrega:', error);
            toast.error('Erro ao salvar configurações. Tente novamente.', { type: "error" });
        } finally {
            setLoading(false);
        }
    };

    // Funções para horários de funcionamento
    const handleAvailabilityChange = (event) => {
        if (!isAuthenticated) {
            console.warn('Não é possível alterar disponibilidade - usuário não autenticado');
            return;
        }
        setAvailability(event.target.value);
    };

    const toggleDay = (day) => {
        if (!isAuthenticated) {
            console.warn('Não é possível alterar dias - usuário não autenticado');
            return;
        }
        
        setActiveDays((prevActiveDays) => {
            const newActiveDays = new Set(prevActiveDays);
            if (newActiveDays.has(day)) {
                newActiveDays.delete(day);
            } else {
                newActiveDays.add(day);
                if (!timeRanges[day] || timeRanges[day].length === 0) {
                    addTimeRange(day);
                }
            }
            return newActiveDays;
        });
    };

    const handleTimeChange = (day, index, field, value) => {
        if (!isAuthenticated) {
            console.warn('Não é possível alterar horários - usuário não autenticado');
            return;
        }
        
        setTimeRanges((prevTimeRanges) => {
            const updatedRanges = [...prevTimeRanges[day]];
            const updatedTimeRange = { ...updatedRanges[index], [field]: value };
            updatedRanges[index] = updatedTimeRange;
            return {
                ...prevTimeRanges,
                [day]: updatedRanges,
            };
        });
    };

    const addTimeRange = (day) => {
        if (!isAuthenticated) {
            console.warn('Não é possível adicionar horário - usuário não autenticado');
            return;
        }
        
        setTimeRanges((prevTimeRanges) => {
            const newRanges = prevTimeRanges[day] ? [...prevTimeRanges[day]] : [];
            newRanges.push({ start: '08:00', end: '18:00' });
            return {
                ...prevTimeRanges,
                [day]: newRanges,
            };
        });
    };

    const removeTimeRange = (day, index) => {
        if (!isAuthenticated) {
            console.warn('Não é possível remover horário - usuário não autenticado');
            return;
        }
        
        setTimeRanges((prevTimeRanges) => {
            const updatedRanges = [...prevTimeRanges[day]];
            updatedRanges.splice(index, 1);
            return {
                ...prevTimeRanges,
                [day]: updatedRanges,
            };
        });
    };

    const mapDayToFull = (day) => {
        switch (day) {
            case 'domingo':
                return 'Domingo';
            case 'segunda':
                return 'Segunda';
            case 'terca':
                return 'Terça';
            case 'quarta':
                return 'Quarta';
            case 'quinta':
                return 'Quinta';
            case 'sexta':
                return 'Sexta';
            case 'sabado':
                return 'Sábado';
            default:
                return day;
        }
    };

    const mapDayToOneChar = (day) => {
        switch (day) {
            case 'domingo':
                return 'D';
            case 'segunda':
                return 'S';
            case 'terca':
                return 'T';
            case 'quarta':
                return 'Q';
            case 'quinta':
                return 'Q';
            case 'sexta':
                return 'S';
            case 'sabado':
                return 'S';
            default:
                return day.charAt(0).toUpperCase();
        }
    };

    const handleSaveHorarioFuncionamento = async () => {
        console.log('Salvando horários de funcionamento:', { availability, activeDays, timeRanges, selectedTimeZone });
        
        // Verificar se está autenticado
        if (!isAuthenticated || !userID || !empresaParse?.id_empresa || !objIdEmpresa) {
            console.warn('Não é possível salvar horários - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }
        
        try {
            setLoading(true);
            
            let horario_funcionamento = {};
            
            if (availability === 'especifico') {
                // Validar se pelo menos um dia foi selecionado
                if (activeDays.size === 0) {
                    toast('Selecione pelo menos um dia da semana!', { type: 'error' });
                    return;
                }
                
                // Construir objeto de horários apenas para dias ativos
                daysOfWeek.forEach((day) => {
                    if (activeDays.has(day)) {
                        const dayRanges = timeRanges[day];
                        if (dayRanges && dayRanges.length > 0) {
                            horario_funcionamento[day] = dayRanges;
                        }
                    }
                });
                
                // Verificar se há pelo menos um horário configurado
                if (Object.keys(horario_funcionamento).length === 0) {
                    toast('Configure pelo menos um horário de funcionamento!', { type: 'error' });
                    return;
                }
            }
            
            const response = await updateTempoHorarioFuncionamento(
                userID, 
                objIdEmpresa, 
                empresaParse.id_empresa, 
                availability, 
                horario_funcionamento, 
                selectedTimeZone
            );
            
            if (response) {
                console.log("Horário de funcionamento salvo:", response);
                toast('Horários de funcionamento salvos com sucesso!', { type: 'success' });
                
                // Marcar etapa 5 como completa e avançar para etapa 6
                const novasEtapasCompletas = [...completedSteps];
                if (!novasEtapasCompletas.includes(5)) {
                    novasEtapasCompletas.push(5);
                }
                
                setCompletedSteps(novasEtapasCompletas);
                setCurrentStep(6);
                
                // Salvar progresso no banco de dados
                try {
                    if (objIdEmpresa) {
                        await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 5, 6);
                        console.log('Progresso salvo: etapa 5 (horários) completa, avançando para etapa 6 (cardápio)');
                    }
                } catch (error) {
                    console.warn('Erro ao salvar progresso da configuração inicial:', error);
                }
            }
            
        } catch (error) {
            console.error('Erro ao salvar horários de funcionamento:', error);
            toast('Erro ao salvar horários de funcionamento. Tente novamente.', { type: 'error' });
        } finally {
            setLoading(false);
        }
    };

    // Funções para cardápio
    const handleCardapioOptionChange = (option) => {
        if (!isAuthenticated) {
            console.warn('Não é possível alterar opção de cardápio - usuário não autenticado');
            return;
        }
        setCardapioOption(option);
        setImportProgress(null);
        setImportComplete(false);
        setIsImporting(false);
        setShowVideo(false);
        setVideoType('');
    };

    const handleImportCardapio = async (importType, link) => {
        if (!isAuthenticated || !userID || !empresaParse?.id_empresa) {
            console.warn('Não é possível importar cardápio - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }

        if (!link || link.trim() === '') {
            toast('Por favor, insira o link do cardápio', { type: 'error' });
            return;
        }

        try {
            setIsImporting(true);
            setImportProgress(0);
            setImportMessage('Iniciando importação...');
            setImportStage('iniciando');
            
            console.log(`Importando cardápio ${importType}:`, link);
            
            let response;
            if (importType === 'ifood') {
                response = await importCardapioIfood(link, empresaParse.id_empresa);
            } else if (importType === 'anotaai') {
                response = await importCardapioAnotaai(link, empresaParse.id_empresa);
            }

            console.log('Resposta do import:', response);
            
            if (response && response.data) {
                toast('Importação iniciada! Acompanhe o progresso em tempo real.', { type: 'info' });
            }

        } catch (error) {
            console.error('Erro ao importar cardápio:', error);
            setIsImporting(false);
            setImportProgress(null);
            setImportMessage('');
            setImportStage('');
            toast('Erro ao iniciar importação: ' + (error.response?.data?.message || error.message), { type: 'error' });
        }
    };

    const handleSendCardapioToTeam = async () => {
        if (!isAuthenticated || !userID || !empresaParse?.id_empresa || !objIdEmpresa) {
            console.warn('Não é possível enviar cardápio - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }

        const { customLink, pdfFile, observacoes } = cardapioFormData;
        
        if (!customLink && !pdfFile) {
            toast('Por favor, forneça o link do cardápio ou anexe um arquivo PDF', { type: 'error' });
            return;
        }

        try {
            setLoading(true);
            
            console.log('Dados do cardápio para envio:', {
                empresaId: objIdEmpresa,
                idEmpresa: empresaParse.id_empresa,
                userId: userID,
                customLink: customLink || '',
                observacoes: observacoes || '',
                pdfFile: pdfFile ? pdfFile.name : null,
                userLookupName: userParse?.name || 'Usuário'
            });
            
            // Chamar API real implementada
            const response = await enviarCardapioParaEquipe(
                objIdEmpresa,
                empresaParse.id_empresa,
                userID,
                customLink || '',
                pdfFile,
                observacoes || '',
                userParse?.name || 'Usuário'
            );

            console.log('Cardápio enviado para equipe:', response.data);
            
            if (response.data.success) {
                toast('Cardápio enviado para nossa equipe! Aguarde o contato em breve.', { type: 'success' });

                // Marcar etapa como completa
                const novasEtapasCompletas = [...completedSteps];
                if (!novasEtapasCompletas.includes(6)) {
                    novasEtapasCompletas.push(6);
                }
                
                setCompletedSteps(novasEtapasCompletas);
                setCurrentStep(7);

                // Salvar progresso no banco de dados
                try {
                    if (objIdEmpresa) {
                        await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 6, 7);
                        console.log('Progresso salvo: etapa 6 (cardápio) completa, avançando para etapa 7 (formas de entrega)');
                    }
                } catch (error) {
                    console.warn('Erro ao salvar progresso da configuração inicial:', error);
                }
            } else {
                toast(response.data.message || 'Erro ao enviar cardápio', { type: 'error' });
            }

        } catch (error) {
            console.error('Erro ao enviar cardápio para equipe:', error);
            
            // Tratar erro específico da API
            if (error.response && error.response.data) {
                toast(error.response.data.message || 'Erro ao enviar cardápio', { type: 'error' });
            } else {
                toast('Erro ao enviar cardápio. Tente novamente.', { type: 'error' });
            }
        } finally {
            setLoading(false);
        }
    };



    const handleSkipCardapio = async () => {
        if (!isAuthenticated || !objIdEmpresa) {
            console.warn('Não é possível pular cardápio - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }

        try {
            setLoading(true);
            
            // Marcar etapa 6 como completa e avançar para etapa 7
            const novasEtapasCompletas = [...completedSteps];
            if (!novasEtapasCompletas.includes(6)) {
                novasEtapasCompletas.push(6);
            }
            
            setCompletedSteps(novasEtapasCompletas);
            setCurrentStep(7);
            
            toast('Etapa de cardápio pulada. Você pode adicionar o cardápio depois!', { type: 'info' });

            // Salvar progresso no banco de dados
            try {
                if (objIdEmpresa) {
                    await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 6, 7);
                    console.log('Progresso salvo: etapa 6 (cardápio) pulada, avançando para etapa 7 (formas de entrega)');
                }
            } catch (error) {
                console.warn('Erro ao salvar progresso da configuração inicial:', error);
            }

        } catch (error) {
            console.error('Erro ao pular cardápio:', error);
            toast('Erro ao pular etapa. Tente novamente.', { type: 'error' });
        } finally {
            setLoading(false);
        }
    };



    const handleFinishCardapioImport = async () => {
        if (!isAuthenticated || !objIdEmpresa) {
            console.warn('Não é possível finalizar importação - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }

        try {
            setLoading(true);
            
            // Marcar etapa 6 como completa e avançar para etapa 7
            const novasEtapasCompletas = [...completedSteps];
            if (!novasEtapasCompletas.includes(6)) {
                novasEtapasCompletas.push(6);
            }
            
            setCompletedSteps(novasEtapasCompletas);
            setCurrentStep(7);

            // Salvar progresso no banco de dados
            try {
                if (objIdEmpresa) {
                    await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 6, 7);
                    console.log('Progresso salvo: etapa 6 (cardápio) completa, avançando para etapa 7 (formas de entrega)');
                }
            } catch (error) {
                console.warn('Erro ao salvar progresso da configuração inicial:', error);
            }

        } catch (error) {
            console.error('Erro ao finalizar importação:', error);
            toast('Erro ao finalizar importação. Tente novamente.', { type: 'error' });
        } finally {
            setLoading(false);
        }
    };

    // Função para resetar o status de importação em caso de erro
    const handleResetImportStatus = async () => {
        if (!empresaParse?.id_empresa) return;
        
        setLoading(true);
        try {
            await resetImportStatus(empresaParse.id_empresa);
            
            // Resetar estados locais
            setIsImporting(false);
            setImportProgress(null);
            setImportMessage('');
            setImportStage('');
            setImportDetails({});
            setImportComplete(false);
            
            toast.success('Status de importação resetado. Você pode tentar importar novamente.', { autoClose: 5000 });
        } catch (error) {
            console.error('Erro ao resetar status de importação:', error);
            toast.error('Erro ao resetar status. Tente novamente.', { autoClose: 5000 });
        } finally {
            setLoading(false);
        }
    };

    // Função para salvar configurações de entrega
    const handleSaveConfiguracoesEntrega = async () => {
        if (!isAuthenticated || !objIdEmpresa) {
            console.warn('Não é possível salvar configurações - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }

        try {
            setLoading(true);
            
            await updateConfiguracoesEntrega(objIdEmpresa, entregaDisabled, retiradaDisabled);
            toast.success("Configurações de entrega salvas com sucesso!");

            // Marcar etapa 7 como completa e avançar para etapa 8
            const novasEtapasCompletas = [...completedSteps];
            if (!novasEtapasCompletas.includes(7)) {
                novasEtapasCompletas.push(7);
            }
            
            setCompletedSteps(novasEtapasCompletas);
            setCurrentStep(8);

            // Salvar progresso no banco de dados
            try {
                if (objIdEmpresa) {
                    await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 7, 8);
                    console.log('Progresso salvo: etapa 7 (formas de entrega) completa, avançando para etapa 8 (WhatsApp)');
                }
            } catch (error) {
                console.warn('Erro ao salvar progresso da configuração inicial:', error);
            }

        } catch (error) {
            console.error("Erro ao salvar configurações:", error);
            toast.error("Erro ao salvar as configurações de entrega.");
        } finally {
            setLoading(false);
        }
    };

    // 📱 NOVA FUNÇÃO PARA GERAR QR CODE MANUALMENTE (PRIMEIRA VEZ)
    const generateQrCodeManually = async () => {
        console.log('🔄 Usuário solicitou geração manual do QR Code...');
        setUserRequestedQrCode(true);
        fetchQrCode();
    };

    // Função para buscar QR Code do WhatsApp
    const fetchQrCode = () => {
        if (!isMountedRef.current || !empresaParse?._id) return;

        console.log('Buscando QR Code do WhatsApp...');
        setGeneratingQrCode(true);

        getQrCodeWhatsapp(empresaParse._id).then((response) => {
            console.log('Resposta da API QR Code:', response);

            if (!isMountedRef.current) return;

            if (response.data.qrcode.error === 'is_loged') {
                console.log('WhatsApp já está conectado');
                setIsWhatsappLoged(true);
                setWhatsappConnected(true);
                setGeneratingQrCode(false);
                return;
            }

            if (response.data.qrcode.qr !== '') {
                console.log('WhatsApp não está conectado, exibindo QR Code');
                setIsWhatsappLoged(false);
                setWhatsappConnected(false);
            }

            setQrCodeImg(response.data.qrcode.qr);
            
            // 🔧 REGENERAÇÃO AUTOMÁTICA DESABILITADA - Usuário deve clicar manualmente
            // const ttl = response.data.qrcode.ttl;
            // const currentTime = Math.floor(Date.now() / 1000);
            // const timeUntilExpiry = (ttl - currentTime) * 1000;

            // if (timeUntilExpiry > 0) {
            //     const regenerateIn = Math.max(timeUntilExpiry - 5000, 15000);
            //     console.log(`QR Code será regenerado em ${regenerateIn / 1000} segundos`);

            //     timeoutRef.current = setTimeout(() => {
            //         if (isMountedRef.current && !cancelQrCodeFetchRef.current) {
            //             fetchQrCode();
            //         }
            //     }, regenerateIn);
            // } else {
            //     console.log('QR Code já expirado, regenerando em 2 segundos...');
            //     timeoutRef.current = setTimeout(() => {
            //         if (isMountedRef.current && !cancelQrCodeFetchRef.current) {
            //             fetchQrCode();
            //         }
            //     }, 2000);
            // }

            // Verificar status de conexão periodicamente
            const checkConnectionStatus = () => {
                if (!isMountedRef.current || cancelQrCodeFetchRef.current) return;

                getQrCodeWhatsapp(empresaParse._id).then((statusResponse) => {
                    if (statusResponse.data.qrcode.error === 'is_loged') {
                        console.log('WhatsApp conectado com sucesso!');
                        setIsWhatsappLoged(true);
                        setWhatsappConnected(true);
                        setGeneratingQrCode(false);
                        if (timeoutRef.current) {
                            clearTimeout(timeoutRef.current);
                        }
                    } else {
                        setTimeout(checkConnectionStatus, 5000);
                    }
                }).catch((error) => {
                    console.log('Erro ao verificar status de conexão:', error);
                    setTimeout(checkConnectionStatus, 5000);
                });
            };

            setTimeout(checkConnectionStatus, 5000);

        }).catch((error) => {
            console.error('Erro ao buscar QR Code:', error);
            setGeneratingQrCode(false);

            // 🔧 REGENERAÇÃO AUTOMÁTICA DESABILITADA - Usuário deve tentar manualmente
            // if (isMountedRef.current && !cancelQrCodeFetchRef.current) {
            //     console.log('Tentando novamente em 10 segundos...');
            //     timeoutRef.current = setTimeout(() => {
            //         if (isMountedRef.current && !cancelQrCodeFetchRef.current) {
            //             fetchQrCode();
            //         }
            //     }, 10000);
            // }
        }).finally(() => {
            if (isMountedRef.current) {
                setGeneratingQrCode(false);
            }
        });
    };

    // Função para avançar da etapa do WhatsApp
    const handleSaveWhatsAppConnection = async () => {
        if (!isAuthenticated || !objIdEmpresa) {
            console.warn('Não é possível salvar conexão WhatsApp - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }

        try {
            setLoading(true);

            // Marcar etapa 8 como completa e avançar para etapa 9
            const novasEtapasCompletas = [...completedSteps];
            if (!novasEtapasCompletas.includes(8)) {
                novasEtapasCompletas.push(8);
            }
            
            setCompletedSteps(novasEtapasCompletas);
            setCurrentStep(9);

            // Salvar progresso no banco de dados (sem marcar como finalizada)
            try {
                if (objIdEmpresa) {
                    await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 8, 9, false);
                    console.log('Progresso salvo: etapa 8 (WhatsApp) completa, avançando para etapa 9 (confirmação)');
                }
            } catch (error) {
                console.warn('Erro ao salvar progresso da configuração inicial:', error);
            }

            toast.success('WhatsApp configurado com sucesso!', { type: 'success' });

        } catch (error) {
            console.error("Erro ao salvar conexão WhatsApp:", error);
            toast.error("Erro ao salvar configuração do WhatsApp.");
        } finally {
            setLoading(false);
        }
    };

    // Função para pular configuração do WhatsApp
    const handleSkipWhatsApp = async () => {
        if (!isAuthenticated || !objIdEmpresa) {
            console.warn('Não é possível pular WhatsApp - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }

        try {
            setLoading(true);

            // Marcar etapa 8 como completa e avançar para etapa 9
            const novasEtapasCompletas = [...completedSteps];
            if (!novasEtapasCompletas.includes(8)) {
                novasEtapasCompletas.push(8);
            }
            
            setCompletedSteps(novasEtapasCompletas);
            setCurrentStep(9);

            // Salvar progresso no banco de dados (sem marcar como finalizada)
            try {
                if (objIdEmpresa) {
                    await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 8, 9, false);
                    console.log('Progresso salvo: etapa 8 (WhatsApp) pulada, avançando para etapa 9 (confirmação)');
                }
            } catch (error) {
                console.warn('Erro ao salvar progresso da configuração inicial:', error);
            }

            toast.info('Etapa do WhatsApp pulada. Você pode configurar depois!', { type: 'info' });

        } catch (error) {
            console.error("Erro ao pular WhatsApp:", error);
            toast.error("Erro ao pular etapa do WhatsApp.");
        } finally {
            setLoading(false);
        }
    };

    // Funções para tooltips e ajuda
    const showTooltipFor = (tooltipId) => {
        setShowTooltip(tooltipId);
    };

    const hideTooltip = () => {
        setShowTooltip(null);
    };

    // Componente de Tooltip personalizado
    const CustomTooltip = ({ id, content, position = 'top' }) => {
        if (showTooltip !== id) return null;
        
        return (
            <div 
                className={`custom-tooltip tooltip-${position}`}
                onMouseEnter={() => showTooltipFor(id)}
                onMouseLeave={hideTooltip}
            >
                <div className="tooltip-content">
                    {content}
                </div>
                <div className="tooltip-arrow"></div>
            </div>
        );
    };

    // Componente de Modal de Ajuda
    const HelpModal = () => {
        if (!showHelpModal) return null;

        return (
            <div className="help-modal-overlay" onClick={() => setShowHelpModal(false)}>
                <div className="help-modal-content" onClick={(e) => e.stopPropagation()}>
                    <div className="help-modal-header">
                        <h3>Como Configurar Horários de Funcionamento</h3>
                        <button 
                            className="help-modal-close"
                            onClick={() => setShowHelpModal(false)}
                        >
                            ×
                        </button>
                    </div>
                    <div className="help-modal-body">
                        <div className="help-section">
                            <h4>🕒 Sempre Disponível</h4>
                            <p>Selecione esta opção se seu estabelecimento funciona <strong>24 horas por dia, 7 dias por semana</strong>. Ideal para delivery que não para nunca!</p>
                        </div>
                        
                        <div className="help-section">
                            <h4>📅 Horários Específicos</h4>
                            <p>Configure dias e horários exatos de funcionamento:</p>
                            <ul>
                                <li><strong>Selecionar dias:</strong> Clique nas letras dos dias (D, S, T, Q, Q, S, S) para ativar/desativar</li>
                                <li><strong>Definir horários:</strong> Use os campos de hora para definir início e fim</li>
                                <li><strong>Múltiplos horários:</strong> Clique em "Adicionar Horário" para ter vários horários no mesmo dia</li>
                                <li><strong>Remover horários:</strong> Use o botão "Remover" para excluir horários extras</li>
                            </ul>
                        </div>

                        <div className="help-section">
                            <h4>💡 Exemplos Práticos</h4>
                            <div className="help-examples">
                                <div className="help-example">
                                    <strong>Restaurante tradicional:</strong><br/>
                                    Segunda a Sábado: 11:00 às 15:00 e 18:00 às 23:00<br/>
                                    Domingo: 11:00 às 22:00
                                </div>
                                <div className="help-example">
                                    <strong>Lanchonete:</strong><br/>
                                    Todos os dias: 06:00 às 00:00
                                </div>
                                <div className="help-example">
                                    <strong>Delivery 24h:</strong><br/>
                                    Sempre disponível
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    // Utilitários para área de entrega
    const handleChange = (type, value, delta) => {
        if (value + delta >= 0) {
            if (type === 'raio') {
                setRaio(Number(value) + delta);
            }
        }
    };

    const handleDecrement = (type) => {
        handleChange(type, type === 'raio' && raio, -1);
    };

    const handleIncrement = (type) => {
        handleChange(type, type === 'raio' && raio, 1);
    };

    const onChangeValorEntrega = (event) => {
        const value = event.target.value;
        // Permitir string vazia para limpar o campo
        if (value === '') {
            setValorEntrega('');
        } else {
            const numValue = parseFloat(value);
            if (!isNaN(numValue) && numValue >= 0) {
                setValorEntrega(numValue);
            }
        }
    };

    const handleFocus = () => {
        setIsFocused(true);
    };

    const handleBlur = () => {
        setIsFocused(false);
    };

    const handleSkipDeliveryArea = async () => {
        console.log('🚀 Iniciando processo de pular etapa de área de entrega');
        console.log('Estado atual:', { typeOfRegion, objIdEmpresa, completedSteps });
        
        // Verificar se está autenticado e tem dados necessários
        if (!isAuthenticated || !userID || !empresaParse?.id_empresa || !objIdEmpresa) {
            console.warn('Não é possível pular etapa - dados não disponíveis');
            toast('Erro: dados de autenticação não disponíveis', { type: 'error' });
            return;
        }
        
        try {
            setLoading(true);
            
            // Marcar que a empresa não oferece entrega
            console.log('📤 Enviando requisição para marcar como skip...');
            console.log('Parâmetros:', { objIdEmpresa, idEmpresa: empresaParse.id_empresa, userID, tipo: 'skip' });
            
            const skipResponse = await updateTypeRegion(objIdEmpresa, empresaParse.id_empresa, userID, 'skip');
            console.log('✅ Resposta da API skip:', skipResponse);
            
            // Atualizar estado local
            setTypeOfRegion('skip');
            console.log('📝 Estado typeOfRegion atualizado para: skip');
            
            // Feedback para o cliente
            toast.success('Etapa pulada com sucesso! Configurado para apenas retirada no local.', { 
                type: "success",
                autoClose: 4000 
            });
            
            // Marcar etapa como concluída
            if (!completedSteps.includes(4)) {
                setCompletedSteps([...completedSteps, 4]);
                console.log('📋 Etapa 4 marcada como concluída');
            }
            
            // Avançar para próxima etapa (horários de funcionamento)
            setCurrentStep(5);
            console.log('➡️ Avançando para etapa 5 (horários de funcionamento)');

            // Salvar progresso no banco de dados
            try {
                if (objIdEmpresa) {
                    await atualizarProgressoConfiguracaoInicial(objIdEmpresa, 4, 5);
                    console.log('Progresso salvo: etapa 4 pulada, avançando para etapa 5');
                }
            } catch (error) {
                console.warn('Erro ao salvar progresso da configuração inicial:', error);
            }
            
        } catch (error) {
            console.error('❌ Erro ao pular etapa:', error);
            toast.error('Erro ao pular etapa. Tente novamente.', { type: "error" });
        } finally {
            setLoading(false);
            console.log('🏁 Processo de pular etapa finalizado');
        }
    };

    // Se ainda está verificando autenticação, mostrar carregamento
    if (isCheckingAuth) {
        return (
            <div className="configuracao-inicial-modern">
                <div className="loading-auth-container">
                    <div className="loading-spinner"></div>
                    <p>Verificando autenticação...</p>
                </div>
            </div>
        );
    }

    // Se não está autenticado, não renderizar nada (já foi redirecionado)
    if (!isAuthenticated) {
        return null;
    }

    // Se os dados necessários ainda não estão prontos, mostrar carregamento
    if (!isDataReady) {
        return (
            <div className="configuracao-inicial-modern">
                <div className="loading-auth-container">
                    <div className="loading-spinner"></div>
                    <p>Carregando dados...</p>
                </div>
            </div>
        );
    }

    const renderStepContent = () => {
        switch (currentStep) {
            case 1:
                return (
                    <div className="modern-step-content">
                        <div className="step-icon">
                            <AiOutlineUser />
                        </div>
                        <h3>✅ Informações Pessoais Concluídas</h3>
                        <p>Seus dados pessoais foram cadastrados com sucesso!</p>
                        <div className="completed-info">
                            <div className="info-item">
                                <AiOutlineCheck className="check-icon" />
                                <span>Nome da empresa cadastrado</span>
                            </div>
                            <div className="info-item">
                                <AiOutlineCheck className="check-icon" />
                                <span>Email e credenciais definidas</span>
                            </div>
                            <div className="info-item">
                                <AiOutlineCheck className="check-icon" />
                                <span>Conta criada com sucesso</span>
                            </div>
                        </div>
                        <div className="step-navigation">
                            <button 
                                onClick={handleNextStep}
                                className="btn-continue"
                            >
                                Próximo
                            </button>
                        </div>
                    </div>
                );
            case 2:
                return (
                    <div className="modern-step-content">
                        <div className="step-icon">
                            <AiOutlineHome />
                        </div>
                        <h3>📍 Endereço do Estabelecimento</h3>
                        <p>Informe o endereço do seu estabelecimento para configurar entregas e localização.</p>
                        
                        {/* Indicador de dados carregados */}
                        {empresaData && empresaData.cep && (
                            <div className="form-loaded-indicator">
                                <AiOutlineCheck className="icon" />
                                <span>Dados do endereço foram carregados automaticamente dos registros salvos.</span>
                            </div>
                        )}

                        {/* Banner de etapa já completada */}
                        {completedSteps.includes(2) && (
                            <div className="step-completed-banner">
                                <AiOutlineCheckCircle className="icon" />
                                <span>Esta etapa já foi completada anteriormente. Você pode editar os dados ou prosseguir.</span>
                                <button onClick={handleNextStep}>
                                    Próxima Etapa →
                                </button>
                            </div>
                        )}
                        
                        <form onSubmit={formikEndereco.handleSubmit} className="modern-form">
                            <div className="form-row">
                                <div className="form-group">
                                    <label>CEP *</label>
                                    <div className="input-with-loading">
                                        <InputMask
                                            mask="99999-999"
                                            name="cep"
                                            value={formikEndereco.values.cep}
                                            onChange={handleCepChange}
                                            placeholder="00000-000"
                                            disabled={cepLoading}
                                            className={cepLoading ? 'loading-configuracao-inicial' : ''}
                                        />
                                        {cepLoading && (
                                            <div className="loading-indicator-configuracao-inicial">
                                                <div className="spinner"></div>
                                            </div>
                                        )}
                                    </div>
                                    {formikEndereco.errors.cep && (
                                        <span className="error">{formikEndereco.errors.cep}</span>
                                    )}
                                    <small className="help-text">Digite o CEP para preenchimento automático</small>
                                </div>
                                <div className="form-group">
                                    <label>Estado *</label>
                                    <input
                                        type="text"
                                        name="estado"
                                        value={formikEndereco.values.estado}
                                        onChange={formikEndereco.handleChange}
                                        placeholder="Ex: SP"
                                        className={`${formikEndereco.values.estado ? 'auto-filled' : ''} ${empresaData && empresaData.estado ? 'data-filled' : ''}`}
                                    />
                                    {formikEndereco.errors.estado && (
                                        <span className="error">{formikEndereco.errors.estado}</span>
                                    )}
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label>Município *</label>
                                    <input
                                        type="text"
                                        name="municipio"
                                        value={formikEndereco.values.municipio}
                                        onChange={formikEndereco.handleChange}
                                        placeholder="Ex: São Paulo"
                                        className={`${formikEndereco.values.municipio ? 'auto-filled' : ''} ${empresaData && empresaData.municipio ? 'data-filled' : ''}`}
                                    />
                                    {formikEndereco.errors.municipio && (
                                        <span className="error">{formikEndereco.errors.municipio}</span>
                                    )}
                                </div>
                                <div className="form-group">
                                    <label>Bairro *</label>
                                    <input
                                        type="text"
                                        name="bairro"
                                        value={formikEndereco.values.bairro}
                                        onChange={formikEndereco.handleChange}
                                        placeholder="Ex: Centro"
                                        className={`${formikEndereco.values.bairro ? 'auto-filled' : ''} ${empresaData && empresaData.bairro ? 'data-filled' : ''}`}
                                    />
                                    {formikEndereco.errors.bairro && (
                                        <span className="error">{formikEndereco.errors.bairro}</span>
                                    )}
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group flex-2">
                                    <label>Logradouro *</label>
                                    <input
                                        type="text"
                                        name="logradouro"
                                        value={formikEndereco.values.logradouro}
                                        onChange={formikEndereco.handleChange}
                                        placeholder="Ex: Rua das Flores"
                                        className={`${formikEndereco.values.logradouro ? 'auto-filled' : ''} ${empresaData && empresaData.logradouro ? 'data-filled' : ''}`}
                                    />
                                    {formikEndereco.errors.logradouro && (
                                        <span className="error">{formikEndereco.errors.logradouro}</span>
                                    )}
                                </div>
                                <div className="form-group">
                                    <label>Número *</label>
                                    <input
                                        type="text"
                                        name="address_number"
                                        value={formikEndereco.values.address_number}
                                        onChange={formikEndereco.handleChange}
                                        placeholder="123"
                                        className={empresaData && empresaData.address_number ? 'data-filled' : ''}
                                    />
                                    {formikEndereco.errors.address_number && (
                                        <span className="error">{formikEndereco.errors.address_number}</span>
                                    )}
                                </div>
                            </div>

                            <div className="form-group">
                                <label>Complemento</label>
                                <input
                                    type="text"
                                    name="complemento"
                                    value={formikEndereco.values.complemento}
                                    onChange={formikEndereco.handleChange}
                                    placeholder="Ex: Sala 101, Andar 2"
                                    className={empresaData && empresaData.complemento ? 'data-filled' : ''}
                                />
                            </div>

                            <div className="step-navigation">
                                <button 
                                    type="button"
                                    onClick={handlePreviousStep}
                                    className="btn-back"
                                >
                                    Voltar
                                </button>
                                <button 
                                    type="submit" 
                                    disabled={loading}
                                    className={`btn-submit ${loading ? 'loading-configuracao-inicial' : ''}`}
                                >
                                    {loading ? 'Salvando...' : 'Salvar e Continuar'}
                                </button>
                            </div>
                        </form>
                    </div>
                );
            case 3:
                return (
                    <div className="modern-step-content">
                        <div className="step-icon">
                            <AiOutlineCheckCircle />
                        </div>
                        <h3>📍 Posicionamento no Mapa</h3>
                        <p>Ajuste a localização exata do seu estabelecimento no mapa para melhor precisão nas entregas.</p>
                        
                        <div className="config-inicial-map-instruction">
                            <div className="config-inicial-instruction-alert">
                                <FaExclamationCircle className="config-inicial-warning-icon" />
                                <span>Para mudar o local do estabelecimento, arraste o marcador 
                                    <MdLocationPin className="config-inicial-pin-icon" /> para a localização desejada e clique em Salvar.
                                </span>
                            </div>
                        </div>

                        <div className="config-inicial-map-container">
                            {latitudeEmpresa && longitudeEmpresa ? (
                                <>
                                    {/* Indicador de coordenadas carregadas */}
                                    <div className="form-loaded-indicator">
                                        <AiOutlineCheck className="icon" />
                                        <span>
                                            {/* Verificar se são coordenadas padrão de Brasília */}
                                            {parseFloat(latitudeEmpresa) === -15.7942 && parseFloat(longitudeEmpresa) === -47.8822 ? 
                                                'Mapa centralizado em Brasília. Arraste o marcador para a localização exata do seu estabelecimento.' :
                                                'Localização obtida automaticamente do seu endereço. Você pode ajustar a posição se necessário.'
                                            }
                                        </span>
                                    </div>
                                    <Map 
                                        latitudeEmpresa={latitudeEmpresa} 
                                        longitudeEmpresa={longitudeEmpresa}
                                        setLatitudeEmpresa={setLatitudeEmpresa} 
                                        setLongitudeEmpresa={setLongitudeEmpresa}
                                    />
                                </>
                            ) : (
                                <div className="config-inicial-map-no-coordinates">
                                    <div className="no-coordinates-message">
                                        <FaExclamationCircle className="warning-icon" />
                                        <h4>Posição não definida</h4>
                                        <p>Você ainda não definiu a localização exata do seu estabelecimento no mapa.</p>
                                        <p>Para continuar, você precisa:</p>
                                        <ol>
                                            <li>Clique no botão "Definir Localização" abaixo</li>
                                            <li>Arraste o marcador para a posição correta</li>
                                            <li>Clique em "Salvar e Continuar"</li>
                                        </ol>
                                        <button 
                                            className="btn-define-location"
                                            onClick={() => {
                                                // Definir coordenadas padrão apenas para centralizar o mapa
                                                // Brasília como referência inicial
                                                setLatitudeEmpresa(-15.7942);
                                                setLongitudeEmpresa(-47.8822);
                                            }}
                                        >
                                            Definir Localização
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>

                        <div className="step-navigation">
                            <button 
                                type="button"
                                onClick={handlePreviousStep}
                                className="btn-back"
                            >
                                Voltar
                            </button>
                            <button 
                                onClick={handleSaveMapLocation}
                                disabled={loading || !latitudeEmpresa || !longitudeEmpresa}
                                className={`btn-submit ${loading ? 'loading-configuracao-inicial' : ''} ${!latitudeEmpresa || !longitudeEmpresa ? 'disabled' : ''}`}
                            >
                                {loading ? 'Salvando...' : 'Salvar e Continuar'}
                            </button>
                        </div>
                    </div>
                );
            case 4:
                return (
                    <div className="modern-step-content">
                        <div className="step-icon">
                            <MdDeliveryDining />
                        </div>
                        <h3>🚚 Área de Entrega</h3>
                        <p>Configure como você deseja delimitar sua região de entrega: por bairros específicos ou por raio de distância.</p>
                        
                        {/* Aviso sobre pular etapa */}
                        <div className="delivery-info-banner">
                            <div className="info-content">
                                <span className="info-icon">💡</span>
                                <div>
                                    <strong>Trabalha apenas com retirada?</strong>
                                    <p>Se você não oferece serviço de entrega, pode pular esta etapa clicando em "Pular Etapa".</p>
                                </div>
                            </div>
                        </div>

                        {/* Seleção do tipo de região */}
                        <div className="delivery-type-selection">
                            <div className="delivery-type-buttons">
                                <button 
                                    type="button"
                                    onClick={() => {
                                        console.log('Botão bairro clicado');
                                        handleChangeRegionType("bairro");
                                    }}
                                    className={`delivery-type-btn ${typeOfRegion === "bairro" ? 'active' : ''}`}
                                >
                                    📍 Por Bairro
                                </button>
                                <button 
                                    type="button"
                                    onClick={() => {
                                        console.log('Botão raio clicado');
                                        handleChangeRegionType("raio");
                                    }}
                                    className={`delivery-type-btn ${typeOfRegion === "raio" ? 'active' : ''}`}
                                >
                                    📐 Por Raio (KM)
                                </button>
                            </div>
                        </div>

                        {/* Conteúdo baseado no tipo selecionado */}
                        {typeOfRegion === "bairro" ? (
                            <div className="delivery-config-section">
                                <h4>Configuração por Bairro</h4>
                                <p>Adicione os bairros onde você realiza entregas e defina o valor para cada um.</p>
                                
                                <form onSubmit={formikAreaEntrega.handleSubmit} className="delivery-form">
                                    <div className="form-row">
                                        <div className="form-group">
                                            <label>Nome do Bairro *</label>
                                            <input
                                                type="text"
                                                name="bairro_entrega"
                                                value={formikAreaEntrega.values.bairro_entrega}
                                                onChange={formikAreaEntrega.handleChange}
                                                placeholder="Digite o nome do bairro"
                                            />
                                            {formikAreaEntrega.errors.bairro_entrega && (
                                                <span className="error">{formikAreaEntrega.errors.bairro_entrega}</span>
                                            )}
                                        </div>
                                        <div className="form-group">
                                            <label>Valor da Entrega *</label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                value={valorEntrega}
                                                onChange={onChangeValorEntrega}
                                                placeholder="Ex: 5.50"
                                            />
                                        </div>
                                        <button 
                                            type="submit" 
                                            className="btn-add-delivery"
                                            disabled={!formikAreaEntrega.values.bairro_entrega || formikAreaEntrega.values.bairro_entrega.trim() === '' || valorEntrega === '' || valorEntrega < 0}
                                        >
                                            Adicionar
                                        </button>
                                    </div>
                                </form>

                                {/* Lista de bairros cadastrados */}
                                <div className="delivery-list">
                                    <h5>Bairros cadastrados ({arrayBairroEntrega.length})</h5>
                                    {arrayBairroEntrega.length > 0 ? (
                                        <div className="delivery-table">
                                            <div className="delivery-table-header">
                                                <span>Bairro</span>
                                                <span>Valor</span>
                                                <span>Ações</span>
                                            </div>
                                            {arrayBairroEntrega.map((item, index) => (
                                                <div key={index} className="delivery-table-row">
                                                    <span>{item.bairro_entrega}</span>
                                                    <span>R$ {item.valor_entrega.toFixed(2).replace('.', ',')}</span>
                                                    <button 
                                                        type="button"
                                                        onClick={() => handleDeleteBairro(item._id)}
                                                        className="btn-delete"
                                                    >
                                                        <FaTrash />
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="no-delivery-areas">Nenhum bairro cadastrado ainda.</p>
                                    )}
                                </div>
                            </div>
                        ) : typeOfRegion === "raio" ? (
                            <div className="delivery-config-section">
                                <h4>Configuração por Raio</h4>
                                <p>Defina raios de entrega em quilômetros a partir da sua localização e o valor para cada raio.</p>
                                
                                <div className="delivery-form">
                                    <div className="form-row">
                                        <div className="form-group">
                                            <label>Raio (KM) *</label>
                                            <div className="number-input-group">
                                                <button 
                                                    type="button" 
                                                    className={`number-btn ${raio === 1 ? 'disabled' : ''}`}
                                                    onClick={() => handleDecrement("raio")}
                                                    disabled={raio === 1}
                                                >
                                                    -
                                                </button>
                                                <input
                                                    type="number"
                                                    min="1"
                                                    max="9999"
                                                    value={raio}
                                                    onChange={(e) => {
                                                        const value = parseInt(e.target.value);
                                                        if (value > 0 && value <= 9999) {
                                                            setRaio(value);
                                                        } else if (e.target.value === '') {
                                                            setRaio('');
                                                        }
                                                    }}
                                                />
                                                <button 
                                                    type="button" 
                                                    className="number-btn"
                                                    onClick={() => handleIncrement("raio")}
                                                >
                                                    +
                                                </button>
                                            </div>
                                        </div>
                                        <div className="form-group">
                                            <label>Valor da Entrega *</label>
                                            <input
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                value={valorEntrega}
                                                onChange={onChangeValorEntrega}
                                                placeholder="Ex: 5.50"
                                            />
                                        </div>
                                        <button 
                                            type="button" 
                                            className="btn-add-delivery"
                                            onClick={handleAddRaio}
                                            disabled={!raio || raio === 0 || raio === '' || valorEntrega === '' || valorEntrega < 0}
                                        >
                                            Adicionar
                                        </button>
                                    </div>
                                </div>

                                {/* Lista de raios cadastrados */}
                                <div className="delivery-list">
                                    <h5>Raios cadastrados ({arrayRaioEntrega.length})</h5>
                                    {arrayRaioEntrega.length > 0 ? (
                                        <div className="delivery-table">
                                            <div className="delivery-table-header">
                                                <span>Raio (KM)</span>
                                                <span>Valor</span>
                                                <span>Ações</span>
                                            </div>
                                            {arrayRaioEntrega
                                                .sort((a, b) => a.raio_entrega - b.raio_entrega)
                                                .map((item, index) => (
                                                <div key={index} className="delivery-table-row">
                                                    <span>{(item.raio_entrega / 1000)} km</span>
                                                    <span>R$ {item.valor_entrega.toFixed(2).replace('.', ',')}</span>
                                                    <button 
                                                        type="button"
                                                        onClick={() => handleDeleteRaio(item._id)}
                                                        className="btn-delete"
                                                    >
                                                        <FaTrash />
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="no-delivery-areas">Nenhum raio cadastrado ainda.</p>
                                    )}
                                </div>
                            </div>
                        ) : (
                            <div className="delivery-selection-prompt">
                                <p>👆 Por favor, selecione o tipo de região de entrega acima para continuar.</p>
                                <p style={{ marginTop: '8px', fontSize: '13px', color: '#666' }}>
                                    Ou clique em "Pular Etapa" se trabalha apenas com retirada no local.
                                </p>
                            </div>
                        )}

                        <div className="step-navigation">
                            <button 
                                type="button"
                                onClick={handlePreviousStep}
                                className="btn-back"
                            >
                                Voltar
                            </button>
                            <button 
                                type="button"
                                onClick={handleSkipDeliveryArea}
                                disabled={loading}
                                className={`btn-skip ${loading ? 'loading-configuracao-inicial' : ''}`}
                            >
                                {loading ? 'Pulando...' : 'Pular Etapa'}
                            </button>
                            <button 
                                onClick={handleSaveAreaEntrega}
                                disabled={loading || !typeOfRegion || 
                                    (typeOfRegion === 'raio' && arrayRaioEntrega.length === 0) ||
                                    (typeOfRegion === 'bairro' && arrayBairroEntrega.length === 0)
                                }
                                className={`btn-submit ${loading ? 'loading-configuracao-inicial' : ''} ${
                                    !typeOfRegion || 
                                    (typeOfRegion === 'raio' && arrayRaioEntrega.length === 0) ||
                                    (typeOfRegion === 'bairro' && arrayBairroEntrega.length === 0) 
                                    ? 'disabled' : ''}`}
                            >
                                {loading ? 'Salvando...' : 'Salvar e Continuar'}
                            </button>
                        </div>
                    </div>
                );
            case 5:
                return (
                    <div className="modern-step-content">
                        <div className="step-icon">
                            <MdSchedule />
                        </div>
                        <div className="step-header-with-help">
                            <h3>🕒 Horários de Funcionamento</h3>
                            <button 
                                className="help-button"
                                onClick={() => setShowHelpModal(true)}
                                type="button"
                            >
                                <BsQuestionCircleFill />
                                <span>Precisa de ajuda?</span>
                            </button>
                        </div>
                        <p>Configure os horários de atendimento do seu estabelecimento.</p>
                        
                        {/* Banner informativo */}
                        <div className="delivery-info-banner">
                            <div className="info-content">
                                <span className="info-icon">💡</span>
                                <div>
                                    <strong>Configure seus horários</strong>
                                    <p>Defina quando seu estabelecimento está disponível para receber pedidos. Clique em "Precisa de ajuda?" para ver exemplos práticos.</p>
                                </div>
                            </div>
                        </div>

                        <div className="schedule-settings">
                            {/* Seleção de Fuso Horário */}
                            <div className="timezone-select">
                                <label htmlFor="timezone">Fuso horário *</label>
                                <select
                                    id="timezone"
                                    name="timezone"
                                    value={selectedTimeZone}
                                    onChange={e => setSelectedTimeZone(e.target.value)}
                                    disabled={!isAuthenticated}
                                >
                                    {timeZones.map((zone) => (
                                        <option key={zone.zoneName} value={zone.zoneName}>
                                            {zone.zoneName.replace('_', ' ')}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Opções de Disponibilidade */}
                            <div className="availability-options">
                                <div className={`option ${availability === 'sempre' ? 'selected' : ''}`}>
                                    <input 
                                        type="radio" 
                                        id="alwaysAvailable" 
                                        name="availability" 
                                        value="sempre" 
                                        checked={availability === 'sempre'}
                                        onChange={handleAvailabilityChange}
                                        disabled={!isAuthenticated}
                                    />
                                    <label htmlFor="alwaysAvailable">
                                        <div className="option-header">
                                            <span className="option-title">🔄 Sempre disponível</span>
                                            <div 
                                                className="tooltip-container"
                                                onMouseEnter={() => showTooltipFor('always-available')}
                                                onMouseLeave={hideTooltip}
                                            >
                                                <AiOutlineInfoCircle 
                                                    className="info-icon-small"
                                                />
                                                <CustomTooltip 
                                                    id="always-available" 
                                                    content="Seu estabelecimento funcionará 24 horas por dia, 7 dias por semana. Ideal para delivery que não para!"
                                                />
                                            </div>
                                        </div>
                                        <div className="option-description">
                                            Aberto 24 horas, todos os dias da semana
                                        </div>
                                    </label>
                                </div>

                                <div className={`option ${availability === 'especifico' ? 'selected' : ''}`}>
                                    <input 
                                        type="radio" 
                                        id="specificTimes" 
                                        name="availability" 
                                        value="especifico" 
                                        checked={availability === 'especifico'}
                                        onChange={handleAvailabilityChange}
                                        disabled={!isAuthenticated}
                                    />
                                    <label htmlFor="specificTimes">
                                        <div className="option-header">
                                            <span className="option-title">📅 Horários específicos</span>
                                            <div 
                                                className="tooltip-container"
                                                onMouseEnter={() => showTooltipFor('specific-times')}
                                                onMouseLeave={hideTooltip}
                                            >
                                                <AiOutlineInfoCircle 
                                                    className="info-icon-small"
                                                />
                                                <CustomTooltip 
                                                    id="specific-times" 
                                                    content="Configure dias e horários exatos. Exemplo: Segunda a Sexta das 08:00 às 18:00"
                                                />
                                            </div>
                                        </div>
                                        <div className="option-description">
                                            Defina dias e horários personalizados
                                        </div>
                                    </label>
                                </div>
                            </div>

                            {/* Configuração de Horários Específicos */}
                            {availability === 'especifico' && (
                                <>
                                    {/* Seletor de Dias */}
                                    <div className="day-selector">
                                        <div className="section-header-with-help">
                                            <label>Dias da semana *</label>
                                            <div 
                                                className="tooltip-container"
                                                onMouseEnter={() => showTooltipFor('day-selector')}
                                                onMouseLeave={hideTooltip}
                                            >
                                                <AiOutlineInfoCircle 
                                                    className="info-icon-small"
                                                />
                                                <CustomTooltip 
                                                    id="day-selector" 
                                                    content="Clique nas letras dos dias para ativar/desativar. Dias selecionados ficam azuis!"
                                                    position="bottom"
                                                />
                                            </div>
                                        </div>
                                        <div className="days-instruction">
                                            <span>👆 Clique nos dias que seu estabelecimento funcionará</span>
                                        </div>
                                        <div className="days">
                                            {daysOfWeek.map((day) => (
                                                <div
                                                    key={day}
                                                    className={activeDays.has(day) ? 'custom-checkbox checked' : 'custom-checkbox'}
                                                    onClick={() => isAuthenticated && toggleDay(day)}
                                                    title={`${mapDayToFull(day)} - Clique para ${activeDays.has(day) ? 'desativar' : 'ativar'}`}
                                                >
                                                    {activeDays.has(day) ? 
                                                        <p className="fas fa-check">{mapDayToOneChar(day)}</p> : 
                                                        <p>{mapDayToOneChar(day)}</p>
                                                    }
                                                </div>
                                            ))}
                                        </div>
                                        {/*}
                                        <div className="days-legend">
                                            <span className="legend-item">
                                                <div className="legend-checkbox checked-sample">S</div>
                                                <span>Exemplo do dia Ativo</span>
                                            </span>
                                            <span className="legend-item">
                                                <div className="legend-checkbox unchecked-sample">S</div>
                                                <span>Exemplo do diaInativo</span>
                                            </span>
                                        </div>
                                        */}
                                    </div>

                                    {/* Horários para cada dia ativo */}
                                    {activeDays.size === 0 && (
                                        <div className="no-days-selected">
                                            <AiOutlineInfoCircle className="info-icon" />
                                            <p>Selecione pelo menos um dia acima para configurar os horários</p>
                                        </div>
                                    )}
                                    
                                    {daysOfWeek.map((day) => (
                                        activeDays.has(day) && (
                                            <div key={day} className="divInputsFuncionamento">
                                                <div className="day-header-with-help">
                                                    <h5>{mapDayToFull(day)} *</h5>
                                                    {timeRanges[day] && timeRanges[day].length === 1 && (
                                                        <div 
                                                            className="tooltip-container"
                                                            onMouseEnter={() => showTooltipFor(`multiple-hours-${day}`)}
                                                            onMouseLeave={hideTooltip}
                                                        >
                                                            <AiOutlineInfoCircle 
                                                                className="info-icon-small"
                                                            />
                                                            <CustomTooltip 
                                                                id={`multiple-hours-${day}`} 
                                                                content="Precisa de mais horários? Clique em 'Adicionar Horário' para criar intervalos como almoço e jantar!"
                                                                position="right"
                                                            />
                                                        </div>
                                                    )}
                                                </div>
                                                
                                                {Array.isArray(timeRanges[day]) && timeRanges[day].map((range, index) => (
                                                    <div key={index} className="timeInputFuncionamento">
                                                        <div className="time-range-label">
                                                            {index === 0 ? '🌅 Período 1:' : `🌆 Período ${index + 1}:`}
                                                        </div>
                                                        <input
                                                            type="time"
                                                            value={range.start || '08:00'}
                                                            style={{padding: 5}}
                                                            onChange={(e) =>
                                                                handleTimeChange(day, index, 'start', e.target.value)
                                                            }
                                                            disabled={!isAuthenticated}
                                                            title="Horário de abertura"
                                                        />
                                                        <span>às</span>
                                                        <input
                                                            type="time"
                                                            value={range.end || '18:00'}
                                                            style={{padding: 5}}
                                                            onChange={(e) =>
                                                                handleTimeChange(day, index, 'end', e.target.value)
                                                            }
                                                            disabled={!isAuthenticated}
                                                            title="Horário de fechamento"
                                                        />
                                                        {index === 0 ? null : (
                                                            <div 
                                                                className="btnRemoveHorarioFuncionamento" 
                                                                onClick={() => isAuthenticated && removeTimeRange(day, index)}
                                                                title="Remover este horário"
                                                            >
                                                                <FaRegTrashAlt /><i>Remover</i>
                                                            </div>
                                                        )}
                                                    </div>
                                                ))}
                                                
                                                <div 
                                                    className="btnAddHorarioFuncionamento" 
                                                    onClick={() => isAuthenticated && addTimeRange(day)}
                                                    title="Adicionar outro horário para este dia (exemplo: horário de almoço e jantar)"
                                                >
                                                    <LuPlusCircle />
                                                    <span>Adicionar Horário</span>
                                                    <span className="add-hour-hint">
                                                        {timeRanges[day] && timeRanges[day].length === 1 ? 
                                                            '(ex: almoço e jantar)' : 
                                                            `(${timeRanges[day]?.length || 0} horários)`
                                                        }
                                                    </span>
                                                </div>
                                                
                                                {timeRanges[day] && timeRanges[day].length > 1 && (
                                                    <div className="multiple-hours-tip">
                                                        <FaInfoCircle />
                                                        <span>Múltiplos horários configurados para {mapDayToFull(day)}</span>
                                                    </div>
                                                )}
                                            </div>
                                        )
                                    ))}
                                </>
                            )}
                        </div>

                        <div className="step-navigation">
                            <button 
                                type="button"
                                onClick={handlePreviousStep}
                                className="btn-back"
                            >
                                Voltar
                            </button>
                            <button 
                                onClick={handleSaveHorarioFuncionamento}
                                disabled={loading || !isAuthenticated || 
                                    (availability === 'especifico' && activeDays.size === 0)
                                }
                                className={`btn-submit ${loading ? 'loading-configuracao-inicial' : ''} ${
                                    !isAuthenticated || (availability === 'especifico' && activeDays.size === 0) 
                                    ? 'disabled' : ''}`}
                            >
                                {loading ? 'Salvando...' : 'Salvar e Continuar'}
                            </button>
                        </div>

                        {/* Modal de Ajuda */}
                        <HelpModal />
                    </div>
                );
            case 6:
                return (
                    <div className="modern-step-content">
                        <div className="step-icon">
                            <MdRestaurantMenu />
                        </div>
                        <h3>🍽️ Adicionar Cardápio</h3>
                        <p>Escolha como deseja criar seu cardápio para começar a vender!</p>
                        
                        {/* Banner informativo */}
                        <div className="cardapio-info-banner">
                            <div className="info-content">
                                <span className="info-icon">💡</span>
                                <div>
                                    <strong>Facilitamos o processo para você começar rapidamente!</strong>
                                    <p>Escolha a opção que melhor se adequa ao seu negócio. Você pode sempre editar seu cardápio depois!</p>
                                </div>
                            </div>
                        </div>

                        {/* Opções de cardápio */}
                        <div className="cardapio-options">
                            <div className="cardapio-option-card">
                                <div className="option-icon">
                                    <FaFileUpload />
                                </div>
                                <h4>📥 Importar do iFood (mais rápido)</h4>
                                <p>Já tem um cardápio no iFood? Importe tudo automaticamente e evite o cadastro manual.</p>
                                <div className="option-benefits">
                                    <span className="benefit">✅ Rápido e automático</span>
                                    <span className="benefit">✅ Mantém preços e descrições</span>
                                    <span className="benefit">✅ Importa fotos dos produtos</span>
                                </div>
                                <button 
                                    type="button"
                                    onClick={() => handleCardapioOptionChange('importar')}
                                    className={`option-btn ${cardapioOption === 'importar' ? 'active' : ''}`}
                                >
                                    Importar Cardápio
                                </button>
                            </div>

                            <div className="cardapio-option-card">
                                <div className="option-icon">
                                    <FaHandshake />
                                </div>
                                <h4>🤝 Deixe com a gente</h4>
                                <p>Envie seu cardápio em PDF ou link, e nossa equipe cuida de tudo para você!</p>
                                <div className="option-benefits">
                                    <span className="benefit">✅ Sem trabalho para você</span>
                                    <span className="benefit">✅ Feito por profissionais</span>
                                    <span className="benefit">✅ Aceita PDF ou link</span>
                                </div>
                                <button 
                                    type="button"
                                    onClick={() => handleCardapioOptionChange('deixar')}
                                    className={`option-btn ${cardapioOption === 'deixar' ? 'active' : ''}`}
                                >
                                    Deixar com Equipe
                                </button>
                            </div>

                            <div className="cardapio-option-card">
                                <div className="option-icon">
                                    <MdCreateNewFolder />
                                </div>
                                <h4>👨‍💼 Pular e criar depois</h4>
                                <p>Prefere começar a vender rapidamente? Pule agora e crie seu cardápio completo no gestor dedicado.</p>
                                <div className="option-benefits">
                                    <span className="benefit">✅ Comece rapidamente</span>
                                    <span className="benefit">✅ Acesse gestor completo</span>
                                    <span className="benefit">✅ Crie quando quiser</span>
                                </div>
                                <button 
                                    type="button"
                                    onClick={() => handleCardapioOptionChange('criar')}
                                    className={`option-btn ${cardapioOption === 'criar' ? 'active' : ''}`}
                                >
                                    Pular Cardápio
                                </button>
                            </div>
                        </div>

                        {/* Conteúdo baseado na opção selecionada */}
                        {cardapioOption === 'importar' && (
                            <div className="cardapio-import-section">
                                <h4>📥 Importar Cardápio</h4>
                                <p>Selecione de onde deseja importar seu cardápio:</p>
                                
                                {/* Switch para escolher fonte de importação */}
                                <div className="import-source-switch">
                                    <div className="switch-header">
                                        <span>Escolha a fonte do seu cardápio:</span>
                                    </div>
                                    <div className="switch-buttons">
                                        <button 
                                            type="button"
                                            onClick={() => setSelectedImportSource('ifood')}
                                            className={`switch-btn ${selectedImportSource === 'ifood' ? 'active' : ''}`}
                                        >
                                            <FaUtensils className="switch-icon ifood" />
                                            iFood
                                        </button>
                                        <button 
                                            type="button"
                                            onClick={() => setSelectedImportSource('anotaai')}
                                            className={`switch-btn ${selectedImportSource === 'anotaai' ? 'active' : ''}`}
                                        >
                                            <FaUtensils className="switch-icon anotaai" />
                                            AnotaAí
                                        </button>
                                    </div>
                                </div>

                                {/* Opção de importação selecionada */}
                                <div className="import-option-selected">
                                    {selectedImportSource === 'ifood' && (
                                        <div className="import-option">
                                            <div className="import-header">
                                                <FaUtensils className="import-icon ifood" />
                                                <span>Importar do iFood</span>
                                            </div>
                                            <div className="import-form">
                                                <label>Link do seu cardápio iFood:</label>
                                                <input
                                                    type="text"
                                                    placeholder="https://www.ifood.com.br/delivery/..."
                                                    value={cardapioFormData.ifoodLink}
                                                    onChange={(e) => setCardapioFormData(prev => ({...prev, ifoodLink: e.target.value}))}
                                                    disabled={!isAuthenticated}
                                                />
                                                <button 
                                                    type="button"
                                                    onClick={() => {
                                                        setShowVideo(true);
                                                        setVideoType('ifood');
                                                    }}
                                                    className="help-link"
                                                >
                                                    Como encontrar o link do iFood?
                                                </button>
                                                
                                                {/* Barra de progresso corrigida */}
                                                {((importProgress !== null && importProgress < 100) || isImporting) && (
                                                    <div className="import-progress">
                                                        <div className="progress-info">
                                                            <span className="progress-message">
                                                                {importMessage || 'Importando...'}
                                                            </span>
                                                            <span className="progress-percentage">
                                                                {importProgress !== null ? `${importProgress}%` : '0%'}
                                                            </span>
                                                        </div>
                                                        <ProgressBar 
                                                            now={importProgress || 0} 
                                                            animated={isImporting}
                                                            variant="success"
                                                            style={{ height: '25px', marginBottom: '10px' }}
                                                        />
                                                        {importDetails && importStage === 'categorias' && (
                                                            <div className="progress-details">
                                                                <small>
                                                                    📋 Processando categoria: <strong>{importDetails.categoria_atual}</strong>
                                                                    <br />
                                                                    ({importDetails.categorias_processadas}/{importDetails.total_categorias})
                                                                </small>
                                                            </div>
                                                        )}
                                                        {importDetails && importStage === 'itens' && (
                                                            <div className="progress-details">
                                                                <small>
                                                                    🍽️ Processando itens da categoria: <strong>{importDetails.categoria_atual}</strong>
                                                                    <br />
                                                                    ({importDetails.itens_processados}/{importDetails.total_itens})
                                                                </small>
                                                            </div>
                                                        )}
                                                        {importDetails && importStage === 'adicionais' && (
                                                            <div className="progress-details">
                                                                <small>
                                                                    ➕ Processando grupo: <strong>{importDetails.grupo_atual}</strong>
                                                                    <br />
                                                                    ({importDetails.grupos_processados}/{importDetails.total_grupos})
                                                                </small>
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                                
                                                {importComplete && (
                                                    <div className="import-success">
                                                        <AiOutlineCheck className="success-icon" />
                                                        <span>Importação concluída com sucesso!</span>
                                                        <button 
                                                            type="button"
                                                            onClick={handleFinishCardapioImport}
                                                            className="btn-advance-step"
                                                            style={{ 
                                                                marginTop: '15px', 
                                                                padding: '10px 20px',
                                                                backgroundColor: '#28a745',
                                                                color: 'white',
                                                                border: 'none',
                                                                borderRadius: '5px',
                                                                cursor: 'pointer'
                                                            }}
                                                        >
                                                            Avançar para Próxima Etapa →
                                                        </button>
                                                    </div>
                                                )}
                                                
                                                {importStage === 'erro' && (
                                                    <div className="import-error" style={{ marginBottom: '15px' }}>
                                                        <div className="error-info" style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                                                            <FaExclamationCircle className="error-icon" style={{ color: '#dc3545', marginRight: '8px' }} />
                                                            <span style={{ color: '#dc3545' }}>Erro durante importação. Tente novamente.</span>
                                                        </div>
                                                        <button 
                                                            type="button"
                                                            onClick={handleResetImportStatus}
                                                            disabled={loading}
                                                            className="btn-reset-import"
                                                            style={{ 
                                                                marginTop: '5px', 
                                                                padding: '8px 16px',
                                                                backgroundColor: '#dc3545',
                                                                color: 'white',
                                                                border: 'none',
                                                                borderRadius: '5px',
                                                                cursor: 'pointer',
                                                                fontSize: '14px'
                                                            }}
                                                        >
                                                            {loading ? 'Resetando...' : 'Resetar e Tentar Novamente'}
                                                        </button>
                                                    </div>
                                                )}
                                                
                                                <button 
                                                    type="button"
                                                    onClick={() => handleImportCardapio('ifood', cardapioFormData.ifoodLink)}
                                                    disabled={!cardapioFormData.ifoodLink || isImporting || importComplete || !isAuthenticated}
                                                    className="import-btn"
                                                >
                                                    {isImporting ? 'Importando...' : 'Importar do iFood'}
                                                </button>
                                            </div>
                                        </div>
                                    )}

                                    {selectedImportSource === 'anotaai' && (
                                        <div className="import-option">
                                            <div className="import-header">
                                                <FaUtensils className="import-icon anotaai" />
                                                <span>Importar do AnotaAí</span>
                                            </div>
                                            <div className="import-form">
                                                <label>Link do seu cardápio AnotaAí:</label>
                                                <input
                                                    type="text"
                                                    placeholder="https://pedido.anota.ai/loja/..."
                                                    value={cardapioFormData.anotaaiLink}
                                                    onChange={(e) => setCardapioFormData(prev => ({...prev, anotaaiLink: e.target.value}))}
                                                    disabled={!isAuthenticated}
                                                />
                                                <button 
                                                    type="button"
                                                    onClick={() => {
                                                        setShowVideo(true);
                                                        setVideoType('anotaai');
                                                    }}
                                                    className="help-link"
                                                >
                                                    Como encontrar o link do AnotaAí?
                                                </button>
                                                
                                                {/* Barra de progresso corrigida */}
                                                {((importProgress !== null && importProgress < 100) || isImporting) && (
                                                    <div className="import-progress">
                                                        <div className="progress-info">
                                                            <span className="progress-message">
                                                                {importMessage || 'Importando...'}
                                                            </span>
                                                            <span className="progress-percentage">
                                                                {importProgress !== null ? `${importProgress}%` : '0%'}
                                                            </span>
                                                        </div>
                                                        <ProgressBar 
                                                            now={importProgress || 0} 
                                                            animated={isImporting}
                                                            variant="success"
                                                            style={{ height: '25px', marginBottom: '10px' }}
                                                        />
                                                        {importDetails && importStage === 'categorias' && (
                                                            <div className="progress-details">
                                                                <small>
                                                                    📋 Processando categoria: <strong>{importDetails.categoria_atual}</strong>
                                                                    <br />
                                                                    ({importDetails.categorias_processadas}/{importDetails.total_categorias})
                                                                </small>
                                                            </div>
                                                        )}
                                                        {importDetails && importStage === 'itens' && (
                                                            <div className="progress-details">
                                                                <small>
                                                                    🍽️ Processando itens da categoria: <strong>{importDetails.categoria_atual}</strong>
                                                                    <br />
                                                                    ({importDetails.itens_processados}/{importDetails.total_itens})
                                                                </small>
                                                            </div>
                                                        )}
                                                        {importDetails && importStage === 'adicionais' && (
                                                            <div className="progress-details">
                                                                <small>
                                                                    ➕ Processando grupo: <strong>{importDetails.grupo_atual}</strong>
                                                                    <br />
                                                                    ({importDetails.grupos_processados}/{importDetails.total_grupos})
                                                                </small>
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                                
                                                {importComplete && (
                                                    <div className="import-success">
                                                        <AiOutlineCheck className="success-icon" />
                                                        <span>Importação concluída com sucesso!</span>
                                                        <button 
                                                            type="button"
                                                            onClick={handleFinishCardapioImport}
                                                            className="btn-advance-step"
                                                            style={{ 
                                                                marginTop: '15px', 
                                                                padding: '10px 20px',
                                                                backgroundColor: '#28a745',
                                                                color: 'white',
                                                                border: 'none',
                                                                borderRadius: '5px',
                                                                cursor: 'pointer'
                                                            }}
                                                        >
                                                            Avançar para Próxima Etapa →
                                                        </button>
                                                    </div>
                                                )}
                                                
                                                {importStage === 'erro' && (
                                                    <div className="import-error" style={{ marginBottom: '15px' }}>
                                                        <div className="error-info" style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                                                            <FaExclamationCircle className="error-icon" style={{ color: '#dc3545', marginRight: '8px' }} />
                                                            <span style={{ color: '#dc3545' }}>Erro durante importação. Tente novamente.</span>
                                                        </div>
                                                        <button 
                                                            type="button"
                                                            onClick={handleResetImportStatus}
                                                            disabled={loading}
                                                            className="btn-reset-import"
                                                            style={{ 
                                                                marginTop: '5px', 
                                                                padding: '8px 16px',
                                                                backgroundColor: '#dc3545',
                                                                color: 'white',
                                                                border: 'none',
                                                                borderRadius: '5px',
                                                                cursor: 'pointer',
                                                                fontSize: '14px'
                                                            }}
                                                        >
                                                            {loading ? 'Resetando...' : 'Resetar e Tentar Novamente'}
                                                        </button>
                                                    </div>
                                                )}
                                                
                                                <button 
                                                    type="button"
                                                    onClick={() => handleImportCardapio('anotaai', cardapioFormData.anotaaiLink)}
                                                    disabled={!cardapioFormData.anotaaiLink || isImporting || importComplete || !isAuthenticated}
                                                    className="import-btn"
                                                >
                                                    {isImporting ? 'Importando...' : 'Importar do AnotaAí'}
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* Modal de vídeo */}
                                {showVideo && (
                                    <div className="video-modal-overlay" onClick={() => setShowVideo(false)}>
                                        <div className="video-modal-content" onClick={(e) => e.stopPropagation()}>
                                            <div className="video-modal-header">
                                                <h4>Como encontrar o link do {videoType === 'ifood' ? 'iFood' : 'AnotaAí'}</h4>
                                                <button 
                                                    className="video-modal-close"
                                                    onClick={() => setShowVideo(false)}
                                                >
                                                    ×
                                                </button>
                                            </div>
                                            <div className="video-modal-body">
                                                <p>Vídeo tutorial estará disponível em breve!</p>
                                                <div className="video-placeholder">
                                                    <FaUtensils className="video-icon" />
                                                    <p>Tutorial do {videoType === 'ifood' ? 'iFood' : 'AnotaAí'}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}

                        {cardapioOption === 'deixar' && (
                            <div className="cardapio-team-section">
                                <h4>🤝 Deixe Nossa Equipe Cuidar do Seu Cardápio</h4>
                                <p>Envie seu cardápio e nossa equipe especializará criará tudo para você!</p>
                                
                                <div className="team-form">
                                    <div className="form-group">
                                        <label>
                                            <FaLink className="form-icon" />
                                            Link do seu cardápio (se tiver):
                                        </label>
                                        <input
                                            type="text"
                                            placeholder="https://exemplo.com/cardapio ou deixe vazio se for enviar PDF"
                                            value={cardapioFormData.customLink}
                                            onChange={(e) => setCardapioFormData(prev => ({...prev, customLink: e.target.value}))}
                                            disabled={!isAuthenticated}
                                        />
                                    </div>

                                    <div className="form-group">
                                        <label>
                                            <FaFilePdf className="form-icon" />
                                            Ou anexe seu cardápio em PDF:
                                        </label>
                                        <div className="file-upload">
                                            <input
                                                type="file"
                                                accept=".pdf"
                                                onChange={(e) => setCardapioFormData(prev => ({...prev, pdfFile: e.target.files[0]}))}
                                                disabled={!isAuthenticated}
                                                id="pdfFile"
                                            />
                                            <label htmlFor="pdfFile" className="file-upload-btn">
                                                <FaCloudUploadAlt />
                                                {cardapioFormData.pdfFile ? cardapioFormData.pdfFile.name : 'Selecionar PDF'}
                                            </label>
                                        </div>
                                    </div>

                                    <div className="form-group">
                                        <label>
                                            <AiOutlineFileText className="form-icon" />
                                            Observações adicionais:
                                        </label>
                                        <textarea
                                            placeholder="Alguma observação especial? Ex: preços específicos, categorias especiais, etc."
                                            value={cardapioFormData.observacoes}
                                            onChange={(e) => setCardapioFormData(prev => ({...prev, observacoes: e.target.value}))}
                                            disabled={!isAuthenticated}
                                            rows="4"
                                        />
                                    </div>

                                    <div className="team-info">
                                        <div className="info-box-cfg-inicial">
                                            <h5>📋 O que nossa equipe fará:</h5>
                                            <ul>
                                                <li>✅ Analisar seu cardápio</li>
                                                <li>✅ Criar todas as categorias</li>
                                                <li>✅ Cadastrar todos os produtos</li>
                                                <li>✅ Definir preços e descrições</li>
                                                <li>✅ Organizar de forma atrativa</li>
                                            </ul>
                                        </div>
                                        <div className="info-box-cfg-inicial">
                                            <h5>⏱️ Prazo de entrega:</h5>
                                            <p>Em até <strong>24 horas</strong> úteis seu cardápio estará pronto!</p>
                                        </div>
                                    </div>

                                    <button 
                                        type="button"
                                        onClick={handleSendCardapioToTeam}
                                        disabled={(!cardapioFormData.customLink && !cardapioFormData.pdfFile) || loading || !isAuthenticated}
                                        className={`send-team-btn ${loading ? 'loading' : ''}`}
                                    >
                                        {loading ? 'Enviando...' : 'Enviar para Nossa Equipe'}
                                    </button>
                                </div>
                            </div>
                        )}

                        {cardapioOption === 'criar' && (
                            <div className="cardapio-create-section">
                                <h4>👨‍💼 Criar Seu Cardápio</h4>
                                <p>Você pode criar seu cardápio completo no gestor de cardápio após finalizar a configuração inicial.</p>
                                
                                <div className="create-info-box">
                                    <div className="info-content">
                                        <MdRestaurantMenu className="info-icon" />
                                        <div>
                                            <h5>Acesse o Gestor de Cardápio</h5>
                                            <p>Após concluir a configuração inicial, você terá acesso a uma área completa para:</p>
                                            <ul>
                                                <li>✅ Criar categorias personalizadas</li>
                                                <li>✅ Adicionar produtos com fotos</li>
                                                <li>✅ Definir preços e descrições</li>
                                                <li>✅ Configurar adicionais e variações</li>
                                                <li>✅ Organizar por disponibilidade</li>
                                                <li>✅ Gerenciar horários específicos</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div className="create-actions">
                                    <button 
                                        type="button"
                                        onClick={handleSkipCardapio}
                                        className="skip-cardapio-btn"
                                        disabled={loading}
                                    >
                                        {loading ? 'Pulando...' : 'Pular e Criar Cardápio Depois'}
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* Mostrar botão de finalização se importação estiver completa */}
                        {cardapioOption === 'importar' && importComplete && (
                            <div className="import-complete-actions">
                                <button 
                                    type="button"
                                    onClick={handleFinishCardapioImport}
                                    className="finish-import-btn"
                                >
                                    Continuar com Cardápio Importado
                                </button>
                            </div>
                        )}

                        <div className="step-navigation">
                            <button 
                                type="button"
                                onClick={handlePreviousStep}
                                className="btn-back"
                            >
                                Voltar
                            </button>
                        </div>
                    </div>
                );
            case 7:
                return (
                    <div className="modern-step-content">
                        <div className="step-icon">
                            <FaTruck />
                        </div>
                        <h3>🚚 Configurações de Entrega e Retirada</h3>
                        <p>Configure quais tipos de serviço sua loja oferece aos clientes.</p>
                        
                        {/* Banner informativo */}
                        <div className="delivery-info-banner">
                            <div className="info-content">
                                <span className="info-icon">💡</span>
                                <div>
                                    <strong>Configure seus serviços</strong>
                                    <p>Defina se oferece entrega e/ou retirada no local. Você pode desabilitar qualquer serviço que não oferece.</p>
                                </div>
                            </div>
                        </div>

                        <div className="entrega-config-section">
                            <div className="config-option-entrega">
                                <div className="option-content-entrega">
                                    <div className={`option-icon-entrega ${entregaDisabled ? 'disabled' : ''}`}>
                                        <FaTruck />
                                    </div>
                                    <div className="option-text-entrega">
                                        <h4>Serviço de Entrega</h4>
                                        <p>
                                            {!entregaDisabled 
                                                ? 'Entrega habilitada - clientes podem solicitar entrega' 
                                                : 'Entrega desabilitada - clientes não poderão solicitar entrega'
                                            }
                                        </p>
                                    </div>
                                </div>
                                <div className="toggle-switch-entrega">
                                    <label className="switch-entrega">
                                        <input
                                            type="checkbox"
                                            checked={!entregaDisabled}
                                            onChange={(e) => setEntregaDisabled(!e.target.checked)}
                                            disabled={!isAuthenticated}
                                        />
                                        <span className="slider-entrega"></span>
                                    </label>
                                </div>
                            </div>

                            <div className="config-option-entrega">
                                <div className="option-content-entrega">
                                    <div className={`option-icon-entrega ${retiradaDisabled ? 'disabled' : ''}`}>
                                        <MdStorefront />
                                    </div>
                                    <div className="option-text-entrega">
                                        <h4>Retirada no Local</h4>
                                        <p>
                                            {!retiradaDisabled 
                                                ? 'Retirada habilitada - clientes podem retirar no local' 
                                                : 'Retirada desabilitada - clientes não poderão retirar no local'
                                            }
                                        </p>
                                    </div>
                                </div>
                                <div className="toggle-switch-entrega">
                                    <label className="switch-entrega">
                                        <input
                                            type="checkbox"
                                            checked={!retiradaDisabled}
                                            onChange={(e) => setRetiradaDisabled(!e.target.checked)}
                                            disabled={!isAuthenticated}
                                        />
                                        <span className="slider-entrega"></span>
                                    </label>
                                </div>
                            </div>

                            <div className="info-box-entrega">
                                <div className="info-content-box">
                                    <AiOutlineInfoCircle className="info-icon-box" />
                                    <div>
                                        <strong>Informação Importante</strong>
                                        <p>
                                            Ao desabilitar um serviço, os clientes não conseguirão mais selecioná-lo durante o pedido. 
                                            Certifique-se de que pelo menos um tipo de serviço esteja habilitado.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="step-navigation">
                            <button 
                                type="button"
                                onClick={handlePreviousStep}
                                className="btn-back"
                            >
                                Voltar
                            </button>
                            <button 
                                onClick={handleSaveConfiguracoesEntrega}
                                disabled={loading || !isAuthenticated}
                                className={`btn-submit ${loading ? 'loading-configuracao-inicial' : ''}`}
                            >
                                {loading ? 'Salvando...' : 'Salvar e Continuar'}
                            </button>
                        </div>
                    </div>
                );
            case 8:
                return (
                    <div className="modern-step-content">
                        <div className="step-icon">
                            <FaWhatsapp />
                        </div>
                        <h3>📱 Sincronizar WhatsApp</h3>
                        <p>Conecte o WhatsApp para receber e responder mensagens dos seus clientes diretamente na plataforma.</p>
                        
                        {/* Banner informativo */}
                        <div className="whatsapp-info-banner">
                            <div className="info-content">
                                <span className="info-icon">💡</span>
                                <div>
                                    <strong>Configure o WhatsApp Business</strong>
                                    <p>Com a integração, você pode gerenciar todas as conversas em um só lugar e automatizar respostas.</p>
                                </div>
                            </div>
                        </div>

                        {whatsappConnected || isWhatsappLoged ? (
                            <div className="whatsapp-connected-section">
                                <div className="success-message">
                                    <FaCheckCircle className="success-icon" />
                                    <h4>WhatsApp Conectado com Sucesso!</h4>
                                    <p>Seu WhatsApp está conectado e pronto para receber mensagens dos clientes.</p>
                                </div>
                                
                                <div className="whatsapp-features">
                                    <h5>O que você pode fazer agora:</h5>
                                    <ul>
                                        <li>✅ Receber mensagens dos clientes automaticamente</li>
                                        <li>✅ Responder mensagens direto da plataforma</li>
                                        <li>✅ Configurar respostas automáticas</li>
                                        <li>✅ Acompanhar histórico de conversas</li>
                                        <li>✅ Gerenciar múltiplos atendentes</li>
                                    </ul>
                                </div>
                            </div>
                        ) : (
                            <div className="whatsapp-qr-section">
                                <div className="qr-instructions">
                                    <h4>Para conectar o WhatsApp, siga os passos:</h4>
                                    <ol>
                                        <li>Abra o WhatsApp no seu celular</li>
                                        <li>Toque em <strong>Mais opções</strong> no Android ou em <strong>Configurações</strong> no iPhone</li>
                                        <li>Toque em <strong>Dispositivos conectados</strong> e depois em <strong>Conectar dispositivo</strong></li>
                                        <li>Aponte seu celular para esta tela e escaneie o QR Code</li>
                                    </ol>
                                </div>
                                
                                <div className="qr-code-container">
                                    <div className="qr-code-wrapper">
                                        {generatingQrCode ? (
                                            <div className="qr-loading">
                                                <Loading type={"spinningBubbles"} className="qr-loading-spinner" />
                                                <p>Gerando QR Code...</p>
                                            </div>
                                        ) : qrCodeImg ? (
                                            <div style={{ textAlign: 'center' }}>
                                                <img
                                                    src={qrCodeImg}
                                                    alt="QR Code WhatsApp"
                                                    className="qr-code-image"
                                                    onLoad={() => console.log('QR Code WhatsApp carregado')}
                                                    onError={(e) => {
                                                        console.error('Erro ao carregar QR Code WhatsApp:', e);
                                                        console.log('URL da imagem:', qrCodeImg);
                                                    }}
                                                />
                                                {/* Botão para regenerar QR Code manualmente */}
                                                <div style={{ marginTop: '15px' }}>
                                                    <button
                                                        onClick={generateQrCodeManually}
                                                        disabled={generatingQrCode}
                                                        style={{
                                                            backgroundColor: generatingQrCode ? '#95a5a6' : '#25D366',
                                                            color: 'white',
                                                            border: 'none',
                                                            borderRadius: '8px',
                                                            padding: '10px 20px',
                                                            cursor: generatingQrCode ? 'not-allowed' : 'pointer',
                                                            fontSize: '14px',
                                                            fontWeight: '500',
                                                            transition: 'all 0.3s ease',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            gap: '8px',
                                                            margin: '0 auto'
                                                        }}
                                                    >
                                                        {generatingQrCode ? (
                                                            <>
                                                                <span>⏳</span>
                                                                <span>Gerando...</span>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <span>🔄</span>
                                                                <span>Gerar novo QR Code</span>
                                                            </>
                                                        )}
                                                    </button>
                                                </div>
                                            </div>
                                        ) : userRequestedQrCode ? (
                                            // 🔧 NOVO: Estado quando usuário solicitou QR Code mas ainda está carregando
                                            <div className="qr-placeholder">
                                                <div>⏳</div>
                                                <div>Gerando QR Code...</div>
                                            </div>
                                        ) : (
                                            // 🔧 NOVO: Estado inicial - Botão para gerar QR Code manualmente
                                            <div style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                flexDirection: 'column',
                                                gap: '20px',
                                                padding: '40px 20px',
                                                textAlign: 'center'
                                            }}>
                                                <div style={{
                                                    fontSize: '48px',
                                                    color: '#25D366',
                                                    marginBottom: '10px'
                                                }}>
                                                    📱
                                                </div>
                                                <div style={{ fontSize: '18px', fontWeight: '500', color: '#333' }}>
                                                    Pronto para conectar o WhatsApp?
                                                </div>
                                                <div style={{ 
                                                    fontSize: '14px', 
                                                    color: '#666',
                                                    lineHeight: '1.5',
                                                    marginBottom: '10px'
                                                }}>
                                                    Clique no botão abaixo para gerar seu QR Code e conectar o WhatsApp
                                                </div>
                                                <button
                                                    onClick={generateQrCodeManually}
                                                    style={{
                                                        backgroundColor: '#25D366',
                                                        color: 'white',
                                                        border: 'none',
                                                        borderRadius: '12px',
                                                        padding: '16px 32px',
                                                        cursor: 'pointer',
                                                        fontSize: '16px',
                                                        fontWeight: '600',
                                                        transition: 'all 0.3s ease',
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        gap: '10px',
                                                        boxShadow: '0 4px 12px rgba(37, 211, 102, 0.3)',
                                                        minWidth: '200px'
                                                    }}
                                                    onMouseOver={(e) => {
                                                        e.target.style.backgroundColor = '#128C7E';
                                                        e.target.style.transform = 'translateY(-2px)';
                                                    }}
                                                    onMouseOut={(e) => {
                                                        e.target.style.backgroundColor = '#25D366';
                                                        e.target.style.transform = 'translateY(0)';
                                                    }}
                                                >
                                                    <span style={{ fontSize: '20px' }}>📱</span>
                                                    <span>Gerar QR Code</span>
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                
                                <div className="qr-help">
                                    <p><strong>Dica:</strong> Certifique-se de que seu celular está conectado à internet e que o WhatsApp está atualizado.</p>
                                </div>
                            </div>
                        )}

                        <div className="step-navigation">
                            <button 
                                type="button"
                                onClick={handlePreviousStep}
                                className="btn-back"
                            >
                                Voltar
                            </button>
                            <button 
                                type="button"
                                onClick={handleSkipWhatsApp}
                                disabled={loading}
                                className={`btn-skip ${loading ? 'loading-configuracao-inicial' : ''}`}
                            >
                                {loading ? 'Pulando...' : 'Pular Etapa'}
                            </button>
                            {(whatsappConnected || isWhatsappLoged) && (
                                <button 
                                    onClick={handleSaveWhatsAppConnection}
                                    disabled={loading || !isAuthenticated}
                                    className={`btn-submit ${loading ? 'loading-configuracao-inicial' : ''}`}
                                >
                                    {loading ? 'Salvando...' : 'Continuar'}
                                </button>
                            )}
                        </div>
                    </div>
                );
            case 9:
                return (
                    <div className="modern-step-content success-step">
                        <div className="success-animation">
                            <div className="success-circle">
                                <AiOutlineCheck className="success-check" />
                            </div>
                        </div>
                        <h3>🎉 Configuração Concluída!</h3>
                        <p>Parabéns! Todas as configurações foram salvas com sucesso.</p>
                        
                        <div className="confirmation-grid">
                            <div className="confirmation-card">
                                <AiOutlineUser className="card-icon" />
                                <h4>Informações Pessoais</h4>
                                <p>✅ Concluído</p>
                            </div>
                            <div className="confirmation-card">
                                <AiOutlineHome className="card-icon" />
                                <h4>Endereço</h4>
                                <p>✅ Configurado</p>
                            </div>
                            <div className="confirmation-card">
                                <AiOutlineCheck className="card-icon" />
                                <h4>Localização no Mapa</h4>
                                <p>✅ Posicionado</p>
                            </div>
                            <div className="confirmation-card">
                                <MdDeliveryDining className="card-icon" />
                                <h4>Área de Entrega</h4>
                                <p>✅ {
                                    typeOfRegion === 'skip' ? 'Pulada - Apenas retirada' :
                                    typeOfRegion === 'raio' ? `${arrayRaioEntrega.length} raios configurado(s)` : 
                                    typeOfRegion === 'bairro' ? `${arrayBairroEntrega.length} bairros configurado(s)` :
                                    'Configurado'
                                }</p>
                            </div>
                            <div className="confirmation-card">
                                <MdSchedule className="card-icon" />
                                <h4>Horários de Funcionamento</h4>
                                <p>✅ {
                                    availability === 'sempre' ? 'Sempre disponível' :
                                    availability === 'especifico' ? `${activeDays.size} dias configurados` :
                                    'Configurado'
                                }</p>
                            </div>
                            <div className="confirmation-card">
                                <MdRestaurantMenu className="card-icon" />
                                <h4>Cardápio</h4>
                                <p>✅ {
                                    cardapioOption === 'importar' ? 'Importado' :
                                    cardapioOption === 'deixar' ? 'Enviado para equipe' :
                                    cardapioOption === 'criar' ? 'Configurado para criação' :
                                    'Pulado'
                                }</p>
                            </div>
                            <div className="confirmation-card">
                                <FaTruck className="card-icon" />
                                <h4>Configurações de Entrega</h4>
                                <p>✅ {
                                    entregaDisabled && retiradaDisabled ? 'Ambos desabilitados' :
                                    entregaDisabled ? 'Apenas retirada' :
                                    retiradaDisabled ? 'Apenas entrega' :
                                    'Entrega e retirada habilitadas'
                                }</p>
                            </div>
                            <div className="confirmation-card">
                                <FaWhatsapp className="card-icon" />
                                <h4>WhatsApp</h4>
                                <p>✅ {
                                    whatsappConnected || isWhatsappLoged ? 'Conectado' : 'Pulado'
                                }</p>
                            </div>
                        </div>
                        
                        <div style={{ marginTop: '30px', textAlign: 'center' }}>
                            <p style={{ fontSize: '1.1rem', marginBottom: '20px' }}>
                                Agora você pode começar a usar o PedeJA para gerenciar seu negócio!
                            </p>
                        </div>
                        
                        <div className="step-navigation">
                            <button 
                                type="button"
                                onClick={handlePreviousStep}
                                className="btn-back"
                            >
                                Voltar
                            </button>
                            <button 
                                onClick={handleFinish}
                                disabled={loading}
                                className={`btn-finish ${loading ? 'loading-configuracao-inicial' : ''}`}
                            >
                                {loading ? 'Finalizando...' : 'Finalizar e Acessar Sistema'}
                            </button>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <div className="configuracao-inicial-modern">
            <div className="header-modern">
                <img src={logoPedeJA} alt="PEDEEJA" className="logo" />
                <h1>Configuração Inicial</h1>
                <p>Complete as etapas abaixo para finalizar seu cadastro</p>
            </div>

            <RegistrationStepper
                steps={steps}
                currentStep={currentStep}
                completedSteps={completedSteps}
                onStepChange={handleStepChange}
                onComplete={handleFinish}
            >
                {renderStepContent()}
            </RegistrationStepper>
        </div>
    );
};

export default ConfiguracaoInicial; 