{"version": 3, "file": "ie11Sha256.js", "sourceRoot": "", "sources": ["../src/ie11Sha256.ts"], "names": [], "mappings": ";;;AAAA,6CAA4C;AAC5C,yCAAgD;AAEhD,gEAAsD;AAEtD,kEAA2D;AAE3D;IAIE,gBAAY,MAAmB;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED,uBAAM,GAAN,UAAO,MAAkB;QAAzB,iBAgBC;QAfC,IAAI,IAAA,yBAAW,EAAC,MAAM,CAAC,EAAE;YACvB,OAAO;SACR;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,SAAS;YAC5C,SAAS,CAAC,OAAO,GAAG;gBAClB,KAAI,CAAC,SAAS,GAAG,OAAO,CAAC,MAAM,CAC7B,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAC7C,CAAC;YACJ,CAAC,CAAC;YACF,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;YAE7C,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAO,CAAC,CAAC,CAAC;IACjC,CAAC;IAED,uBAAM,GAAN;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CACxB,UAAA,SAAS;YACP,OAAA,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;gBAC1B,SAAS,CAAC,OAAO,GAAG;oBAClB,MAAM,CAAC,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;gBACzD,CAAC,CAAC;gBACF,SAAS,CAAC,UAAU,GAAG;oBACrB,IAAI,SAAS,CAAC,MAAM,EAAE;wBACpB,OAAO,CAAC,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;qBAC3C;oBACD,MAAM,CAAC,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;gBACzD,CAAC,CAAC;gBAEF,SAAS,CAAC,MAAM,EAAE,CAAC;YACrB,CAAC,CAAC;QAZF,CAYE,CACL,CAAC;IACJ,CAAC;IAED,sBAAK,GAAL;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAA,OAAO;gBACpD,OAAC,IAAA,iCAAY,GAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAC7C,6BAAiB,EACjB,OAAO,CACV;YAHD,CAGC,CACJ,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAO,CAAC,CAAC,CAAC;SAChC;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,OAAO,CAC3B,IAAA,iCAAY,GAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CACjE,CAAC;SACH;IACH,CAAC;IACH,aAAC;AAAD,CAAC,AA7DD,IA6DC;AA7DY,wBAAM;AA+DnB,SAAS,aAAa,CAAC,MAAkB;IACvC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;QACjC,IAAM,YAAY,GAAI,IAAA,iCAAY,GAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CACzE,KAAK,EACL,iBAAiB,CAAC,MAAM,CAAC,EACzB,6BAAiB,EACjB,KAAK,EACL,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,YAAY,CAAC,UAAU,GAAG;YACxB,IAAI,YAAY,CAAC,MAAM,EAAE;gBACvB,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aAC9B;YAED,MAAM,CAAC,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC;QACF,YAAY,CAAC,OAAO,GAAG;YACrB,MAAM,CAAC,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAgB;IACzC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAO,IAAA,4BAAQ,EAAC,IAAI,CAAC,CAAC;KACvB;IAED,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO,IAAI,UAAU,CACnB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,iBAAiB,CAC/C,CAAC;KACH;IAED,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC"}