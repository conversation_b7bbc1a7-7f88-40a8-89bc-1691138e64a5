{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\roboConfig\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport './style.css';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport LeftMenu from \"../../components/LeftMenu\";\nimport { Modal } from \"../../components/Modal\";\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\nimport styled from 'styled-components';\nimport { FaEdit, FaTrash, FaPlus, FaSearch } from \"react-icons/fa\";\nimport { IoMdClose } from \"react-icons/io\";\nimport CryptoJS from 'crypto-js';\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RoboConfigContainer = styled.div`\n    display: flex;\n    margin-left: ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'};\n    height: auto;\n    width: auto;\n    transition: 150ms;\n    background-color: rgb(247,247,247) !important;\n    overflow: initial;\n    z-index: 9;\n\n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\nconst RoboCfg = () => {\n  _s();\n  var _selectedResponse2, _selectedResponse3, _selectedResponse4, _selectedResponse5;\n  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\n  const userEncrypted = localStorage.getItem('user');\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  const userParse = JSON.parse(user);\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const empresaObjId = empresaParse._id;\n  const {\n    sidebar\n  } = useContext(SidebarContext);\n  const navigate = useNavigate();\n  const [customResponses, setCustomResponses] = useState([]);\n  const [filteredResponses, setFilteredResponses] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isAddModalOpen, setIsAddModalOpen] = useState(false);\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false);\n  const [editingIndex, setEditingIndex] = useState(null);\n  const [editingData, setEditingData] = useState({\n    question: '',\n    response: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(5);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [deleteIndex, setDeleteIndex] = useState(null);\n\n  // Carregar respostas personalizadas\n  useEffect(() => {\n    fetchCustomResponses();\n  }, []);\n\n  // Filtrar respostas baseado na busca\n  useEffect(() => {\n    if (searchTerm) {\n      const filtered = customResponses.filter(response => response.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredResponses(filtered);\n    } else {\n      setFilteredResponses(customResponses);\n    }\n  }, [searchTerm, customResponses]);\n  const fetchCustomResponses = async () => {\n    try {\n      setLoading(true);\n      const response = await getCustomResponses(empresaObjId);\n      setCustomResponses(response.data.customResponses || []);\n    } catch (error) {\n      console.error(\"Erro ao buscar respostas personalizadas:\", error);\n      toast.error(\"Erro ao carregar respostas personalizadas\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const debouncedUpdateQuestionActive = debounce(async (empresaObjId, questionIdentifier, isActive, setCompanyResponses, companyResponses) => {\n    try {\n      // Atualiza no backend\n      await updateQuestionActive(empresaObjId, questionIdentifier, isActive);\n\n      // Atualiza localmente após confirmação do backend\n      const updatedResponses = companyResponses.map(response => response.questionIdentifier === questionIdentifier ? {\n        ...response,\n        active: isActive\n      } : response);\n      setCompanyResponses(updatedResponses);\n      console.log(\"Disponibilidade atualizada com sucesso.\");\n    } catch (error) {\n      console.error(\"Erro ao atualizar disponibilidade:\", error);\n    }\n  }, 300); // Aguarda 300ms após o último evento\n\n  const handleBack = () => {\n    //setSidebar(!sidebar)\n    navigate(\"/\");\n  };\n  const handleSelectQuestionAndAnswer = message => {\n    setSelectedResponse(message);\n  };\n\n  // Função para atualizar o response do selectedResponse diretamente\n  const handleResponseChange = e => {\n    setSelectedResponse(prevSelectedResponse => ({\n      ...prevSelectedResponse,\n      response: e.target.value\n    }));\n  };\n\n  // Função para salvar as alterações no companyResponses\n  const handleSaveResponse = async () => {\n    if (selectedResponse) {\n      // Atualiza a resposta localmente\n      const updatedResponses = companyResponses.map(response => response.questionIdentifier === selectedResponse.questionIdentifier ? {\n        ...response,\n        response: selectedResponse.response\n      } // Atualiza apenas a mensagem de resposta\n      : response);\n      setCompanyResponses(updatedResponses);\n      try {\n        // Chama a API para atualizar o backend\n        await updateQuestionResponses(empresaObjId, updatedResponses);\n        toast(\"Respostas atualizadas com sucesso!\", {\n          autoClose: 2000,\n          type: \"success\"\n        });\n      } catch (error) {\n        console.error(\"Erro ao atualizar respostas:\", error);\n        toast(\"Ocorreu um erro ao salvar as respostas.\", {\n          autoClose: 2000,\n          type: \"error\"\n        });\n      }\n    }\n  };\n\n  // Função genérica para inserir texto no cursor\n  const insertAtCursor = (textarea, text) => {\n    if (textarea) {\n      const startPos = textarea.selectionStart;\n      const endPos = textarea.selectionEnd;\n      const value = textarea.value;\n\n      // Atualiza o texto no campo com o novo valor\n      textarea.value = value.substring(0, startPos) + text + value.substring(endPos);\n\n      // Mantém o cursor após o texto inserido\n      textarea.selectionStart = textarea.selectionEnd = startPos + text.length;\n    }\n  };\n\n  // Funções específicas para cada botão\n  const handleInsertNomeCliente = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"{nome_cliente}\");\n  };\n  const handleInsertLinkCardapio = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"{link}\");\n  };\n  const handleInsertSaudacao = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"Agradecemos a preferência\");\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: /*#__PURE__*/_jsxDEV(Teste, {\n        sidebar: sidebar,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-100 p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-header\",\n              style: {\n                marginBottom: \"0px\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"title\",\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  children: \"Configura\\xE7\\xF5es do Rob\\xF4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                justifyContent: \"right\" /*, height:\"80px\"*/\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contentItemComplete flex-column flex-md-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group inputGroup-etapasItem\",\n                style: {\n                  height: 50\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: tela === \"tela1\" ? \"etapasAddItem etapaAtiva\" : \"etapasAddItem\",\n                  style: {\n                    borderBottom: '1px solid lightgray'\n                  },\n                  onClick: () => setTela(\"tela1\"),\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"1. Personalizar Mensagens\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mh-100\",\n                style: {\n                  maxWidth: \"80%\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"formGroupRow\",\n                  children: tela === \"tela1\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"roboCfg-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"roboCfg-sidebar\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        children: \"Mensagens\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 231,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"roboCfg-message-list\",\n                        children: companyResponses.map((message, index) => {\n                          var _selectedResponse;\n                          return /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: `roboCfg-message-item ${((_selectedResponse = selectedResponse) === null || _selectedResponse === void 0 ? void 0 : _selectedResponse.questionIdentifier) === message.questionIdentifier ? 'roboCfg-selected' : ''}`,\n                            onClick: () => handleSelectQuestionAndAnswer(message),\n                            children: [message.questionType, /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"checkBoxContentMsg\",\n                              children: [message.active ? /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"roboCfg-edit-button-ativo\",\n                                children: \"Ativo\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 242,\n                                columnNumber: 73\n                              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"roboCfg-edit-button-inativo\",\n                                children: \"Inativo\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 244,\n                                columnNumber: 73\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"wrapper\",\n                                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"switch_box box_1\",\n                                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                                    type: \"checkbox\",\n                                    className: \"switch_1\",\n                                    checked: message.active || false\n                                    // Dentro do checkbox\n                                    ,\n                                    onChange: e => {\n                                      const isActive = e.target.checked;\n\n                                      // Chamar a função debounce para evitar múltiplas chamadas ao backend\n                                      debouncedUpdateQuestionActive(empresaObjId, message.questionIdentifier, isActive, setCompanyResponses, companyResponses);\n                                    }\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 248,\n                                    columnNumber: 77\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 247,\n                                  columnNumber: 73\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 246,\n                                columnNumber: 69\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 240,\n                              columnNumber: 65\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 234,\n                            columnNumber: 61\n                          }, this);\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 232,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"roboCfg-main\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"roboCfg-preview\",\n                        style: {\n                          backgroundImage: `url(${backgroundWhatsApp})`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-chat\",\n                          children: [((_selectedResponse2 = selectedResponse) === null || _selectedResponse2 === void 0 ? void 0 : _selectedResponse2.question) && /*#__PURE__*/_jsxDEV(Message, {\n                            fromMe: false,\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              children: (_selectedResponse3 = selectedResponse) === null || _selectedResponse3 === void 0 ? void 0 : _selectedResponse3.question\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 273,\n                              columnNumber: 69\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"12:00\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 274,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 272,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(Message, {\n                            fromMe: true,\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              children: (_selectedResponse4 = selectedResponse) === null || _selectedResponse4 === void 0 ? void 0 : _selectedResponse4.response\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 280,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"12:01\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 281,\n                              columnNumber: 65\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 279,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 269,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 268,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"roboCfg-editor\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Personalize a mensagem\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 286,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                          className: \"roboCfg-textarea\",\n                          value: ((_selectedResponse5 = selectedResponse) === null || _selectedResponse5 === void 0 ? void 0 : _selectedResponse5.response) || \"\",\n                          onChange: handleResponseChange // Altera diretamente o response do selectedResponse\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 287,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-buttons\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-button\",\n                            type: \"button\",\n                            onClick: handleInsertNomeCliente // Chama a função para Nome do cliente\n                            ,\n                            children: \"Nome do cliente\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 293,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-button\",\n                            type: \"button\",\n                            onClick: handleInsertLinkCardapio // Chama a função para Link do cardápio\n                            ,\n                            children: \"Link do card\\xE1pio\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 300,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 292,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-save-cancel\",\n                          children: /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-save-button\",\n                            type: \"button\",\n                            onClick: handleSaveResponse,\n                            children: \"Salvar\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 317,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 316,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 285,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(RoboCfg, \"waM1wUoBa4U7D0dOyCO753JRUrA=\", false, function () {\n  return [useNavigate];\n});\n_c = RoboCfg;\nexport default RoboCfg;\nvar _c;\n$RefreshReg$(_c, \"RoboCfg\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "SidebarContext", "PermissionGate", "LeftMenu", "Modal", "ModalAddCustomResponse", "ModalEditCustomResponse", "styled", "FaEdit", "FaTrash", "FaPlus", "FaSearch", "IoMdClose", "CryptoJS", "getCustomResponses", "addCustomResponse", "updateCustomResponse", "deleteCustomResponse", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RoboConfigContainer", "div", "sidebar", "RoboCfg", "_s", "_selectedResponse2", "_selectedResponse3", "_selectedResponse4", "_selectedResponse5", "secret<PERSON>ey", "userEncrypted", "localStorage", "getItem", "user", "AES", "decrypt", "toString", "enc", "Utf8", "userParse", "JSON", "parse", "empresa", "empresaParse", "empresaObjId", "_id", "navigate", "customResponses", "setCustomResponses", "filteredResponses", "setFilteredResponses", "searchTerm", "setSearchTerm", "isAddModalOpen", "setIsAddModalOpen", "isEditModalOpen", "setIsEditModalOpen", "editingIndex", "setEditingIndex", "editingData", "setEditingData", "question", "response", "loading", "setLoading", "currentPage", "setCurrentPage", "itemsPerPage", "showDeleteConfirm", "setShowDeleteConfirm", "deleteIndex", "setDeleteIndex", "fetchCustomResponses", "filtered", "filter", "toLowerCase", "includes", "data", "error", "console", "debouncedUpdateQuestionActive", "debounce", "questionIdentifier", "isActive", "setCompanyResponses", "companyResponses", "updateQuestionActive", "updatedResponses", "map", "active", "log", "handleBack", "handleSelectQuestionAndAnswer", "message", "setSelectedResponse", "handleResponseChange", "e", "prevSelectedResponse", "target", "value", "handleSaveResponse", "selectedResponse", "updateQuestionResponses", "autoClose", "type", "insertAtCursor", "textarea", "text", "startPos", "selectionStart", "endPos", "selectionEnd", "substring", "length", "handleInsertNomeCliente", "document", "querySelector", "handleInsertLinkCardapio", "handleInsertSaudacao", "children", "permissions", "<PERSON>e", "className", "style", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "height", "tela", "borderBottom", "onClick", "<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "index", "_selectedResponse", "questionType", "checked", "onChange", "backgroundImage", "backgroundWhatsApp", "Message", "fromMe", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/roboConfig/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport './style.css';\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\nimport { Modal } from \"../../components/Modal\";\r\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\r\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\r\nimport styled from 'styled-components';\r\nimport { FaEdit, FaTrash, FaPlus, FaSearch } from \"react-icons/fa\";\r\nimport { IoMdClose } from \"react-icons/io\";\r\nimport CryptoJS from 'crypto-js';\r\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst RoboConfigContainer = styled.div`\r\n    display: flex;\r\n    margin-left: ${({ sidebar }) => (sidebar ? '250px' : '100px')};\r\n    height: auto;\r\n    width: auto;\r\n    transition: 150ms;\r\n    background-color: rgb(247,247,247) !important;\r\n    overflow: initial;\r\n    z-index: 9;\r\n\r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\nconst RoboCfg = () => {\r\n    const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\r\n    const userEncrypted = localStorage.getItem('user');\r\n    const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\r\n    const userParse = JSON.parse(user);\r\n    const empresa = localStorage.getItem('empresa');\r\n    const empresaParse = JSON.parse(empresa);\r\n    const empresaObjId = empresaParse._id;\r\n\r\n    const { sidebar } = useContext(SidebarContext);\r\n    const navigate = useNavigate();\r\n\r\n    const [customResponses, setCustomResponses] = useState([]);\r\n    const [filteredResponses, setFilteredResponses] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [isAddModalOpen, setIsAddModalOpen] = useState(false);\r\n    const [isEditModalOpen, setIsEditModalOpen] = useState(false);\r\n    const [editingIndex, setEditingIndex] = useState(null);\r\n    const [editingData, setEditingData] = useState({ question: '', response: '' });\r\n    const [loading, setLoading] = useState(false);\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [itemsPerPage] = useState(5);\r\n    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\r\n    const [deleteIndex, setDeleteIndex] = useState(null);\r\n\r\n    // Carregar respostas personalizadas\r\n    useEffect(() => {\r\n        fetchCustomResponses();\r\n    }, []);\r\n\r\n    // Filtrar respostas baseado na busca\r\n    useEffect(() => {\r\n        if (searchTerm) {\r\n            const filtered = customResponses.filter(response =>\r\n                response.toLowerCase().includes(searchTerm.toLowerCase())\r\n            );\r\n            setFilteredResponses(filtered);\r\n        } else {\r\n            setFilteredResponses(customResponses);\r\n        }\r\n    }, [searchTerm, customResponses]);\r\n\r\n    const fetchCustomResponses = async () => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await getCustomResponses(empresaObjId);\r\n            setCustomResponses(response.data.customResponses || []);\r\n        } catch (error) {\r\n            console.error(\"Erro ao buscar respostas personalizadas:\", error);\r\n            toast.error(\"Erro ao carregar respostas personalizadas\");\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const debouncedUpdateQuestionActive = debounce(async (empresaObjId, questionIdentifier, isActive, setCompanyResponses, companyResponses) => {\r\n        try {\r\n            // Atualiza no backend\r\n            await updateQuestionActive(empresaObjId, questionIdentifier, isActive);\r\n    \r\n            // Atualiza localmente após confirmação do backend\r\n            const updatedResponses = companyResponses.map((response) =>\r\n                response.questionIdentifier === questionIdentifier\r\n                    ? { ...response, active: isActive }\r\n                    : response\r\n            );\r\n            setCompanyResponses(updatedResponses);\r\n            console.log(\"Disponibilidade atualizada com sucesso.\");\r\n        } catch (error) {\r\n            console.error(\"Erro ao atualizar disponibilidade:\", error);\r\n        }\r\n    }, 300); // Aguarda 300ms após o último evento\r\n\r\n\r\n    const handleBack = () => {\r\n        //setSidebar(!sidebar)\r\n        navigate(\"/\");\r\n    }\r\n\r\n    const handleSelectQuestionAndAnswer = (message) => {\r\n        setSelectedResponse(message);\r\n    };\r\n\r\n    // Função para atualizar o response do selectedResponse diretamente\r\n    const handleResponseChange = (e) => {\r\n        setSelectedResponse((prevSelectedResponse) => ({\r\n            ...prevSelectedResponse,\r\n            response: e.target.value,\r\n        }));\r\n    };\r\n\r\n    // Função para salvar as alterações no companyResponses\r\n    const handleSaveResponse = async () => {\r\n        if (selectedResponse) {\r\n            // Atualiza a resposta localmente\r\n            const updatedResponses = companyResponses.map((response) =>\r\n                response.questionIdentifier === selectedResponse.questionIdentifier\r\n                    ? { ...response, response: selectedResponse.response } // Atualiza apenas a mensagem de resposta\r\n                    : response\r\n            );\r\n            setCompanyResponses(updatedResponses);\r\n\r\n            try {\r\n                // Chama a API para atualizar o backend\r\n                await updateQuestionResponses(empresaObjId, updatedResponses);\r\n                toast(\"Respostas atualizadas com sucesso!\", { autoClose: 2000, type: \"success\" });\r\n            } catch (error) {\r\n                console.error(\"Erro ao atualizar respostas:\", error);\r\n                toast(\"Ocorreu um erro ao salvar as respostas.\", { autoClose: 2000, type: \"error\" });\r\n            }\r\n        }\r\n    };\r\n\r\n\r\n    // Função genérica para inserir texto no cursor\r\n    const insertAtCursor = (textarea, text) => {\r\n        if (textarea) {\r\n            const startPos = textarea.selectionStart;\r\n            const endPos = textarea.selectionEnd;\r\n            const value = textarea.value;\r\n\r\n            // Atualiza o texto no campo com o novo valor\r\n            textarea.value = value.substring(0, startPos) + text + value.substring(endPos);\r\n\r\n            // Mantém o cursor após o texto inserido\r\n            textarea.selectionStart = textarea.selectionEnd = startPos + text.length;\r\n        }\r\n    };\r\n\r\n    // Funções específicas para cada botão\r\n    const handleInsertNomeCliente = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"{nome_cliente}\");\r\n    };\r\n\r\n    const handleInsertLinkCardapio = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"{link}\");\r\n    };\r\n\r\n    const handleInsertSaudacao = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"Agradecemos a preferência\");\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <PermissionGate permissions={['default']}>\r\n\r\n                <Teste sidebar={sidebar}>\r\n                    <div className=\"w-100 p-4\">\r\n                        {/*<form onSubmit={handleSubmitButton}>*/}\r\n                        <form /*onSubmit={formik.handleSubmit}*/ >\r\n\r\n                            <div className=\"form-header\" style={{ marginBottom: \"0px\" }}>\r\n                                <div className=\"title\">\r\n                                    <h1>Configurações do Robô</h1>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div style={{ display: \"flex\", justifyContent: \"right\"/*, height:\"80px\"*/ }}>\r\n\r\n                                {/*<div className=\"div-buttons\">\r\n                                    <div className=\"continue-button\">\r\n                                        {tela === \"tela1\" ?\r\n                                            <button type=\"button\" onClick={saveChanges} disabled={isSubmitting}>\r\n                                                <AiIcons.AiOutlineSave style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Salvar</a>\r\n                                            </button>\r\n                                            :\r\n                                            <button type=\"button\" disabled={isSubmitting}>\r\n                                                <AiIcons.AiOutlineSave style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Salvar</a>\r\n                                            </button>\r\n                                        }\r\n                                    </div>\r\n\r\n                                    <div className=\"back-button\">\r\n                                        <button onClick={handleBack}>\r\n                                            <SlIcons.SlActionUndo style={{ color: \"#ff4c4c\", marginRight: \"5px\", fontSize: \"18px\", marginBottom: \"2px\" }} /><a >Voltar</a>\r\n                                        </button>\r\n                                    </div>\r\n                                </div>*/}\r\n\r\n                            </div>\r\n\r\n                            <div className=\"contentItemComplete flex-column flex-md-row\">\r\n                                <div className=\"input-group inputGroup-etapasItem\" style={{ height: 50 }}>\r\n                                    <div className={tela === \"tela1\" ? \"etapasAddItem etapaAtiva\" : \"etapasAddItem\"}\r\n                                        style={{ borderBottom: '1px solid lightgray' }} onClick={() => setTela(\"tela1\")}\r\n                                    >\r\n                                        <label>1. Personalizar Mensagens</label>\r\n                                    </div>                                   \r\n                                </div>\r\n\r\n                                <div className=\"input-group mh-100\" style={{ maxWidth: \"80%\" }}>\r\n\r\n                                    <div className=\"formGroupRow\">\r\n                                        {tela === \"tela1\" &&\r\n                                            <div className=\"roboCfg-container\">\r\n                                                <div className=\"roboCfg-sidebar\">\r\n                                                    <h3>Mensagens</h3>\r\n                                                    <ul className=\"roboCfg-message-list\">\r\n                                                        {companyResponses.map((message, index) => (\r\n                                                            <li\r\n                                                                key={index}\r\n                                                                className={`roboCfg-message-item ${selectedResponse?.questionIdentifier === message.questionIdentifier ? 'roboCfg-selected' : ''}`}\r\n                                                                onClick={() => handleSelectQuestionAndAnswer(message)}\r\n                                                            >\r\n                                                                {message.questionType}\r\n                                                                <div className=\"checkBoxContentMsg\">\r\n                                                                    {message.active ? \r\n                                                                        <div className=\"roboCfg-edit-button-ativo\">Ativo</div> \r\n                                                                        : \r\n                                                                        <div className=\"roboCfg-edit-button-inativo\">Inativo</div>\r\n                                                                    }\r\n                                                                    <div className=\"wrapper\">\r\n                                                                        <div className=\"switch_box box_1\">\r\n                                                                            <input\r\n                                                                                type=\"checkbox\"\r\n                                                                                className=\"switch_1\"\r\n                                                                                checked={message.active || false}\r\n                                                                                // Dentro do checkbox\r\n                                                                                onChange={(e) => {\r\n                                                                                    const isActive = e.target.checked;\r\n                                                                                \r\n                                                                                    // Chamar a função debounce para evitar múltiplas chamadas ao backend\r\n                                                                                    debouncedUpdateQuestionActive(empresaObjId, message.questionIdentifier, isActive, setCompanyResponses, companyResponses);\r\n                                                                                }}\r\n                                                                            />\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </li>\r\n                                                        ))}\r\n                                                    </ul>\r\n                                                </div>\r\n                                                <div className=\"roboCfg-main\">\r\n                                                    <div className=\"roboCfg-preview\" style={{ backgroundImage: `url(${backgroundWhatsApp})` }}>\r\n                                                        <div className=\"roboCfg-chat\">\r\n\r\n                                                            {selectedResponse?.question &&\r\n                                                                <Message fromMe={false}>\r\n                                                                    <p>{selectedResponse?.question}</p>\r\n                                                                    <span>12:00</span>\r\n                                                                </Message>\r\n                                                            }\r\n\r\n\r\n                                                            <Message fromMe={true}>\r\n                                                                <p>{selectedResponse?.response}</p>\r\n                                                                <span>12:01</span>\r\n                                                            </Message>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div className=\"roboCfg-editor\">\r\n                                                        <h4>Personalize a mensagem</h4>\r\n                                                        <textarea\r\n                                                            className=\"roboCfg-textarea\"\r\n                                                            value={selectedResponse?.response || \"\"}\r\n                                                            onChange={handleResponseChange} // Altera diretamente o response do selectedResponse\r\n                                                        />\r\n                                                        <div className=\"roboCfg-buttons\">\r\n                                                            <button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertNomeCliente} // Chama a função para Nome do cliente\r\n                                                            >\r\n                                                                Nome do cliente\r\n                                                            </button>\r\n                                                            <button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertLinkCardapio} // Chama a função para Link do cardápio\r\n                                                            >\r\n                                                                Link do cardápio\r\n                                                            </button>\r\n                                                            {/*<button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertSaudacao} // Chama a função para Saudação\r\n                                                            >\r\n                                                                Saudação\r\n                                                            </button>*/}\r\n                                                        </div>\r\n\r\n                                                        <div className=\"roboCfg-save-cancel\">\r\n                                                            <button className=\"roboCfg-save-button\" type=\"button\" onClick={handleSaveResponse}>Salvar</button>\r\n                                                            {/*<button className=\"roboCfg-cancel-button\">Cancelar</button>*/}\r\n                                                        </div>\r\n\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        }\r\n                                        {/*tela === \"tela2\" && (\r\n                                            <div>NADA AQUI POR ENQUANTO</div>\r\n                                        )*/}\r\n                                    </div>\r\n\r\n                                </div>\r\n\r\n\r\n\r\n                            </div>\r\n\r\n                        </form>\r\n                    </div>\r\n                </Teste>\r\n\r\n            </PermissionGate>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default RoboCfg;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,aAAa;AACpB,SAASC,cAAc,QAAQ,iBAAiB;AAChD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,OAAOC,sBAAsB,MAAM,yCAAyC;AAC5E,OAAOC,uBAAuB,MAAM,0CAA0C;AAC9E,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAClE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,QAAQ,MAAM,WAAW;AAChC,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,oBAAoB;AACtH,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,mBAAmB,GAAGhB,MAAM,CAACiB,GAAG;AACtC;AACA,mBAAmB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;EAClB,MAAMC,SAAS,GAAG,uFAAuF;EACzG,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMC,IAAI,GAAGvB,QAAQ,CAACwB,GAAG,CAACC,OAAO,CAACL,aAAa,EAAED,SAAS,CAAC,CAACO,QAAQ,CAAC1B,QAAQ,CAAC2B,GAAG,CAACC,IAAI,CAAC;EACvF,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACR,IAAI,CAAC;EAClC,MAAMS,OAAO,GAAGX,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMW,YAAY,GAAGH,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC;EACxC,MAAME,YAAY,GAAGD,YAAY,CAACE,GAAG;EAErC,MAAM;IAAEvB;EAAQ,CAAC,GAAG3B,UAAU,CAACG,cAAc,CAAC;EAC9C,MAAMgD,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC;IAAEmE,QAAQ,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyE,YAAY,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAClC,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACAE,SAAS,CAAC,MAAM;IACZ4E,oBAAoB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5E,SAAS,CAAC,MAAM;IACZ,IAAIuD,UAAU,EAAE;MACZ,MAAMsB,QAAQ,GAAG1B,eAAe,CAAC2B,MAAM,CAACZ,QAAQ,IAC5CA,QAAQ,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,CAAC,CAAC,CAC5D,CAAC;MACDzB,oBAAoB,CAACuB,QAAQ,CAAC;IAClC,CAAC,MAAM;MACHvB,oBAAoB,CAACH,eAAe,CAAC;IACzC;EACJ,CAAC,EAAE,CAACI,UAAU,EAAEJ,eAAe,CAAC,CAAC;EAEjC,MAAMyB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACAR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMF,QAAQ,GAAG,MAAMnD,kBAAkB,CAACiC,YAAY,CAAC;MACvDI,kBAAkB,CAACc,QAAQ,CAACe,IAAI,CAAC9B,eAAe,IAAI,EAAE,CAAC;IAC3D,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE/D,KAAK,CAAC+D,KAAK,CAAC,2CAA2C,CAAC;IAC5D,CAAC,SAAS;MACNd,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMgB,6BAA6B,GAAGC,QAAQ,CAAC,OAAOrC,YAAY,EAAEsC,kBAAkB,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,gBAAgB,KAAK;IACxI,IAAI;MACA;MACA,MAAMC,oBAAoB,CAAC1C,YAAY,EAAEsC,kBAAkB,EAAEC,QAAQ,CAAC;;MAEtE;MACA,MAAMI,gBAAgB,GAAGF,gBAAgB,CAACG,GAAG,CAAE1B,QAAQ,IACnDA,QAAQ,CAACoB,kBAAkB,KAAKA,kBAAkB,GAC5C;QAAE,GAAGpB,QAAQ;QAAE2B,MAAM,EAAEN;MAAS,CAAC,GACjCrB,QACV,CAAC;MACDsB,mBAAmB,CAACG,gBAAgB,CAAC;MACrCR,OAAO,CAACW,GAAG,CAAC,yCAAyC,CAAC;IAC1D,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC9D;EACJ,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;EAGT,MAAMa,UAAU,GAAGA,CAAA,KAAM;IACrB;IACA7C,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,MAAM8C,6BAA6B,GAAIC,OAAO,IAAK;IAC/CC,mBAAmB,CAACD,OAAO,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAIC,CAAC,IAAK;IAChCF,mBAAmB,CAAEG,oBAAoB,KAAM;MAC3C,GAAGA,oBAAoB;MACvBnC,QAAQ,EAAEkC,CAAC,CAACE,MAAM,CAACC;IACvB,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIC,gBAAgB,EAAE;MAClB;MACA,MAAMd,gBAAgB,GAAGF,gBAAgB,CAACG,GAAG,CAAE1B,QAAQ,IACnDA,QAAQ,CAACoB,kBAAkB,KAAKmB,gBAAgB,CAACnB,kBAAkB,GAC7D;QAAE,GAAGpB,QAAQ;QAAEA,QAAQ,EAAEuC,gBAAgB,CAACvC;MAAS,CAAC,CAAC;MAAA,EACrDA,QACV,CAAC;MACDsB,mBAAmB,CAACG,gBAAgB,CAAC;MAErC,IAAI;QACA;QACA,MAAMe,uBAAuB,CAAC1D,YAAY,EAAE2C,gBAAgB,CAAC;QAC7DxE,KAAK,CAAC,oCAAoC,EAAE;UAAEwF,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC;MACrF,CAAC,CAAC,OAAO1B,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD/D,KAAK,CAAC,yCAAyC,EAAE;UAAEwF,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAQ,CAAC,CAAC;MACxF;IACJ;EACJ,CAAC;;EAGD;EACA,MAAMC,cAAc,GAAGA,CAACC,QAAQ,EAAEC,IAAI,KAAK;IACvC,IAAID,QAAQ,EAAE;MACV,MAAME,QAAQ,GAAGF,QAAQ,CAACG,cAAc;MACxC,MAAMC,MAAM,GAAGJ,QAAQ,CAACK,YAAY;MACpC,MAAMZ,KAAK,GAAGO,QAAQ,CAACP,KAAK;;MAE5B;MACAO,QAAQ,CAACP,KAAK,GAAGA,KAAK,CAACa,SAAS,CAAC,CAAC,EAAEJ,QAAQ,CAAC,GAAGD,IAAI,GAAGR,KAAK,CAACa,SAAS,CAACF,MAAM,CAAC;;MAE9E;MACAJ,QAAQ,CAACG,cAAc,GAAGH,QAAQ,CAACK,YAAY,GAAGH,QAAQ,GAAGD,IAAI,CAACM,MAAM;IAC5E;EACJ,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMR,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,gBAAgB,CAAC;EAC9C,CAAC;EAED,MAAMW,wBAAwB,GAAGA,CAAA,KAAM;IACnC,MAAMX,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,QAAQ,CAAC;EACtC,CAAC;EAED,MAAMY,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,MAAMZ,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,2BAA2B,CAAC;EACzD,CAAC;EAED,oBACIzF,OAAA,CAAAE,SAAA;IAAAoG,QAAA,eACItG,OAAA,CAAClB,cAAc;MAACyH,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAD,QAAA,eAErCtG,OAAA,CAACwG,KAAK;QAACnG,OAAO,EAAEA,OAAQ;QAAAiG,QAAA,eACpBtG,OAAA;UAAKyG,SAAS,EAAC,WAAW;UAAAH,QAAA,eAEtBtG,OAAA;YAAAsG,QAAA,gBAEItG,OAAA;cAAKyG,SAAS,EAAC,aAAa;cAACC,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAL,QAAA,eACxDtG,OAAA;gBAAKyG,SAAS,EAAC,OAAO;gBAAAH,QAAA,eAClBtG,OAAA;kBAAAsG,QAAA,EAAI;gBAAqB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN/G,OAAA;cAAK0G,KAAK,EAAE;gBAAEM,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,OAAO;cAAoB;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBvE,CAAC,eAEN/G,OAAA;cAAKyG,SAAS,EAAC,6CAA6C;cAAAH,QAAA,gBACxDtG,OAAA;gBAAKyG,SAAS,EAAC,mCAAmC;gBAACC,KAAK,EAAE;kBAAEQ,MAAM,EAAE;gBAAG,CAAE;gBAAAZ,QAAA,eACrEtG,OAAA;kBAAKyG,SAAS,EAAEU,IAAI,KAAK,OAAO,GAAG,0BAA0B,GAAG,eAAgB;kBAC5ET,KAAK,EAAE;oBAAEU,YAAY,EAAE;kBAAsB,CAAE;kBAACC,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAAC,OAAO,CAAE;kBAAAhB,QAAA,eAEhFtG,OAAA;oBAAAsG,QAAA,EAAO;kBAAyB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN/G,OAAA;gBAAKyG,SAAS,EAAC,oBAAoB;gBAACC,KAAK,EAAE;kBAAEa,QAAQ,EAAE;gBAAM,CAAE;gBAAAjB,QAAA,eAE3DtG,OAAA;kBAAKyG,SAAS,EAAC,cAAc;kBAAAH,QAAA,EACxBa,IAAI,KAAK,OAAO,iBACbnH,OAAA;oBAAKyG,SAAS,EAAC,mBAAmB;oBAAAH,QAAA,gBAC9BtG,OAAA;sBAAKyG,SAAS,EAAC,iBAAiB;sBAAAH,QAAA,gBAC5BtG,OAAA;wBAAAsG,QAAA,EAAI;sBAAS;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClB/G,OAAA;wBAAIyG,SAAS,EAAC,sBAAsB;wBAAAH,QAAA,EAC/BlC,gBAAgB,CAACG,GAAG,CAAC,CAACK,OAAO,EAAE4C,KAAK;0BAAA,IAAAC,iBAAA;0BAAA,oBACjCzH,OAAA;4BAEIyG,SAAS,EAAE,wBAAwB,EAAAgB,iBAAA,GAAArC,gBAAgB,cAAAqC,iBAAA,uBAAhBA,iBAAA,CAAkBxD,kBAAkB,MAAKW,OAAO,CAACX,kBAAkB,GAAG,kBAAkB,GAAG,EAAE,EAAG;4BACnIoD,OAAO,EAAEA,CAAA,KAAM1C,6BAA6B,CAACC,OAAO,CAAE;4BAAA0B,QAAA,GAErD1B,OAAO,CAAC8C,YAAY,eACrB1H,OAAA;8BAAKyG,SAAS,EAAC,oBAAoB;8BAAAH,QAAA,GAC9B1B,OAAO,CAACJ,MAAM,gBACXxE,OAAA;gCAAKyG,SAAS,EAAC,2BAA2B;gCAAAH,QAAA,EAAC;8BAAK;gCAAAM,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,gBAEtD/G,OAAA;gCAAKyG,SAAS,EAAC,6BAA6B;gCAAAH,QAAA,EAAC;8BAAO;gCAAAM,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eAE9D/G,OAAA;gCAAKyG,SAAS,EAAC,SAAS;gCAAAH,QAAA,eACpBtG,OAAA;kCAAKyG,SAAS,EAAC,kBAAkB;kCAAAH,QAAA,eAC7BtG,OAAA;oCACIuF,IAAI,EAAC,UAAU;oCACfkB,SAAS,EAAC,UAAU;oCACpBkB,OAAO,EAAE/C,OAAO,CAACJ,MAAM,IAAI;oCAC3B;oCAAA;oCACAoD,QAAQ,EAAG7C,CAAC,IAAK;sCACb,MAAMb,QAAQ,GAAGa,CAAC,CAACE,MAAM,CAAC0C,OAAO;;sCAEjC;sCACA5D,6BAA6B,CAACpC,YAAY,EAAEiD,OAAO,CAACX,kBAAkB,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,gBAAgB,CAAC;oCAC5H;kCAAE;oCAAAwC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACL;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACD;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACL,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC;0BAAA,GA3BDS,KAAK;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OA4BV,CAAC;wBAAA,CACR;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN/G,OAAA;sBAAKyG,SAAS,EAAC,cAAc;sBAAAH,QAAA,gBACzBtG,OAAA;wBAAKyG,SAAS,EAAC,iBAAiB;wBAACC,KAAK,EAAE;0BAAEmB,eAAe,EAAE,OAAOC,kBAAkB;wBAAI,CAAE;wBAAAxB,QAAA,eACtFtG,OAAA;0BAAKyG,SAAS,EAAC,cAAc;0BAAAH,QAAA,GAExB,EAAA9F,kBAAA,GAAA4E,gBAAgB,cAAA5E,kBAAA,uBAAhBA,kBAAA,CAAkBoC,QAAQ,kBACvB5C,OAAA,CAAC+H,OAAO;4BAACC,MAAM,EAAE,KAAM;4BAAA1B,QAAA,gBACnBtG,OAAA;8BAAAsG,QAAA,GAAA7F,kBAAA,GAAI2E,gBAAgB,cAAA3E,kBAAA,uBAAhBA,kBAAA,CAAkBmC;4BAAQ;8BAAAgE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnC/G,OAAA;8BAAAsG,QAAA,EAAM;4BAAK;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC,eAId/G,OAAA,CAAC+H,OAAO;4BAACC,MAAM,EAAE,IAAK;4BAAA1B,QAAA,gBAClBtG,OAAA;8BAAAsG,QAAA,GAAA5F,kBAAA,GAAI0E,gBAAgB,cAAA1E,kBAAA,uBAAhBA,kBAAA,CAAkBmC;4BAAQ;8BAAA+D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnC/G,OAAA;8BAAAsG,QAAA,EAAM;4BAAK;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN/G,OAAA;wBAAKyG,SAAS,EAAC,gBAAgB;wBAAAH,QAAA,gBAC3BtG,OAAA;0BAAAsG,QAAA,EAAI;wBAAsB;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC/B/G,OAAA;0BACIyG,SAAS,EAAC,kBAAkB;0BAC5BvB,KAAK,EAAE,EAAAvE,kBAAA,GAAAyE,gBAAgB,cAAAzE,kBAAA,uBAAhBA,kBAAA,CAAkBkC,QAAQ,KAAI,EAAG;0BACxC+E,QAAQ,EAAE9C,oBAAqB,CAAC;wBAAA;0BAAA8B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC,CAAC,eACF/G,OAAA;0BAAKyG,SAAS,EAAC,iBAAiB;0BAAAH,QAAA,gBAC5BtG,OAAA;4BACIyG,SAAS,EAAC,gBAAgB;4BAC1BlB,IAAI,EAAC,QAAQ;4BACb8B,OAAO,EAAEpB,uBAAwB,CAAC;4BAAA;4BAAAK,QAAA,EACrC;0BAED;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACT/G,OAAA;4BACIyG,SAAS,EAAC,gBAAgB;4BAC1BlB,IAAI,EAAC,QAAQ;4BACb8B,OAAO,EAAEjB,wBAAyB,CAAC;4BAAA;4BAAAE,QAAA,EACtC;0BAED;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAQR,CAAC,eAEN/G,OAAA;0BAAKyG,SAAS,EAAC,qBAAqB;0BAAAH,QAAA,eAChCtG,OAAA;4BAAQyG,SAAS,EAAC,qBAAqB;4BAAClB,IAAI,EAAC,QAAQ;4BAAC8B,OAAO,EAAElC,kBAAmB;4BAAAmB,QAAA,EAAC;0BAAM;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEjG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEI;EAAC,gBACnB,CAAC;AAEX,CAAC;AAAAxG,EAAA,CAvTKD,OAAO;EAAA,QAUQ1B,WAAW;AAAA;AAAAqJ,EAAA,GAV1B3H,OAAO;AAyTb,eAAeA,OAAO;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}