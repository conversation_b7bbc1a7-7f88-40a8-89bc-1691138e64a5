const axios = require('axios');

class Whatsapp {
    endpoint = '';
    token = '';
    instanceId = '';

	constructor(endpoint, token, instanceId = '') {

		if (!endpoint) {
			throw "ERRO_SET_ENDPOINT"
		}

		this.endpoint = endpoint;
		this.token = token;
		this.instanceId = instanceId;
	}

    async axioGETRequest (path = '/') {
		const options = {
			method: "GET",
			url: this.endpoint + path,
			headers: {accept: 'application/json', apikey: this.token}
		};

		return axios.request(options);

    }

	async axioPOSTRequest (path = '/' , data) {
		const options = {
			method: "POST",
			url: this.endpoint + path,
			headers: {accept: 'application/json', apikey: this.token, 'Content-Type': 'application/json'},
			data: data
		};

		return axios.request(options);

    }

	async axioPUTRequest (path = '/' , data) {
		const options = {
			method: "PUT",
			url: this.endpoint + path,
			headers: {accept: 'application/json', apikey: this.token, 'Content-Type': 'application/json'},
			data: data
		};

		return axios.request(options);

    }

	async axioDELETERequest (path = '/') {
		const options = {
			method: "DELETE",
			url: this.endpoint + path,
			headers: {accept: 'application/json', apikey: this.token}
		};

		return axios.request(options);

    }

	async genQR(webhook = "") {
		return this.axioGETRequest("/instance/connect/" + this.instanceId)
	}

	async getInstanceStatus() {
		return this.axioGETRequest("/instance/connectionState/" + this.instanceId);
	}

	// 🔧 FUNÇÃO PARA SANITIZAR MENSAGEM
	sanitizeMessage(message) {
		if (!message) return "";
		
		return message
			.replace(/\\"/g, '"')           // Remove escape de aspas duplas
			.replace(/\\\\/g, '\\')         // Remove escape duplo de barras
			.replace(/\n{3,}/g, '\n\n')     // Limita quebras de linha
			.trim();                        // Remove espaços extras
	}

	// 🔧 FUNÇÃO PARA SANITIZAR NÚMERO WHATSAPP
	sanitizeWhatsappNumber(number) {
		if (!number) return "";
		
		// Remove o sufixo @s.whatsapp.net se presente
		let sanitizedNumber = number.replace(/@s\.whatsapp\.net$/, '');
		
		// Remove todos os caracteres não numéricos
		sanitizedNumber = sanitizedNumber.replace(/\D/g, '');
		
		// Verificar se precisa adicionar código do país (55)
		if (sanitizedNumber.length >= 10 && sanitizedNumber.length <= 11) {
			// Número com DDD + telefone (10-11 dígitos) - adicionar código do país
			sanitizedNumber = '55' + sanitizedNumber;
			console.log(`📞 Código do país adicionado: ${number} → ${sanitizedNumber}`);
		} else if (sanitizedNumber.length === 13 && sanitizedNumber.startsWith('55')) {
			// Número já tem código do país correto (13 dígitos começando com 55)
			console.log(`📞 Número já com código do país: ${number} → ${sanitizedNumber}`);
		} else if (sanitizedNumber.length === 12 && !sanitizedNumber.startsWith('55')) {
			// Número com 12 dígitos mas sem código do país - pode estar com DDD de 3 dígitos incorreto
			console.log(`📞 Número com formato suspeito (12 dígitos): ${number} → ${sanitizedNumber}`);
		} else {
			console.log(`📞 Número sanitizado: ${number} → ${sanitizedNumber}`);
		}
		
		return sanitizedNumber;
	}

	async SendPresenceUpdate(jid, presenceType, delay) {
		try {
			// 🔧 SANITIZAR JID ANTES DE USAR
			const sanitizedJid = this.sanitizeWhatsappNumber(jid);
			
			const data = {
				number: sanitizedJid,
				delay: delay, // Delay em millisegundos (padrão 10ms)
				presence: presenceType // "composing" (digitando) ou "recording" (gravando áudio)
			};

			console.log(`⌨️ Enviando presence update "${presenceType}" para ${sanitizedJid} (delay: ${delay}ms)`);

			// 🔧 ENDPOINT CORRETO: /chat/sendPresence/
			const response = await this.axioPOSTRequest("/chat/sendPresence/" + this.instanceId, data);
			
			console.log(`✅ Presence update "${presenceType}" enviado com sucesso para ${sanitizedJid}`);
			return response;

		} catch (error) {
			// 🔧 NÃO LANÇAR EXCEÇÃO - APENAS LOGAR O ERRO
			console.warn(`⚠️ Falha ao enviar presence update "${presenceType}" para ${jid}:`, error.message);
			console.warn(`📋 Status do erro:`, error.response?.status);
			console.warn(`📋 Dados da resposta:`, error.response?.data);
			
			// Retorna null para indicar falha sem quebrar o fluxo
			return null;
		}
	}

	// 🔧 FUNÇÃO SIMPLIFICADA: PRESENCE LONGO (AUTO-CANCELADO PELO WHATSAPP)
	StartLongPresence(jid, presenceType = "composing", longDelayMs) {
		const sanitizedJid = this.sanitizeWhatsappNumber(jid);
		
		console.log(`🔄 Iniciando presence longo "${presenceType}" para ${sanitizedJid} (delay: ${longDelayMs}ms)`);
		console.log(`📋 Presence será cancelado automaticamente quando mensagem for enviada`);
		
		// 🔧 ENVIAR PRESENCE SEM AWAIT - NÃO BLOQUEAR O FLUXO PRINCIPAL
		this.SendPresenceUpdate(jid, presenceType, longDelayMs).catch(error => {
			console.warn(`⚠️ Erro ao enviar presence longo para ${sanitizedJid}:`, error.message);
		});
		
		// 🎯 SEM NECESSIDADE DE CANCELAMENTO MANUAL - WhatsApp cancela automaticamente!
		return {
			// Função vazia para manter compatibilidade, mas não faz nada
			stop: async () => {
				console.log(`✅ Cancelando presence manualmente`);
				await this.SendPresenceUpdate(jid, "paused", 1000);
			}
		};
	}

	async SendSimpleTxtMessage(number, message = "") {
		try {
			// 🔧 VERIFICAR SE INSTÂNCIA ESTÁ CONECTADA
			try {
				const connectionState = await this.getConnectionState();
				if (connectionState?.data?.state !== 'open') {
					console.warn(`⚠️ Instância ${this.instanceId} não está conectada. Estado: ${connectionState?.data?.state}`);
				}
			} catch (connectionError) {
				console.warn(`⚠️ Não foi possível verificar conexão da instância: ${connectionError.message}`);
			}

			// 🔧 SANITIZAR NÚMERO E MENSAGEM ANTES DE ENVIAR
			const sanitizedNumber = this.sanitizeWhatsappNumber(number);
			const sanitizedMessage = this.sanitizeMessage(message);
			
			const data = {
				number: sanitizedNumber,
				text: sanitizedMessage
			};

			console.log(`📤 Enviando mensagem para ${sanitizedNumber}:`, sanitizedMessage.substring(0, 100) + '...');

			const response = await this.axioPOSTRequest("/message/sendText/" + this.instanceId, data);
			
			console.log(`✅ Mensagem enviada com sucesso para ${sanitizedNumber}`);
			return response;

		} catch (error) {
			console.error(`❌ Erro ao enviar mensagem para ${number}:`, error.message);
			console.error(`📋 Status do erro:`, error.response?.status);
			console.error(`📋 Dados da resposta:`, error.response?.data);
			
			// 🔧 TENTAR NOVAMENTE COM MENSAGEM MAIS SIMPLES SE FALHAR
			if (error.response?.status === 400) {
				console.log(`🔄 Tentando novamente com mensagem simplificada...`);
				try {
					const simpleMessage = message.replace(/[*"\\]/g, '').substring(0, 500);
					const sanitizedNumber = this.sanitizeWhatsappNumber(number);
					const simpleData = { number: sanitizedNumber, text: simpleMessage };
					
					return await this.axioPOSTRequest("/message/sendText/" + this.instanceId, simpleData);
				} catch (retryError) {
					console.error(`❌ Falha na segunda tentativa:`, retryError.message);
					throw retryError;
				}
			}
			
			throw error;
		}
	}

	async getProfile(number) {
		// 🔧 SANITIZAR NÚMERO ANTES DE USAR
		const sanitizedNumber = this.sanitizeWhatsappNumber(number);
		
		const data = {
			number: sanitizedNumber
		};

		const request = await this.axioPOSTRequest("/chat/fetchProfile/" + this.instanceId, data);

        if(request.status !== 200){
            throw "ERRO_GET_PROFILE"
        }

        return request.data;
	}

	async getProfilePicture(number) {
		// 🔧 SANITIZAR NÚMERO ANTES DE USAR
		const sanitizedNumber = this.sanitizeWhatsappNumber(number);
		
		const data = {
			number: sanitizedNumber
		};

		const request = await this.axioPOSTRequest("/chat/fetchProfilePictureUrl/" + this.instanceId, data);

        if(request.status !== 200){
            console.error(`❌ Erro ao buscar foto de perfil: ${request.status}`);
            throw "ERRO_GET_PROFILE_PICTURE"
        }

        return request.data;
	}

	async removeSession() {
		return this.axioDELETERequest("/instance/logout/" + this.instanceId);
	}

	async findChats() {
		// Evolution API v2 usa POST para findChats
		return this.axioPOSTRequest("/chat/findChats/" + this.instanceId, {});
	}

	async findMessages(remoteJid) {
		const data = {
			where: {
				key: {
					remoteJid: remoteJid
				}
			}
		};

		const result = await this.axioPOSTRequest("/chat/findMessages/" + this.instanceId, data);

		return result;
	}

	async markMessageAsRead(readMessages) {
		const data = {
			readMessages: readMessages  // Corrigido: "readMessages" ao invés de "read_messages"
		};

		// Usar POST conforme a documentação mostra
		const result = await this.axioPOSTRequest("/chat/markMessageAsRead/" + this.instanceId, data);

		return result;
	}

	async fetchProfile() {
		// Busca o perfil da própria instância conectada
		return this.axioPOSTRequest("/chat/fetchProfile/" + this.instanceId, {});
	}

	async getMyProfilePicture(myJid) {
		// Busca a foto de perfil da própria instância usando o JID
		// 🔧 SANITIZAR NÚMERO ANTES DE USAR
		const sanitizedNumber = this.sanitizeWhatsappNumber(myJid);
		
		const data = {
			number: sanitizedNumber
		};

		const request = await this.axioPOSTRequest("/chat/fetchProfilePictureUrl/" + this.instanceId, data);

        if(request.status !== 200){
            console.error(`❌ Erro ao buscar minha foto de perfil: ${request.status}`);
            throw "ERRO_GET_MY_PROFILE_PICTURE"
        }

        return request.data;
	}

	async setWebhook(webhookUrl, events = ['MESSAGES_UPSERT', 'MESSAGES_UPDATE', 'CONNECTION_UPDATE']) {
		const data = {
			webhook: {
				url: webhookUrl,
				byEvents: true, // **📌 REVERTER PARA byEvents: true (COMO ESTAVA FUNCIONANDO)**
				events: events,
				enabled: true
			}
		};

		console.log(`🔗 Configurando webhook:`, data);
		return this.axioPOSTRequest("/webhook/set/" + this.instanceId, data);
	}

	async getWebhook() {

		return this.axioGETRequest("/webhook/find/" + this.instanceId);
	}

	async getConnectionState() {
		// Busca o estado da conexão da instância
		return this.axioGETRequest("/instance/connectionState/" + this.instanceId);
	}

}

module.exports = Whatsapp