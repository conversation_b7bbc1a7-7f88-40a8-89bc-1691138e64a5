import React from "react";
import * as FaIcons from "react-icons/fa";
import * as AiIcons from "react-icons/ai";
import * as IoIcons from "react-icons/io";
import * as RiIcons from "react-icons/ri";
import * as HiIcons from "react-icons/hi";
import * as IoiosIcons from "react-icons/io";
import { BsFillMenuButtonFill } from "react-icons/bs";
import { HiOutlineSquaresPlus } from "react-icons/hi2";

import { CiBoxList } from "react-icons/ci";
import { CgReorder } from "react-icons/cg";
import { MdPointOfSale } from "react-icons/md";
import { MdOutlinePlaylistAdd } from "react-icons/md";
import { BsFillGearFill } from "react-icons/bs";
import { RiWhatsappLine } from "react-icons/ri";
import { RiAdminFill } from "react-icons/ri";
import { RiFileAddFill } from "react-icons/ri";
import { RiBarChartLine } from "react-icons/ri";
import { AiFillPrinter } from "react-icons/ai";
import { FaArrowTrendUp } from "react-icons/fa6";
import { FaArrowRightToCity } from "react-icons/fa6";
import { TbReportAnalytics, TbTruckDelivery } from "react-icons/tb";
import { PiCashRegister } from "react-icons/pi";
import { LuImport } from "react-icons/lu";
import { BsRobot } from "react-icons/bs";
import { MdTableBar } from "react-icons/md";

const IconMeusPedidos = () => (
  <svg
    id="line_gradient"
    height={20}
    viewBox="0 0 64 64"
    width={20}
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    data-name="line gradient"
  >
    <linearGradient
      id="linear-gradient"
      gradientUnits="userSpaceOnUse"
      x1="5.108"
      x2="58.889"
      y1={32}
      y2={32}
    >
      <stop offset={0} stopColor="#096fe0" />
      <stop offset=".15843" stopColor="#167ce3" />
      <stop offset={1} stopColor="#5ec3f6" />
    </linearGradient>
    <path
      d="m17.00192 3h-8.78335c-1.71338 0-3.10714 1.39376-3.10714 3.10714-.08269 1.76723 1.33236 3.27198 3.10715 3.26694h8.78333c4.11772-.09088 4.12111-6.28248 0-6.37409zm1.03571 3.26695c0 .57146-.46476 1.03571-1.03571 1.03571h-8.78335c-1.37751.0017-1.3808-2.23264.00004-2.23123-.00004 0 8.7833 0 8.7833 0 .64043-.00438 1.10241.57244 1.03571 1.19552zm1.03571 5.33737h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30266h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30165h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30266h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm18.35409-55.92857h-8.78334c-1.71338 0-3.10714 1.39376-3.10714 3.10714-.08269 1.76723 1.33236 3.27198 3.10715 3.26694h8.78333c4.11772-.09088 4.12111-6.28248 0-6.37409zm1.03571 3.26695c0 .57146-.46425 1.03571-1.03571 1.03571h-8.78334c-1.37751.0017-1.3808-2.23264.00004-2.23123-.00004 0 8.7833 0 8.7833 0 .64097-.00438 1.10233.57242 1.03571 1.19552zm1.03571 5.33737h-12.9262c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30266h-12.9262c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm18.3546-30.11562h-8.78435c-1.71338 0-3.10714 1.39376-3.10714 3.10714-.08269 1.76723 1.33236 3.27198 3.10715 3.26694h8.78434c4.11772-.09088 4.12111-6.28248 0-6.37409zm1.03571 3.26695c0 .57146-.46425 1.03571-1.03571 1.03571h-8.78435c-1.37764.00192-1.38091-2.23286.00004-2.23123-.00004 0 8.78431 0 8.78431 0 .64097-.00438 1.10233.57242 1.03571 1.19552zm1.03571 5.33737h-12.92721c-.57247 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46324 1.03571 1.03571 1.03571h12.92721c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85578v-6.53289h10.85578zm1.03571 4.30266h-12.92721c-.57247 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46324 1.03571 1.03571 1.03571h12.92721c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85578v-6.53289h10.85578zm1.03571 4.30165h-12.92721c-.57247 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46324 1.03571 1.03571 1.03571h12.92721c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85578v-6.53289h10.85578z"
      fill="url(#linear-gradient)"
    />
  </svg>
);

const IconWhatsapp = () => (
  <svg
    height={20}
    viewBox="0 0 512 512"
    width={20}
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <linearGradient id="a">
      <stop offset={0} stopColor="#2af598" />
      <stop offset={1} stopColor="#009efd" />
    </linearGradient>
    <linearGradient
      id="b"
      gradientUnits="userSpaceOnUse"
      x1="255.7523048184"
      x2="255.7523048184"
      xlinkHref="#a"
      y1={0}
      y2="512.0000297334"
    />
    <linearGradient
      id="c"
      gradientUnits="userSpaceOnUse"
      x1="257.750272915"
      x2="257.750272915"
      xlinkHref="#a"
      y1="141.862828"
      y2="361.650308"
    />
    <path
      d="m435.921875 74.351562c-48.097656-47.917968-112.082031-74.3242182-180.179687-74.351562-67.945313 0-132.03125 26.382812-180.445313 74.289062-48.5 47.988282-75.234375 111.761719-75.296875 179.339844v.078125.046875c.0078125 40.902344 10.753906 82.164063 31.152344 119.828125l-30.453125 138.417969 140.011719-31.847656c35.460937 17.871094 75.027343 27.292968 114.933593 27.308594h.101563c67.933594 0 132.019531-26.386719 180.441406-74.296876 48.542969-48.027343 75.289062-111.71875 75.320312-179.339843.019532-67.144531-26.820312-130.882813-75.585937-179.472657zm-180.179687 393.148438h-.089844c-35.832032-.015625-71.335938-9.011719-102.667969-26.023438l-6.621094-3.59375-93.101562 21.175782 20.222656-91.90625-3.898437-6.722656c-19.382813-33.425782-29.625-70.324219-29.625-106.71875.074218-117.800782 96.863281-213.75 215.773437-213.75 57.445313.023437 111.421875 22.292968 151.984375 62.699218 41.175781 41.03125 63.84375 94.710938 63.824219 151.152344-.046875 117.828125-96.855469 213.6875-215.800781 213.6875zm0 0"
      fill="url(#b)"
    />
    <path
      d="m186.152344 141.863281h-11.210938c-3.902344 0-10.238281 1.460938-15.597656 7.292969-5.363281 5.835938-20.476562 19.941406-20.476562 48.628906s20.964843 56.40625 23.886718 60.300782c2.925782 3.890624 40.46875 64.640624 99.929688 88.011718 49.417968 19.421875 59.476562 15.558594 70.199218 14.585938 10.726563-.96875 34.613282-14.101563 39.488282-27.714844s4.875-25.285156 3.414062-27.722656c-1.464844-2.429688-5.367187-3.886719-11.214844-6.800782-5.851562-2.917968-34.523437-17.261718-39.886718-19.210937-5.363282-1.941406-9.261719-2.914063-13.164063 2.925781-3.902343 5.828125-15.390625 19.3125-18.804687 23.203125-3.410156 3.894531-6.824219 4.382813-12.675782 1.464844-5.851562-2.925781-24.5-9.191406-46.847656-29.050781-17.394531-15.457032-29.464844-35.167969-32.878906-41.003906-3.410156-5.832032-.363281-8.988282 2.570312-11.898438 2.628907-2.609375 6.179688-6.179688 9.105469-9.582031 2.921875-3.40625 3.753907-5.835938 5.707031-9.726563 1.949219-3.890625.972657-7.296875-.488281-10.210937-1.464843-2.917969-12.691406-31.75-17.894531-43.28125h.003906c-4.382812-9.710938-8.996094-10.039063-13.164062-10.210938zm0 0"
      fill="url(#c)"
    />
  </svg>
);

const IconPDV = () => (
  <svg
    height={20}
    viewBox="0 0 64 64"
    width={20}
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <linearGradient
      id="New_Gradient_Swatch_3"
      gradientUnits="userSpaceOnUse"
      x1="42.43"
      x2="42.43"
      y1="65.857"
      y2="-4.017"
    >
      <stop offset={0} stopColor="#006df0" />
      <stop offset={1} stopColor="#00e7f0" />
    </linearGradient>
    <linearGradient
      id="New_Gradient_Swatch_3-2"
      x1="51.829"
      x2="51.829"
      xlinkHref="#New_Gradient_Swatch_3"
      y1="65.857"
      y2="-4.017"
    />
    <linearGradient
      id="New_Gradient_Swatch_3-3"
      x1={49}
      x2={49}
      xlinkHref="#New_Gradient_Swatch_3"
      y1="65.857"
      y2="-4.017"
    />
    <linearGradient
      id="New_Gradient_Swatch_3-4"
      x1={32}
      x2={32}
      xlinkHref="#New_Gradient_Swatch_3"
      y1="65.857"
      y2="-4.017"
    />
    <linearGradient
      id="New_Gradient_Swatch_3-7"
      x1={29}
      x2={29}
      xlinkHref="#New_Gradient_Swatch_3"
      y1="65.857"
      y2="-4.017"
    />
    <linearGradient
      id="New_Gradient_Swatch_3-8"
      x1={22}
      x2={22}
      xlinkHref="#New_Gradient_Swatch_3"
      y1="65.857"
      y2="-4.017"
    />
    <linearGradient
      id="New_Gradient_Swatch_3-9"
      x1={31}
      x2={31}
      xlinkHref="#New_Gradient_Swatch_3"
      y1="65.857"
      y2="-4.017"
    />
    <g
      id="mobilephone_food_delivery_package_shopping"
      data-name="mobilephone, food, delivery, package, shopping"
    >
      <path
        d="m61.73 38.42a3.208 3.208 0 0 0 -.27-.65 3.011 3.011 0 0 0 -4.1-1.1l-4.8 2.78a2.962 2.962 0 0 0 -2.56-1.45h-7.76l-1.37-.68a2.971 2.971 0 0 0 -1.34-.32h-4.86a2.974 2.974 0 0 0 -1.8.6l-1.87 1.4v-1a1 1 0 0 0 -1-1h-5v-3a1 1 0 0 0 -2 0v20a1 1 0 0 0 2 0v-3h5a1 1 0 0 0 1-1v-1.13l.94.63a2.979 2.979 0 0 0 1.67.5h12.85a2.936 2.936 0 0 0 1.53-.42l12.37-7.71a3.023 3.023 0 0 0 1.5-2.61 2.842 2.842 0 0 0 -.13-.84zm-32.73 10.58h-4v-10h4zm30.33-8.85-12.38 7.72a.922.922 0 0 1 -.49.13h-12.85a1.023 1.023 0 0 1 -.56-.17l-2.05-1.37v-4.96l3.07-2.3a.984.984 0 0 1 .6-.2h4.86a.956.956 0 0 1 .44.11l1.58.78a1 1 0 0 0 .45.11h8a1 1 0 0 1 .87.51 1.065 1.065 0 0 1 .13.53.956.956 0 0 1 -.31.67 1.008 1.008 0 0 1 -.69.29h-7a1 1 0 0 0 0 2s7.25 0 7.31-.02a2.958 2.958 0 0 0 1.8-.85 2.92 2.92 0 0 0 .83-1.6l5.43-3.13a.984.984 0 0 1 1.35.37 1.121 1.121 0 0 1 .1.22.884.884 0 0 1 .04.27 1.014 1.014 0 0 1 -.53.89z"
        fill="url(#New_Gradient_Swatch_3)"
      />
      <path
        d="m48 21a1 1 0 0 0 1 1 5.013 5.013 0 0 1 4.715 3.334 1 1 0 0 0 1.885-.668 7.017 7.017 0 0 0 -6.6-4.666 1 1 0 0 0 -1 1z"
        fill="url(#New_Gradient_Swatch_3-2)"
      />
      <path
        d="m46 16a2.959 2.959 0 0 0 .346 1.369 10.009 10.009 0 0 0 -7.346 9.631v.184a2.988 2.988 0 0 0 .343 5.738l1.762 3.525a1 1 0 0 0 .895.553h14a1 1 0 0 0 .9-.553l1.762-3.525a2.988 2.988 0 0 0 .338-5.738v-.184a10.009 10.009 0 0 0 -7.346-9.631 2.959 2.959 0 0 0 .346-1.369 3 3 0 0 0 -6 0zm9.382 19h-12.764l-1-2h14.764zm2.618-4h-18a1 1 0 0 1 0-2h18a1 1 0 0 1 0 2zm-1-4h-16a8 8 0 0 1 16 0zm-7-11a1 1 0 1 1 -1-1 1 1 0 0 1 1 1z"
        fill="url(#New_Gradient_Swatch_3-3)"
      />
      <g fill="url(#New_Gradient_Swatch_3-4)">
        <path d="m50 50a1 1 0 0 0 -1 1v6a3 3 0 0 1 -3 3h-28a3 3 0 0 1 -3-3v-50a3 3 0 0 1 3-3h28a3 3 0 0 1 3 3v4a1 1 0 0 0 2 0v-4a5.006 5.006 0 0 0 -5-5h-28a5.006 5.006 0 0 0 -5 5v50a5.006 5.006 0 0 0 5 5h28a5.006 5.006 0 0 0 5-5v-6a1 1 0 0 0 -1-1z" />
        <path d="m28.24 7.552-.816-1.213a2.98 2.98 0 0 0 -2.494-1.339h-5.93a3 3 0 0 0 -3 3v48a3 3 0 0 0 3 3h26a3 3 0 0 0 3-3v-4a1 1 0 0 0 -2 0v4a1 1 0 0 1 -1 1h-26a1 1 0 0 1 -1-1v-48a1 1 0 0 1 1-1h5.93a.99.99 0 0 1 .83.448l.816 1.213a2.979 2.979 0 0 0 2.494 1.339h5.86a2.978 2.978 0 0 0 2.49-1.332l.824-1.227a.988.988 0 0 1 .826-.441h5.93a1 1 0 0 1 1 1v3a1 1 0 0 0 2 0v-3a3 3 0 0 0 -3-3h-5.93a2.977 2.977 0 0 0 -2.49 1.332l-.824 1.227a.988.988 0 0 1 -.826.441h-5.86a.99.99 0 0 1 -.83-.448z" />
        <path d="m33 7a1 1 0 0 0 0-2h-2a1 1 0 0 0 0 2z" />
      </g>
      <path
        d="m38 21v-8a1 1 0 0 0 -1-1h-16a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1zm-2-1h-14v-6h14z"
        fill="url(#New_Gradient_Swatch_3-7)"
      />
      <path
        d="m24 29a1 1 0 0 0 -1-1h-2a1 1 0 0 0 0 2h2a1 1 0 0 0 1-1z"
        fill="url(#New_Gradient_Swatch_3-8)"
      />
      <path
        d="m27 28a1 1 0 0 0 0 2h8a1 1 0 0 0 0-2z"
        fill="url(#New_Gradient_Swatch_3-9)"
      />
      <path
        d="m23 24h-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2z"
        fill="url(#New_Gradient_Swatch_3-8)"
      />
      <path
        d="m38 25a1 1 0 0 0 -1-1h-10a1 1 0 0 0 0 2h10a1 1 0 0 0 1-1z"
        fill="url(#New_Gradient_Swatch_3-4)"
      />
    </g>
  </svg>
);

export const SidebarData = [
  {
    title: "Meus Pedidos",
    path: "/",
    permission: "default",
    icon: (
      <IconMeusPedidos
        style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
      />
    ),
  },
  {
    title: "WhatsApp",
    path: "/whatsapp",
    permission: "default",
    icon: (
      <IconWhatsapp style={{ color: "rgb(120,120,120)", fontSize: "20px" }} />
    ),
  },
  {
    title: "Pedidos Balcão",
    path: "/pdv",
    permission: "default",
    icon: <IconPDV style={{ color: "rgb(120,120,120)", fontSize: "20px" }} />,
  },
  {
    title: "Salão",
    permission: "default",
    icon: <MdTableBar style={{ color: "rgb(120,120,120)", fontSize: "20px" }} />,
    subNav: [
      {
        title: "Mesas",
        path: "/pedidos-mesas",
        icon: <MdTableBar style={{ color: "rgb(120,120,120)", fontSize: "20px" }} />,
        cName: "sub-nav",
      },
      {
        title: "Garçons",
        path: "/mesas-garcons",
        icon: (
          <FaIcons.FaUsers
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
        cName: "sub-nav",
      },
    ],
  },
  {
    title: "Caixa",
    path: "/caixa",
    permission: "default",
    icon: (
      <PiCashRegister style={{ color: "rgb(120,120,120)", fontSize: "20px" }} />
    ),
  },
  {
    title: "Cardápio",
    //path: '/list-empresa',
    permission: "default",
    icon: (
      <BsFillMenuButtonFill
        style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
      />
    ),
    iconClosed: (
      <RiIcons.RiArrowDownSFill
        style={{
          marginLeft: "-5px",
          color: "rgb(120,120,120)",
          fontSize: "20px",
        }}
      />
    ),
    iconOpened: (
      <RiIcons.RiArrowUpSFill
        style={{
          marginLeft: "-5px",
          color: "rgb(120,120,120)",
          fontSize: "20px",
        }}
      />
    ),

    subNav: [
      {
        title: "Gestor de Cardápio",
        path: "/list-categoria",
        icon: (
          <CiBoxList
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
        cName: "sub-nav",
      },
      /*{
        title: "Itens",
        path: "/list-item",
        icon: (
          <CiBoxList style={{ color: "rgb(120,120,120)", fontSize: "20px" }} />
        ),
        cName: "sub-nav",
      },*/
      {
        title: "Gerenciar complementos",
        path: "/list-adicional",
        icon: (
          <MdOutlinePlaylistAdd
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
        cName: "sub-nav",
      },
    ],
  },
  {
    title: "Relatórios",
    //path: '/list-empresa',
    permission: "default",
    icon: (
      <TbReportAnalytics
        style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
      />
    ),
    iconClosed: (
      <RiIcons.RiArrowDownSFill
        style={{
          marginLeft: "-5px",
          color: "rgb(120,120,120)",
          fontSize: "20px",
        }}
      />
    ),
    iconOpened: (
      <RiIcons.RiArrowUpSFill
        style={{
          marginLeft: "-5px",
          color: "rgb(120,120,120)",
          fontSize: "20px",
        }}
      />
    ),

    subNav: [
      {
        title: "Geral",
        path: "/relatorio-geral",
        icon: (
          <RiBarChartLine
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
        cName: "sub-nav",
      },
      {
        title: "Meu desempenho",
        path: "/desempenho",
        icon: (
          <FaArrowTrendUp
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
        cName: "sub-nav",
      },
      /*{
        title: 'Itens',
        path: '/list-item',
        icon: <CiBoxList style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>,
        cName: 'sub-nav'
      },
      {
        title: 'Adicionais',
        path: '/list-adicional',
        icon: <MdOutlinePlaylistAdd style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>,
        cName: 'sub-nav'
      }*/
    ],
  },
  //{
  //  title: "Empresas",
  //  path: "/list-empresa",
  //  permission: "viewIndustry",
  //  icon: (
  //    <IoiosIcons.IoIosBusiness
  //      style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
  //    />
  //  ),
    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,

    // subNav: [
    //   {
    //     title: 'Cadastrar Indústria',
    //     path: '/empresa',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    //     cName: 'sub-nav'
    //   },
    //   {
    //     title: 'Listar Indústrias',
    //     path: '/list-empresa',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    //     cName: 'sub-nav'
    //   }
    // ]
  //},
  //{
  //  title: "Usuários",
  //  path: "/list-users",
  //  permission: "viewUser",
  //  icon: (
  //    <FaIcons.FaUsers
  //      style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
  //    />
  //  ),
    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,

    // subNav: [
    //   {
    //     title: 'Cadastrar Usuário',
    //     path: '/cadastro-user',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    //     cName: 'sub-nav'
    //   },
    //   {
    //     title: 'Listar Usuários',
    //     path: '/list-users',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    //     cName: 'sub-nav'
    //   }
    // ]
  //},
  {
    title: "Entregadores",
    path: "/list-entregadores",
    permission: "default",
    icon: (
      <TbTruckDelivery
        style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
      />
    ),
    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,

    // subNav: [
    //   {
    //     title: 'Cadastrar Usuário',
    //     path: '/cadastro-user',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    //     cName: 'sub-nav'
    //   },
    //   {
    //     title: 'Listar Usuários',
    //     path: '/list-users',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    //     cName: 'sub-nav'
    //   }
    // ]
  },
  {
    title: "Minha Conta",
    //path: '/list-users',
    permission: "default",
    icon: (
      <FaIcons.FaRegUser
        style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
      />
    ),
    iconClosed: (
      <RiIcons.RiArrowDownSFill
        style={{
          marginLeft: "-5px",
          color: "rgb(120,120,120)",
          fontSize: "20px",
        }}
      />
    ),
    iconOpened: (
      <RiIcons.RiArrowUpSFill
        style={{
          marginLeft: "-5px",
          color: "rgb(120,120,120)",
          fontSize: "20px",
        }}
      />
    ),

    subNav: [
      {
        title: "Planos",
        path: "/planos",
        icon: (
          <IoIcons.IoIosPaper
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
        cName: "sub-nav",
      },
    ],
  },
  {
    title: "Clientes",
    path: "/list-cliente",
    icon: (
      <IoiosIcons.IoIosContacts
        style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
      />
    ),
    permission: "viewCVO",
    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,

    // subNav: [
    //   {
    //     title: 'Cadastrar Cliente',
    //     path: '/',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>
    //   },
    //   {
    //     title: 'Listar Clientes',
    //     path: '/',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>
    //   }
    // ]
  },
  {
    title: "Vendedores",
    path: "/list-vendedor",
    icon: (
      <IoIcons.IoMdPeople
        style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
      />
    ),
    permission: "viewCVO",
    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,

    // subNav: [
    //   {
    //     title: 'Cadastrar Vendedor',
    //     path: '/',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>
    //   },
    //   {
    //     title: 'Listar Vendedores',
    //     path: '/',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>
    //   }
    // ]
  },
  {
    title: "Orçamentos",
    path: "/list-orcamento",
    icon: (
      <FaIcons.FaEnvelopeOpenText
        style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
      />
    ),
    permission: "viewCVO",
    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,
    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:"-5px", color:"rgb(120,120,120)", fontSize:"20px"}}/>,

    // subNav: [
    //   {
    //     title: 'Cadastrar Orçamento',
    //     path: '/',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>
    //   },
    //   {
    //     title: 'Listar Orçamentos',
    //     path: '/',
    //     icon: <IoIcons.IoIosPaper style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>
    //   }
    // ]
  },
  {
    title: "Configurações",
    //path: '/configuracoes',
    icon: (
      <BsFillGearFill style={{ color: "rgb(120,120,120)", fontSize: "20px" }} />
    ),
    permission: "default",
    iconClosed: (
      <RiIcons.RiArrowDownSFill
        style={{
          marginLeft: "-5px",
          color: "rgb(120,120,120)",
          fontSize: "20px",
        }}
      />
    ),
    iconOpened: (
      <RiIcons.RiArrowUpSFill
        style={{
          marginLeft: "-5px",
          color: "rgb(120,120,120)",
          fontSize: "20px",
        }}
      />
    ),

    subNav: [
      {
        title: "Endereço",
        path: "/endereco-estabelecimento",
        icon: (
          <FaIcons.FaMapMarkerAlt
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
      {
        title: "Regiões de entrega",
        path: "/raio-entrega",
        icon: (
          <FaIcons.FaMapMarked
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },/*
      {
        title: "Sincronizar Whatsapp",
        path: "/whatsapp-sync",
        icon: (
          <FaIcons.FaWhatsapp
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      }*/
      {
        title: "Impressora",
        path: "/impressora",
        icon: (
          <AiFillPrinter
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
      {
        title: "Horários de Funcionamento",
        path: "/horario-funcionamento",
        icon: (
          <FaIcons.FaRegClock
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
      {
        title: "Formas de pagamento aceitas",
        path: "/formas-pagamento",
        icon: (
          <FaIcons.FaMoneyCheckAlt
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
      {
        title: "Entrega/Retirada",
        path: "/entrega-retirada",
        icon: (
          <FaArrowRightToCity
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
      {
        title: "Meus pedidos",
        path: "/meus-pedidos-configuracao",
        icon: (
          <AiIcons.AiOutlineTool
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
      {
        title: "Respostas do Bot",
        path: "/robo-configuracao",
        icon: (
          <BsRobot
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
      {
        title: "Importar Cardápio",
        path: "/importacao-cardapio",
        icon: (
          <LuImport
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
    ],
  },
  {
    title: "Administrativo",
    //path: '/configuracoes',
    icon: (
      <RiAdminFill style={{ color: "rgb(120,120,120)", fontSize: "20px" }} />
    ),
    permission: "AdminOnly",
    iconClosed: (
      <RiIcons.RiArrowDownSFill
        style={{
          marginLeft: "-5px",
          color: "rgb(120,120,120)",
          fontSize: "20px",
        }}
      />
    ),
    iconOpened: (
      <RiIcons.RiArrowUpSFill
        style={{
          marginLeft: "-5px",
          color: "rgb(120,120,120)",
          fontSize: "20px",
        }}
      />
    ),

    subNav: [
      {
        title: "Adicionar Planos",
        path: "/planos-admin",
        icon: (
          <RiFileAddFill
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
      {
        title: "Listar Planos",
        path: "/list-plans-admin",
        icon: (
          <IoIcons.IoIosPaper
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
      {
        title: "Listar Empresas",
        path: "/list-empresas",
        icon: (
          <FaIcons.FaBuilding
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
      {
        title: "Administrar Cardápios",
        path: "/admin-solicitacoes-cardapio",
        icon: (
          <FaIcons.FaUtensils
            style={{ color: "rgb(120,120,120)", fontSize: "20px" }}
          />
        ),
      },
    ],
  },
  // {
  //   title: 'Configurações',
  //   path: '/',
  //   permission: 'default',
  //   icon: <IoiosIcons.IoIosSettings style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>
  // },
  // {
  //   title: 'Logout',
  //   path: '/',
  //   permission: 'default',
  //   icon: <HiIcons.HiOutlineLogout style={{color:"rgb(120,120,120)", fontSize:"20px"}}/>
  // }
];
