{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\roboConfig\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport './style.css';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport LeftMenu from \"../../components/LeftMenu\";\nimport { Modal } from \"../../components/Modal\";\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\nimport styled from 'styled-components';\nimport { FaEdit, FaTrash, FaPlus, FaSearch } from \"react-icons/fa\";\nimport { IoMdClose } from \"react-icons/io\";\nimport CryptoJS from 'crypto-js';\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Teste = styled.div`\n\n    display: flex;\n    margin-left:  ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'}; \n    height:auto;\n    width:auto;\n    transition: 150ms;\n    background-color:rgb(247,247,247)!important;\n    overflow: initial;\n    z-Index: 9;\n\n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\n_c = Teste;\nconst Message = styled.div`\n  background: ${props => props.fromMe ? '#dcf8c6' : '#ffffff'};\n  padding: 10px;\n  border-radius: 7.5px;\n  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);\n  font-size: 1rem;\n  margin: 1px 0px 1px ${props => props.fromMe ? 'auto' : '0px'};\n  width: fit-content;\n  max-width: 60%;\n  margin-bottom: ${props => props.fromMe ? '3px' : '10px'};\n  white-space: pre-wrap; /* Adiciona suporte para quebras de linha */\n  span {\n    font-size: 0.9rem;\n    color: rgba(0, 0, 0, 0.45);\n    display: block;\n    text-align: right;\n  }\n`;\n_c2 = Message;\nconst RoboCfg = () => {\n  _s();\n  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\n  const userEncrypted = localStorage.getItem('user');\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  const userParse = JSON.parse(user);\n  const userID = userParse._id;\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const idEmpresa = empresaParse.id_empresa;\n  const empresaObjId = empresaParse._id;\n  const [refresh, setRefresh] = useState(false);\n  const [tela, setTela] = useState(\"tela1\");\n  const {\n    sidebar,\n    setSidebar\n  } = useContext(SidebarContext);\n  ; //TESTANDO CONST NO AppROUTES\n  const [companyResponses, setCompanyResponses] = useState([]);\n  const [selectedResponse, setSelectedResponse] = useState(null);\n  const navigate = useNavigate();\n  useEffect(() => {\n    // Função para buscar os dados do banco de dados\n    const fetchData = async () => {\n      try {\n        // Supondo que getEmpresaWithObjId seja uma função que retorna uma Promise\n        const empresaTemp = await getEmpresaWithObjId(empresaObjId);\n        const companyResponsesTemp = await getCompanyResponses(empresaObjId);\n        console.log(\"companyResponses:\", companyResponsesTemp.data.responses);\n        setCompanyResponses(companyResponsesTemp.data.responses);\n        setSelectedResponse(companyResponsesTemp.data.responses[0]);\n      } catch (error) {\n        console.error(\"Erro ao buscar dados da empresa:\", error);\n        // Lidar com o erro, talvez definindo algum estado de erro na UI\n      }\n    };\n    fetchData();\n  }, [refresh]); // O array vazio [] indica que o useEffect será executado uma vez após a montagem do componente\n\n  const debouncedUpdateQuestionActive = debounce(async (empresaObjId, questionIdentifier, isActive, setCompanyResponses, companyResponses) => {\n    try {\n      // Atualiza no backend\n      await updateQuestionActive(empresaObjId, questionIdentifier, isActive);\n\n      // Atualiza localmente após confirmação do backend\n      const updatedResponses = companyResponses.map(response => response.questionIdentifier === questionIdentifier ? {\n        ...response,\n        active: isActive\n      } : response);\n      setCompanyResponses(updatedResponses);\n      console.log(\"Disponibilidade atualizada com sucesso.\");\n    } catch (error) {\n      console.error(\"Erro ao atualizar disponibilidade:\", error);\n    }\n  }, 300); // Aguarda 300ms após o último evento\n\n  const handleBack = () => {\n    //setSidebar(!sidebar)\n    navigate(\"/\");\n  };\n  const handleSelectQuestionAndAnswer = message => {\n    setSelectedResponse(message);\n  };\n\n  // Função para atualizar o response do selectedResponse diretamente\n  const handleResponseChange = e => {\n    setSelectedResponse(prevSelectedResponse => ({\n      ...prevSelectedResponse,\n      response: e.target.value\n    }));\n  };\n\n  // Função para salvar as alterações no companyResponses\n  const handleSaveResponse = async () => {\n    if (selectedResponse) {\n      // Atualiza a resposta localmente\n      const updatedResponses = companyResponses.map(response => response.questionIdentifier === selectedResponse.questionIdentifier ? {\n        ...response,\n        response: selectedResponse.response\n      } // Atualiza apenas a mensagem de resposta\n      : response);\n      setCompanyResponses(updatedResponses);\n      try {\n        // Chama a API para atualizar o backend\n        await updateQuestionResponses(empresaObjId, updatedResponses);\n        toast(\"Respostas atualizadas com sucesso!\", {\n          autoClose: 2000,\n          type: \"success\"\n        });\n      } catch (error) {\n        console.error(\"Erro ao atualizar respostas:\", error);\n        toast(\"Ocorreu um erro ao salvar as respostas.\", {\n          autoClose: 2000,\n          type: \"error\"\n        });\n      }\n    }\n  };\n\n  // Função genérica para inserir texto no cursor\n  const insertAtCursor = (textarea, text) => {\n    if (textarea) {\n      const startPos = textarea.selectionStart;\n      const endPos = textarea.selectionEnd;\n      const value = textarea.value;\n\n      // Atualiza o texto no campo com o novo valor\n      textarea.value = value.substring(0, startPos) + text + value.substring(endPos);\n\n      // Mantém o cursor após o texto inserido\n      textarea.selectionStart = textarea.selectionEnd = startPos + text.length;\n    }\n  };\n\n  // Funções específicas para cada botão\n  const handleInsertNomeCliente = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"{nome_cliente}\");\n  };\n  const handleInsertLinkCardapio = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"{link}\");\n  };\n  const handleInsertSaudacao = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"Agradecemos a preferência\");\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: /*#__PURE__*/_jsxDEV(Teste, {\n        sidebar: sidebar,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-100 p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-header\",\n              style: {\n                marginBottom: \"0px\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"title\",\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  children: \"Configura\\xE7\\xF5es do Rob\\xF4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                justifyContent: \"right\" /*, height:\"80px\"*/\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contentItemComplete flex-column flex-md-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group inputGroup-etapasItem\",\n                style: {\n                  height: 50\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: tela === \"tela1\" ? \"etapasAddItem etapaAtiva\" : \"etapasAddItem\",\n                  style: {\n                    borderBottom: '1px solid lightgray'\n                  },\n                  onClick: () => setTela(\"tela1\"),\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"1. Personalizar Mensagens\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mh-100\",\n                style: {\n                  maxWidth: \"80%\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"formGroupRow\",\n                  children: tela === \"tela1\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"roboCfg-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"roboCfg-sidebar\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        children: \"Mensagens\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 234,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"roboCfg-message-list\",\n                        children: companyResponses.map((message, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                          className: `roboCfg-message-item ${(selectedResponse === null || selectedResponse === void 0 ? void 0 : selectedResponse.questionIdentifier) === message.questionIdentifier ? 'roboCfg-selected' : ''}`,\n                          onClick: () => handleSelectQuestionAndAnswer(message),\n                          children: [message.questionType, /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"checkBoxContentMsg\",\n                            children: [message.active ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"roboCfg-edit-button-ativo\",\n                              children: \"Ativo\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 245,\n                              columnNumber: 73\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"roboCfg-edit-button-inativo\",\n                              children: \"Inativo\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 247,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"wrapper\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"switch_box box_1\",\n                                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                                  type: \"checkbox\",\n                                  className: \"switch_1\",\n                                  checked: message.active || false\n                                  // Dentro do checkbox\n                                  ,\n                                  onChange: e => {\n                                    const isActive = e.target.checked;\n\n                                    // Chamar a função debounce para evitar múltiplas chamadas ao backend\n                                    debouncedUpdateQuestionActive(empresaObjId, message.questionIdentifier, isActive, setCompanyResponses, companyResponses);\n                                  }\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 251,\n                                  columnNumber: 77\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 250,\n                                columnNumber: 73\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 249,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 243,\n                            columnNumber: 65\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 237,\n                          columnNumber: 61\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"roboCfg-main\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"roboCfg-preview\",\n                        style: {\n                          backgroundImage: `url(${backgroundWhatsApp})`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-chat\",\n                          children: [(selectedResponse === null || selectedResponse === void 0 ? void 0 : selectedResponse.question) && /*#__PURE__*/_jsxDEV(Message, {\n                            fromMe: false,\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              children: selectedResponse === null || selectedResponse === void 0 ? void 0 : selectedResponse.question\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 276,\n                              columnNumber: 69\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"12:00\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 277,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 275,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(Message, {\n                            fromMe: true,\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              children: selectedResponse === null || selectedResponse === void 0 ? void 0 : selectedResponse.response\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 283,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"12:01\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 284,\n                              columnNumber: 65\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 282,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 272,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 271,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"roboCfg-editor\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Personalize a mensagem\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 289,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                          className: \"roboCfg-textarea\",\n                          value: (selectedResponse === null || selectedResponse === void 0 ? void 0 : selectedResponse.response) || \"\",\n                          onChange: handleResponseChange // Altera diretamente o response do selectedResponse\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 290,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-buttons\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-button\",\n                            type: \"button\",\n                            onClick: handleInsertNomeCliente // Chama a função para Nome do cliente\n                            ,\n                            children: \"Nome do cliente\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 296,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-button\",\n                            type: \"button\",\n                            onClick: handleInsertLinkCardapio // Chama a função para Link do cardápio\n                            ,\n                            children: \"Link do card\\xE1pio\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 303,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 295,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-save-cancel\",\n                          children: /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-save-button\",\n                            type: \"button\",\n                            onClick: handleSaveResponse,\n                            children: \"Salvar\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 320,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 319,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 288,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(RoboCfg, \"OeaYYXm/8Xr3mNEg6ft5+u4HHJI=\", false, function () {\n  return [useNavigate];\n});\n_c3 = RoboCfg;\nexport default RoboCfg;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Teste\");\n$RefreshReg$(_c2, \"Message\");\n$RefreshReg$(_c3, \"RoboCfg\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "SidebarContext", "PermissionGate", "LeftMenu", "Modal", "ModalAddCustomResponse", "ModalEditCustomResponse", "styled", "FaEdit", "FaTrash", "FaPlus", "FaSearch", "IoMdClose", "CryptoJS", "getCustomResponses", "addCustomResponse", "updateCustomResponse", "deleteCustomResponse", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON>e", "div", "sidebar", "_c", "Message", "props", "fromMe", "_c2", "RoboCfg", "_s", "secret<PERSON>ey", "userEncrypted", "localStorage", "getItem", "user", "AES", "decrypt", "toString", "enc", "Utf8", "userParse", "JSON", "parse", "userID", "_id", "empresa", "empresaParse", "idEmpresa", "id_empresa", "empresaObjId", "refresh", "setRefresh", "tela", "<PERSON><PERSON><PERSON>", "setSidebar", "companyResponses", "setCompanyResponses", "selectedResponse", "setSelectedResponse", "navigate", "fetchData", "empresaTemp", "getEmpresaWithObjId", "companyResponsesTemp", "getCompanyResponses", "console", "log", "data", "responses", "error", "debouncedUpdateQuestionActive", "debounce", "questionIdentifier", "isActive", "updateQuestionActive", "updatedResponses", "map", "response", "active", "handleBack", "handleSelectQuestionAndAnswer", "message", "handleResponseChange", "e", "prevSelectedResponse", "target", "value", "handleSaveResponse", "updateQuestionResponses", "autoClose", "type", "insertAtCursor", "textarea", "text", "startPos", "selectionStart", "endPos", "selectionEnd", "substring", "length", "handleInsertNomeCliente", "document", "querySelector", "handleInsertLinkCardapio", "handleInsertSaudacao", "children", "permissions", "className", "style", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "height", "borderBottom", "onClick", "max<PERSON><PERSON><PERSON>", "index", "questionType", "checked", "onChange", "backgroundImage", "backgroundWhatsApp", "question", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/roboConfig/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport './style.css';\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\nimport { Modal } from \"../../components/Modal\";\r\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\r\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\r\nimport styled from 'styled-components';\r\nimport { FaEdit, FaTrash, FaPlus, FaSearch } from \"react-icons/fa\";\r\nimport { IoMdClose } from \"react-icons/io\";\r\nimport CryptoJS from 'crypto-js';\r\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst Teste = styled.div`\r\n\r\n    display: flex;\r\n    margin-left:  ${({ sidebar }) => (sidebar ? '250px' : '100px')}; \r\n    height:auto;\r\n    width:auto;\r\n    transition: 150ms;\r\n    background-color:rgb(247,247,247)!important;\r\n    overflow: initial;\r\n    z-Index: 9;\r\n\r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\nconst Message = styled.div`\r\n  background: ${(props) => (props.fromMe ? '#dcf8c6' : '#ffffff')};\r\n  padding: 10px;\r\n  border-radius: 7.5px;\r\n  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);\r\n  font-size: 1rem;\r\n  margin: 1px 0px 1px ${(props) => (props.fromMe ? 'auto' : '0px')};\r\n  width: fit-content;\r\n  max-width: 60%;\r\n  margin-bottom: ${(props) => (props.fromMe ? '3px' : '10px')};\r\n  white-space: pre-wrap; /* Adiciona suporte para quebras de linha */\r\n  span {\r\n    font-size: 0.9rem;\r\n    color: rgba(0, 0, 0, 0.45);\r\n    display: block;\r\n    text-align: right;\r\n  }\r\n`;\r\n\r\nconst RoboCfg = () => {\r\n\r\n    const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\r\n    const userEncrypted = localStorage.getItem('user')\r\n    const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\r\n    const userParse = JSON.parse(user)\r\n    const userID = userParse._id;\r\n    const empresa = localStorage.getItem('empresa')\r\n    const empresaParse = JSON.parse(empresa)\r\n    const idEmpresa = empresaParse.id_empresa;\r\n    const empresaObjId = empresaParse._id;\r\n    \r\n    const [refresh, setRefresh] = useState(false);\r\n    const [tela, setTela] = useState(\"tela1\")\r\n    const { sidebar, setSidebar } = useContext(SidebarContext);; //TESTANDO CONST NO AppROUTES\r\n    const [companyResponses, setCompanyResponses] = useState([]);\r\n    const [selectedResponse, setSelectedResponse] = useState(null)\r\n    const navigate = useNavigate();\r\n\r\n    useEffect(() => {\r\n        // Função para buscar os dados do banco de dados\r\n        const fetchData = async () => {\r\n            try {\r\n                // Supondo que getEmpresaWithObjId seja uma função que retorna uma Promise\r\n                const empresaTemp = await getEmpresaWithObjId(empresaObjId);\r\n                const companyResponsesTemp = await getCompanyResponses(empresaObjId);\r\n                console.log(\"companyResponses:\", companyResponsesTemp.data.responses);\r\n                setCompanyResponses(companyResponsesTemp.data.responses);\r\n                setSelectedResponse(companyResponsesTemp.data.responses[0]);             \r\n            } catch (error) {\r\n                console.error(\"Erro ao buscar dados da empresa:\", error);\r\n                // Lidar com o erro, talvez definindo algum estado de erro na UI\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, [refresh]); // O array vazio [] indica que o useEffect será executado uma vez após a montagem do componente\r\n\r\n    const debouncedUpdateQuestionActive = debounce(async (empresaObjId, questionIdentifier, isActive, setCompanyResponses, companyResponses) => {\r\n        try {\r\n            // Atualiza no backend\r\n            await updateQuestionActive(empresaObjId, questionIdentifier, isActive);\r\n    \r\n            // Atualiza localmente após confirmação do backend\r\n            const updatedResponses = companyResponses.map((response) =>\r\n                response.questionIdentifier === questionIdentifier\r\n                    ? { ...response, active: isActive }\r\n                    : response\r\n            );\r\n            setCompanyResponses(updatedResponses);\r\n            console.log(\"Disponibilidade atualizada com sucesso.\");\r\n        } catch (error) {\r\n            console.error(\"Erro ao atualizar disponibilidade:\", error);\r\n        }\r\n    }, 300); // Aguarda 300ms após o último evento\r\n\r\n\r\n    const handleBack = () => {\r\n        //setSidebar(!sidebar)\r\n        navigate(\"/\");\r\n    }\r\n\r\n    const handleSelectQuestionAndAnswer = (message) => {\r\n        setSelectedResponse(message);\r\n    };\r\n\r\n    // Função para atualizar o response do selectedResponse diretamente\r\n    const handleResponseChange = (e) => {\r\n        setSelectedResponse((prevSelectedResponse) => ({\r\n            ...prevSelectedResponse,\r\n            response: e.target.value,\r\n        }));\r\n    };\r\n\r\n    // Função para salvar as alterações no companyResponses\r\n    const handleSaveResponse = async () => {\r\n        if (selectedResponse) {\r\n            // Atualiza a resposta localmente\r\n            const updatedResponses = companyResponses.map((response) =>\r\n                response.questionIdentifier === selectedResponse.questionIdentifier\r\n                    ? { ...response, response: selectedResponse.response } // Atualiza apenas a mensagem de resposta\r\n                    : response\r\n            );\r\n            setCompanyResponses(updatedResponses);\r\n\r\n            try {\r\n                // Chama a API para atualizar o backend\r\n                await updateQuestionResponses(empresaObjId, updatedResponses);\r\n                toast(\"Respostas atualizadas com sucesso!\", { autoClose: 2000, type: \"success\" });\r\n            } catch (error) {\r\n                console.error(\"Erro ao atualizar respostas:\", error);\r\n                toast(\"Ocorreu um erro ao salvar as respostas.\", { autoClose: 2000, type: \"error\" });\r\n            }\r\n        }\r\n    };\r\n\r\n\r\n    // Função genérica para inserir texto no cursor\r\n    const insertAtCursor = (textarea, text) => {\r\n        if (textarea) {\r\n            const startPos = textarea.selectionStart;\r\n            const endPos = textarea.selectionEnd;\r\n            const value = textarea.value;\r\n\r\n            // Atualiza o texto no campo com o novo valor\r\n            textarea.value = value.substring(0, startPos) + text + value.substring(endPos);\r\n\r\n            // Mantém o cursor após o texto inserido\r\n            textarea.selectionStart = textarea.selectionEnd = startPos + text.length;\r\n        }\r\n    };\r\n\r\n    // Funções específicas para cada botão\r\n    const handleInsertNomeCliente = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"{nome_cliente}\");\r\n    };\r\n\r\n    const handleInsertLinkCardapio = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"{link}\");\r\n    };\r\n\r\n    const handleInsertSaudacao = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"Agradecemos a preferência\");\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <PermissionGate permissions={['default']}>\r\n\r\n                <Teste sidebar={sidebar}>\r\n                    <div className=\"w-100 p-4\">\r\n                        {/*<form onSubmit={handleSubmitButton}>*/}\r\n                        <form /*onSubmit={formik.handleSubmit}*/ >\r\n\r\n                            <div className=\"form-header\" style={{ marginBottom: \"0px\" }}>\r\n                                <div className=\"title\">\r\n                                    <h1>Configurações do Robô</h1>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div style={{ display: \"flex\", justifyContent: \"right\"/*, height:\"80px\"*/ }}>\r\n\r\n                                {/*<div className=\"div-buttons\">\r\n                                    <div className=\"continue-button\">\r\n                                        {tela === \"tela1\" ?\r\n                                            <button type=\"button\" onClick={saveChanges} disabled={isSubmitting}>\r\n                                                <AiIcons.AiOutlineSave style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Salvar</a>\r\n                                            </button>\r\n                                            :\r\n                                            <button type=\"button\" disabled={isSubmitting}>\r\n                                                <AiIcons.AiOutlineSave style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Salvar</a>\r\n                                            </button>\r\n                                        }\r\n                                    </div>\r\n\r\n                                    <div className=\"back-button\">\r\n                                        <button onClick={handleBack}>\r\n                                            <SlIcons.SlActionUndo style={{ color: \"#ff4c4c\", marginRight: \"5px\", fontSize: \"18px\", marginBottom: \"2px\" }} /><a >Voltar</a>\r\n                                        </button>\r\n                                    </div>\r\n                                </div>*/}\r\n\r\n                            </div>\r\n\r\n                            <div className=\"contentItemComplete flex-column flex-md-row\">\r\n                                <div className=\"input-group inputGroup-etapasItem\" style={{ height: 50 }}>\r\n                                    <div className={tela === \"tela1\" ? \"etapasAddItem etapaAtiva\" : \"etapasAddItem\"}\r\n                                        style={{ borderBottom: '1px solid lightgray' }} onClick={() => setTela(\"tela1\")}\r\n                                    >\r\n                                        <label>1. Personalizar Mensagens</label>\r\n                                    </div>                                   \r\n                                </div>\r\n\r\n                                <div className=\"input-group mh-100\" style={{ maxWidth: \"80%\" }}>\r\n\r\n                                    <div className=\"formGroupRow\">\r\n                                        {tela === \"tela1\" &&\r\n                                            <div className=\"roboCfg-container\">\r\n                                                <div className=\"roboCfg-sidebar\">\r\n                                                    <h3>Mensagens</h3>\r\n                                                    <ul className=\"roboCfg-message-list\">\r\n                                                        {companyResponses.map((message, index) => (\r\n                                                            <li\r\n                                                                key={index}\r\n                                                                className={`roboCfg-message-item ${selectedResponse?.questionIdentifier === message.questionIdentifier ? 'roboCfg-selected' : ''}`}\r\n                                                                onClick={() => handleSelectQuestionAndAnswer(message)}\r\n                                                            >\r\n                                                                {message.questionType}\r\n                                                                <div className=\"checkBoxContentMsg\">\r\n                                                                    {message.active ? \r\n                                                                        <div className=\"roboCfg-edit-button-ativo\">Ativo</div> \r\n                                                                        : \r\n                                                                        <div className=\"roboCfg-edit-button-inativo\">Inativo</div>\r\n                                                                    }\r\n                                                                    <div className=\"wrapper\">\r\n                                                                        <div className=\"switch_box box_1\">\r\n                                                                            <input\r\n                                                                                type=\"checkbox\"\r\n                                                                                className=\"switch_1\"\r\n                                                                                checked={message.active || false}\r\n                                                                                // Dentro do checkbox\r\n                                                                                onChange={(e) => {\r\n                                                                                    const isActive = e.target.checked;\r\n                                                                                \r\n                                                                                    // Chamar a função debounce para evitar múltiplas chamadas ao backend\r\n                                                                                    debouncedUpdateQuestionActive(empresaObjId, message.questionIdentifier, isActive, setCompanyResponses, companyResponses);\r\n                                                                                }}\r\n                                                                            />\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </li>\r\n                                                        ))}\r\n                                                    </ul>\r\n                                                </div>\r\n                                                <div className=\"roboCfg-main\">\r\n                                                    <div className=\"roboCfg-preview\" style={{ backgroundImage: `url(${backgroundWhatsApp})` }}>\r\n                                                        <div className=\"roboCfg-chat\">\r\n\r\n                                                            {selectedResponse?.question &&\r\n                                                                <Message fromMe={false}>\r\n                                                                    <p>{selectedResponse?.question}</p>\r\n                                                                    <span>12:00</span>\r\n                                                                </Message>\r\n                                                            }\r\n\r\n\r\n                                                            <Message fromMe={true}>\r\n                                                                <p>{selectedResponse?.response}</p>\r\n                                                                <span>12:01</span>\r\n                                                            </Message>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div className=\"roboCfg-editor\">\r\n                                                        <h4>Personalize a mensagem</h4>\r\n                                                        <textarea\r\n                                                            className=\"roboCfg-textarea\"\r\n                                                            value={selectedResponse?.response || \"\"}\r\n                                                            onChange={handleResponseChange} // Altera diretamente o response do selectedResponse\r\n                                                        />\r\n                                                        <div className=\"roboCfg-buttons\">\r\n                                                            <button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertNomeCliente} // Chama a função para Nome do cliente\r\n                                                            >\r\n                                                                Nome do cliente\r\n                                                            </button>\r\n                                                            <button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertLinkCardapio} // Chama a função para Link do cardápio\r\n                                                            >\r\n                                                                Link do cardápio\r\n                                                            </button>\r\n                                                            {/*<button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertSaudacao} // Chama a função para Saudação\r\n                                                            >\r\n                                                                Saudação\r\n                                                            </button>*/}\r\n                                                        </div>\r\n\r\n                                                        <div className=\"roboCfg-save-cancel\">\r\n                                                            <button className=\"roboCfg-save-button\" type=\"button\" onClick={handleSaveResponse}>Salvar</button>\r\n                                                            {/*<button className=\"roboCfg-cancel-button\">Cancelar</button>*/}\r\n                                                        </div>\r\n\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        }\r\n                                        {/*tela === \"tela2\" && (\r\n                                            <div>NADA AQUI POR ENQUANTO</div>\r\n                                        )*/}\r\n                                    </div>\r\n\r\n                                </div>\r\n\r\n\r\n\r\n                            </div>\r\n\r\n                        </form>\r\n                    </div>\r\n                </Teste>\r\n\r\n            </PermissionGate>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default RoboCfg;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,aAAa;AACpB,SAASC,cAAc,QAAQ,iBAAiB;AAChD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,OAAOC,sBAAsB,MAAM,yCAAyC;AAC5E,OAAOC,uBAAuB,MAAM,0CAA0C;AAC9E,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAClE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,QAAQ,MAAM,WAAW;AAChC,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,oBAAoB;AACtH,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,KAAK,GAAGhB,MAAM,CAACiB,GAAG;AACxB;AACA;AACA,oBAAoB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAdIH,KAAK;AAgBX,MAAMI,OAAO,GAAGpB,MAAM,CAACiB,GAAG;AAC1B,gBAAiBI,KAAK,IAAMA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,SAAU;AACjE;AACA;AACA;AACA;AACA,wBAAyBD,KAAK,IAAMA,KAAK,CAACC,MAAM,GAAG,MAAM,GAAG,KAAM;AAClE;AACA;AACA,mBAAoBD,KAAK,IAAMA,KAAK,CAACC,MAAM,GAAG,KAAK,GAAG,MAAO;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAjBIH,OAAO;AAmBb,MAAMI,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAElB,MAAMC,SAAS,GAAG,uFAAuF;EACzG,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMC,IAAI,GAAGxB,QAAQ,CAACyB,GAAG,CAACC,OAAO,CAACL,aAAa,EAAED,SAAS,CAAC,CAACO,QAAQ,CAAC3B,QAAQ,CAAC4B,GAAG,CAACC,IAAI,CAAC;EACvF,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACR,IAAI,CAAC;EAClC,MAAMS,MAAM,GAAGH,SAAS,CAACI,GAAG;EAC5B,MAAMC,OAAO,GAAGb,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMa,YAAY,GAAGL,IAAI,CAACC,KAAK,CAACG,OAAO,CAAC;EACxC,MAAME,SAAS,GAAGD,YAAY,CAACE,UAAU;EACzC,MAAMC,YAAY,GAAGH,YAAY,CAACF,GAAG;EAErC,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0D,IAAI,EAAEC,OAAO,CAAC,GAAG3D,QAAQ,CAAC,OAAO,CAAC;EACzC,MAAM;IAAE4B,OAAO;IAAEgC;EAAW,CAAC,GAAG3D,UAAU,CAACG,cAAc,CAAC;EAAC,CAAC,CAAC;EAC7D,MAAM,CAACyD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC+D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAMiE,QAAQ,GAAG9D,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZ;IACA,MAAMgE,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACA;QACA,MAAMC,WAAW,GAAG,MAAMC,mBAAmB,CAACb,YAAY,CAAC;QAC3D,MAAMc,oBAAoB,GAAG,MAAMC,mBAAmB,CAACf,YAAY,CAAC;QACpEgB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEH,oBAAoB,CAACI,IAAI,CAACC,SAAS,CAAC;QACrEZ,mBAAmB,CAACO,oBAAoB,CAACI,IAAI,CAACC,SAAS,CAAC;QACxDV,mBAAmB,CAACK,oBAAoB,CAACI,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;MAC/D,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZJ,OAAO,CAACI,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD;MACJ;IACJ,CAAC;IAEDT,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,CAACV,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf,MAAMoB,6BAA6B,GAAGC,QAAQ,CAAC,OAAOtB,YAAY,EAAEuB,kBAAkB,EAAEC,QAAQ,EAAEjB,mBAAmB,EAAED,gBAAgB,KAAK;IACxI,IAAI;MACA;MACA,MAAMmB,oBAAoB,CAACzB,YAAY,EAAEuB,kBAAkB,EAAEC,QAAQ,CAAC;;MAEtE;MACA,MAAME,gBAAgB,GAAGpB,gBAAgB,CAACqB,GAAG,CAAEC,QAAQ,IACnDA,QAAQ,CAACL,kBAAkB,KAAKA,kBAAkB,GAC5C;QAAE,GAAGK,QAAQ;QAAEC,MAAM,EAAEL;MAAS,CAAC,GACjCI,QACV,CAAC;MACDrB,mBAAmB,CAACmB,gBAAgB,CAAC;MACrCV,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAC1D,CAAC,CAAC,OAAOG,KAAK,EAAE;MACZJ,OAAO,CAACI,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC9D;EACJ,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;EAGT,MAAMU,UAAU,GAAGA,CAAA,KAAM;IACrB;IACApB,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,MAAMqB,6BAA6B,GAAIC,OAAO,IAAK;IAC/CvB,mBAAmB,CAACuB,OAAO,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIC,CAAC,IAAK;IAChCzB,mBAAmB,CAAE0B,oBAAoB,KAAM;MAC3C,GAAGA,oBAAoB;MACvBP,QAAQ,EAAEM,CAAC,CAACE,MAAM,CAACC;IACvB,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI9B,gBAAgB,EAAE;MAClB;MACA,MAAMkB,gBAAgB,GAAGpB,gBAAgB,CAACqB,GAAG,CAAEC,QAAQ,IACnDA,QAAQ,CAACL,kBAAkB,KAAKf,gBAAgB,CAACe,kBAAkB,GAC7D;QAAE,GAAGK,QAAQ;QAAEA,QAAQ,EAAEpB,gBAAgB,CAACoB;MAAS,CAAC,CAAC;MAAA,EACrDA,QACV,CAAC;MACDrB,mBAAmB,CAACmB,gBAAgB,CAAC;MAErC,IAAI;QACA;QACA,MAAMa,uBAAuB,CAACvC,YAAY,EAAE0B,gBAAgB,CAAC;QAC7D5D,KAAK,CAAC,oCAAoC,EAAE;UAAE0E,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC;MACrF,CAAC,CAAC,OAAOrB,KAAK,EAAE;QACZJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDtD,KAAK,CAAC,yCAAyC,EAAE;UAAE0E,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAQ,CAAC,CAAC;MACxF;IACJ;EACJ,CAAC;;EAGD;EACA,MAAMC,cAAc,GAAGA,CAACC,QAAQ,EAAEC,IAAI,KAAK;IACvC,IAAID,QAAQ,EAAE;MACV,MAAME,QAAQ,GAAGF,QAAQ,CAACG,cAAc;MACxC,MAAMC,MAAM,GAAGJ,QAAQ,CAACK,YAAY;MACpC,MAAMX,KAAK,GAAGM,QAAQ,CAACN,KAAK;;MAE5B;MACAM,QAAQ,CAACN,KAAK,GAAGA,KAAK,CAACY,SAAS,CAAC,CAAC,EAAEJ,QAAQ,CAAC,GAAGD,IAAI,GAAGP,KAAK,CAACY,SAAS,CAACF,MAAM,CAAC;;MAE9E;MACAJ,QAAQ,CAACG,cAAc,GAAGH,QAAQ,CAACK,YAAY,GAAGH,QAAQ,GAAGD,IAAI,CAACM,MAAM;IAC5E;EACJ,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMR,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,gBAAgB,CAAC;EAC9C,CAAC;EAED,MAAMW,wBAAwB,GAAGA,CAAA,KAAM;IACnC,MAAMX,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,QAAQ,CAAC;EACtC,CAAC;EAED,MAAMY,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,MAAMZ,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,2BAA2B,CAAC;EACzD,CAAC;EAED,oBACI3E,OAAA,CAAAE,SAAA;IAAAsF,QAAA,eACIxF,OAAA,CAAClB,cAAc;MAAC2G,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAD,QAAA,eAErCxF,OAAA,CAACG,KAAK;QAACE,OAAO,EAAEA,OAAQ;QAAAmF,QAAA,eACpBxF,OAAA;UAAK0F,SAAS,EAAC,WAAW;UAAAF,QAAA,eAEtBxF,OAAA;YAAAwF,QAAA,gBAEIxF,OAAA;cAAK0F,SAAS,EAAC,aAAa;cAACC,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAJ,QAAA,eACxDxF,OAAA;gBAAK0F,SAAS,EAAC,OAAO;gBAAAF,QAAA,eAClBxF,OAAA;kBAAAwF,QAAA,EAAI;gBAAqB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENhG,OAAA;cAAK2F,KAAK,EAAE;gBAAEM,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,OAAO;cAAoB;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBvE,CAAC,eAENhG,OAAA;cAAK0F,SAAS,EAAC,6CAA6C;cAAAF,QAAA,gBACxDxF,OAAA;gBAAK0F,SAAS,EAAC,mCAAmC;gBAACC,KAAK,EAAE;kBAAEQ,MAAM,EAAE;gBAAG,CAAE;gBAAAX,QAAA,eACrExF,OAAA;kBAAK0F,SAAS,EAAEvD,IAAI,KAAK,OAAO,GAAG,0BAA0B,GAAG,eAAgB;kBAC5EwD,KAAK,EAAE;oBAAES,YAAY,EAAE;kBAAsB,CAAE;kBAACC,OAAO,EAAEA,CAAA,KAAMjE,OAAO,CAAC,OAAO,CAAE;kBAAAoD,QAAA,eAEhFxF,OAAA;oBAAAwF,QAAA,EAAO;kBAAyB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENhG,OAAA;gBAAK0F,SAAS,EAAC,oBAAoB;gBAACC,KAAK,EAAE;kBAAEW,QAAQ,EAAE;gBAAM,CAAE;gBAAAd,QAAA,eAE3DxF,OAAA;kBAAK0F,SAAS,EAAC,cAAc;kBAAAF,QAAA,EACxBrD,IAAI,KAAK,OAAO,iBACbnC,OAAA;oBAAK0F,SAAS,EAAC,mBAAmB;oBAAAF,QAAA,gBAC9BxF,OAAA;sBAAK0F,SAAS,EAAC,iBAAiB;sBAAAF,QAAA,gBAC5BxF,OAAA;wBAAAwF,QAAA,EAAI;sBAAS;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClBhG,OAAA;wBAAI0F,SAAS,EAAC,sBAAsB;wBAAAF,QAAA,EAC/BlD,gBAAgB,CAACqB,GAAG,CAAC,CAACK,OAAO,EAAEuC,KAAK,kBACjCvG,OAAA;0BAEI0F,SAAS,EAAE,wBAAwB,CAAAlD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEe,kBAAkB,MAAKS,OAAO,CAACT,kBAAkB,GAAG,kBAAkB,GAAG,EAAE,EAAG;0BACnI8C,OAAO,EAAEA,CAAA,KAAMtC,6BAA6B,CAACC,OAAO,CAAE;0BAAAwB,QAAA,GAErDxB,OAAO,CAACwC,YAAY,eACrBxG,OAAA;4BAAK0F,SAAS,EAAC,oBAAoB;4BAAAF,QAAA,GAC9BxB,OAAO,CAACH,MAAM,gBACX7D,OAAA;8BAAK0F,SAAS,EAAC,2BAA2B;8BAAAF,QAAA,EAAC;4BAAK;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,gBAEtDhG,OAAA;8BAAK0F,SAAS,EAAC,6BAA6B;8BAAAF,QAAA,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAE9DhG,OAAA;8BAAK0F,SAAS,EAAC,SAAS;8BAAAF,QAAA,eACpBxF,OAAA;gCAAK0F,SAAS,EAAC,kBAAkB;gCAAAF,QAAA,eAC7BxF,OAAA;kCACIyE,IAAI,EAAC,UAAU;kCACfiB,SAAS,EAAC,UAAU;kCACpBe,OAAO,EAAEzC,OAAO,CAACH,MAAM,IAAI;kCAC3B;kCAAA;kCACA6C,QAAQ,EAAGxC,CAAC,IAAK;oCACb,MAAMV,QAAQ,GAAGU,CAAC,CAACE,MAAM,CAACqC,OAAO;;oCAEjC;oCACApD,6BAA6B,CAACrB,YAAY,EAAEgC,OAAO,CAACT,kBAAkB,EAAEC,QAAQ,EAAEjB,mBAAmB,EAAED,gBAAgB,CAAC;kCAC5H;gCAAE;kCAAAuD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACL;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACD;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC;wBAAA,GA3BDO,KAAK;0BAAAV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA4BV,CACP;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNhG,OAAA;sBAAK0F,SAAS,EAAC,cAAc;sBAAAF,QAAA,gBACzBxF,OAAA;wBAAK0F,SAAS,EAAC,iBAAiB;wBAACC,KAAK,EAAE;0BAAEgB,eAAe,EAAE,OAAOC,kBAAkB;wBAAI,CAAE;wBAAApB,QAAA,eACtFxF,OAAA;0BAAK0F,SAAS,EAAC,cAAc;0BAAAF,QAAA,GAExB,CAAAhD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqE,QAAQ,kBACvB7G,OAAA,CAACO,OAAO;4BAACE,MAAM,EAAE,KAAM;4BAAA+E,QAAA,gBACnBxF,OAAA;8BAAAwF,QAAA,EAAIhD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqE;4BAAQ;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnChG,OAAA;8BAAAwF,QAAA,EAAM;4BAAK;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC,eAIdhG,OAAA,CAACO,OAAO;4BAACE,MAAM,EAAE,IAAK;4BAAA+E,QAAA,gBAClBxF,OAAA;8BAAAwF,QAAA,EAAIhD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoB;4BAAQ;8BAAAiC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnChG,OAAA;8BAAAwF,QAAA,EAAM;4BAAK;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNhG,OAAA;wBAAK0F,SAAS,EAAC,gBAAgB;wBAAAF,QAAA,gBAC3BxF,OAAA;0BAAAwF,QAAA,EAAI;wBAAsB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC/BhG,OAAA;0BACI0F,SAAS,EAAC,kBAAkB;0BAC5BrB,KAAK,EAAE,CAAA7B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoB,QAAQ,KAAI,EAAG;0BACxC8C,QAAQ,EAAEzC,oBAAqB,CAAC;wBAAA;0BAAA4B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC,CAAC,eACFhG,OAAA;0BAAK0F,SAAS,EAAC,iBAAiB;0BAAAF,QAAA,gBAC5BxF,OAAA;4BACI0F,SAAS,EAAC,gBAAgB;4BAC1BjB,IAAI,EAAC,QAAQ;4BACb4B,OAAO,EAAElB,uBAAwB,CAAC;4BAAA;4BAAAK,QAAA,EACrC;0BAED;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACThG,OAAA;4BACI0F,SAAS,EAAC,gBAAgB;4BAC1BjB,IAAI,EAAC,QAAQ;4BACb4B,OAAO,EAAEf,wBAAyB,CAAC;4BAAA;4BAAAE,QAAA,EACtC;0BAED;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAQR,CAAC,eAENhG,OAAA;0BAAK0F,SAAS,EAAC,qBAAqB;0BAAAF,QAAA,eAChCxF,OAAA;4BAAQ0F,SAAS,EAAC,qBAAqB;4BAACjB,IAAI,EAAC,QAAQ;4BAAC4B,OAAO,EAAE/B,kBAAmB;4BAAAkB,QAAA,EAAC;0BAAM;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEjG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEI;EAAC,gBACnB,CAAC;AAEX,CAAC;AAAApF,EAAA,CAtSKD,OAAO;EAAA,QAiBQ/B,WAAW;AAAA;AAAAkI,GAAA,GAjB1BnG,OAAO;AAwSb,eAAeA,OAAO;AAAC,IAAAL,EAAA,EAAAI,GAAA,EAAAoG,GAAA;AAAAC,YAAA,CAAAzG,EAAA;AAAAyG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}