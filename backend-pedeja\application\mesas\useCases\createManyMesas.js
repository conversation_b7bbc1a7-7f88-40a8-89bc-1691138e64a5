class CreateManyMesasUseCase {
    constructor(mesasDAO){
        this.mesasDAO = mesasDAO
    }

    async execute({ url, empresa_id, quantity }) {
        try {
            let status = 'free';

            let mesasDone = [];
            for(let i = 1; i <= quantity; i++){
                let number = 1;

                const lastMesa = await this.mesasDAO.findLastNumber({ empresa_id });
                if(lastMesa){
                    number = Number(lastMesa.number) + 1;
                }

                let name = `Mesa ${number}`;
                let qr_code = `${url}?qrcode=${name.replace(' ', '_')}`;

                mesasDone.push(await this.mesasDAO.createMesas({ qr_code, empresa_id, name, status, number, total: 0, total_payed: 0 }))

            }

            return mesasDone;
        } catch(error){
            console.error(error);
            return {
                status: false,
                message: "Error on get mesas"
            }
        }
    }
}

module.exports = CreateManyMesasUseCase;