{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\components\\\\LeftMenu\\\\SidebarData.jsx\";\nimport React from \"react\";\nimport * as FaIcons from \"react-icons/fa\";\nimport * as AiIcons from \"react-icons/ai\";\nimport * as IoIcons from \"react-icons/io\";\nimport * as RiIcons from \"react-icons/ri\";\nimport * as HiIcons from \"react-icons/hi\";\nimport * as IoiosIcons from \"react-icons/io\";\nimport { BsFillMenuButtonFill } from \"react-icons/bs\";\nimport { HiOutlineSquaresPlus } from \"react-icons/hi2\";\nimport { CiBoxList } from \"react-icons/ci\";\nimport { CgReorder } from \"react-icons/cg\";\nimport { MdPointOfSale } from \"react-icons/md\";\nimport { MdOutlinePlaylistAdd } from \"react-icons/md\";\nimport { BsFillGearFill } from \"react-icons/bs\";\nimport { RiWhatsappLine } from \"react-icons/ri\";\nimport { RiAdminFill } from \"react-icons/ri\";\nimport { RiFileAddFill } from \"react-icons/ri\";\nimport { RiBarChartLine } from \"react-icons/ri\";\nimport { AiFillPrinter } from \"react-icons/ai\";\nimport { FaArrowTrendUp } from \"react-icons/fa6\";\nimport { FaArrowRightToCity } from \"react-icons/fa6\";\nimport { TbReportAnalytics, TbTruckDelivery } from \"react-icons/tb\";\nimport { PiCashRegister } from \"react-icons/pi\";\nimport { LuImport } from \"react-icons/lu\";\nimport { BsRobot } from \"react-icons/bs\";\nimport { MdTableBar } from \"react-icons/md\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst IconMeusPedidos = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  id: \"line_gradient\",\n  height: 20,\n  viewBox: \"0 0 64 64\",\n  width: 20,\n  xmlns: \"http://www.w3.org/2000/svg\",\n  xmlnsXlink: \"http://www.w3.org/1999/xlink\",\n  \"data-name\": \"line gradient\",\n  children: [/*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"linear-gradient\",\n    gradientUnits: \"userSpaceOnUse\",\n    x1: \"5.108\",\n    x2: \"58.889\",\n    y1: 32,\n    y2: 32,\n    children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n      offset: 0,\n      stopColor: \"#096fe0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n      offset: \".15843\",\n      stopColor: \"#167ce3\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n      offset: 1,\n      stopColor: \"#5ec3f6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"m17.00192 3h-8.78335c-1.71338 0-3.10714 1.39376-3.10714 3.10714-.08269 1.76723 1.33236 3.27198 3.10715 3.26694h8.78333c4.11772-.09088 4.12111-6.28248 0-6.37409zm1.03571 3.26695c0 .57146-.46476 1.03571-1.03571 1.03571h-8.78335c-1.37751.0017-1.3808-2.23264.00004-2.23123-.00004 0 8.7833 0 8.7833 0 .64043-.00438 1.10241.57244 1.03571 1.19552zm1.03571 5.33737h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30266h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30165h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30266h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm18.35409-55.92857h-8.78334c-1.71338 0-3.10714 1.39376-3.10714 3.10714-.08269 1.76723 1.33236 3.27198 3.10715 3.26694h8.78333c4.11772-.09088 4.12111-6.28248 0-6.37409zm1.03571 3.26695c0 .57146-.46425 1.03571-1.03571 1.03571h-8.78334c-1.37751.0017-1.3808-2.23264.00004-2.23123-.00004 0 8.7833 0 8.7833 0 .64097-.00438 1.10233.57242 1.03571 1.19552zm1.03571 5.33737h-12.9262c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30266h-12.9262c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm18.3546-30.11562h-8.78435c-1.71338 0-3.10714 1.39376-3.10714 3.10714-.08269 1.76723 1.33236 3.27198 3.10715 3.26694h8.78434c4.11772-.09088 4.12111-6.28248 0-6.37409zm1.03571 3.26695c0 .57146-.46425 1.03571-1.03571 1.03571h-8.78435c-1.37764.00192-1.38091-2.23286.00004-2.23123-.00004 0 8.78431 0 8.78431 0 .64097-.00438 1.10233.57242 1.03571 1.19552zm1.03571 5.33737h-12.92721c-.57247 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46324 1.03571 1.03571 1.03571h12.92721c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85578v-6.53289h10.85578zm1.03571 4.30266h-12.92721c-.57247 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46324 1.03571 1.03571 1.03571h12.92721c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85578v-6.53289h10.85578zm1.03571 4.30165h-12.92721c-.57247 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46324 1.03571 1.03571 1.03571h12.92721c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85578v-6.53289h10.85578z\",\n    fill: \"url(#linear-gradient)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 30,\n  columnNumber: 3\n}, this);\n_c = IconMeusPedidos;\nconst IconWhatsapp = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  height: 20,\n  viewBox: \"0 0 512 512\",\n  width: 20,\n  xmlns: \"http://www.w3.org/2000/svg\",\n  xmlnsXlink: \"http://www.w3.org/1999/xlink\",\n  children: [/*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"a\",\n    children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n      offset: 0,\n      stopColor: \"#2af598\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n      offset: 1,\n      stopColor: \"#009efd\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"b\",\n    gradientUnits: \"userSpaceOnUse\",\n    x1: \"255.7523048184\",\n    x2: \"255.7523048184\",\n    xlinkHref: \"#a\",\n    y1: 0,\n    y2: \"512.0000297334\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"c\",\n    gradientUnits: \"userSpaceOnUse\",\n    x1: \"257.750272915\",\n    x2: \"257.750272915\",\n    xlinkHref: \"#a\",\n    y1: \"141.862828\",\n    y2: \"361.650308\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"m435.921875 74.351562c-48.097656-47.917968-112.082031-74.3242182-180.179687-74.351562-67.945313 0-132.03125 26.382812-180.445313 74.289062-48.5 47.988282-75.234375 111.761719-75.296875 179.339844v.078125.046875c.0078125 40.902344 10.753906 82.164063 31.152344 119.828125l-30.453125 138.417969 140.011719-31.847656c35.460937 17.871094 75.027343 27.292968 114.933593 27.308594h.101563c67.933594 0 132.019531-26.386719 180.441406-74.296876 48.542969-48.027343 75.289062-111.71875 75.320312-179.339843.019532-67.144531-26.820312-130.882813-75.585937-179.472657zm-180.179687 393.148438h-.089844c-35.832032-.015625-71.335938-9.011719-102.667969-26.023438l-6.621094-3.59375-93.101562 21.175782 20.222656-91.90625-3.898437-6.722656c-19.382813-33.425782-29.625-70.324219-29.625-106.71875.074218-117.800782 96.863281-213.75 215.773437-213.75 57.445313.023437 111.421875 22.292968 151.984375 62.699218 41.175781 41.03125 63.84375 94.710938 63.824219 151.152344-.046875 117.828125-96.855469 213.6875-215.800781 213.6875zm0 0\",\n    fill: \"url(#b)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"m186.152344 141.863281h-11.210938c-3.902344 0-10.238281 1.460938-15.597656 7.292969-5.363281 5.835938-20.476562 19.941406-20.476562 48.628906s20.964843 56.40625 23.886718 60.300782c2.925782 3.890624 40.46875 64.640624 99.929688 88.011718 49.417968 19.421875 59.476562 15.558594 70.199218 14.585938 10.726563-.96875 34.613282-14.101563 39.488282-27.714844s4.875-25.285156 3.414062-27.722656c-1.464844-2.429688-5.367187-3.886719-11.214844-6.800782-5.851562-2.917968-34.523437-17.261718-39.886718-19.210937-5.363282-1.941406-9.261719-2.914063-13.164063 2.925781-3.902343 5.828125-15.390625 19.3125-18.804687 23.203125-3.410156 3.894531-6.824219 4.382813-12.675782 1.464844-5.851562-2.925781-24.5-9.191406-46.847656-29.050781-17.394531-15.457032-29.464844-35.167969-32.878906-41.003906-3.410156-5.832032-.363281-8.988282 2.570312-11.898438 2.628907-2.609375 6.179688-6.179688 9.105469-9.582031 2.921875-3.40625 3.753907-5.835938 5.707031-9.726563 1.949219-3.890625.972657-7.296875-.488281-10.210937-1.464843-2.917969-12.691406-31.75-17.894531-43.28125h.003906c-4.382812-9.710938-8.996094-10.039063-13.164062-10.210938zm0 0\",\n    fill: \"url(#c)\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 59,\n  columnNumber: 3\n}, this);\n_c2 = IconWhatsapp;\nconst IconPDV = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  height: 20,\n  viewBox: \"0 0 64 64\",\n  width: 20,\n  xmlns: \"http://www.w3.org/2000/svg\",\n  xmlnsXlink: \"http://www.w3.org/1999/xlink\",\n  children: [/*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"New_Gradient_Swatch_3\",\n    gradientUnits: \"userSpaceOnUse\",\n    x1: \"42.43\",\n    x2: \"42.43\",\n    y1: \"65.857\",\n    y2: \"-4.017\",\n    children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n      offset: 0,\n      stopColor: \"#006df0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n      offset: 1,\n      stopColor: \"#00e7f0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"New_Gradient_Swatch_3-2\",\n    x1: \"51.829\",\n    x2: \"51.829\",\n    xlinkHref: \"#New_Gradient_Swatch_3\",\n    y1: \"65.857\",\n    y2: \"-4.017\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"New_Gradient_Swatch_3-3\",\n    x1: 49,\n    x2: 49,\n    xlinkHref: \"#New_Gradient_Swatch_3\",\n    y1: \"65.857\",\n    y2: \"-4.017\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"New_Gradient_Swatch_3-4\",\n    x1: 32,\n    x2: 32,\n    xlinkHref: \"#New_Gradient_Swatch_3\",\n    y1: \"65.857\",\n    y2: \"-4.017\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"New_Gradient_Swatch_3-7\",\n    x1: 29,\n    x2: 29,\n    xlinkHref: \"#New_Gradient_Swatch_3\",\n    y1: \"65.857\",\n    y2: \"-4.017\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"New_Gradient_Swatch_3-8\",\n    x1: 22,\n    x2: 22,\n    xlinkHref: \"#New_Gradient_Swatch_3\",\n    y1: \"65.857\",\n    y2: \"-4.017\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n    id: \"New_Gradient_Swatch_3-9\",\n    x1: 31,\n    x2: 31,\n    xlinkHref: \"#New_Gradient_Swatch_3\",\n    y1: \"65.857\",\n    y2: \"-4.017\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 158,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n    id: \"mobilephone_food_delivery_package_shopping\",\n    \"data-name\": \"mobilephone, food, delivery, package, shopping\",\n    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"m61.73 38.42a3.208 3.208 0 0 0 -.27-.65 3.011 3.011 0 0 0 -4.1-1.1l-4.8 2.78a2.962 2.962 0 0 0 -2.56-1.45h-7.76l-1.37-.68a2.971 2.971 0 0 0 -1.34-.32h-4.86a2.974 2.974 0 0 0 -1.8.6l-1.87 1.4v-1a1 1 0 0 0 -1-1h-5v-3a1 1 0 0 0 -2 0v20a1 1 0 0 0 2 0v-3h5a1 1 0 0 0 1-1v-1.13l.94.63a2.979 2.979 0 0 0 1.67.5h12.85a2.936 2.936 0 0 0 1.53-.42l12.37-7.71a3.023 3.023 0 0 0 1.5-2.61 2.842 2.842 0 0 0 -.13-.84zm-32.73 10.58h-4v-10h4zm30.33-8.85-12.38 7.72a.922.922 0 0 1 -.49.13h-12.85a1.023 1.023 0 0 1 -.56-.17l-2.05-1.37v-4.96l3.07-2.3a.984.984 0 0 1 .6-.2h4.86a.956.956 0 0 1 .44.11l1.58.78a1 1 0 0 0 .45.11h8a1 1 0 0 1 .87.51 1.065 1.065 0 0 1 .13.53.956.956 0 0 1 -.31.67 1.008 1.008 0 0 1 -.69.29h-7a1 1 0 0 0 0 2s7.25 0 7.31-.02a2.958 2.958 0 0 0 1.8-.85 2.92 2.92 0 0 0 .83-1.6l5.43-3.13a.984.984 0 0 1 1.35.37 1.121 1.121 0 0 1 .1.22.884.884 0 0 1 .04.27 1.014 1.014 0 0 1 -.53.89z\",\n      fill: \"url(#New_Gradient_Swatch_3)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"m48 21a1 1 0 0 0 1 1 5.013 5.013 0 0 1 4.715 3.334 1 1 0 0 0 1.885-.668 7.017 7.017 0 0 0 -6.6-4.666 1 1 0 0 0 -1 1z\",\n      fill: \"url(#New_Gradient_Swatch_3-2)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"m46 16a2.959 2.959 0 0 0 .346 1.369 10.009 10.009 0 0 0 -7.346 9.631v.184a2.988 2.988 0 0 0 .343 5.738l1.762 3.525a1 1 0 0 0 .895.553h14a1 1 0 0 0 .9-.553l1.762-3.525a2.988 2.988 0 0 0 .338-5.738v-.184a10.009 10.009 0 0 0 -7.346-9.631 2.959 2.959 0 0 0 .346-1.369 3 3 0 0 0 -6 0zm9.382 19h-12.764l-1-2h14.764zm2.618-4h-18a1 1 0 0 1 0-2h18a1 1 0 0 1 0 2zm-1-4h-16a8 8 0 0 1 16 0zm-7-11a1 1 0 1 1 -1-1 1 1 0 0 1 1 1z\",\n      fill: \"url(#New_Gradient_Swatch_3-3)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n      fill: \"url(#New_Gradient_Swatch_3-4)\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"m50 50a1 1 0 0 0 -1 1v6a3 3 0 0 1 -3 3h-28a3 3 0 0 1 -3-3v-50a3 3 0 0 1 3-3h28a3 3 0 0 1 3 3v4a1 1 0 0 0 2 0v-4a5.006 5.006 0 0 0 -5-5h-28a5.006 5.006 0 0 0 -5 5v50a5.006 5.006 0 0 0 5 5h28a5.006 5.006 0 0 0 5-5v-6a1 1 0 0 0 -1-1z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"m28.24 7.552-.816-1.213a2.98 2.98 0 0 0 -2.494-1.339h-5.93a3 3 0 0 0 -3 3v48a3 3 0 0 0 3 3h26a3 3 0 0 0 3-3v-4a1 1 0 0 0 -2 0v4a1 1 0 0 1 -1 1h-26a1 1 0 0 1 -1-1v-48a1 1 0 0 1 1-1h5.93a.99.99 0 0 1 .83.448l.816 1.213a2.979 2.979 0 0 0 2.494 1.339h5.86a2.978 2.978 0 0 0 2.49-1.332l.824-1.227a.988.988 0 0 1 .826-.441h5.93a1 1 0 0 1 1 1v3a1 1 0 0 0 2 0v-3a3 3 0 0 0 -3-3h-5.93a2.977 2.977 0 0 0 -2.49 1.332l-.824 1.227a.988.988 0 0 1 -.826.441h-5.86a.99.99 0 0 1 -.83-.448z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"m33 7a1 1 0 0 0 0-2h-2a1 1 0 0 0 0 2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"m38 21v-8a1 1 0 0 0 -1-1h-16a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1zm-2-1h-14v-6h14z\",\n      fill: \"url(#New_Gradient_Swatch_3-7)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"m24 29a1 1 0 0 0 -1-1h-2a1 1 0 0 0 0 2h2a1 1 0 0 0 1-1z\",\n      fill: \"url(#New_Gradient_Swatch_3-8)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"m27 28a1 1 0 0 0 0 2h8a1 1 0 0 0 0-2z\",\n      fill: \"url(#New_Gradient_Swatch_3-9)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"m23 24h-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2z\",\n      fill: \"url(#New_Gradient_Swatch_3-8)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"m38 25a1 1 0 0 0 -1-1h-10a1 1 0 0 0 0 2h10a1 1 0 0 0 1-1z\",\n      fill: \"url(#New_Gradient_Swatch_3-4)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 100,\n  columnNumber: 3\n}, this);\n_c3 = IconPDV;\nexport const SidebarData = [{\n  title: \"Meus Pedidos\",\n  path: \"/\",\n  permission: \"default\",\n  icon: /*#__PURE__*/_jsxDEV(IconMeusPedidos, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 7\n  }, this)\n}, {\n  title: \"WhatsApp\",\n  path: \"/whatsapp\",\n  permission: \"default\",\n  icon: /*#__PURE__*/_jsxDEV(IconWhatsapp, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 227,\n    columnNumber: 7\n  }, this)\n}, {\n  title: \"Pedidos Balcão\",\n  path: \"/pdv\",\n  permission: \"default\",\n  icon: /*#__PURE__*/_jsxDEV(IconPDV, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 234,\n    columnNumber: 11\n  }, this)\n}, {\n  title: \"Salão\",\n  permission: \"default\",\n  icon: /*#__PURE__*/_jsxDEV(MdTableBar, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 11\n  }, this),\n  subNav: [{\n    title: \"Mesas\",\n    path: \"/pedidos-mesas\",\n    icon: /*#__PURE__*/_jsxDEV(MdTableBar, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 15\n    }, this),\n    cName: \"sub-nav\"\n  }, {\n    title: \"Garçons\",\n    path: \"/mesas-garcons\",\n    icon: /*#__PURE__*/_jsxDEV(FaIcons.FaUsers, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 11\n    }, this),\n    cName: \"sub-nav\"\n  }]\n}, {\n  title: \"Caixa\",\n  path: \"/caixa\",\n  permission: \"default\",\n  icon: /*#__PURE__*/_jsxDEV(PiCashRegister, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 264,\n    columnNumber: 7\n  }, this)\n}, {\n  title: \"Cardápio\",\n  //path: '/list-empresa',\n  permission: \"default\",\n  icon: /*#__PURE__*/_jsxDEV(BsFillMenuButtonFill, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 7\n  }, this),\n  iconClosed: /*#__PURE__*/_jsxDEV(RiIcons.RiArrowDownSFill, {\n    style: {\n      marginLeft: \"-5px\",\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 277,\n    columnNumber: 7\n  }, this),\n  iconOpened: /*#__PURE__*/_jsxDEV(RiIcons.RiArrowUpSFill, {\n    style: {\n      marginLeft: \"-5px\",\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 7\n  }, this),\n  subNav: [{\n    title: \"Gestor de Cardápio\",\n    path: \"/list-categoria\",\n    icon: /*#__PURE__*/_jsxDEV(CiBoxList, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 11\n    }, this),\n    cName: \"sub-nav\"\n  },\n  /*{\r\n    title: \"Itens\",\r\n    path: \"/list-item\",\r\n    icon: (\r\n      <CiBoxList style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }} />\r\n    ),\r\n    cName: \"sub-nav\",\r\n  },*/\n  {\n    title: \"Gerenciar complementos\",\n    path: \"/list-adicional\",\n    icon: /*#__PURE__*/_jsxDEV(MdOutlinePlaylistAdd, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 11\n    }, this),\n    cName: \"sub-nav\"\n  }]\n}, {\n  title: \"Relatórios\",\n  //path: '/list-empresa',\n  permission: \"default\",\n  icon: /*#__PURE__*/_jsxDEV(TbReportAnalytics, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 331,\n    columnNumber: 7\n  }, this),\n  iconClosed: /*#__PURE__*/_jsxDEV(RiIcons.RiArrowDownSFill, {\n    style: {\n      marginLeft: \"-5px\",\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 336,\n    columnNumber: 7\n  }, this),\n  iconOpened: /*#__PURE__*/_jsxDEV(RiIcons.RiArrowUpSFill, {\n    style: {\n      marginLeft: \"-5px\",\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 345,\n    columnNumber: 7\n  }, this),\n  subNav: [{\n    title: \"Geral\",\n    path: \"/relatorio-geral\",\n    icon: /*#__PURE__*/_jsxDEV(RiBarChartLine, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 11\n    }, this),\n    cName: \"sub-nav\"\n  }, {\n    title: \"Meu desempenho\",\n    path: \"/desempenho\",\n    icon: /*#__PURE__*/_jsxDEV(FaArrowTrendUp, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 11\n    }, this),\n    cName: \"sub-nav\"\n  }\n  /*{\r\n    title: 'Itens',\r\n    path: '/list-item',\r\n    icon: <CiBoxList style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    cName: 'sub-nav'\r\n  },\r\n  {\r\n    title: 'Adicionais',\r\n    path: '/list-adicional',\r\n    icon: <MdOutlinePlaylistAdd style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    cName: 'sub-nav'\r\n  }*/]\n},\n//{\n//  title: \"Empresas\",\n//  path: \"/list-empresa\",\n//  permission: \"viewIndustry\",\n//  icon: (\n//    <IoiosIcons.IoIosBusiness\n//      style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\n//    />\n//  ),\n// iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n// iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n\n// subNav: [\n//   {\n//     title: 'Cadastrar Indústria',\n//     path: '/empresa',\n//     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n//     cName: 'sub-nav'\n//   },\n//   {\n//     title: 'Listar Indústrias',\n//     path: '/list-empresa',\n//     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n//     cName: 'sub-nav'\n//   }\n// ]\n//},\n//{\n//  title: \"Usuários\",\n//  path: \"/list-users\",\n//  permission: \"viewUser\",\n//  icon: (\n//    <FaIcons.FaUsers\n//      style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\n//    />\n//  ),\n// iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n// iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n\n// subNav: [\n//   {\n//     title: 'Cadastrar Usuário',\n//     path: '/cadastro-user',\n//     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n//     cName: 'sub-nav'\n//   },\n//   {\n//     title: 'Listar Usuários',\n//     path: '/list-users',\n//     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n//     cName: 'sub-nav'\n//   }\n// ]\n//},\n{\n  title: \"Entregadores\",\n  path: \"/list-entregadores\",\n  permission: \"default\",\n  icon: /*#__PURE__*/_jsxDEV(TbTruckDelivery, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 448,\n    columnNumber: 7\n  }, this)\n  // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n  // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n\n  // subNav: [\n  //   {\n  //     title: 'Cadastrar Usuário',\n  //     path: '/cadastro-user',\n  //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n  //     cName: 'sub-nav'\n  //   },\n  //   {\n  //     title: 'Listar Usuários',\n  //     path: '/list-users',\n  //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n  //     cName: 'sub-nav'\n  //   }\n  // ]\n}, {\n  title: \"Minha Conta\",\n  //path: '/list-users',\n  permission: \"default\",\n  icon: /*#__PURE__*/_jsxDEV(FaIcons.FaRegUser, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 475,\n    columnNumber: 7\n  }, this),\n  iconClosed: /*#__PURE__*/_jsxDEV(RiIcons.RiArrowDownSFill, {\n    style: {\n      marginLeft: \"-5px\",\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 480,\n    columnNumber: 7\n  }, this),\n  iconOpened: /*#__PURE__*/_jsxDEV(RiIcons.RiArrowUpSFill, {\n    style: {\n      marginLeft: \"-5px\",\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 489,\n    columnNumber: 7\n  }, this),\n  subNav: [{\n    title: \"Planos\",\n    path: \"/planos\",\n    icon: /*#__PURE__*/_jsxDEV(IoIcons.IoIosPaper, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 11\n    }, this),\n    cName: \"sub-nav\"\n  }]\n}, {\n  title: \"Clientes\",\n  path: \"/list-cliente\",\n  icon: /*#__PURE__*/_jsxDEV(IoiosIcons.IoIosContacts, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 515,\n    columnNumber: 7\n  }, this),\n  permission: \"viewCVO\"\n  // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n  // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n\n  // subNav: [\n  //   {\n  //     title: 'Cadastrar Cliente',\n  //     path: '/',\n  //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\n  //   },\n  //   {\n  //     title: 'Listar Clientes',\n  //     path: '/',\n  //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\n  //   }\n  // ]\n}, {\n  title: \"Vendedores\",\n  path: \"/list-vendedor\",\n  icon: /*#__PURE__*/_jsxDEV(IoIcons.IoMdPeople, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 540,\n    columnNumber: 7\n  }, this),\n  permission: \"viewCVO\"\n  // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n  // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n\n  // subNav: [\n  //   {\n  //     title: 'Cadastrar Vendedor',\n  //     path: '/',\n  //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\n  //   },\n  //   {\n  //     title: 'Listar Vendedores',\n  //     path: '/',\n  //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\n  //   }\n  // ]\n}, {\n  title: \"Orçamentos\",\n  path: \"/list-orcamento\",\n  icon: /*#__PURE__*/_jsxDEV(FaIcons.FaEnvelopeOpenText, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 565,\n    columnNumber: 7\n  }, this),\n  permission: \"viewCVO\"\n  // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n  // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\n\n  // subNav: [\n  //   {\n  //     title: 'Cadastrar Orçamento',\n  //     path: '/',\n  //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\n  //   },\n  //   {\n  //     title: 'Listar Orçamentos',\n  //     path: '/',\n  //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\n  //   }\n  // ]\n}, {\n  title: \"Configurações\",\n  //path: '/configuracoes',\n  icon: /*#__PURE__*/_jsxDEV(BsFillGearFill, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 590,\n    columnNumber: 7\n  }, this),\n  permission: \"default\",\n  iconClosed: /*#__PURE__*/_jsxDEV(RiIcons.RiArrowDownSFill, {\n    style: {\n      marginLeft: \"-5px\",\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 594,\n    columnNumber: 7\n  }, this),\n  iconOpened: /*#__PURE__*/_jsxDEV(RiIcons.RiArrowUpSFill, {\n    style: {\n      marginLeft: \"-5px\",\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 603,\n    columnNumber: 7\n  }, this),\n  subNav: [{\n    title: \"Endereço\",\n    path: \"/endereco-estabelecimento\",\n    icon: /*#__PURE__*/_jsxDEV(FaIcons.FaMapMarkerAlt, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Regiões de entrega\",\n    path: \"/raio-entrega\",\n    icon: /*#__PURE__*/_jsxDEV(FaIcons.FaMapMarked, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 11\n    }, this)\n  },\n  /*\r\n  {\r\n  title: \"Sincronizar Whatsapp\",\r\n  path: \"/whatsapp-sync\",\r\n  icon: (\r\n    <FaIcons.FaWhatsapp\r\n      style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n    />\r\n  ),\r\n  }*/\n  {\n    title: \"Impressora\",\n    path: \"/impressora\",\n    icon: /*#__PURE__*/_jsxDEV(AiFillPrinter, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Horários de Funcionamento\",\n    path: \"/horario-funcionamento\",\n    icon: /*#__PURE__*/_jsxDEV(FaIcons.FaRegClock, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Formas de pagamento aceitas\",\n    path: \"/formas-pagamento\",\n    icon: /*#__PURE__*/_jsxDEV(FaIcons.FaMoneyCheckAlt, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 662,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Entrega/Retirada\",\n    path: \"/entrega-retirada\",\n    icon: /*#__PURE__*/_jsxDEV(FaArrowRightToCity, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Meus pedidos\",\n    path: \"/meus-pedidos-configuracao\",\n    icon: /*#__PURE__*/_jsxDEV(AiIcons.AiOutlineTool, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Robô\",\n    path: \"/robo-configuracao\",\n    icon: /*#__PURE__*/_jsxDEV(BsRobot, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 689,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Respostas Personalizadas\",\n    path: \"/respostas-personalizadas\",\n    icon: /*#__PURE__*/_jsxDEV(BsRobot, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 698,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Importar Cardápio\",\n    path: \"/importacao-cardapio\",\n    icon: /*#__PURE__*/_jsxDEV(LuImport, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 707,\n      columnNumber: 11\n    }, this)\n  }]\n}, {\n  title: \"Administrativo\",\n  //path: '/configuracoes',\n  icon: /*#__PURE__*/_jsxDEV(RiAdminFill, {\n    style: {\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 718,\n    columnNumber: 7\n  }, this),\n  permission: \"AdminOnly\",\n  iconClosed: /*#__PURE__*/_jsxDEV(RiIcons.RiArrowDownSFill, {\n    style: {\n      marginLeft: \"-5px\",\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 722,\n    columnNumber: 7\n  }, this),\n  iconOpened: /*#__PURE__*/_jsxDEV(RiIcons.RiArrowUpSFill, {\n    style: {\n      marginLeft: \"-5px\",\n      color: \"rgb(120,120,120)\",\n      fontSize: \"20px\"\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 731,\n    columnNumber: 7\n  }, this),\n  subNav: [{\n    title: \"Adicionar Planos\",\n    path: \"/planos-admin\",\n    icon: /*#__PURE__*/_jsxDEV(RiFileAddFill, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Listar Planos\",\n    path: \"/list-plans-admin\",\n    icon: /*#__PURE__*/_jsxDEV(IoIcons.IoIosPaper, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 754,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Listar Empresas\",\n    path: \"/list-empresas\",\n    icon: /*#__PURE__*/_jsxDEV(FaIcons.FaBuilding, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 763,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: \"Administrar Cardápios\",\n    path: \"/admin-solicitacoes-cardapio\",\n    icon: /*#__PURE__*/_jsxDEV(FaIcons.FaUtensils, {\n      style: {\n        color: \"rgb(120,120,120)\",\n        fontSize: \"20px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 772,\n      columnNumber: 11\n    }, this)\n  }]\n}\n// {\n//   title: 'Configurações',\n//   path: '/',\n//   permission: 'default',\n//   icon: <IoiosIcons.IoIosSettings style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\n// },\n// {\n//   title: 'Logout',\n//   path: '/',\n//   permission: 'default',\n//   icon: <HiIcons.HiOutlineLogout style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\n// }\n];\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"IconMeusPedidos\");\n$RefreshReg$(_c2, \"IconWhatsapp\");\n$RefreshReg$(_c3, \"IconPDV\");", "map": {"version": 3, "names": ["React", "FaIcons", "AiIcons", "IoIcons", "RiIcons", "HiIcons", "IoiosIcons", "BsFillMenuButtonFill", "HiOutlineSquaresPlus", "CiBoxList", "CgReorder", "MdPointOfSale", "MdOutlinePlaylistAdd", "BsFillGearFill", "RiWhatsappLine", "RiAdminFill", "RiFileAddFill", "RiBarChartLine", "AiFillPrinter", "FaArrowTrendUp", "FaArrowRightToCity", "TbReportAnalytics", "TbTruckDelivery", "PiCashRegister", "LuImport", "BsRobot", "MdTableBar", "jsxDEV", "_jsxDEV", "IconMeusPedidos", "id", "height", "viewBox", "width", "xmlns", "xmlnsXlink", "children", "gradientUnits", "x1", "x2", "y1", "y2", "offset", "stopColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "fill", "_c", "IconWhatsapp", "xlinkHref", "_c2", "IconPDV", "_c3", "SidebarData", "title", "path", "permission", "icon", "style", "color", "fontSize", "subNav", "cName", "FaUsers", "iconClosed", "RiArrowDownSFill", "marginLeft", "iconOpened", "RiArrowUpSFill", "FaRegUser", "IoIosPaper", "IoIosContacts", "IoMdPeople", "FaEnvelopeOpenText", "FaMapMarkerAlt", "FaMapMarked", "FaRegClock", "FaMoneyCheckAlt", "AiOutlineTool", "FaBuilding", "FaUtensils", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/components/LeftMenu/SidebarData.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport * as FaIcons from \"react-icons/fa\";\r\nimport * as AiIcons from \"react-icons/ai\";\r\nimport * as IoIcons from \"react-icons/io\";\r\nimport * as RiIcons from \"react-icons/ri\";\r\nimport * as HiIcons from \"react-icons/hi\";\r\nimport * as IoiosIcons from \"react-icons/io\";\r\nimport { BsFillMenuButtonFill } from \"react-icons/bs\";\r\nimport { HiOutlineSquaresPlus } from \"react-icons/hi2\";\r\n\r\nimport { CiBoxList } from \"react-icons/ci\";\r\nimport { CgReorder } from \"react-icons/cg\";\r\nimport { MdPointOfSale } from \"react-icons/md\";\r\nimport { MdOutlinePlaylistAdd } from \"react-icons/md\";\r\nimport { BsFillGearFill } from \"react-icons/bs\";\r\nimport { RiWhatsappLine } from \"react-icons/ri\";\r\nimport { RiAdminFill } from \"react-icons/ri\";\r\nimport { RiFileAddFill } from \"react-icons/ri\";\r\nimport { RiBarChartLine } from \"react-icons/ri\";\r\nimport { AiFillPrinter } from \"react-icons/ai\";\r\nimport { FaArrowTrendUp } from \"react-icons/fa6\";\r\nimport { FaArrowRightToCity } from \"react-icons/fa6\";\r\nimport { TbReportAnalytics, TbTruckDelivery } from \"react-icons/tb\";\r\nimport { PiCashRegister } from \"react-icons/pi\";\r\nimport { LuImport } from \"react-icons/lu\";\r\nimport { BsRobot } from \"react-icons/bs\";\r\nimport { MdTableBar } from \"react-icons/md\";\r\n\r\nconst IconMeusPedidos = () => (\r\n  <svg\r\n    id=\"line_gradient\"\r\n    height={20}\r\n    viewBox=\"0 0 64 64\"\r\n    width={20}\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    xmlnsXlink=\"http://www.w3.org/1999/xlink\"\r\n    data-name=\"line gradient\"\r\n  >\r\n    <linearGradient\r\n      id=\"linear-gradient\"\r\n      gradientUnits=\"userSpaceOnUse\"\r\n      x1=\"5.108\"\r\n      x2=\"58.889\"\r\n      y1={32}\r\n      y2={32}\r\n    >\r\n      <stop offset={0} stopColor=\"#096fe0\" />\r\n      <stop offset=\".15843\" stopColor=\"#167ce3\" />\r\n      <stop offset={1} stopColor=\"#5ec3f6\" />\r\n    </linearGradient>\r\n    <path\r\n      d=\"m17.00192 3h-8.78335c-1.71338 0-3.10714 1.39376-3.10714 3.10714-.08269 1.76723 1.33236 3.27198 3.10715 3.26694h8.78333c4.11772-.09088 4.12111-6.28248 0-6.37409zm1.03571 3.26695c0 .57146-.46476 1.03571-1.03571 1.03571h-8.78335c-1.37751.0017-1.3808-2.23264.00004-2.23123-.00004 0 8.7833 0 8.7833 0 .64043-.00438 1.10241.57244 1.03571 1.19552zm1.03571 5.33737h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30266h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30165h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30266h-12.92619c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57197 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46375-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm18.35409-55.92857h-8.78334c-1.71338 0-3.10714 1.39376-3.10714 3.10714-.08269 1.76723 1.33236 3.27198 3.10715 3.26694h8.78333c4.11772-.09088 4.12111-6.28248 0-6.37409zm1.03571 3.26695c0 .57146-.46425 1.03571-1.03571 1.03571h-8.78334c-1.37751.0017-1.3808-2.23264.00004-2.23123-.00004 0 8.7833 0 8.7833 0 .64097-.00438 1.10233.57242 1.03571 1.19552zm1.03571 5.33737h-12.9262c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm1.03571 4.30266h-12.9262c-.57197 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46375 1.03571 1.03571 1.03571h12.9262c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85477v-6.53289h10.85477zm18.3546-30.11562h-8.78435c-1.71338 0-3.10714 1.39376-3.10714 3.10714-.08269 1.76723 1.33236 3.27198 3.10715 3.26694h8.78434c4.11772-.09088 4.12111-6.28248 0-6.37409zm1.03571 3.26695c0 .57146-.46425 1.03571-1.03571 1.03571h-8.78435c-1.37764.00192-1.38091-2.23286.00004-2.23123-.00004 0 8.78431 0 8.78431 0 .64097-.00438 1.10233.57242 1.03571 1.19552zm1.03571 5.33737h-12.92721c-.57247 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46324 1.03571 1.03571 1.03571h12.92721c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85578v-6.53289h10.85578zm1.03571 4.30266h-12.92721c-.57247 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46324 1.03571 1.03571 1.03571h12.92721c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85578v-6.53289h10.85578zm1.03571 4.30165h-12.92721c-.57247 0-1.03571.46324-1.03571 1.03571v8.60432c0 .57247.46324 1.03571 1.03571 1.03571h12.92721c.57247 0 1.03571-.46324 1.03571-1.03571v-8.60432c0-.57247-.46324-1.03571-1.03571-1.03571zm-1.03571 8.60432h-10.85578v-6.53289h10.85578z\"\r\n      fill=\"url(#linear-gradient)\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nconst IconWhatsapp = () => (\r\n  <svg\r\n    height={20}\r\n    viewBox=\"0 0 512 512\"\r\n    width={20}\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    xmlnsXlink=\"http://www.w3.org/1999/xlink\"\r\n  >\r\n    <linearGradient id=\"a\">\r\n      <stop offset={0} stopColor=\"#2af598\" />\r\n      <stop offset={1} stopColor=\"#009efd\" />\r\n    </linearGradient>\r\n    <linearGradient\r\n      id=\"b\"\r\n      gradientUnits=\"userSpaceOnUse\"\r\n      x1=\"255.7523048184\"\r\n      x2=\"255.7523048184\"\r\n      xlinkHref=\"#a\"\r\n      y1={0}\r\n      y2=\"512.0000297334\"\r\n    />\r\n    <linearGradient\r\n      id=\"c\"\r\n      gradientUnits=\"userSpaceOnUse\"\r\n      x1=\"257.750272915\"\r\n      x2=\"257.750272915\"\r\n      xlinkHref=\"#a\"\r\n      y1=\"141.862828\"\r\n      y2=\"361.650308\"\r\n    />\r\n    <path\r\n      d=\"m435.921875 74.351562c-48.097656-47.917968-112.082031-74.3242182-180.179687-74.351562-67.945313 0-132.03125 26.382812-180.445313 74.289062-48.5 47.988282-75.234375 111.761719-75.296875 179.339844v.078125.046875c.0078125 40.902344 10.753906 82.164063 31.152344 119.828125l-30.453125 138.417969 140.011719-31.847656c35.460937 17.871094 75.027343 27.292968 114.933593 27.308594h.101563c67.933594 0 132.019531-26.386719 180.441406-74.296876 48.542969-48.027343 75.289062-111.71875 75.320312-179.339843.019532-67.144531-26.820312-130.882813-75.585937-179.472657zm-180.179687 393.148438h-.089844c-35.832032-.015625-71.335938-9.011719-102.667969-26.023438l-6.621094-3.59375-93.101562 21.175782 20.222656-91.90625-3.898437-6.722656c-19.382813-33.425782-29.625-70.324219-29.625-106.71875.074218-117.800782 96.863281-213.75 215.773437-213.75 57.445313.023437 111.421875 22.292968 151.984375 62.699218 41.175781 41.03125 63.84375 94.710938 63.824219 151.152344-.046875 117.828125-96.855469 213.6875-215.800781 213.6875zm0 0\"\r\n      fill=\"url(#b)\"\r\n    />\r\n    <path\r\n      d=\"m186.152344 141.863281h-11.210938c-3.902344 0-10.238281 1.460938-15.597656 7.292969-5.363281 5.835938-20.476562 19.941406-20.476562 48.628906s20.964843 56.40625 23.886718 60.300782c2.925782 3.890624 40.46875 64.640624 99.929688 88.011718 49.417968 19.421875 59.476562 15.558594 70.199218 14.585938 10.726563-.96875 34.613282-14.101563 39.488282-27.714844s4.875-25.285156 3.414062-27.722656c-1.464844-2.429688-5.367187-3.886719-11.214844-6.800782-5.851562-2.917968-34.523437-17.261718-39.886718-19.210937-5.363282-1.941406-9.261719-2.914063-13.164063 2.925781-3.902343 5.828125-15.390625 19.3125-18.804687 23.203125-3.410156 3.894531-6.824219 4.382813-12.675782 1.464844-5.851562-2.925781-24.5-9.191406-46.847656-29.050781-17.394531-15.457032-29.464844-35.167969-32.878906-41.003906-3.410156-5.832032-.363281-8.988282 2.570312-11.898438 2.628907-2.609375 6.179688-6.179688 9.105469-9.582031 2.921875-3.40625 3.753907-5.835938 5.707031-9.726563 1.949219-3.890625.972657-7.296875-.488281-10.210937-1.464843-2.917969-12.691406-31.75-17.894531-43.28125h.003906c-4.382812-9.710938-8.996094-10.039063-13.164062-10.210938zm0 0\"\r\n      fill=\"url(#c)\"\r\n    />\r\n  </svg>\r\n);\r\n\r\nconst IconPDV = () => (\r\n  <svg\r\n    height={20}\r\n    viewBox=\"0 0 64 64\"\r\n    width={20}\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    xmlnsXlink=\"http://www.w3.org/1999/xlink\"\r\n  >\r\n    <linearGradient\r\n      id=\"New_Gradient_Swatch_3\"\r\n      gradientUnits=\"userSpaceOnUse\"\r\n      x1=\"42.43\"\r\n      x2=\"42.43\"\r\n      y1=\"65.857\"\r\n      y2=\"-4.017\"\r\n    >\r\n      <stop offset={0} stopColor=\"#006df0\" />\r\n      <stop offset={1} stopColor=\"#00e7f0\" />\r\n    </linearGradient>\r\n    <linearGradient\r\n      id=\"New_Gradient_Swatch_3-2\"\r\n      x1=\"51.829\"\r\n      x2=\"51.829\"\r\n      xlinkHref=\"#New_Gradient_Swatch_3\"\r\n      y1=\"65.857\"\r\n      y2=\"-4.017\"\r\n    />\r\n    <linearGradient\r\n      id=\"New_Gradient_Swatch_3-3\"\r\n      x1={49}\r\n      x2={49}\r\n      xlinkHref=\"#New_Gradient_Swatch_3\"\r\n      y1=\"65.857\"\r\n      y2=\"-4.017\"\r\n    />\r\n    <linearGradient\r\n      id=\"New_Gradient_Swatch_3-4\"\r\n      x1={32}\r\n      x2={32}\r\n      xlinkHref=\"#New_Gradient_Swatch_3\"\r\n      y1=\"65.857\"\r\n      y2=\"-4.017\"\r\n    />\r\n    <linearGradient\r\n      id=\"New_Gradient_Swatch_3-7\"\r\n      x1={29}\r\n      x2={29}\r\n      xlinkHref=\"#New_Gradient_Swatch_3\"\r\n      y1=\"65.857\"\r\n      y2=\"-4.017\"\r\n    />\r\n    <linearGradient\r\n      id=\"New_Gradient_Swatch_3-8\"\r\n      x1={22}\r\n      x2={22}\r\n      xlinkHref=\"#New_Gradient_Swatch_3\"\r\n      y1=\"65.857\"\r\n      y2=\"-4.017\"\r\n    />\r\n    <linearGradient\r\n      id=\"New_Gradient_Swatch_3-9\"\r\n      x1={31}\r\n      x2={31}\r\n      xlinkHref=\"#New_Gradient_Swatch_3\"\r\n      y1=\"65.857\"\r\n      y2=\"-4.017\"\r\n    />\r\n    <g\r\n      id=\"mobilephone_food_delivery_package_shopping\"\r\n      data-name=\"mobilephone, food, delivery, package, shopping\"\r\n    >\r\n      <path\r\n        d=\"m61.73 38.42a3.208 3.208 0 0 0 -.27-.65 3.011 3.011 0 0 0 -4.1-1.1l-4.8 2.78a2.962 2.962 0 0 0 -2.56-1.45h-7.76l-1.37-.68a2.971 2.971 0 0 0 -1.34-.32h-4.86a2.974 2.974 0 0 0 -1.8.6l-1.87 1.4v-1a1 1 0 0 0 -1-1h-5v-3a1 1 0 0 0 -2 0v20a1 1 0 0 0 2 0v-3h5a1 1 0 0 0 1-1v-1.13l.94.63a2.979 2.979 0 0 0 1.67.5h12.85a2.936 2.936 0 0 0 1.53-.42l12.37-7.71a3.023 3.023 0 0 0 1.5-2.61 2.842 2.842 0 0 0 -.13-.84zm-32.73 10.58h-4v-10h4zm30.33-8.85-12.38 7.72a.922.922 0 0 1 -.49.13h-12.85a1.023 1.023 0 0 1 -.56-.17l-2.05-1.37v-4.96l3.07-2.3a.984.984 0 0 1 .6-.2h4.86a.956.956 0 0 1 .44.11l1.58.78a1 1 0 0 0 .45.11h8a1 1 0 0 1 .87.51 1.065 1.065 0 0 1 .13.53.956.956 0 0 1 -.31.67 1.008 1.008 0 0 1 -.69.29h-7a1 1 0 0 0 0 2s7.25 0 7.31-.02a2.958 2.958 0 0 0 1.8-.85 2.92 2.92 0 0 0 .83-1.6l5.43-3.13a.984.984 0 0 1 1.35.37 1.121 1.121 0 0 1 .1.22.884.884 0 0 1 .04.27 1.014 1.014 0 0 1 -.53.89z\"\r\n        fill=\"url(#New_Gradient_Swatch_3)\"\r\n      />\r\n      <path\r\n        d=\"m48 21a1 1 0 0 0 1 1 5.013 5.013 0 0 1 4.715 3.334 1 1 0 0 0 1.885-.668 7.017 7.017 0 0 0 -6.6-4.666 1 1 0 0 0 -1 1z\"\r\n        fill=\"url(#New_Gradient_Swatch_3-2)\"\r\n      />\r\n      <path\r\n        d=\"m46 16a2.959 2.959 0 0 0 .346 1.369 10.009 10.009 0 0 0 -7.346 9.631v.184a2.988 2.988 0 0 0 .343 5.738l1.762 3.525a1 1 0 0 0 .895.553h14a1 1 0 0 0 .9-.553l1.762-3.525a2.988 2.988 0 0 0 .338-5.738v-.184a10.009 10.009 0 0 0 -7.346-9.631 2.959 2.959 0 0 0 .346-1.369 3 3 0 0 0 -6 0zm9.382 19h-12.764l-1-2h14.764zm2.618-4h-18a1 1 0 0 1 0-2h18a1 1 0 0 1 0 2zm-1-4h-16a8 8 0 0 1 16 0zm-7-11a1 1 0 1 1 -1-1 1 1 0 0 1 1 1z\"\r\n        fill=\"url(#New_Gradient_Swatch_3-3)\"\r\n      />\r\n      <g fill=\"url(#New_Gradient_Swatch_3-4)\">\r\n        <path d=\"m50 50a1 1 0 0 0 -1 1v6a3 3 0 0 1 -3 3h-28a3 3 0 0 1 -3-3v-50a3 3 0 0 1 3-3h28a3 3 0 0 1 3 3v4a1 1 0 0 0 2 0v-4a5.006 5.006 0 0 0 -5-5h-28a5.006 5.006 0 0 0 -5 5v50a5.006 5.006 0 0 0 5 5h28a5.006 5.006 0 0 0 5-5v-6a1 1 0 0 0 -1-1z\" />\r\n        <path d=\"m28.24 7.552-.816-1.213a2.98 2.98 0 0 0 -2.494-1.339h-5.93a3 3 0 0 0 -3 3v48a3 3 0 0 0 3 3h26a3 3 0 0 0 3-3v-4a1 1 0 0 0 -2 0v4a1 1 0 0 1 -1 1h-26a1 1 0 0 1 -1-1v-48a1 1 0 0 1 1-1h5.93a.99.99 0 0 1 .83.448l.816 1.213a2.979 2.979 0 0 0 2.494 1.339h5.86a2.978 2.978 0 0 0 2.49-1.332l.824-1.227a.988.988 0 0 1 .826-.441h5.93a1 1 0 0 1 1 1v3a1 1 0 0 0 2 0v-3a3 3 0 0 0 -3-3h-5.93a2.977 2.977 0 0 0 -2.49 1.332l-.824 1.227a.988.988 0 0 1 -.826.441h-5.86a.99.99 0 0 1 -.83-.448z\" />\r\n        <path d=\"m33 7a1 1 0 0 0 0-2h-2a1 1 0 0 0 0 2z\" />\r\n      </g>\r\n      <path\r\n        d=\"m38 21v-8a1 1 0 0 0 -1-1h-16a1 1 0 0 0 -1 1v8a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1zm-2-1h-14v-6h14z\"\r\n        fill=\"url(#New_Gradient_Swatch_3-7)\"\r\n      />\r\n      <path\r\n        d=\"m24 29a1 1 0 0 0 -1-1h-2a1 1 0 0 0 0 2h2a1 1 0 0 0 1-1z\"\r\n        fill=\"url(#New_Gradient_Swatch_3-8)\"\r\n      />\r\n      <path\r\n        d=\"m27 28a1 1 0 0 0 0 2h8a1 1 0 0 0 0-2z\"\r\n        fill=\"url(#New_Gradient_Swatch_3-9)\"\r\n      />\r\n      <path\r\n        d=\"m23 24h-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2z\"\r\n        fill=\"url(#New_Gradient_Swatch_3-8)\"\r\n      />\r\n      <path\r\n        d=\"m38 25a1 1 0 0 0 -1-1h-10a1 1 0 0 0 0 2h10a1 1 0 0 0 1-1z\"\r\n        fill=\"url(#New_Gradient_Swatch_3-4)\"\r\n      />\r\n    </g>\r\n  </svg>\r\n);\r\n\r\nexport const SidebarData = [\r\n  {\r\n    title: \"Meus Pedidos\",\r\n    path: \"/\",\r\n    permission: \"default\",\r\n    icon: (\r\n      <IconMeusPedidos\r\n        style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n      />\r\n    ),\r\n  },\r\n  {\r\n    title: \"WhatsApp\",\r\n    path: \"/whatsapp\",\r\n    permission: \"default\",\r\n    icon: (\r\n      <IconWhatsapp style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }} />\r\n    ),\r\n  },\r\n  {\r\n    title: \"Pedidos Balcão\",\r\n    path: \"/pdv\",\r\n    permission: \"default\",\r\n    icon: <IconPDV style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }} />,\r\n  },\r\n  {\r\n    title: \"Salão\",\r\n    permission: \"default\",\r\n    icon: <MdTableBar style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }} />,\r\n    subNav: [\r\n      {\r\n        title: \"Mesas\",\r\n        path: \"/pedidos-mesas\",\r\n        icon: <MdTableBar style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }} />,\r\n        cName: \"sub-nav\",\r\n      },\r\n      {\r\n        title: \"Garçons\",\r\n        path: \"/mesas-garcons\",\r\n        icon: (\r\n          <FaIcons.FaUsers\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n        cName: \"sub-nav\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Caixa\",\r\n    path: \"/caixa\",\r\n    permission: \"default\",\r\n    icon: (\r\n      <PiCashRegister style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }} />\r\n    ),\r\n  },\r\n  {\r\n    title: \"Cardápio\",\r\n    //path: '/list-empresa',\r\n    permission: \"default\",\r\n    icon: (\r\n      <BsFillMenuButtonFill\r\n        style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n      />\r\n    ),\r\n    iconClosed: (\r\n      <RiIcons.RiArrowDownSFill\r\n        style={{\r\n          marginLeft: \"-5px\",\r\n          color: \"rgb(120,120,120)\",\r\n          fontSize: \"20px\",\r\n        }}\r\n      />\r\n    ),\r\n    iconOpened: (\r\n      <RiIcons.RiArrowUpSFill\r\n        style={{\r\n          marginLeft: \"-5px\",\r\n          color: \"rgb(120,120,120)\",\r\n          fontSize: \"20px\",\r\n        }}\r\n      />\r\n    ),\r\n\r\n    subNav: [\r\n      {\r\n        title: \"Gestor de Cardápio\",\r\n        path: \"/list-categoria\",\r\n        icon: (\r\n          <CiBoxList\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n        cName: \"sub-nav\",\r\n      },\r\n      /*{\r\n        title: \"Itens\",\r\n        path: \"/list-item\",\r\n        icon: (\r\n          <CiBoxList style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }} />\r\n        ),\r\n        cName: \"sub-nav\",\r\n      },*/\r\n      {\r\n        title: \"Gerenciar complementos\",\r\n        path: \"/list-adicional\",\r\n        icon: (\r\n          <MdOutlinePlaylistAdd\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n        cName: \"sub-nav\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Relatórios\",\r\n    //path: '/list-empresa',\r\n    permission: \"default\",\r\n    icon: (\r\n      <TbReportAnalytics\r\n        style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n      />\r\n    ),\r\n    iconClosed: (\r\n      <RiIcons.RiArrowDownSFill\r\n        style={{\r\n          marginLeft: \"-5px\",\r\n          color: \"rgb(120,120,120)\",\r\n          fontSize: \"20px\",\r\n        }}\r\n      />\r\n    ),\r\n    iconOpened: (\r\n      <RiIcons.RiArrowUpSFill\r\n        style={{\r\n          marginLeft: \"-5px\",\r\n          color: \"rgb(120,120,120)\",\r\n          fontSize: \"20px\",\r\n        }}\r\n      />\r\n    ),\r\n\r\n    subNav: [\r\n      {\r\n        title: \"Geral\",\r\n        path: \"/relatorio-geral\",\r\n        icon: (\r\n          <RiBarChartLine\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n        cName: \"sub-nav\",\r\n      },\r\n      {\r\n        title: \"Meu desempenho\",\r\n        path: \"/desempenho\",\r\n        icon: (\r\n          <FaArrowTrendUp\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n        cName: \"sub-nav\",\r\n      },\r\n      /*{\r\n        title: 'Itens',\r\n        path: '/list-item',\r\n        icon: <CiBoxList style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n        cName: 'sub-nav'\r\n      },\r\n      {\r\n        title: 'Adicionais',\r\n        path: '/list-adicional',\r\n        icon: <MdOutlinePlaylistAdd style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n        cName: 'sub-nav'\r\n      }*/\r\n    ],\r\n  },\r\n  //{\r\n  //  title: \"Empresas\",\r\n  //  path: \"/list-empresa\",\r\n  //  permission: \"viewIndustry\",\r\n  //  icon: (\r\n  //    <IoiosIcons.IoIosBusiness\r\n  //      style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n  //    />\r\n  //  ),\r\n    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n\r\n    // subNav: [\r\n    //   {\r\n    //     title: 'Cadastrar Indústria',\r\n    //     path: '/empresa',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    //     cName: 'sub-nav'\r\n    //   },\r\n    //   {\r\n    //     title: 'Listar Indústrias',\r\n    //     path: '/list-empresa',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    //     cName: 'sub-nav'\r\n    //   }\r\n    // ]\r\n  //},\r\n  //{\r\n  //  title: \"Usuários\",\r\n  //  path: \"/list-users\",\r\n  //  permission: \"viewUser\",\r\n  //  icon: (\r\n  //    <FaIcons.FaUsers\r\n  //      style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n  //    />\r\n  //  ),\r\n    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n\r\n    // subNav: [\r\n    //   {\r\n    //     title: 'Cadastrar Usuário',\r\n    //     path: '/cadastro-user',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    //     cName: 'sub-nav'\r\n    //   },\r\n    //   {\r\n    //     title: 'Listar Usuários',\r\n    //     path: '/list-users',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    //     cName: 'sub-nav'\r\n    //   }\r\n    // ]\r\n  //},\r\n  {\r\n    title: \"Entregadores\",\r\n    path: \"/list-entregadores\",\r\n    permission: \"default\",\r\n    icon: (\r\n      <TbTruckDelivery\r\n        style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n      />\r\n    ),\r\n    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n\r\n    // subNav: [\r\n    //   {\r\n    //     title: 'Cadastrar Usuário',\r\n    //     path: '/cadastro-user',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    //     cName: 'sub-nav'\r\n    //   },\r\n    //   {\r\n    //     title: 'Listar Usuários',\r\n    //     path: '/list-users',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    //     cName: 'sub-nav'\r\n    //   }\r\n    // ]\r\n  },\r\n  {\r\n    title: \"Minha Conta\",\r\n    //path: '/list-users',\r\n    permission: \"default\",\r\n    icon: (\r\n      <FaIcons.FaRegUser\r\n        style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n      />\r\n    ),\r\n    iconClosed: (\r\n      <RiIcons.RiArrowDownSFill\r\n        style={{\r\n          marginLeft: \"-5px\",\r\n          color: \"rgb(120,120,120)\",\r\n          fontSize: \"20px\",\r\n        }}\r\n      />\r\n    ),\r\n    iconOpened: (\r\n      <RiIcons.RiArrowUpSFill\r\n        style={{\r\n          marginLeft: \"-5px\",\r\n          color: \"rgb(120,120,120)\",\r\n          fontSize: \"20px\",\r\n        }}\r\n      />\r\n    ),\r\n\r\n    subNav: [\r\n      {\r\n        title: \"Planos\",\r\n        path: \"/planos\",\r\n        icon: (\r\n          <IoIcons.IoIosPaper\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n        cName: \"sub-nav\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Clientes\",\r\n    path: \"/list-cliente\",\r\n    icon: (\r\n      <IoiosIcons.IoIosContacts\r\n        style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n      />\r\n    ),\r\n    permission: \"viewCVO\",\r\n    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n\r\n    // subNav: [\r\n    //   {\r\n    //     title: 'Cadastrar Cliente',\r\n    //     path: '/',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\r\n    //   },\r\n    //   {\r\n    //     title: 'Listar Clientes',\r\n    //     path: '/',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\r\n    //   }\r\n    // ]\r\n  },\r\n  {\r\n    title: \"Vendedores\",\r\n    path: \"/list-vendedor\",\r\n    icon: (\r\n      <IoIcons.IoMdPeople\r\n        style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n      />\r\n    ),\r\n    permission: \"viewCVO\",\r\n    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n\r\n    // subNav: [\r\n    //   {\r\n    //     title: 'Cadastrar Vendedor',\r\n    //     path: '/',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\r\n    //   },\r\n    //   {\r\n    //     title: 'Listar Vendedores',\r\n    //     path: '/',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\r\n    //   }\r\n    // ]\r\n  },\r\n  {\r\n    title: \"Orçamentos\",\r\n    path: \"/list-orcamento\",\r\n    icon: (\r\n      <FaIcons.FaEnvelopeOpenText\r\n        style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n      />\r\n    ),\r\n    permission: \"viewCVO\",\r\n    // iconClosed: <RiIcons.RiArrowDownSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n    // iconOpened: <RiIcons.RiArrowUpSFill style={{marginLeft:\"-5px\", color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>,\r\n\r\n    // subNav: [\r\n    //   {\r\n    //     title: 'Cadastrar Orçamento',\r\n    //     path: '/',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\r\n    //   },\r\n    //   {\r\n    //     title: 'Listar Orçamentos',\r\n    //     path: '/',\r\n    //     icon: <IoIcons.IoIosPaper style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\r\n    //   }\r\n    // ]\r\n  },\r\n  {\r\n    title: \"Configurações\",\r\n    //path: '/configuracoes',\r\n    icon: (\r\n      <BsFillGearFill style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }} />\r\n    ),\r\n    permission: \"default\",\r\n    iconClosed: (\r\n      <RiIcons.RiArrowDownSFill\r\n        style={{\r\n          marginLeft: \"-5px\",\r\n          color: \"rgb(120,120,120)\",\r\n          fontSize: \"20px\",\r\n        }}\r\n      />\r\n    ),\r\n    iconOpened: (\r\n      <RiIcons.RiArrowUpSFill\r\n        style={{\r\n          marginLeft: \"-5px\",\r\n          color: \"rgb(120,120,120)\",\r\n          fontSize: \"20px\",\r\n        }}\r\n      />\r\n    ),\r\n\r\n    subNav: [\r\n      {\r\n        title: \"Endereço\",\r\n        path: \"/endereco-estabelecimento\",\r\n        icon: (\r\n          <FaIcons.FaMapMarkerAlt\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Regiões de entrega\",\r\n        path: \"/raio-entrega\",\r\n        icon: (\r\n          <FaIcons.FaMapMarked\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },/*\r\n      {\r\n        title: \"Sincronizar Whatsapp\",\r\n        path: \"/whatsapp-sync\",\r\n        icon: (\r\n          <FaIcons.FaWhatsapp\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      }*/\r\n      {\r\n        title: \"Impressora\",\r\n        path: \"/impressora\",\r\n        icon: (\r\n          <AiFillPrinter\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Horários de Funcionamento\",\r\n        path: \"/horario-funcionamento\",\r\n        icon: (\r\n          <FaIcons.FaRegClock\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Formas de pagamento aceitas\",\r\n        path: \"/formas-pagamento\",\r\n        icon: (\r\n          <FaIcons.FaMoneyCheckAlt\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Entrega/Retirada\",\r\n        path: \"/entrega-retirada\",\r\n        icon: (\r\n          <FaArrowRightToCity\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Meus pedidos\",\r\n        path: \"/meus-pedidos-configuracao\",\r\n        icon: (\r\n          <AiIcons.AiOutlineTool\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Robô\",\r\n        path: \"/robo-configuracao\",\r\n        icon: (\r\n          <BsRobot\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Respostas Personalizadas\",\r\n        path: \"/respostas-personalizadas\",\r\n        icon: (\r\n          <BsRobot\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Importar Cardápio\",\r\n        path: \"/importacao-cardapio\",\r\n        icon: (\r\n          <LuImport\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Administrativo\",\r\n    //path: '/configuracoes',\r\n    icon: (\r\n      <RiAdminFill style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }} />\r\n    ),\r\n    permission: \"AdminOnly\",\r\n    iconClosed: (\r\n      <RiIcons.RiArrowDownSFill\r\n        style={{\r\n          marginLeft: \"-5px\",\r\n          color: \"rgb(120,120,120)\",\r\n          fontSize: \"20px\",\r\n        }}\r\n      />\r\n    ),\r\n    iconOpened: (\r\n      <RiIcons.RiArrowUpSFill\r\n        style={{\r\n          marginLeft: \"-5px\",\r\n          color: \"rgb(120,120,120)\",\r\n          fontSize: \"20px\",\r\n        }}\r\n      />\r\n    ),\r\n\r\n    subNav: [\r\n      {\r\n        title: \"Adicionar Planos\",\r\n        path: \"/planos-admin\",\r\n        icon: (\r\n          <RiFileAddFill\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Listar Planos\",\r\n        path: \"/list-plans-admin\",\r\n        icon: (\r\n          <IoIcons.IoIosPaper\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Listar Empresas\",\r\n        path: \"/list-empresas\",\r\n        icon: (\r\n          <FaIcons.FaBuilding\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n      {\r\n        title: \"Administrar Cardápios\",\r\n        path: \"/admin-solicitacoes-cardapio\",\r\n        icon: (\r\n          <FaIcons.FaUtensils\r\n            style={{ color: \"rgb(120,120,120)\", fontSize: \"20px\" }}\r\n          />\r\n        ),\r\n      },\r\n    ],\r\n  },\r\n  // {\r\n  //   title: 'Configurações',\r\n  //   path: '/',\r\n  //   permission: 'default',\r\n  //   icon: <IoiosIcons.IoIosSettings style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\r\n  // },\r\n  // {\r\n  //   title: 'Logout',\r\n  //   path: '/',\r\n  //   permission: 'default',\r\n  //   icon: <HiIcons.HiOutlineLogout style={{color:\"rgb(120,120,120)\", fontSize:\"20px\"}}/>\r\n  // }\r\n];\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,UAAU,MAAM,gBAAgB;AAC5C,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,oBAAoB,QAAQ,iBAAiB;AAEtD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,gBAAgB;AACnE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,eAAe,GAAGA,CAAA,kBACtBD,OAAA;EACEE,EAAE,EAAC,eAAe;EAClBC,MAAM,EAAE,EAAG;EACXC,OAAO,EAAC,WAAW;EACnBC,KAAK,EAAE,EAAG;EACVC,KAAK,EAAC,4BAA4B;EAClCC,UAAU,EAAC,8BAA8B;EACzC,aAAU,eAAe;EAAAC,QAAA,gBAEzBR,OAAA;IACEE,EAAE,EAAC,iBAAiB;IACpBO,aAAa,EAAC,gBAAgB;IAC9BC,EAAE,EAAC,OAAO;IACVC,EAAE,EAAC,QAAQ;IACXC,EAAE,EAAE,EAAG;IACPC,EAAE,EAAE,EAAG;IAAAL,QAAA,gBAEPR,OAAA;MAAMc,MAAM,EAAE,CAAE;MAACC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvCnB,OAAA;MAAMc,MAAM,EAAC,QAAQ;MAACC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5CnB,OAAA;MAAMc,MAAM,EAAE,CAAE;MAACC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzB,CAAC,eACjBnB,OAAA;IACEoB,CAAC,EAAC,yxGAAyxG;IAC3xGC,IAAI,EAAC;EAAuB;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACC,CACN;AAACG,EAAA,GA3BIrB,eAAe;AA6BrB,MAAMsB,YAAY,GAAGA,CAAA,kBACnBvB,OAAA;EACEG,MAAM,EAAE,EAAG;EACXC,OAAO,EAAC,aAAa;EACrBC,KAAK,EAAE,EAAG;EACVC,KAAK,EAAC,4BAA4B;EAClCC,UAAU,EAAC,8BAA8B;EAAAC,QAAA,gBAEzCR,OAAA;IAAgBE,EAAE,EAAC,GAAG;IAAAM,QAAA,gBACpBR,OAAA;MAAMc,MAAM,EAAE,CAAE;MAACC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvCnB,OAAA;MAAMc,MAAM,EAAE,CAAE;MAACC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzB,CAAC,eACjBnB,OAAA;IACEE,EAAE,EAAC,GAAG;IACNO,aAAa,EAAC,gBAAgB;IAC9BC,EAAE,EAAC,gBAAgB;IACnBC,EAAE,EAAC,gBAAgB;IACnBa,SAAS,EAAC,IAAI;IACdZ,EAAE,EAAE,CAAE;IACNC,EAAE,EAAC;EAAgB;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC,eACFnB,OAAA;IACEE,EAAE,EAAC,GAAG;IACNO,aAAa,EAAC,gBAAgB;IAC9BC,EAAE,EAAC,eAAe;IAClBC,EAAE,EAAC,eAAe;IAClBa,SAAS,EAAC,IAAI;IACdZ,EAAE,EAAC,YAAY;IACfC,EAAE,EAAC;EAAY;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC,eACFnB,OAAA;IACEoB,CAAC,EAAC,s/BAAs/B;IACx/BC,IAAI,EAAC;EAAS;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC,eACFnB,OAAA;IACEoB,CAAC,EAAC,gmCAAgmC;IAClmCC,IAAI,EAAC;EAAS;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACC,CACN;AAACM,GAAA,GAvCIF,YAAY;AAyClB,MAAMG,OAAO,GAAGA,CAAA,kBACd1B,OAAA;EACEG,MAAM,EAAE,EAAG;EACXC,OAAO,EAAC,WAAW;EACnBC,KAAK,EAAE,EAAG;EACVC,KAAK,EAAC,4BAA4B;EAClCC,UAAU,EAAC,8BAA8B;EAAAC,QAAA,gBAEzCR,OAAA;IACEE,EAAE,EAAC,uBAAuB;IAC1BO,aAAa,EAAC,gBAAgB;IAC9BC,EAAE,EAAC,OAAO;IACVC,EAAE,EAAC,OAAO;IACVC,EAAE,EAAC,QAAQ;IACXC,EAAE,EAAC,QAAQ;IAAAL,QAAA,gBAEXR,OAAA;MAAMc,MAAM,EAAE,CAAE;MAACC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvCnB,OAAA;MAAMc,MAAM,EAAE,CAAE;MAACC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzB,CAAC,eACjBnB,OAAA;IACEE,EAAE,EAAC,yBAAyB;IAC5BQ,EAAE,EAAC,QAAQ;IACXC,EAAE,EAAC,QAAQ;IACXa,SAAS,EAAC,wBAAwB;IAClCZ,EAAE,EAAC,QAAQ;IACXC,EAAE,EAAC;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC,eACFnB,OAAA;IACEE,EAAE,EAAC,yBAAyB;IAC5BQ,EAAE,EAAE,EAAG;IACPC,EAAE,EAAE,EAAG;IACPa,SAAS,EAAC,wBAAwB;IAClCZ,EAAE,EAAC,QAAQ;IACXC,EAAE,EAAC;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC,eACFnB,OAAA;IACEE,EAAE,EAAC,yBAAyB;IAC5BQ,EAAE,EAAE,EAAG;IACPC,EAAE,EAAE,EAAG;IACPa,SAAS,EAAC,wBAAwB;IAClCZ,EAAE,EAAC,QAAQ;IACXC,EAAE,EAAC;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC,eACFnB,OAAA;IACEE,EAAE,EAAC,yBAAyB;IAC5BQ,EAAE,EAAE,EAAG;IACPC,EAAE,EAAE,EAAG;IACPa,SAAS,EAAC,wBAAwB;IAClCZ,EAAE,EAAC,QAAQ;IACXC,EAAE,EAAC;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC,eACFnB,OAAA;IACEE,EAAE,EAAC,yBAAyB;IAC5BQ,EAAE,EAAE,EAAG;IACPC,EAAE,EAAE,EAAG;IACPa,SAAS,EAAC,wBAAwB;IAClCZ,EAAE,EAAC,QAAQ;IACXC,EAAE,EAAC;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC,eACFnB,OAAA;IACEE,EAAE,EAAC,yBAAyB;IAC5BQ,EAAE,EAAE,EAAG;IACPC,EAAE,EAAE,EAAG;IACPa,SAAS,EAAC,wBAAwB;IAClCZ,EAAE,EAAC,QAAQ;IACXC,EAAE,EAAC;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC,eACFnB,OAAA;IACEE,EAAE,EAAC,4CAA4C;IAC/C,aAAU,gDAAgD;IAAAM,QAAA,gBAE1DR,OAAA;MACEoB,CAAC,EAAC,q3BAAq3B;MACv3BC,IAAI,EAAC;IAA6B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eACFnB,OAAA;MACEoB,CAAC,EAAC,sHAAsH;MACxHC,IAAI,EAAC;IAA+B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACFnB,OAAA;MACEoB,CAAC,EAAC,gaAAga;MAClaC,IAAI,EAAC;IAA+B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACFnB,OAAA;MAAGqB,IAAI,EAAC,+BAA+B;MAAAb,QAAA,gBACrCR,OAAA;QAAMoB,CAAC,EAAC;MAAwO;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnPnB,OAAA;QAAMoB,CAAC,EAAC;MAA0d;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrenB,OAAA;QAAMoB,CAAC,EAAC;MAAuC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eACJnB,OAAA;MACEoB,CAAC,EAAC,+FAA+F;MACjGC,IAAI,EAAC;IAA+B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACFnB,OAAA;MACEoB,CAAC,EAAC,yDAAyD;MAC3DC,IAAI,EAAC;IAA+B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACFnB,OAAA;MACEoB,CAAC,EAAC,uCAAuC;MACzCC,IAAI,EAAC;IAA+B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACFnB,OAAA;MACEoB,CAAC,EAAC,0CAA0C;MAC5CC,IAAI,EAAC;IAA+B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eACFnB,OAAA;MACEoB,CAAC,EAAC,2DAA2D;MAC7DC,IAAI,EAAC;IAA+B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACD,CACN;AAACQ,GAAA,GA9GID,OAAO;AAgHb,OAAO,MAAME,WAAW,GAAG,CACzB;EACEC,KAAK,EAAE,cAAc;EACrBC,IAAI,EAAE,GAAG;EACTC,UAAU,EAAE,SAAS;EACrBC,IAAI,eACFhC,OAAA,CAACC,eAAe;IACdgC,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD;AAEL,CAAC,EACD;EACEU,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE,SAAS;EACrBC,IAAI,eACFhC,OAAA,CAACuB,YAAY;IAACU,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAE3E,CAAC,EACD;EACEU,KAAK,EAAE,gBAAgB;EACvBC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,SAAS;EACrBC,IAAI,eAAEhC,OAAA,CAAC0B,OAAO;IAACO,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC1E,CAAC,EACD;EACEU,KAAK,EAAE,OAAO;EACdE,UAAU,EAAE,SAAS;EACrBC,IAAI,eAAEhC,OAAA,CAACF,UAAU;IAACmC,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC5EiB,MAAM,EAAE,CACN;IACEP,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,gBAAgB;IACtBE,IAAI,eAAEhC,OAAA,CAACF,UAAU;MAACmC,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5EkB,KAAK,EAAE;EACT,CAAC,EACD;IACER,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,gBAAgB;IACtBE,IAAI,eACFhC,OAAA,CAAC3B,OAAO,CAACiE,OAAO;MACdL,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CACF;IACDkB,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACER,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,QAAQ;EACdC,UAAU,EAAE,SAAS;EACrBC,IAAI,eACFhC,OAAA,CAACL,cAAc;IAACsC,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAE7E,CAAC,EACD;EACEU,KAAK,EAAE,UAAU;EACjB;EACAE,UAAU,EAAE,SAAS;EACrBC,IAAI,eACFhC,OAAA,CAACrB,oBAAoB;IACnBsD,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CACF;EACDoB,UAAU,eACRvC,OAAA,CAACxB,OAAO,CAACgE,gBAAgB;IACvBP,KAAK,EAAE;MACLQ,UAAU,EAAE,MAAM;MAClBP,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EACDuB,UAAU,eACR1C,OAAA,CAACxB,OAAO,CAACmE,cAAc;IACrBV,KAAK,EAAE;MACLQ,UAAU,EAAE,MAAM;MAClBP,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EAEDiB,MAAM,EAAE,CACN;IACEP,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,EAAE,iBAAiB;IACvBE,IAAI,eACFhC,OAAA,CAACnB,SAAS;MACRoD,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CACF;IACDkB,KAAK,EAAE;EACT,CAAC;EACD;AACN;AACA;AACA;AACA;AACA;AACA;AACA;EACM;IACER,KAAK,EAAE,wBAAwB;IAC/BC,IAAI,EAAE,iBAAiB;IACvBE,IAAI,eACFhC,OAAA,CAAChB,oBAAoB;MACnBiD,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CACF;IACDkB,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACER,KAAK,EAAE,YAAY;EACnB;EACAE,UAAU,EAAE,SAAS;EACrBC,IAAI,eACFhC,OAAA,CAACP,iBAAiB;IAChBwC,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CACF;EACDoB,UAAU,eACRvC,OAAA,CAACxB,OAAO,CAACgE,gBAAgB;IACvBP,KAAK,EAAE;MACLQ,UAAU,EAAE,MAAM;MAClBP,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EACDuB,UAAU,eACR1C,OAAA,CAACxB,OAAO,CAACmE,cAAc;IACrBV,KAAK,EAAE;MACLQ,UAAU,EAAE,MAAM;MAClBP,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EAEDiB,MAAM,EAAE,CACN;IACEP,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,kBAAkB;IACxBE,IAAI,eACFhC,OAAA,CAACX,cAAc;MACb4C,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CACF;IACDkB,KAAK,EAAE;EACT,CAAC,EACD;IACER,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE,aAAa;IACnBE,IAAI,eACFhC,OAAA,CAACT,cAAc;MACb0C,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CACF;IACDkB,KAAK,EAAE;EACT;EACA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAXM;AAaJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF;AACA;EACER,KAAK,EAAE,cAAc;EACrBC,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAE,SAAS;EACrBC,IAAI,eACFhC,OAAA,CAACN,eAAe;IACduC,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD;EAEH;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,CAAC,EACD;EACEU,KAAK,EAAE,aAAa;EACpB;EACAE,UAAU,EAAE,SAAS;EACrBC,IAAI,eACFhC,OAAA,CAAC3B,OAAO,CAACuE,SAAS;IAChBX,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CACF;EACDoB,UAAU,eACRvC,OAAA,CAACxB,OAAO,CAACgE,gBAAgB;IACvBP,KAAK,EAAE;MACLQ,UAAU,EAAE,MAAM;MAClBP,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EACDuB,UAAU,eACR1C,OAAA,CAACxB,OAAO,CAACmE,cAAc;IACrBV,KAAK,EAAE;MACLQ,UAAU,EAAE,MAAM;MAClBP,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EAEDiB,MAAM,EAAE,CACN;IACEP,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE,SAAS;IACfE,IAAI,eACFhC,OAAA,CAACzB,OAAO,CAACsE,UAAU;MACjBZ,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CACF;IACDkB,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,EACD;EACER,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAE,eAAe;EACrBE,IAAI,eACFhC,OAAA,CAACtB,UAAU,CAACoE,aAAa;IACvBb,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CACF;EACDY,UAAU,EAAE;EACZ;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,gBAAgB;EACtBE,IAAI,eACFhC,OAAA,CAACzB,OAAO,CAACwE,UAAU;IACjBd,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CACF;EACDY,UAAU,EAAE;EACZ;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,CAAC,EACD;EACEF,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,iBAAiB;EACvBE,IAAI,eACFhC,OAAA,CAAC3B,OAAO,CAAC2E,kBAAkB;IACzBf,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxD,CACF;EACDY,UAAU,EAAE;EACZ;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,CAAC,EACD;EACEF,KAAK,EAAE,eAAe;EACtB;EACAG,IAAI,eACFhC,OAAA,CAACf,cAAc;IAACgD,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAC1E;EACDY,UAAU,EAAE,SAAS;EACrBQ,UAAU,eACRvC,OAAA,CAACxB,OAAO,CAACgE,gBAAgB;IACvBP,KAAK,EAAE;MACLQ,UAAU,EAAE,MAAM;MAClBP,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EACDuB,UAAU,eACR1C,OAAA,CAACxB,OAAO,CAACmE,cAAc;IACrBV,KAAK,EAAE;MACLQ,UAAU,EAAE,MAAM;MAClBP,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EAEDiB,MAAM,EAAE,CACN;IACEP,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,2BAA2B;IACjCE,IAAI,eACFhC,OAAA,CAAC3B,OAAO,CAAC4E,cAAc;MACrBhB,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,EAAE,eAAe;IACrBE,IAAI,eACFhC,OAAA,CAAC3B,OAAO,CAAC6E,WAAW;MAClBjB,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC;EAAC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACM;IACEU,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE,aAAa;IACnBE,IAAI,eACFhC,OAAA,CAACV,aAAa;MACZ2C,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,2BAA2B;IAClCC,IAAI,EAAE,wBAAwB;IAC9BE,IAAI,eACFhC,OAAA,CAAC3B,OAAO,CAAC8E,UAAU;MACjBlB,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,6BAA6B;IACpCC,IAAI,EAAE,mBAAmB;IACzBE,IAAI,eACFhC,OAAA,CAAC3B,OAAO,CAAC+E,eAAe;MACtBnB,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE,mBAAmB;IACzBE,IAAI,eACFhC,OAAA,CAACR,kBAAkB;MACjByC,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE,4BAA4B;IAClCE,IAAI,eACFhC,OAAA,CAAC1B,OAAO,CAAC+E,aAAa;MACpBpB,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,eACFhC,OAAA,CAACH,OAAO;MACNoC,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,0BAA0B;IACjCC,IAAI,EAAE,2BAA2B;IACjCE,IAAI,eACFhC,OAAA,CAACH,OAAO;MACNoC,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,mBAAmB;IAC1BC,IAAI,EAAE,sBAAsB;IAC5BE,IAAI,eACFhC,OAAA,CAACJ,QAAQ;MACPqC,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC;AAEL,CAAC,EACD;EACEU,KAAK,EAAE,gBAAgB;EACvB;EACAG,IAAI,eACFhC,OAAA,CAACb,WAAW;IAAC8C,KAAK,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,QAAQ,EAAE;IAAO;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CACvE;EACDY,UAAU,EAAE,WAAW;EACvBQ,UAAU,eACRvC,OAAA,CAACxB,OAAO,CAACgE,gBAAgB;IACvBP,KAAK,EAAE;MACLQ,UAAU,EAAE,MAAM;MAClBP,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EACDuB,UAAU,eACR1C,OAAA,CAACxB,OAAO,CAACmE,cAAc;IACrBV,KAAK,EAAE;MACLQ,UAAU,EAAE,MAAM;MAClBP,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;IACZ;EAAE;IAAAnB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EAEDiB,MAAM,EAAE,CACN;IACEP,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE,eAAe;IACrBE,IAAI,eACFhC,OAAA,CAACZ,aAAa;MACZ6C,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,mBAAmB;IACzBE,IAAI,eACFhC,OAAA,CAACzB,OAAO,CAACsE,UAAU;MACjBZ,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE,gBAAgB;IACtBE,IAAI,eACFhC,OAAA,CAAC3B,OAAO,CAACiF,UAAU;MACjBrB,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACEU,KAAK,EAAE,uBAAuB;IAC9BC,IAAI,EAAE,8BAA8B;IACpCE,IAAI,eACFhC,OAAA,CAAC3B,OAAO,CAACkF,UAAU;MACjBtB,KAAK,EAAE;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAO;IAAE;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD;AAAC,IAAAG,EAAA,EAAAG,GAAA,EAAAE,GAAA;AAAA6B,YAAA,CAAAlC,EAAA;AAAAkC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA7B,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}