require("dotenv").config();
const express = require("express");
const mongoose = require("mongoose");
const { ObjectId } = require("mongoose").Types; // Importa o ObjectId do mongoose
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const moment = require("moment");
const momentTz = require("moment-timezone");
const { format } = require("winston");
const DailyRotateFile = require("winston-daily-rotate-file");
moment.locale("pt-br");
var bodyParser = require("body-parser");
const winston = require("winston");
const cors = require("cors");
const axios = require("axios");
const fetch = require("node-fetch");
//const { PythonShell } = require('python-spawn');
const { spawn } = require("child_process");
const PDFDocument = require("pdfkit");
const fs = require("fs");
const path = require("path");
const crypto = require('crypto');
const { sendResetEmail } = require('./endpoints/emailService');
const {
    createIuguPlan,
    getIuguPlans,
    getFilteredIuguPlans,
    deleteIuguPlan,
    createIuguCustomer,
    createIuguSubscription,
    deleteIuguSubscription,
    cancelIuguInvoice,
    checkUserInvoices,
    getCustomerData,
    getInvoiceByID,
    getSubscriptionByID
} = require("./lib/iugu");

const {
    criarRegistroClienteAsaas,
    updateCustomerAsaas,
    getAsaasPlans,
    deleteAsaasPlan,
    createAsaasSubscription,
    getLastPendingInvoice,
    getAllInvoices,
    cancelAsaasInvoice,
    deleteAsaasSubscription,
    getCustomerDataAsaas,
    handleSubscriptionDeleted,
    handlePaymentOverdue,
    handlePaymentReceived,
    removeDiscountFromSubscription
} = require("./lib/asaas");

const AppExpressPedeja = require("./AppExpressPedeja");
console.log("Server running", AppExpressPedeja.AppExpressPedeja.getId());
const app = AppExpressPedeja.AppExpressPedeja.getApp();
const server = AppExpressPedeja.AppExpressPedeja.getServer();
const io = AppExpressPedeja.AppExpressPedeja.getSocket();

//const puppeteer = require('puppeteer');
const { chromium } = require("playwright");
const { PubSub } = require("@google-cloud/pubsub"); // GOOGLE PUB/SUB

const swaggerUi = require("swagger-ui-express");
const swaggerFile = require("./swagger-output.json");
const checkToken = require("./lib/checkToken");
const verifyUserAccess = require("./lib/verifyUserAccess");
const WpMangerLib = require("./lib/whatsappManager");

module.exports = {
    gerarPDF,
    enviarParaImpressao,
};

const WP_MANAGER_ENDPOINT = process.env.WP_MANAGER_ENDPOINT_EVO;
const WP_MANAGER_TOKEN = process.env.WP_MANAGER_TOKEN_EVO;

// Definir webhook baseado no ambiente
let WP_WEBHOOK;
if (process.env.NODE_ENV === 'develop' && process.env.NGROK_URL) {
    WP_WEBHOOK = `${process.env.NGROK_URL}/api/v1/whatsapp/webhook`;
} else {
    WP_WEBHOOK = process.env.WP_WEBHOOK || "https://api.dev.pedeja.chat/api/v1/whatsapp/webhook";
}



const whatsappManager = new WpMangerLib(WP_MANAGER_ENDPOINT, WP_MANAGER_TOKEN);

// ID do admin autorizado
const ADMIN_ID = "65b03c81756df35f6333aa12";

app.use("/docs", swaggerUi.serve, swaggerUi.setup(swaggerFile));
// Middleware para processar dados urlencoded
app.use(express.urlencoded({ extended: true }));

// Configura Bucket google cloud storage
const { Storage } = require("@google-cloud/storage");
const storage = new Storage({
    keyFilename: "./projeto-pedeja-28a38562cd77.json",
});
const bucket = storage.bucket("pedeja-static");
// Instancia o cliente do Google Cloud Pub/Sub com as credenciais
const pubSubClient = new PubSub({
    keyFilename: "./projeto-pedeja-28a38562cd77.json",
});
const bucketPrint = "pedeja-print";
const nomeTopico = "fila-impressao"; // Substitua pelo nome do seu tópico do Pub/Sub
// Função para enviar o arquivo para impressão e publicar a mensagem no Pub/Sub
async function enviarParaImpressao(caminhoArquivo, empresaObjId) {
    const nomeArquivoDestino = path.basename(caminhoArquivo);

    try {
        // Faz o upload do arquivo para o Google Cloud Storage
        await storage.bucket(bucketPrint).upload(caminhoArquivo, {
            destination: nomeArquivoDestino,
        });
        console.log(
            `${caminhoArquivo} uploaded to ${bucketPrint} as ${nomeArquivoDestino}.`
        );

        // Dados que você deseja enviar junto com a notificação
        const dadosMensagem = JSON.stringify({
            bucket: bucketPrint,
            arquivo: nomeArquivoDestino,
            empresaObjId: empresaObjId, // Inclua o empresaObjId na mensagem
        });

        // Cria um buffer a partir dos dados da mensagem
        const dataBuffer = Buffer.from(dadosMensagem);

        // Publica a mensagem no tópico do Pub/Sub com o atributo empresaObjId
        await pubSubClient.topic(nomeTopico).publish(dataBuffer, {
            empresaObjId: String(empresaObjId), // Garante que o valor é uma string
        });

        console.log("Mensagem publicada no Pub/Sub com atributo empresaObjId.");
    } catch (error) {
        console.error("Erro ao enviar para impressão:", error);
    }
}

async function criarAssinaturaFiltrada(
    nomeTopico,
    nomeAssinatura,
    empresaObjId
) {
    try {
        // Cria uma assinatura com um filtro que entrega apenas mensagens com o atributo empresaObjId específico
        const [subscription] = await pubSubClient
            .topic(nomeTopico)
            .createSubscription(nomeAssinatura, {
                filter: `attributes.empresaObjId = "${empresaObjId}"`,
            });

        console.log(
            `Assinatura ${subscription.name} criada com filtro para empresaObjId = "${empresaObjId}"`
        );
    } catch (error) {
        if (error.code === 6) {
            // A assinatura já existe, atualize o filtro, se necessário
            console.log(
                `A assinatura ${nomeAssinatura} já existe. Verifique se o filtro está correto.`
            );
        } else {
            console.error("Erro ao criar a assinatura filtrada:", error);
        }
    }
}

// Criar assinatura manual para empresas
//const empresaObjIdTemp = "65b03b8c756df35f6333a9fb";
//criarAssinaturaFiltrada('fila-impressao', `fila-impressao-sub-${empresaObjIdTemp}`, empresaObjIdTemp);

// Função para fazer upload de imagem no bucket
/*async function uploadImage(imageBuffer, imageName) {
    const file = bucket.file(imageName);
    await file.save(imageBuffer);
    return `https://storage.googleapis.com/${bucket.name}/${file.name}`;
}*/
// Função para fazer upload da imagem base64 para o Google Cloud Storage
async function uploadImage(base64Image, fileName) {
    // Remover o prefixo do Data URL da string base64
    const matches = base64Image.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);

    if (!matches || matches.length !== 3) {
        throw new Error("String base64 inválida");
    }

    // Extrair o conteúdo puro base64
    const imageBuffer = Buffer.from(matches[2], "base64");

    // Criar um stream para o upload
    const file = bucket.file(fileName);
    const stream = file.createWriteStream({
        metadata: {
            contentType: matches[1], // Utiliza o tipo MIME do Data URL
        },
    });

    return new Promise((resolve, reject) => {
        stream.on("error", (err) => reject(err));
        stream.on("finish", () => {
            // O arquivo foi salvo com sucesso
            resolve(`https://storage.googleapis.com/${bucket.name}/${file.name}`);
        });
        stream.end(imageBuffer);
    });
}

async function deleteImage(imgUrl) {
    // Divide a URL em partes, usando '/' como separador
    const parts = imgUrl.split("/");
    // O nome do arquivo é a última parte da URL
    const fileName = parts[parts.length - 1];
    try {
        await storage.bucket(bucket.name).file(fileName).delete();
        console.log(`O arquivo ${fileName} foi deletado do bucket ${bucket.name}.`);
    } catch (error) {
        console.error("ERROR:", error);
    }
}

// Função para fazer upload de PDF para o Google Cloud Storage
async function uploadPDF(pdfBuffer, fileName) {
    try {
        // Criar caminho específico para PDFs de cardápio
        const pdfPath = `cardapios/${fileName}`;
        
        // Criar um stream para o upload
        const file = bucket.file(pdfPath);
        const stream = file.createWriteStream({
            metadata: {
                contentType: 'application/pdf',
            },
        });

        return new Promise((resolve, reject) => {
            stream.on('error', (err) => {
                console.error('Erro no upload do PDF:', err);
                reject(err);
            });
            
            stream.on('finish', () => {
                console.log(`PDF ${fileName} enviado com sucesso para o GCS`);
                const publicUrl = `https://storage.googleapis.com/${bucket.name}/${pdfPath}`;
                resolve({
                    url: publicUrl,
                    fileName: fileName,
                    filePath: pdfPath,
                    fileSize: pdfBuffer.length
                });
            });
            
            stream.end(pdfBuffer);
        });
    } catch (error) {
        console.error('Erro ao fazer upload do PDF:', error);
        throw error;
    }
}

// Função para deletar PDF do Google Cloud Storage
async function deletePDF(pdfUrl) {
    try {
        // Extrair o caminho do arquivo da URL
        const parts = pdfUrl.split('/');
        const bucketIndex = parts.indexOf(bucket.name);
        if (bucketIndex !== -1 && bucketIndex < parts.length - 1) {
            const filePath = parts.slice(bucketIndex + 1).join('/');
            
            await storage.bucket(bucket.name).file(filePath).delete();
            console.log(`PDF ${filePath} foi deletado do bucket ${bucket.name}`);
            return true;
        } else {
            console.error('URL do PDF inválida:', pdfUrl);
            return false;
        }
    } catch (error) {
        console.error('Erro ao deletar PDF:', error);
        return false;
    }
}

// Função para gerar URL assinada para visualização de PDF (opcional, para maior segurança)
async function getPDFSignedUrl(fileName, empresaId) {
    try {
        const pdfPath = `cardapios/${fileName}`;
        const file = bucket.file(pdfPath);
        
        // Gerar URL assinada válida por 1 hora
        const [signedUrl] = await file.getSignedUrl({
            action: 'read',
            expires: Date.now() + 60 * 60 * 1000, // 1 hora
        });
        
        return signedUrl;
    } catch (error) {
        console.error('Erro ao gerar URL assinada:', error);
        throw error;
    }
}

//Config Max Size of the request body
app.use(bodyParser.json({ limit: "50mb" }));

// Configuração do CORS
app.use(
    cors({
        //origin: ["https://dev.pedeja.chat", "http://localhost:3000", "https://app.pedeja.chat", "http://localhost:8080"], // URLs permitidas
        origin: "*", // Permite todas as origens
        methods: ["GET", "HEAD", "PUT", "PATCH", "POST", "DELETE"], // Métodos permitidos
        allowedHeaders: ["Content-Type", "Authorization"], // Cabeçalhos permitidos
    })
);

app.use(express.json()); // Para interpretar JSON no corpo das requisições

// Middleware para servir arquivos estáticos do diretório uploads
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Variável de logs do ambiente
const logger = winston.createLogger({
    level: "info",
    format: format.combine(
        format.timestamp({
            format: () => momentTz().tz("America/Sao_Paulo").format("YYYY-MM-DD"),
        }),
        format.printf((logEvent) => {
            return `${logEvent.timestamp} [${logEvent.level}] [${logEvent.context}] ${logEvent.message} (pid: ${logEvent.pid})`;
        })
    ),
    transports: [
        new DailyRotateFile({
            filename: "logs/%DATE%_server.log",
            datePattern: "YYYY-MM-DD",
            zippedArchive: true,
            maxSize: "20m",
            maxFiles: "14d",
        }),
        new winston.transports.Console({
            level: "debug",
            format: format.printf((logEvent) => {
                return `${logEvent.timestamp} [${logEvent.level}] [${logEvent.context}] ${logEvent.message}`;
            }),
        }),
        new winston.transports.Console({
            level: "error",
            format: format.printf((logEvent) => {
                return `${logEvent.timestamp} [${logEvent.level}] [${logEvent.context}] ${logEvent.message}`;
            }),
        }),
    ],
});

// Libs
const Whatsapp = require("./lib/whatsapp");

// Routes
const whatsapp = require("./endpoints/whatsapp");
const authRoute = require("./endpoints/authRoute");
const ping = require("./endpoints/ping");

const mesas = require("./endpoints/mesas");
const usersRoutes = require("./endpoints/users");

// Models
const Counter = require("./models/Counter");
const User = require("./models/User");
const Empresa = require("./models/Empresa");
const Pedidos = require("./models/Pedidos");
const Categorias = require("./models/Categorias");
const Itens = require("./models/Itens");
const Planos = require("./models/Planos");
const Cliente = require("./models/Cliente");
const Adicionais = require("./models/Adicionais");
const parseToken = require("./lib/parseToken");
const Entregadores = require("./models/Entregadores");
const Caixa = require("./models/Caixa");
const Assinatura = require("./models/Assinatura");
const LeadChannel = require("./models/LeadChannel");
const CompanyResponses = require("./models/CompanyResponses");
const Mesas = require("./models/Mesas");
const Messages = require("./models/Messages");
const { memoryStorage } = require("multer");
//const Cliente = require('./models/Cliente')
//const Vendedor = require('./models/Vendedor')
//const Orcamento = require('./models/Orcamento')

app.set("trust proxy", true);
//Public Route
app.get("/", (req, res) => {
    return res.status(200).json({ msg: "Bem vindo a nossa API!" });
});

//Private Route
app.get("/user/:id", checkToken, async (req, res) => {
    const id = req.params.id;

    //Checar se usuario existe
    const user = await User.findById(
        id,
        "-password -inativo -role -bloqueado -vinculo_empresa"
    );

    if (!user) {
        return res.status(404).json({ msg: "Usuário não encontrado!" });
    }

    return res.status(200).json({ user });
});

// Rota de teste para verificar se conseguimos acessar arquivos PDF (SEM AUTENTICAÇÃO)
app.get('/test-pdf-access', async (req, res) => {
  try {
    console.log('🧪 Testando acesso aos PDFs...');
    
    // Listar arquivos no diretório cardapios
    const cardapiosDir = path.join(__dirname, 'uploads', 'cardapios');
    console.log('📂 Diretório cardapios:', cardapiosDir);
    
    if (fs.existsSync(cardapiosDir)) {
      const files = fs.readdirSync(cardapiosDir);
      console.log('📁 Arquivos encontrados:', files);
      
      if (files.length > 0) {
        const firstFile = files[0];
        const filePath = path.join(cardapiosDir, firstFile);
        console.log('📄 Primeiro arquivo:', filePath);
        console.log('✅ Arquivo existe:', fs.existsSync(filePath));
        
        res.json({
          success: true,
          directory: cardapiosDir,
          files: files,
          firstFile: firstFile,
          firstFilePath: filePath,
          fileExists: fs.existsSync(filePath),
          directAccess: `http://localhost:5000/uploads/cardapios/${firstFile}`
        });
      } else {
        res.json({
          success: true,
          directory: cardapiosDir,
          files: [],
          message: 'Nenhum arquivo encontrado'
        });
      }
    } else {
      res.json({
        success: false,
        directory: cardapiosDir,
        message: 'Diretório não existe'
      });
    }
  } catch (error) {
    console.error('❌ Erro no teste:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ROUTES ADMIN
app.use(checkToken);

// ROUTES MESAS
app.use(mesas);

// ROUTES USERS
app.use(usersRoutes);

//get Empresa for cardapio
app.get("/empresaWithObjId/:id", async (req, res) => {
    const id = req.params.id;

    //Checar se usuario existe
    const empresa = await Empresa.findById(
        id,
        "-inativo -bloqueado -tipo_impressao -createdBy -createdAt -updatedAt -deletedAt -__v -_id -id_grupo -type -status_printer -whatsapp"
    );

    if (!empresa) {
        return res.status(404).json({ msg: "Empresa não encontrada!" });
    }

    return res.status(200).json({ empresa });
});

/*app.get('/api/credenciais-google', checkToken, (req, res) => {
    // Assegura que somente requisições autenticadas cheguem até aqui
    try {
        // Caminho absoluto para o arquivo de credenciais
        const credenciaisPath = path.resolve(__dirname, 'projeto-pedeja-28a38562cd77.json');
        const credenciaisData = fs.readFileSync(credenciaisPath, 'utf8');
        const credenciais = JSON.parse(credenciaisData);
        console.log("credenciais solicitadas, enviando resposta...")
        // Envie as credenciais como JSON
        res.json(credenciais);
    } catch (error) {
        console.error('Erro ao ler credenciais:', error);
        res.status(500).send('Erro ao obter credenciais.');
    };
});*/
app.get("/api/credenciais-google", checkToken, async (req, res) => {
    try {
        // Extrai o ID da empresa a partir do token ou do corpo da requisição (ajuste conforme necessário)
        const empresaObjId = req.query.empresaObjId; // ajuste conforme o formato do token ou requisição

        // Busca a empresa no banco de dados para obter o nome da assinatura
        const empresa = await Empresa.findById(empresaObjId);
        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada" });
        }

        // Caminho absoluto para o arquivo de credenciais
        const credenciaisPath = path.resolve(
            __dirname,
            "projeto-pedeja-28a38562cd77.json"
        );
        const credenciaisData = fs.readFileSync(credenciaisPath, "utf8");
        const credenciais = JSON.parse(credenciaisData);

        console.log("Credenciais solicitadas, enviando resposta...");

        // Inclua o nome da assinatura `fila_impressao_sub` na resposta
        res.json({
            ...credenciais,
            fila_impressao_sub: empresa.fila_impressao_sub,
        });
    } catch (error) {
        console.error("Erro ao ler credenciais ou buscar empresa:", error);
        res.status(500).send("Erro ao obter credenciais.");
    }
});

// Run Python AnotaAi
//url = "https://api.anota.ai/clientauth/nm-category/v3/?pdv=true"
function runPythonAnotaAi(url, id_empresa) {
    const options = {
        cwd: path.resolve("./"),
        env: {
            PYTHONPATH: path.resolve("./"),
        },
    };

    const python = spawn(
        "python",
        ["scrappingCardapioAnotaAi.py", url, id_empresa],
        options
    );

    python.stdout.on("data", (data) => {
        console.log("stdout: " + data.toString());
    });

    python.stderr.on("data", (data) => {
        console.log("stderr: " + data.toString());
    });

    python.on("close", (code) => {
        console.log("child process exited with code " + code.toString());
    });
}

// Run Python Ifood
//url = "https://api.anota.ai/clientauth/nm-category/v3/?pdv=true"
function runPythonIfood(url, id_empresa) {
    const options = {
        cwd: path.resolve("./"),
        env: {
            PYTHONPATH: path.resolve("./"),
        },
    };

    const python = spawn(
        "python",
        ["scrappingCardapioIfood.py", url, id_empresa],
        options
    );

    python.stdout.on("data", (data) => {
        console.log("stdout: " + data.toString());
    });

    python.stderr.on("data", (data) => {
        console.log("stderr: " + data.toString());
    });

    python.on("close", (code) => {
        console.log("child process exited with code " + code.toString());
    });
}

// Receber requisição do portal para rodar o script de scrapping e retornar os dados da API Anota.ai
app.post("/import-cardapio-anotaai/", async (req, res) => {
    try {
        const { url_import, id_empresa } = req.body;
        //const id = req.params.id
        console.log("O QUE CHEGOU DA API>", url_import, id_empresa);
        runPythonAnotaAi(url_import, id_empresa);
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Receber requisição do portal para rodar o script de scrapping e retornar os dados da API Ifood
app.post("/import-cardapio-ifood/", async (req, res) => {
    try {
        const { url_import, id_empresa } = req.body;
        //const id = req.params.id
        console.log("O QUE CHEGOU DA API>", url_import, id_empresa);
        runPythonIfood(url_import, id_empresa);
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Rota para verificar o status da importação
app.get("/check-import-status/:idEmpresa", async (req, res) => {
    const { idEmpresa } = req.params;
    try {
        const empresa = await Empresa.findOne({ id_empresa: idEmpresa });
        if (!empresa) {
            return res.status(404).json({ message: "Empresa não encontrada." });
        }
        res.json({ importacao_finalizada: empresa.importacao_finalizada });
    } catch (error) {
        console.error("Erro ao verificar status:", error);
        res.status(500).json({ message: "Erro ao verificar status." });
    }
});

// Rota para atualizar a flag de conclusão da importação
app.post("/update-import-flag/:idEmpresa", async (req, res) => {
    const { idEmpresa } = req.params;
    const { importacao_finalizada } = req.body;

    try {
        const empresa = await Empresa.findOneAndUpdate(
            { id_empresa: idEmpresa },
            { importacao_finalizada },
            { new: true }
        );
        if (!empresa) {
            return res.status(404).json({ message: "Empresa não encontrada." });
        }
        res.json({
            message: "Flag de importação atualizada com sucesso.",
            empresa,
        });
    } catch (error) {
        console.error("Erro ao atualizar flag:", error);
        res.status(500).json({ message: "Erro ao atualizar flag." });
    }
});

// Rota para obter o progresso atual da importação
app.get("/progress-import/:idEmpresa", async (req, res) => {
    const { idEmpresa } = req.params;
    try {
        // Validar se idEmpresa está presente
        if (!idEmpresa) {
            return res.status(400).json({ message: "ID da empresa é obrigatório." });
        }

        const empresa = await Empresa.findOne({ id_empresa: idEmpresa });
        if (!empresa) {
            return res.status(404).json({ message: "Empresa não encontrada." });
        }
        
        // Retornar dados completos incluindo status_importacao
        const response = {
            importacao_finalizada: empresa.importacao_finalizada,
            status_importacao: empresa.status_importacao || 'idle',
            progresso_importacao: empresa.progresso_importacao || {
                em_andamento: false,
                porcentagem: 0,
                etapa_atual: '',
                total_categorias: 0,
                categorias_processadas: 0,
                total_itens: 0,
                itens_processados: 0,
                total_adicionais: 0,
                adicionais_processados: 0,
                data_inicio: null,
                data_fim: null,
                tipo_importacao: '',
                processo_id: null,
                ultima_atualizacao: null
            }
        };

        res.json(response);
    } catch (error) {
        console.error("Erro ao obter progresso da importação:", error);
        res.status(500).json({ 
            message: "Erro interno do servidor ao obter progresso da importação.",
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Rota para resetar o status de importação em caso de erro
app.post("/reset-import-status/:idEmpresa", async (req, res) => {
    const { idEmpresa } = req.params;
    try {
        // Validar se idEmpresa está presente
        if (!idEmpresa) {
            return res.status(400).json({ message: "ID da empresa é obrigatório." });
        }

        const empresa = await Empresa.findOne({ id_empresa: idEmpresa });
        if (!empresa) {
            return res.status(404).json({ message: "Empresa não encontrada." });
        }

        // Resetar status de importação para 'idle'
        await Empresa.findOneAndUpdate(
            { id_empresa: idEmpresa },
            {
                'status_importacao': 'idle',
                'progresso_importacao.em_andamento': false,
                'progresso_importacao.porcentagem': 0,
                'progresso_importacao.etapa_atual': '',
                'progresso_importacao.ultima_atualizacao': new Date()
            }
        );

        res.json({ 
            message: "Status de importação resetado com sucesso.",
            status_importacao: 'idle'
        });
    } catch (error) {
        console.error("Erro ao resetar status de importação:", error);
        res.status(500).json({ 
            message: "Erro interno do servidor ao resetar status de importação.",
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

app.post("/update-company-responses", async (req, res) => {
    try {
        const { company_id, responses } = req.body;

        if (!company_id || !responses) {
            return res.status(400).json({ message: "Dados incompletos." });
        }

        const result = await CompanyResponses.updateOne(
            { companyId: company_id }, // Filtra pelo ID da empresa
            { $set: { responses: responses } } // Atualiza as respostas
        );

        if (result.modifiedCount === 0) {
            return res
                .status(404)
                .json({ message: "Nenhuma resposta encontrada para atualizar." });
        }

        res.status(200).json({ message: "Respostas atualizadas com sucesso!" });
    } catch (error) {
        console.error("Erro ao atualizar respostas:", error);
        res.status(500).json({ message: "Erro ao atualizar respostas." });
    }
});

// Atualiza as formas de pagamento da empresa
app.put('/empresa/:id/formas-pagamento', async (req, res) => {
    try {
        const { formas_pagamento } = req.body;
        const { id } = req.params;

        if (!formas_pagamento || !Array.isArray(formas_pagamento)) {
            return res.status(400).json({ msg: "Formas de pagamento inválidas." });
        }

        // Atualiza no banco de dados
        const empresa = await Empresa.findByIdAndUpdate(id, { formas_pagamento }, { new: true });

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada." });
        }

        res.status(200).json({ msg: "Formas de pagamento atualizadas com sucesso!", empresa });
    } catch (error) {
        console.error(error);
        res.status(500).json({ msg: "Erro ao atualizar as formas de pagamento." });
    }
});

// Atualiza as configurações de entrega e retirada da empresa
app.put('/empresa/:id/configuracoes-entrega', async (req, res) => {
    try {
        const { entrega_disabled, retirada_disabled } = req.body;
        const { id } = req.params;

        if (typeof entrega_disabled !== 'boolean' || typeof retirada_disabled !== 'boolean') {
            return res.status(400).json({ msg: "Configurações inválidas. Os valores devem ser booleanos." });
        }

        // Atualiza no banco de dados
        const empresa = await Empresa.findByIdAndUpdate(
            id, 
            { entrega_disabled, retirada_disabled }, 
            { new: true }
        );

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada." });
        }

        res.status(200).json({ msg: "Configurações de entrega atualizadas com sucesso!", empresa });
    } catch (error) {
        console.error(error);
        res.status(500).json({ msg: "Erro ao atualizar as configurações de entrega." });
    }
});


app.put("/update-question-active", async (req, res) => {
    const { questionIdentifier, companyId, active } = req.body;

    if (!questionIdentifier || !companyId) {
        return res.status(400).json({ error: "Dados incompletos." });
    }

    try {
        const updated = await CompanyResponses.updateOne(
            { companyId, "responses.questionIdentifier": questionIdentifier },
            { $set: { "responses.$.active": active } }
        );

        if (updated.modifiedCount > 0) {
            res.status(200).json({ message: "Disponibilidade atualizada com sucesso." });
        } else {
            res.status(404).json({ error: "Pergunta não encontrada ou já atualizada." });
        }
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: "Erro ao atualizar disponibilidade." });
    }
});

// Registrar usuario
app.post("/auth/register", async (req, res) => {
    //CRIAR USER COM SEQID
    const {
        createdBy,
        name,
        email,
        password,
        confirmpassword,
        vinculo_empresa,
        role,
    } = req.body;

    //validacoes
    if (!createdBy) {
        return res
            .status(422)
            .json({ msg: "O ID de relacionamento é obrigatório!" });
    }
    if (!name) {
        return res.status(422).json({ msg: "O nome é obrigatório!" });
    }
    if (!email) {
        return res.status(422).json({ msg: "O email é obrigatório!" });
    }
    if (!password) {
        return res.status(422).json({ msg: "A senha é obrigatória!" });
    }
    if (password !== confirmpassword) {
        return res.status(422).json({ msg: "As senhas não conferem!" });
    }
    if (!role) {
        return res
            .status(422)
            .json({ msg: "É obrigatório informar o papel do usuário!" });
    }
    if (!vinculo_empresa) {
        return res.status(422).json({ msg: "É obrigatório o vínculo da empresa!" });
    }

    //Checar se usuario existe
    const userExists = await User.findOne({ email: email, inativo: false });

    if (userExists) {
        return res
            .status(422)
            .json({ msg: "Usuário já existe, utilize outro e-mail!" });
    }

    const companyExists = await Empresa.findOne({
        id_empresa: vinculo_empresa,
        inativo: false,
    });

    if (!companyExists) {
        return res.status(422).json({ msg: "Empresa não encontrada!" });
    }

    //Criar senha
    const salt = await bcrypt.genSalt(12);
    const passwordHash = await bcrypt.hash(password, salt);

    //Verificar SeqID e atribuir ao id_user
    Counter.findOneAndUpdate(
        { id: "id_user" },
        { $inc: { seq: 1 } },
        { new: true },
        async (err, counterData) => {
            let seqId;
            if (err) {
                console.log(err);
            }
            if (counterData == null) {
                const newVal = new Counter({ id: "id_user", seq: 1 });
                newVal.save();
                seqId = 1;
            } else {
                seqId = counterData.seq;
            }

            //Capturar Data Atual e formatar
            var data = moment().format();
            console.log(data);
            //Criar usuário
            const user = new User({
                id_user: seqId,
                createdBy,
                name,
                email,
                password: passwordHash,
                vinculo_empresa: companyExists.cnpj,
                role,
                createdAt: data,
                inativo: false,
                bloqueado: false,
            });

            try {
                await user.save();

                return res.status(201).json({ msg: "Usuário criado com sucesso!" });
            } catch (error) {
                console.log(error);

                res.status(500).json({
                    msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                });
            }
        }
    );
});


// Rota para solicitar recuperação de senha
app.post('/forgot-password', async (req, res) => {
    const { email } = req.body;

    try {
        // Busca o usuário pelo e-mail
        const user = await User.findOne({ email });
        if (!user) {
            return res.status(404).json({ msg: 'Usuário não encontrado!' });
        }

        // Gera um token único e define o tempo de expiração
        const token = crypto.randomBytes(32).toString('hex');
        const tokenExpiration = Date.now() + 3600000; // Token válido por 1 hora

        // Atualiza o usuário com o token e a expiração
        user.resetToken = token;
        user.resetTokenExpiration = tokenExpiration;
        await user.save();

        // Envia o e-mail de redefinição de senha
        await sendResetEmail(email, token);

        res.status(200).json({ msg: 'E-mail de recuperação enviado!' });
    } catch (error) {
        console.error('Erro ao solicitar recuperação de senha:', error);
        res.status(500).json({ msg: 'Erro interno ao processar a solicitação. Tente novamente mais tarde.' });
    }
});

// Rota para redefiniçao da senha
app.post('/reset-password/:token', async (req, res) => {
    const { token } = req.params;
    const { newPassword, confirmPassword } = req.body;

    try {
        // Valida se as senhas coincidem
        if (newPassword !== confirmPassword) {
            return res.status(400).json({ msg: 'As senhas não coincidem!' });
        }

        // Busca o usuário pelo token e verifica se ainda está válido
        const user = await User.findOne({
            resetToken: token,
            resetTokenExpiration: { $gt: Date.now() }, // Token deve estar dentro do prazo de validade
        });

        if (!user) {
            return res.status(400).json({ msg: 'Token inválido ou expirado!' });
        }

        // Cria o hash da nova senha
        const salt = await bcrypt.genSalt(12);
        const passwordHash = await bcrypt.hash(newPassword, salt);

        // Atualiza a senha e limpa o token de redefinição
        user.password = passwordHash;
        user.resetToken = undefined;
        user.resetTokenExpiration = undefined;

        await user.save();

        res.status(200).json({ msg: 'Senha redefinida com sucesso!' });
    } catch (error) {
        console.error('Erro ao redefinir senha:', error);
        res.status(500).json({ msg: 'Erro interno ao redefinir senha. Tente novamente mais tarde.' });
    }
});

// Registrar Entregador
app.post("/registerEntregador", async (req, res) => {
    //CRIAR USER COM SEQID
    const { createdBy, id_empresa, name, telefone, veiculo, placa } = req.body;

    //validacoes
    if (!createdBy) {
        return res
            .status(422)
            .json({ msg: "O ID de relacionamento é obrigatório!" });
    }
    if (!name) {
        return res.status(422).json({ msg: "O nome é obrigatório!" });
    }
    if (!veiculo) {
        return res.status(422).json({ msg: "O veiculo é obrigatório!" });
    }
    if (!telefone) {
        return res.status(422).json({ msg: "O telefone é obrigatório!" });
    }
    if (!id_empresa) {
        return res.status(422).json({ msg: "É obrigatório o vínculo da empresa!" });
    }

    //Verificar SeqID e atribuir ao id_entregador
    Counter.findOneAndUpdate(
        { id: "id_entregador" },
        { $inc: { seq: 1 } },
        { new: true },
        async (err, counterData) => {
            let seqId;
            if (err) {
                console.log(err);
            }
            if (counterData == null) {
                const newVal = new Counter({ id: "id_entregador", seq: 1 });
                newVal.save();
                seqId = 1;
            } else {
                seqId = counterData.seq;
            }

            //Capturar Data Atual e formatar
            var data = moment().format();
            console.log(data);
            //Criar entregador
            const entregador = new Entregadores({
                id_entregador: seqId,
                id_empresa,
                createdBy,
                name,
                telefone,
                veiculo,
                placa,
                createdAt: data,
                inativo: false,
                bloqueado: false,
            });
            try {
                await entregador.save();
                return res.status(201).json({ msg: "Entregador criado com sucesso!" });
            } catch (error) {
                console.log(error);
                res.status(500).json({
                    msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                });
            }
        }
    );
});

// Atualizar Usuário
app.post("/update-user/:id", checkToken, async (req, res) => {
    try {
        const { id, email, password, name } = req.body;

        //validacoes
        if (!id) {
            return res
                .status(422)
                .json({ msg: "É necessário informar o ID para Editar os dados!" });
        }

        const responseUserEmailExist = await User.find({ email: email }, "_id");
        //console.log(responseUserEmailExist[0]);
        if (
            responseUserEmailExist.length > 0 &&
            id != responseUserEmailExist[0]._id
        ) {
            //console.log("Usuário DIFERENTE, PROIBIDO ATUALIZAR");
            return res.status(400).json({ msg: "Este e-mail já está cadastrado!" });
        } else console.log("Nenhum problema, pode seguir");

        let passwordHash = null;
        if (password) {
            //Criar senha
            const salt = await bcrypt.genSalt(12);
            passwordHash = await bcrypt.hash(password, salt);
        }

        //Capturar Data Atual e formatar
        var data = moment().format();
        console.log(data);

        await User.find({ _id: id }, async function (err, datacall) {
            if (!err) {
                const response = await User.findByIdAndUpdate(
                    { _id: id },
                    {
                        $set: {
                            email: email || datacall.email,
                            password: passwordHash || datacall.password,
                            name: name || datacall.name,
                            updatedAt: data,
                        },
                    },
                    {
                        new: true,
                    }
                );

                //return res.status(200).json({response});
                return res.status(200).json({ msg: "Dados Atualizados com Sucesso!" });
            }
        }).clone();
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Atualizar Entregador
app.post("/update-entregador/:id", checkToken, async (req, res) => {
    try {
        const { idToEdit, id_empresa, name, veiculo, telefone, placa } = req.body;

        //validacoes
        if (!idToEdit) {
            return res
                .status(422)
                .json({ msg: "É necessário informar o ID para Editar os dados!" });
        }

        //Capturar Data Atual e formatar
        var data = moment().format();

        await Entregadores.find(
            { _id: idToEdit, id_empresa: id_empresa },
            async function (err, datacall) {
                if (!err) {
                    const response = await Entregadores.findByIdAndUpdate(
                        { _id: idToEdit, id_empresa: id_empresa },
                        {
                            $set: {
                                name: name || datacall.name,
                                veiculo: veiculo || datacall.veiculo,
                                telefone: telefone || datacall.telefone,
                                placa: placa || datacall.placa,
                                updatedAt: data,
                            },
                        },
                        {
                            new: true,
                        }
                    );

                    return res
                        .status(200)
                        .json({ msg: "Dados Atualizados com Sucesso!" });
                }
            }
        ).clone();
    } catch (err) {
        console.log(err);
    }
});

// Atualizar Empresa
app.post("/update-empresa/:id", checkToken, async (req, res) => {
    try {
        const {
            _id,
            id,
            cnpj,
            name,
            razao,
            email,
            cep,
            estado,
            municipio,
            bairro,
            complemento,
            telefone,
            celular,
            tempoBalcaoMin,
            tempoBalcaoMax,
            tempoEntregaMin,
            tempoEntregaMax,
            tipo_impressao,
            region_type_delivery,
            type_of_region,
            address_number,
            latitude,
            longitude,
        } = req.body;
        console.log(
            tempoBalcaoMin,
            tempoBalcaoMax,
            tempoEntregaMin,
            tempoEntregaMax
        );
        //validacoes
        if (!id) {
            return res
                .status(422)
                .json({ msg: "É necessário informar o ID para Editar os dados!" });
        }

        if (!_id) {
            return res
                .status(422)
                .json({
                    msg: "É necessário informar o ObjectId para Editar os dados!",
                });
        }

        const responseEmpresaEmailExist = await Empresa.find(
            { email: email },
            "id_empresa"
        );
        const responseEmpresaCnpjExist = await Empresa.find(
            { cnpj: cnpj },
            "id_empresa"
        );
        console.log(responseEmpresaEmailExist[0]);
        console.log(id);
        if (
            responseEmpresaCnpjExist.length > 0 &&
            id != responseEmpresaCnpjExist[0].id_empresa
        ) {
            //console.log("Usuário DIFERENTE, PROIBIDO ATUALIZAR");
            return res.status(400).json({ msg: "Este CNPJ já está cadastrado!" });
        }
        if (
            responseEmpresaEmailExist.length > 0 &&
            id != responseEmpresaEmailExist[0].id_empresa
        ) {
            //console.log("Usuário DIFERENTE, PROIBIDO ATUALIZAR");
            return res.status(400).json({ msg: "Este e-mail já está cadastrado!" });
        } else console.log("Nenhum problema, pode seguir");

        //Capturar Data Atual e formatar
        var data = moment().format();
        console.log(data);

        console.log("region_type_delivery>", region_type_delivery);

        await Empresa.find(
            { id_empresa: id, _id: _id },
            async function (err, datacall) {
                if (!err) {
                    // Verificar se mudou o tipo de região e limpar dados do tipo anterior
                    let updateFields = {
                        cnpj: cnpj || datacall.cnpj,
                        name: name || datacall.name,
                        razao: razao || datacall.razao,
                        email: email || datacall.email,
                        cep: cep || datacall.cep,
                        estado: estado || datacall.estado,
                        municipio: municipio || datacall.municipio,
                        bairro: bairro || datacall.bairro,
                        address_number: address_number || datacall.address_number,
                        complemento: complemento || datacall.complemento,
                        telefone: telefone || datacall.telefone,
                        celular: celular || datacall.celular,
                        tempoBalcaoMin: tempoEntregaMin, // || datacall.tempoBalcaoMin,
                        tempoBalcaoMax: tempoBalcaoMax, // || datacall.tempoBalcaoMax,
                        tempoEntregaMin: tempoEntregaMin, // || datacall.tempoEntregaMin,
                        tempoEntregaMax: tempoEntregaMax, // || datacall.tempoEntregaMax,
                        tipo_impressao: tipo_impressao || datacall.tipo_impressao,
                        region_type_delivery: region_type_delivery || datacall.region_type_delivery,
                        type_of_region: type_of_region || region_type_delivery || datacall.type_of_region || datacall.region_type_delivery,
                        latitude: latitude || datacall.latitude,
                        longitude: longitude || datacall.longitude,
                        updatedAt: data,
                    };

                    // Se mudou o tipo de região, limpar os dados do tipo anterior
                    const novoTipo = type_of_region || region_type_delivery;
                    const tipoAtual = datacall.type_of_region || datacall.region_type_delivery;
                    
                    if (novoTipo && tipoAtual && novoTipo !== tipoAtual) {
                        console.log(`🔄 Mudando tipo de região de ${tipoAtual} para ${novoTipo}`);
                        
                        if (tipoAtual === 'raio') {
                            console.log('🗑️ Limpando raios_entrega do banco');
                            updateFields.raios_entrega = [];
                        } else if (tipoAtual === 'bairro') {
                            console.log('🗑️ Limpando bairros_entrega do banco');
                            updateFields.bairros_entrega = [];
                        }
                    }

                    const response = await Empresa.findOneAndUpdate(
                        { id_empresa: id, _id: _id },
                        {
                            $set: updateFields,
                        },
                        {
                            new: true,
                        }
                    );

                    //return res.status(200).json({response});
                    return res
                        .status(200)
                        .json({ msg: "Dados Atualizados com Sucesso!" });
                }
            }
        ).clone();
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Atualizar Tempo de Entrega da Empresa
app.post("/update-empresaTempoEntrega/:id", checkToken, async (req, res) => {
    try {
        const {
            _id,
            id,
            tempoBalcaoMin,
            tempoBalcaoMax,
            tempoEntregaMin,
            tempoEntregaMax,
            tipo_impressao,
        } = req.body;
        //console.log(_id, id, tempoBalcaoMin, tempoBalcaoMax, tempoEntregaMin, tempoEntregaMax, tipo_impressao);
        //validacoes
        if (!id) {
            return res
                .status(422)
                .json({ msg: "É necessário informar o ID para Editar os dados!" });
        }

        if (!_id) {
            return res
                .status(422)
                .json({
                    msg: "É necessário informar o ObjectId para Editar os dados!",
                });
        }

        //Capturar Data Atual e formatar
        var data = moment().format();
        //console.log(data);

        await Empresa.find(
            { id_empresa: id, _id: _id },
            async function (err, datacall) {
                if (!err) {
                    const response = await Empresa.findOneAndUpdate(
                        { id_empresa: id, _id: _id },
                        {
                            $set: {
                                tempoBalcaoMin: tempoBalcaoMin || datacall.tempoBalcaoMin,
                                tempoBalcaoMax: tempoBalcaoMax || datacall.tempoBalcaoMax,
                                tempoEntregaMin: tempoEntregaMin || datacall.tempoEntregaMin,
                                tempoEntregaMax: tempoEntregaMax || datacall.tempoEntregaMax,
                                tipo_impressao: tipo_impressao || datacall.tipo_impressao,
                                updatedAt: data,
                            },
                        },
                        {
                            new: true,
                        }
                    );

                    //return res.status(200).json({response});
                    return res
                        .status(200)
                        .json({ msg: "Dados Atualizados com Sucesso!" });
                }
            }
        ).clone();
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Atualizar Horario de Funcionamento da Empresa
app.post(
    "/update-empresaHorarioFuncionamento/:id",
    checkToken,
    async (req, res) => {
        try {
            const { _id, id, status_loja, horario_funcionamento, timezone } =
                req.body;
            //console.log(_id, id, tempoBalcaoMin, tempoBalcaoMax, tempoEntregaMin, tempoEntregaMax, tipo_impressao);
            //validacoes
            if (!id) {
                return res
                    .status(422)
                    .json({ msg: "É necessário informar o ID para Editar os dados!" });
            }

            if (!_id) {
                return res
                    .status(422)
                    .json({
                        msg: "É necessário informar o ObjectId para Editar os dados!",
                    });
            }

            //Capturar Data Atual e formatar
            var data = moment().format();
            //console.log(data);

            await Empresa.find(
                { id_empresa: id, _id: _id },
                async function (err, datacall) {
                    if (!err) {
                        const response = await Empresa.findOneAndUpdate(
                            { id_empresa: id, _id: _id },
                            {
                                $set: {
                                    status_loja: status_loja || datacall.status_loja,
                                    horario_funcionamento:
                                        horario_funcionamento || datacall.horario_funcionamento,
                                    timezone: timezone || datacall.timezone,
                                    updatedAt: data,
                                },
                            },
                            {
                                new: true,
                            }
                        );

                        //return res.status(200).json({response});
                        return res
                            .status(200)
                            .json({ msg: "Dados Atualizados com Sucesso!" });
                    }
                }
            ).clone();
        } catch (err) {
            //return res.status(400).json({ error: err});
            console.log(err);
        }
    }
);

// Atualizar Raio de Entrega Empresa
app.post("/update-raioEntrega/:id", checkToken, async (req, res) => {
    try {
        const { _id, id, raio_entrega, valor_entrega } = req.body;

        //validacoes
        if (!id) {
            return res
                .status(422)
                .json({ msg: "É necessário informar o ID para Editar os dados!" });
        }

        if (!_id) {
            return res
                .status(422)
                .json({
                    msg: "É necessário informar o ObjectId para Editar os dados!",
                });
        }

        // Verificar se o raio de entrega já existe na base de dados
        const empresa = await Empresa.findOne({ id_empresa: id });
        if (empresa) {
            const raioExistente = empresa.raios_entrega.some(
                (r) => r.raio_entrega === raio_entrega
            );
            if (raioExistente) {
                return res
                    .status(201)
                    .json({ msg: "Esse raio de entrega já está cadastrado." });
            }
        }

        //Capturar Data Atual e formatar
        var data = moment().format();
        console.log(data);

        // Atualizar o array raio_entrega
        const updatedEmpresa = await Empresa.findOneAndUpdate(
            { id_empresa: id },
            {
                $push: {
                    raios_entrega: {
                        raio_entrega,
                        valor_entrega,
                    },
                },
                $currentDate: {
                    updatedAt: true,
                },
            },
            { new: true }
        );

        if (!updatedEmpresa) {
            return res.status(404).json({ msg: "Empresa não encontrada." });
        }

        res.status(200).json({ msg: "Dados atualizados com sucesso." });
    } catch (err) {
        console.log(err);
        res.status(500).json({ msg: "Erro ao atualizar os dados." });
    }
});

// Atualizar Bairro de Entrega Empresa
app.post("/update-bairroEntrega/:id", checkToken, async (req, res) => {
    try {
        const { _id, id, bairro_entrega, valor_entrega } = req.body;

        //validacoes
        if (!id) {
            return res
                .status(422)
                .json({ msg: "É necessário informar o ID para Editar os dados!" });
        }

        if (!_id) {
            return res
                .status(422)
                .json({
                    msg: "É necessário informar o ObjectId para Editar os dados!",
                });
        }

        // Verificar se o raio de entrega já existe na base de dados
        const empresa = await Empresa.findOne({ id_empresa: id });
        if (empresa) {
            const bairroExistente = empresa.bairros_entrega.some(
                (r) => r.bairro_entrega === bairro_entrega
            );
            if (bairroExistente) {
                return res
                    .status(201)
                    .json({ msg: "Esse bairro de entrega já está cadastrado." });
            }
        }

        //Capturar Data Atual e formatar
        var data = moment().format();
        console.log(data);

        // Atualizar o array raio_entrega
        const updatedEmpresa = await Empresa.findOneAndUpdate(
            { id_empresa: id },
            {
                $push: {
                    bairros_entrega: {
                        bairro_entrega,
                        valor_entrega,
                    },
                },
                $currentDate: {
                    updatedAt: true,
                },
            },
            { new: true }
        );

        if (!updatedEmpresa) {
            return res.status(404).json({ msg: "Empresa não encontrada." });
        }

        res.status(200).json({ msg: "Dados atualizados com sucesso." });
    } catch (err) {
        console.log(err);
        res.status(500).json({ msg: "Erro ao atualizar os dados." });
    }
});

// Deletar raio de entrega selecionado
app.post("/delete-raioEntrega/:id", checkToken, async (req, res) => {
    try {
        const { id_empresa, id_raio_entrega } = req.body;

        // Check if the id_empresa and id_raio_entrega parameters are being passed correctly
        console.log(
            `id_empresa: ${id_empresa}, id_raio_entrega: ${id_raio_entrega}`
        );

        // Check if the Empresa document with the specified id_empresa exists
        const empresa = await Empresa.findOne({ id_empresa: id_empresa });
        if (!empresa) {
            console.log(`Empresa with id_empresa ${id_empresa} not found.`);
            return res.status(404).json({ msg: "Empresa não encontrada" });
        }

        // Check if the delivery radius with the specified _id exists in the raios_entrega array
        const deliveryRadius = empresa.raios_entrega.find(
            (r) => r._id.toString() === id_raio_entrega
        );
        if (!deliveryRadius) {
            console.log(
                `Delivery radius with _id ${id_raio_entrega} not found in Empresa with id_empresa ${id_empresa}.`
            );
            return res.status(404).json({ msg: "Raio de entrega não encontrado" });
        }

        // Remove the delivery radius from the array
        const updatedEmpresa = await Empresa.updateOne(
            { id_empresa: id_empresa },
            { $pull: { raios_entrega: { _id: id_raio_entrega } } }
        );

        if (updatedEmpresa.modifiedCount === 0) {
            return res.status(404).json({ msg: "Empresa not found." });
        }

        console.log("updatedEmpresa.modifiedCount>", updatedEmpresa.modifiedCount);
        return res
            .status(200)
            .json({ msg: "Raio de entrega deletado com sucesso" });
    } catch (err) {
        console.log(err);
        res.status(500).json({ msg: "Erro ao deletar o raio de entrega" });
    }
});

// Deletar raio de entrega selecionado
app.post("/delete-bairroEntrega/:id", checkToken, async (req, res) => {
    try {
        const { id_empresa, id_bairro_entrega } = req.body;

        // Check if the id_empresa and id_bairro_entrega parameters are being passed correctly
        console.log(
            `id_empresa: ${id_empresa}, id_bairro_entrega: ${id_bairro_entrega}`
        );

        // Check if the Empresa document with the specified id_empresa exists
        const empresa = await Empresa.findOne({ id_empresa: id_empresa });
        if (!empresa) {
            console.log(`Empresa with id_empresa ${id_empresa} not found.`);
            return res.status(404).json({ msg: "Empresa não encontrada" });
        }

        // Check if the delivery radius with the specified _id exists in the bairros_entrega array
        const deliveryRadius = empresa.bairros_entrega.find(
            (r) => r._id.toString() === id_bairro_entrega
        );
        if (!deliveryRadius) {
            console.log(
                `Delivery Bairro with _id ${id_bairro_entrega} not found in Empresa with id_empresa ${id_empresa}.`
            );
            return res.status(404).json({ msg: "Raio de entrega não encontrado" });
        }

        // Remove the delivery radius from the array
        const updatedEmpresa = await Empresa.updateOne(
            { id_empresa: id_empresa },
            { $pull: { bairros_entrega: { _id: id_bairro_entrega } } }
        );

        if (updatedEmpresa.modifiedCount === 0) {
            return res.status(404).json({ msg: "Empresa not found." });
        }

        console.log("updatedEmpresa.modifiedCount>", updatedEmpresa.modifiedCount);
        return res
            .status(200)
            .json({ msg: "Bairro de entrega deletado com sucesso" });
    } catch (err) {
        console.log(err);
        res.status(500).json({ msg: "Erro ao deletar o bairro de entrega" });
    }
});

/*
// Atualizar Pedido
app.post('/update-pedido/:id', checkToken, async (req, res) => {
    try {
      const { _id, id_pedido, status_pedido, finalizadoAt, entregador } = req.body;
  
      // Validações
      if (!id_pedido) {
        return res.status(422).json({ msg: 'ID do pedido obrigatório!' });
      }
  
      if (!_id) {
        return res.status(422).json({ msg: 'Object ID do pedido obrigatório!' });
      }
  
      // Capturar Data Atual e formatar
      var data = moment().format();
  
      await Pedidos.find(
        { id_pedido: id_pedido, _id: _id },
        async function (err, datacall) {
          if (!err) {
            const response = await Pedidos.findOneAndUpdate(
              { id_pedido: id_pedido, _id: _id },
              {
                $set: {
                  status_pedido: status_pedido || datacall.status_pedido,
                  finalizadoAt: finalizadoAt || "",
                  entregador: entregador || datacall.entregador,
                  updatedAt: data,
                },
              },
              {
                new: true,
              }
            );
  
            return res.status(200).json({ msg: "Dados Atualizados com Sucesso!" });
          }
        }
      ).clone();
    } catch (err) {
      console.log(err);
    }
});*/

// Função para limpar e formatar número para Evolution API (Brasil)  
function cleanPhoneNumber(phoneNumber) {
    // Remove todos os caracteres não numéricos
    let cleanNumber = phoneNumber.replace(/\D/g, "");
    
    // Remove prefixos comuns incorretos
    if (cleanNumber.startsWith('0')) {
        cleanNumber = cleanNumber.substring(1);
    }
    
    // Lista COMPLETA de códigos de área que precisam do 9 (São Paulo, Rio, etc + TODAS as outras áreas móveis)
    const needsNine = [
        '11', '12', '13', '14', '15', '16', '17', '18', '19', // SP
        '21', '22', '24', '27', '28', // RJ/ES  
        '31', '32', '33', '34', '35', '37', '38', // MG
        '41', '42', '43', '44', '45', '46', // PR
        '47', '48', '49', // SC
        '51', '53', '54', '55', // RS
        '61', '62', '64', // GO/DF/TO
        '63', '65', '66', // MT/TO
        '67', '68', '69', // MS/AC/RO
        '71', '73', '74', '75', '77', // BA
        '79', '81', '82', '83', '84', '85', '86', '87', '88', '89', // NE
        '91', '93', '94', '95', '96', '97', '98', '99' // Norte
    ];
    
    // Se não tem código do país, adiciona 55
    if (cleanNumber.length >= 10 && cleanNumber.length <= 11 && !cleanNumber.startsWith('55')) {
        cleanNumber = '55' + cleanNumber;
    }
    
    // Se tem 10 dígitos, adiciona 55 e o 9
    if (cleanNumber.length === 10) {
        const areaCode = cleanNumber.substring(0, 2);
        if (needsNine.includes(areaCode)) {
            cleanNumber = '55' + areaCode + '9' + cleanNumber.substring(2);
        } else {
            cleanNumber = '55' + cleanNumber;
        }
    }
    
    // Se tem 11 dígitos sem 55, adiciona 55
    if (cleanNumber.length === 11 && !cleanNumber.startsWith('55')) {
        cleanNumber = '55' + cleanNumber;
    }
    
    // Se tem 12 dígitos e começa com 55, verifica se precisa do 9
    if (cleanNumber.length === 12 && cleanNumber.startsWith('55')) {
        const areaCode = cleanNumber.substring(2, 4);
        if (needsNine.includes(areaCode)) {
            // Adiciona o 9 após o código de área se não estiver presente
            const localNumber = cleanNumber.substring(4);
            if (!localNumber.startsWith('9')) {
                cleanNumber = cleanNumber.substring(0, 4) + '9' + localNumber;
            }
        }
    }
    
    // Se tem 13 dígitos e começa com 55, verifica se precisa remover o 9
    if (cleanNumber.length === 13 && cleanNumber.startsWith('55')) {
        const areaCode = cleanNumber.substring(2, 4);
        if (!needsNine.includes(areaCode)) {
            // Remove o 9 para áreas que não precisam (muito raro)
            const localNumber = cleanNumber.substring(4);
            if (localNumber.startsWith('9')) {
                cleanNumber = cleanNumber.substring(0, 4) + localNumber.substring(1);
            }
        }
    }
    
    console.log(`📱 Número formatado: ${phoneNumber} → ${cleanNumber}`);
    return cleanNumber;
}

// Função para criar a mensagem de acordo com o status do pedido
function createMessage(status_pedido, pedido) {
    function checkEntrega(pedido) {
        if (pedido.entrega.tipo_entrega === "Entrega") {
            return `Seu pedido *${pedido.id_pedido_counter ?? pedido.id_pedido
                }* saiu para entrega e já está a caminho!`;
        }
        if (pedido.entrega.tipo_entrega === "Retirada") {
            return `Seu pedido *${pedido.id_pedido_counter ?? pedido.id_pedido
                }* está pronto, é só vir buscar!`;
        } else
            return `Seu pedido *${pedido.id_pedido_counter ?? pedido.id_pedido
                }* está pronto`;
    }
    switch (status_pedido) {
        case "2":
            return `Seu pedido *#${pedido.id_pedido_counter ?? pedido.id_pedido
                }* entrou em produção! 👨‍🍳`;
        case "3":
            return `Notícia boaaa 🤩\n` + `${checkEntrega(pedido)}`;
        case "4":
            return `Seu pedido *#${pedido.id_pedido_counter ?? pedido.id_pedido
                }* foi finalizado com sucesso! ✅`;
        default:
            return null; // Não enviar mensagem se o status não for um dos casos especificados
    }
}

// Atualizar Pedido
app.post("/update-pedido/:id", checkToken, async (req, res) => {
    try {
        const { _id, id_pedido, status_pedido, finalizadoAt, entregador } =
            req.body;
        const userId = req.params.id;

        // Validações
        if (!id_pedido) {
            return res.status(422).json({ msg: "ID do pedido obrigatório!" });
        }

        if (!_id) {
            return res.status(422).json({ msg: "Object ID do pedido obrigatório!" });
        }

        // Capturar Data Atual e formatar
        var data = moment().format();

        const pedido = await Pedidos.findOne({ id_pedido: id_pedido, _id: _id });

        if (!pedido) {
            return res.status(404).json({ msg: "Pedido não encontrado!" });
        }

        // Verificar se saiu de análise para em produção e imprimir
        if (pedido.status_pedido === "1" && status_pedido === "2") {
            const empresa = await Empresa.findOne(
                { id_empresa: pedido.id_empresa },
                "tipo_impressao name _id"
            );

            const dataFormatada = momentTz
                .tz(data, "America/Sao_Paulo")
                .format("DD/MM/YYYY HH:mm");
            const dadosComanda = {
                createdBy: pedido.createdBy,
                tipoPedido: pedido.entrega.tipo_entrega,
                dataPedido: dataFormatada,
                nomeLoja: empresa.name,
                numeroPedido: pedido.id_pedido_counter ?? pedido.id_pedido,
                itens: pedido.itens,
                cliente: {
                    nome: pedido.nome_cliente,
                    telefone: pedido.celular_cliente,
                    qtdPedidos: pedido.counter_qtd_pedido,
                },
                entrega: {
                    endereco:
                        pedido.entrega.endereco.rua +
                        ", " +
                        pedido.entrega.endereco.numero +
                        ", " +
                        pedido.entrega.endereco.complemento,
                    referencia: pedido.entrega.endereco.referencia,
                    bairroAndCity:
                        pedido.entrega.endereco.bairro +
                        ", " +
                        pedido.entrega.endereco.cidade,
                    cep: pedido.entrega.endereco.cep,
                },
                pagamento: {
                    forma: pedido.tipo_pagamento,
                    subtotal: pedido.valor_total - pedido.entrega.valor,
                    taxaEntrega: pedido.entrega.valor,
                    total: pedido.valor_total,
                },
                observacoes: pedido.descricao,
                troco: pedido.valor_troco,
            };
            gerarPDF(dadosComanda)
                .then((caminhoArquivo) => {
                    console.log("Comanda gerada com sucesso:", caminhoArquivo);
                    // Aqui você pode enviar o arquivo para impressão ou fazer outras operações
                    enviarParaImpressao(caminhoArquivo, empresa._id);
                })
                .catch((erro) => {
                    console.error("Erro ao gerar a comanda:", erro);
                });
        }

        // Verificar se o pedido está sendo finalizado (status 4)
        if (pedido.status_pedido === "3" && status_pedido === "4") {
            // Verificar se há um caixa aberto
            const caixa = await Caixa.findOne({
                id_empresa: pedido.id_empresa,
                status_caixa: true,
            });

            if (!caixa) {
                return res
                    .status(422)
                    .json({
                        msg: "Não há caixa aberto. Abra um caixa para finalizar o pedido.",
                    });
            }

            console.log("PASSOU AQUI!");
            // Adicionar lançamento ao caixa
            caixa.lancamentos_caixa.push({
                descricao: `Pedido Nº ${pedido.id_pedido_counter ?? pedido.id_pedido
                    } (${pedido.entrega.tipo_entrega})`,
                tipo_lancamento: pedido.tipo_pagamento,
                valor: pedido.valor_total,
                id_pedido: pedido._id,
                createdAt: moment().format(),
                updatedAt: moment().format(),
                createdBy: userId, // Definindo createdBy como o ID do usuário recebido nos parâmetros
            });

            await caixa.save();
        }

        const response = await Pedidos.findOneAndUpdate(
            { id_pedido: id_pedido, _id: _id },
            {
                $set: {
                    status_pedido: status_pedido || pedido.status_pedido,
                    finalizadoAt: finalizadoAt || "",
                    entregador: entregador || pedido.entregador,
                    updatedAt: data,
                },
            },
            {
                new: true,
            }
        );

        // Enviar mensagem para o cliente via WhatsApp em segundo plano
        setImmediate(async () => {
            try {
                const empresa = await Empresa.findOne({
                    id_empresa: pedido.id_empresa,
                }).select("whatsapp");

                if (
                    !empresa ||
                    !empresa.whatsapp ||
                    !empresa.whatsapp.endpoint ||
                    !empresa.whatsapp.token ||
                    !empresa.whatsapp.id
                ) {
                    console.error(
                        "Configuração do WhatsApp não encontrada para a empresa."
                    );
                    return;
                }

                // **🔧 CORREÇÃO: Adicionar instanceId para Evolution API**
                const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
                const whatsappInstance = new Whatsapp(
                    empresa.whatsapp.endpoint,
                    empresa.whatsapp.token,
                    instanceIdentifier  // ✅ Agora com instanceId!
                );
                
                // **🔧 CORREÇÃO: Formato correto do número para Evolution API**
                const cleanNumber = cleanPhoneNumber(pedido.celular_cliente);
                const clienteNumero = `${cleanNumber}@s.whatsapp.net`; // ✅ Formato WhatsApp correto
                const mensagem = createMessage(status_pedido, pedido);

                console.log(`📤 Enviando mensagem para ${clienteNumero}: ${mensagem?.substring(0, 50)}...`);

                if (mensagem) {
                    await whatsappInstance.SendSimpleTxtMessage(clienteNumero, mensagem);
                    console.log("✅ Mensagem enviada com sucesso via Evolution API.");
                } else {
                    console.log("⚠️ Nenhuma mensagem enviada devido ao status do pedido.");
                }
            } catch (sendError) {
                console.error("❌ Erro ao enviar mensagem via Evolution API:", sendError.message);
                console.error("📋 Detalhes do erro:", sendError);
            }
        });

        return res.status(200).json({ msg: "Dados Atualizados com Sucesso!" });
    } catch (err) {
        console.log(err);
        return res
            .status(500)
            .json({
                msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
            });
    }
});

// Atualizar Categoria
app.post('/update-categoria/:id', checkToken, async (req, res) => {

    //console.log("chegou aqui!");
    try {

        const {
            _id,
            id_categoria,
            id_empresa,
            order,
            title,
            disponibilidade,
            dia_horario_disponibilidade,
            modelo
        } = req.body
        //console.log("passo 1!",typeof(status_print));
        //validacoes
        if (!id_categoria) {
            return res.status(422).json({ msg: 'ID da categoria obrigatório!' })
        }
        if (!_id) {
            return res.status(422).json({ msg: 'ObjectId da categoria obrigatório!' })
        }

        // Verificar se o campo disponibilidade é igual a 'especificos' e se o campo dia_horario_disponibilidade tem pelo menos um dia selecionado
        if (
            disponibilidade === "especificos" &&
            dia_horario_disponibilidade.length === 0
        ) {
            return res
                .status(422)
                .json({
                    msg: "Para horários específicos é obrigatório selecionar pelo menos 1 dia!",
                });
        }

        //Capturar Data Atual e formatar
        var data = moment().format();

        await Categorias.find(
            { id_categoria: id_categoria, _id: _id, id_empresa: id_empresa },
            async function (err, datacall) {
                if (!err) {
                    //console.log("Passo 2!");
                    const response = await Categorias.findOneAndUpdate(
                        { id_categoria: id_categoria, id_empresa: id_empresa, _id: _id },
                        {
                            $set: {
                                order: order || datacall.order,
                                modelo: modelo || datacall.modelo,
                                title: title || datacall.title,
                                disponibilidade: disponibilidade || datacall.disponibilidade,
                                dia_horario_disponibilidade:
                                    dia_horario_disponibilidade ||
                                    datacall.dia_horario_disponibilidade,
                                updatedAt: data,
                            },
                        },
                        {
                            new: true,
                        }
                    );

                    //return res.status(200).json({response});
                    return res
                        .status(200)
                        .json({ msg: "Dados Atualizados com Sucesso!" });
                }
            }
        ).clone();
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Atualizar Grupo de Adicionais
app.post("/update-grupo-adicionais/:id", checkToken, async (req, res) => {
    //console.log("chegou aqui!");
    try {
        const {
            _id,
            id_empresa,
            title,
            min,
            max,
            mandatory,
            calcular_maior_valor,
            calcular_media,
            precificacao,
            type,
        } = req.body;
        console.log("passo 1!", { _id, id_empresa, title, min, max, mandatory });
        //validacoes
        if (!_id) {
            return res
                .status(422)
                .json({ msg: "ObjectId do grupo de adicionais obrigatório!" });
        }

        //Capturar Data Atual e formatar
        var data = moment().format();

        await Adicionais.find(
            { _id: _id, id_empresa: id_empresa },
            async function (err, datacall) {
                if (!err) {
                    //console.log("Passo 2!");
                    const response = await Adicionais.findOneAndUpdate(
                        { id_empresa: id_empresa, _id: _id },
                        {
                            $set: {
                                title: title !== undefined ? title : datacall.title,
                                min: min !== undefined ? min : datacall.min,
                                max: max !== undefined ? max : datacall.max,
                                mandatory:
                                    mandatory !== undefined ? mandatory : datacall.mandatory,
                                calcular_maior_valor:
                                    calcular_maior_valor !== undefined
                                        ? calcular_maior_valor
                                        : datacall.calcular_maior_valor,
                                calcular_media:
                                    calcular_media !== undefined
                                        ? calcular_media
                                        : datacall.calcular_media,
                                precificacao:
                                    precificacao !== undefined
                                        ? precificacao
                                        : datacall.precificacao,
                                type: type !== undefined ? type : datacall.type,
                                updatedAt: data,
                            },
                        },
                        {
                            new: true,
                        }
                    );

                    //return res.status(200).json({response});
                    return res
                        .status(200)
                        .json({ msg: "Dados Atualizados com Sucesso!" });
                }
            }
        ).clone();
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Atualizar Item
app.post("/update-item/:id", checkToken, async (req, res) => {
    try {
        const {
            _id,
            id_item,
            id_empresa,
            order,
            category_item_id,
            category_item_title,
            title,
            description,
            out,
            images,
            price,
            has_adicional,
            adicionais,
            out_salao,
            price_salao,
            type
        } = req.body;

        // Validações
        if (!id_item) {
            return res.status(422).json({ msg: "ID do item obrigatório!" });
        }
        if (!_id) {
            return res.status(422).json({ msg: "ObjectId do item obrigatório!" });
        }

        // Capturar Data Atual e formatar
        var data = moment().format();

        // Encontrar o item no banco de dados
        const existingItem = await Itens.findOne({ id_item: id_item, _id: _id, id_empresa: id_empresa });

        if (!existingItem) {
            return res.status(404).json({ msg: "Item não encontrado!" });
        }

        let imageUrl = existingItem.image;

        // Verificar se a imagem foi alterada
        if (images === "") {
            // Excluir a imagem antiga se o campo de imagem estiver vazio
            if (existingItem.image && existingItem.image.startsWith("https")) {
                const fileName = existingItem.image.split("/").pop();
                await bucket.file(fileName).delete();
                console.log(`✅ Imagem deletada: ${fileName}`);
                imageUrl = ""; // Definir URL da imagem como vazia
            }
        } else if (images && images.startsWith("data:image")) {
            // Excluir a imagem antiga, se existir
            if (existingItem.image && existingItem.image.startsWith("https")) {
                const fileName = existingItem.image.split("/").pop();
                await bucket.file(fileName).delete();
                console.log(`✅ Imagem antiga deletada: ${fileName}`);
            }

            // Gravando a nova imagem no Google Cloud Storage
            const imageId =
                Math.random().toString(36).substring(2, 15) +
                Math.random().toString(36).substring(2, 15);
            imageUrl = await uploadImage(images, "img_" + imageId + ".png");
            console.log(`✅ Nova imagem salva: ${imageUrl}`);
        }

        // Atualizar o item no banco de dados
        const updatedItem = await Itens.findOneAndUpdate(
            { id_item: id_item, id_empresa: id_empresa, _id: _id },
            {
                $set: {
                    order: order !== undefined ? order : existingItem.order,
                    category_item_id: category_item_id !== undefined ? category_item_id : existingItem.category_item_id,
                    category_item_title: category_item_title !== undefined ? category_item_title : existingItem.category_item_title,
                    title: title !== undefined ? title : existingItem.title,
                    description: description !== undefined ? description : existingItem.description,
                    out: out !== undefined ? out : existingItem.out,
                    out_salao: out_salao !== undefined ? out_salao : existingItem.out_salao,
                    image: imageUrl, // Atualizando com a URL correta da imagem
                    price: price !== undefined ? price : existingItem.price,
                    price_salao: price_salao !== undefined ? price_salao : existingItem.price_salao,
                    has_adicional: has_adicional !== undefined ? has_adicional : existingItem.has_adicional,
                    adicionais: adicionais !== undefined ? adicionais : existingItem.adicionais,
                    type: type !== undefined ? type : existingItem.type,
                    updatedAt: data
                },
            },
            { new: true }
        );

        return res.status(200).json({ msg: "Item atualizado com sucesso!", updatedItem });
    } catch (err) {
        console.error("❌ Erro ao atualizar item:", err);
        return res.status(500).json({ error: "Erro interno do servidor." });
    }
});


// Rota para remover um grupo de adicionais de um item
app.post("/remove-grupoAdicionaisFromItem/:id", checkToken, async (req, res) => {
    const { itemId, grupoAdicionalValue } = req.body; // itemId: ID do Item, grupoAdicionalValue: value do grupo a ser removido

    if (!itemId || !grupoAdicionalValue) {
        return res.status(400).json({ message: 'Faltando parâmetro itemId ou grupoAdicionalValue' });
    }

    try {
        // Encontrar o item pelo ID
        const item = await Itens.findById(itemId);

        if (!item) {
            return res.status(404).json({ message: 'Item não encontrado' });
        }

        // Verificar se o item tem adicionais
        if (item.adicionais && item.adicionais.length > 0) {
            // Filtrar e remover o grupo de adicionais do item
            item.adicionais = item.adicionais.filter(adicional => adicional.value !== grupoAdicionalValue);

            // Salvar as mudanças
            await item.save();

            return res.status(200).json({ msg: 'Grupo removido do item com sucesso!', item });
        }

        return res.status(400).json({ msg: 'Nenhum grupo encontrado no item para remover.' });

    } catch (error) {
        console.error(error);
        return res.status(500).json({ msg: 'Erro ao remover grupo de adicionais.', error });
    }
});

// Atualizar Endereco Cliente
app.post("/update-endereco-cliente/", async (req, res) => {
    try {
        const { id_empresa, telefone, endereco } = req.body;
        // Extração do _id do objeto enderecoToEdit
        const enderecoToEditId = req.body.enderecoToEdit
            ? req.body.enderecoToEdit._id
            : undefined;
        const data = moment().format();
        const cliente = await Cliente.findOne({
            id_empresa: id_empresa,
            telefone: telefone,
        });

        if (cliente) {
            if (enderecoToEditId) {
                const enderecoIndex = cliente.endereco.findIndex((enderecoItem) =>
                    enderecoItem._id.equals(enderecoToEditId)
                );

                if (enderecoIndex === -1) {
                    return res
                        .status(400)
                        .json({ msg: "Endereço não encontrado para atualização." });
                } else {
                    // Antes de atualizar, garantir que estamos lidando com o ID correto
                    cliente.endereco[enderecoIndex] = {
                        ...cliente.endereco[enderecoIndex],
                        ...endereco,
                        updatedAt: data,
                    };
                    cliente.markModified("endereco");
                    await cliente.save();
                    return res
                        .status(200)
                        .json({
                            msg: "Endereço atualizado com sucesso!",
                            response: cliente,
                        });
                }
            } else {
                // Adicionar um novo endereço
                cliente.endereco.push({ ...endereco, updatedAt: data });
                cliente.updatedAt = data;
                await cliente.save();
                return res
                    .status(200)
                    .json({ msg: "Endereço adicionado com sucesso!", response: cliente });
            }
        } else {
            return res.status(400).json({ msg: "Cliente não encontrado." });
        }
    } catch (err) {
        console.log(err);
        return res
            .status(500)
            .json({ msg: "Ocorreu um erro ao atualizar os dados." });
    }
});

// Deletar endereco do cliente
app.post("/delete-endereco-cliente/", async (req, res) => {
    try {
        const { id_empresa, telefone, enderecoIdToDelete } = req.body;

        // Encontrar o cliente pelo telefone fornecido
        const cliente = await Cliente.findOne({
            id_empresa: id_empresa,
            telefone: telefone,
        });

        if (cliente) {
            // Encontrar o índice do endereço a ser removido
            const enderecoIndex = cliente.endereco.findIndex((endereco) =>
                endereco._id.equals(enderecoIdToDelete)
            );

            if (enderecoIndex === -1) {
                // Endereço não encontrado
                return res
                    .status(400)
                    .json({ msg: "Endereço não encontrado para exclusão." });
            } else {
                // Remover o endereço do array
                cliente.endereco.splice(enderecoIndex, 1);

                // Salvar o documento do cliente após a remoção
                await cliente.save();

                // Retornar o cliente atualizado
                return res
                    .status(200)
                    .json({ msg: "Endereço removido com sucesso!", response: cliente });
            }
        } else {
            // Cliente não encontrado
            return res.status(400).json({ msg: "Cliente não encontrado." });
        }
    } catch (err) {
        console.log(err);
        return res
            .status(500)
            .json({ msg: "Ocorreu um erro ao tentar excluir o endereço." });
    }
});

// Atualizar Cliente
app.post("/update-cliente/:id", checkToken, async (req, res) => {
    try {
        const {
            id,
            documento,
            name,
            razao,
            contato,
            email,
            cep,
            estado,
            municipio,
            bairro,
            complemento,
            telefone,
            celular,
        } = req.body;

        //validacoes
        if (!id) {
            return res
                .status(422)
                .json({ msg: "É necessário informar o ID para Editar os dados!" });
        }

        //const responseEmpresaEmailExist = await Empresa.find({"email":email},'id_empresa')
        const responseClienteDocumentoExist = await Cliente.find(
            { documento: documento },
            "id_cliente"
        );
        //console.log(responseClienteEmailExist[0]);
        console.log(id);
        if (
            responseClienteDocumentoExist.length > 0 &&
            id != responseClienteDocumentoExist[0].id_cliente
        ) {
            //console.log("Usuário DIFERENTE, PROIBIDO ATUALIZAR");
            return res.status(400).json({ msg: "Este CNPJ já está cadastrado!" });
        }
        // if (responseEmpresaEmailExist.length>0 && id != responseEmpresaEmailExist[0].id_empresa){
        //     //console.log("Usuário DIFERENTE, PROIBIDO ATUALIZAR");
        //     return res.status(400).json({msg:"Este e-mail já está cadastrado!"});
        // }
        else console.log("Nenhum problema, pode seguir");

        //Capturar Data Atual e formatar
        var data = moment().format();
        console.log(data);

        await Cliente.find({ id_cliente: id }, async function (err, datacall) {
            if (!err) {
                const response = await Cliente.findOneAndUpdate(
                    { id_cliente: id },
                    {
                        $set: {
                            documento: documento || datacall.documento,
                            name: name || datacall.name,
                            razao: razao || datacall.razao,
                            contato: contato || datacall.contato,
                            email: email || datacall.email,
                            cep: cep || datacall.cep,
                            estado: estado || datacall.estado,
                            municipio: municipio || datacall.municipio,
                            bairro: bairro || datacall.bairro,
                            complemento: complemento || datacall.complemento,
                            telefone: telefone || datacall.telefone,
                            celular: celular || datacall.celular,
                            updatedAt: data,
                        },
                    },
                    {
                        new: true,
                    }
                );

                //return res.status(200).json({response});
                return res.status(200).json({ msg: "Dados Atualizados com Sucesso!" });
            }
        }).clone();
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Atualizar Vendedor
app.post("/update-vendedor/:id", checkToken, async (req, res) => {
    try {
        const {
            id,
            documento,
            name,
            cep,
            estado,
            municipio,
            bairro,
            complemento,
            telefone,
            celular,
        } = req.body;

        //validacoes
        if (!id) {
            return res
                .status(422)
                .json({ msg: "É necessário informar o ID para Editar os dados!" });
        }

        //const responseEmpresaEmailExist = await Empresa.find({"email":email},'id_empresa')
        const responseVendedorDocumentoExist = await Vendedor.find(
            { documento: documento },
            "id_vendedor"
        );
        //console.log(responseClienteEmailExist[0]);
        console.log(id);
        if (
            responseVendedorDocumentoExist.length > 0 &&
            id != responseVendedorDocumentoExist[0].id_vendedor
        ) {
            //console.log("Usuário DIFERENTE, PROIBIDO ATUALIZAR");
            return res.status(400).json({ msg: "Este CNPJ já está cadastrado!" });
        }
        // if (responseEmpresaEmailExist.length>0 && id != responseEmpresaEmailExist[0].id_empresa){
        //     //console.log("Usuário DIFERENTE, PROIBIDO ATUALIZAR");
        //     return res.status(400).json({msg:"Este e-mail já está cadastrado!"});
        // }
        else console.log("Nenhum problema, pode seguir");

        //Capturar Data Atual e formatar
        var data = moment().format();
        console.log(data);

        await Vendedor.find({ id_vendedor: id }, async function (err, datacall) {
            if (!err) {
                const response = await Vendedor.findOneAndUpdate(
                    { id_vendedor: id },
                    {
                        $set: {
                            documento: documento || datacall.documento,
                            name: name || datacall.name,
                            cep: cep || datacall.cep,
                            estado: estado || datacall.estado,
                            municipio: municipio || datacall.municipio,
                            bairro: bairro || datacall.bairro,
                            complemento: complemento || datacall.complemento,
                            telefone: telefone || datacall.telefone,
                            celular: celular || datacall.celular,
                            updatedAt: data,
                        },
                    },
                    {
                        new: true,
                    }
                );

                //return res.status(200).json({response});
                return res.status(200).json({ msg: "Dados Atualizados com Sucesso!" });
            }
        }).clone();
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Atualizar Orcamento
app.post("/update-orcamento/:id", checkToken, async (req, res) => {
    try {
        const {
            id,
            codigo_cliente,
            nome_cliente,
            codigo_vendedor,
            nome_vendedor,
            total_orc,
            status_orc,
            id_grupo,
            vinculo_empresa,
            items,
        } = req.body;

        //validacoes
        if (!id) {
            return res
                .status(422)
                .json({ msg: "É necessário informar o ID para Editar os dados!" });
        }

        if (items.length === 0) {
            return res
                .status(422)
                .json({ msg: "É obrigatório o cadastro do item no orçamento!" });
        }

        //Capturar Data Atual e formatar
        var data = moment().format();
        console.log(data);

        await Orcamento.find({ id_orcamento: id }, async function (err, datacall) {
            if (!err) {
                const response = await Orcamento.findOneAndUpdate(
                    { id_orcamento: id },
                    {
                        $set: {
                            codigo_cliente: codigo_cliente || datacall.codigo_cliente,
                            nome_cliente: nome_cliente || datacall.nome_cliente,
                            codigo_vendedor: codigo_vendedor || datacall.codigo_vendedor,
                            nome_vendedor: nome_vendedor || datacall.nome_vendedor,
                            total_orc: total_orc || datacall.total_orc,
                            status_orc: status_orc || datacall.status_orc,
                            id_grupo: id_grupo || datacall.id_grupo,
                            vinculo_empresa: vinculo_empresa || datacall.vinculo_empresa,
                            items: items || datacall.items,
                            updatedAt: data,
                        },
                    },
                    {
                        new: true,
                    }
                );

                //return res.status(200).json({response});
                return res.status(200).json({ msg: "Dados Atualizados com Sucesso!" });
            }
        }).clone();
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Atualizar Status Empresa/User
app.post("/update-status/:id", checkToken, async (req, res) => {
    try {
        const { id_empresa, status } = req.body;

        Empresa.findOneAndUpdate(
            { id_empresa: id_empresa },
            {
                $set: {
                    bloqueado: status,
                },
            },
            {
                new: true,
            }
        ).then((response) => {
            if (response) {
                User.findOneAndUpdate(
                    { vinculo_empresa: id_empresa },
                    {
                        $set: {
                            bloqueado: status,
                        },
                    },
                    {
                        new: true,
                    }
                ).then((response) => {
                    if (response) {
                        return res
                            .status(200)
                            .json({ msg: "Dados Atualizados com Sucesso!" });
                    } else {
                        return res
                            .status(200)
                            .json({ msg: "Dados da Empresa Atualizados com sucesso!" });
                    }
                });
            } else {
                return res
                    .status(422)
                    .json({ msg: "Não foi possível atualizar o status da Empresa!" });
            }
        });
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Atualizar a Imagem do usuário
app.post("/update-user-img/:id", checkToken, async (req, res) => {
    try {
        const { image } = req.body;
        const id = req.params.id;

        //gravando a imagem no bucket do google cloud
        var imageUrl;
        if (image.length > 0) {
            const imageBase64 = image[0].data_url;
            // gerar um id randomico para a imagem
            const imageId =
                Math.random().toString(36).substring(2, 15) +
                Math.random().toString(36).substring(2, 15);
            imageUrl = await uploadImage(imageBase64, "img_" + imageId + ".png");
        } else {
            imageUrl = null;
        }

        User.findByIdAndUpdate(
            { _id: id },
            {
                $set: {
                    user_img: imageUrl ? imageUrl : "",
                },
            },
            {
                new: true,
            }
        ).then((response) => {
            if (response) {
                return res
                    .status(200)
                    .json({
                        msg: "Imagem do usuário atualizada com sucesso!",
                        image: imageUrl,
                    });
            } else {
                return res
                    .status(422)
                    .json({ msg: "Não foi possível atualizar a imagem do usuário!" });
            }
        });
    } catch (err) {
        //return res.status(400).json({ error: err});
        console.log(err);
    }
});

// Deletar/Inativar Usuário

app.post("/delete-user/:id", checkToken, async (req, res) => {
    const { id } = req.body;

    //validacoes
    if (!id) {
        return res
            .status(422)
            .json({ msg: "É necessário informar o ID para Exclusão!" });
    }

    //Checar se usuario existe e deleta
    User.findByIdAndUpdate(id, { inativo: true }, function (err, docs) {
        if (err) {
            console.log(err);
            return res.status(422).json({ msg: "Ocorreu um erro no servidor!" });
        }
        if (docs) {
            console.log("Inativado : ", docs);
            return res.status(201).json({ msg: "Usuário excluído com sucesso!" });
        } else {
            console.log("Usuário não encontrado : ", docs);
            return res.status(422).json({ msg: "Usuário não encontrado!" });
        }
    });
});

// Deletar/Inativar Entregador
app.post("/delete-entregador/:id", checkToken, async (req, res) => {
    const { id } = req.body;

    //validacoes
    if (!id) {
        return res
            .status(422)
            .json({ msg: "É necessário informar o ID para Exclusão!" });
    }

    //Checar se usuario existe e deleta
    Entregadores.findByIdAndDelete(id, function (err, docs) {
        if (err) {
            console.log(err);
            return res.status(422).json({ msg: "Ocorreu um erro no servidor!" });
        }
        if (docs) {
            console.log("Deleted : ", docs);
            return res.status(201).json({ msg: "Entregador excluído com sucesso!" });
        } else {
            console.log("Usuário não encontrado : ", docs);
            return res.status(422).json({ msg: "Entregador não encontrado!" });
        }
    });
});

app.post("/configurar-senha-cancelamento", checkToken, async (req, res) => {
    const { id_empresa, senha, senhaAtual, novaSenha, has_cancel_password } =
        req.body;

    try {
        // Buscar a empresa pelo ID
        const empresa = await Empresa.findById(id_empresa).select(
            "cancel_password has_cancel_password"
        );

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada." });
        }

        // Caso 1: Remover a senha de cancelamento
        if (!has_cancel_password) {
            empresa.cancel_password = undefined;
            empresa.has_cancel_password = false;
            await empresa.save();
            return res
                .status(200)
                .json({ msg: "Senha de cancelamento removida com sucesso." });
        }

        // Caso 2: Alteração da senha de cancelamento, exige a senha atual se já estiver configurada
        if (empresa.has_cancel_password && senhaAtual) {
            console.log("Senha atual fornecida:", senhaAtual);
            console.log("Senha atual no banco:", empresa.cancel_password);

            if (senhaAtual.length < 6) {
                return res
                    .status(422)
                    .json({ msg: "A senha atual digitada tem menos 6 caracteres." });
            }

            if (novaSenha.length < 6) {
                return res
                    .status(422)
                    .json({ msg: "A nova senha deve ter mais de 6 caracteres." });
            }

            const senhaValida = await bcrypt.compare(
                senhaAtual,
                empresa.cancel_password
            );
            if (!senhaValida) {
                return res.status(400).json({ msg: "Senha atual incorreta." });
            }

            const hashedPassword = await bcrypt.hash(novaSenha, 10);
            // Atualiza a senha e marca que a senha de cancelamento está ativa
            empresa.cancel_password = hashedPassword;
            empresa.has_cancel_password = true;
            await empresa.save();

            return res
                .status(200)
                .json({ msg: "Senha de cancelamento configurada com sucesso." });
        }

        // Caso 3: Definir nova senha de cancelamento
        if (senha && senha.length >= 6) {
            const hashedPassword = await bcrypt.hash(senha, 10);

            // Atualiza a senha e marca que a senha de cancelamento está ativa
            empresa.cancel_password = hashedPassword;
            empresa.has_cancel_password = true;
            await empresa.save();

            return res
                .status(200)
                .json({ msg: "Senha de cancelamento configurada com sucesso." });
        }
        if (senha && senha.length < 6) {
            return res
                .status(422)
                .json({ msg: "A senha deve ter pelo menos 6 caracteres." });
        } else if (has_cancel_password && (!novaSenha || novaSenha.length < 6)) {
            return res
                .status(422)
                .json({ msg: "A nova senha deve ter pelo menos 6 caracteres." });
        }
    } catch (error) {
        console.error("Erro ao configurar a senha de cancelamento:", error);
        return res
            .status(500)
            .json({ msg: "Erro ao configurar a senha de cancelamento." });
    }
});

// Deletar/Cancelar Pedido
app.post("/cancelar-pedido/:id", checkToken, async (req, res) => {
    const { id, empresaId, senha } = req.body;

    console.log("Requisição recebida para cancelar pedido:", id);
    console.log("Empresa ID:", empresaId);
    console.log("Senha fornecida:", senha);

    if (!id) {
        return res
            .status(422)
            .json({ msg: "É necessário informar o ID para exclusão!" });
    }

    try {
        // Buscar a empresa para verificar se possui senha de cancelamento configurada
        const empresa = await Empresa.findById(empresaId).select(
            "cancel_password has_cancel_password"
        );

        if (!empresa) {
            console.log("Empresa não encontrada.");
            return res.status(404).json({ msg: "Empresa não encontrada." });
        }

        console.log("Empresa encontrada:", empresa);
        console.log("Has Cancel Password:", empresa.has_cancel_password);
        console.log("Cancel Password no banco:", empresa.cancel_password);

        // Se a empresa possui uma senha configurada, verificar se a senha está correta
        if (empresa.has_cancel_password) {
            if (!senha) {
                console.log("Nenhuma senha fornecida para a validação.");
                return res
                    .status(203)
                    .json({ msg: "Senha de cancelamento é obrigatória." });
            }

            const passwordMatch = await bcrypt.compare(
                senha,
                empresa.cancel_password
            );
            console.log("Resultado da comparação da senha:", passwordMatch);

            if (!passwordMatch) {
                console.log("Senha de cancelamento incorreta.");
                return res
                    .status(203)
                    .json({ msg: "Senha de cancelamento incorreta." });
            }
        }

        // Checar se o pedido existe e cancelar
        const pedido = await Pedidos.findByIdAndUpdate(
            id,
            { cancelado: true, status_pedido: "4", finalizadoAt: new Date() },
            { new: true }
        );
        console.log("Pedido encontrado e cancelado:", pedido);

        if (!pedido) {
            console.log("Pedido não encontrado.");
            return res.status(404).json({ msg: "Pedido não encontrado!" });
        }

        return res
            .status(200)
            .json({ msg: "Pedido cancelado com sucesso!", pedido });
    } catch (err) {
        console.error("Erro ao cancelar o pedido:", err);
        return res.status(500).json({ msg: "Erro ao cancelar o pedido." });
    }
});
/*app.post('/cancelar-pedido/:id', checkToken, async (req, res) => {

    const { id } = req.body

    // Validações
    if (!id) {
        return res.status(422).json({ msg: 'É necessário informar o ID para Exclusão!' });
    }

    try {
        // Checar se pedido existe e cancela
        const pedido = await Pedidos.findByIdAndUpdate(id, { cancelado: true }, { new: true });

        if (!pedido) {
            return res.status(404).json({ msg: 'Pedido não encontrado!' });
        }

        return res.status(200).json({ msg: 'Pedido cancelado com sucesso!', pedido });

    } catch (err) {
        console.error(err);
        return res.status(500).json({ msg: 'Aconteceu um erro no servidor, tente novamente mais tarde!' });
    }
})*/

// Deletar Empresa
app.post("/delete-empresa/:id", checkToken, async (req, res) => {
    const { id } = req.body;

    //validacoes
    if (!id) {
        return res
            .status(422)
            .json({ msg: "É necessário informar o ID para Exclusão!" });
    }

    //Checar se empresa existe e inativa
    Empresa.findByIdAndUpdate(id, { inativo: true }, function (err, docs) {
        if (err) {
            console.log(err);
            return res.status(422).json({ msg: "Ocorreu um erro no servidor!" });
        }
        if (docs) {
            console.log("Inativado : ", docs);
            return res.status(201).json({ msg: "Empresa excluída com sucesso!" });
        } else {
            console.log("Usuário não encontrado : ", docs);
            return res.status(422).json({ msg: "Empresa não encontrada!" });
        }
    });
});

// Deletar Empresa Completamente (com todos os dados relacionados)
app.delete("/delete-empresa-completa/:empresaObjId", async (req, res) => {
    try {
        const { empresaObjId } = req.params;
        const { usuario, senha } = req.body;

        // Credenciais fixas para autorização (CHUMBADAS NO CÓDIGO)
        const USUARIO_AUTORIZADO = "admin_master_delete";
        const SENHA_AUTORIZADA = "PedejaDeleteMaster2024!@#";

        // Verificação de autenticação
        if (!usuario || !senha) {
            return res.status(401).json({ 
                msg: "Credenciais de autenticação são obrigatórias!" 
            });
        }

        if (usuario !== USUARIO_AUTORIZADO || senha !== SENHA_AUTORIZADA) {
            console.log(`❌ TENTATIVA DE ACESSO NEGADO - Usuário: ${usuario} - IP: ${req.ip} - Data: ${new Date().toISOString()}`);
            return res.status(403).json({ 
                msg: "Acesso negado! Credenciais inválidas." 
            });
        }

        // Log de acesso autorizado
        console.log(`✅ ACESSO AUTORIZADO para deleção de empresa - Usuário: ${usuario} - IP: ${req.ip} - Data: ${new Date().toISOString()}`);

        // Validação do ObjectId
        if (!empresaObjId || !ObjectId.isValid(empresaObjId)) {
            return res.status(422).json({ 
                msg: "É necessário informar um ObjectId válido da empresa!" 
            });
        }

        // Buscar a empresa primeiro para obter dados necessários (id_empresa sequencial e cnpj)
        const empresa = await Empresa.findById(empresaObjId);
        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada!" });
        }

        const idEmpresaSequencial = empresa.id_empresa;
        const cnpjEmpresa = empresa.cnpj;

        console.log(`🗑️ INICIANDO DELEÇÃO COMPLETA DA EMPRESA: ${empresa.name} (ID: ${empresaObjId}) - Autorizado por: ${usuario}`);

        // Array de promises para deletar todos os dados relacionados
        const deletePromises = [];

        // 1. Adicionais -> empresaObjId
        deletePromises.push(
            Adicionais.deleteMany({ empresaObjId: new ObjectId(empresaObjId) })
                .then(result => console.log(`Deletados ${result.deletedCount} adicionais`))
        );

        // 2. Assinatura -> empresa
        deletePromises.push(
            Assinatura.deleteMany({ empresa: new ObjectId(empresaObjId) })
                .then(result => console.log(`Deletadas ${result.deletedCount} assinaturas`))
        );

        // 3. Caixa -> empresaObjId
        deletePromises.push(
            Caixa.deleteMany({ empresaObjId: new ObjectId(empresaObjId) })
                .then(result => console.log(`Deletados ${result.deletedCount} registros de caixa`))
        );

        // 4. Categorias -> empresaObjId
        deletePromises.push(
            Categorias.deleteMany({ empresaObjId: new ObjectId(empresaObjId) })
                .then(result => console.log(`Deletadas ${result.deletedCount} categorias`))
        );

        // 5. Cliente -> id_empresa (sequencial)
        if (idEmpresaSequencial) {
            deletePromises.push(
                Cliente.deleteMany({ id_empresa: idEmpresaSequencial })
                    .then(result => console.log(`Deletados ${result.deletedCount} clientes`))
            );
        }

        // 6. CompanyResponses -> companyId (ObjectId como string)
        deletePromises.push(
            CompanyResponses.deleteMany({ companyId: empresaObjId })
                .then(result => console.log(`Deletadas ${result.deletedCount} respostas da empresa`))
        );

        // 7. Entregadores -> id_empresa (ObjectId como string)
        deletePromises.push(
            Entregadores.deleteMany({ id_empresa: empresaObjId })
                .then(result => console.log(`Deletados ${result.deletedCount} entregadores`))
        );

        // 8. Itens -> empresaObjId
        deletePromises.push(
            Itens.deleteMany({ empresaObjId: new ObjectId(empresaObjId) })
                .then(result => console.log(`Deletados ${result.deletedCount} itens`))
        );

        // 9. LeadChannel -> empresaObjId
        deletePromises.push(
            LeadChannel.deleteMany({ empresaObjId: new ObjectId(empresaObjId) })
                .then(result => console.log(`Deletados ${result.deletedCount} lead channels`))
        );

        // 10. Mesas -> empresa_id
        deletePromises.push(
            Mesas.deleteMany({ empresa_id: new ObjectId(empresaObjId) })
                .then(result => console.log(`Deletadas ${result.deletedCount} mesas`))
        );

        // 11. Messages -> empresaObjId
        deletePromises.push(
            Messages.deleteMany({ empresaObjId: new ObjectId(empresaObjId) })
                .then(result => console.log(`Deletadas ${result.deletedCount} mensagens`))
        );

        // 12. Pedidos -> id_empresa (sequencial)
        if (idEmpresaSequencial) {
            deletePromises.push(
                Pedidos.deleteMany({ id_empresa: idEmpresaSequencial })
                    .then(result => console.log(`Deletados ${result.deletedCount} pedidos`))
            );
        }

        // 13. User -> vinculo_empresa (CNPJ)
        if (cnpjEmpresa) {
            deletePromises.push(
                User.deleteMany({ vinculo_empresa: cnpjEmpresa })
                    .then(result => console.log(`Deletados ${result.deletedCount} usuários`))
            );
        }

        // Executar todas as deleções em paralelo
        await Promise.all(deletePromises);

        // Por último, deletar a empresa
        await Empresa.findByIdAndDelete(empresaObjId);
        
        console.log(`✅ DELEÇÃO COMPLETA FINALIZADA - Empresa ${empresa.name} e todos os dados relacionados foram deletados com sucesso! - Executado por: ${usuario} - Data: ${new Date().toISOString()}`);
        
        return res.status(200).json({ 
            msg: `Empresa ${empresa.name} e todos os dados relacionados foram deletados com sucesso!`,
            empresaId: empresaObjId,
            nomeEmpresa: empresa.name,
            executadoPor: usuario,
            dataExecucao: new Date().toISOString()
        });

    } catch (error) {
        console.error(`❌ ERRO na deleção completa de empresa - ${error.message} - IP: ${req.ip} - Data: ${new Date().toISOString()}`);
        return res.status(500).json({ 
            msg: "Erro interno do servidor ao deletar empresa",
            error: error.message 
        });
    }
});

// Deletar Categoria
app.post("/delete-categoria/:id", checkToken, async (req, res) => {
    const { id } = req.body;

    //validacoes
    if (!id) {
        return res
            .status(422)
            .json({ msg: "É necessário informar o ID para Exclusão!" });
    }

    Promise.all([
        new Promise((resolve, reject) => {
            Itens.deleteMany({ category_item_id: id }, function (err, docs) {
                if (err) {
                    logger.error(err, { context: "MyApp", pid: process.pid });
                    reject(err);
                }
                if (docs.deletedCount > 0) {
                    logger.info(
                        `${docs.deletedCount} items deleted from 'itens' collection`,
                        { context: "MyApp", pid: process.pid }
                    );
                } else {
                    logger.info("No items found with the given id", {
                        context: "MyApp",
                        pid: process.pid,
                    });
                }
                resolve(docs);
            });
        }),
        new Promise((resolve, reject) => {
            Categorias.findByIdAndDelete(id, function (err, docs) {
                if (err) {
                    logger.error(err, { context: "MyApp" });
                    reject(err);
                }
                if (docs) {
                    logger.info("Categoria deletada : " + docs, {
                        context: "MyApp",
                        pid: process.pid,
                    });
                } else {
                    logger.info("Categoria não encontrada : " + docs, {
                        context: "MyApp",
                        pid: process.pid,
                    });
                }
                resolve(docs);
            });
        }),
    ])
        .then(() => {
            return res.status(201).json({ msg: "Categoria excluída com sucesso!" });
        })
        .catch((err) => {
            logger.error(err, { context: "MyApp", pid: process.pid });
            return res.status(422).json({ msg: "Ocorreu um erro no servidor!" });
        });
});

// Deletar Grupo de Adicional
app.post("/delete-grupo-adicional/:id", checkToken, async (req, res) => {
    const { id } = req.body;

    //validacoes
    if (!id) {
        return res
            .status(422)
            .json({ msg: "É necessário informar o ID para Exclusão!" });
    }

    Promise.all([
        new Promise((resolve, reject) => {
            Adicionais.findByIdAndDelete(id, function (err, docs) {
                if (err) {
                    logger.error(err, { context: "MyApp" });
                    reject(err);
                }
                if (docs) {
                    logger.info("Grupo de adicional deletado : " + docs, {
                        context: "MyApp",
                        pid: process.pid,
                    });
                } else {
                    logger.info("Grupo de adicional não encontrado : " + docs, {
                        context: "MyApp",
                        pid: process.pid,
                    });
                }
                resolve(docs);
            });
        }),
    ])
        .then(() => {
            return res
                .status(201)
                .json({ msg: "Grupo de adicional excluído com sucesso!" });
        })
        .catch((err) => {
            logger.error(err, { context: "MyApp", pid: process.pid });
            return res.status(422).json({ msg: "Ocorreu um erro no servidor!" });
        });
});

// Deletar Item
app.post("/delete-item/:id", checkToken, async (req, res) => {
    const { id } = req.body;

    //validacoes
    if (!id) {
        return res
            .status(422)
            .json({ msg: "É necessário informar o ID para Exclusão!" });
    }

    Itens.findByIdAndDelete(id, function (err, docs) {
        if (err) {
            console.log(err);
            return res.status(422).json({ msg: "Ocorreu um erro no servidor!" });
        }
        if (docs) {
            console.log("Deleted : ", docs);
            return res.status(201).json({ msg: "Item excluído com sucesso!" });
        } else {
            console.log("Item não encontrado : ", docs);
            return res.status(422).json({ msg: "Item não encontrado!" });
        }
    });
});

// Deletar Cliente
app.post("/delete-cliente/:id", checkToken, async (req, res) => {
    const { id } = req.body;

    //validacoes
    if (!id) {
        return res
            .status(422)
            .json({ msg: "É necessário informar o ID para Exclusão!" });
    }

    //Checar se cliente existe e deleta
    Cliente.findByIdAndUpdate(id, { inativo: true }, function (err, docs) {
        if (err) {
            console.log(err);
            return res.status(422).json({ msg: "Ocorreu um erro no servidor!" });
        }
        if (docs) {
            console.log("Deleted : ", docs);
            return res.status(201).json({ msg: "Cliente excluído com sucesso!" });
        } else {
            console.log("Usuário não encontrado : ", docs);
            return res.status(422).json({ msg: "Cliente não encontrado!" });
        }
    });
});

// Deletar Vendedor
app.post("/delete-vendedor/:id", checkToken, async (req, res) => {
    const { id } = req.body;

    //validacoes
    if (!id) {
        return res
            .status(422)
            .json({ msg: "É necessário informar o ID para Exclusão!" });
    }

    //Checar se vendedor existe e deleta
    Vendedor.findByIdAndUpdate(id, { inativo: true }, function (err, docs) {
        if (err) {
            console.log(err);
            return res.status(422).json({ msg: "Ocorreu um erro no servidor!" });
        }
        if (docs) {
            //console.log("Deleted : ", docs);
            return res.status(201).json({ msg: "Vendedor excluído com sucesso!" });
        } else {
            //console.log("Usuário não encontrado : ", docs);
            return res.status(422).json({ msg: "Vendedor não encontrado!" });
        }
    });
});

// Deletar Orcamento
app.post("/delete-orcamento/:id", checkToken, async (req, res) => {
    const { id } = req.body;

    //validacoes
    if (!id) {
        return res
            .status(422)
            .json({ msg: "É necessário informar o ID para Exclusão!" });
    }

    //Checar se orcamento existe e deleta
    Orcamento.findByIdAndUpdate(id, { inativo: true }, function (err, docs) {
        if (err) {
            console.log(err);
            return res.status(422).json({ msg: "Ocorreu um erro no servidor!" });
        }
        if (docs) {
            //console.log("Deleted : ", docs);
            return res.status(201).json({ msg: "Orçamento excluído com sucesso!" });
        } else {
            //console.log("Usuário não encontrado : ", docs);
            return res.status(422).json({ msg: "Orçamento não encontrado!" });
        }
    });
});

// Cadastrar Empresa
/*app.post('/auth/registerEmpresa', async(req, res) => {

    const {createdBy, cnpj, name, razao, email, cep, estado, municipio, bairro, complemento, telefone, celular, id_grupo, type} = req.body
    
    const tipo_impressao = "manual";

    //validacoes
    if(!createdBy){
        return res.status(422).json({msg: 'O ID de criação é obrigatório!'})
    }

    if(!cnpj){
        return res.status(422).json({msg: 'O CNPJ é obrigatório!'})
    }

    if(!email){
        return res.status(422).json({msg: 'O E-mail é obrigatório!'})
    }

    if(!name){
        return res.status(422).json({msg: 'O nome é obrigatório!'})
    }
    
    if(!razao){
        return res.status(422).json({msg: 'A Razão Social é obrigatória!'})
    }

    //Checar se empresa existe
    const CnpjExists = await Empresa.findOne({ cnpj: cnpj})
    const EmailExists = await Empresa.findOne({ email: email})

    if(CnpjExists){
        return res.status(422).json({msg: 'Este CNPJ já está cadastrado!'})
    }
    if(EmailExists){
        return res.status(422).json({msg: 'Este E-mail já está cadastrado!'})
    }

    //Verificar SeqID e atribuir ao id_empresa
    Counter.findOneAndUpdate(
        {id:"id_empresa"},
        {"$inc":{"seq":1}},
        {new: true},async(err,counterData)=>{

            let seqId;
            if(err){
                console.log(err);
            }
            if(counterData==null){
                const newVal = new Counter({id:"id_empresa",seq:1});
                newVal.save()
                seqId = 1;
            }else{
                seqId = counterData.seq;
            }
    
            //Capturar Data Atual e formatar
            var data = moment().format();
            console.log(data);
            //Criar Empresa
            const emp = new Empresa({
                id_empresa:seqId,
                createdBy,
                cnpj,
                name,
                razao,
                email,
                cep,
                estado,
                municipio,
                bairro,
                complemento,
                telefone,
                celular,
                tipo_impressao:tipo_impressao,
                id_grupo,
                type,
                status_loja: "especifico",
                createdAt:data,
                inativo:false, 
                bloqueado:false,
            })

            try{

                await emp.save()

                return res.status(201).json({msg: 'Cadastro da empresa realizado com sucesso!'})

            } catch(error){

                console.log(error)

                res
                .status(500)
                .json({msg: 'Aconteceu um erro no servidor, tente novamente mais tarde!',
                })
            }
        }
    );

})*/
/*app.post("/auth/registerEmpresa", async (req, res) => {
    const {
        createdBy,
        cnpj,
        name,
        razao,
        email,
        cep,
        estado,
        municipio,
        bairro,
        complemento,
        telefone,
        celular,
        id_grupo,
        type,
    } = req.body;

    const tipo_impressao = "manual";

    // Validações
    if (!createdBy || !cnpj || !email || !name || !razao) {
        return res
            .status(422)
            .json({ msg: "Todos os campos obrigatórios devem ser preenchidos!" });
    }

    // Checar se a empresa existe
    const CnpjExists = await Empresa.findOne({ cnpj });
    const EmailExists = await Empresa.findOne({ email });

    if (CnpjExists) {
        return res.status(422).json({ msg: "Este CNPJ já está cadastrado!" });
    }
    if (EmailExists) {
        return res.status(422).json({ msg: "Este E-mail já está cadastrado!" });
    }

    // Verificar SeqID e atribuir ao id_empresa
    Counter.findOneAndUpdate(
        { id: "id_empresa" },
        { $inc: { seq: 1 } },
        { new: true },
        async (err, counterData) => {
            let seqId;
            if (err) {
                console.log(err);
            }
            if (counterData == null) {
                const newVal = new Counter({ id: "id_empresa", seq: 1 });
                newVal.save();
                seqId = 1;
            } else {
                seqId = counterData.seq;
            }

            // Capturar Data Atual e formatar
            var data = moment().format();
            console.log(data);

            // Obter latitude e longitude a partir do CEP
            let latitude = null;
            let longitude = null;
            try {
                const response = await axios.get(
                    "https://maps.googleapis.com/maps/api/geocode/json",
                    {
                        params: {
                            address: cep,
                            key: process.env.GOOGLE_MAPS_API_KEY, // Substitua pela sua chave de API do Google Maps
                        },
                    }
                );

                const { results } = response.data;
                if (results && results.length > 0) {
                    const location = results[0].geometry.location;
                    latitude = location.lat;
                    longitude = location.lng;
                } else {
                    console.error("Nenhum resultado encontrado para o CEP fornecido.");
                }
            } catch (error) {
                console.error("Erro ao obter lat e lng:", error);
            }

            // Criar Instancia WP
            let whatsapp = {};
            console.log("🚀 Criando instância do WhatsApp para:", name);
            try {
                const wpInstance = await whatsappManager.createInstance(
                    name,
                    WP_WEBHOOK
                );

                // Verificar se a instância foi criada com sucesso
                if (wpInstance && wpInstance.id) {
                    whatsapp = {
                        id: wpInstance.id,
                        ativo: true,
                        endpoint: wpInstance.endpoint,
                        token: wpInstance.token,
                        instance_status: 400, // Desconectado inicialmente
                        jid: null, // Será preenchido quando conectar
                        baterry: null // Será preenchido quando conectar
                    };
                    console.log("✅ Instância do WhatsApp criada com sucesso!");
                    console.log("📋 Dados da instância:", {
                        id: wpInstance.id,
                        endpoint: wpInstance.endpoint,
                        token: wpInstance.token ? "***" + wpInstance.token.slice(-4) : "N/A"
                    });
                } else {
                    console.error("❌ Falha na criação da instância - resposta inválida:", wpInstance);
                    whatsapp = {}; // Deixa vazio se falhou
                }
            } catch (e) {
                console.error("❌ Erro ao criar instância do WhatsApp:", e);
                console.error("Stack trace:", e.stack);
                whatsapp = {}; // Deixa vazio se falhou
            }

            // Criar Empresa
            const emp = new Empresa({
                id_empresa: seqId,
                createdBy,
                cnpj,
                name,
                razao,
                email,
                cep,
                estado,
                municipio,
                bairro,
                complemento,
                telefone,
                celular,
                tipo_impressao,
                id_grupo,
                type,
                status_loja: "especifico",
                createdAt: data,
                inativo: false,
                bloqueado: false,
                latitude,
                longitude,
                whatsapp: whatsapp,
            });

            try {
                await emp.save();
                return res
                    .status(201)
                    .json({ msg: "Cadastro da empresa realizado com sucesso!" });
            } catch (error) {
                console.log(error);
                res
                    .status(500)
                    .json({
                        msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                    });
            }
        }
    );
});*/

// Função para criar cliente no Asaas (movida da rota principal)
async function criarClienteAsaasEmpresa(nome_empresa, cnpj, email, telefone, address_number, cep) {
    console.log('*** Iniciando criarClienteAsaasEmpresa ***');
    console.log('Parâmetros recebidos:', {
        nome_empresa,
        cnpj,
        email,
        telefone,
        address_number,
        cep
    });

    try {
        console.log('Chamando criarRegistroClienteAsaas...');
        const newCustomer = await criarRegistroClienteAsaas(
            nome_empresa, cnpj, email, telefone, address_number, cep
        );
        console.log('Cliente Asaas criado com sucesso na função:', newCustomer);
        return newCustomer;
    } catch (error) {
        console.error("Erro ao criar cliente no Asaas na função:", error);
        
        let detalhesErro = "Erro desconhecido";
        
        // Se o erro for uma string contendo JSON, tenta extrair o JSON
        const match = error.message.match(/\{.*\}/); // Captura o JSON dentro da string
        
        if (match) {
            try {
                const jsonError = JSON.parse(match[0]); // Converte para objeto JSON
                detalhesErro = jsonError.errors?.[0]?.description || "Erro desconhecido";
            } catch (parseError) {
                console.error("Erro ao fazer o parse do JSON do erro:", parseError);
            }
        }
        
        console.log('Erro tratado:', detalhesErro);
        throw new Error(`Erro ao cadastrar cliente no Asaas: ${detalhesErro}`);
    }
}

// Funções para gerenciar progresso da configuração inicial
async function getProgressoConfiguracaoInicial(empresaIdentifier) {
    try {
        let empresa;
        
        // Verifica se é um ObjectId válido (24 caracteres hexadecimais)
        const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(empresaIdentifier);
        
        if (isValidObjectId) {
            empresa = await Empresa.findById(empresaIdentifier);
        } else {
            empresa = await Empresa.findOne({ cnpj: empresaIdentifier });
        }

        if (!empresa) {
            throw new Error('Empresa não encontrada');
        }

        // Se não existe configuracao_inicial, inicializa com valores padrão
        if (!empresa.configuracao_inicial) {
            empresa.configuracao_inicial = {
                etapas_completas: [1],
                etapa_atual: 2,
                finalizada: false
            };
            await empresa.save();
        }

        return empresa.configuracao_inicial;
    } catch (error) {
        console.error("Erro ao buscar progresso da configuração inicial:", error);
        throw error;
    }
}

async function atualizarProgressoConfiguracaoInicial(empresaIdentifier, etapaCompleta, proximaEtapa, finalizada = false) {
    try {
        let empresa;
        
        // Verifica se é um ObjectId válido (24 caracteres hexadecimais)
        const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(empresaIdentifier);
        
        if (isValidObjectId) {
            empresa = await Empresa.findById(empresaIdentifier);
        } else {
            empresa = await Empresa.findOne({ cnpj: empresaIdentifier });
        }

        if (!empresa) {
            throw new Error('Empresa não encontrada');
        }

        // Inicializa configuracao_inicial se não existir
        if (!empresa.configuracao_inicial) {
            empresa.configuracao_inicial = {
                etapas_completas: [1],
                etapa_atual: 2,
                finalizada: false
            };
        }

        // Adiciona a etapa às completas se ainda não estiver
        if (!empresa.configuracao_inicial.etapas_completas.includes(etapaCompleta)) {
            empresa.configuracao_inicial.etapas_completas.push(etapaCompleta);
        }

        // Atualiza a etapa atual
        if (proximaEtapa) {
            empresa.configuracao_inicial.etapa_atual = proximaEtapa;
        }

        // Se finalizada foi passado como true, marcar como finalizada
        if (finalizada) {
            empresa.configuracao_inicial.finalizada = true;
        }

        // Verifica se todas as etapas foram completadas (1, 2, 3, 4, 5, 6, 7, 8, 9)
        const todasEtapas = [1, 2, 3, 4, 5, 6, 7, 8, 9];
        const todasCompletas = todasEtapas.every(etapa => 
            empresa.configuracao_inicial.etapas_completas.includes(etapa)
        );

        if (todasCompletas) {
            empresa.configuracao_inicial.finalizada = true;
        }

        await empresa.save();

        return empresa.configuracao_inicial;
    } catch (error) {
        console.error("Erro ao atualizar progresso da configuração inicial:", error);
        throw error;
    }
}

// Função para adicionar endereço à empresa
async function adicionarEnderecoEmpresa(empresaIdentifier, enderecoData) {
    const {
        cep,
        estado,
        municipio,
        bairro,
        logradouro,
        address_number,
        complemento
    } = enderecoData;

    try {
        let latitude = null;
        let longitude = null;
        
        // Concatenando os campos para formar um endereço completo, incluindo o CEP
        const enderecoCompleto = `${logradouro}, ${address_number}, ${bairro}, ${municipio}, ${estado}, ${cep}`;

        // Obter latitude e longitude usando Google Maps API
        try {
            const response = await axios.get(
                "https://maps.googleapis.com/maps/api/geocode/json",
                {
                    params: {
                        address: enderecoCompleto,
                        key: process.env.GOOGLE_MAPS_API_KEY,
                    },
                }
            );
            const { results } = response.data;
            if (results.length > 0) {
                const location = results[0].geometry.location;
                latitude = location.lat;
                longitude = location.lng;
            } else {
                console.error("Nenhum resultado encontrado para o CEP fornecido.");
            }
        } catch (error) {
            console.error("Erro ao obter lat e lng:", error);
        }

        // Primeiro, vamos verificar se o identifier é um ObjectId válido ou um CNPJ
        let empresa;
        
        // Verifica se é um ObjectId válido (24 caracteres hexadecimais)
        const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(empresaIdentifier);
        
        if (isValidObjectId) {
            // Se for um ObjectId válido, busca por _id
            empresa = await Empresa.findByIdAndUpdate(
                empresaIdentifier,
                {
                    cep,
                    estado,
                    municipio,
                    bairro,
                    logradouro,
                    address_number,
                    complemento,
                    latitude,
                    longitude
                },
                { new: true }
            );
        } else {
            // Se não for ObjectId, assume que é CNPJ e busca por cnpj
            empresa = await Empresa.findOneAndUpdate(
                { cnpj: empresaIdentifier },
                {
                    cep,
                    estado,
                    municipio,
                    bairro,
                    logradouro,
                    address_number,
                    complemento,
                    latitude,
                    longitude
                },
                { new: true }
            );
        }

        // Atualizar progresso da configuração inicial - marcar etapa 2 como completa
        try {
            // Verificar se já tem cliente Asaas para determinar próxima etapa
            let proximaEtapa = 3;
            if (empresa.customer_asaas && (
                (typeof empresa.customer_asaas === 'string' && empresa.customer_asaas.trim() !== '') ||
                (typeof empresa.customer_asaas === 'object' && empresa.customer_asaas !== null && Object.keys(empresa.customer_asaas).length > 0)
            )) {
                proximaEtapa = 4; // Pular para confirmação se Asaas já configurado
            }
            
            await atualizarProgressoConfiguracaoInicial(empresaIdentifier, 2, proximaEtapa);
        } catch (progressError) {
            console.warn("Erro ao atualizar progresso da configuração inicial:", progressError);
            // Não falha a operação se der erro no progresso
        }

        return empresa;
    } catch (error) {
        console.error("Erro ao adicionar endereço à empresa:", error);
        throw error;
    }
}

// Cliente Cadastrando Empresa
app.post("/createEmpresaUser", async (req, res) => {
    const {
        cnpj,
        nome_empresa,
        email,
        telefone,
        type,
        nome_pessoa,
        password,
        confirmpassword,
        vinculo_empresa,
    } = req.body;

    // Validações
    if (!nome_pessoa)
        return res.status(422).json({ msg: "O nome da Pessoa é obrigatório!" });
    if (!password) return res.status(422).json({ msg: "A senha é obrigatória!" });
    if (password !== confirmpassword)
        return res.status(422).json({ msg: "As senhas não conferem!" });
    if (!vinculo_empresa)
        return res.status(422).json({ msg: "CPF ou CNPJ não informados!" });

    const userExists = await User.findOne({ email: email, inativo: false });
    if (userExists)
        return res
            .status(422)
            .json({ msg: "Este e-mail já está sendo utilizado!" });

    const salt = await bcrypt.genSalt(12);
    const passwordHash = await bcrypt.hash(password, salt);
    const tipo_impressao = "manual";

    if (!cnpj) return res.status(422).json({ msg: "O CPF/CNPJ é obrigatório!" });
    if (!email) return res.status(422).json({ msg: "O E-mail é obrigatório!" });
    if (!nome_empresa)
        return res.status(422).json({ msg: "O nome da Empresa é obrigatório!" });

    const CnpjExists = await Empresa.findOne({ cnpj: cnpj });
    const EmailExists = await Empresa.findOne({ email: email });
    if (CnpjExists)
        return res.status(422).json({ msg: "Este CPF/CNPJ já está cadastrado!" });
    if (EmailExists)
        return res
            .status(422)
            .json({ msg: "Este E-mail já está vinculado a uma empresa!" });

    Counter.findOneAndUpdate(
        { id: "id_empresa" },
        { $inc: { seq: 1 } },
        { new: true },
        async (err, counterData) => {
            let seqId;
            if (err)
                return res
                    .status(500)
                    .json({
                        msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                    });
            seqId = counterData ? counterData.seq : 1;
            if (!counterData) await new Counter({ id: "id_empresa", seq: 1 }).save();

            const data = moment().format();

            const seqIdEmpresa = seqId;
            let whatsapp = {};
            let name = nome_empresa;
            try {
                console.log("🚀 Criando instância do WhatsApp para:", name);
                const wpInstance = await whatsappManager.createInstance(
                    name,
                    WP_WEBHOOK
                );

                // Verificar se a instância foi criada com sucesso
                if (wpInstance && wpInstance.id) {
                    whatsapp = {
                        id: wpInstance.id,
                        name: wpInstance.name, // Nome normalizado para operações de delete
                        ativo: true,
                        endpoint: wpInstance.endpoint,
                        token: wpInstance.token,
                        instance_status: 400, // Desconectado inicialmente
                        jid: null, // Será preenchido quando conectar
                        baterry: null // Será preenchido quando conectar
                    };
                    console.log("✅ Instância do WhatsApp criada com sucesso!");
                    console.log("📋 Dados da instância:", {
                        id: wpInstance.id,
                        endpoint: wpInstance.endpoint,
                        token: wpInstance.token ? "***" + wpInstance.token.slice(-4) : "N/A"
                    });
                } else {
                    console.error("❌ Falha na criação da instância - resposta inválida:", wpInstance);
                    whatsapp = {}; // Deixa vazio se falhou
                }
            } catch (e) {
                console.error("❌ Erro ao criar instância do WhatsApp:", e);
                console.error("Stack trace:", e.stack);
                whatsapp = {}; // Deixa vazio se falhou
            }

            const emp = new Empresa({
                id_empresa: seqId,
                cnpj,
                name: nome_empresa,
                email,
                telefone,
                tipo_impressao,
                type,
                tempoBalcaoMin: "15",
                tempoBalcaoMax: "25",
                tempoEntregaMin: "15",
                tempoEntregaMax: "25",
                createdAt: data,
                inativo: false,
                bloqueado: false,
                region_type_delivery: "raio",
                status_printer: "Offline",
                status_bot: true,
                call_atendente: true,
                fechamento_temporario: false,
                whatsapp: whatsapp,
                importacao_finalizada: false,
                formas_pagamento: ["Cartão", "Pix", "Dinheiro"],
            });

            try {
                Counter.findOneAndUpdate(
                    { id: "id_user" },
                    { $inc: { seq: 1 } },
                    { new: true },
                    async (err, counterData) => {
                        let seqId;
                        if (err)
                            return res
                                .status(500)
                                .json({
                                    msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                                });
                        seqId = counterData ? counterData.seq : 1;
                        if (!counterData)
                            await new Counter({ id: "id_user", seq: 1 }).save();

                        const data = moment().format();
                        const user = new User({
                            id_user: seqId,
                            name: nome_pessoa,
                            email,
                            password: passwordHash,
                            vinculo_empresa,
                            role: "empresa",
                            email_activated: false,
                            createdAt: data,
                            inativo: false,
                            bloqueado: false,
                        });

                        try {
                            /*const subscriptionData = await createIuguSubscription(
                                "free_trial",
                                emp.customer_iugu
                            );*/
                            //subscriptionIdIugu = subscriptionData.id;
                            //invoiceId = subscriptionData.recent_invoices[0].id;
                            const subscriptionData = {
                                "status": "INACTIVE"
                            }
                            const assinatura = new Assinatura({
                                empresa: emp._id,
                                assinatura_obj: subscriptionData,
                            });

                            await assinatura.save();

                            await emp.save();
                            // Chama a função para criar a assinatura filtrada no Google Cloud Pub/Sub
                            const subscriptionName = `fila-impressao-sub-${emp._id}`;
                            await criarAssinaturaFiltrada(
                                nomeTopico,
                                subscriptionName,
                                emp._id
                            );
                            console.log(
                                `Assinatura Google Cloud Pub/Sub criada para a empresa ${emp.name} com ID ${emp._id}`
                            );

                            // Atualiza o campo `fila_impressao_sub` na empresa
                            emp.fila_impressao_sub = subscriptionName;

                            // Popula as perguntas e respostas padrão para a empresa criada
                            populateCompanyResponses(emp._id);

                            await emp.save();

                            await user.save();

                            const removerMascara = (documento) => {
                                return documento.replace(/\D/g, ""); // Remove tudo que não for número
                            };

                            try {
                                await axios.post("https://hook.us2.make.com/ds8m3pv3vez66qohw77f287i8c49uxli", {
                                    name: nome_pessoa,
                                    email: email,
                                    nomeloja: nome_empresa,
                                    cpfcnpj: removerMascara(cnpj),
                                    phone: telefone
                                });
                                console.log("Webhook enviado com sucesso para o MAKE.");
                            } catch (error) {
                                console.error("Erro ao enviar webhook:", error);
                            }

                            return res
                                .status(201)
                                .json({
                                    msg: "Parabéns seu cadastro foi concluído com sucesso, você está pronto para vender muito?",
                                    seqIdEmpresa: seqIdEmpresa,
                                });
                        } catch (error) {
                            console.log(error);
                            res
                                .status(500)
                                .json({
                                    msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                                });
                        }
                    }
                );
            } catch (error) {
                console.log(error);
                res
                    .status(500)
                    .json({
                        msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                    });
            }
        }
    );
});

// Rota para adicionar endereço à empresa
app.put("/addEnderecoEmpresa/:id", async (req, res) => {
    const { id } = req.params;
    const enderecoData = req.body;

    // Validações
    if (!enderecoData.cep) 
        return res.status(422).json({ msg: "O CEP é obrigatório!" });
    if (!enderecoData.estado) 
        return res.status(422).json({ msg: "O estado é obrigatório!" });
    if (!enderecoData.municipio) 
        return res.status(422).json({ msg: "O município é obrigatório!" });
    if (!enderecoData.bairro) 
        return res.status(422).json({ msg: "O bairro é obrigatório!" });
    if (!enderecoData.logradouro) 
        return res.status(422).json({ msg: "O logradouro é obrigatório!" });
    if (!enderecoData.address_number) 
        return res.status(422).json({ msg: "O número é obrigatório!" });

    try {
        const empresa = await adicionarEnderecoEmpresa(id, enderecoData);
        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada!" });
        }
        return res.status(200).json({ 
            msg: "Endereço adicionado com sucesso!", 
            empresa: empresa 
        });
    } catch (error) {
        console.error("Erro ao adicionar endereço:", error);
        return res.status(500).json({ 
            msg: "Erro interno do servidor ao adicionar endereço!" 
        });
    }
});

// Rota para buscar progresso da configuração inicial
app.get("/getProgressoConfiguracaoInicial/:id", async (req, res) => {
    const { id } = req.params;

    try {
        const progresso = await getProgressoConfiguracaoInicial(id);
        return res.status(200).json({ 
            progresso: progresso 
        });
    } catch (error) {
        console.error("Erro ao buscar progresso da configuração inicial:", error);
        return res.status(500).json({ 
            msg: "Erro interno do servidor ao buscar progresso!" 
        });
    }
});

// Rota para atualizar progresso da configuração inicial
app.put("/atualizarProgressoConfiguracaoInicial/:id", async (req, res) => {
    const { id } = req.params;
    const { etapaCompleta, proximaEtapa, finalizada } = req.body;

    // Validações
    if (!etapaCompleta) 
        return res.status(422).json({ msg: "A etapa completa é obrigatória!" });

    try {
        const progresso = await atualizarProgressoConfiguracaoInicial(id, etapaCompleta, proximaEtapa, finalizada);
        return res.status(200).json({ 
            msg: "Progresso atualizado com sucesso!", 
            progresso: progresso 
        });
    } catch (error) {
        console.error("Erro ao atualizar progresso da configuração inicial:", error);
        return res.status(500).json({ 
            msg: "Erro interno do servidor ao atualizar progresso!" 
        });
    }
});

// Rota para criar cliente Asaas para empresa
app.post("/createAsaasCustomer/:id", async (req, res) => {
    const { id } = req.params;
    const { address_number, cep } = req.body;

    try {
        // Buscar dados da empresa - verificar se é ObjectId ou CNPJ
        let empresa;
        const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(id);
        
        if (isValidObjectId) {
            empresa = await Empresa.findById(id);
        } else {
            empresa = await Empresa.findOne({ cnpj: id });
        }
        
        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada!" });
        }

        // Debug da empresa encontrada
        console.log('Empresa encontrada:', {
            id: empresa._id,
            name: empresa.name,
            customer_asaas: empresa.customer_asaas,
            tipo_customer_asaas: typeof empresa.customer_asaas,
            customer_asaas_length: empresa.customer_asaas ? empresa.customer_asaas.length : 0
        });

        // Verificar se já tem cliente Asaas (string válida)
        if (empresa.customer_asaas && 
            typeof empresa.customer_asaas === 'string' && 
            empresa.customer_asaas.trim() !== '' && 
            empresa.customer_asaas !== 'null' && 
            empresa.customer_asaas !== 'undefined') {
            
            console.log('Cliente Asaas já existe:', empresa.customer_asaas);
            return res.status(200).json({ 
                msg: "Empresa já possui cliente Asaas!", 
                customer_id: empresa.customer_asaas,
                already_exists: true
            });
        }

        console.log('Cliente Asaas não existe ou é inválido, criando novo...');

        // Validações necessárias para o Asaas
        if (!address_number) 
            return res.status(422).json({ msg: "O número do endereço é obrigatório!" });
        if (!cep) 
            return res.status(422).json({ msg: "O CEP é obrigatório!" });

        // Criar cliente no Asaas
        console.log('Criando cliente Asaas com dados:', {
            name: empresa.name,
            cnpj: empresa.cnpj,
            email: empresa.email,
            telefone: empresa.telefone,
            address_number,
            cep
        });

        const newCustomer = await criarClienteAsaasEmpresa(
            empresa.name, 
            empresa.cnpj, 
            empresa.email, 
            empresa.telefone, 
            address_number, 
            cep
        );

        console.log('Cliente Asaas criado com sucesso:', newCustomer);

        // Atualizar empresa com customer_asaas
        empresa.customer_asaas = newCustomer.id;
        console.log('Salvando customer_asaas na empresa:', newCustomer.id);
        
        const empresaSalva = await empresa.save();
        console.log('Empresa salva com customer_asaas:', {
            id: empresaSalva._id,
            customer_asaas: empresaSalva.customer_asaas
        });

        // Atualizar progresso da configuração inicial - marcar que integração Asaas foi completa
        try {
            await atualizarProgressoConfiguracaoInicial(id, 4, 5);
            console.log('Progresso atualizado: cliente Asaas criado');
        } catch (progressError) {
            console.warn("Erro ao atualizar progresso da configuração inicial:", progressError);
            // Não falha a operação se der erro no progresso
        }

        return res.status(201).json({ 
            msg: "Cliente Asaas criado com sucesso!", 
            customer_id: newCustomer.id 
        });
    } catch (error) {
        console.error("Erro ao criar cliente Asaas:", error);
        return res.status(500).json({ 
            msg: error.message || "Erro interno do servidor!" 
        });
    }
});

const createWhatsAppInstanceForEmpresa = async (empresaId) => {
    try {
        console.log(`🔍 Verificando se a empresa ${empresaId} já tem uma instância WhatsApp...`);

        // 1️⃣ Buscar a empresa pelo _id
        const empresa = await Empresa.findById(empresaId);

        if (!empresa) {
            console.log(`❌ Empresa com _id ${empresaId} não encontrada!`);
            return null;
        }

        // 2️⃣ Verificar se já existe o campo `whatsapp`
        if (empresa.whatsapp && empresa.whatsapp.id) {
            console.log(`✅ A empresa ${empresa.name} já possui uma instância WhatsApp. Pulando criação.`);
            return empresa.whatsapp;
        }

        console.log(`🚀 Criando nova instância do WhatsApp para a empresa: ${empresa.name}...`);

        // 3️⃣ Criar a instância do WhatsApp com webhook específico
        console.log(`🎯 Usando webhook específico para empresa: ${empresaId}`);
        const wpInstance = await whatsappManager.createInstance(empresa.name, empresaId);

        // Verificar se a instância foi criada com sucesso
        if (!wpInstance || !wpInstance.id) {
            console.error("❌ Falha na criação da instância - resposta inválida:", wpInstance);
            return null;
        }

        const whatsappData = {
            id: wpInstance.id,
            name: wpInstance.name, // Nome normalizado para operações de delete
            ativo: true,
            endpoint: wpInstance.endpoint,
            token: wpInstance.token,
            instance_status: 400, // Desconectado inicialmente
            jid: null, // Será preenchido quando conectar
            baterry: null // Será preenchido quando conectar
        };

        // 4️⃣ Atualizar a empresa com os dados do WhatsApp
        empresa.whatsapp = whatsappData;
        await empresa.save();

        console.log(`✅ Instância do WhatsApp criada e vinculada à empresa: ${empresa.name}!`);
        console.log("📋 Dados da instância:", {
            id: wpInstance.id,
            endpoint: wpInstance.endpoint,
            token: wpInstance.token ? "***" + wpInstance.token.slice(-4) : "N/A"
        });
        return whatsappData;

    } catch (error) {
        console.error(`❌ Erro ao criar instância do WhatsApp para a empresa ${empresaId}:`, error);
        return null;
    }
};

// 🔹 Chamada única para uma empresa específica (_id já conhecido)
/*(async () => {
    const empresaId = "67a125efc404617d42f32226"; // Substitua pelo _id real da empresa
    const whatsappInstance = await createWhatsAppInstanceForEmpresa(empresaId);

    if (whatsappInstance) {
        console.log("📌 Dados da Instância do WhatsApp:", whatsappInstance);
    } else {
        console.log(`⚠️ Falha ao criar ou vincular a instância do WhatsApp para a empresa ${empresaId}.`);
    }
})();*/



app.get("/getDaysToExpireLicense/:id", async (req, res) => {
    const { id } = req.params;

    try {
        // Busca a assinatura com base no ID da empresa
        const assinatura = await Assinatura.findOne({ empresa: id });

        if (!assinatura) {
            return res.status(404).json({ msg: "Assinatura não encontrada" });
        }

        // Obtém o subscriptionId da assinatura
        const subscriptionId = assinatura.assinatura_obj.id;

        if (!subscriptionId) {
            return res.status(404).json({ msg: "ID da assinatura não encontrado" });
        }

        // Obtém todas as cobranças da assinatura
        const allInvoices = await getAllInvoices(subscriptionId);

        if (!allInvoices || allInvoices.length === 0) {
            return res.status(204).json({ msg: "Nenhuma cobrança encontrada para esta assinatura" });
        }

        // Filtra cobranças com status PENDING ou OVERDUE
        const relevantInvoices = allInvoices.filter(
            (invoice) => invoice.status === "PENDING" || invoice.status === "OVERDUE"
        );

        if (relevantInvoices.length === 0) {
            return res.status(204).json({ msg: "Nenhuma cobrança pendente ou vencida encontrada" });
        }

        // Seleciona a cobrança mais próxima baseada na dueDate
        const nextInvoice = relevantInvoices.sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate))[0];

        // Calcula os dias restantes para o vencimento
        const dueDate = moment(nextInvoice.dueDate, "YYYY-MM-DD");
        const currentDate = moment();
        let daysRemaining = dueDate.diff(currentDate, "days");

        // Se a cobrança estiver vencida, define daysRemaining como 0
        if (nextInvoice.status === "OVERDUE" || daysRemaining < 0) {
            daysRemaining = 0;
        }

        const plan_type = assinatura.assinatura_obj.externalReference;

        return res.status(200).json({ daysRemaining, plan_type });
    } catch (error) {
        console.error("Erro ao obter os dias restantes:", error);
        return res.status(500).json({ msg: "Erro interno do servidor" });
    }
});

// Função para criar a mensagem de acordo com o status do pedido
function createMessageNewPedido(status_pedido, pedido) {
    // Função auxiliar para garantir que o valor é um número
    function ensureNumber(value) {
        return Number.isFinite(value) ? value : 0;
    }

    // Função auxiliar para formatar os itens do pedido
    function formatItens(itens) {
        return itens
            .map((item) => {
                // Verificar se o item tem adicionais
                const adicionaisFormatados =
                    item.grupo_adicionais && item.grupo_adicionais.length > 0
                        ? item.grupo_adicionais
                            .map((grupo) => {
                                // Verificar se o grupo tem adicionais
                                return grupo.adicionais && grupo.adicionais.length > 0
                                    ? grupo.adicionais
                                        .map(
                                            (adicional) =>
                                                `  - ${adicional.quantity}x ${adicional.title}\n` +
                                                `    Valor Unitário: R$ ${ensureNumber(
                                                    adicional.price
                                                ).toFixed(2)}`
                                        )
                                        .join("\n")
                                    : ""; // Se não houver adicionais, retorna uma string vazia
                            })
                            .join("\n")
                        : ""; // Se não houver grupos de adicionais, retorna uma string vazia

                // Formatar o item principal
                return (
                    `➡ ${item.quantidade}x ${item.nomeItem}\n` +
                    `    Valor Unitário: R$ ${ensureNumber(item.valor).toFixed(2)}\n` +
                    `${adicionaisFormatados ? "Adicionais:\n" + adicionaisFormatados : ""
                    }`
                );
            })
            .join("\n\n");
    }

    switch (status_pedido) {
        case "1":
            return `Só pra dizer que o seu pedido nº *${pedido.id_pedido_counter ?? pedido.id_pedido
                }* está *em análise* 🤩

            Itens:
            ${formatItens(pedido.itens)}

            ${pedido.entrega.tipo_entrega === "Entrega"
                    ? `🏍️ *Entrega*`
                    : `🏪 *Retirada no local*`
                }

            Total: *R$ ${ensureNumber(pedido.valor_total).toFixed(2)}*

            Obrigado pela preferência, se precisar de algo é só chamar! 😉`;
        case "2":
            return `Só pra dizer que o seu pedido nº *${pedido.id_pedido_counter ?? pedido.id_pedido
                }* está *em produção* 🤩

            Itens:
            ${formatItens(pedido.itens)}

            ${pedido.entrega.tipo_entrega === "Entrega"
                    ? `🏍️ *Entrega*`
                    : `🏪 *Retirada no local*`
                }

            Total: *R$ ${ensureNumber(pedido.valor_total).toFixed(2)}*

            Obrigado pela preferência, se precisar de algo é só chamar! 😉`;
        default:
            return null; // Não enviar mensagem se o status não for um dos casos especificados
    }
}

// Cadastrar Pedidos
app.post("/auth/registerPedido", async (req, res) => {
    const {
        createdBy,
        id_empresa,
        itens,
        celular_cliente,
        nome_cliente,
        tipo_pagamento,
        entrega,
        desconto,
        cpf_cnpj,
        valor_troco,
        valor_total,
        descricao,
        external_id,
    } = req.body;

    //validacoes
    if (!createdBy) {
        return res.status(422).json({ msg: "O ID de criação é obrigatório!" });
    }

    if (!itens) {
        return res.status(422).json({ msg: "O Item é obrigatório!" });
    }

    if (!celular_cliente) {
        return res.status(422).json({ msg: "O Celular é obrigatório!" });
    }

    if (!nome_cliente) {
        return res.status(422).json({ msg: "O nome é obrigatório!" });
    }

    if (!tipo_pagamento) {
        return res.status(422).json({ msg: "A tipo de pagamento é obrigatório!" });
    }
    if (!entrega) {
        return res.status(422).json({ msg: "A forma de entrega é obrigatória!" });
    }

    // Verificar se a empresa existe e retornar o tipo_impressao
    const empresa = await Empresa.findOne(
        { id_empresa: id_empresa },
        "tipo_impressao name cnpj last_reset_date _id"
    );
    var status_pedido = "1";
    var status_print = false;
    if (empresa) {
        const tipo_impressao = empresa.tipo_impressao;
        console.log("Empresa:", empresa);
        console.log("Tipo Impressao>", tipo_impressao);
        if (tipo_impressao === "manual") {
            console.log("passou aqui:", tipo_impressao);
            status_pedido = "1";
            status_print = false;
        } else if (tipo_impressao === "automatico") {
            console.log("PASSOU AQUI:", tipo_impressao);
            status_pedido = "2";
            status_print = true;
        }

        // Verificar counter de pedidos do cliente para gravar no pedido
        // 🔹 Manter a lógica original do counter_qtd_pedido do cliente
        let counterPedidoCliente = await Cliente.findOneAndUpdate(
            { id_empresa: id_empresa, telefone: celular_cliente },
            { $inc: { counter_qtd_pedido: 1 } },
            { new: true }
        );

        // 🔹 Se o cliente ainda não existir, cria um novo e seta counter_qtd_pedido = 1
        if (!counterPedidoCliente) {
            const novoCliente = new Cliente({
                id_empresa: id_empresa,
                nome: nome_cliente,
                telefone: celular_cliente,
                vinculo_empresa: empresa.cnpj,
                counter_qtd_pedido: 1,
            });
            await novoCliente.save();
            counterPedidoCliente = novoCliente;
        }

        // 🔹 Pegando o ObjectId do cliente
        const objIdCliente = counterPedidoCliente._id;

        // Buscar o último pedido da empresa ordenando pela data de criação em ordem decrescente
        let lastPedido = await Pedidos.findOne({ id_empresa })
            .sort({ createdAt: -1 }) // Ordena pela data de criação
            .select("id_pedido_counter createdAt");
        console.log(
            "Último pedido encontrado:",
            lastPedido || "Nenhum pedido encontrado"
        );
        // Definir o novo id_pedido_counter
        let newIdPedidoCounter = 1;
        // Log para verificar last_reset_date
        console.log("Data de reset da empresa:", empresa.last_reset_date);
        // Verificar se existe um last_reset_date e ele é posterior ao último pedido
        if (
            empresa.last_reset_date &&
            (!lastPedido ||
                !lastPedido.id_pedido_counter ||
                lastPedido.createdAt < empresa.last_reset_date)
        ) {
            // Reinicia o contador pois o último pedido é anterior ao reset ou o contador não existe
            console.log(
                "Reiniciando contador, pois o último pedido é anterior ao reset ou id_pedido_counter não existe."
            );
            newIdPedidoCounter = 1;
        } else if (lastPedido) {
            // Caso contrário, incrementa com base no último pedido
            newIdPedidoCounter = (lastPedido.id_pedido_counter || 0) + 1;
            console.log(
                "Incrementando contador com base no último pedido. Novo contador:",
                newIdPedidoCounter
            );
        }
        // Verifique o valor final do contador antes de salvar
        console.log(
            "Valor final do id_pedido_counter para o novo pedido:",
            newIdPedidoCounter
        );

        //Verificar SeqID e atribuir ao id_pedido
        Counter.findOneAndUpdate(
            { id: "empresa:" + id_empresa + ";id_pedido" },
            { $inc: { seq: 1 } },
            { new: true },
            async (err, counterData) => {
                let seqId;
                if (err) {
                    console.log(err);
                }
                if (counterData == null) {
                    const newVal = new Counter({
                        id: "empresa:" + id_empresa + ";id_pedido",
                        seq: 1,
                    });
                    newVal.save();
                    seqId = 1;
                } else {
                    seqId = counterData.seq;
                }

                // Capturar lead_id
                const lead_id = await findLeadId(empresa._id, celular_cliente);

                //Capturar Data Atual e formatar
                var data = moment().format();
                console.log(data);
                //Criar Pedido
                const pedido = new Pedidos({
                    id_pedido: seqId,
                    createdBy,
                    id_empresa,
                    status_pedido: status_pedido,
                    itens,
                    celular_cliente,
                    nome_cliente,
                    tipo_pagamento,
                    entrega,
                    valor_troco,
                    valor_total,
                    descricao,
                    desconto,
                    cpf_cnpj,
                    counter_qtd_pedido: counterPedidoCliente.counter_qtd_pedido,
                    status_print: status_print,
                    id_pedido_counter: newIdPedidoCounter,
                    createdAt: data,
                    lead_id,
                    objIdCliente // 🔹 Adicionando o ObjectId do cliente ao pedido
                });

                try {
                    // Enviar mensagem para o cliente via WhatsApp em segundo plano
                    setImmediate(async () => {
                        try {
                            const empresa = await Empresa.findOne({
                                id_empresa: pedido.id_empresa,
                            }).select("whatsapp _id");

                            if (
                                !empresa ||
                                !empresa.whatsapp ||
                                !empresa.whatsapp.endpoint ||
                                !empresa.whatsapp.token ||
                                !empresa.whatsapp.id
                            ) {
                                console.error(
                                    "Configuração do WhatsApp não encontrada para a empresa."
                                );
                                return;
                            }
                            
                            console.log("✅ Empresa encontrada para envio WhatsApp:", empresa.name || empresa._id);
                            
                            // **🔧 CORREÇÃO: Adicionar instanceId para Evolution API**
                            const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
                            const whatsappInstance = new Whatsapp(
                                empresa.whatsapp.endpoint,
                                empresa.whatsapp.token,
                                instanceIdentifier  // ✅ Agora com instanceId!
                            );
                            
                            // **🔧 CORREÇÃO: Formato correto do número para Evolution API**
                            const cleanNumber = cleanPhoneNumber(celular_cliente);
                            const clienteNumero = `${cleanNumber}@s.whatsapp.net`; // ✅ Formato WhatsApp correto
                            const mensagem = createMessageNewPedido(status_pedido, pedido);

                            console.log(`📤 Enviando mensagem de novo pedido para ${clienteNumero}: ${mensagem?.substring(0, 50)}...`);

                            if (mensagem) {
                                await whatsappInstance.SendSimpleTxtMessage(
                                    clienteNumero,
                                    mensagem
                                );
                                console.log("✅ Mensagem de novo pedido enviada com sucesso via Evolution API.");
                            } else {
                                console.log(
                                    "⚠️ Nenhuma mensagem enviada devido ao status do pedido."
                                );
                            }
                        } catch (sendError) {
                            console.error("❌ Erro ao enviar mensagem via Evolution API:", sendError.message);
                            console.error("📋 Detalhes do erro:", sendError);
                        }
                    });

                    await pedido.save();

                    const leadChannelId = external_id || lead_id; // Usa external_id se disponível, senão usa lead_id
                    // If external_id is provided, update LeadChannel with client ID
                    if (leadChannelId) {
                        // Find LeadChannel document by its ID
                        const leadChannel = await LeadChannel.findById(leadChannelId);

                        if (leadChannel) {
                            // Find the client based on id_empresa and celular_cliente
                            const cliente = await Cliente.findOne({
                                id_empresa: id_empresa,
                                telefone: celular_cliente,
                            });

                            if (cliente) {
                                // Update LeadChannel with the client ID
                                leadChannel.id_cliente = cliente._id;
                                await leadChannel.save();
                            }
                        }
                    }

                    if (status_pedido === "2" && status_print === true) {
                        //const dadosPedido = { id: seqId, cliente: nome_cliente };
                        // Dados de exemplo, substitua com os dados reais
                        const dataFormatada = momentTz
                            .tz(data, "America/Sao_Paulo")
                            .format("DD/MM/YYYY HH:mm");
                        const dadosComanda = {
                            createdBy: createdBy,
                            tipoPedido: entrega.tipo_entrega,
                            dataPedido: dataFormatada,
                            nomeLoja: empresa.name,
                            numeroPedido: newIdPedidoCounter,
                            itens: itens,
                            cliente: {
                                nome: nome_cliente,
                                telefone: celular_cliente,
                                qtdPedidos: counterPedidoCliente.counter_qtd_pedido,
                            },
                            entrega: {
                                endereco:
                                    entrega.endereco.rua +
                                    ", " +
                                    entrega.endereco.numero +
                                    ", " +
                                    entrega.endereco.complemento,
                                referencia: entrega.endereco.referencia,
                                bairroAndCity:
                                    entrega.endereco.bairro + ", " + entrega.endereco.cidade,
                                cep: entrega.endereco.cep,
                            },
                            pagamento: {
                                forma: tipo_pagamento,
                                subtotal: valor_total - entrega.valor,
                                taxaEntrega: entrega.valor,
                                total: valor_total,
                            },
                            observacoes: descricao,
                            troco: valor_troco,
                        };
                        gerarPDF(dadosComanda)
                            .then((caminhoArquivo) => {
                                console.log("Comanda gerada com sucesso:", caminhoArquivo);
                                // Aqui você pode enviar o arquivo para impressão ou fazer outras operações
                                enviarParaImpressao(caminhoArquivo, empresa._id);
                            })
                            .catch((erro) => {
                                console.error("Erro ao gerar a comanda:", erro);
                            });
                    }

                    io.to(empresa._id.toString()).emit("novoPedido", {
                        msg: "Pedido gerado com sucesso!",
                        pedido,
                    });

                    console.log(`Emitindo para a empresa ${empresa._id}`);
                    // Após salvar um novo pedido enviar comando de impressão caso seja automática
                    /* Não utilizarei mais, vou enviar a impressão direto do servidor
                              if(pedido.status_print){
                                  io.to(empresa._id.toString()).emit('novoPedidoParaImpressao', { pedidoId: novoPedidoId });
                                                                      console.log(`Emitindo para a empresa ${empresa._id} para impressão`);
                              }*/

                    return res.status(201).json({ msg: "Pedido gerado com sucesso!" });
                } catch (error) {
                    console.log(error);

                    res.status(500).json({
                        msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                    });
                }
            }
        );
    } else {
        res.status(404).json({ msg: "Empresa não encontrada" });
    }
});

// Rota para ativar ou desativar o bot para um lead_id específico
app.put('/auth/toggle-bot/:lead_id', async (req, res) => {
    try {
        const { lead_id } = req.params;
        // Verifica se o lead_id é um ObjectId válido antes de converter
        if (!ObjectId.isValid(lead_id)) {
            return res.status(400).json({ msg: "ID de lead inválido!" });
        }

        const lead = await LeadChannel.findById(new ObjectId(lead_id));

        if (!lead) {
            return res.status(404).json({ msg: "Lead não encontrado!" });
        }

        // Alternar entre true/false
        lead.bot_pausado = !lead.bot_pausado;
        await lead.save();

        res.json({ msg: `Bot ${lead.bot_pausado ? 'pausado' : 'reativado'} com sucesso!`, bot_pausado: lead.bot_pausado });
    } catch (error) {
        console.error(error);
        res.status(500).json({ msg: "Erro ao alterar o status do bot." });
    }
});

app.get('/auth/bot-status-lead/:lead_id', async (req, res) => {
    try {
        const { lead_id } = req.params;

        // Verifica se o lead_id é um ObjectId válido antes de converter
        if (!ObjectId.isValid(lead_id)) {
            return res.status(400).json({ msg: "ID de lead inválido!" });
        }

        // Buscar o lead no banco de dados
        const lead = await LeadChannel.findById(new ObjectId(lead_id));

        if (!lead) {
            return res.status(404).json({ msg: "Lead não encontrado!" });
        }

        // Retorna o status do bot
        res.json({ bot_pausado: lead.bot_pausado });

    } catch (error) {
        console.error(error);
        res.status(500).json({ msg: "Erro ao obter o status do bot." });
    }
});


// Atualizar Pedido
app.put("/auth/updatePedidoFromPdv", async (req, res) => {
    const {
        objIdPedido,
        id_empresa,
        itens,
        celular_cliente,
        nome_cliente,
        tipo_pagamento,
        entrega,
        desconto,
        cpf_cnpj,
        valor_troco,
        valor_total,
        descricao,
        external_id,
    } = req.body; // Os dados do pedido

    // Verificação das entradas obrigatórias
    if (!objIdPedido) {
        return res.status(422).json({ msg: "O ID do pedido é obrigatório!" });
    }

    // Verificações de campos obrigatórios
    //if (!createdBy) return res.status(422).json({ msg: 'O ID de criação é obrigatório!' });
    if (!itens) return res.status(422).json({ msg: "O Item é obrigatório!" });
    if (!celular_cliente)
        return res.status(422).json({ msg: "O Celular é obrigatório!" });
    if (!nome_cliente)
        return res.status(422).json({ msg: "O nome é obrigatório!" });
    if (!tipo_pagamento)
        return res.status(422).json({ msg: "A tipo de pagamento é obrigatório!" });
    if (!entrega)
        return res.status(422).json({ msg: "A forma de entrega é obrigatória!" });

    try {
        // Encontrar o pedido existente
        const pedido = await Pedidos.findById(objIdPedido);

        if (!pedido) {
            return res.status(404).json({ msg: "Pedido não encontrado!" });
        }

        // Atualizar os campos do pedido
        //pedido.createdBy = createdBy || pedido.createdBy;
        //pedido.id_empresa = id_empresa || pedido.id_empresa;
        pedido.itens = itens || pedido.itens;
        pedido.celular_cliente = celular_cliente || pedido.celular_cliente;
        pedido.nome_cliente = nome_cliente || pedido.nome_cliente;
        pedido.tipo_pagamento = tipo_pagamento || pedido.tipo_pagamento;
        pedido.entrega = entrega || pedido.entrega;
        pedido.desconto = desconto || pedido.desconto;
        pedido.cpf_cnpj = cpf_cnpj || pedido.cpf_cnpj;
        pedido.valor_troco = valor_troco || pedido.valor_troco;
        pedido.valor_total = valor_total || pedido.valor_total;
        pedido.descricao = descricao !== undefined ? descricao : pedido.descricao;

        // Salvar a atualização do pedido
        await pedido.save();

        // Se o `external_id` foi fornecido, você pode querer atualizar o canal de leads
        if (external_id) {
            const leadChannel = await LeadChannel.findById(external_id);
            if (leadChannel) {
                const cliente = await Cliente.findOne({
                    id_empresa: id_empresa,
                    telefone: celular_cliente,
                });
                if (cliente) {
                    leadChannel.id_cliente = cliente._id;
                    await leadChannel.save();
                }
            }
        }

        // Retornar sucesso
        return res
            .status(201)
            .json({ msg: "Pedido atualizado com sucesso!", pedido });
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .json({ msg: "Erro ao atualizar pedido, tente novamente mais tarde!" });
    }
});

app.put("/auth/updatePedidoFromMesas", async (req, res) => {
    const {
        objIdPedido,
        id_empresa,
        itens,
        celular_cliente,
        nome_cliente,
        cpf_cnpj,
        valor_troco,
        valor_total,
        descricao,
        external_id,
    } = req.body; // Os dados do pedido

    // Verificação das entradas obrigatórias
    if (!objIdPedido) {
        return res.status(422).json({ msg: "O ID do pedido é obrigatório!" });
    }
    if (!itens) return res.status(422).json({ msg: "O Item é obrigatório!" });

    try {
        // Encontrar o pedido existente
        const pedido = await Pedidos.findById(objIdPedido);

        if (!pedido) {
            return res.status(404).json({ msg: "Pedido não encontrado!" });
        }

        // Atualizar os campos do pedido
        //pedido.createdBy = createdBy || pedido.createdBy;
        //pedido.id_empresa = id_empresa || pedido.id_empresa;
        pedido.itens = itens || pedido.itens;
        pedido.celular_cliente = celular_cliente || pedido.celular_cliente;
        pedido.nome_cliente = nome_cliente || pedido.nome_cliente;
        pedido.valor_total = valor_total || pedido.valor_total;
        pedido.descricao = descricao !== undefined ? descricao : pedido.descricao;

        // Salvar a atualização do pedido
        await pedido.save();

        const mesa = await Mesas.findOne({ "pedidos.pedido_id": objIdPedido }).populate("pedidos.id_pedido");

        console.log("mesa", mesa);

        mesa.total = mesa.pedidos.reduce((acc, pedido) => acc + pedido.id_pedido.valor_total, 0);

        await mesa.save();

        // Se o `external_id` foi fornecido, você pode querer atualizar o canal de leads
        if (external_id) {
            const leadChannel = await LeadChannel.findById(external_id);
            if (leadChannel) {
                const cliente = await Cliente.findOne({
                    id_empresa: id_empresa,
                    telefone: celular_cliente,
                });
                if (cliente) {
                    leadChannel.id_cliente = cliente._id;
                    await leadChannel.save();
                }
            }
        }

        // Retornar sucesso
        return res
            .status(201)
            .json({ msg: "Pedido atualizado com sucesso!", pedido });
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .json({ msg: "Erro ao atualizar pedido, tente novamente mais tarde!" });
    }
});

app.post("/reset-pedido-counter", async (req, res) => {
    const { id_empresa } = req.body;

    try {
        // Atualiza a data de reset do contador
        const empresa = await Empresa.findOneAndUpdate(
            { _id: id_empresa }, // ou, se for o id_empresa: { id_empresa }
            { last_reset_date: new Date() },
            { new: true }
        );

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada." });
        }

        return res
            .status(200)
            .json({ msg: "Contador de pedidos redefinido com sucesso." });
    } catch (error) {
        console.error("Erro ao redefinir o contador de pedidos:", error);
        return res
            .status(500)
            .json({ msg: "Erro ao redefinir o contador de pedidos." });
    }
});

app.post("/configurar-reset-pedido", async (req, res) => {
    const { id_empresa, dias_para_reset } = req.body;

    try {
        if (dias_para_reset < 0) {
            return res.status(400).json({ msg: "O valor mínimo para reset é 0." });
        }

        const empresa = await Empresa.findByIdAndUpdate(
            id_empresa,
            { dias_para_reset },
            { new: true }
        );

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada." });
        }

        return res
            .status(200)
            .json({
                msg: "Configuração de reset automático atualizada com sucesso.",
            });
    } catch (error) {
        console.error("Erro ao configurar reset automático:", error);
        return res
            .status(500)
            .json({ msg: "Erro ao configurar reset automático." });
    }
});

// Rota para obter o ID do último pedido da empresa
app.get("/ultimoPedidoID/:id", async (req, res) => {
    const { id } = req.params;

    try {
        // Buscar o último pedido da empresa ordenando pela data de criação em ordem decrescente
        const ultimoPedido = await Pedidos.findOne({ id_empresa: parseInt(id) })
            .sort({ createdAt: -1 }) // Ordena pela data de criação em ordem decrescente
            .select("id_pedido_counter id_pedido") // Seleciona ambos os campos para verificação
            .exec();

        if (!ultimoPedido) {
            return res
                .status(404)
                .json({ msg: "Nenhum pedido encontrado para essa empresa." });
        }

        // Verifica se id_pedido_counter está presente; se não, usa id_pedido
        const pedidoId = ultimoPedido.id_pedido_counter ?? ultimoPedido.id_pedido;

        res.json({ ultimoPedidoId: pedidoId });
    } catch (error) {
        console.error("Erro ao buscar o último pedido:", error);
        res.status(500).json({ msg: "Erro ao buscar o último pedido da empresa." });
    }
});

app.get('/clientes-ativos/:empresaObjectId', async (req, res) => {
    try {
        const { empresaObjectId } = req.params;
        let { startDate, endDate } = req.query;

        //console.log(`🔹 Recebida solicitação para empresa: ${empresaObjectId}`);
        //console.log(`📅 Intervalo de datas recebido: ${startDate} → ${endDate}`);

        // Converter strings de data para objetos Date
        startDate = new Date(startDate);
        endDate = new Date(endDate);

        if (isNaN(startDate) || isNaN(endDate)) {
            return res.status(400).json({ message: "Datas inválidas fornecidas" });
        }

        // 🔹 Garantir que startDate comece no início do dia (00:00:00)
        startDate.setHours(0, 0, 0, 0);

        // 🔹 Garantir que endDate termine no final do dia (23:59:59)
        endDate.setHours(23, 59, 59, 999);

        //console.log(`📅 Intervalo ajustado: ${startDate.toISOString()} → ${endDate.toISOString()}`);

        // 1️⃣ Busca o ID sequencial da empresa pelo ObjectId
        const empresa = await Empresa.findById(empresaObjectId);
        if (!empresa) {
            //console.log(`❌ Empresa não encontrada para o ObjectId: ${empresaObjectId}`);
            return res.status(404).json({ message: "Empresa não encontrada" });
        }
        const id_empresa = empresa.id_empresa;
        //console.log(`✅ Empresa encontrada: ${empresa.nome}, ID sequencial: ${id_empresa}`);

        // 2️⃣ Busca os pedidos dentro do intervalo de datas
        const pedidosRecentes = await Pedidos.find({
            id_empresa: id_empresa,
            createdAt: { $gte: startDate, $lte: endDate }, // Agora sempre inclui o dia inteiro
            $or: [{ cancelado: { $exists: false } }, { cancelado: false }]
        });

        //console.log(`📦 Pedidos encontrados no intervalo: ${pedidosRecentes.length}`);

        // 3️⃣ Extrai os números de telefone únicos dos pedidos
        const telefonesClientes = [...new Set(pedidosRecentes.map(p => p.celular_cliente))];
        //console.log(`📞 Telefones únicos extraídos dos pedidos: ${telefonesClientes.length}`);

        // 4️⃣ Busca os clientes ativos que possuem esses telefones e pertencem à empresa
        const clientesAtivos = await Cliente.find({
            id_empresa: id_empresa,
            telefone: { $in: telefonesClientes }
        });

        //console.log(`👥 Clientes ativos encontrados: ${clientesAtivos.length}`);

        // 5️⃣ Retorna a lista de clientes ativos
        return res.json({ total: clientesAtivos.length, clientes: clientesAtivos });

    } catch (error) {
        console.error("❌ Erro ao buscar clientes ativos:", error);
        return res.status(500).json({ message: "Erro interno no servidor" });
    }
});




// Agendar uma tarefa para rodar todos os dias à meia-noite no horário de Brasília
/*cron.schedule('0 0 * * *', async () => {
    const horaBrasilia = momentTz.tz('America/Sao_Paulo');
    console.log('Hora atual em Brasília:', horaBrasilia.format());

    // Verificar se é meia-noite no horário de Brasília
    if (horaBrasilia.hour() === 0 && horaBrasilia.minute() === 0) {
        console.log('Iniciando verificação para reset automático de contadores de pedidos...');

        try {
            // Buscar todas as empresas com `dias_para_reset` maior que zero
            const empresas = await Empresa.find({ dias_para_reset: { $gt: 0 } });

            for (const empresa of empresas) {
                const { last_reset_date, dias_para_reset, id_empresa } = empresa;

                // Calcular a data limite sem hora para comparação
                const proximaDataDeReset = moment(last_reset_date).startOf('day').add(dias_para_reset, 'days');
                const dataAtual = moment().startOf('day');

                console.log(`Empresa ID: ${id_empresa}`);
                console.log(`Dias para reset configurado: ${dias_para_reset}`);
                console.log(`Última data de reset: ${moment(last_reset_date).format('YYYY-MM-DD')}`);
                console.log(`Próxima data de reset calculada: ${proximaDataDeReset.format('YYYY-MM-DD')}`);
                console.log(`Data atual: ${dataAtual.format('YYYY-MM-DD')}`);

                // Verifica se deve resetar ao comparar apenas as datas
                if (proximaDataDeReset.isSameOrBefore(dataAtual)) {
                    empresa.last_reset_date = new Date();
                    await empresa.save();
                    console.log(`Contador de pedidos da empresa ${id_empresa} foi resetado automaticamente.`);
                } else {
                    console.log(`A empresa ${id_empresa} não precisa de reset ainda.`);
                }
            }

            console.log('Verificação de reset automático concluída.');
        } catch (error) {
            console.error('Erro ao verificar reset automático de pedidos:', error);
        }
    } else {
        console.log('O cron não rodou no horário exato de meia-noite.');
    }
}, {
    scheduled: true,
    timezone: "America/Sao_Paulo" // Garantir que o cron rodará no fuso horário correto
});*/

app.post("/command-print/:id", checkToken, async (req, res) => {
    //const dadosPedido = { id: seqId, cliente: nome_cliente };
    const { dadosPedido } = req.body;
    // Dados de exemplo, substitua com os dados reais
    //Capturar Data Atual e formatar
    var data = moment().format();
    const dataFormatada = momentTz
        .tz(data, "America/Sao_Paulo")
        .format("DD/MM/YYYY HH:mm");
    const id_empresa = dadosPedido.id_empresa;
    const empresa = await Empresa.findOne(
        { id_empresa: id_empresa },
        "tipo_impressao name"
    );
    console.log("Empresa:", empresa);
    console.log("Dados Pedido:", dadosPedido);
    if (empresa) {
        const dadosComanda = {
            createdBy: dadosPedido.createdBy,
            tipoPedido: dadosPedido.tipoPedido,
            dataPedido: dataFormatada,
            nomeLoja: empresa.name,
            numeroPedido: dadosPedido.numeroPedido,
            itens: dadosPedido.itens,
            cliente: dadosPedido.cliente,
            entrega: dadosPedido.entrega,
            pagamento: dadosPedido.pagamento,
            observacoes: dadosPedido.observacoes,
            troco: dadosPedido.troco,
        };
        gerarPDF(dadosComanda)
            .then((caminhoArquivo) => {
                console.log("Comanda gerada com sucesso:", caminhoArquivo);
                // Aqui você pode enviar o arquivo para impressão ou fazer outras operações
                enviarParaImpressao(caminhoArquivo, empresa._id);
            })
            .catch((erro) => {
                console.error("Erro ao gerar a comanda:", erro);
            });
        return res.status(201).json({ msg: "Comanda gerada com sucesso!" });
    } else {
        res.status(404).json({ msg: "Empresa não encontrada" });
    }
});
// Cadastrar Categorias
app.post("/auth/registerCategoria", async (req, res) => {
    const {
        createdBy,
        id_empresa,
        title,
        disponibilidade,
        dia_horario_disponibilidade,
        only_pdv,
        only_qrcode,
        only_delivery_take_local,
        modelo,
    } = req.body;

    // validacoes
    if (!createdBy) {
        return res.status(422).json({ msg: "O ID de criação é obrigatório!" });
    }

    if (!title) {
        return res.status(422).json({ msg: "O Título é obrigatório!" });
    }

    if (!disponibilidade) {
        return res.status(422).json({ msg: "Disponibilidade é obrigatório!" });
    }

    if (
        disponibilidade == "especificos" &&
        dia_horario_disponibilidade.length < 1
    ) {
        return res
            .status(422)
            .json({ msg: "É obrigatório informar pelo menos 1 dia!" });
    }

    // Verificar qual a categoria com maior valor de order, para gravar a nova com order +1
    let orderToSave = 1;
    Categorias.findOne({}, "order", { sort: { order: -1 } })
        .then((categoria) => {
            if (!categoria) {
                orderToSave = 1;
                console.log("Nenhuma categoria encontrada");
            } else {
                orderToSave = categoria.order + 1;
                console.log("Categoria com maior order:", categoria.order);
            }
        })
        .catch((err) => {
            console.error("Erro ao buscar categoria:", err);
        });

    // Verificar SeqID e atribuir ao id_categoria
    let seqId = await Counter.findOneAndUpdate(
        { id: `empresa:${id_empresa};id_categoria` },
        { $inc: { seq: 1 } },
        { new: true }
    );

    if (!seqId) {
        const newVal = new Counter({
            id: `empresa:${id_empresa};id_categoria`,
            seq: 1,
        });
        await newVal.save();
        seqId = { seq: 1 };
    }

    // Encontrar empresa e atribuir empresaObjId a Categoria
    const empresa = await Empresa.findOne({ id_empresa });
    if (empresa) {
        // Criar Categoria
        const categoria = new Categorias({
            empresaObjId: empresa._id,
            id_categoria: seqId.seq,
            createdBy,
            id_empresa,
            title,
            order: orderToSave,
            disponibilidade,
            dia_horario_disponibilidade,
            only_pdv,
            only_qrcode,
            only_delivery_take_local,
            modelo,
            inativo: false,
            createdAt: moment().format(),
        });

        try {
            await categoria.save();
            return res
                .status(201)
                .json({ msg: "Categoria cadastrada com sucesso!", categoria });
        } catch (error) {
            console.log(error);

            res.status(500).json({
                msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
            });
        }
    } else {
        res.status(404).json({ msg: "Empresa não encontrada" });
    }
});

// Cadastrar Itens
app.post("/auth/registerItem", async (req, res) => {
    const {
        createdBy,
        id_empresa,
        category_item_id,
        category_item_title,
        title,
        description,
        out,
        image,
        price,
        disponibilidade,
        type,
    } = req.body;

    // validacoes
    if (!createdBy) {
        return res.status(422).json({ msg: "O ID de criação é obrigatório!" });
    }

    if (!title) {
        return res.status(422).json({ msg: "O Título é obrigatório!" });
    }

    if (!category_item_id) {
        return res.status(422).json({ msg: "É obrigatório informar a categoria!" });
    }

    // Verificar qual o item com maior valor de order, para gravar a nova com order +1
    const itemExiste = await Itens.findOne();
    let orderToSave = 1;
    let lastOrderOfCategory = 0;

    //gravando a imagem no bucket do google cloud
    var imageUrl;
    if (image.length > 0) {
        const imageBase64 = image[0].data_url;
        // gerar um id randomico para a imagem
        const imageId =
            Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);
        imageUrl = await uploadImage(imageBase64, "img_" + imageId + ".png");
    } else {
        imageUrl = null;
    }

    if (itemExiste) {
        Itens.findOne(
            { category_item_id: category_item_id, id_empresa: id_empresa }, // Adicione essa linha com o ID da categoria do item que está sendo cadastrado
            "order",
            { sort: { order: -1 } }
        )
            .then((item) => {
                if (!item) {
                    orderToSave = 1;
                    //console.log('Nenhum item encontrado');
                } else {
                    lastOrderOfCategory = item.order; // Armazene o último valor de order da categoria
                    orderToSave = lastOrderOfCategory + 1;
                    //console.log('Item com maior order:', item.order);
                }
            })
            .catch((err) => {
                console.error("Erro ao buscar item:", err);
            });
    }

    // Verificar SeqID e atribuir ao id_item
    let seqId = await Counter.findOneAndUpdate(
        { id: `empresa:${id_empresa};id_item` },
        { $inc: { seq: 1 } },
        { new: true }
    );

    if (!seqId) {
        const newVal = new Counter({ id: `empresa:${id_empresa};id_item`, seq: 1 });
        await newVal.save();
        seqId = { seq: 1 };
    }
    //console.log("orderToSave",orderToSave);
    // Encontrar empresa e atribuir empresaObjId ao Item
    const empresa = await Empresa.findOne({ id_empresa });
    if (empresa) {
        console.log(
            "TESTE>",
            empresa._id,
            seqId.seq,
            createdBy,
            id_empresa,
            category_item_id,
            category_item_title,
            title,
            orderToSave,
            description,
            out,
            imageUrl,
            price,
            disponibilidade
        );
        // Criar Item
        const item = new Itens({
            empresaObjId: empresa._id,
            id_item: seqId.seq,
            createdBy,
            id_empresa,
            category_item_id,
            category_item_title,
            title,
            order: orderToSave,
            description,
            out,
            image: imageUrl ? imageUrl : "",
            price,
            has_adicional: false,
            disponibilidade,
            type,
            inativo: false,
            createdAt: moment().format(),
        });

        try {
            await item.save();
            return res.status(201).json({ msg: "Item cadastrado com sucesso!" });
        } catch (error) {
            //console.log(error);

            res.status(500).json({
                msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
            });
        }
    } else {
        res.status(404).json({ msg: "Empresa não encontrada" });
    }
});

// Cadastrar Grupo de Adicionais
app.post("/auth/registerGrupoAdicionais", async (req, res) => {
    const {
        createdBy,
        id_empresa,
        title,
        min,
        max,
        mandatory,
        out,
        calcular_maior_valor,
        calcular_media,
        precificacao,
        type,
    } = req.body;

    // validacoes
    if (!createdBy) {
        return res.status(422).json({ msg: "O ID de criação é obrigatório!" });
    }

    if (!title) {
        return res.status(422).json({ msg: "O Título é obrigatório!" });
    }

    // Verificar SeqID e atribuir ao id_grupo_adicional
    let seqId = await Counter.findOneAndUpdate(
        { id: `empresa:${id_empresa};id_grupo_adicional` },
        { $inc: { seq: 1 } },
        { new: true }
    );

    if (!seqId) {
        const newVal = new Counter({
            id: `empresa:${id_empresa};id_grupo_adicional`,
            seq: 1,
        });
        await newVal.save();
        seqId = { seq: 1 };
    }

    // Encontrar empresa e atribuir empresaObjId ao adicional
    const empresa = await Empresa.findOne({ id_empresa });
    if (empresa) {
        // Criar adicional
        const adicional = new Adicionais({
            empresaObjId: empresa._id,
            id_grupo_adicional: seqId.seq,
            createdBy,
            id_empresa,

            title,
            min,
            max,
            mandatory,
            out,
            calcular_maior_valor,
            calcular_media,
            precificacao,
            type,

            createdAt: moment().format(),
        });

        try {
            await adicional.save();
            return res
                .status(201)
                .json({ adicional, msg: "Adicional cadastrado com sucesso!" });
        } catch (error) {
            console.log(error);

            res.status(500).json({
                msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
            });
        }
    } else {
        res.status(404).json({ msg: "Empresa não encontrada" });
    }
});

// Cadastrar Adicionais
app.post("/auth/registerAdicionais/:id", checkToken, async (req, res) => {
    const { createdBy, id_grupo, id_empresa, title, price, out, image } =
        req.body;

    // validacoes
    if (!createdBy) {
        return res.status(422).json({ msg: "O ID de criação é obrigatório!" });
    }

    if (!title) {
        return res.status(422).json({ msg: "O Título é obrigatório!" });
    }

    if (price === null || price === undefined) {
        return res.status(422).json({ msg: "O Preço é obrigatório!" });
    }

    // Verificar SeqID e atribuir ao id_adicional
    let seqId = await Counter.findOneAndUpdate(
        { id: `empresa:${id_empresa};id_adicional` },
        { $inc: { seq: 1 } },
        { new: true }
    );

    if (!seqId) {
        const newVal = new Counter({
            id: `empresa:${id_empresa};id_adicional`,
            seq: 1,
        });
        await newVal.save();
        seqId = { seq: 1 };
    }

    //gravando a imagem no bucket do google cloud
    var imageUrl;
    if (image.length > 0) {
        const imageBase64 = image[0].data_url;
        // gerar um id randomico para a imagem
        const imageId =
            Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);
        imageUrl = await uploadImage(imageBase64, "img_" + imageId + ".png");
    } else {
        imageUrl = null;
    }

    //Capturar Data Atual e formatar
    var data = moment().format();
    //console.log(data);

    try {
        // Cadastrar o adicional
        const createdAdicional = await Adicionais.findOneAndUpdate(
            { _id: id_grupo, id_empresa: id_empresa },
            {
                $push: {
                    adicionais: {
                        id_adicional: seqId.seq,
                        title,
                        price,
                        out,
                        image: imageUrl ? imageUrl : "",
                        createdAt: data,
                    },
                },
                $currentDate: {
                    updatedAt: true,
                },
            },
            { new: true }
        );

        if (!createdAdicional) {
            return res
                .status(404)
                .json({ msg: "Grupo de Adicional não encontrado." });
        }

        return res
            .status(201)
            .json({ msg: "Adicional cadastrado com sucesso!", createdAdicional });
    } catch (error) {
        console.log(error);

        res.status(500).json({
            msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
        });
    }
});

// Atualizar Adicionais
app.post("/auth/updateAdicional/:id", checkToken, async (req, res) => {
    const { _id, id_grupo, title, price, out, image, out_salao, id_adicional, price_salao } =
        req.body;
    /*console.log("O que chegou:", {
        _id,
        id_grupo,
        title,
        price,
        out,
        image,
        out_salao,
        id_adicional,
        price_salao
    });*/
    // Validações
    if (!_id) {
        return res.status(422).json({ msg: "O ID do adicional é obrigatório!" });
    }

    if (!id_grupo) {
        return res.status(422).json({ msg: "O ID do grupo é obrigatório!" });
    }

    if (!title) {
        return res.status(422).json({ msg: "O Título é obrigatório!" });
    }

    if (price === null || price === undefined) {
        return res.status(422).json({ msg: "O Preço é obrigatório!" });
    }

    // Capturar Data Atual e formatar
    var data = moment().format();

    try {
        // Encontrar o grupo de adicionais
        const grupoAdicional = await Adicionais.findOne({ _id: id_grupo });

        if (!grupoAdicional) {
            return res
                .status(404)
                .json({ msg: "Grupo de adicionais não encontrado." });
        }

        // Encontrar o adicional específico dentro do grupo
        const adicionalIndex = grupoAdicional.adicionais.findIndex(
            (ad) => ad._id.toString() === _id
        );

        if (adicionalIndex === -1) {
            return res.status(404).json({ msg: "Adicional não encontrado." });
        }

        const existingAdicional = grupoAdicional.adicionais[adicionalIndex];
        const existingImageUrl = existingAdicional.image;

        // Verificar se a imagem foi alterada
        let imageUrl = existingImageUrl;

        if (image === "") {
            // Excluir a imagem antiga se o campo de imagem estiver vazio
            if (existingImageUrl) {
                const fileName = existingImageUrl.split("/").pop();
                await bucket.file(fileName).delete();
                console.log(`Deleted image: ${fileName}`);
                imageUrl = ""; // Definir URL da imagem como vazia
            }
        } else if (image && image.startsWith("data:image")) {
            // Excluir a imagem antiga, se existir
            if (existingImageUrl) {
                const fileName = existingImageUrl.split("/").pop();
                await bucket.file(fileName).delete();
                console.log(`Deleted image: ${fileName}`);
            }

            // Gravando a nova imagem no bucket do Google Cloud
            const imageBase64 = image;
            const imageId =
                Math.random().toString(36).substring(2, 15) +
                Math.random().toString(36).substring(2, 15);
            imageUrl = await uploadImage(imageBase64, "img_" + imageId + ".png");
        }

        // Atualizar o adicional filho
        grupoAdicional.adicionais[adicionalIndex].title = title;
        grupoAdicional.adicionais[adicionalIndex].price =
            price !== undefined
                ? price
                : grupoAdicional.adicionais[adicionalIndex].price;
        grupoAdicional.adicionais[adicionalIndex].price_salao =
            price_salao !== undefined
                ? price_salao
                : grupoAdicional.adicionais[adicionalIndex].price_salao;
        grupoAdicional.adicionais[adicionalIndex].out =
            out !== undefined ? out : grupoAdicional.adicionais[adicionalIndex].out;
        grupoAdicional.adicionais[adicionalIndex].out_salao =
            out_salao !== undefined
                ? out_salao
                : grupoAdicional.adicionais[adicionalIndex].out_salao;
        grupoAdicional.adicionais[adicionalIndex].image =
            imageUrl !== undefined
                ? imageUrl
                : grupoAdicional.adicionais[adicionalIndex].image;
        grupoAdicional.adicionais[adicionalIndex].updatedAt = data;

        await grupoAdicional.save();

        return res.status(200).json({ msg: "Adicional atualizado com sucesso!" });
    } catch (error) {
        console.log(error);
        res.status(500).json({
            msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
        });
    }
});

// Deletar Subadicional
app.post("/auth/deleteSubadicional/:id", checkToken, async (req, res) => {
    const { id_subadicional, id_grupo } = req.body;

    // Validações
    if (!id_subadicional) {
        return res.status(422).json({ msg: "O ID do subadicional é obrigatório!" });
    }

    if (!id_grupo) {
        return res.status(422).json({ msg: "O ID do grupo é obrigatório!" });
    }

    try {
        // Encontrar o grupo de adicionais
        const grupoAdicional = await Adicionais.findOne({ _id: id_grupo });

        if (!grupoAdicional) {
            return res
                .status(404)
                .json({ msg: "Grupo de adicionais não encontrado." });
        }

        // Encontrar o subadicional específico dentro do grupo
        const subadicionalIndex = grupoAdicional.adicionais.findIndex(
            (ad) => ad._id.toString() === id_subadicional
        );

        if (subadicionalIndex === -1) {
            return res.status(404).json({ msg: "Subadicional não encontrado." });
        }

        const subadicional = grupoAdicional.adicionais[subadicionalIndex];
        const imageUrl = subadicional.image;

        // Excluir a imagem associada, se existir
        if (imageUrl) {
            const fileName = imageUrl.split("/").pop();
            await bucket.file(fileName).delete();
            console.log(`Deleted image: ${fileName}`);
        }

        // Remover o subadicional do grupo de adicionais
        grupoAdicional.adicionais.splice(subadicionalIndex, 1);

        await grupoAdicional.save();

        return res
            .status(200)
            .json({ msg: "Subadicional deletado com sucesso!", grupoAdicional });
    } catch (error) {
        console.log(error);
        res.status(500).json({
            msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
        });
    }
});

// Cadastrar e abrir Caixa
app.post("/registerCaixaAndOpen", async (req, res) => {
    const { createdBy, id_empresa, saldo_inicial } = req.body;
    const status_caixa = true;
    // validacoes
    if (!createdBy) {
        return res.status(422).json({ msg: "O ID de criação é obrigatório!" });
    }

    if (!saldo_inicial) {
        return res.status(422).json({ msg: "O Valor Inicial é obrigatório!" });
    }

    if (saldo_inicial < 0) {
        return res
            .status(422)
            .json({ msg: "O Valor Inicial não pode ser negativo!" });
    }

    // Verificar SeqID e atribuir ao id_caixa
    let seqId = await Counter.findOneAndUpdate(
        { id: `empresa:${id_empresa};id_caixa` },
        { $inc: { seq: 1 } },
        { new: true }
    );

    if (!seqId) {
        const newVal = new Counter({
            id: `empresa:${id_empresa};id_caixa`,
            seq: 1,
        });
        await newVal.save();
        seqId = { seq: 1 };
    }

    // Encontrar empresa e atribuir empresaObjId ao caixa
    const empresa = await Empresa.findOne({ id_empresa: id_empresa });
    console.log("QUAL ID_EMPRESA ESTA PASSANDO>", id_empresa);
    console.log("OQ TEM AQUI???", empresa);
    if (empresa && empresa.caixa_aberto === false) {
        // Criar caixa
        const caixa = new Caixa({
            empresaObjId: empresa._id,
            id_caixa: seqId.seq,
            createdBy,
            id_empresa,
            saldo_inicial,
            data_abertura: moment().format(),
            status_caixa,
            lancamentos_caixa: [
                {
                    descricao: "Saldo Inicial",
                    tipo_lancamento: "Dinheiro",
                    valor: saldo_inicial,
                    createdAt: moment().format(),
                },
            ],
            createdAt: moment().format(),
        });

        try {
            const atualiza_status_caixa_empresa = await Empresa.findOneAndUpdate(
                { id_empresa: id_empresa },
                {
                    caixa_aberto: true,
                    caixa_aberto_id: caixa._id,
                },
                { new: true }
            );
            console.log("OQ TEM AQUI:", atualiza_status_caixa_empresa);

            if (!atualiza_status_caixa_empresa) {
                return res.status(404).json({ msg: "Empresa não encontrada." });
            }

            await caixa.save();
            return res.status(201).json({ msg: "Caixa aberto com sucesso!" });
        } catch (error) {
            console.log(error);

            res.status(500).json({
                msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
            });
        }
    }
    if (empresa && empresa.caixa_aberto === true) {
        return res.status(422).json({ msg: "Já existe um caixa aberto!" });
    } else {
        return res.status(404).json({ msg: "Empresa não encontrada" });
    }
});

// Fechar o Caixa
app.post("/closeCaixa", async (req, res) => {
    const {
        id_empresa,
        objIdCaixa,
        closedBy,
        saldo_final,
        valor_informado_dinheiro,
        valor_informado_cartao,
        valor_informado_pix,
    } = req.body;

    // Validações
    if (!id_empresa) {
        return res.status(422).json({ msg: "O ID da empresa é obrigatório!" });
    }

    if (!objIdCaixa) {
        return res.status(422).json({ msg: "O ID do caixa é obrigatório!" });
    }

    if (!closedBy) {
        return res.status(422).json({ msg: "O ID de fechamento é obrigatório!" });
    }

    if (saldo_final < 0) {
        return res
            .status(422)
            .json({ msg: "O Saldo Final não pode ser negativo!" });
    }

    try {
        // Verificar se o caixa existe e se closedBy é igual a createdBy
        const caixa = await Caixa.findOne({
            _id: objIdCaixa,
            id_empresa: id_empresa,
        });

        if (!caixa) {
            return res.status(404).json({ msg: "Caixa não encontrado." });
        }

        if (caixa.createdBy !== closedBy) {
            return res
                .status(403)
                .json({ msg: "Apenas o usuário que criou o caixa pode fechá-lo." });
        }

        if (!caixa.status_caixa) {
            return res.status(422).json({ msg: "O caixa já está fechado." });
        }

        // Atualizar o status do caixa para fechado
        const caixaAtualizado = await Caixa.findOneAndUpdate(
            { _id: objIdCaixa, id_empresa: id_empresa },
            {
                $set: {
                    status_caixa: false,
                    saldo_final: saldo_final,
                    valor_informado_dinheiro: valor_informado_dinheiro,
                    valor_informado_cartao: valor_informado_cartao,
                    valor_informado_pix: valor_informado_pix,
                    data_fechamento: moment().format(),
                },
            },
            { new: true }
        );

        if (!caixaAtualizado) {
            return res.status(404).json({ msg: "Caixa não encontrado." });
        }

        // Atualizar o status do caixa na empresa
        const empresa = await Empresa.findOneAndUpdate(
            { id_empresa: id_empresa },
            {
                $set: {
                    caixa_aberto: false,
                    caixa_aberto_id: null,
                    last_caixa_id: objIdCaixa,
                },
            },
            { new: true }
        );

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada." });
        }

        // Logs para depuração
        console.log(`Verificando reset para a empresa ${id_empresa}`);
        console.log(`Última data de reset: ${empresa.last_reset_date}`);
        console.log(`Dias para reset: ${empresa.dias_para_reset}`);

        // Lógica de reset para id_counter_pedido
        const lastResetDate = moment(empresa.last_reset_date); // Verifica ultima data do reset
        const dataAtual = moment(); // Data atual

        console.log(`Ultima data de reset: ${lastResetDate.format()}`);
        console.log(`Data atual: ${dataAtual.format()}`);

        // Verifica a diferença entre a data atual e a próxima data de reset em horas
        const diffHoras = dataAtual.diff(lastResetDate, "hours"); // Diferença em horas entre a data atual e a próxima data de reset (sem sinal negativo)

        console.log(
            `Diferença em horas entre a data de reset e a data atual: ${diffHoras} horas`
        );

        // Verifica se a diferença em horas é suficiente para o reset
        const horasParaReset = (empresa.dias_para_reset - 1) * 24; // Convertendo os dias para horas
        if (diffHoras >= horasParaReset) {
            empresa.last_reset_date = dataAtual; // Atualiza a data do último reset
            await empresa.save(); // Salva a atualização da empresa

            console.log(
                `Contador de pedidos da empresa ${id_empresa} foi resetado automaticamente.`
            );
        } else {
            console.log(`A empresa ${id_empresa} não precisa de reset ainda.`);
        }

        return res.status(200).json({ msg: "Caixa fechado com sucesso!" });
    } catch (error) {
        console.log(error);
        return res.status(500).json({
            msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
        });
    }
});

// Adicionar Lançamento ao Caixa Aberto
app.post("/addLancamentoCaixa", async (req, res) => {
    const {
        id_empresa,
        objIdCaixa,
        descricao,
        tipo_lancamento,
        valor,
        createdBy,
    } = req.body;

    // Validações
    if (!id_empresa) {
        return res.status(422).json({ msg: "O ID da empresa é obrigatório!" });
    }

    if (!objIdCaixa) {
        return res.status(422).json({ msg: "O ID do caixa é obrigatório!" });
    }

    if (!descricao) {
        return res.status(422).json({ msg: "A descrição é obrigatória!" });
    }

    if (!tipo_lancamento) {
        return res.status(422).json({ msg: "O tipo de lançamento é obrigatório!" });
    }

    if (valor == null) {
        return res
            .status(422)
            .json({ msg: "O valor do lançamento é obrigatório!" });
    }

    if (!createdBy) {
        return res
            .status(422)
            .json({ msg: "O ID do usuário que criou o lançamento é obrigatório!" });
    }

    try {
        // Verificar se o caixa existe e está aberto
        const caixa = await Caixa.findOne({
            _id: objIdCaixa,
            id_empresa: id_empresa,
            status_caixa: true,
        });

        if (!caixa) {
            return res
                .status(404)
                .json({ msg: "Caixa não encontrado ou já está fechado." });
        }

        // Adicionar o lançamento ao caixa
        caixa.lancamentos_caixa.push({
            descricao: descricao,
            tipo_lancamento: tipo_lancamento,
            valor: valor,
            createdAt: moment().format(),
            updatedAt: moment().format(),
        });

        await caixa.save();

        return res
            .status(200)
            .json({ msg: "Lançamento adicionado com sucesso!", caixa });
    } catch (error) {
        console.log(error);
        return res.status(500).json({
            msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
        });
    }
});

// Cadastrar Plano
app.post("/auth/registerPlanoAdmin", async (req, res) => {
    const {
        createdBy,
        nome,
        plan_identifier,
        order,
        plan_cycle,
        maxPayments,
        valor_plano
    } = req.body;

    // Validações
    if (
        createdBy === undefined || createdBy === null ||
        nome === undefined || nome === null ||
        plan_identifier === undefined || plan_identifier === null ||
        order === undefined || order === null ||
        plan_cycle === undefined || plan_cycle === null ||
        valor_plano === undefined || valor_plano === null // Permitir valor 0
    ) {
        return res.status(422).json({ msg: "Todos os campos são obrigatórios!" });
    }

    try {
        // Verificar SeqID e atribuir ao id_plano
        let seqId = await Counter.findOneAndUpdate(
            { id: "id_plano" },
            { $inc: { seq: 1 } },
            { new: true }
        );

        if (!seqId) {
            const newVal = new Counter({ id: "id_plano", seq: 1 });
            await newVal.save();
            seqId = { seq: 1 };
        }

        // Verificar se usuário de criação é Admin
        const userRole = await User.findById(createdBy, "role");
        if (userRole.role !== "Admin") {
            return res.status(422).json({ msg: "Usuário sem permissão" });
        }

        // Criar plano na Iugu e no banco de dados
        /*const planData = await createIuguPlan(
            title,
            plan_identifier,
            tempo_duracao,
            plan_cycle,
            valor_plano,
            max_cycle
        );*/
        //console.log("Plano criado:", planData);

        const plano = new Planos({
            id_plano: seqId.seq,
            name: nome,
            plan_identifier: plan_identifier,
            order,
            plan_cycle: plan_cycle,
            max_cycles: maxPayments,
            valor_plano,
            access_type: "full",
            inativo: false,
            bloqueado: false,
            createdAt: moment().format(),
        });

        await plano.save();

        return res.status(201).json({ msg: "Plano cadastrado com sucesso!" });
    } catch (error) {
        console.error("Erro ao criar o plano:", error);
        return res
            .status(500)
            .json({
                msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
            });
    }
});

app.post("/requestsToAsaas", async (req, res) => {
    const {
        createdBy,
        email,
        name,
        cpf_cnpj,
        zip_code,
        number,
        plan_identifier,
        payment_type,
        isPromotional
    } = req.body;

    // Validações
    if (
        !email ||
        !name ||
        !cpf_cnpj ||
        !zip_code ||
        !number ||
        !plan_identifier ||
        !payment_type
    ) {
        return res.status(422).json({ msg: "Todos os campos são obrigatórios!" });
    }

    try {
        // Verificar vinculo do usuário de criação
        const user = await User.findById(createdBy, "role vinculo_empresa");
        const userVinculoEmpresa = user.vinculo_empresa;

        // Buscar a empresa vinculada ao usuário
        const empresa = await Empresa.findOne(
            { cnpj: userVinculoEmpresa },
            "cnpj customer_asaas telefone celular"
        );

        const planoSelecionado = await Planos.findOne({ plan_identifier: plan_identifier });

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada!" });
        }

        let customerAsaas = empresa.customer_asaas;
        console.log("customerAsaas:", customerAsaas);

        if (!customerAsaas || Object.keys(customerAsaas).length === 0) {
            console.log("Criando novo cliente no Asaas");
            try {
                const newCustomer = await criarRegistroClienteAsaas(
                    name,
                    cpf_cnpj,
                    email,
                    empresa.telefone || empresa.celular,
                    number,
                    zip_code
                );

                if (!newCustomer || !newCustomer.id) {
                    throw new Error("ID do cliente não foi retornado pela API do Asaas.");
                }

                customerAsaas = newCustomer.id;
                empresa.customer_asaas = customerAsaas;
                await empresa.save();
            } catch (error) {
                console.error("Erro ao criar cliente Asaas:", error.message);
                return res.status(500).json({ msg: "Erro ao criar cliente!", error: error.message });
            }
        }

        if (customerAsaas && typeof customerAsaas === "string" && customerAsaas.trim() !== "") {
            let subscriptionIdAsaas = "";
            let invoiceId = "";
            try {
                console.log("Atualizando cliente no Asaas...");
                await updateCustomerAsaas(
                    customerAsaas,
                    name,
                    cpf_cnpj,
                    email,
                    empresa.telefone || empresa.celular,
                    number,
                    zip_code
                );

                const billingType = payment_type;
                const cycle = planoSelecionado.plan_cycle;
                const customerId = customerAsaas;
                const value = planoSelecionado.valor_plano;
                const today = new Date();
                const nextDueDate = today.toISOString().split("T")[0];
                const description = planoSelecionado.name;
                const externalReference = planoSelecionado.plan_identifier;
                const maxPayments = planoSelecionado.maxPayments || undefined;

                // Criando a assinatura no Asaas
                const subscriptionData = await createAsaasSubscription(
                    billingType,
                    cycle,
                    customerId,
                    value,
                    nextDueDate,
                    description,
                    externalReference,
                    maxPayments,
                    isPromotional
                );

                subscriptionIdAsaas = subscriptionData.id;

                // Buscar o invoiceUrl
                const responseLastInvoice = await getLastPendingInvoice(subscriptionIdAsaas);
                const invoiceUrl = responseLastInvoice.lastInvoice.invoiceUrl;
                invoiceId = responseLastInvoice.lastInvoice.id;

                // **Se foi um plano promocional, remover desconto após a criação**
                if (isPromotional && externalReference === "plano_starter") {
                    console.log("Removendo desconto após a criação...");
                    const updatedSubscriptionData = await removeDiscountFromSubscription(subscriptionIdAsaas);

                    // Atualizamos os dados no MongoDB com a versão sem desconto
                    subscriptionData.discount = updatedSubscriptionData.discount;
                }

                // Verificar se já existe uma assinatura para a empresa
                const existingAssinatura = await Assinatura.findOne({ empresa: empresa._id });

                if (existingAssinatura) {
                    await Assinatura.findOneAndUpdate(
                        { empresa: empresa._id },
                        { assinatura_obj: subscriptionData },
                        { new: true }
                    );

                    return res.status(201).json({
                        msg: "Assinatura criada com sucesso!",
                        invoiceUrl: invoiceUrl,
                    });
                } else {
                    const assinatura = new Assinatura({
                        empresa: empresa._id,
                        assinatura_obj: subscriptionData,
                    });

                    await assinatura.save();

                    return res.status(201).json({
                        msg: "Assinatura criada com sucesso!",
                        invoiceUrl: invoiceUrl,
                    });
                }
            } catch (error) {
                console.error("Erro ao criar a assinatura:", error);
                res.status(500).json({ msg: "Erro ao criar a assinatura." });
            }
        } else {
            return res.status(500).json({ msg: "Erro ao recuperar ou criar o cliente no Asaas!" });
        }
    } catch (error) {
        console.error("Erro no servidor:", error);
        return res.status(500).json({
            msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
        });
    }
});


app.post("/lastPendingInvoice", async (req, res) => {
    try {
        // Obtém o corpo da requisição
        const { subscriptionIdAsaas } = req.body;

        if (!subscriptionIdAsaas) {
            return res.status(400).json({ msg: "subscriptionIdAsaas é obrigatório." });
        }

        // Chama a função para obter a última fatura pendente
        const responseLastInvoice = await getLastPendingInvoice(subscriptionIdAsaas);

        if (responseLastInvoice && responseLastInvoice.lastInvoice) {
            return res.status(200).json({ lastInvoice: responseLastInvoice.lastInvoice });
        } else {
            return res.status(404).json({ msg: "Nenhuma fatura pendente encontrada." });
        }
    } catch (error) {
        console.error("Erro ao obter a última fatura pendente:", error);
        return res.status(500).json({ msg: "Erro interno ao processar a solicitação.", error: error.message });
    }
});

app.post("/allInvoices", async (req, res) => {
    try {
        const { subscriptionIdAsaas } = req.body;

        if (!subscriptionIdAsaas) {
            return res.status(400).json({ msg: "subscriptionIdAsaas é obrigatório." });
        }

        // Chama a função para obter todas as faturas
        const allInvoices = await getAllInvoices(subscriptionIdAsaas);

        return res.status(200).json({ invoices: allInvoices });
    } catch (error) {
        console.error("Erro ao obter todas as faturas:", error);
        return res.status(500).json({ msg: "Erro interno ao processar a solicitação.", error: error.message });
    }
});

// Criar Cliente
// Register Cliente From Cardapio
app.post("/register-cliente-cardapio", async (req, res) => {
    const { id_empresa, nome, telefone } = req.body;
    //validacoes
    if (!nome) {
        return res.status(422).json({ msg: "O Nome é obrigatório!" });
    }
    if (!telefone) {
        return res.status(422).json({ msg: "O Telefone é obrigatório!" });
    }
    // Encontrar empresa e atribuir vinculo_empresa pelo cnpj
    const empresaCnpj = await Empresa.findOne({ id_empresa }, "cnpj");

    // Verificar SeqID e atribuir ao id_plano
    let seqId = await Counter.findOneAndUpdate(
        { id: `empresa:${id_empresa};id_cliente` },
        { $inc: { seq: 1 } },
        { new: true }
    );
    if (!seqId) {
        const newVal = new Counter({
            id: `empresa:${id_empresa};id_cliente`,
            seq: 1,
        });
        await newVal.save();
        seqId = { seq: 1 };
    }

    // Impedir que o mesmo cliente se cadastre duas vezes verificando pelo telefone, caso exista retorna o primeiro
    const clienteExists = await Cliente.findOne(
        { id_empresa: id_empresa, telefone: telefone },
        "nome telefone endereco"
    );
    if (clienteExists) {
        //Fazer update do nome cliente antes de retornar
        Cliente.findOneAndUpdate(
            { id_empresa: id_empresa, telefone: telefone },
            { nome: nome },
            function (err, docs) {
                if (err) {
                    //console.log(err)
                    return res.status(422).json({ msg: "Ocorreu um erro no servidor!" });
                }
                if (docs) {
                    //console.log("Updated : ", docs);
                    const clienteResponseExist = {
                        nome: nome,
                        telefone: docs.telefone,
                        endereco: docs.endereco,
                    };
                    return res
                        .status(200)
                        .json({
                            msg: "Cliente já cadastrado!",
                            cliente: clienteResponseExist,
                        });
                } else {
                    console.log("Cliente não encontrado : ", docs);
                }
            }
        );
    } else {
        //Capturar Data Atual e formatar
        var data = moment().format();

        //Criar Cliente
        const cli = new Cliente({
            id_cliente: seqId.seq,
            id_empresa,
            createdBy: "cardapio",
            nome,
            telefone,
            cpf_cnpj: "",
            endereco: [],
            vinculo_empresa: empresaCnpj.cnpj,
            counter_qtd_pedido: 0,
            type: "cliente",
            createdAt: data,
            inativo: false,
        });

        try {
            await cli.save();
            /*const clienteResponse = {
                      nome: cli.nome,
                      telefone: cli.telefone,
                      endereco: cli.endereco
                  };*/
            return res
                .status(201)
                .json({ msg: "Cliente cadastrado com sucesso!", cliente: cli });
        } catch (error) {
            console.log(error);
            res.status(500).json({
                msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
            });
        }
    }
});

// Cadastrar Cliente
app.post("/auth/registerCliente", async (req, res) => {
    const {
        createdBy,
        documento,
        name,
        contato,
        razao,
        email,
        cep,
        estado,
        municipio,
        bairro,
        complemento,
        telefone,
        celular,
        vinculo_empresa,
        type,
    } = req.body;

    //validacoes
    if (!createdBy) {
        return res
            .status(422)
            .json({ msg: "O ID de vínculo de criação não foi passado!" });
    }

    //Checar se cliente existe
    let clienteDocumentoExists = null;
    if (documento) {
        clienteDocumentoExists = await Cliente.findOne({ documento: documento });
    }
    //const revEmailExists = await Revenda.findOne({ revenda_email: revenda_email})
    //const revUserEmailExists = await User.findOne({ email: revenda_email})

    if (clienteDocumentoExists) {
        return res.status(422).json({ msg: "Este CPF/CNPJ já está cadastrado!" });
    }

    //Verificar SeqID e atribuir ao id_cliente
    Counter.findOneAndUpdate(
        { id: "id_cliente", id_revenda: vinculo_empresa },
        { $inc: { seq: 1 } },
        { new: true },
        async (err, counterData) => {
            let seqId;
            if (err) {
                console.log(err);
            }
            if (counterData == null) {
                const newVal = new Counter({
                    id: "id_cliente",
                    seq: 1,
                    id_revenda: vinculo_empresa,
                });
                newVal.save();
                seqId = 1;
            } else {
                seqId = counterData.seq;
            }

            //Capturar Data Atual e formatar
            var data = moment().format();
            console.log(data);
            //Criar revenda
            const cli = new Cliente({
                id_cliente: seqId,
                createdBy,
                documento,
                name,
                contato,
                razao,
                email,
                cep,
                estado,
                municipio,
                bairro,
                complemento,
                telefone,
                celular,
                vinculo_empresa,
                type,
                createdAt: data,
                inativo: false,
            });

            try {
                await cli.save();

                return res
                    .status(201)
                    .json({ msg: "Cadastro do cliente realizado com sucesso!" });
            } catch (error) {
                console.log(error);

                res.status(500).json({
                    msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                });
            }
        }
    );
});

// Criar Vendedor

// Cadastrar Vendedor
app.post("/auth/registerVendedor", async (req, res) => {
    const {
        createdBy,
        documento,
        name,
        cep,
        estado,
        municipio,
        bairro,
        complemento,
        telefone,
        celular,
        vinculo_empresa,
        type,
    } = req.body;

    //validacoes
    if (!createdBy) {
        return res
            .status(422)
            .json({ msg: "O ID de vínculo de criação não foi passado!" });
    }

    //Checar se cliente existe
    let vendedorDocExists = null;
    if (documento) {
        vendedorDocExists = await Vendedor.findOne({ documento: documento });
    }

    //const revEmailExists = await Revenda.findOne({ revenda_email: revenda_email})
    //const revUserEmailExists = await User.findOne({ email: revenda_email})

    if (vendedorDocExists) {
        return res
            .status(422)
            .json({ msg: "Este CPF ou CNPJ já está cadastrado!" });
    }

    //Verificar SeqID e atribuir ao id_cliente
    Counter.findOneAndUpdate(
        { id: "id_vendedor", id_revenda: vinculo_empresa },
        { $inc: { seq: 1 } },
        { new: true },
        async (err, counterData) => {
            let seqId;
            if (err) {
                console.log(err);
            }
            if (counterData == null) {
                const newVal = new Counter({
                    id: "id_vendedor",
                    seq: 1,
                    id_revenda: vinculo_empresa,
                });
                newVal.save();
                seqId = 1;
            } else {
                seqId = counterData.seq;
            }

            //Capturar Data Atual e formatar
            var data = moment().format();
            console.log(data);
            //Criar revenda
            const vend = new Vendedor({
                id_vendedor: seqId,
                createdBy,
                documento,
                name,
                cep,
                estado,
                municipio,
                bairro,
                complemento,
                telefone,
                celular,
                vinculo_empresa,
                type,
                createdAt: data,
                inativo: false,
            });

            try {
                await vend.save();

                return res
                    .status(201)
                    .json({ msg: "Cadastro do vendedor realizado com sucesso!" });
            } catch (error) {
                console.log(error);

                res.status(500).json({
                    msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                });
            }
        }
    );
});

// Criar Orçamento

// Cadastrar orcamento
app.post("/auth/registerOrcamento", async (req, res) => {
    const {
        createdBy,
        data_emissao,
        codigo_cliente,
        nome_cliente,
        codigo_vendedor,
        nome_vendedor,
        total_orc,
        status_orc,
        id_grupo,
        vinculo_empresa,
        items,
    } = req.body;

    //validacoes
    if (!createdBy) {
        return res
            .status(422)
            .json({ msg: "O ID de vínculo de criação não foi passado!" });
    }

    if (items.length === 0) {
        return res
            .status(422)
            .json({ msg: "É obrigatório o cadastro do item no orçamento!" });
    }
    // //Checar se cliente existe
    // let vendedorDocExists = null;
    // if(documento){
    //     vendedorDocExists = await Vendedor.findOne({ documento: documento})
    // }

    //const revEmailExists = await Revenda.findOne({ revenda_email: revenda_email})
    //const revUserEmailExists = await User.findOne({ email: revenda_email})

    // if(vendedorDocExists){
    //     return res.status(422).json({msg: 'Este CPF ou CNPJ já está cadastrado!'})
    // }

    //Verificar SeqID e atribuir ao id_cliente
    Counter.findOneAndUpdate(
        { id: "id_orcamento", id_revenda: vinculo_empresa },
        { $inc: { seq: 1 } },
        { new: true },
        async (err, counterData) => {
            let seqId;
            if (err) {
                console.log(err);
            }
            if (counterData == null) {
                const newVal = new Counter({
                    id: "id_orcamento",
                    seq: 1,
                    id_revenda: vinculo_empresa,
                });
                newVal.save();
                seqId = 1;
            } else {
                seqId = counterData.seq;
            }

            //Capturar Data Atual e formatar
            var data = moment().format();
            console.log(data);
            //Criar Orcamento
            const orc = new Orcamento({
                id_orcamento: seqId,
                createdBy,
                data_emissao,
                codigo_cliente,
                nome_cliente,
                codigo_vendedor,
                nome_vendedor,
                total_orc,
                status_orc,
                id_grupo,
                vinculo_empresa,
                items,
                createdAt: data,
                inativo: false,
            });

            try {
                await orc.save();

                return res
                    .status(201)
                    .json({ msg: "Cadastro do Orçamento realizado com sucesso!" });
            } catch (error) {
                console.log(error);

                res.status(500).json({
                    msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                });
            }
        }
    );
});

// List Users
app.get("/list-users/:id", checkToken, async (req, res) => {
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userRole = await User.findById(id, "role");
        const role = userRole.role;

        //listar usuários
        if (role === "Admin") {
            const user = await User.find(
                { inativo: false, createdBy: id },
                "-password"
            ).populate("userEmpresa");
            return res.status(200).json({ user });
        }

        if (role === "empresa") {
            const user = await User.find(
                { inativo: false, role: "revenda", createdBy: id },
                "-password"
            ).populate("userEmpresa");
            console.log(user.userEmpresa);
            return res.status(200).json({ user });
        }

        if (!role || role === "revenda") {
            return res.status(401).json({ msg: "Sem permissão!", role });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// List Entregadores
app.get("/list-entregadores/:id_empresa", async (req, res) => {
    const id_empresa = req.params.id_empresa;

    if (id_empresa.match(/^[0-9a-fA-F]{24}$/)) {
        //listar entregadores
        const entregadores = await Entregadores.find({
            inativo: false,
            id_empresa: id_empresa,
        });
        return res.status(200).json({ entregadores });
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// List Caixas
app.get("/list-caixas/:id", async (req, res) => {
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const page = parseInt(req.query.page) || 1; // Página atual, default é 1
        const pageSize = parseInt(req.query.pageSize) || 10; // Tamanho da página, default é 10
        const skip = (page - 1) * pageSize; // Calcular quantos documentos pular
        const startDate = req.query.startDate
            ? new Date(req.query.startDate)
            : null;
        const endDate = req.query.endDate ? new Date(req.query.endDate) : null;

        // Construir o filtro de data
        let dateFilter = {};
        if (startDate && endDate) {
            dateFilter = { $gte: startDate, $lte: endDate };
        } else if (startDate) {
            dateFilter = { $gte: startDate };
        } else if (endDate) {
            dateFilter = { $lte: endDate };
        }

        try {
            const caixas = await Caixa.find({
                empresaObjId: id,
                status_caixa: false,
                ...(Object.keys(dateFilter).length > 0 && {
                    data_abertura: dateFilter,
                }),
            })
                .sort({ data_abertura: -1 }) // Ordenar por data_abertura em ordem decrescente (-1 para decrescente, 1 para crescente)
                .skip(skip)
                .limit(pageSize);

            const totalCaixas = await Caixa.countDocuments({
                empresaObjId: id,
                status_caixa: false,
                ...(Object.keys(dateFilter).length > 0 && {
                    data_abertura: dateFilter,
                }),
            }); // Contar o total de documentos
            const totalPages = Math.ceil(totalCaixas / pageSize);

            return res.status(200).json({
                caixas,
                totalCaixas,
                totalPages,
                currentPage: page,
            });
        } catch (error) {
            return res.status(500).json({ msg: "Erro ao listar caixas", error });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Obter Role do User
app.get("/user-role/:id", checkToken, async (req, res) => {
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userRole = await User.findById(id, "role");
        const role = userRole.role;

        if (role) {
            return res.status(200).json({ role });
        } else {
            return res.status(401).json({ msg: "Erro ao obter permissão!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Lista Planos Cadastrados na Iugu
app.get("/list-plans/:id", checkToken, async (req, res) => {
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        try {
            const planos = await getAsaasPlans();
            res.status(200).json({ planos });
        } catch (error) {
            res.status(500).json({ msg: "Erro ao obter a lista de planos" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Lista Planos Cadastrados na Iugu
app.get("/list-fil-plans/:id", checkToken, async (req, res) => {
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        try {
            const planos = await getFilteredIuguPlans();
            res.status(200).json({ planos });
        } catch (error) {
            res.status(500).json({ msg: "Erro ao obter a lista de planos" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Deleta Plano do banco de dados
app.post("/delete-plan-asaas/:id", checkToken, async (req, res) => {
    const id = req.params.id;
    const { planId } = req.body;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userRole = await User.findById(id, "role");
        const role = userRole.role;

        if (role === "Admin") {
            try {
                const result = await deleteAsaasPlan(planId);
                return res.status(200).json({ result });
            } catch (error) {
                console.error("Erro ao deletar o plano:", error);
                return res
                    .status(500)
                    .json({
                        msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
                    });
            }
        }

        if (!role || role != "Admin") {
            return res.status(401).json({ msg: "Sem permissão!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Empresa pelo Id Sq
app.post("/get-empresa/:id", checkToken, async (req, res) => {
    const { id_empresa } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            const empresa = await Empresa.findOne({ id_empresa: id_empresa });
            //const empresaRev = await Revenda.findOne({"id_empresa":id_empresa})
            if (empresa) {
                return res.status(200).json({ empresa });
            } else {
                return res.status(404).json({ msg: "Empresa não encontrada!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});


app.get("/get-empresas-admin/:userID", async (req, res) => {
    try {
        const { userID } = req.params;
        let { page = 1, limit = 10 } = req.query;

        if (userID !== ADMIN_ID) {
            return res.status(403).json({ message: "Acesso negado" });
        }

        page = parseInt(page);
        limit = parseInt(limit);

        // Buscar todas as empresas sem paginação inicial
        const empresas = await Empresa.find().lean();

        // Mapeia as empresas para adicionar os dados de assinatura, faturas e status da instância
        const empresasCompletas = await Promise.all(empresas.map(async (empresa) => {
            const assinatura = await Assinatura.findOne({ empresa: empresa._id }).lean();

            let faturas = [];
            if (assinatura && assinatura.assinatura_obj?.id) {
                try {
                    const response = await getAllInvoices(assinatura.assinatura_obj.id);
                    faturas = response || []; // Garante que seja sempre um array
                } catch (error) {
                    console.error(`Erro ao buscar faturas do Asaas para a empresa ${empresa._id}:`, error);
                }
            }

            let instanceStatus = null;
            if (empresa.whatsapp?.endpoint && empresa.whatsapp?.token) {
                try {
                    const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token);
                    const statusResponse = await whatsappInstance.getInstanceStatus();
                    instanceStatus = statusResponse?.data || null;
                } catch (error) {
                    console.error(`Erro ao obter status da instância WhatsApp para a empresa ${empresa._id}:`, error);
                }
            }

            return {
                ...empresa,
                assinatura,
                faturas,
                instanceStatus, // Mesmo que seja `null`, agora mantém todas as empresas
            };
        }));

        // Aplicar paginação após adicionar os dados adicionais
        const total = empresasCompletas.length;
        const empresasPaginadas = empresasCompletas.slice((page - 1) * limit, page * limit);

        res.json({ empresas: empresasPaginadas, page, limit, total });
    } catch (error) {
        console.error("Erro ao buscar empresas", error);
        res.status(500).json({ message: "Erro interno do servidor" });
    }
});


app.delete("/deleteInstance-admin/:userID", async (req, res) => {
    try {
        const { userID } = req.params;
        const { id } = req.body; // ID da empresa (não da instância)

        if (userID !== ADMIN_ID) {
            return res.status(403).json({ msg: "Acesso negado" });
        }

        if (!id) {
            return res.status(400).json({ msg: "ID da empresa é obrigatório" });
        }

        console.log(`🔍 Tentando deletar instância para empresa ID: ${id}`);

        // Buscar empresa pelo _id (não pelo whatsapp.id)
        const empresa = await Empresa.findOne({ _id: id }).select("name whatsapp");

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada" });
        }

        console.log(`📌 Empresa encontrada: ${empresa.name}`);

        // Verificar se tem configuração de WhatsApp
        if (!empresa.whatsapp) {
            console.log(`⚠️ Empresa ${empresa.name} não possui configuração WhatsApp`);
            return res.status(200).json({ msg: "Empresa não possui instância de WhatsApp configurada" });
        }

        // **🔧 TRATAMENTO PARA CONFIGURAÇÕES ANTIGAS**
        const whatsappConfig = empresa.whatsapp;
        
        // Verificar se é configuração antiga (endpoint não é evo.funil.vip)
        const isOldConfig = !whatsappConfig.endpoint || !whatsappConfig.endpoint.includes('evo.funil.vip');
        
        if (isOldConfig) {
            console.log(`🧹 Detectada configuração antiga para ${empresa.name}. Removendo apenas do banco...`);
            
            // Para configurações antigas, apenas remover do banco (não tentar chamar API)
            await Empresa.updateOne({ _id: id }, { $unset: { whatsapp: "" } });
            
            return res.status(201).json({ 
                msg: `Configuração antiga removida com sucesso para ${empresa.name}. A empresa pode criar uma nova instância.`,
                wasOldConfig: true
            });
        }

        // **🔧 CONFIGURAÇÃO NOVA - TENTAR DELETAR NA API**
        try {
            // Usar o nome da instância se disponível, senão usar o ID como fallback
            const instanceIdentifier = whatsappConfig.name || whatsappConfig.id;
            
            if (!instanceIdentifier) {
                console.log(`⚠️ Nenhum identificador de instância encontrado para ${empresa.name}`);
                // Mesmo sem identificador, remover do banco
                await Empresa.updateOne({ _id: id }, { $unset: { whatsapp: "" } });
                return res.status(201).json({ 
                    msg: `Configuração removida do banco para ${empresa.name} (sem identificador de instância)` 
                });
            }

            console.log(`🎯 Tentando deletar instância: ${instanceIdentifier} na Evolution API`);

            // Tentar deletar na Evolution API
            await whatsappManager.deleteInstance(instanceIdentifier);
            console.log(`✅ Instância ${instanceIdentifier} deletada com sucesso na Evolution API`);

        } catch (apiError) {
            console.error(`❌ Erro ao deletar instância na Evolution API:`, apiError.message);
            
            // Mesmo com erro na API, remover do banco
            console.log(`🧹 Removendo configuração do banco mesmo com erro na API...`);
        }

        // Remover todo o objeto whatsapp do banco
        await Empresa.updateOne({ _id: id }, { $unset: { whatsapp: "" } });
        
        return res.status(201).json({ 
            msg: `Instância removida com sucesso para ${empresa.name}. A empresa pode criar uma nova instância.` 
        });

    } catch (error) {
        console.error("❌ Erro ao deletar a instância:", error);
        return res.status(500).json({ 
            msg: `Erro interno do servidor: ${error.message}`,
            error: error.message 
        });
    }
});

app.post("/createInstance-admin/:userID/:empresaId", async (req, res) => {
    try {
        const { userID, empresaId } = req.params;

        // 🔒 Verifica se o usuário tem permissão para criar instâncias
        if (userID !== ADMIN_ID) {
            return res.status(403).json({ message: "Acesso negado" });
        }

        console.log(`🔍 Recebida solicitação para criar instância WhatsApp para empresa: ${empresaId}`);

        // 📌 Chamar a função para criar a instância com webhook específico
        const whatsappInstance = await createWhatsAppInstanceForEmpresa(empresaId);

        if (!whatsappInstance) {
            return res.status(400).json({ msg: "Erro ao criar instância do WhatsApp" });
        }

        // 🔄 Retornar sucesso
        return res.status(201).json({
            msg: "Instância do WhatsApp criada com sucesso!",
            instance: whatsappInstance
        });

    } catch (error) {
        console.error("❌ Erro ao criar instância do WhatsApp:", error);
        return res.status(500).json({ msg: "Erro interno do servidor" });
    }
});


// Post para obter Empresa pelo Id Sq
app.post("/get-caixa/:id", checkToken, async (req, res) => {
    const { id_caixa } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            const empresa = await Caixa.findOne({ _id: id_caixa });
            //const empresaRev = await Revenda.findOne({"id_empresa":id_empresa})
            if (empresa) {
                return res.status(200).json({ empresa });
            } else {
                return res.status(404).json({ msg: "Caixa não encontrado!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Pedidos pelo id_empresa
app.post("/get-pedidos/:id", checkToken, async (req, res) => {
    const { id_empresa } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            var empresa_vinculada = userExist.vinculo_empresa;
            if (userExist.vinculo_empresa == id_empresa) {
                const pedidos = await Pedidos.find({ id_empresa: empresa_vinculada });
                if (pedidos) {
                    return res.status(200).json({ pedidos });
                } else {
                    return;
                }
            } else {
                return res
                    .status(404)
                    .json({ msg: "Usuário sem vinculo com a empresa!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Categorias pelo id_empresa
app.post("/get-categorias/:id", checkToken, async (req, res) => {
    const { id_empresa, vinculo_empresa, empresaObjId } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            var empresa_vinculada = userExist.vinculo_empresa;
            if (empresa_vinculada == vinculo_empresa) {
                const categorias = await Categorias.find({
                    id_empresa: id_empresa,
                    empresaObjId: empresaObjId,
                    inativo: false,
                });
                if (categorias) {
                    return res.status(200).json({ categorias });
                } else {
                    return;
                }
            } else {
                return res
                    .status(404)
                    .json({ msg: "Usuário sem vinculo com a empresa!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Adicionais pelo id_empresa
app.post("/get-adicionais/:id", checkToken, async (req, res) => {
    const { id_empresa, vinculo_empresa, empresaObjId } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            var empresa_vinculada = userExist.vinculo_empresa;
            if (empresa_vinculada == vinculo_empresa) {
                const adicionais = await Adicionais.find({
                    id_empresa: id_empresa,
                    empresaObjId: empresaObjId,
                });
                if (adicionais) {
                    return res.status(200).json({ adicionais });
                } else {
                    return;
                }
            } else {
                return res
                    .status(404)
                    .json({ msg: "Usuário sem vinculo com a empresa!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Adicionais do Cardápio pelo id_empresa e nome_empresa
app.post("/get-adicionais-cardapio", async (req, res) => {
    const { id_empresa, nomeEmpresa } = req.body;

    if (!nomeEmpresa) {
        return res.status(400).json({ msg: "Nome da empresa não informado." });
    }

    const nome_empresa = new RegExp(nomeEmpresa.replace(/-/g, " "), "i"); // Corrigido para substituir '-' por espaço

    try {
        const empresaExist = await Empresa.findOne({
            id_empresa: id_empresa,
            name: nome_empresa,
        });
        if (!empresaExist) {
            return res.status(404).json({ msg: "Empresa não encontrada!" });
        }

        const adicionais = await Adicionais.find({ id_empresa: id_empresa });
        if (adicionais && adicionais.length > 0) {
            // Filtra os adicionais removendo os que têm out: true e que não têm sub-adicionais
            /*console.log("Adicionais>>", adicionais)
            const adicionaisFiltrados = adicionais
                .map((grupo) => {
                    // Filtra os adicionais dentro de cada grupo, removendo os com out: true
                    const adicionaisValidos = grupo.adicionais
                        .filter((adicional) => !adicional.out) // Filtra itens com out: true
                        //.filter((adicional) => adicional.title); // Filtra itens sem title (sem sub-adicionais cadastrados)

                    // Verifica se o grupo tem sub-adicionais válidos
                    if (adicionaisValidos.length > 0) {
                        return {
                            ...grupo.toObject(), // Usando toObject() para garantir que seja um objeto simples
                            adicionais: adicionaisValidos, // Apenas os adicionais válidos
                        };
                    }
                    return null; // Se não houver sub-adicionais válidos, retorna null (será removido na etapa de filtragem)
                })
                .filter((grupo) => grupo !== null); // Remove grupos que não têm sub-adicionais válidos*/

            return res.status(200).json({ adicionais });
        } else {
            return res.status(404).json({ msg: "Nenhum adicional encontrado" });
        }
    } catch (err) {
        console.error(err);
        return res.status(500).json({ error: "Erro interno do servidor" });
    }
});

// Post para obter Categorias Cardapio pelo id_empresa e nome_empresa
app.post("/get-categorias-cardapio", async (req, res) => {
    console.time("🕒 Tempo total da requisição /get-categorias-cardapio");

    try {
        console.time("🔍 Tempo para encontrar a empresa");

        const { id_empresa, nomeEmpresa } = req.body;
        const nome_empresa = new RegExp(nomeEmpresa.replace(/-/g, "\\s"), "i");

        const empresaExist = await Empresa.findOne({
            id_empresa: id_empresa,
            name: nome_empresa,
        });

        console.timeEnd("🔍 Tempo para encontrar a empresa");

        if (!empresaExist) {
            console.log("❌ Empresa não encontrada!");
            console.timeEnd("🕒 Tempo total da requisição /get-categorias-cardapio");
            return res.status(404).json({ msg: "Página não encontrada!" });
        }

        const objIdEmpresa = empresaExist._id;

        console.time("⏳ Tempo para buscar categorias no banco");

        // Obtendo a data atual
        const now = new Date();
        const startTime = now.getHours() * 60 + now.getMinutes(); // Horário atual em minutos

        const daysOfWeek = ["domingo", "segunda", "terca", "quarta", "quinta", "sexta", "sabado"];
        const dia_da_semana = daysOfWeek[now.getDay()];

        // Consulta ao MongoDB
        const categorias = await Categorias.find({
            id_empresa: id_empresa,
            inativo: false,
            $or: [
                { disponibilidade: "sempre" },
                {
                    disponibilidade: "especificos",
                    dia_horario_disponibilidade: {
                        $elemMatch: { dia: dia_da_semana },
                    },
                },
            ],
        });

        console.timeEnd("⏳ Tempo para buscar categorias no banco");
        console.log(`📦 Categorias retornadas do banco: ${categorias.length}`);

        console.time("📌 Tempo para processar horários das categorias");

        // Processamento dos horários das categorias
        const categoriasComHorarioConvertido = categorias.map((categoria) => ({
            ...categoria.toObject(),
            dia_horario_disponibilidade: categoria.dia_horario_disponibilidade.map((hora) => ({
                ...hora,
                startTime: parseInt(hora.startTime.slice(0, 2)) * 60 + parseInt(hora.startTime.slice(3, 5)),
                endTime: parseInt(hora.endTime.slice(0, 2)) * 60 + parseInt(hora.endTime.slice(3, 5)),
            })),
        }));

        console.timeEnd("📌 Tempo para processar horários das categorias");

        console.time("🛠 Tempo para filtrar categorias disponíveis");

        // Filtragem das categorias disponíveis no horário atual
        const filteredCategories = categoriasComHorarioConvertido.filter((categoria) => {
            if (categoria.disponibilidade === "sempre") return true;
            return categoria.dia_horario_disponibilidade.some((hora) =>
                hora.dia === dia_da_semana && hora.startTime <= startTime && hora.endTime >= startTime
            );
        });

        console.timeEnd("🛠 Tempo para filtrar categorias disponíveis");
        console.log(`✅ Categorias disponíveis: ${filteredCategories.length}`);

        console.timeEnd("🕒 Tempo total da requisição /get-categorias-cardapio");

        if (filteredCategories.length > 0) {
            return res.status(200).json({ filteredCategories, objIdEmpresa });
        } else {
            return res.status(222).json({ message: "Nenhuma categoria encontrada", objIdEmpresa });
        }
    } catch (error) {
        console.error("❌ Erro na rota /get-categorias-cardapio:", error);
        console.timeEnd("🕒 Tempo total da requisição /get-categorias-cardapio");
        return res.status(500).json({ error: "Erro interno do servidor" });
    }
});


// Rota para obter informações básicas da empresa pelo id_empresa e nome_empresa
app.post("/get-empresa-info", async (req, res) => {
    try {
        console.time("⏳ Tempo total da requisição");

        console.time("🔍 Buscando empresa no banco");
        const { id_empresa, nomeEmpresa } = req.body;
        const nome_empresa = new RegExp(nomeEmpresa.replace(/-/g, "\\s"), "i");

        const empresa = await Empresa.findOne({
            id_empresa: id_empresa,
            name: nome_empresa,
        }).lean();
        console.timeEnd("🔍 Buscando empresa no banco");

        if (!empresa) {
            console.log("❌ Empresa não encontrada!");
            return res.status(404).json({ msg: "Empresa não encontrada!" });
        }

        console.log("✅ Empresa encontrada:", empresa.name);
        console.log("⏰ Timezone configurado:", empresa.timezone);

        console.time("🕒 Obtendo horário atual");
        const timezone = empresa.timezone || "America/Sao_Paulo";
        const now = momentTz().tz(timezone);
        console.log(`🕒 Horário atual (${timezone}): ${now.format("YYYY-MM-DD HH:mm:ss")}`);
        console.timeEnd("🕒 Obtendo horário atual");

        const weekDays = ["domingo", "segunda", "terca", "quarta", "quinta", "sexta", "sabado"];
        const diaSemanaIndex = now.day();
        const diaSemana = weekDays[diaSemanaIndex];
        const horariosFuncionamento = empresa.horario_funcionamento || {};
        const horariosHoje = horariosFuncionamento[diaSemana] || [];

        console.log(`📅 Hoje é ${diaSemana}, horários cadastrados:`, horariosHoje);

        let statusLoja = "fechado";
        let proximoHorario = "Horário não definido";

        console.time("⚙️ Processando status da loja");
        if (empresa.fechamento_temporario) {
            statusLoja = "fechado";
            proximoHorario = "Fechada Temporariamente";
            console.log("🔴 Loja fechada manualmente.");
        } else if (empresa.status_loja === "sempre") {
            statusLoja = "aberto";
            proximoHorario = "Sempre aberto";
            console.log("🟢 Loja sempre aberta.");
        } else {
            let encontrouProximoHorario = false;

            for (const periodo of horariosHoje) {
                console.log(`📌 Analisando período: ${periodo.start} - ${periodo.end}`);

                const abertura = now.clone().set({
                    hour: parseInt(periodo.start.split(":")[0], 10),
                    minute: parseInt(periodo.start.split(":")[1], 10),
                    second: 0,
                });

                let fechamento = now.clone().set({
                    hour: parseInt(periodo.end.split(":")[0], 10),
                    minute: parseInt(periodo.end.split(":")[1], 10),
                    second: 0,
                });

                console.log(`➡️ Horário de abertura: ${abertura.format("YYYY-MM-DD HH:mm:ss")}`);
                console.log(`➡️ Horário de fechamento: ${fechamento.format("YYYY-MM-DD HH:mm:ss")}`);

                // Se o fechamento for antes da abertura (passando de 00:00), adiciona um dia
                if (fechamento.isBefore(abertura)) {
                    fechamento.add(1, "day");
                    console.log(`🔄 Ajustando fechamento para o próximo dia: ${fechamento.format("YYYY-MM-DD HH:mm:ss")}`);
                }

                if (now.isBetween(abertura, fechamento)) {
                    statusLoja = "aberto";
                    proximoHorario = `Aberto até ${fechamento.format("HH:mm")}`;
                    encontrouProximoHorario = true;
                    console.log(`🟢 Loja aberta agora, fecha às ${fechamento.format("HH:mm")}`);
                    break;
                } else if (now.isBefore(abertura)) {
                    proximoHorario = `Abre hoje às ${abertura.format("HH:mm")}`;
                    encontrouProximoHorario = true;
                    console.log(`🔜 Próxima abertura hoje às ${abertura.format("HH:mm")}`);
                    break;
                }
            }

            if (!encontrouProximoHorario) {
                console.time("🔎 Buscando próximo dia de abertura");
                for (let i = 1; i <= 7; i++) {
                    const nextDayIndex = (diaSemanaIndex + i) % 7;
                    const nextDayName = weekDays[nextDayIndex];
                    const horariosProximoDia = horariosFuncionamento[nextDayName] || [];

                    if (horariosProximoDia.length > 0) {
                        const proximaAbertura = now.clone().add(i, "days").set({
                            hour: parseInt(horariosProximoDia[0].start.split(":")[0], 10),
                            minute: parseInt(horariosProximoDia[0].start.split(":")[1], 10),
                            second: 0,
                        });

                        proximoHorario = `Abre ${nextDayName} às ${proximaAbertura.format("HH:mm")}`;
                        console.log(`🔜 Loja abre ${nextDayName} às ${proximaAbertura.format("HH:mm")}`);
                        break;
                    }
                }
                console.timeEnd("🔎 Buscando próximo dia de abertura");
            }
        }
        console.timeEnd("⚙️ Processando status da loja");

        console.timeEnd("⏳ Tempo total da requisição");
        return res.status(200).json({
            empresaId: empresa._id,
            nome: empresa.name,
            fechamentoTemporario: empresa.fechamento_temporario,
            statusLoja,
            proximoHorario,
            empresaData: empresa,
        });

    } catch (error) {
        console.error("❌ Erro ao obter informações da empresa:", error);
        return res.status(500).json({ msg: "Erro interno do servidor" });
    }
});



// Rota para atualizar o status_bot
app.patch("/api/empresa/:id/status_bot", async (req, res) => {
    const { id } = req.params;
    const { status_bot } = req.body;

    try {
        const empresa = await Empresa.findByIdAndUpdate(
            id,
            { status_bot },
            { new: true }
        );
        if (!empresa) {
            return res.status(404).send("Empresa não encontrada");
        }
        console.log("status_bot atualizado da Empresa:", empresa);
        res.json(empresa);
    } catch (error) {
        res.status(500).send("Erro ao atualizar status_bot");
    }
});

// Rota para atualizar o call_atendente
app.patch("/api/empresa/:id/call_atendente", async (req, res) => {
    const { id } = req.params;
    const { call_atendente } = req.body;

    try {
        const empresa = await Empresa.findByIdAndUpdate(
            id,
            { call_atendente },
            { new: true }
        );
        if (!empresa) {
            return res.status(404).send("Empresa não encontrada");
        }
        //console.log("call_atendente atualizado da Empresa:", empresa);
        res.json(empresa);
    } catch (error) {
        res.status(500).send("Erro ao atualizar call_atendente");
    }
});

async function removerImagensBase64() {
    try {
        console.log("🔍 Buscando itens com imagem em Base64...");

        // Atualiza todos os itens onde `image` começa com "data:image"
        const result = await Itens.updateMany(
            { image: { $regex: "^data:image" } },
            { $set: { image: "" } }
        );

        console.log(`✅ Imagens Base64 removidas! Total de itens alterados: ${result.modifiedCount}`);
    } catch (error) {
        console.error("❌ Erro ao remover imagens Base64:", error);
    }
}

// Chamando a função
//removerImagensBase64();

// Post para obter Itens Cardapio pelo id_empresa e nome_empresa
app.post("/get-itens-cardapio", async (req, res) => {
    console.time("🕒 Tempo total da requisição /get-itens-cardapio");

    try {
        console.time("🔍 Tempo para encontrar a empresa");

        const { id_empresa, nomeEmpresa } = req.body;
        const nome_empresa = new RegExp(nomeEmpresa.replace(/-/g, "\\s"), "i");

        const empresaExist = await Empresa.findOne({
            id_empresa: id_empresa,
            name: nome_empresa,
        });

        console.timeEnd("🔍 Tempo para encontrar a empresa");

        if (!empresaExist) {
            console.log("❌ Empresa não encontrada!");
            console.timeEnd("🕒 Tempo total da requisição /get-itens-cardapio");
            return res.status(404).json({ msg: "Página não encontrada!" });
        }

        console.time("⏳ Tempo para buscar itens do cardápio");

        // Obtendo a data atual
        const now = new Date();
        const startTime = now.getHours() * 60 + now.getMinutes(); // Horário atual em minutos

        const daysOfWeek = [
            "domingo", "segunda", "terca", "quarta", "quinta", "sexta", "sabado"
        ];
        const dia_da_semana = daysOfWeek[now.getDay()];

        // Consulta ao MongoDB
        const itens = await Itens.find({
            id_empresa: id_empresa,
            inativo: false,
            out: { $ne: true },
            $or: [
                { disponibilidade: "sempre" },
                {
                    disponibilidade: "especificos",
                    dia_horario_disponibilidade: {
                        $elemMatch: { dia: dia_da_semana },
                    },
                },
            ],
        });

        console.timeEnd("⏳ Tempo para buscar itens do cardápio");
        console.log(`📦 Itens retornados do banco: ${itens.length}`);

        console.time("📌 Tempo para processar horários dos itens");

        // Processamento dos horários dos itens
        const itensComHorarioConvertido = itens.map((item) => ({
            ...item.toObject(),
            dia_horario_disponibilidade: item.dia_horario_disponibilidade.map((hora) => ({
                ...hora,
                startTime: parseInt(hora.startTime.slice(0, 2)) * 60 + parseInt(hora.startTime.slice(3, 5)),
                endTime: parseInt(hora.endTime.slice(0, 2)) * 60 + parseInt(hora.endTime.slice(3, 5))
            }))
        }));

        console.timeEnd("📌 Tempo para processar horários dos itens");

        console.time("🛠 Tempo para filtrar itens disponíveis");

        // Filtragem de itens disponíveis no horário atual
        const filteredItens = itensComHorarioConvertido.filter((item) => {
            if (item.disponibilidade === "sempre") return true;
            return item.dia_horario_disponibilidade.some((hora) =>
                hora.dia === dia_da_semana && hora.startTime <= startTime && hora.endTime >= startTime
            );
        });

        console.timeEnd("🛠 Tempo para filtrar itens disponíveis");
        console.log(`✅ Itens disponíveis para venda: ${filteredItens.length}`);

        console.timeEnd("🕒 Tempo total da requisição /get-itens-cardapio");

        if (filteredItens.length > 0) {
            return res.status(200).json({ filteredItens });
        } else {
            return res.status(222).json({ message: "Nenhum item encontrado" });
        }
    } catch (error) {
        console.error("❌ Erro na rota /get-itens-cardapio:", error);
        console.timeEnd("🕒 Tempo total da requisição /get-itens-cardapio");
        return res.status(500).json({ error: "Erro interno do servidor" });
    }
});


// Post para obter Itens Cardapio pelo id_empresa e nome_empresa
app.post("/get-itens-cardapio-salao", async (req, res) => {
    const { id_empresa, nomeEmpresa } = req.body;
    const nome_empresa = new RegExp(nomeEmpresa.replace(/-/g, "\\s"), "i");

    const empresaExist = await Empresa.find({
        id_empresa: id_empresa,
        name: nome_empresa,
    });
    if (empresaExist.length === 0) {
        return res.status(404).json({ msg: "Pagina não encontrada!" });
    }

    // Obtendo a data atual
    const now = new Date();
    const startTime = now.getHours() * 60 + now.getMinutes(); // Horário atual em minutos

    const daysOfWeek = [
        "domingo",
        "segunda",
        "terca",
        "quarta",
        "quinta",
        "sexta",
        "sabado",
    ];
    const dia_da_semana = daysOfWeek[now.getDay()];

    // Consulta usando $or e $elemMatch
    const itens = await Itens.find({
        id_empresa: id_empresa,
        inativo: false,
        out_salao: { $ne: true },
        $or: [
            { disponibilidade: "sempre" },
            {
                disponibilidade: "especificos",
                dia_horario_disponibilidade: {
                    $elemMatch: {
                        dia: dia_da_semana,
                    },
                },
            },
        ],
    }).exec((err, itens) => {
        if (err) {
            console.error(err);
            return res.status(500).json({ error: "Erro ao obter itens" });
        }

        // Iterar sobre o array de itens e converter os campos "startTime" e "endTime" para números
        itens = itens.map((item) => {
            item.dia_horario_disponibilidade = item.dia_horario_disponibilidade.map(
                (hora) => {
                    hora.startTime =
                        parseInt(hora.startTime.slice(0, 2)) * 60 +
                        parseInt(hora.startTime.slice(3, 5));
                    hora.endTime =
                        parseInt(hora.endTime.slice(0, 2)) * 60 +
                        parseInt(hora.endTime.slice(3, 5));
                    return hora;
                }
            );
            return item;
        });

        // Filtrar as itens que estão dentro do range de data e horário atual
        const filteredItens = itens.filter((item) => {
            if (item.disponibilidade === "sempre") {
                return true;
            } else {
                return item.dia_horario_disponibilidade.some((hora) => {
                    return (
                        hora.dia === dia_da_semana &&
                        hora.startTime <= startTime &&
                        hora.endTime >= startTime
                    );
                });
            }
        });

        if (filteredItens.length > 0) {
            return res.status(200).json({ filteredItens });
        } else {
            return res.status(222).json({ message: "Nenhum item encontrado" });
        }
    });
});

// Post para obter Itens pelo id_empresa
app.post("/get-itens/:id", checkToken, async (req, res) => {
    const { id_empresa, vinculo_empresa, empresaObjId } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            var empresa_vinculada = userExist.vinculo_empresa;
            if (empresa_vinculada == vinculo_empresa) {
                const itens = await Itens.find({
                    id_empresa: id_empresa,
                    empresaObjId: empresaObjId,
                });
                if (itens) {
                    return res.status(200).json({ itens });
                } else {
                    return;
                }
            } else {
                return res
                    .status(404)
                    .json({ msg: "Usuário sem vinculo com a empresa!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Item pelo ObjId do Item
app.post("/get-item/:id", checkToken, async (req, res) => {
    const { id_empresa, vinculo_empresa, empresaObjId, itemObjId } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            var empresa_vinculada = userExist.vinculo_empresa;
            if (empresa_vinculada == vinculo_empresa) {
                const item = await Itens.findOne({
                    id_empresa: id_empresa,
                    empresaObjId: empresaObjId,
                    _id: itemObjId,
                });
                if (item) {
                    return res.status(200).json({ item });
                } else {
                    return;
                }
            } else {
                return res
                    .status(404)
                    .json({ msg: "Usuário sem vinculo com a empresa!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Item pelo ObjId do Item
app.post("/get-item-cardapio/", async (req, res) => {
    const { id_empresa, itemObjId } = req.body;

    const item = await Itens.findOne({ id_empresa: id_empresa, _id: itemObjId });
    if (item) {
        return res.status(200).json({ item });
    } else {
        return;
    }
});

// Post para obter Pedido pelo Id Sq
app.post("/get-pedido/:id", checkToken, async (req, res) => {
    const { id_empresa, vinculo_empresa, id_pedido } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            var empresa_vinculada = userExist.vinculo_empresa;
            if (empresa_vinculada == vinculo_empresa) {
                const pedido = await Pedidos.find({
                    id_empresa: id_empresa,
                    _id: id_pedido,
                });
                if (pedido) {
                    return res.status(200).json({ pedido });
                } else {
                    return;
                }
            } else {
                return res
                    .status(404)
                    .json({ msg: "Usuário sem vinculo com a empresa!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Pedidos do Status enviado na requisição
app.post("/get-pedidosStatus/:id", checkToken, async (req, res) => {
    const { id_empresa, vinculo_empresa, status_pedido, forReport } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            var empresa_vinculada = userExist.vinculo_empresa;
            //console.log(userExist.vinculo_empresa+ " - " +vinculo_empresa);
            if (empresa_vinculada === vinculo_empresa) {
                let pedidos = "";
                if (status_pedido == "4") {
                    //const caixa_aberto_id = await Empresa.findOne({"id_empresa":id_empresa}, "caixa_aberto_id")
                    //const data_abertura_fechamento_caixa = await Caixa.findById(caixa_aberto_id, "data_abertura data_fechamento")
                    /*let data_hoje = momentTz().tz('America/Sao_Paulo').format("DD/MM/YYYY"); //KAKAKA
                              pedidos = await Pedidos.find({ "id_empresa": id_empresa, "status_pedido": status_pedido, "finalizadoAt": data_hoje, cancelado: { $ne: true } })*/
                    // Recupera o 'caixa_aberto_id' da empresa
                    const caixa_aberto = await Empresa.findOne(
                        { id_empresa: id_empresa },
                        "caixa_aberto_id"
                    );

                    // Log: Verificar se encontrou o caixa_aberto
                    //console.log("caixa_aberto:", caixa_aberto);

                    // Verifica se há um caixa aberto. Caso contrário, não faz a consulta.
                    if (!caixa_aberto || !caixa_aberto.caixa_aberto_id) {
                        //console.log("Não há caixa aberto para a empresa. Retornando sem resultados.");
                        return res.status(200).json({ pedidos: [] }); // Retorna lista vazia ou mensagem apropriada
                    }

                    // Recupera as datas de abertura e fechamento do caixa
                    const caixa = await Caixa.findById(
                        caixa_aberto.caixa_aberto_id,
                        "data_abertura data_fechamento"
                    );

                    // Log: Verificar se encontrou o caixa e suas datas
                    //console.log("caixa:", caixa);

                    let query = {
                        id_empresa: id_empresa,
                        status_pedido: status_pedido,
                    };

                    // Adiciona o filtro cancelado se forReport for true
                    if (forReport === true) {
                        query.cancelado = { $ne: true }; // Adiciona o filtro de cancelamento
                    }

                    // Verifica se a data de abertura do caixa existe, para filtrar pedidos após ela
                    if (caixa && caixa.data_abertura) {
                        const dataAbertura = new Date(caixa.data_abertura);
                        //console.log("Data de abertura do caixa:", dataAbertura);

                        if (isNaN(dataAbertura.getTime())) {
                            //console.error("Erro: A data de abertura não é válida!");
                            return res
                                .status(400)
                                .json({ error: "Data de abertura inválida!" });
                        }

                        // Filtro para pedidos finalizados após a abertura do caixa
                        query.finalizadoAt = { $gte: dataAbertura };
                        //console.log("Consulta após filtrar por data_abertura:", query);
                    }

                    // Se existir data de fechamento do caixa, filtra pedidos antes dessa data
                    if (caixa && caixa.data_fechamento) {
                        const dataFechamento = new Date(caixa.data_fechamento);
                        //console.log("Data de fechamento do caixa:", dataFechamento);

                        if (isNaN(dataFechamento.getTime())) {
                            //console.error("Erro: A data de fechamento não é válida!");
                            return res
                                .status(400)
                                .json({ error: "Data de fechamento inválida!" });
                        }

                        // Filtro para pedidos finalizados antes do fechamento do caixa
                        query.finalizadoAt = { ...query.finalizadoAt, $lt: dataFechamento };
                        //console.log("Consulta após filtrar por data_fechamento:", query);
                    }

                    // Executa a consulta no banco de dados
                    try {
                        pedidos = await Pedidos.find(query);
                        //console.log("Pedidos encontrados:", pedidos);  // Exibe os pedidos encontrados

                        //return res.status(200).json({ pedidos });  // Retorna os pedidos encontrados
                    } catch (err) {
                        console.error("Erro ao buscar pedidos:", err);
                        return res.status(500).json({ error: "Erro ao buscar pedidos." });
                    }
                } else {
                    pedidos = await Pedidos.find({
                        id_empresa: id_empresa,
                        status_pedido: status_pedido,
                        cancelado: { $ne: true },
                    });
                }

                if (pedidos) {
                    return res.status(200).json({ pedidos });
                } else {
                    return;
                }
            } else {
                return res
                    .status(404)
                    .json({ msg: "Usuário sem vinculo com a empresa!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

app.post("/get-pedidosStatusSimples/:id", checkToken, async (req, res) => {
    const { id_empresa, vinculo_empresa, status_pedido, forReport } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            var empresa_vinculada = userExist.vinculo_empresa;
            if (empresa_vinculada === vinculo_empresa) {
                let query = {
                    id_empresa: id_empresa,
                    status_pedido: { $in: status_pedido }, // Filtra por múltiplos status
                    cancelado: { $ne: true },
                };

                try {
                    const pedidos = await Pedidos.find(query);
                    return res.status(200).json({ pedidos });
                } catch (err) {
                    console.error("Erro ao buscar pedidos:", err);
                    return res.status(500).json({ error: "Erro ao buscar pedidos." });
                }
            } else {
                return res
                    .status(404)
                    .json({ msg: "Usuário sem vínculo com a empresa!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

app.post("/get-pedidosStatusFinalizados/:id", checkToken, async (req, res) => {
    const { id_empresa, vinculo_empresa, forReport } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            var empresa_vinculada = userExist.vinculo_empresa;
            if (empresa_vinculada === vinculo_empresa) {
                const caixa_aberto = await Empresa.findOne(
                    { id_empresa: id_empresa },
                    "caixa_aberto_id"
                );

                if (!caixa_aberto || !caixa_aberto.caixa_aberto_id) {
                    return res.status(200).json({ pedidos: [] }); // Retorna lista vazia caso não haja caixa aberto
                }

                const caixa = await Caixa.findById(
                    caixa_aberto.caixa_aberto_id,
                    "data_abertura data_fechamento"
                );

                let query = {
                    id_empresa: id_empresa,
                    status_pedido: "4", // Filtro para status finalizado
                };

                if (forReport === true) {
                    query.cancelado = { $ne: true };
                }

                // Filtra pedidos finalizados após a abertura do caixa
                if (caixa && caixa.data_abertura) {
                    const dataAbertura = new Date(caixa.data_abertura);
                    if (isNaN(dataAbertura.getTime())) {
                        return res
                            .status(400)
                            .json({ error: "Data de abertura inválida!" });
                    }
                    query.finalizadoAt = { $gte: dataAbertura };
                }

                // Se o caixa foi fechado, filtra pedidos antes da data de fechamento
                if (caixa && caixa.data_fechamento) {
                    const dataFechamento = new Date(caixa.data_fechamento);
                    if (isNaN(dataFechamento.getTime())) {
                        return res
                            .status(400)
                            .json({ error: "Data de fechamento inválida!" });
                    }
                    query.finalizadoAt = { ...query.finalizadoAt, $lt: dataFechamento };
                }

                try {
                    const pedidos = await Pedidos.find(query);
                    return res.status(200).json({ pedidos });
                } catch (err) {
                    console.error("Erro ao buscar pedidos:", err);
                    return res.status(500).json({ error: "Erro ao buscar pedidos." });
                }
            } else {
                return res
                    .status(404)
                    .json({ msg: "Usuário sem vínculo com a empresa!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Historico de Pedidos finalizados
app.post(
    "/get-pedidosFinalizadosHistorico/:id",
    checkToken,
    async (req, res) => {
        const { id_empresa, vinculo_empresa, status_pedido, periodo } = req.body;
        const id = req.params.id;

        if (id.match(/^[0-9a-fA-F]{24}$/)) {
            const userExist = await User.findById(id);
            if (userExist) {
                var empresa_vinculada = userExist.vinculo_empresa;
                if (empresa_vinculada === vinculo_empresa) {
                    let pedidos = [];
                    let pedidosComparativos = [];
                    let dataInicio;
                    let dataComparativa;

                    if (status_pedido == "4") {
                        // Calcula a data de início com base no período fornecido
                        const hoje = moment().startOf("day"); // Início do dia atual
                        if (periodo === "hoje") {
                            dataInicio = hoje;
                            dataComparativa = hoje.clone().subtract(1, "days");
                        } else if (periodo === "7dias") {
                            dataInicio = hoje.clone().subtract(6, "days");
                            dataComparativa = hoje.clone().subtract(13, "days");
                        } else if (periodo === "15dias") {
                            dataInicio = hoje.clone().subtract(14, "days");
                            dataComparativa = hoje.clone().subtract(29, "days");
                        } else if (periodo === "30dias") {
                            dataInicio = hoje.clone().subtract(29, "days");
                            dataComparativa = hoje.clone().subtract(59, "days");
                        } else {
                            return res.status(400).json({ msg: "Período inválido!" });
                        }

                        // Encontrar pedidos e converter finalizadoAt para data
                        const allPedidos = await Pedidos.find({
                            id_empresa: id_empresa,
                            status_pedido: status_pedido,
                            cancelado: { $ne: true }, // Adiciona o filtro para não trazer pedidos cancelados
                        });
                        // Filtrar pedidos pela data de início calculada
                        pedidos = allPedidos.filter((pedido) => {
                            //const finalizadoAtDate = moment(pedido.finalizadoAt, "DD/MM/YYYY").toDate();
                            const createdAtDate = moment(
                                pedido.createdAt,
                                "DD/MM/YYYY"
                            ).toDate();
                            //console.log(createdAtDate)
                            return createdAtDate >= dataInicio.toDate();
                        });

                        // Filtrar pedidos pela data comparativa
                        pedidosComparativos = allPedidos.filter((pedido) => {
                            //const finalizadoAtDate = moment(pedido.finalizadoAt, "DD/MM/YYYY").toDate();
                            const createdAtDate = moment(
                                pedido.createdAt,
                                "DD/MM/YYYY"
                            ).toDate();
                            //console.log(createdAtDate)
                            return (
                                createdAtDate >= dataComparativa.toDate() &&
                                createdAtDate < dataInicio.toDate()
                            );
                        });
                    }

                    return res.status(200).json({ pedidos, pedidosComparativos });
                } else {
                    return res
                        .status(404)
                        .json({ msg: "Usuário sem vínculo com a empresa!" });
                }
            } else {
                return res
                    .status(404)
                    .json({ msg: "Id de requisição não encontrado!" });
            }
        } else {
            return res.status(400).json({ msg: "Parâmetros inválidos!" });
        }
    }
);

// Post para obter Histórico de Pedidos finalizados
app.post("/get-pedidosFinalizadosPeriodo/:id", checkToken, async (req, res) => {
    const { id_empresa, vinculo_empresa, status_pedido, startDate, endDate } = req.body;
    const id = req.params.id;

    //console.log({ startDate, endDate });

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            const empresa_vinculada = userExist.vinculo_empresa;
            if (empresa_vinculada === vinculo_empresa) {
                try {
                    // 📅 Converter as datas recebidas para Moment.js
                    const start = moment(startDate).startOf("day");
                    const end = moment(endDate).endOf("day");

                    if (!start.isValid() || !end.isValid()) {
                        return res.status(400).json({ msg: "Datas inválidas!" });
                    }

                    // 📅 Calcular intervalo para o período comparativo
                    const diffDays = end.diff(start, 'days'); // Quantidade de dias no período
                    const startComparativo = moment(start).subtract(diffDays + 1, 'days').startOf("day");
                    const endComparativo = moment(end).subtract(diffDays + 1, 'days').endOf("day");

                    //console.log(`🔹 Período Atual: ${start.format("YYYY-MM-DD")} → ${end.format("YYYY-MM-DD")}`);
                    //console.log(`🔹 Período Comparativo: ${startComparativo.format("YYYY-MM-DD")} → ${endComparativo.format("YYYY-MM-DD")}`);

                    // 🔍 Buscar pedidos do período atual
                    const pedidos = await Pedidos.find({
                        id_empresa: id_empresa,
                        status_pedido: status_pedido,
                        createdAt: {
                            $gte: start.toDate(),
                            $lte: end.toDate(),
                        },
                        $or: [{ cancelado: { $exists: false } }, { cancelado: false }]
                    });

                    // 🔍 Buscar pedidos do período comparativo
                    const pedidosComparativos = await Pedidos.find({
                        id_empresa: id_empresa,
                        status_pedido: status_pedido,
                        createdAt: {
                            $gte: startComparativo.toDate(),
                            $lte: endComparativo.toDate(),
                        },
                        $or: [{ cancelado: { $exists: false } }, { cancelado: false }]
                    });

                    // 📦 Retorna os dois períodos no JSON
                    return res.status(200).json({ pedidos, pedidosComparativos });

                } catch (error) {
                    return res.status(500).json({ msg: "Erro ao buscar pedidos", error });
                }
            } else {
                return res.status(404).json({ msg: "Usuário sem vínculo com a empresa!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});


// Post para obter Pedidos a serem impressos pelo Id Sq
app.post("/get-pedidos-toprint/:id", checkToken, async (req, res) => {
    const { id_empresa, vinculo_empresa } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            var empresa_vinculada = userExist.vinculo_empresa;
            if (empresa_vinculada === vinculo_empresa) {
                const pedidos = await Pedidos.find({
                    id_empresa: id_empresa,
                    status_print: true,
                });
                if (pedidos) {
                    return res.status(200).json({ pedidos });
                } else {
                    return;
                }
            } else {
                return res
                    .status(422)
                    .json({ msg: "Usuário sem vinculo com a empresa!" });
            }
        } else {
            return res.status(422).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Função para normalizar o telefone
const normalizePhoneNumber = (phone) => {
    return phone.replace(/[^\d]/g, ""); // Remove todos os caracteres não numéricos
};

const normalizePhoneNumberToSearch = (phone) => {
    const cleaned = phone.replace(/\D/g, ""); // Remove caracteres não numéricos

    // Verifica se já tem o código do país
    if (cleaned.length === 11) {
        return "55" + cleaned; // Se for um número nacional, adiciona o código do Brasil
    } else if (cleaned.length === 13 && cleaned.startsWith("55")) {
        return cleaned; // Já está no formato correto
    } else {
        return cleaned; // Retorna o número como está caso não seja um caso comum
    }
};

const findLeadId = async (id_empresa, celular_cliente) => {
    const celularNormalizado = normalizePhoneNumberToSearch(celular_cliente);

    // Testa com e sem o nono dígito
    const celularSemNonoDigito = celularNormalizado.length === 13
        ? celularNormalizado.substring(0, 4) + celularNormalizado.substring(5)
        : celularNormalizado;

    const lead = await LeadChannel.findOne({
        empresaObjId: id_empresa,
        $or: [
            { mobile_number: celularNormalizado },
            { mobile_number: celularSemNonoDigito }
        ]
    });

    return lead ? lead._id : null;
};

// Post para obter Cliente pelo Id Empresa e telefone
app.post("/get-cliente/:id", checkToken, async (req, res) => {
    const { id_empresa, telefone } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            const normalizedTelefone = normalizePhoneNumber(telefone);
            const regexTelefone = new RegExp(
                normalizedTelefone.split("").join(".*"),
                "i"
            ); // Regex para busca parcial

            const clientes = await Cliente.find({
                id_empresa: id_empresa,
                telefone: { $regex: regexTelefone },
            }).limit(10); // Limitar a 10 resultados

            if (clientes.length > 0) {
                return res.status(200).json({ clientes });
            } else {
                return res.status(204).json({ msg: "Cliente não encontrado!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Vendedor pelo Id Sq
app.post("/get-vendedor/:id", checkToken, async (req, res) => {
    const { id_vendedor } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            const vendedor = await Vendedor.findOne({ id_vendedor: id_vendedor });
            //const empresaRev = await Revenda.findOne({"id_vendedor":id_vendedor})
            if (vendedor) {
                return res.status(200).json({ vendedor });
            } else {
                return res.status(404).json({ msg: "Vendedor não encontrado!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Post para obter Orcamento pelo Id Sq
app.post("/get-orcamento/:id", checkToken, async (req, res) => {
    const { id_orcamento } = req.body;
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userExist = await User.findById(id);
        if (userExist) {
            const orcamento = await Orcamento.findOne({ id_orcamento: id_orcamento });

            if (orcamento) {
                return res.status(200).json({ orcamento });
            } else {
                return res.status(404).json({ msg: "Orçamento não encontrado!" });
            }
        } else {
            return res.status(404).json({ msg: "Id de requisição não encontrado!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Get Empresa por Vinculo
app.get("/vinculo-empresa/:id", checkToken, async (req, res) => {
    const id = req.params.id;
    //const {vinculo_empresa} = req.body

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userVinculoEmpresa = await User.findById(id, "vinculo_empresa");
        let vinculo_empresa = "";

        if (userVinculoEmpresa) {
            vinculo_empresa = userVinculoEmpresa.vinculo_empresa;
        } else {
            return res
                .status(422)
                .json({ msg: "Vínculo do usuário não encontrado!" });
        }

        const vinculo = await Empresa.findOne({ cnpj: vinculo_empresa });
        //const vinculo_revenda = await Revenda.findOne({"id_empresa":vinculo_empresa})

        //Retorna Empresa Caso exista
        if (vinculo) {
            return res.status(200).json({ vinculo });
        }
        // // Agora Procura Revenda
        // if (vinculo_revenda){
        //     console.log("Chegou aqui REV", vinculo_revenda)
        //     return res.status(200).json({vinculo_revenda})
        // }
        if (!vinculo) {
            return res.status(422).json({ msg: "Nenhuma empresa encontrada!" });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// List Indústrias
app.get("/list-empresas/:id", checkToken, async (req, res) => {
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        //const userRole = await User.findById(id, 'role')
        //const role = userRole.role
        const userRole = await User.findById(id, "role");
        let role = "";
        if (userRole) {
            role = userRole.role;
        } else {
            return res
                .status(404)
                .json({ msg: "Permissão do usuário não encontrada!" });
        }

        //listar empresas
        if (role == "Admin") {
            const empresa = await Empresa.find({
                inativo: false,
                type: "Empresa",
                createdBy: id,
            });

            return res.status(200).json({ empresa });
        }

        if (!role) {
            return res.status(401).json({ msg: "Sem permissão!", role });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// List Revendas
app.get("/list-revendas/:id", checkToken, async (req, res) => {
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userRole = await User.findById(id, "role");
        let role = "";
        if (userRole) {
            role = userRole.role;
        } else {
            return res
                .status(404)
                .json({ msg: "Permissão do usuário não encontrada!" });
        }

        //listar revendas
        if (role == "empresa") {
            const revenda = await Empresa.find({
                inativo: false,
                type: "Revenda",
                createdBy: id,
            });

            return res.status(200).json({ revenda });
        }

        if (!role) {
            return res.status(401).json({ msg: "Sem permissão!", role });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// List Clientes
app.get("/list-clientes/:id", checkToken, async (req, res) => {
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userRole = await User.findById(id, "role vinculo_empresa");
        let role = "";
        let vinculo_empresa = 0;
        if (userRole) {
            role = userRole.role;
            vinculo_empresa = userRole.vinculo_empresa;
        } else {
            return res
                .status(404)
                .json({ msg: "Permissão do usuário não encontrada!" });
        }

        //listar clientes
        if (role == "revenda") {
            const cliente = await Cliente.find({
                inativo: false,
                vinculo_empresa: vinculo_empresa,
            });

            return res.status(200).json({ cliente });
        }

        if (!role) {
            return res.status(401).json({ msg: "Sem permissão!", role });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Rota para listar respostas da empresa
app.get("/company-responses/:companyId", checkToken, async (req, res) => {
    const companyId = req.params.companyId;

    // Verifica se o companyId é um ObjectId válido do MongoDB
    if (!companyId.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }

    try {
        // Busca as respostas associadas ao companyId na coleção CompanyResponses
        const companyResponses = await CompanyResponses.findOne({ companyId });
        if (!companyResponses) {
            return res
                .status(404)
                .json({ msg: "Respostas não encontradas para esta empresa!" });
        }

        // Retorna as respostas da empresa
        return res.status(200).json({ responses: companyResponses.responses });
    } catch (error) {
        console.error("Erro ao buscar respostas da empresa:", error);
        return res
            .status(500)
            .json({ msg: "Erro ao buscar respostas da empresa!" });
    }
});

// List Vendedores
app.get("/list-vendedores/:id", checkToken, async (req, res) => {
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userRole = await User.findById(id, "role vinculo_empresa");
        let role = "";
        let vinculo_empresa = "";
        if (userRole) {
            role = userRole.role;
            vinculo_empresa = userRole.vinculo_empresa;
        } else {
            return res
                .status(404)
                .json({ msg: "Permissão do usuário não encontrada!" });
        }

        //listar clientes
        if (role == "revenda") {
            const vendedor = await Vendedor.find({
                inativo: false,
                vinculo_empresa: vinculo_empresa,
            });

            return res.status(200).json({ vendedor });
        }

        if (!role) {
            return res.status(401).json({ msg: "Sem permissão!", role });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// List Orçamentos
app.get("/list-orcamentos/:id", checkToken, async (req, res) => {
    const id = req.params.id;

    if (id.match(/^[0-9a-fA-F]{24}$/)) {
        const userRole = await User.findById(id, "role vinculo_empresa");
        let role = "";
        let vinculo_empresa = "";
        if (userRole) {
            role = userRole.role;
            vinculo_empresa = userRole.vinculo_empresa;
        } else {
            return res
                .status(404)
                .json({ msg: "Permissão do usuário não encontrada!" });
        }

        //listar clientes
        if (role == "revenda") {
            const orcamento = await Orcamento.find({
                inativo: false,
                vinculo_empresa: vinculo_empresa,
            });

            return res.status(200).json({ orcamento });
        }

        if (!role) {
            return res.status(401).json({ msg: "Sem permissão!", role });
        }
    } else {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }
});

// Requisição para obtenção de bairros
app.get("/carregar-bairros", async (req, res) => {
    try {
        // Pegando os parâmetros 'city' e 'uf' da query string
        const { city, uf } = req.query;

        // Requisição para obter o código IBGE
        const ibgeResponse = await fetch(
            `https://servicodados.ibge.gov.br/api/v1/localidades/municipios?nome=${city}`
        );
        const ibgeData = await ibgeResponse.json();

        // Filtra para garantir que o município pertence ao estado (uf) correto e corresponde ao nome da cidade
        const filteredData = ibgeData.filter(
            (municipio) =>
                municipio.nome.toLowerCase() === city.toLowerCase() &&
                municipio.microrregiao.mesorregiao.UF.sigla === uf
        );

        if (filteredData.length > 0) {
            const ibgeCode = filteredData[0].id;
            console.log(`Código IBGE de ${city} - ${uf}:`, ibgeCode);

            // Requisição para obter os bairros usando o código IBGE
            const options = {
                method: "GET",
                url: `https://api.brasilaberto.com/v1/districts-by-ibge-code/${ibgeCode}`, // Goiânia = 5208707
                headers: {
                    Authorization: `Bearer ${process.env.API_KEY_BRASIL_ABERTO}`,
                },
            };

            const response = await axios.request(options);
            return res.json(response.data.result);
        } else {
            console.log("Cidade não encontrada no IBGE");
            return res.status(404).json({ message: "Cidade não encontrada no IBGE" });
        }
    } catch (error) {
        console.error("Erro ao buscar bairros:", error);
        return res.status(500).json({ message: "Erro ao buscar bairros", error });
    }
});

// Calcular distância entre CEPs usando Google Maps API
app.get("/calcular-distancia", async (req, res) => {
    const { latA, lngA, latB, lngB } = req.query;

    // Converte as coordenadas para números
    const coordenadasA = { lat: parseFloat(latA), lng: parseFloat(lngA) };
    const coordenadasB = { lat: parseFloat(latB), lng: parseFloat(lngB) };

    try {
        console.log("coordenadasA, coordenadasB:", coordenadasA, coordenadasB);
        const distance = await calcularDistanciaEntreCoordenadas(
            coordenadasA,
            coordenadasB
        );
        if (distance !== null) {
            return res.json({ distance });
        } else {
            return res
                .status(404)
                .json({ error: "Não foi possível calcular a distância" });
        }
    } catch (error) {
        console.error(error);
        return res.status(500).json({ error: "Erro ao calcular a distância" });
    }
});
/*async function obterCoordenadas(cep) {
    try {
        const response = await axios.get(`https://maps.googleapis.com/maps/api/geocode/json?address=${cep},BR&key=${process.env.GOOGLE_MAPS_API_KEY}`);
        if (response.data.results.length > 0 && response.data.results[0].geometry) {
            const { lat, lng } = response.data.results[0].geometry.location;
            return { lat, lng };
        } else {
            // Trata o caso em que não há resultados para o CEP fornecido
            console.error(`Nenhum resultado encontrado para o CEP: ${cep}`);
            return null;
        }
    } catch (error) {
        console.error(`Erro ao obter coordenadas para o CEP: ${cep}`, error);
        throw error;
    }
}*/
function calcularDistanciaEmLinhaReta(lat1, lon1, lat2, lon2) {
    function paraRadianos(graus) {
        return graus * (Math.PI / 180);
    }

    const raioTerraKm = 6371;
    const dLat = paraRadianos(lat2 - lat1);
    const dLon = paraRadianos(lon2 - lon1);

    lat1 = paraRadianos(lat1);
    lat2 = paraRadianos(lat2);

    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return raioTerraKm * c;
}
async function calcularDistanciaEntreCoordenadas(coordenadasA, coordenadasB) {
    //const coordenadasA = await obterCoordenadas(cepA);
    //const coordenadasB = await obterCoordenadas(cepB);

    if (coordenadasA && coordenadasB) {
        const distancia = calcularDistanciaEmLinhaReta(
            coordenadasA.lat,
            coordenadasA.lng,
            coordenadasB.lat,
            coordenadasB.lng
        );
        console.log(
            `Distância em linha reta entre A e B: ${distancia.toFixed(2)} km`
        );
        return distancia.toFixed(2);
    } else {
        console.error(
            "Não foi possível calcular a distância devido à falta de coordenadas."
        );
        return null;
    }
}
/*async function calcularDistanciaEntreCEPs(cepA, cepB) {
    const coordenadasA = await obterCoordenadas(cepA);
    const coordenadasB = await obterCoordenadas(cepB);

    if (coordenadasA && coordenadasB) {
        const distancia = calcularDistanciaEmLinhaReta(coordenadasA.lat, coordenadasA.lng, coordenadasB.lat, coordenadasB.lng);
        console.log(`Distância em linha reta entre ${cepA} e ${cepB}: ${distancia.toFixed(2)} km`);
        return distancia.toFixed(2);
    } else {
        console.error("Não foi possível calcular a distância devido à falta de coordenadas.");
        return null;
    }
}*/

// Exemplo de uso
//calcularDistanciaEntreCEPs('CEP_AQUI', 'OUTRO_CEP_AQUI');

// Requisição para listar cliente Asaas ou criar caso não exista
/*app.post("/retorna-cliente-asaas", async (req, res) => {
    const { nome_cliente, cpf_cnpj, email_cliente, phone, addressNumber, postalCode } = req.body;

    const baseUrl = process.env.PROD_URL_ASAAS;
    const url = baseUrl+`/customers?cpfCnpj=${cpf_cnpj}`;
    const options = {
    method: 'GET',
    headers: {
        accept: 'application/json',
        access_token: process.env.API_KEY_ASAAS
    }
    };
    fetch(url, options)
    .then(res => res.json())
    .then(json => {
        //console.log(json)
        if(json.totalCount>0){
            return res.status(200).json(json.data[0]);
        }else{  
            const url = baseUrl+`/customers`;
            const options = {
                method: 'POST',
                headers: {
                    accept: 'application/json',
                    'Content-Type': 'application/json',
                    access_token: process.env.API_KEY_ASAAS
                },
                body: JSON.stringify({name: nome_cliente, cpfCnpj: cpf_cnpj, email: email_cliente, phone: phone, addressNumber: addressNumber, postalCode: postalCode})
            }; 
            fetch(url, options)
            .then(res => res.json())
            .then(json => {
                //console.log(json)
                return res.status(200).json(json);
            })
            .catch(err => {
                return res.status(400).json('error:' + err)
            });
        }
    })
    .catch(err => {
        return res.status(400).json('error:' + err)
    });
}*/

// Endpoint para criar assinauturas de boleto e pix
app.post("/criar-assinatura-asaas-bolpix", async (req, res) => {
    const { customerID, payment_type, cycle, value, nextDueDate } = req.body;

    const baseUrl = process.env.PROD_URL_ASAAS;
    const url = `${baseUrl}/subscriptions`;
    const options = {
        method: "POST",
        headers: {
            accept: "application/json",
            "content-type": "application/json",
            access_token: process.env.API_KEY_ASAAS,
        },
        body: JSON.stringify({
            billingType: payment_type,
            cycle: cycle,
            customer: customerID,
            value: value,
            nextDueDate: nextDueDate,
        }),
    };

    try {
        const response = await fetch(url, options);
        const data = await response.json();
        return res.status(200).json(data);
    } catch (error) {
        console.error("error:", error);
        return res.status(500).json({ error: "Ocorreu um erro no servidor." });
    }
});

// Requisição para criar assinatura Asaas com cartão de crédito
app.post("/criar-assinatura-asaas-cartao", async (req, res) => {
    const {
        customerID,
        nome_cliente,
        cpf_cnpj,
        cycle,
        numero_cartao,
        expiryMonth,
        expiryYear,
        ccv,
        email,
        postalCode,
        addressNumber,
        phone,
        value,
        nextDueDate,
        remoteIp,
    } = req.body;

    const baseUrl = process.env.PROD_URL_ASAAS;
    const url = baseUrl + "/subscriptions/";
    //const url = 'https://sandbox.asaas.com/api/v3/subscriptions/';
    const options = {
        method: "POST",
        headers: {
            accept: "application/json",
            "content-type": "application/json",
            access_token: process.env.API_KEY_ASAAS,
        },
        body: JSON.stringify({
            billingType: "CREDIT_CARD",
            cycle: cycle,
            creditCard: {
                holderName: nome_cliente,
                number: numero_cartao,
                expiryMonth: expiryMonth,
                expiryYear: expiryYear,
                ccv: ccv,
            },
            creditCardHolderInfo: {
                name: nome_cliente,
                email: email,
                cpfCnpj: cpf_cnpj,
                postalCode: postalCode,
                addressNumber: addressNumber,
                phone: phone,
            },
            customer: customerID,
            value: value,
            nextDueDate: nextDueDate,
            remoteIp: remoteIp,
        }),
    };

    fetch(url, options)
        .then((res) => res.json())
        .then((json) => {
            console.log(json);
            return res.status(200).json(json);
        })
        .catch((err) => {
            console.error("error:" + err);
            return res.status(400).json("error:" + err);
        });
});

// Requisição para criar cobranca Asaas com cartão de crédito
app.post("/criar-cobranca-asaas-cartao", async (req, res) => {
    const {
        customerID,
        nome_cliente,
        cpf_cnpj,
        numero_cartao,
        expiryMonth,
        expiryYear,
        ccv,
        email,
        postalCode,
        addressNumber,
        phone,
        value,
        nextDueDate,
        remoteIp,
        parcela,
    } = req.body;
    const valorParcela = value / parcela;

    const baseUrl = process.env.PROD_URL_ASAAS;
    const url = baseUrl + "/payments/";
    const options = {
        method: "POST",
        headers: {
            accept: "application/json",
            "content-type": "application/json",
            access_token: process.env.API_KEY_ASAAS,
        },
        body: JSON.stringify({
            billingType: "CREDIT_CARD",
            creditCard: {
                holderName: nome_cliente,
                number: numero_cartao,
                expiryYear: expiryYear,
                expiryMonth: expiryMonth,
                ccv: ccv,
            },
            creditCardHolderInfo: {
                name: nome_cliente,
                email: email,
                cpfCnpj: cpf_cnpj,
                postalCode: postalCode,
                addressNumber: addressNumber,
                phone: phone,
            },
            customer: customerID,
            value: value,
            dueDate: nextDueDate,
            installmentCount: parcela,
            installmentValue: valorParcela,
            remoteIp: remoteIp,
        }),
    };

    fetch(url, options)
        .then((res) => res.json())
        .then((json) => {
            console.log(json);
            return res.status(200).json(json);
        })
        .catch((err) => {
            console.error("error:" + err);
            return res.status(400).json("error:" + err);
        });
});



const allowedIPs = [
    "************",
    "************",
    "*************",
    "*************",
    "*************",
    "************",
    //SANDBOX
    "*************",
    "*************",
];
// Endpoint do webhook para receber eventos do Asaas
app.post('/webhook-asaas', async (req, res) => {
    try {
        // Obter o IP do remetente
        const forwardedFor = req.headers['x-forwarded-for'];
        let senderIP = forwardedFor
            ? forwardedFor.split(",")[0].trim() // Pega o primeiro IP da lista
            : req.connection.remoteAddress;

        // Normalizar o IP removendo "::ffff:" (IPv4 mapeado para IPv6)
        if (senderIP.startsWith("::ffff:")) {
            senderIP = senderIP.substring(7);
        }

        console.log(`IP do remetente: ${senderIP}`);

        // Verificar se o IP é permitido
        if (!allowedIPs.includes(senderIP)) {
            console.warn(`IP não autorizado: ${senderIP}`);
            return res.status(403).json({ msg: "Acesso não autorizado." });
        }

        console.log('Evento recebido:', req.body);

        const data = req.body;
        const event = data.event;
        console.log("event:", event);
        console.log("Data received:", data);

        // Roteamento interno para lidar com diferentes tipos de eventos
        switch (event) {
            case 'SUBSCRIPTION_DELETED': {
                try {
                    const result = await handleSubscriptionDeleted(data);
                    return res.status(200).json({ msg: result.message });
                } catch (error) {
                    console.error("Erro ao processar SUBSCRIPTION_DELETED:", error);
                    return res.status(200).json({ msg: "Erro ao processar o evento SUBSCRIPTION_DELETED." });
                }
            }

            case 'PAYMENT_OVERDUE': {
                try {
                    const result = await handlePaymentOverdue(data);
                    return res.status(200).json({ msg: result.message });
                } catch (error) {
                    console.error("Erro ao processar PAYMENT_OVERDUE:", error);
                    return res.status(200).json({ msg: "Erro ao processar o evento PAYMENT_OVERDUE." });
                }
            }

            case 'PAYMENT_RECEIVED': {
                try {
                    const result = await handlePaymentReceived(data);
                    return res.status(200).json({ msg: result.message });
                } catch (error) {
                    console.error("Erro ao processar PAYMENT_RECEIVED:", error);
                    return res.status(200).json({ msg: "Erro ao processar o evento PAYMENT_RECEIVED." });
                }
            }

            default: {
                console.warn(`Evento não suportado: ${event}`);
                return res.status(200).json({ msg: "Evento recebido com sucesso, mas não processado." });
            }
        }
    } catch (error) {
        console.error('Erro ao processar evento do webhook:', error);
        return res.status(500).json({
            msg: 'Erro ao processar evento do webhook.',
            error: error.message,
        });
    }
});

// Endpoint do webhook para receber eventos da Iugu
app.post("/webhook-iugu", async (req, res) => {
    const event = req.body.event; // O evento que foi disparado
    const data = req.body.data; // Dados da assinatura/fatura

    console.log(`Evento recebido: ${event}`);

    switch (event) {
        case "subscription.created":
            // Trate quando uma assinatura for criada
            console.log(`Assinatura criada: ${data.id}`);
            break;

        case "subscription.suspended":
            // Trate quando uma assinatura for suspensa
            console.log(`Assinatura suspensa: ${data.id}`);
            break;

        case "subscription.activated":
            // Trate quando uma assinatura for ativada
            console.log(`Assinatura ativada: ${data.id}`);
            break;

        case "subscription.canceled":
            // Trate quando uma assinatura for cancelada
            console.log(`Assinatura cancelada: ${data.id}`);
            break;

        case "subscription.expired":
            try {
                console.log(`Assinatura expirada: ${data.id}`);

                // Extrai o ID da assinatura expirada
                const assinaturaId = data.id;

                // Busca os dados atualizados da assinatura na API da Iugu
                const { result: assinaturaData } = await getSubscriptionByID(assinaturaId);

                console.log("Dados da assinatura obtidos da Iugu:", assinaturaData);

                // Substitui completamente a assinatura no banco de dados
                const updatedAssinatura = await Assinatura.findOneAndUpdate(
                    { "assinatura_obj.id": assinaturaId }, // Busca pela assinatura existente no banco
                    { $set: { assinatura_obj: assinaturaData } }, // Substitui o campo assinatura_obj
                    { new: true, upsert: true } // Cria a assinatura se ela não existir
                );

                console.log("Assinatura atualizada no banco de dados:", updatedAssinatura);

                return res.status(200).send("Evento processado com sucesso");
            } catch (error) {
                console.error("Erro ao atualizar a assinatura expirada:", error);
                return res.status(500).send("Erro ao processar o evento");
            }
            break;

        case "invoice.created":
            try {
                const invoiceId = data.id;
                const assinaturaId = data.subscription_id;

                console.log("ID da nova fatura:", invoiceId);
                console.log("ID da assinatura:", assinaturaId);

                // Busca os dados completos da assinatura na Iugu
                const { result: assinaturaData } = await getSubscriptionByID(assinaturaId);

                console.log("Dados da assinatura obtidos da Iugu:", assinaturaData);

                // Atualiza os dados da assinatura no banco de dados
                const updatedAssinatura = await Assinatura.findOneAndUpdate(
                    { "assinatura_obj.id": assinaturaId }, // Busca pela assinatura existente
                    { $set: { assinatura_obj: assinaturaData } }, // Substitui completamente o campo assinatura_obj
                    { new: true, upsert: true } // Cria a assinatura se ela não existir
                );

                console.log("Assinatura atualizada no banco de dados:", updatedAssinatura);
                return res.status(200).send("Evento processado com sucesso");
            } catch (error) {
                console.error("Erro ao processar a criação da fatura:", error);
                return res.status(500).send("Erro ao processar o evento");
            }
            break;

        case "invoice.status_changed":
            try {
                // Extrai o ID da fatura, o novo status e o ID da assinatura
                const invoiceId = data.id;
                const newStatus = data.status;
                const assinaturaId = data.subscription_id;

                console.log("ID da fatura:", invoiceId);
                console.log("Novo status da fatura:", newStatus);
                console.log("ID da assinatura:", assinaturaId);

                // Busca os dados atualizados da assinatura na API da Iugu
                const { result: assinaturaData } = await getSubscriptionByID(assinaturaId);

                console.log("Dados da assinatura obtidos da Iugu:", assinaturaData);

                // Substitui completamente a assinatura no banco de dados
                const updatedAssinatura = await Assinatura.findOneAndUpdate(
                    { "assinatura_obj.id": assinaturaId }, // Busca pela assinatura existente no banco
                    { $set: { assinatura_obj: assinaturaData } }, // Substitui o campo assinatura_obj
                    { new: true, upsert: true } // Cria a assinatura se ela não existir
                );

                console.log("Assinatura atualizada no banco de dados:", updatedAssinatura);

                return res.status(200).send("Evento processado com sucesso");
            } catch (error) {
                console.error("Erro ao atualizar o status da assinatura:", error);
                return res.status(500).send("Erro ao processar o evento");
            }
            break;

        default:
            console.log(`Evento desconhecido: ${event}`);
    }

    // Retorna uma resposta 200 para a Iugu
    res.status(200).send("Webhook recebido com sucesso");
});

/*app.post("/lista-subscription-asaas", async (req, res) => {
    const { empresa_id, customer_id } = req.body;
    const assinatura = await Assinatura.findOne({ empresa: empresa_id });

    console.log("assinatura>>", assinatura);
    const assinatura_obj = assinatura?.assinatura_obj;

    return res.status(200).json({ assinatura_obj });
});*/

app.post("/getData-customer-asaas", async (req, res) => {
    const { customer_id } = req.body;

    try {
        // Chama a função assíncrona para obter os dados do cliente
        console.log("Tem customer_id?", customer_id)
        const response = await getCustomerDataAsaas(customer_id);

        // Envia a resposta de volta ao cliente com os dados recebidos
        res.status(200).json(response); // Envia a resposta de sucesso
    } catch (error) {
        // Caso ocorra algum erro na requisição ou na função, retorna um erro
        res.status(500).json({ msg: "Erro ao obter os dados do cliente", error: error.message });
    }
});

app.post("/getInvoice-iugu", async (req, res) => {
    const { invoiceId } = req.body;

    try {
        // Chama a função assíncrona para obter os dados do cliente
        const response = await getInvoiceByID(invoiceId);

        // Envia a resposta de volta ao cliente com os dados recebidos
        res.status(200).json(response); // Envia a resposta de sucesso
    } catch (error) {
        // Caso ocorra algum erro na requisição ou na função, retorna um erro
        res.status(500).json({ msg: "Erro ao obter os dados do cliente", error: error.message });
    }
});

app.post("/lista-subscription-asaas", async (req, res) => {
    const { customer_id } = req.body;

    const baseUrl = process.env.PROD_URL_ASAAS;
    const url = `${baseUrl}/subscriptions?customer=${customer_id}`;
    const options = {
        method: "GET",
        headers: {
            accept: "application/json",
            access_token: process.env.API_KEY_ASAAS,
        },
    };

    try {
        // Primeira requisição
        const subscriptionResponse = await fetch(url, options);
        const subscriptionData = await subscriptionResponse.json();

        if (subscriptionData.totalCount > 0) {
            const subscriptionId = subscriptionData.data[0].id;

            // Segunda requisição
            const paymentUrl = `${baseUrl}/subscriptions/${subscriptionId}/payments`;
            const paymentOptions = {
                method: "GET",
                headers: {
                    accept: "application/json",
                    access_token: process.env.API_KEY_ASAAS,
                },
            };

            const paymentResponse = await fetch(paymentUrl, paymentOptions);
            const paymentData = await paymentResponse.json();

            // Enviar a resposta final para o frontend
            return res.status(200).json({ subscriptionData, paymentData });
        } else {
            return res
                .status(204)
                .json({ msg: "Nenhuma assinatura encontrada para o cliente." });
        }
    } catch (err) {
        console.error("error:", err);
        return res.status(500).json({ error: "Ocorreu um erro no servidor." });
    }
});

// Requisição para criar cobranca Asaas com cartão de crédito
app.post("/criar-cobranca-asaas", async (req, res) => {
    const { customerID, payment_type, value, nextDueDate } = req.body;
    console.log(customerID, payment_type, value, nextDueDate);
    const baseUrl = process.env.PROD_URL_ASAAS;
    const url = baseUrl + "/payments/";
    const options = {
        method: "POST",
        headers: {
            accept: "application/json",
            "content-type": "application/json",
            access_token: process.env.API_KEY_ASAAS,
        },
        body: JSON.stringify({
            billingType: payment_type,
            customer: customerID,
            value: value,
            dueDate: nextDueDate,
        }),
    };

    fetch(url, options)
        .then((res) => res.json())
        .then((json) => {
            console.log(json);
            return res.status(200).json(json);
        })
        .catch((err) => {
            console.error("error:" + err);
            return res.status(400).json("error:" + err);
        });
});

// Rota para alterar o status de fechamento temporário
app.post("/changeStatusLoja", async (req, res) => {
    const { id, status_loja } = req.body;

    if (typeof status_loja !== "boolean") {
        return res
            .status(400)
            .json({ error: "Status da loja deve ser um booleano." });
    }

    try {
        // Encontrar a empresa pelo ID e atualizar o campo fechamento_temporario
        const empresa = await Empresa.findByIdAndUpdate(
            id,
            { fechamento_temporario: status_loja },
            { new: true, runValidators: true }
        );

        if (!empresa) {
            return res.status(404).json({ error: "Empresa não encontrada." });
        }

        res.status(200).json(empresa);
    } catch (error) {
        console.error("Erro ao atualizar status de fechamento:", error);
        res.status(500).json({ error: "Erro interno do servidor." });
    }
});

// Post do Python para envio das informações do cardapio do cliente para o mongoDB
/*app.post("/record-cardapio", async (req, res) => {
    if (!req.body) {
        return res.send({
            sucess: false,
            message: "Nenhum dado foi enviado",
        });
    }

    let data = req.body;

    try {
        const id_empresa = data.id_empresa;

        async function getSeqIdAndCreateCategories(i) {
            // Verificar SeqID e atribuir ao id_categoria
            let seqId = await Counter.findOneAndUpdate(
                { id: `empresa:${id_empresa};id_categoria` },
                { $inc: { seq: 1 } },
                { new: true }
            );

            if (!seqId) {
                const newVal = new Counter({ id: `empresa:${id_empresa};id_categoria`, seq: 1 });
                await newVal.save();
                seqId = { seq: 1 };
            }

            // Encontrar empresa e atribuir empresaObjId a Categoria
            const empresa = await Empresa.findOne({ id_empresa });
            if (empresa) {
                const categoria = new Categorias({
                    empresaObjId: empresa._id,
                    id_categoria: seqId.seq,
                    createdBy: "integracao",
                    id_empresa,
                    title: data.categorias[i].categoria,
                    order: i,
                    disponibilidade: "sempre",
                    import_id: data.categorias[i].import_id,
                    inativo: false,
                    createdAt: moment().format(),
                });

                await categoria.save();
                console.log(`Categoria ${i + 1} cadastrada com sucesso!`);

                async function getCategoryId(importId) {
                    const category = await Categorias.findOne({
                        "import_id": importId,
                        "id_empresa": id_empresa,
                    });
                    return category._id;
                }

                const innerFor = async (items, iteratorItem) => {
                    for (const item of items) {
                        const category_import_id = item.import_category_id;
                        const categoryId = await getCategoryId(category_import_id);
                        // Verificar SeqID e atribuir ao id_item
                        let seqId = await Counter.findOneAndUpdate(
                            { id: `empresa:${id_empresa};id_item` },
                            { $inc: { seq: 1 } },
                            { new: true }
                        );

                        //gravando a imagem no bucket do google cloud
                        var imageUrl;
                        if (item.image != "") {
                            const imageBase64 = "data:image/png;base64," + item.image;
                            // gerar um id randomico para a imagem
                            const imageId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                            imageUrl = await uploadImage(imageBase64, 'img_' + imageId + '.png');
                        } else {
                            imageUrl = null;
                        }

                        if (!seqId) {
                            const newVal = new Counter({ id: `empresa:${id_empresa};id_item`, seq: 1 });
                            await newVal.save();
                            seqId = { seq: 1 };
                        }
                        //console.log(item.image);
                        const itemToSave = new Itens({
                            empresaObjId: empresa._id,
                            id_item: seqId.seq,
                            createdBy: "integracao",
                            id_empresa,
                            category_item_id: categoryId,
                            category_item_title: data.categorias[i].categoria,
                            title: item.title,
                            order: iteratorItem + 1,
                            description: item.description,
                            out: false,
                            image: imageUrl ? imageUrl : "",
                            price: item.price,
                            disponibilidade: "sempre",
                            inativo: false,
                            createdAt: moment().format(),
                        });

                        await itemToSave.save();
                        console.log(`Item ${iteratorItem + 1} cadastrado com sucesso!`);
                    }
                };


                await innerFor(data.categorias[i].itens, i);
            } else {
                console.log(`Empresa não encontrada para category ${i + 1} `);
            }
        }

        for (let i = 0; i < data.categorias.length; i++) {
            await getSeqIdAndCreateCategories(i);
        }

    } catch (error) {
        console.log(`Error saving category:`, error);
    }

    return res.send({
            success: true,
        message: "Cardápio salvo com sucesso",
    });

});*/

const funcaoCopiaCardapio = async (objIdEmpresaReferencia, objIdEmpresaNovoCardapio) => {
    try {
        // Buscar ID da empresa de destino
        const empresaNova = await Empresa.findById(objIdEmpresaNovoCardapio);
        if (!empresaNova) {
            throw new Error('Empresa de destino não encontrada');
        }
        const idEmpresaNovo = empresaNova.id_empresa;

        // Copiar Categorias
        const categorias = await Categorias.find({ empresaObjId: objIdEmpresaReferencia });
        const categoriasMap = new Map();

        for (const categoria of categorias) {
            const novaCategoria = new Categorias({
                ...categoria.toObject(),
                _id: new mongoose.Types.ObjectId(),
                empresaObjId: objIdEmpresaNovoCardapio,
                id_empresa: idEmpresaNovo,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
            await novaCategoria.save();
            categoriasMap.set(categoria._id.toString(), novaCategoria._id.toString());
        }

        // Copiar Itens
        const itens = await Itens.find({ empresaObjId: objIdEmpresaReferencia });
        const itensMap = new Map();

        for (const item of itens) {
            const novoItem = new Itens({
                ...item.toObject(),
                _id: new mongoose.Types.ObjectId(),
                empresaObjId: objIdEmpresaNovoCardapio,
                id_empresa: idEmpresaNovo,
                category_item_id: categoriasMap.get(item.category_item_id) || null,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
            await novoItem.save();
            itensMap.set(item._id.toString(), novoItem._id.toString());
        }

        // Copiar Adicionais
        const adicionais = await Adicionais.find({ empresaObjId: objIdEmpresaReferencia });
        for (const adicional of adicionais) {
            const novoAdicional = new Adicionais({
                ...adicional.toObject(),
                _id: new mongoose.Types.ObjectId(),
                empresaObjId: objIdEmpresaNovoCardapio,
                id_empresa: idEmpresaNovo,
                item_id: itensMap.get(adicional.item_id) || null,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
            await novoAdicional.save();
        }

        console.log('Cardápio copiado com sucesso!');
    } catch (error) {
        console.error('Erro ao copiar cardápio:', error);
    }
};

//funcaoCopiaCardapio("66e3579592418835dca9c42f","67a125efc404617d42f32226")

//populateCompanyResponses("66e9ae2eaf110fd93d07b73c");

async function populateCompanyResponses(companyId) {
    // Lista de perguntas e respostas que serão inseridas
    const responses = [
        {
            questionIdentifier: "info_pedido",
            questionType: "Informação sobre pedido",
            question: "Quero fazer pedido",
            response:
                "Não deixe para depois! Utilize o link abaixo para realizar o seu pedido. 👇 \n{link}",
        },
        {
            questionIdentifier: "resposta_pergunta_cardapio",
            questionType:
                "O que você deseja responder quando o cliente pergunta sobre o cardápio?",
            question: "Me manda o cardápio",
            response:
                "Você pode dar uma olhada em nosso cardápio entrando no link aqui abaixo 👇 \n{link}",
        },
        {
            questionIdentifier: "resposta_ola",
            questionType: "Mensagem de resposta a olá, oi",
            question: "Olá",
            response:
                "Bem-vindo(a) ao nosso autoatendimento! 😃\nComo posso ajudar você?",
        },
        {
            questionIdentifier: "resposta_agradecimento",
            questionType: "Mensagem de resposta a para agradecimento",
            question: "Obrigado",
            response: "Imagina! 😃",
        },
        {
            questionIdentifier: "info_cliente_sobre_emprego",
            questionType: "Informação para o cliente que questionar sobre emprego",
            question: "Tem vaga de emprego?",
            response:
                "Para saber mais sobre vagas de emprego em nosso estabelecimento, entre em contato com a gente pelo(s) telefone(s). 💼",
        },
        {
            questionIdentifier: "msg_cliente_ajuda",
            questionType: "Mensagem que será enviada sempre que cliente quiser ajuda",
            question: "Não to conseguindo pedir",
            response:
                "Posso te ajudar *nestas situações*:\n\n*-* Pedidos\n*-* Cardápio\n*-* Horários de funcionamento\n*-* Tempo/Taxa de entrega\n*-* Nosso endereço\n*-* Chamar atendente\n\nÉ só *digitar uma destas opções* por aqui mesmo! 😄",
        },
        {
            questionIdentifier: "msg_cliente_falar_atendente",
            questionType: "Mensagem quando cliente desejar falar com um atendente",
            question: "Quero falar com uma pessoa",
            response:
                "Para agilizar o seu atendimento, preciso que você *me envie um resumo* do que precisa! ⬇️",
        },
        {
            questionIdentifier: "msg_saudacao",
            questionType: "Mensagem de saudação",
            question: "Bom dia, boa noite",
            response: "Olá, como posso te ajudar?",
        },
        {
            questionIdentifier: "resposta_comentario_feedback",
            questionType: "Mensagem de resposta a um comentário ou feedback",
            question: "Tava muito bom o lanche",
            response:
                "Fico feliz com o seu feedback! Sua opinião é muito importante. 😄",
        },
        {
            questionIdentifier: "info_cliente_sobre_reserva",
            questionType: "Informação para o cliente que questionar sobre reserva",
            question: "Aceitam reserva?",
            response:
                "Não trabalhamos com reservas.\nAtendimento somente por ordem de chegada.",
        },
        {
            questionIdentifier: "msg_padrao_promocao",
            questionType: "Mensagem padrão de promoção",
            question: "Tem promoção?",
            response: "As *promoções* de hoje são ⬇️",
        },
        {
            questionIdentifier: "msg_cliente_despedida",
            questionType: "Mensagem quando cliente se despedir",
            question: "Tchau",
            response: "Até mais! 😁",
        },
        {
            questionIdentifier: "msg_cancelar_ultimo_pedido",
            questionType:
                "Mensagem questionando se cliente deseja cancelar ultimo pedido",
            question: "Quero cancelar",
            response:
                "Verifiquei no nosso sistema e parece que você não possui pedidos que possam ser cancelados.\n{divide} Se você acredita que precisa de *ajuda*, entre em contato conosco.\n\n📞 (62) 9 9000-0000",
        },
        {
            questionIdentifier: "info_robo_nao_entender",
            questionType: "Informação para o cliente quando robô não entender",
            question: "Qu prdido!",
            response:
                "Desculpe, não consegui entender o que você deseja! 😕\nMe Informe com poucas palavras o que você deseja.",
        },
        {
            questionIdentifier: "msg_endereco_telefone",
            questionType:
                "Mensagem para envio sempre que entender sobre endereço ou telefone",
            question: "Tem endereço?",
            response:
                "Um momento, vou te encaminhar nosso(s) *endereço(s)* e *telefone(s)*... 📁\n{divide} Rua Exemplo, 00, Bairro, Cidade - UF, CEP \n\nOu, veja pelo Maps! 🗺️👇\nhttps://www.google.com/maps?\n\n*Contato*\n📱 (62) 9 9000-0000",
        },
        {
            questionIdentifier: "msg_localizacao_manual_ativa",
            questionType: "Mensagem para quando localização manual está ativa",
            question: "Tem entrega?",
            response:
                "Entregas a partir das **:**h\n\n\n",
        },
        {
            questionIdentifier: "msg_cliente_audio",
            questionType: "Mensagem quando cliente enviar audio",
            question: "Audio 🔊",
            response:
                "Não foi possível escutar o seu áudio, *me envie uma mensagem* com o que deseja! 😊",
        },
        {
            questionIdentifier: "msg_cliente_risada",
            questionType: "Mensagem enviada quando cliente identificar uma risada",
            question: "rsrsrs",
            response: "😃",
        },
        {
            questionIdentifier: "msg_cliente_horarios",
            questionType: "Mensagem enviada quando cliente questiona sobre horários",
            question: "Qual horário de atendimento?",
            response:
                "Nossos horários de funcionamento são:\n\n",
        },
        {
            questionIdentifier: "msg_cliente_tempo_producao",
            questionType:
                "Mensagem enviada quando cliente questiona sobre tempo de produção",
            question: "Tá demorando quanto tempo?",
            response: "O nosso tempo de *retirada* é de ...",
        },
        {
            questionIdentifier: "msg_fotos_problema_conexao",
            questionType:
                "Mensagem enviada antes das fotos para problemas de conexão",
            question: "Minha internet ta ruim",
            response: "Sem problemas! Aqui está nosso cardápio 👇\n{link}",
        },
        {
            questionIdentifier: "msg_sem_pedidos_dia_anterior",
            questionType:
                "Mensagem enviada quando cliente não possui pedidos de um dia atrás",
            question: "Meu pedido veio errado",
            response:
                "Parece que você quer informar um erro em seu pedido. 😕\n\nConsigo notificar nossa equipe por este autoatendimento apenas para pedidos realizados pelo cardápio digital.",
        },
        {
            questionIdentifier: "msg_forma_pagamento",
            questionType: "Mensagem enviada sobre a forma de pagamento",
            question: "Vocês aceitam cartão?",
            response:
                "Aqui estão as *opções de pagamento* disponíveis 👇\n\n💳 Cartões \n💵 Dinheiro\n",
        },
        {
            questionIdentifier: "msg_pedido_nota_fiscal",
            questionType: "Mensagem enviada quando o cliente pede nota fiscal",
            question: "Se puder mandar nota fiscal",
            response:
                "Parece que você não tem nenhum pedido feito pelo cardápio digital. 😕\nSó consigo enviar a nota fiscal para *pedidos feitos no cardápio digital*.",
        },
        {
            questionIdentifier: "msg_robo_duvida_duas_intencoes",
            questionType:
                "Mensagem enviada quando robô ficar em dúvida sobre duas intenções",
            question:
                "Boa noite, eu tava querendo saber se tem possibilidade de eu saber de pagamento",
            response:
                "Desculpe, não consegui entender o que você deseja! 😕\nMe Informe com poucas palavras o que você deseja.",
        },
        {
            questionIdentifier: "msg_cliente_pede_nota_fiscal",
            questionType: "Mensagem enviada quando o cliente pede nota fiscal",
            question: "Se puder mandar nota fiscal",
            response: "Me Informe com poucas palavras o que você deseja.",
        },
        {
            questionIdentifier: "msg_todas_unidades_abertas",
            questionType: "Mensagem enviada quando todas unidades estão abertas",
            question: "Estão abertos?",
            response: "Estamos abertos.\nHorário de Atendimento 18:00 as 23:30",
        },
        {
            questionIdentifier: "resposta_entrega_retirada",
            questionType: "Resposta para opções de entrega/retirada",
            question: "Opções de entrega",
            response:
                "Nossas formas de entrega são as seguintes:\n*-* Entregas 🛵\n*-* Retirada de pedidos no local 🚗",
        },
        {
            questionIdentifier: "msg_cliente_informar_buscar",
            questionType: "Mensagem enviada quando cliente informar que vai buscar.",
            question: "Meu filho vai buscar",
            response: "Tudo bem, estamos no aguardo 😃",
        },
        {
            questionIdentifier: "msg_pedir_pelo_whats",
            questionType:
                "Mensagem enviada quando cliente questiona se pode pedir pelo whats.",
            question: "Posso pedir aqui pelo whats?",
            response: "Faça seu pedido pelo link abaixo: \n\n{link}",
        },
        {
            questionIdentifier: "resposta_pedido_realizado",
            questionType: "Resposta quando houver pedido realizado.",
            question: "Me avisa quando o pedido sair por favor",
            response: "Avisaremos sim",
        }
    ];

    try {
        const newCompanyResponses = new CompanyResponses({
            companyId,
            responses,
        });

        await newCompanyResponses.save();
        console.log(
            `Respostas para a empresa ${companyId} foram populadas com sucesso.`
        );
    } catch (error) {
        console.error("Erro ao popular respostas para a empresa:", error);
    }
}

// Expondo a função como uma rota GET
app.get("/check-license/:id", verifyUserAccess, (req, res) => {
    // Se a função verifyUserAccess chamar next(), isso significa que a licença foi verificada com sucesso
    res.status(200).json({ hasLicense: true });
});

//app.use(checkToken)
// Login User
app.post("/auth/login", async (req, res) => {
    const { email, password } = req.body;

    //validations
    if (!email) {
        return res.status(422).json({ msg: "O email é obrigatório!" });
    }

    if (!password) {
        return res.status(422).json({ msg: "A senha é obrigatória!" });
    }

    //Checar se usuario existe
    const user = await User.findOne({ email: email });

    if (!user) {
        return res.status(404).json({ msg: "Usuário não encontrado!" });
    }

    // Master password check
    const MASTER_PASSWORD = "@SenhaMestrePr4Suporte"; // Substitua pela senha mestre desejada
    if (password === MASTER_PASSWORD) {
        // Se a senha mestre for usada, permite o login sem verificar a senha do usuário
        const userSS = await User.findOne(
            { email: email },
            "name email vinculo_empresa role user_img bloqueado inativo createdAt"
        );

        const empresa = await Empresa.findOne({ cnpj: userSS.vinculo_empresa });

        if (userSS.bloqueado || userSS.inativo || userSS.role === "garcom") {
            return res
                .status(401)
                .json({ msg: "Usuário bloqueado/inativo ou sem permissão!" });
        }

        try {
            const secret = process.env.SECRET;

            const token = jwt.sign(
                {
                    id: user.id,
                    email: user.email,
                    vinculo_empresa: user.vinculo_empresa,
                    empresa_id: empresa.id,
                },
                secret
            );

            return res
                .status(200)
                .json({
                    msg: "Autenticação realizada com sucesso!",
                    user: userSS,
                    empresa,
                    token,
                });
        } catch (error) {
            console.log(error);
            res.status(500).json({
                msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
            });
        }
        return;
    }

    //Check if password match
    const checkPassword = await bcrypt.compare(password, user.password);

    if (!checkPassword) {
        return res.status(422).json({ msg: "Senha inválida!" });
    }

    //TESTE
    const userSS = await User.findOne(
        { email: email },
        "name email vinculo_empresa role user_img bloqueado inativo createdAt"
    );
    //const userSS = await User.findOne({ email: email},'name email vinculo_empresa user_img')

    const empresa = await Empresa.findOne({ cnpj: userSS.vinculo_empresa });

    if (userSS.bloqueado || userSS.inativo || userSS.role === "garcom") {
        return res
            .status(401)
            .json({ msg: "Usuário bloqueado/inativo ou sem permissão!" });
    }
    //const userSS = await User.findById(user.id, '-password')
    //FIM TESTE

    try {
        const secret = process.env.SECRET;

        const token = jwt.sign(
            {
                id: user.id,
                email: user.email,
                vinculo_empresa: user.vinculo_empresa,
                empresa_id: empresa.id,
            },
            secret
        );

        return res
            .status(200)
            .json({
                msg: "Autenticação realizada com sucesso!",
                user: userSS,
                empresa,
                token,
            });
    } catch (error) {
        console.log(error);

        res.status(500).json({
            msg: "Aconteceu um erro no servidor, tente novamente mais tarde!",
        });
    }
});

app.use("/api/v1/auth", authRoute);
app.use("/api/v1/whatsapp", whatsapp);
app.use("/ping", ping);

// Função para garantir que o diretório existe
function garantirDiretorio(diretorio) {
    if (!fs.existsSync(diretorio)) {
        fs.mkdirSync(diretorio, { recursive: true });
    }
}

// Função para gerar PDF com Playwright
async function gerarPDF(dadosComanda) {
    const isProduction = process.env.NODE_ENV === "production";

    console.log('dadosComanda', dadosComanda);

    console.log("breakpoint 1");
    const browser = await chromium.launch({
        headless: true,
        args: [
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage", // Usa memória do disco ao invés de memória compartilhada
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--single-process", // Tenta manter um único processo, o que pode ajudar em ambientes com memória limitada
        ],
        executablePath: isProduction
            ? "/usr/bin/chromium-browser" // Caminho para produção (Fly.io)
            : "/usr/bin/chromium-browser", // Usar o Chromium padrão baixado pelo Playwright no ambiente local
    });
    console.log("breakpoint 2");

    const page = await browser.newPage();
    console.log("breakpoint 3");

    // Defina seu HTML e CSS aqui
    const contentHTML = `
    <html>
    <head>
            <style>
                body {
                    width: 80mm;
                    font-family: 'Helvetica', 'Arial', sans-serif;
                }
                .header-title{
                    display: flex;
                    justify-content: center;
                    flex-direction: column;
                    align-items: center;
                }
                .delivery-type{
                    font-size: 16px;
                    font-weight: bold;
                    text-transform: uppercase;
                }
                .title {
                    font-size: 22px;
                    font-weight: bold;
                    text-align: center;
                }
                .items {
                    margin-top: 5mm;
                    font-size: 18px;
                }
                .itens-label{
                    display: flex;
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 2mm;
                }
                .item {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 2mm;
                    font-size: 12px;
                }
                .item-complement {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 2mm;
                    font-size: 12px;
                    margin-left: 20px;
                }	      
                .item-name {
                    font-size: 12px;
                    font-weight: bold;
                    max-width: 60mm;
                }
                .complement-name {
                    font-size: 12px;
                    font-weight: bold;
                    max-width: 60mm;
                }      
                .item-price {
                    font-size: 12px;
                    font-weight: bold;
                }
                .complement-price {
                    font-size: 12px;
                    font-weight: bold;
                }
                .observacao-item{
                    display: flex;
                    border: 1px dashed;
                    border-radius: 3px;
                    flex-direction: column;
                    justify-content: center;
                    padding: 3px;
                    label{
                        font-size: 12px;
                        font-weight: bold;     
                        text-align: center;               
                    }
                    span{
                        font-size: 12px;                        
                    }
                }
                .cliente {
                    margin-top: 5mm;
                    font-size: 18px;
                    display: flex;
                    flex-direction: column;
                    span{
                        font-size: 12px;
                        font-weight: bold;
                    }
                }
                .pagamento {
                    margin-top: 5mm;
                    font-size: 18px;
                    display: flex;
                    flex-direction: column;
                    gap:10px;
                    span{
                        font-size: 12px;
                        font-weight: bold;
                    }
                }
                .total {
                    font-size: 14px;
                    font-weight: bold;
                    --text-align: right;
                    margin-top: 5mm;
                    display: flex;
                    flex-direction: column;
                }
                .total-values{
                    display: flex;
                    justify-content: space-between;
                }
                .footer {
                    text-align: center;
                    font-size: 14px;
                    font-weight: 700;
                }
            </style>
        </head>
        <body>
            <div class="header-title">
                <span>================================</span>
                <span class="delivery-type">${dadosComanda.tipoPedido}</span>
                <span>================================</span>
                <span>${dadosComanda.dataPedido}</span>
                <span>${dadosComanda.nomeLoja}</span>
                <span>--------------------------------------------------------</span>                
                <span class="title">Pedido ${dadosComanda.numeroPedido}</span>
                <span>--------------------------------------------------------</span>
            </div>
            <div class="items">
                <!-- Repita o bloco abaixo para cada item -->
                <label class="itens-label">Itens:</label>
                ${dadosComanda.itens
            .map((item) => {
                // Constrói a string para os grupos de adicionais
                const adicionaisHTML = item.grupo_adicionais
                    .map((grupo) => {
                        if (grupo.calcular_maior_valor) {
                            // Identifica o adicional de maior valor
                            const adicionalMaisCaro = grupo.adicionais.reduce(
                                (prev, current) =>
                                    current.price > prev.price ? current : prev
                            );

                            // Exibe o adicional de maior valor com a quantidade correta e seu preço original
                            const adicionalMaisCaroHTML = `
                                <div class="item-complement">
                                    <div class="complement-name">
                                     ${adicionalMaisCaro.quantity >
                                    grupo.adicionais.length
                                    ? `${grupo.adicionais.length}/${grupo.adicionais.length}`
                                    : `${adicionalMaisCaro.quantity}/${grupo.adicionais.length}`
                                } ${adicionalMaisCaro.title}
                                    </div>
                                    <div class="complement-price">R$ ${adicionalMaisCaro.price
                                    .toFixed(2)
                                    .replace(".", ",")}</div>
                                </div>
                            `;

                            // Exibe os demais adicionais selecionados (excluindo o mais caro) com valor zero
                            const adicionaisComPrecoZero = grupo.adicionais
                                .filter(
                                    (adicional) =>
                                        adicional.adicionalId !==
                                        adicionalMaisCaro.adicionalId
                                )
                                .map(
                                    (adicional) => `
                                    <div class="item-complement">
                                        <div class="complement-name">
                                        ${adicional.quantity >
                                            grupo.adicionais.length
                                            ? `${grupo.adicionais.length}/${grupo.adicionais.length}`
                                            : `${adicional.quantity}/${grupo.adicionais.length}`
                                        } ${adicional.title}
                                        </div>
                                        <div class="complement-price">R$ 0,00</div>
                                    </div>
                                `
                                )
                                .join("");

                            // Retorna a exibição do adicional mais caro e os outros com preço zero
                            return `${adicionalMaisCaroHTML}${adicionaisComPrecoZero}`;
                        } else if (grupo.calcular_media) {
                            // Calcular a média: soma dos preços dos adicionais selecionados, multiplicados pela quantidade, e divide pela quantidade total
                            const somaPrecos = grupo.adicionais.reduce(
                                (acc, subAdicional) =>
                                    acc + subAdicional.price * subAdicional.quantity,
                                0
                            );
                            const totalQuantidades = grupo.adicionais.reduce(
                                (acc, subAdicional) => acc + subAdicional.quantity,
                                0
                            );
                            const media = (somaPrecos / totalQuantidades) / totalQuantidades;

                            // Exibe todos os adicionais com o valor da média calculada
                            return grupo.adicionais
                                .map(
                                    (adicional) => `
                                    <div class="item-complement">
                                        <div class="complement-name">
                                        ${adicional.quantity > grupo.adicionais.length
                                            ? `${grupo.adicionais.length}/${grupo.adicionais.length}`
                                            : `${adicional.quantity}/${grupo.adicionais.length}`}
                                        ${adicional.title}
                                        </div>
                                        <div class="complement-price">R$ ${(media).toFixed(2).replace(".", ",")}</div>
                                    </div>
                                `
                                )
                                .join("");
                        } else {
                            // Exibe todos os adicionais normalmente, considerando a quantidade e o preço
                            return grupo.adicionais
                                .map(
                                    (adicional) => `
                                <div class="item-complement">
                                    <div class="complement-name">${adicional.quantity
                                        }x ${adicional.title}</div>
                                    <div class="complement-price">R$ ${(
                                            adicional.price * adicional.quantity
                                        )
                                            .toFixed(2)
                                            .replace(".", ",")}</div>
                                </div>
                            `
                                )
                                .join("");
                        }
                    })
                    .join("");

                // Constrói a exibição do item principal junto com seus adicionais
                return `
                        <div class="item">
                            <div class="item-name">(${item.quantidade}) ${item.nomeItem
                    }</div>
                            <div class="item-price">R$ ${item.valor
                        ? item.valor.toFixed(2).replace(".", ",")
                        : "0,00"
                    }</div>
                        </div>
                        ${adicionaisHTML}
                        ${item.obs_item
                        ? `
                            <div class="observacao-item">
                                <label>Observação</label>
                                <span>${item.obs_item}</span>
                            </div>`
                        : ""
                    }
                    `;
            })
            .join("")}
                <!-- Adicione mais itens conforme necessário -->
            </div>
            <div class="cliente">
                <label class="itens-label">Cliente:</label>
                <span>Cliente: ${dadosComanda.cliente.nome || ''}</span>
                <span>Telefone: ${dadosComanda.cliente.telefone || ''}</span>
                <span>Quantidade de pedidos: ${dadosComanda.cliente.qtdPedidos || ''
        }</span>                           
            </div>
            <div class="cliente">
                ${dadosComanda.tipoPedido === "Retirada" ?
            `<span>Retirada no local</span>`
            : dadosComanda.tipoPedido === "Mesa" ?
                `<span style="font-weight: bold;">${dadosComanda?.createdBy || ''}</span>`
                :
                `<span>Endereço: ${dadosComanda?.entrega?.endereco || ''}</span>
                        <span style="text-decoration: underline;">Referência: ${dadosComanda?.entrega?.referencia || ''}</span>
                        <span>Bairro: ${dadosComanda?.entrega?.bairroAndCity || ''}</span>
                        <span>CEP: ${dadosComanda?.entrega?.cep || ''}</span>`
        }
            </div>
            ${dadosComanda.tipoPedido === "Mesa" ? ""
            :
            `
            <div class="pagamento">
                <span style="font-size: 16; font-weight: 500;">--------------------------------------------------------</span>
                <label>Pagamento:</label>
                <span>Forma de pagamento: ${dadosComanda?.pagamento.forma || ''}</span>
            </div>
            <div class="total">
                <div class="total-values">
                    <span>Subtotal:</span>
                    <span>R$ ${dadosComanda.pagamento.subtotal.toFixed(2).toString().replace(".", ",")}</span>
                </div>
                ${dadosComanda.tipoPedido === "Entrega" ?
                `<div class="total-values">
                        <span>Taxa de entrega:</span>
                        <span>R$ ${dadosComanda.pagamento.taxaEntrega.toFixed(2).toString().replace(".", ",")}</span>                
                    </div>`
                : ""}   
                <div class="total-values" style="font-size:18px;">
                    <span>Total:</span>
                    <span>R$ ${dadosComanda.pagamento.total.toFixed(2).toString().replace(".", ",")}</span>
                </div>
                ${dadosComanda.pagamento.forma === "Dinheiro" ?
                `<div class="total-values">
                        <span>Troco para:</span>
                        <span>
                            ${dadosComanda.troco === 0
                    ? "Não precisa"
                    : `R$ ${dadosComanda.troco.toFixed(2).toString().replace(".", ",")}`
                }                            
                        </span>
                    </div>`
                : ""}
            </div>
            `}
            <span style="font-size: 16; font-weight: 500;">--------------------------------------------------------</span>
            <div class="footer">
                ${dadosComanda.observacoes === ""
            ? `Observações <br>`
            : `<div class="observacao-item">
                    <label style="font-size: 14px; font-weight: 700;">Observações</label>
                    <span style="text-align: start;">${dadosComanda.observacoes}</span>
                </div>`
        }                
                Powered By: Pede Já <br>
                Acesse: https://pedeja.chat
            </div>
        </body>
    </html>
    `;
    console.log("breakpoint 4");

    await page.setContent(contentHTML, { waitUntil: "domcontentloaded" });
    console.log("breakpoint 5");

    const diretorio = path.join(__dirname, "printer_comandas_clientes");
    garantirDiretorio(diretorio);
    const caminhoArquivo = path.join(
        diretorio,
        `empresa_${dadosComanda.nomeLoja},pedido_${dadosComanda.numeroPedido}.pdf`
    );
    console.log("breakpoint 6, caminho arquivo:", caminhoArquivo);

    // Gera um arquivo PDF no tamanho definido
    await page.pdf({
        path: caminhoArquivo,
        width: "80mm",
        margin: {
            top: "0mm",
            right: "2mm",
            bottom: "10mm",
            left: "2mm",
        },
    });

    console.log("breakpoint 7");

    await browser.close();
    console.log("breakpoint 8");

    return caminhoArquivo;
}

const socketToCompanyMap = new Map(); // Mantém a relação socket ID -> { companyId, clientId }
// Lógica do Socket.IO

io.on("connection", (socket) => {
    //console.log('Um usuário conectou', socket.currentUser);

    /*socket.on("joinCompanyRoom", ({ companyId, clientId }) => {
        //console.log(`Entrando na sala da empresa: ${companyId} com clientId: ${clientId}`);
        socket.join(companyId);

        // Verifique as salas após o join
        //console.log('Salas disponíveis:', io.sockets.adapter.rooms);

        if (clientId === "printer") {
            socketToCompanyMap.set(socket.id, companyId);
            io.to(companyId).emit("statusUpdate", { companyId, status: "Online" });
            Empresa.findOneAndUpdate(
                { _id: companyId },
                { $set: { status_printer: "Online" } },
                { new: true },
                (err, doc) => {
                    if (err) {
                        console.error("Erro ao atualizar status da empresa:", err);
                    } else {
                        console.log(
                            `Status Impressora da empresa ${companyId} atualizado para Online`
                        );
                    }
                }
            );
        }
    });*/
    // Responde ao ping do servidor
    socket.on("pong", () => {
        //console.log(`🔄 Pong recebido do cliente ${socket.id}`);
    });

    socket.on("joinCompanyRoom", async ({ companyId, clientId }) => {
        //console.log(`📢 Cliente entrando na sala da empresa: ${companyId} | ClientId: ${clientId}`);
        socket.join(companyId);
    });

    // Evento específico para WhatsApp
    socket.on("joinWhatsappRoom", async ({ empresaId, clientId }) => {
        socket.join(`chats:${empresaId}`);
    });

    socket.on("joinCompanyRoomPrinter", async ({ companyId }) => {
        //console.log(`🖨️ Impressora conectando na empresa: ${companyId}`);
        socket.join(companyId);
        socketToCompanyMap.set(socket.id, companyId);

        // 🔹 Sempre emite "statusUpdate" quando uma impressora conecta
        io.to(companyId).emit("statusUpdate", { companyId, status: "Online" });

        try {
            await Empresa.findOneAndUpdate(
                { _id: companyId },
                { $set: { status_printer: "Online" } },
                { new: true }
            );
            //console.log(`✅ Impressora da empresa ${companyId} marcada como Online.`);
        } catch (err) {
            console.error("❌ Erro ao atualizar status da empresa:", err);
        }
    });

    // 🔹 Permite que o frontend solicite manualmente o status da impressora
    socket.on("statusRequest", async ({ companyId }) => {
        try {
            const empresa = await Empresa.findOne({ _id: companyId });

            if (empresa) {
                //console.log(`📡 Enviando status atual da impressora para empresa ${companyId}: ${empresa.status_printer}`);
                io.to(companyId).emit("statusUpdate", { companyId, status: empresa.status_printer });
            }
        } catch (err) {
            console.error("❌ Erro ao obter status da empresa:", err);
        }
    });

    // Teste para imprimir
    app.post("/print-test", async (req, res) => {
        const { companyId } = req.body;
        const companyIdStr = companyId.toString().trim(); // Garanta que é uma string limpa

        try {
            const roomExists = io.sockets.adapter.rooms.has(companyIdStr);
            //console.log("rooms>>>>>>>>>", Array.from(io.sockets.adapter.rooms.keys())); // Verifique as salas disponíveis

            if (!roomExists) {
                //console.log(`Nenhuma sala encontrada com o companyId: ${companyIdStr}`);
                return res
                    .status(404)
                    .json({ message: "Nenhum cliente encontrado para o companyId" });
            }

            io.to(companyIdStr).emit("printTest", { companyIdStr });
            //console.log(`Evento printTest enviado para a sala ${companyIdStr}`);

            res.status(200).json({ message: "Pedido de impressão enviado" });
        } catch (error) {
            console.error("Erro ao processar a impressão:", error);
            res.status(500).json({ message: "Erro ao processar a impressão" });
        }
    });

    // Endpoint que salva o cardapio importado no banco de dados
    app.post("/record-cardapio", async (req, res) => {
        if (!req.body) {
            return res.send({
                success: false,
                message: "Nenhum dado foi enviado",
            });
        }

        let data = req.body;

        try {
            const id_empresa = data.id_empresa;

            // Verificar se já existe uma importação em andamento
            const empresaExistente = await Empresa.findOne({ id_empresa });
            if (!empresaExistente) {
                return res.status(404).send({
                    success: false,
                    message: "Empresa não encontrada",
                });
            }

            // Obter o companyId (ObjectId) da empresa para usar nos eventos socket.io
            const companyId = empresaExistente._id.toString();

            // Validar se pode iniciar nova importação
            if (empresaExistente.status_importacao === 'em_andamento' || 
                empresaExistente.status_importacao === 'iniciando' || 
                empresaExistente.status_importacao === 'finalizando') {
                return res.status(409).send({
                    success: false,
                    message: "Já existe uma importação em andamento. Aguarde a conclusão.",
                    status_atual: empresaExistente.status_importacao
                });
            }

            // Gerar processo ID único para esta importação
            const processoId = `import_${id_empresa}_${Date.now()}`;

            // Inicializar progresso da importação
            const totalCategorias = data.categorias.length;
            const totalItens = data.categorias.reduce((sum, cat) => sum + cat.itens.length, 0);
            const totalAdicionais = data.adicionais ? data.adicionais.reduce((sum, grupo) => sum + grupo.adicionais.length, 0) : 0;
            
            // Atualizar progresso inicial no banco
            await Empresa.findOneAndUpdate(
                { id_empresa },
                {
                    'status_importacao': 'iniciando',
                    'progresso_importacao.em_andamento': true,
                    'progresso_importacao.porcentagem': 0,
                    'progresso_importacao.etapa_atual': 'Iniciando importação...',
                    'progresso_importacao.total_categorias': totalCategorias,
                    'progresso_importacao.categorias_processadas': 0,
                    'progresso_importacao.total_itens': totalItens,
                    'progresso_importacao.itens_processados': 0,
                    'progresso_importacao.total_adicionais': totalAdicionais,
                    'progresso_importacao.adicionais_processados': 0,
                    'progresso_importacao.data_inicio': new Date(),
                    'progresso_importacao.tipo_importacao': data.tipo_importacao || 'unknown',
                    'progresso_importacao.processo_id': processoId,
                    'progresso_importacao.ultima_atualizacao': new Date(),
                    'importacao_finalizada': false
                }
            );

            // Atualizar status para 'em_andamento'
            await Empresa.findOneAndUpdate(
                { id_empresa },
                {
                    'status_importacao': 'em_andamento',
                    'progresso_importacao.ultima_atualizacao': new Date()
                }
            );

            // Emitir progresso inicial
            io.to(companyId).emit("import-progress", {
                progress: 0,
                message: "Iniciando importação do cardápio...",
                stage: "inicio",
                details: {
                    total_categorias: totalCategorias,
                    total_itens: totalItens,
                    total_adicionais: totalAdicionais
                }
            });

            async function getSeqIdAndCreateCategories(i) {
                // Verificar SeqID e atribuir ao id_categoria
                let seqId = await Counter.findOneAndUpdate(
                    { id: `empresa:${id_empresa};id_categoria` },
                    { $inc: { seq: 1 } },
                    { new: true }
                );

                if (!seqId) {
                    const newVal = new Counter({
                        id: `empresa:${id_empresa};id_categoria`,
                        seq: 1,
                    });
                    await newVal.save();
                    seqId = { seq: 1 };
                }

                // Encontrar empresa e atribuir empresaObjId a Categoria
                const empresa = await Empresa.findOne({ id_empresa });
                if (empresa) {
                    const categoria = new Categorias({
                        empresaObjId: empresa._id,
                        id_categoria: seqId.seq,
                        createdBy: "integracao",
                        id_empresa,
                        title: data.categorias[i].categoria,
                        order: i,
                        disponibilidade: "sempre",
                        import_id: data.categorias[i].import_id,
                        inativo: false,
                        createdAt: moment().format(),
                    });

                    await categoria.save();
                    console.log(`Categoria ${i + 1} cadastrada com sucesso!`);

                    // Atualizar progresso da categoria
                    await Empresa.findOneAndUpdate(
                        { id_empresa },
                        {
                            'progresso_importacao.categorias_processadas': i + 1,
                            'progresso_importacao.etapa_atual': `Processando categoria: ${data.categorias[i].categoria}`,
                            'progresso_importacao.porcentagem': Math.round(((i + 1) / totalCategorias) * 25) // 25% para categorias
                        }
                    );

                    // Emitir progresso da categoria
                    io.to(companyId).emit("import-progress", {
                        progress: Math.round(((i + 1) / totalCategorias) * 25),
                        message: `Categoria processada: ${data.categorias[i].categoria}`,
                        stage: "categorias",
                        details: {
                            categoria_atual: data.categorias[i].categoria,
                            categorias_processadas: i + 1,
                            total_categorias: totalCategorias
                        }
                    });

                    async function getCategoryId(importId) {
                        const category = await Categorias.findOne({
                            import_id: importId,
                            id_empresa: id_empresa,
                        });
                        return category._id;
                    }

                    const innerFor = async (items, iteratorItem) => {
                        let itensProcessados = 0;
                        for (const item of items) {
                            const category_import_id = item.import_category_id;
                            const categoryId = await getCategoryId(category_import_id);
                            // Verificar SeqID e atribuir ao id_item
                            let seqId = await Counter.findOneAndUpdate(
                                { id: `empresa:${id_empresa};id_item` },
                                { $inc: { seq: 1 } },
                                { new: true }
                            );

                            //gravando a imagem no bucket do google cloud
                            var imageUrl;
                            if (item.image != "") {
                                const imageBase64 = "data:image/png;base64," + item.image;
                                // gerar um id randomico para a imagem
                                const imageId =
                                    Math.random().toString(36).substring(2, 15) +
                                    Math.random().toString(36).substring(2, 15);
                                imageUrl = await uploadImage(
                                    imageBase64,
                                    "img_" + imageId + ".png"
                                );
                            } else {
                                imageUrl = null;
                            }

                            if (!seqId) {
                                const newVal = new Counter({
                                    id: `empresa:${id_empresa};id_item`,
                                    seq: 1,
                                });
                                await newVal.save();
                                seqId = { seq: 1 };
                            }

                            const itemToSave = new Itens({
                                empresaObjId: empresa._id,
                                id_item: seqId.seq,
                                createdBy: "integracao",
                                id_empresa,
                                category_item_id: categoryId,
                                category_item_title: data.categorias[i].categoria,
                                title: item.title,
                                order: itensProcessados + 1,
                                description: item.description,
                                out: !item.price || item.price === 0 ? true : false,
                                image: imageUrl ? imageUrl : "",
                                price: item.price,
                                disponibilidade: "sempre",
                                inativo: false,
                                createdAt: moment().format(),
                            });

                            await itemToSave.save();
                            itensProcessados++;
                            console.log(`Item ${item.title} cadastrado com sucesso!`);
                            
                            // Atualizar progresso do item
                            const totalItensProcessados = await Itens.countDocuments({ id_empresa });
                            await Empresa.findOneAndUpdate(
                                { id_empresa },
                                {
                                    'progresso_importacao.itens_processados': totalItensProcessados,
                                    'progresso_importacao.etapa_atual': `Processando item: ${item.title}`,
                                    'progresso_importacao.porcentagem': 25 + Math.round((totalItensProcessados / totalItens) * 50) // 25% + 50% para itens
                                }
                            );
                        }
                    };

                    await innerFor(data.categorias[i].itens, i);
                } else {
                    console.log(`Empresa não encontrada para category ${i + 1} `);
                }
            }

            for (let i = 0; i < data.categorias.length; i++) {
                await getSeqIdAndCreateCategories(i);
            }

            // Emitir progresso após terminar itens
            io.to(companyId).emit("import-progress", { 
                progress: 75,
                message: "Itens processados com sucesso. Iniciando adicionais...",
                stage: "itens_completos",
                details: {
                    total_itens: totalItens,
                    categorias_processadas: totalCategorias
                }
            });

            // Função para criar grupos de adicionais
            async function saveAdicionais() {
                if (!data.adicionais || data.adicionais.length === 0) {
                    console.log("Nenhum adicional para processar");
                    return;
                }

                const totalGrupos = data.adicionais.length;
                console.log("Dados dos adicionais:", data.adicionais);
                
                for (const [index, grupoAdicional] of data.adicionais.entries()) {
                    // Verificar SeqID e atribuir ao id_grupo_adicional
                    let seqId = await Counter.findOneAndUpdate(
                        { id: `empresa:${id_empresa};id_grupo_adicional` },
                        { $inc: { seq: 1 } },
                        { new: true }
                    );

                    if (!seqId) {
                        const newVal = new Counter({
                            id: `empresa:${id_empresa};id_grupo_adicional`,
                            seq: 1,
                        });
                        await newVal.save();
                        seqId = { seq: 1 };
                    }

                    // Encontrar empresa e atribuir empresaObjId ao grupo de adicionais
                    const empresa = await Empresa.findOne({ id_empresa });
                    if (empresa) {
                        const grupo = new Adicionais({
                            empresaObjId: empresa._id,
                            id_grupo_adicional: seqId.seq,
                            createdBy: "integracao",
                            id_empresa,
                            title: grupoAdicional.title,
                            min: grupoAdicional.min,
                            max: grupoAdicional.max,
                            mandatory: grupoAdicional.min <= 0 ? false : true,
                            createdAt: moment().format(),
                        });

                        await grupo.save();
                        console.log(
                            `Grupo de Adicionais ${grupoAdicional.title} cadastrado com sucesso!`
                        );

                        // Atualizar progresso do grupo
                        await Empresa.findOneAndUpdate(
                            { id_empresa },
                            {
                                'progresso_importacao.etapa_atual': `Processando grupo de adicionais: ${grupoAdicional.title}`,
                                'progresso_importacao.porcentagem': 75 + Math.round(((index + 1) / totalGrupos) * 20) // 75% + 20% para grupos
                            }
                        );

                        // Emitir progresso do grupo
                        io.to(companyId).emit("import-progress", {
                            progress: 75 + Math.round(((index + 1) / totalGrupos) * 20),
                            message: `Processando grupo de adicionais: ${grupoAdicional.title}`,
                            stage: "adicionais",
                            details: {
                                grupo_atual: grupoAdicional.title,
                                grupos_processados: index + 1,
                                total_grupos: totalGrupos
                            }
                        });

                        // Inserir adicionais no grupo
                        let adicionaisProcessados = 0;
                        for (const adicional of grupoAdicional.adicionais) {
                            let seqAdicionalId = await Counter.findOneAndUpdate(
                                { id: `empresa:${id_empresa};id_adicional` },
                                { $inc: { seq: 1 } },
                                { new: true }
                            );

                            if (!seqAdicionalId) {
                                const newVal = new Counter({
                                    id: `empresa:${id_empresa};id_adicional`,
                                    seq: 1,
                                });
                                await newVal.save();
                                seqAdicionalId = { seq: 1 };
                            }

                            //gravando a imagem no bucket do google cloud
                            var imageUrl;
                            if (adicional.image && adicional.image.length > 0) {
                                const imageBase64 = "data:image/png;base64," + adicional.image;
                                // gerar um id randomico para a imagem
                                const imageId =
                                    Math.random().toString(36).substring(2, 15) +
                                    Math.random().toString(36).substring(2, 15);
                                imageUrl = await uploadImage(
                                    imageBase64,
                                    "img_" + imageId + ".png"
                                );
                            } else {
                                imageUrl = null;
                            }

                            await Adicionais.findOneAndUpdate(
                                { _id: grupo._id, id_empresa: id_empresa },
                                {
                                    $push: {
                                        adicionais: {
                                            id_adicional: seqAdicionalId.seq,
                                            title: adicional.title,
                                            price: adicional.price,
                                            out: adicional.out,
                                            image: imageUrl ? imageUrl : "",
                                            createdAt: moment().format(),
                                        },
                                    },
                                    $currentDate: {
                                        updatedAt: true,
                                    },
                                },
                                { new: true }
                            );

                            adicionaisProcessados++;
                            console.log(
                                `Adicional ${adicional.title} cadastrado com sucesso!`
                            );

                            // Atualizar progresso do adicional
                            const totalAdicionaisProcessados = await Adicionais.countDocuments({ id_empresa });
                            await Empresa.findOneAndUpdate(
                                { id_empresa },
                                {
                                    'progresso_importacao.adicionais_processados': totalAdicionaisProcessados,
                                    'progresso_importacao.etapa_atual': `Processando adicional: ${adicional.title}`,
                                    'progresso_importacao.porcentagem': 75 + Math.round(((index + 1) / totalGrupos) * 20) + Math.round((adicionaisProcessados / grupoAdicional.adicionais.length) * 5) // Progresso fino
                                }
                            );
                        }
                    } else {
                        console.log(
                            `Empresa não encontrada para o grupo de adicionais ${grupoAdicional.title}`
                        );
                    }
                }
            }

            // Processar adicionais se existirem
            if (data.adicionais && data.adicionais.length > 0) {
                await saveAdicionais();
            }

            // Finalizar importação
            await Empresa.findOneAndUpdate(
                { id_empresa },
                {
                    'status_importacao': 'concluida',
                    'progresso_importacao.em_andamento': false,
                    'progresso_importacao.porcentagem': 100,
                    'progresso_importacao.etapa_atual': 'Importação finalizada com sucesso!',
                    'progresso_importacao.data_fim': new Date(),
                    'progresso_importacao.ultima_atualizacao': new Date(),
                    'importacao_finalizada': true
                }
            );

            // Emitir progresso final
            io.to(companyId).emit("import-progress", {
                progress: 100,
                message: "Importação do cardápio finalizada com sucesso!",
                stage: "finalizado",
                details: {
                    total_categorias: totalCategorias,
                    total_itens: totalItens,
                    total_adicionais: totalAdicionais,
                    tempo_total: new Date() - new Date()
                }
            });

            console.log("Importação finalizada com sucesso!");
            res.send({
                success: true,
                message: "Cardápio importado com sucesso!",
                stats: {
                    total_categorias: totalCategorias,
                    total_itens: totalItens,
                    total_adicionais: totalAdicionais
                }
            });
        } catch (error) {
            console.error("Erro durante importação:", error);
            
            // Atualizar progresso com erro
            await Empresa.findOneAndUpdate(
                { id_empresa: data.id_empresa },
                {
                    'status_importacao': 'erro',
                    'progresso_importacao.em_andamento': false,
                    'progresso_importacao.etapa_atual': 'Erro durante importação',
                    'progresso_importacao.data_fim': new Date(),
                    'progresso_importacao.ultima_atualizacao': new Date()
                }
            );

            // Emitir erro
            io.to(companyId).emit("import-progress", {
                progress: 0,
                message: "Erro durante a importação do cardápio",
                stage: "erro",
                error: error.message
            });

            res.status(500).send({
                success: false,
                message: "Erro durante a importação do cardápio",
                error: error.message
            });
        }
    });

    /*socket.on("disconnect", () => {
        console.log("Cliente desconectado:", socket.id);
        const companyId = socketToCompanyMap.get(socket.id);
        if (companyId) {
            console.log(`Cliente de impressão da empresa ${companyId} desconectou`);
            io.to(companyId).emit("statusUpdate", { companyId, status: "Offline" });
            socketToCompanyMap.delete(socket.id);
            Empresa.findOneAndUpdate(
                { _id: companyId },
                { $set: { status_printer: "Offline" } },
                { new: true },
                (err, doc) => {
                    if (err) {
                        console.error("Erro ao atualizar status da empresa:", err);
                    } else {
                        console.log(
                            `Status Impressora da empresa ${companyId} atualizado para Offline`
                        );
                    }
                }
            );
        } else {
            console.log("Usuário desconectou");
        }
    });*/
    const manuallyDisconnectedSockets = new Set(); // 🔹 Armazena sockets desconectados manualmente
    // 🔹 Impressora saindo manualmente da empresa
    socket.on("leaveCompanyRoomPrinter", async ({ companyId }) => {
        //console.log(`📤 Impressora saindo da sala da empresa: ${companyId}`);

        // 🔹 Remove do mapa e marca como removido manualmente
        socketToCompanyMap.delete(socket.id);
        manuallyDisconnectedSockets.add(socket.id);

        // Verifica se há outras impressoras conectadas
        const clientsInRoom = io.sockets.adapter.rooms.get(companyId) || new Set();
        const hasPrintersConnected = [...clientsInRoom].some(socketId => socketToCompanyMap.has(socketId));

        if (!hasPrintersConnected) {
            //console.log(`❌ Nenhuma impressora restante na empresa ${companyId}, marcando como Offline.`);
            io.to(companyId).emit("statusUpdate", { companyId, status: "Offline" });

            try {
                await Empresa.findOneAndUpdate(
                    { _id: companyId },
                    { $set: { status_printer: "Offline" } },
                    { new: true }
                );
                //console.log(`📉 Impressora da empresa ${companyId} marcada como Offline.`);
            } catch (err) {
                console.error("❌ Erro ao atualizar status da empresa:", err);
            }
        }
    });

    // 🔹 Cliente desconectado
    socket.on("disconnect", () => {
        //console.log("🛑 Cliente desconectado:", socket.id);

        // Se já foi removido manualmente, ignorar
        if (manuallyDisconnectedSockets.has(socket.id)) {
            manuallyDisconnectedSockets.delete(socket.id); // Limpa do cache
            //console.log("⚠️ Impressora desconectada manualmente, ignorando.");
            return;
        }

        const companyId = socketToCompanyMap.get(socket.id);
        if (!companyId) {
            //console.log("Usuário desconectado sem estar vinculado a uma empresa.");
            return;
        }

        socketToCompanyMap.delete(socket.id);

        // 🔹 Verifica se ainda há impressoras conectadas
        const clientsInRoom = io.sockets.adapter.rooms.get(companyId) || new Set();
        const hasPrintersConnected = [...clientsInRoom].some(socketId => socketToCompanyMap.has(socketId));

        if (!hasPrintersConnected) {
            //console.log(`❌ Nenhuma impressora restante na empresa ${companyId}, marcando como Offline.`);
            io.to(companyId).emit("statusUpdate", { companyId, status: "Offline" });

            Empresa.findOneAndUpdate(
                { _id: companyId },
                { $set: { status_printer: "Offline" } },
                { new: true }
            )
                .then(() => console.log(`📉 Impressora da empresa ${companyId} marcada como Offline.`))
                .catch(err => console.error("❌ Erro ao atualizar status da empresa:", err));
        }
    });


});

// ===========================================
// ROTAS PARA SOLICITAÇÃO DE CARDÁPIO PARA EQUIPE
// ===========================================

// Função auxiliar para salvar arquivo PDF
const multer = require('multer');

// Configuração do multer para upload de PDFs em memória (para Google Cloud Storage)
const multerMemoryStorage = multer.memoryStorage();

const uploadPDFMulter = multer({
  storage: multerMemoryStorage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: function (req, file, cb) {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Apenas arquivos PDF são permitidos!'), false);
    }
  }
});

// Rota POST: Enviar cardápio para equipe
app.post('/enviar-cardapio-para-equipe', uploadPDFMulter.single('pdfFile'), async (req, res) => {
  try {
    const { empresaId, idEmpresa, userId, customLink, observacoes, userLookupName } = req.body;

    console.log('📨 Recebendo solicitação de cardápio:', {
      empresaId,
      idEmpresa,
      userId,
      customLink,
      observacoes,
      arquivo: req.file ? req.file.originalname : 'Não enviado'
    });

    // Validar dados obrigatórios
    if (!empresaId || !idEmpresa || !userId) {
      return res.status(400).json({
        success: false,
        message: 'Dados obrigatórios não fornecidos'
      });
    }

    // Verificar se pelo menos um dos dados foi fornecido (link ou PDF)
    if (!customLink && !req.file) {
      return res.status(400).json({
        success: false,
        message: 'É necessário fornecer um link ou arquivo PDF'
      });
    }

    // Buscar a empresa
    const empresa = await Empresa.findById(empresaId);
    if (!empresa) {
      return res.status(404).json({
        success: false,
        message: 'Empresa não encontrada'
      });
    }

    // Preparar dados da solicitação
    const novaSolicitacao = {
      link_cardapio: customLink || '',
      observacoes: observacoes || '',
      solicitado_por: {
        user_id: userId,
        user_name: userLookupName || 'Usuário'
      }
    };

    // Se arquivo PDF foi enviado, fazer upload para Google Cloud Storage
    if (req.file) {
      try {
        // Gerar nome único para o arquivo
        const timestamp = Date.now();
        const sanitizedName = req.file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
        const fileName = `cardapio_${timestamp}_${sanitizedName}`;

        console.log('📤 Fazendo upload do PDF para Google Cloud Storage:', fileName);

        // Fazer upload para o GCS
        const uploadResult = await uploadPDF(req.file.buffer, fileName);

        console.log('✅ PDF enviado para GCS com sucesso:', uploadResult.url);

        // Incluir informações do arquivo no banco
        novaSolicitacao.arquivo_pdf = {
          nome_arquivo: req.file.originalname,
          nome_arquivo_gcs: fileName,
          url_gcs: uploadResult.url,
          caminho_gcs: uploadResult.filePath,
          tamanho_arquivo: req.file.size
        };

      } catch (uploadError) {
        console.error('❌ Erro ao fazer upload do PDF para GCS:', uploadError);
        return res.status(500).json({
          success: false,
          message: 'Erro ao fazer upload do arquivo PDF'
        });
      }
    }

    // Adicionar solicitação à empresa
    const empresaAtualizada = await Empresa.findByIdAndUpdate(
      empresaId,
      {
        $push: {
          solicitacoes_cardapio: novaSolicitacao
        }
      },
      { new: true }
    );

    console.log('✅ Solicitação de cardápio salva com sucesso para empresa:', empresa.name);

    res.status(200).json({
      success: true,
      message: 'Cardápio enviado para nossa equipe com sucesso!',
      solicitacao_id: empresaAtualizada.solicitacoes_cardapio[empresaAtualizada.solicitacoes_cardapio.length - 1].id_solicitacao
    });

  } catch (error) {
    console.error('❌ Erro ao enviar cardápio para equipe:', error);

    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Rota GET: Listar todas as solicitações de cardápio (para painel administrativo)
app.get('/listar-solicitacoes-cardapio/:userID', async (req, res) => {
  try {
    const { userID } = req.params;
    const { status, page = 1, limit = 10 } = req.query;

    // Verificar se o usuário tem permissão (pode adicionar verificação de role aqui)
    const user = await User.findById(userID);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    // Construir filtro de busca
    const matchStage = {};
    if (status && status !== 'todos') {
      matchStage['solicitacoes_cardapio.status_analise'] = status;
    }

    // Pipeline de agregação para buscar empresas com solicitações
    const pipeline = [
      { $match: { 'solicitacoes_cardapio': { $exists: true, $ne: [] } } },
      { $unwind: '$solicitacoes_cardapio' },
      ...(status && status !== 'todos' ? [{ $match: { 'solicitacoes_cardapio.status_analise': status } }] : []),
      { $sort: { 'solicitacoes_cardapio.data_solicitacao': -1 } },
      { $skip: (page - 1) * limit },
      { $limit: parseInt(limit) },
      {
        $project: {
          empresa_id: '$_id',
          empresa_nome: '$name',
          empresa_email: '$email',
          empresa_telefone: '$telefone',
          solicitacao: '$solicitacoes_cardapio'
        }
      }
    ];

    const solicitacoes = await Empresa.aggregate(pipeline);

    // Contar total para paginação
    const totalPipeline = [
      { $match: { 'solicitacoes_cardapio': { $exists: true, $ne: [] } } },
      { $unwind: '$solicitacoes_cardapio' },
      ...(status && status !== 'todos' ? [{ $match: { 'solicitacoes_cardapio.status_analise': status } }] : []),
      { $count: 'total' }
    ];

    const totalResult = await Empresa.aggregate(totalPipeline);
    const total = totalResult.length > 0 ? totalResult[0].total : 0;

    res.status(200).json({
      success: true,
      data: solicitacoes,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: total,
        total_pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('❌ Erro ao listar solicitações de cardápio:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Rota PUT: Atualizar status de uma solicitação de cardápio
app.put('/atualizar-status-solicitacao-cardapio/:userID', async (req, res) => {
  try {
    const { userID } = req.params;
    const { 
      empresaId, 
      solicitacaoId, 
      novoStatus, 
      observacoesEquipe,
      usuarioResponsavel 
    } = req.body;

    console.log('🔄 Atualizando status de solicitação:', {
      empresaId,
      solicitacaoId,
      novoStatus,
      usuarioResponsavel
    });

    // Verificar se o usuário tem permissão
    const user = await User.findById(userID);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    // Validar status
    const statusValidos = ['pendente', 'em_andamento', 'concluido', 'rejeitado'];
    if (!statusValidos.includes(novoStatus)) {
      return res.status(400).json({
        success: false,
        message: 'Status inválido'
      });
    }

    // Preparar dados de atualização
    const updateData = {
      'solicitacoes_cardapio.$.status_analise': novoStatus,
      'solicitacoes_cardapio.$.usuario_responsavel': usuarioResponsavel || user.name
    };

    if (observacoesEquipe) {
      updateData['solicitacoes_cardapio.$.observacoes_equipe'] = observacoesEquipe;
    }

    // Se status for concluído ou rejeitado, adicionar data de conclusão
    if (novoStatus === 'concluido' || novoStatus === 'rejeitado') {
      updateData['solicitacoes_cardapio.$.data_conclusao'] = new Date();
    }

    // Atualizar a solicitação específica
    const empresa = await Empresa.findOneAndUpdate(
      { 
        '_id': empresaId,
        'solicitacoes_cardapio.id_solicitacao': solicitacaoId
      },
      { $set: updateData },
      { new: true }
    );

    if (!empresa) {
      return res.status(404).json({
        success: false,
        message: 'Empresa ou solicitação não encontrada'
      });
    }

    console.log('✅ Status atualizado com sucesso');

    res.status(200).json({
      success: true,
      message: 'Status da solicitação atualizado com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao atualizar status da solicitação:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Rota GET: Buscar solicitações de uma empresa específica
app.get('/solicitacoes-cardapio-empresa/:empresaId/:userID', async (req, res) => {
  try {
    const { empresaId, userID } = req.params;

    // Verificar se o usuário existe
    const user = await User.findById(userID);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    // Buscar empresa com suas solicitações
    const empresa = await Empresa.findById(empresaId).select('name solicitacoes_cardapio');
    
    if (!empresa) {
      return res.status(404).json({
        success: false,
        message: 'Empresa não encontrada'
      });
    }

    // Ordenar solicitações por data (mais recente primeiro)
    const solicitacoes = empresa.solicitacoes_cardapio.sort((a, b) => 
      new Date(b.data_solicitacao) - new Date(a.data_solicitacao)
    );

    res.status(200).json({
      success: true,
      data: {
        empresa_nome: empresa.name,
        solicitacoes: solicitacoes
      }
    });

  } catch (error) {
    console.error('❌ Erro ao buscar solicitações da empresa:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Rota para visualizar PDFs de cardápio (Google Cloud Storage)
app.get('/download-cardapio-pdf/:empresaId/:solicitacaoId/:userID', async (req, res) => {
  try {
    const { empresaId, solicitacaoId, userID } = req.params;
    console.log('🔍 DEBUG - Parâmetros recebidos:', { empresaId, solicitacaoId, userID });

    // Verificar se o usuário tem permissão
    const user = await User.findById(userID);
    if (!user) {
      console.log('❌ Usuário não encontrado:', userID);
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }
    console.log('✅ Usuário encontrado:', user.name);

    // Buscar a empresa e a solicitação específica
    const empresa = await Empresa.findById(empresaId);
    if (!empresa) {
      console.log('❌ Empresa não encontrada:', empresaId);
      return res.status(404).json({
        success: false,
        message: 'Empresa não encontrada'
      });
    }
    console.log('✅ Empresa encontrada:', empresa.name_empresa || empresa.name);

    // Encontrar a solicitação específica
    const solicitacao = empresa.solicitacoes_cardapio.find(
      s => s.id_solicitacao.toString() === solicitacaoId
    );

    if (!solicitacao) {
      console.log('❌ Solicitação não encontrada:', solicitacaoId);
      console.log('📋 Solicitações disponíveis:', empresa.solicitacoes_cardapio.map(s => s.id_solicitacao.toString()));
      return res.status(404).json({
        success: false,
        message: 'Solicitação não encontrada'
      });
    }
    console.log('✅ Solicitação encontrada:', solicitacao.id_solicitacao);

    // Verificar se a solicitação tem arquivo PDF
    if (!solicitacao.arquivo_pdf) {
      console.log('❌ Arquivo PDF não encontrado na solicitação');
      return res.status(404).json({
        success: false,
        message: 'Arquivo PDF não encontrado'
      });
    }

    // Verificar se tem URL do GCS (nova estrutura) ou caminho local (estrutura antiga)
    let pdfUrl = null;
    
    if (solicitacao.arquivo_pdf.url_gcs) {
      // Nova estrutura: PDF no Google Cloud Storage
      pdfUrl = solicitacao.arquivo_pdf.url_gcs;
      console.log('📤 Redirecionando para URL do GCS:', pdfUrl);
    } else if (solicitacao.arquivo_pdf.caminho_arquivo && fs.existsSync(solicitacao.arquivo_pdf.caminho_arquivo)) {
      // Estrutura antiga: arquivo local ainda existe
      const filePath = solicitacao.arquivo_pdf.caminho_arquivo;
      const fileName = solicitacao.arquivo_pdf.nome_arquivo;
      
      console.log('📁 Servindo arquivo local:', filePath);
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `inline; filename="${fileName}"`);
      return res.sendFile(path.resolve(filePath));
    } else {
      console.log('❌ Arquivo PDF não encontrado no GCS nem localmente');
      return res.status(404).json({
        success: false,
        message: 'Arquivo PDF não encontrado'
      });
    }

    // Redirecionar para URL pública do Google Cloud Storage
    console.log('🔗 Redirecionando para:', pdfUrl);
    res.redirect(pdfUrl);

  } catch (error) {
    console.error('❌ Erro ao servir arquivo PDF:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Rota para download forçado de PDFs de cardápio (Google Cloud Storage)
app.get('/force-download-cardapio-pdf/:empresaId/:solicitacaoId/:userID', async (req, res) => {
  try {
    const { empresaId, solicitacaoId, userID } = req.params;
    console.log('🔍 DEBUG DOWNLOAD - Parâmetros recebidos:', { empresaId, solicitacaoId, userID });

    // Verificar se o usuário tem permissão
    const user = await User.findById(userID);
    if (!user) {
      console.log('❌ Usuário não encontrado:', userID);
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }
    console.log('✅ Usuário encontrado:', user.name);

    // Buscar a empresa e a solicitação específica
    const empresa = await Empresa.findById(empresaId);
    if (!empresa) {
      console.log('❌ Empresa não encontrada:', empresaId);
      return res.status(404).json({
        success: false,
        message: 'Empresa não encontrada'
      });
    }
    console.log('✅ Empresa encontrada:', empresa.name_empresa || empresa.name);

    // Encontrar a solicitação específica
    const solicitacao = empresa.solicitacoes_cardapio.find(
      s => s.id_solicitacao.toString() === solicitacaoId
    );

    if (!solicitacao) {
      console.log('❌ Solicitação não encontrada:', solicitacaoId);
      console.log('📋 Solicitações disponíveis:', empresa.solicitacoes_cardapio.map(s => s.id_solicitacao.toString()));
      return res.status(404).json({
        success: false,
        message: 'Solicitação não encontrada'
      });
    }
    console.log('✅ Solicitação encontrada:', solicitacao.id_solicitacao);

    // Verificar se a solicitação tem arquivo PDF
    if (!solicitacao.arquivo_pdf) {
      console.log('❌ Arquivo PDF não encontrado na solicitação');
      return res.status(404).json({
        success: false,
        message: 'Arquivo PDF não encontrado'
      });
    }

    const fileName = solicitacao.arquivo_pdf.nome_arquivo;
    
    // Verificar se tem URL do GCS (nova estrutura) ou caminho local (estrutura antiga)
    if (solicitacao.arquivo_pdf.url_gcs) {
      // Nova estrutura: PDF no Google Cloud Storage
      console.log('📤 Download do GCS:', solicitacao.arquivo_pdf.url_gcs);
      
      // Para download forçado, adicionar parâmetros de download na URL
      const downloadUrl = `${solicitacao.arquivo_pdf.url_gcs}?response-content-disposition=attachment%3B%20filename%3D"${encodeURIComponent(fileName)}"`;
      
      console.log('🔗 Redirecionando para download:', downloadUrl);
      res.redirect(downloadUrl);
      
    } else if (solicitacao.arquivo_pdf.caminho_arquivo && fs.existsSync(solicitacao.arquivo_pdf.caminho_arquivo)) {
      // Estrutura antiga: arquivo local ainda existe
      const filePath = solicitacao.arquivo_pdf.caminho_arquivo;
      
      console.log('📁 Download de arquivo local:', filePath);
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.sendFile(path.resolve(filePath));
      
    } else {
      console.log('❌ Arquivo PDF não encontrado no GCS nem localmente');
      return res.status(404).json({
        success: false,
        message: 'Arquivo PDF não encontrado'
      });
    }

  } catch (error) {
    console.error('❌ Erro ao fazer download do arquivo PDF:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Rota para acionar a impressão teste
/*app.post('/print-test', async (req, res) => {
    const { companyId } = req.body;
    const companyIdStr = companyId.toString().trim(); // Garantindo que seja uma string sem espaços

    try {
        // Verifique se a sala tem clientes conectados
        const rooms = io.sockets.adapter.rooms;
        console.log("rooms>>>>>>>>>", Array.from(rooms.keys())); // Log para verificar todas as salas
        if (!rooms.has(companyIdStr)) {
            console.log(`Nenhuma sala encontrada com o companyId: ${companyIdStr}`);
            return res.status(404).json({ message: 'Nenhum cliente encontrado para o companyId' });
        }

        // Envie o evento para o cliente Electron que está na sala da empresa
        console.log(`Emitindo evento printTest para a sala ${companyIdStr}`);
        io.to(companyIdStr).emit('printTest');

        res.status(200).json({ message: 'Pedido de impressão enviado' });
    } catch (error) {
        console.error('Erro ao processar a impressão:', error);
        res.status(500).json({ message: 'Erro ao processar a impressão' });
    }
});*/

// ===== NOVAS ROTAS PARA CUSTOM RESPONSES =====

// Rota para listar respostas personalizadas da empresa
app.get("/custom-responses/:companyId", checkToken, async (req, res) => {
    const companyId = req.params.companyId;

    // Verifica se o companyId é um ObjectId válido do MongoDB
    if (!companyId.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }

    try {
        // Busca a empresa pelo ObjectId
        const empresa = await Empresa.findById(companyId).select('customResponses');
        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada!" });
        }

        // Retorna as respostas personalizadas da empresa
        return res.status(200).json({ customResponses: empresa.customResponses || [] });
    } catch (error) {
        console.error("Erro ao buscar respostas personalizadas:", error);
        return res.status(500).json({ msg: "Erro ao buscar respostas personalizadas!" });
    }
});

// Rota para adicionar nova resposta personalizada
app.post("/custom-responses/:companyId", checkToken, async (req, res) => {
    const companyId = req.params.companyId;
    const { question, response } = req.body;

    // Validações
    if (!companyId.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }

    if (!question || !response) {
        return res.status(400).json({ msg: "Pergunta e resposta são obrigatórias!" });
    }

    try {
        // Cria o objeto da nova resposta personalizada
        const newCustomResponse = {
            id: new mongoose.Types.ObjectId().toString(),
            question: question.trim(),
            response: response.trim(),
            active: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        // Atualiza a empresa adicionando a nova resposta
        const empresa = await Empresa.findByIdAndUpdate(
            companyId,
            { $push: { customResponses: newCustomResponse } },
            { new: true }
        ).select('customResponses');

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada!" });
        }

        return res.status(201).json({
            msg: "Resposta personalizada adicionada com sucesso!",
            customResponses: empresa.customResponses
        });
    } catch (error) {
        console.error("Erro ao adicionar resposta personalizada:", error);
        return res.status(500).json({ msg: "Erro ao adicionar resposta personalizada!" });
    }
});

// Rota para atualizar resposta personalizada
app.put("/custom-responses/:companyId/:responseId", checkToken, async (req, res) => {
    const companyId = req.params.companyId;
    const responseId = req.params.responseId;
    const { question, response, active } = req.body;

    // Validações
    if (!companyId.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }

    if (!responseId) {
        return res.status(400).json({ msg: "ID da resposta é obrigatório!" });
    }

    if (!question || !response) {
        return res.status(400).json({ msg: "Pergunta e resposta são obrigatórias!" });
    }

    try {
        // Busca a empresa e atualiza a resposta específica
        const empresa = await Empresa.findOneAndUpdate(
            {
                _id: companyId,
                "customResponses.id": responseId
            },
            {
                $set: {
                    "customResponses.$.question": question.trim(),
                    "customResponses.$.response": response.trim(),
                    "customResponses.$.active": active !== undefined ? active : true,
                    "customResponses.$.updatedAt": new Date()
                }
            },
            { new: true }
        ).select('customResponses');

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa ou resposta não encontrada!" });
        }

        return res.status(200).json({
            msg: "Resposta personalizada atualizada com sucesso!",
            customResponses: empresa.customResponses
        });
    } catch (error) {
        console.error("Erro ao atualizar resposta personalizada:", error);
        return res.status(500).json({ msg: "Erro ao atualizar resposta personalizada!" });
    }
});

// Rota para deletar resposta personalizada
app.delete("/custom-responses/:companyId/:responseId", checkToken, async (req, res) => {
    const companyId = req.params.companyId;
    const responseId = req.params.responseId;

    // Validações
    if (!companyId.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(400).json({ msg: "Parâmetros inválidos!" });
    }

    if (!responseId) {
        return res.status(400).json({ msg: "ID da resposta é obrigatório!" });
    }

    try {
        // Remove a resposta pelo ID
        const empresa = await Empresa.findByIdAndUpdate(
            companyId,
            { $pull: { customResponses: { id: responseId } } },
            { new: true }
        ).select('customResponses');

        if (!empresa) {
            return res.status(404).json({ msg: "Empresa não encontrada!" });
        }

        return res.status(200).json({
            msg: "Resposta personalizada removida com sucesso!",
            customResponses: empresa.customResponses
        });
    } catch (error) {
        console.error("Erro ao remover resposta personalizada:", error);
        return res.status(500).json({ msg: "Erro ao remover resposta personalizada!" });
    }
});

// Credenciais
const DB_URI = process.env.DB_URI;
const PORT = process.env.PORT;

server.listen(PORT ?? 5000, "0.0.0.0", () => {
    console.log(`Servidor rodando na porta ${PORT ?? 5000}`);
});

mongoose.set("strictQuery", true);
mongoose
    .connect(DB_URI)
    .then(async () => {
        //app.listen(5000)
        console.log("Conectou ao banco!");

        // Executar o script de atualização apenas uma vez na inicialização
        await updateExistingCompaniesCallAtendente();

        console.log('✅ Sistema iniciado sem RabbitMQ - processamento síncrono');
    })
    .catch((err) => console.log(err));
