{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\roboConfig\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport './style.css';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport LeftMenu from \"../../components/LeftMenu\";\nimport { Modal } from \"../../components/Modal\";\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\nimport styled from 'styled-components';\nimport { FaEdit, FaTrash, FaPlus, FaSearch } from \"react-icons/fa\";\nimport { IoMdClose } from \"react-icons/io\";\nimport CryptoJS from 'crypto-js';\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RoboConfigContainer = styled.div`\n    display: flex;\n    margin-left: ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'};\n    height: auto;\n    width: auto;\n    transition: 150ms;\n    background-color: rgb(247,247,247) !important;\n    overflow: initial;\n    z-index: 9;\n\n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\nconst RoboCfg = () => {\n  _s();\n  var _selectedResponse2, _selectedResponse3, _selectedResponse4, _selectedResponse5;\n  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\n  const userEncrypted = localStorage.getItem('user');\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  const userParse = JSON.parse(user);\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const empresaObjId = empresaParse._id;\n  const {\n    sidebar\n  } = useContext(SidebarContext);\n  const navigate = useNavigate();\n  const [customResponses, setCustomResponses] = useState([]);\n  const [filteredResponses, setFilteredResponses] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isAddModalOpen, setIsAddModalOpen] = useState(false);\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false);\n  const [editingIndex, setEditingIndex] = useState(null);\n  const [editingData, setEditingData] = useState({\n    question: '',\n    response: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(5);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [deleteIndex, setDeleteIndex] = useState(null);\n\n  // Carregar respostas personalizadas\n  useEffect(() => {\n    fetchCustomResponses();\n  }, []);\n\n  // Filtrar respostas baseado na busca\n  useEffect(() => {\n    if (searchTerm) {\n      const filtered = customResponses.filter(response => response.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredResponses(filtered);\n    } else {\n      setFilteredResponses(customResponses);\n    }\n  }, [searchTerm, customResponses]);\n  const fetchCustomResponses = async () => {\n    try {\n      setLoading(true);\n      const response = await getCustomResponses(empresaObjId);\n      setCustomResponses(response.data.customResponses || []);\n    } catch (error) {\n      console.error(\"Erro ao buscar respostas personalizadas:\", error);\n      toast.error(\"Erro ao carregar respostas personalizadas\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Função para extrair pergunta e resposta da string\n  const parseCustomResponse = responseString => {\n    const match = responseString.match(/quando perguntarem sobre (.+?): (.+)/);\n    if (match) {\n      return {\n        question: match[1],\n        response: match[2]\n      };\n    }\n    return {\n      question: '',\n      response: responseString\n    };\n  };\n  const handleAddResponse = async (values, {\n    setSubmitting,\n    resetForm\n  }) => {\n    try {\n      await addCustomResponse(empresaObjId, values.question, values.response);\n      toast.success(\"Resposta personalizada adicionada com sucesso!\");\n      setIsAddModalOpen(false);\n      resetForm();\n      fetchCustomResponses();\n    } catch (error) {\n      console.error(\"Erro ao adicionar resposta:\", error);\n      toast.error(\"Erro ao adicionar resposta personalizada\");\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEditResponse = async (values, {\n    setSubmitting\n  }) => {\n    try {\n      await updateCustomResponse(empresaObjId, editingIndex, values.question, values.response);\n      toast.success(\"Resposta personalizada atualizada com sucesso!\");\n      setIsEditModalOpen(false);\n      setEditingIndex(null);\n      setEditingData({\n        question: '',\n        response: ''\n      });\n      fetchCustomResponses();\n    } catch (error) {\n      console.error(\"Erro ao atualizar resposta:\", error);\n      toast.error(\"Erro ao atualizar resposta personalizada\");\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  // Função genérica para inserir texto no cursor\n  const insertAtCursor = (textarea, text) => {\n    if (textarea) {\n      const startPos = textarea.selectionStart;\n      const endPos = textarea.selectionEnd;\n      const value = textarea.value;\n\n      // Atualiza o texto no campo com o novo valor\n      textarea.value = value.substring(0, startPos) + text + value.substring(endPos);\n\n      // Mantém o cursor após o texto inserido\n      textarea.selectionStart = textarea.selectionEnd = startPos + text.length;\n    }\n  };\n\n  // Funções específicas para cada botão\n  const handleInsertNomeCliente = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"{nome_cliente}\");\n  };\n  const handleInsertLinkCardapio = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"{link}\");\n  };\n  const handleInsertSaudacao = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"Agradecemos a preferência\");\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: /*#__PURE__*/_jsxDEV(Teste, {\n        sidebar: sidebar,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-100 p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-header\",\n              style: {\n                marginBottom: \"0px\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"title\",\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  children: \"Configura\\xE7\\xF5es do Rob\\xF4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                justifyContent: \"right\" /*, height:\"80px\"*/\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contentItemComplete flex-column flex-md-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group inputGroup-etapasItem\",\n                style: {\n                  height: 50\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: tela === \"tela1\" ? \"etapasAddItem etapaAtiva\" : \"etapasAddItem\",\n                  style: {\n                    borderBottom: '1px solid lightgray'\n                  },\n                  onClick: () => setTela(\"tela1\"),\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"1. Personalizar Mensagens\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mh-100\",\n                style: {\n                  maxWidth: \"80%\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"formGroupRow\",\n                  children: tela === \"tela1\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"roboCfg-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"roboCfg-sidebar\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        children: \"Mensagens\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 216,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"roboCfg-message-list\",\n                        children: companyResponses.map((message, index) => {\n                          var _selectedResponse;\n                          return /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: `roboCfg-message-item ${((_selectedResponse = selectedResponse) === null || _selectedResponse === void 0 ? void 0 : _selectedResponse.questionIdentifier) === message.questionIdentifier ? 'roboCfg-selected' : ''}`,\n                            onClick: () => handleSelectQuestionAndAnswer(message),\n                            children: [message.questionType, /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"checkBoxContentMsg\",\n                              children: [message.active ? /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"roboCfg-edit-button-ativo\",\n                                children: \"Ativo\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 227,\n                                columnNumber: 73\n                              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"roboCfg-edit-button-inativo\",\n                                children: \"Inativo\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 229,\n                                columnNumber: 73\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"wrapper\",\n                                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"switch_box box_1\",\n                                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                                    type: \"checkbox\",\n                                    className: \"switch_1\",\n                                    checked: message.active || false\n                                    // Dentro do checkbox\n                                    ,\n                                    onChange: e => {\n                                      const isActive = e.target.checked;\n\n                                      // Chamar a função debounce para evitar múltiplas chamadas ao backend\n                                      debouncedUpdateQuestionActive(empresaObjId, message.questionIdentifier, isActive, setCompanyResponses, companyResponses);\n                                    }\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 233,\n                                    columnNumber: 77\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 232,\n                                  columnNumber: 73\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 231,\n                                columnNumber: 69\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 225,\n                              columnNumber: 65\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 219,\n                            columnNumber: 61\n                          }, this);\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"roboCfg-main\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"roboCfg-preview\",\n                        style: {\n                          backgroundImage: `url(${backgroundWhatsApp})`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-chat\",\n                          children: [((_selectedResponse2 = selectedResponse) === null || _selectedResponse2 === void 0 ? void 0 : _selectedResponse2.question) && /*#__PURE__*/_jsxDEV(Message, {\n                            fromMe: false,\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              children: (_selectedResponse3 = selectedResponse) === null || _selectedResponse3 === void 0 ? void 0 : _selectedResponse3.question\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 258,\n                              columnNumber: 69\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"12:00\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 259,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 257,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(Message, {\n                            fromMe: true,\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              children: (_selectedResponse4 = selectedResponse) === null || _selectedResponse4 === void 0 ? void 0 : _selectedResponse4.response\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 265,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"12:01\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 266,\n                              columnNumber: 65\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 264,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 254,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 253,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"roboCfg-editor\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Personalize a mensagem\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 271,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                          className: \"roboCfg-textarea\",\n                          value: ((_selectedResponse5 = selectedResponse) === null || _selectedResponse5 === void 0 ? void 0 : _selectedResponse5.response) || \"\",\n                          onChange: handleResponseChange // Altera diretamente o response do selectedResponse\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 272,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-buttons\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-button\",\n                            type: \"button\",\n                            onClick: handleInsertNomeCliente // Chama a função para Nome do cliente\n                            ,\n                            children: \"Nome do cliente\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 278,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-button\",\n                            type: \"button\",\n                            onClick: handleInsertLinkCardapio // Chama a função para Link do cardápio\n                            ,\n                            children: \"Link do card\\xE1pio\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 285,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 277,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-save-cancel\",\n                          children: /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-save-button\",\n                            type: \"button\",\n                            onClick: handleSaveResponse,\n                            children: \"Salvar\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 302,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 301,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 270,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(RoboCfg, \"waM1wUoBa4U7D0dOyCO753JRUrA=\", false, function () {\n  return [useNavigate];\n});\n_c = RoboCfg;\nexport default RoboCfg;\nvar _c;\n$RefreshReg$(_c, \"RoboCfg\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "SidebarContext", "PermissionGate", "LeftMenu", "Modal", "ModalAddCustomResponse", "ModalEditCustomResponse", "styled", "FaEdit", "FaTrash", "FaPlus", "FaSearch", "IoMdClose", "CryptoJS", "getCustomResponses", "addCustomResponse", "updateCustomResponse", "deleteCustomResponse", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RoboConfigContainer", "div", "sidebar", "RoboCfg", "_s", "_selectedResponse2", "_selectedResponse3", "_selectedResponse4", "_selectedResponse5", "secret<PERSON>ey", "userEncrypted", "localStorage", "getItem", "user", "AES", "decrypt", "toString", "enc", "Utf8", "userParse", "JSON", "parse", "empresa", "empresaParse", "empresaObjId", "_id", "navigate", "customResponses", "setCustomResponses", "filteredResponses", "setFilteredResponses", "searchTerm", "setSearchTerm", "isAddModalOpen", "setIsAddModalOpen", "isEditModalOpen", "setIsEditModalOpen", "editingIndex", "setEditingIndex", "editingData", "setEditingData", "question", "response", "loading", "setLoading", "currentPage", "setCurrentPage", "itemsPerPage", "showDeleteConfirm", "setShowDeleteConfirm", "deleteIndex", "setDeleteIndex", "fetchCustomResponses", "filtered", "filter", "toLowerCase", "includes", "data", "error", "console", "parseCustomResponse", "responseString", "match", "handleAddResponse", "values", "setSubmitting", "resetForm", "success", "handleEditResponse", "insertAtCursor", "textarea", "text", "startPos", "selectionStart", "endPos", "selectionEnd", "value", "substring", "length", "handleInsertNomeCliente", "document", "querySelector", "handleInsertLinkCardapio", "handleInsertSaudacao", "children", "permissions", "<PERSON>e", "className", "style", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "height", "tela", "borderBottom", "onClick", "<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "companyResponses", "map", "message", "index", "_selectedResponse", "selectedResponse", "questionIdentifier", "handleSelectQuestionAndAnswer", "questionType", "active", "type", "checked", "onChange", "e", "isActive", "target", "debouncedUpdateQuestionActive", "setCompanyResponses", "backgroundImage", "backgroundWhatsApp", "Message", "fromMe", "handleResponseChange", "handleSaveResponse", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/roboConfig/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport './style.css';\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\nimport { Modal } from \"../../components/Modal\";\r\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\r\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\r\nimport styled from 'styled-components';\r\nimport { FaEdit, FaTrash, FaPlus, FaSearch } from \"react-icons/fa\";\r\nimport { IoMdClose } from \"react-icons/io\";\r\nimport CryptoJS from 'crypto-js';\r\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst RoboConfigContainer = styled.div`\r\n    display: flex;\r\n    margin-left: ${({ sidebar }) => (sidebar ? '250px' : '100px')};\r\n    height: auto;\r\n    width: auto;\r\n    transition: 150ms;\r\n    background-color: rgb(247,247,247) !important;\r\n    overflow: initial;\r\n    z-index: 9;\r\n\r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\nconst RoboCfg = () => {\r\n    const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\r\n    const userEncrypted = localStorage.getItem('user');\r\n    const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\r\n    const userParse = JSON.parse(user);\r\n    const empresa = localStorage.getItem('empresa');\r\n    const empresaParse = JSON.parse(empresa);\r\n    const empresaObjId = empresaParse._id;\r\n\r\n    const { sidebar } = useContext(SidebarContext);\r\n    const navigate = useNavigate();\r\n\r\n    const [customResponses, setCustomResponses] = useState([]);\r\n    const [filteredResponses, setFilteredResponses] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [isAddModalOpen, setIsAddModalOpen] = useState(false);\r\n    const [isEditModalOpen, setIsEditModalOpen] = useState(false);\r\n    const [editingIndex, setEditingIndex] = useState(null);\r\n    const [editingData, setEditingData] = useState({ question: '', response: '' });\r\n    const [loading, setLoading] = useState(false);\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [itemsPerPage] = useState(5);\r\n    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\r\n    const [deleteIndex, setDeleteIndex] = useState(null);\r\n\r\n    // Carregar respostas personalizadas\r\n    useEffect(() => {\r\n        fetchCustomResponses();\r\n    }, []);\r\n\r\n    // Filtrar respostas baseado na busca\r\n    useEffect(() => {\r\n        if (searchTerm) {\r\n            const filtered = customResponses.filter(response =>\r\n                response.toLowerCase().includes(searchTerm.toLowerCase())\r\n            );\r\n            setFilteredResponses(filtered);\r\n        } else {\r\n            setFilteredResponses(customResponses);\r\n        }\r\n    }, [searchTerm, customResponses]);\r\n\r\n    const fetchCustomResponses = async () => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await getCustomResponses(empresaObjId);\r\n            setCustomResponses(response.data.customResponses || []);\r\n        } catch (error) {\r\n            console.error(\"Erro ao buscar respostas personalizadas:\", error);\r\n            toast.error(\"Erro ao carregar respostas personalizadas\");\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // Função para extrair pergunta e resposta da string\r\n    const parseCustomResponse = (responseString) => {\r\n        const match = responseString.match(/quando perguntarem sobre (.+?): (.+)/);\r\n        if (match) {\r\n            return {\r\n                question: match[1],\r\n                response: match[2]\r\n            };\r\n        }\r\n        return { question: '', response: responseString };\r\n    };\r\n\r\n    const handleAddResponse = async (values, { setSubmitting, resetForm }) => {\r\n        try {\r\n            await addCustomResponse(empresaObjId, values.question, values.response);\r\n            toast.success(\"Resposta personalizada adicionada com sucesso!\");\r\n            setIsAddModalOpen(false);\r\n            resetForm();\r\n            fetchCustomResponses();\r\n        } catch (error) {\r\n            console.error(\"Erro ao adicionar resposta:\", error);\r\n            toast.error(\"Erro ao adicionar resposta personalizada\");\r\n        } finally {\r\n            setSubmitting(false);\r\n        }\r\n    };\r\n\r\n    const handleEditResponse = async (values, { setSubmitting }) => {\r\n        try {\r\n            await updateCustomResponse(empresaObjId, editingIndex, values.question, values.response);\r\n            toast.success(\"Resposta personalizada atualizada com sucesso!\");\r\n            setIsEditModalOpen(false);\r\n            setEditingIndex(null);\r\n            setEditingData({ question: '', response: '' });\r\n            fetchCustomResponses();\r\n        } catch (error) {\r\n            console.error(\"Erro ao atualizar resposta:\", error);\r\n            toast.error(\"Erro ao atualizar resposta personalizada\");\r\n        } finally {\r\n            setSubmitting(false);\r\n        }\r\n    };\r\n\r\n\r\n    // Função genérica para inserir texto no cursor\r\n    const insertAtCursor = (textarea, text) => {\r\n        if (textarea) {\r\n            const startPos = textarea.selectionStart;\r\n            const endPos = textarea.selectionEnd;\r\n            const value = textarea.value;\r\n\r\n            // Atualiza o texto no campo com o novo valor\r\n            textarea.value = value.substring(0, startPos) + text + value.substring(endPos);\r\n\r\n            // Mantém o cursor após o texto inserido\r\n            textarea.selectionStart = textarea.selectionEnd = startPos + text.length;\r\n        }\r\n    };\r\n\r\n    // Funções específicas para cada botão\r\n    const handleInsertNomeCliente = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"{nome_cliente}\");\r\n    };\r\n\r\n    const handleInsertLinkCardapio = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"{link}\");\r\n    };\r\n\r\n    const handleInsertSaudacao = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"Agradecemos a preferência\");\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <PermissionGate permissions={['default']}>\r\n\r\n                <Teste sidebar={sidebar}>\r\n                    <div className=\"w-100 p-4\">\r\n                        {/*<form onSubmit={handleSubmitButton}>*/}\r\n                        <form /*onSubmit={formik.handleSubmit}*/ >\r\n\r\n                            <div className=\"form-header\" style={{ marginBottom: \"0px\" }}>\r\n                                <div className=\"title\">\r\n                                    <h1>Configurações do Robô</h1>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div style={{ display: \"flex\", justifyContent: \"right\"/*, height:\"80px\"*/ }}>\r\n\r\n                                {/*<div className=\"div-buttons\">\r\n                                    <div className=\"continue-button\">\r\n                                        {tela === \"tela1\" ?\r\n                                            <button type=\"button\" onClick={saveChanges} disabled={isSubmitting}>\r\n                                                <AiIcons.AiOutlineSave style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Salvar</a>\r\n                                            </button>\r\n                                            :\r\n                                            <button type=\"button\" disabled={isSubmitting}>\r\n                                                <AiIcons.AiOutlineSave style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Salvar</a>\r\n                                            </button>\r\n                                        }\r\n                                    </div>\r\n\r\n                                    <div className=\"back-button\">\r\n                                        <button onClick={handleBack}>\r\n                                            <SlIcons.SlActionUndo style={{ color: \"#ff4c4c\", marginRight: \"5px\", fontSize: \"18px\", marginBottom: \"2px\" }} /><a >Voltar</a>\r\n                                        </button>\r\n                                    </div>\r\n                                </div>*/}\r\n\r\n                            </div>\r\n\r\n                            <div className=\"contentItemComplete flex-column flex-md-row\">\r\n                                <div className=\"input-group inputGroup-etapasItem\" style={{ height: 50 }}>\r\n                                    <div className={tela === \"tela1\" ? \"etapasAddItem etapaAtiva\" : \"etapasAddItem\"}\r\n                                        style={{ borderBottom: '1px solid lightgray' }} onClick={() => setTela(\"tela1\")}\r\n                                    >\r\n                                        <label>1. Personalizar Mensagens</label>\r\n                                    </div>                                   \r\n                                </div>\r\n\r\n                                <div className=\"input-group mh-100\" style={{ maxWidth: \"80%\" }}>\r\n\r\n                                    <div className=\"formGroupRow\">\r\n                                        {tela === \"tela1\" &&\r\n                                            <div className=\"roboCfg-container\">\r\n                                                <div className=\"roboCfg-sidebar\">\r\n                                                    <h3>Mensagens</h3>\r\n                                                    <ul className=\"roboCfg-message-list\">\r\n                                                        {companyResponses.map((message, index) => (\r\n                                                            <li\r\n                                                                key={index}\r\n                                                                className={`roboCfg-message-item ${selectedResponse?.questionIdentifier === message.questionIdentifier ? 'roboCfg-selected' : ''}`}\r\n                                                                onClick={() => handleSelectQuestionAndAnswer(message)}\r\n                                                            >\r\n                                                                {message.questionType}\r\n                                                                <div className=\"checkBoxContentMsg\">\r\n                                                                    {message.active ? \r\n                                                                        <div className=\"roboCfg-edit-button-ativo\">Ativo</div> \r\n                                                                        : \r\n                                                                        <div className=\"roboCfg-edit-button-inativo\">Inativo</div>\r\n                                                                    }\r\n                                                                    <div className=\"wrapper\">\r\n                                                                        <div className=\"switch_box box_1\">\r\n                                                                            <input\r\n                                                                                type=\"checkbox\"\r\n                                                                                className=\"switch_1\"\r\n                                                                                checked={message.active || false}\r\n                                                                                // Dentro do checkbox\r\n                                                                                onChange={(e) => {\r\n                                                                                    const isActive = e.target.checked;\r\n                                                                                \r\n                                                                                    // Chamar a função debounce para evitar múltiplas chamadas ao backend\r\n                                                                                    debouncedUpdateQuestionActive(empresaObjId, message.questionIdentifier, isActive, setCompanyResponses, companyResponses);\r\n                                                                                }}\r\n                                                                            />\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </li>\r\n                                                        ))}\r\n                                                    </ul>\r\n                                                </div>\r\n                                                <div className=\"roboCfg-main\">\r\n                                                    <div className=\"roboCfg-preview\" style={{ backgroundImage: `url(${backgroundWhatsApp})` }}>\r\n                                                        <div className=\"roboCfg-chat\">\r\n\r\n                                                            {selectedResponse?.question &&\r\n                                                                <Message fromMe={false}>\r\n                                                                    <p>{selectedResponse?.question}</p>\r\n                                                                    <span>12:00</span>\r\n                                                                </Message>\r\n                                                            }\r\n\r\n\r\n                                                            <Message fromMe={true}>\r\n                                                                <p>{selectedResponse?.response}</p>\r\n                                                                <span>12:01</span>\r\n                                                            </Message>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div className=\"roboCfg-editor\">\r\n                                                        <h4>Personalize a mensagem</h4>\r\n                                                        <textarea\r\n                                                            className=\"roboCfg-textarea\"\r\n                                                            value={selectedResponse?.response || \"\"}\r\n                                                            onChange={handleResponseChange} // Altera diretamente o response do selectedResponse\r\n                                                        />\r\n                                                        <div className=\"roboCfg-buttons\">\r\n                                                            <button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertNomeCliente} // Chama a função para Nome do cliente\r\n                                                            >\r\n                                                                Nome do cliente\r\n                                                            </button>\r\n                                                            <button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertLinkCardapio} // Chama a função para Link do cardápio\r\n                                                            >\r\n                                                                Link do cardápio\r\n                                                            </button>\r\n                                                            {/*<button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertSaudacao} // Chama a função para Saudação\r\n                                                            >\r\n                                                                Saudação\r\n                                                            </button>*/}\r\n                                                        </div>\r\n\r\n                                                        <div className=\"roboCfg-save-cancel\">\r\n                                                            <button className=\"roboCfg-save-button\" type=\"button\" onClick={handleSaveResponse}>Salvar</button>\r\n                                                            {/*<button className=\"roboCfg-cancel-button\">Cancelar</button>*/}\r\n                                                        </div>\r\n\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        }\r\n                                        {/*tela === \"tela2\" && (\r\n                                            <div>NADA AQUI POR ENQUANTO</div>\r\n                                        )*/}\r\n                                    </div>\r\n\r\n                                </div>\r\n\r\n\r\n\r\n                            </div>\r\n\r\n                        </form>\r\n                    </div>\r\n                </Teste>\r\n\r\n            </PermissionGate>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default RoboCfg;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,aAAa;AACpB,SAASC,cAAc,QAAQ,iBAAiB;AAChD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,OAAOC,sBAAsB,MAAM,yCAAyC;AAC5E,OAAOC,uBAAuB,MAAM,0CAA0C;AAC9E,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAClE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,QAAQ,MAAM,WAAW;AAChC,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,oBAAoB;AACtH,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,mBAAmB,GAAGhB,MAAM,CAACiB,GAAG;AACtC;AACA,mBAAmB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;EAClB,MAAMC,SAAS,GAAG,uFAAuF;EACzG,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMC,IAAI,GAAGvB,QAAQ,CAACwB,GAAG,CAACC,OAAO,CAACL,aAAa,EAAED,SAAS,CAAC,CAACO,QAAQ,CAAC1B,QAAQ,CAAC2B,GAAG,CAACC,IAAI,CAAC;EACvF,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACR,IAAI,CAAC;EAClC,MAAMS,OAAO,GAAGX,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMW,YAAY,GAAGH,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC;EACxC,MAAME,YAAY,GAAGD,YAAY,CAACE,GAAG;EAErC,MAAM;IAAEvB;EAAQ,CAAC,GAAG3B,UAAU,CAACG,cAAc,CAAC;EAC9C,MAAMgD,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC;IAAEmE,QAAQ,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyE,YAAY,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAClC,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACAE,SAAS,CAAC,MAAM;IACZ4E,oBAAoB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5E,SAAS,CAAC,MAAM;IACZ,IAAIuD,UAAU,EAAE;MACZ,MAAMsB,QAAQ,GAAG1B,eAAe,CAAC2B,MAAM,CAACZ,QAAQ,IAC5CA,QAAQ,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,CAAC,CAAC,CAC5D,CAAC;MACDzB,oBAAoB,CAACuB,QAAQ,CAAC;IAClC,CAAC,MAAM;MACHvB,oBAAoB,CAACH,eAAe,CAAC;IACzC;EACJ,CAAC,EAAE,CAACI,UAAU,EAAEJ,eAAe,CAAC,CAAC;EAEjC,MAAMyB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACAR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMF,QAAQ,GAAG,MAAMnD,kBAAkB,CAACiC,YAAY,CAAC;MACvDI,kBAAkB,CAACc,QAAQ,CAACe,IAAI,CAAC9B,eAAe,IAAI,EAAE,CAAC;IAC3D,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE/D,KAAK,CAAC+D,KAAK,CAAC,2CAA2C,CAAC;IAC5D,CAAC,SAAS;MACNd,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMgB,mBAAmB,GAAIC,cAAc,IAAK;IAC5C,MAAMC,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,sCAAsC,CAAC;IAC1E,IAAIA,KAAK,EAAE;MACP,OAAO;QACHrB,QAAQ,EAAEqB,KAAK,CAAC,CAAC,CAAC;QAClBpB,QAAQ,EAAEoB,KAAK,CAAC,CAAC;MACrB,CAAC;IACL;IACA,OAAO;MAAErB,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAEmB;IAAe,CAAC;EACrD,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,EAAE;IAAEC,aAAa;IAAEC;EAAU,CAAC,KAAK;IACtE,IAAI;MACA,MAAM1E,iBAAiB,CAACgC,YAAY,EAAEwC,MAAM,CAACvB,QAAQ,EAAEuB,MAAM,CAACtB,QAAQ,CAAC;MACvE/C,KAAK,CAACwE,OAAO,CAAC,gDAAgD,CAAC;MAC/DjC,iBAAiB,CAAC,KAAK,CAAC;MACxBgC,SAAS,CAAC,CAAC;MACXd,oBAAoB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOM,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD/D,KAAK,CAAC+D,KAAK,CAAC,0CAA0C,CAAC;IAC3D,CAAC,SAAS;MACNO,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;EAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAOJ,MAAM,EAAE;IAAEC;EAAc,CAAC,KAAK;IAC5D,IAAI;MACA,MAAMxE,oBAAoB,CAAC+B,YAAY,EAAEa,YAAY,EAAE2B,MAAM,CAACvB,QAAQ,EAAEuB,MAAM,CAACtB,QAAQ,CAAC;MACxF/C,KAAK,CAACwE,OAAO,CAAC,gDAAgD,CAAC;MAC/D/B,kBAAkB,CAAC,KAAK,CAAC;MACzBE,eAAe,CAAC,IAAI,CAAC;MACrBE,cAAc,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC,CAAC;MAC9CU,oBAAoB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOM,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD/D,KAAK,CAAC+D,KAAK,CAAC,0CAA0C,CAAC;IAC3D,CAAC,SAAS;MACNO,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;;EAGD;EACA,MAAMI,cAAc,GAAGA,CAACC,QAAQ,EAAEC,IAAI,KAAK;IACvC,IAAID,QAAQ,EAAE;MACV,MAAME,QAAQ,GAAGF,QAAQ,CAACG,cAAc;MACxC,MAAMC,MAAM,GAAGJ,QAAQ,CAACK,YAAY;MACpC,MAAMC,KAAK,GAAGN,QAAQ,CAACM,KAAK;;MAE5B;MACAN,QAAQ,CAACM,KAAK,GAAGA,KAAK,CAACC,SAAS,CAAC,CAAC,EAAEL,QAAQ,CAAC,GAAGD,IAAI,GAAGK,KAAK,CAACC,SAAS,CAACH,MAAM,CAAC;;MAE9E;MACAJ,QAAQ,CAACG,cAAc,GAAGH,QAAQ,CAACK,YAAY,GAAGH,QAAQ,GAAGD,IAAI,CAACO,MAAM;IAC5E;EACJ,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMT,QAAQ,GAAGU,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DZ,cAAc,CAACC,QAAQ,EAAE,gBAAgB,CAAC;EAC9C,CAAC;EAED,MAAMY,wBAAwB,GAAGA,CAAA,KAAM;IACnC,MAAMZ,QAAQ,GAAGU,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DZ,cAAc,CAACC,QAAQ,EAAE,QAAQ,CAAC;EACtC,CAAC;EAED,MAAMa,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,MAAMb,QAAQ,GAAGU,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DZ,cAAc,CAACC,QAAQ,EAAE,2BAA2B,CAAC;EACzD,CAAC;EAED,oBACIzE,OAAA,CAAAE,SAAA;IAAAqF,QAAA,eACIvF,OAAA,CAAClB,cAAc;MAAC0G,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAD,QAAA,eAErCvF,OAAA,CAACyF,KAAK;QAACpF,OAAO,EAAEA,OAAQ;QAAAkF,QAAA,eACpBvF,OAAA;UAAK0F,SAAS,EAAC,WAAW;UAAAH,QAAA,eAEtBvF,OAAA;YAAAuF,QAAA,gBAEIvF,OAAA;cAAK0F,SAAS,EAAC,aAAa;cAACC,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAL,QAAA,eACxDvF,OAAA;gBAAK0F,SAAS,EAAC,OAAO;gBAAAH,QAAA,eAClBvF,OAAA;kBAAAuF,QAAA,EAAI;gBAAqB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENhG,OAAA;cAAK2F,KAAK,EAAE;gBAAEM,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,OAAO;cAAoB;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBvE,CAAC,eAENhG,OAAA;cAAK0F,SAAS,EAAC,6CAA6C;cAAAH,QAAA,gBACxDvF,OAAA;gBAAK0F,SAAS,EAAC,mCAAmC;gBAACC,KAAK,EAAE;kBAAEQ,MAAM,EAAE;gBAAG,CAAE;gBAAAZ,QAAA,eACrEvF,OAAA;kBAAK0F,SAAS,EAAEU,IAAI,KAAK,OAAO,GAAG,0BAA0B,GAAG,eAAgB;kBAC5ET,KAAK,EAAE;oBAAEU,YAAY,EAAE;kBAAsB,CAAE;kBAACC,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAAC,OAAO,CAAE;kBAAAhB,QAAA,eAEhFvF,OAAA;oBAAAuF,QAAA,EAAO;kBAAyB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENhG,OAAA;gBAAK0F,SAAS,EAAC,oBAAoB;gBAACC,KAAK,EAAE;kBAAEa,QAAQ,EAAE;gBAAM,CAAE;gBAAAjB,QAAA,eAE3DvF,OAAA;kBAAK0F,SAAS,EAAC,cAAc;kBAAAH,QAAA,EACxBa,IAAI,KAAK,OAAO,iBACbpG,OAAA;oBAAK0F,SAAS,EAAC,mBAAmB;oBAAAH,QAAA,gBAC9BvF,OAAA;sBAAK0F,SAAS,EAAC,iBAAiB;sBAAAH,QAAA,gBAC5BvF,OAAA;wBAAAuF,QAAA,EAAI;sBAAS;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClBhG,OAAA;wBAAI0F,SAAS,EAAC,sBAAsB;wBAAAH,QAAA,EAC/BkB,gBAAgB,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK;0BAAA,IAAAC,iBAAA;0BAAA,oBACjC7G,OAAA;4BAEI0F,SAAS,EAAE,wBAAwB,EAAAmB,iBAAA,GAAAC,gBAAgB,cAAAD,iBAAA,uBAAhBA,iBAAA,CAAkBE,kBAAkB,MAAKJ,OAAO,CAACI,kBAAkB,GAAG,kBAAkB,GAAG,EAAE,EAAG;4BACnIT,OAAO,EAAEA,CAAA,KAAMU,6BAA6B,CAACL,OAAO,CAAE;4BAAApB,QAAA,GAErDoB,OAAO,CAACM,YAAY,eACrBjH,OAAA;8BAAK0F,SAAS,EAAC,oBAAoB;8BAAAH,QAAA,GAC9BoB,OAAO,CAACO,MAAM,gBACXlH,OAAA;gCAAK0F,SAAS,EAAC,2BAA2B;gCAAAH,QAAA,EAAC;8BAAK;gCAAAM,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,gBAEtDhG,OAAA;gCAAK0F,SAAS,EAAC,6BAA6B;gCAAAH,QAAA,EAAC;8BAAO;gCAAAM,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eAE9DhG,OAAA;gCAAK0F,SAAS,EAAC,SAAS;gCAAAH,QAAA,eACpBvF,OAAA;kCAAK0F,SAAS,EAAC,kBAAkB;kCAAAH,QAAA,eAC7BvF,OAAA;oCACImH,IAAI,EAAC,UAAU;oCACfzB,SAAS,EAAC,UAAU;oCACpB0B,OAAO,EAAET,OAAO,CAACO,MAAM,IAAI;oCAC3B;oCAAA;oCACAG,QAAQ,EAAGC,CAAC,IAAK;sCACb,MAAMC,QAAQ,GAAGD,CAAC,CAACE,MAAM,CAACJ,OAAO;;sCAEjC;sCACAK,6BAA6B,CAAC9F,YAAY,EAAEgF,OAAO,CAACI,kBAAkB,EAAEQ,QAAQ,EAAEG,mBAAmB,EAAEjB,gBAAgB,CAAC;oCAC5H;kCAAE;oCAAAZ,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACL;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACD;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACL,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC;0BAAA,GA3BDY,KAAK;4BAAAf,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OA4BV,CAAC;wBAAA,CACR;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNhG,OAAA;sBAAK0F,SAAS,EAAC,cAAc;sBAAAH,QAAA,gBACzBvF,OAAA;wBAAK0F,SAAS,EAAC,iBAAiB;wBAACC,KAAK,EAAE;0BAAEgC,eAAe,EAAE,OAAOC,kBAAkB;wBAAI,CAAE;wBAAArC,QAAA,eACtFvF,OAAA;0BAAK0F,SAAS,EAAC,cAAc;0BAAAH,QAAA,GAExB,EAAA/E,kBAAA,GAAAsG,gBAAgB,cAAAtG,kBAAA,uBAAhBA,kBAAA,CAAkBoC,QAAQ,kBACvB5C,OAAA,CAAC6H,OAAO;4BAACC,MAAM,EAAE,KAAM;4BAAAvC,QAAA,gBACnBvF,OAAA;8BAAAuF,QAAA,GAAA9E,kBAAA,GAAIqG,gBAAgB,cAAArG,kBAAA,uBAAhBA,kBAAA,CAAkBmC;4BAAQ;8BAAAiD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnChG,OAAA;8BAAAuF,QAAA,EAAM;4BAAK;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC,eAIdhG,OAAA,CAAC6H,OAAO;4BAACC,MAAM,EAAE,IAAK;4BAAAvC,QAAA,gBAClBvF,OAAA;8BAAAuF,QAAA,GAAA7E,kBAAA,GAAIoG,gBAAgB,cAAApG,kBAAA,uBAAhBA,kBAAA,CAAkBmC;4BAAQ;8BAAAgD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnChG,OAAA;8BAAAuF,QAAA,EAAM;4BAAK;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNhG,OAAA;wBAAK0F,SAAS,EAAC,gBAAgB;wBAAAH,QAAA,gBAC3BvF,OAAA;0BAAAuF,QAAA,EAAI;wBAAsB;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC/BhG,OAAA;0BACI0F,SAAS,EAAC,kBAAkB;0BAC5BX,KAAK,EAAE,EAAApE,kBAAA,GAAAmG,gBAAgB,cAAAnG,kBAAA,uBAAhBA,kBAAA,CAAkBkC,QAAQ,KAAI,EAAG;0BACxCwE,QAAQ,EAAEU,oBAAqB,CAAC;wBAAA;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC,CAAC,eACFhG,OAAA;0BAAK0F,SAAS,EAAC,iBAAiB;0BAAAH,QAAA,gBAC5BvF,OAAA;4BACI0F,SAAS,EAAC,gBAAgB;4BAC1ByB,IAAI,EAAC,QAAQ;4BACbb,OAAO,EAAEpB,uBAAwB,CAAC;4BAAA;4BAAAK,QAAA,EACrC;0BAED;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACThG,OAAA;4BACI0F,SAAS,EAAC,gBAAgB;4BAC1ByB,IAAI,EAAC,QAAQ;4BACbb,OAAO,EAAEjB,wBAAyB,CAAC;4BAAA;4BAAAE,QAAA,EACtC;0BAED;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAQR,CAAC,eAENhG,OAAA;0BAAK0F,SAAS,EAAC,qBAAqB;0BAAAH,QAAA,eAChCvF,OAAA;4BAAQ0F,SAAS,EAAC,qBAAqB;4BAACyB,IAAI,EAAC,QAAQ;4BAACb,OAAO,EAAE0B,kBAAmB;4BAAAzC,QAAA,EAAC;0BAAM;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEjG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEI;EAAC,gBACnB,CAAC;AAEX,CAAC;AAAAzF,EAAA,CAxSKD,OAAO;EAAA,QAUQ1B,WAAW;AAAA;AAAAqJ,EAAA,GAV1B3H,OAAO;AA0Sb,eAAeA,OAAO;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}