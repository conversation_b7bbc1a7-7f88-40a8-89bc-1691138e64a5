//const { checkUserInvoices } = require('./iugu'); // Função criada anteriormente
const { checkUserInvoices } = require('./asaas');
const Empresa = require('../models/Empresa');
const User = require('../models/User'); // Supondo que o model do usuário está disponível

const verifyUserAccess = async (req, res, next) => {
  const userId = req.params.id; // Assumindo que o ID do usuário está disponível no req.params.id
  console.log("ENTROU NA FUNCAO verifyUserAccess???", userId)
  // Busca o usuário e obtém o CNPJ vinculado à empresa
  const user = await User.findById(userId);

  if (!user || !user.vinculo_empresa) {
    return res.status(403).json({ msg: 'Acesso negado. Usuário ou vínculo com empresa não encontrado.' });
  }

  const cnpjEmpresa = user.vinculo_empresa;

  // Agora busca a empresa pelo CNPJ
  const empresa = await Empresa.findOne({ cnpj: cnpjEmpresa });

  if (!empresa) {
    return res.status(403).json({ msg: 'Acesso negado. Empresa não encontrada.' });
  }

  const customerId = empresa.customer_asaas;
  console.log("customerId>>>", customerId)
  try {
    const hasActiveInvoice = await checkUserInvoices(customerId);
    const userCreatedAt = new Date(user.createdAt); // Converter para Date
    //console.log("userCreatedAt:", userCreatedAt);

    // Calcular a diferença em dias
    const agora = new Date();
    const diferencaEmDias = (agora - userCreatedAt) / (1000 * 60 * 60 * 24);
    
    //console.log("Dias desde a criação:", diferencaEmDias);
    if (hasActiveInvoice || diferencaEmDias <= 7) {
      // Permitir o acesso
      next();
    } else {
      // Bloquear o acesso
      console.log('Acesso negado. Nenhuma fatura ativa encontrada.');
      return res.status(201).json({ msg: 'Acesso negado. Nenhuma fatura ativa encontrada.', hasLicense:false });
    }

  } catch (error) {
    console.error('Erro ao verificar o acesso do usuário:', error);
    return res.status(500).json({ msg: 'Erro no servidor ao verificar o acesso do usuário.' });
  }
}

module.exports = verifyUserAccess