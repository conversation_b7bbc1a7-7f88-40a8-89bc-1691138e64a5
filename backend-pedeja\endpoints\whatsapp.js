require('dotenv').config()
const router = require('express').Router();
const Empresa = require('../models/Empresa');
const Messages = require('../models/Messages');
const LeadChannel = require('../models/LeadChannel');
const Whatsapp = require('../lib/whatsapp');
const mongoose = require('mongoose');
const { Client } = require('@upstash/qstash');
require("isomorphic-fetch");
const client = new Client({
	token: process.env.QSTASH_TOKEN,
});

const queue = client.queue({
	queueName: "wp-profile"
})

const cors = require('cors');
const LLMBot = require('../lib/llmbot');

// Configurar CORS
router.use(cors({
	origin: ["https://dev.pedeja.chat", "https://localhost:3000", "https://app.pedeja.chat"], // Substitua pela URL da sua aplicação React
	methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
	allowedHeaders: 'Content-Type,Authorization', // Adicione os headers que você precisa
}));

// Função helper para extrair conteúdo de mensagens
function extractMessageContent(messageObj) {
	if (!messageObj) return 'Mensagem';

	if (messageObj.conversation) {
		return messageObj.conversation;
	} else if (messageObj.extendedTextMessage?.text) {
		return messageObj.extendedTextMessage.text;
	} else if (messageObj.imageMessage?.caption) {
		return `📷 ${messageObj.imageMessage.caption}`;
	} else if (messageObj.videoMessage?.caption) {
		return `🎥 ${messageObj.videoMessage.caption}`;
	} else if (messageObj.documentMessage?.title) {
		return `📄 ${messageObj.documentMessage.title}`;
	} else if (messageObj.audioMessage) {
		return '🎵 Áudio';
	} else if (messageObj.imageMessage) {
		return '📷 Imagem';
	} else if (messageObj.videoMessage) {
		return '🎥 Vídeo';
	} else if (messageObj.documentMessage) {
		return '📄 Documento';
	} else if (messageObj.stickerMessage) {
		return '😀 Figurinha';
	} else {
		return 'Mensagem';
	}
}

// **📌 WEBHOOK PRINCIPAL - PROCESSAMENTO SÍNCRONO ORIGINAL**
router.post("/webhook", async (req, res) => {
	try {
		console.log('📨 Webhook recebido:', JSON.stringify(req.body, null, 2));

		const AppExpressPedeja = require('../AppExpressPedeja');
		const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();

		// EvolutionAPI envia instanceId em diferentes formatos
		const instanceId = req.body.instance ||
						   req.body.instanceId ||
						   req.body.data?.instanceId ||
						   req.body.owner ||
						   req.headers['appname'];

		if (!instanceId) {
			console.log('❌ instanceId não informado');
			return res.status(200).json({ status: 400, error: 'instanceId não informado!' });
		}

		// **🔍 DETECTAR FORMATO DE WEBHOOK ANTIGO/DIFERENTE**
		const isOldApiFormat = Array.isArray(req.body) && req.body[0] === "Msg" && req.body[1]?.cmd;
		if (isOldApiFormat) {
			//console.log(`⚠️ Webhook de API antiga/diferente detectado para instanceId: ${instanceId}`);
			//console.log(`📋 Comando: ${req.body[1]?.cmd}, ACK: ${req.body[1]?.ack}`);
			// Retornar 200 para não gerar erro na API externa, mas não processar
			return res.status(200).json({ status: 200, message: 'Webhook de API antiga ignorado' });
		}

		// Buscar empresa
		let empresa = await Empresa.findOne({
			$or: [
				{ 'whatsapp.id': instanceId },
				{ 'whatsapp.name': instanceId }
			]
		}).select("inativo whatsapp status_bot");

		if (!empresa) {
			console.log(`❌ Empresa não encontrada para instanceId: ${instanceId}`);
			// **🔍 LOG ADICIONAL PARA DEBUG**
			console.log(`🔍 Headers do request:`, JSON.stringify({
				appname: req.headers['appname'],
				'user-agent': req.headers['user-agent'],
				'content-type': req.headers['content-type']
			}));
			return res.status(200).json({ status: 400, error: 'Empresa não encontrada!' });
		}

		console.log(`✅ Empresa encontrada: ${empresa._id}`);

		// Processar diferentes tipos de eventos
		const eventType = req.body.event || req.body.type;

		// Evento de atualização de status de mensagem (acks)
		if (eventType === 'messages.update' || (req.body.messages && req.body.messages[0]?.update)) {
			const messages = req.body.messages || [req.body];
			for (const msgUpdate of messages) {
				const messageId = msgUpdate.key?.id || msgUpdate.id;
				const status = msgUpdate.update?.status || msgUpdate.status;

				if (messageId && status) {
					await Messages.updateOne(
						{ 'channel_id': instanceId, 'id_message': messageId },
						{ $set: { statusMessage: status } }
					);
					console.log("Status da mensagem ID", messageId, "atualizada para:", status);
				}
			}
			return res.status(200).json({ status: 200 });
		}

		if (eventType === 'messages.upsert' || req.body.messages) {
			// **📌 PROCESSAR MENSAGENS**
			const messages = req.body.messages || req.body.data?.messages || [];
			console.log(`📨 Processando ${messages.length} mensagens`);

			for (const message of messages) {
				await processMessage(message, empresa, socketWhatsapp);
			}
		} else if (eventType === 'connection.update' || req.body.state) {
			// **📌 PROCESSAR STATUS DE CONEXÃO**
			await processConnectionUpdate(req.body, empresa, instanceId, socketWhatsapp);
		} else if (eventType === 'qrcode.updated' || req.body.qrcode || req.body.data?.qrcode) {
			// **📌 PROCESSAR QR CODE**
			await processQRCodeUpdate(req.body, empresa, instanceId, socketWhatsapp);
		}

		return res.status(200).json({ status: 200 });

	} catch (error) {
		console.error('❌ Erro no webhook:', error);
		return res.status(200).json({ status: 500, error: error.message });
	}
});

// **📌 PROCESSAMENTO DE MENSAGENS (SÍNCRONO ORIGINAL)**
async function processMessage(message, empresa, socketWhatsapp) {
	try {
		if (!message || !message.key) {
			console.log("⚠️ Mensagem sem estrutura key válida:", message);
			return;
		}

		const messageID = message.key.id;
		const remoteJid = message.key.remoteJid;
		const fromMe = message.key.fromMe || false;
		const pushName = message.pushName || message.verifiedBizName || '';
		
		console.log('📨 Processando mensagem:', { messageID, fromMe, pushName });

		// Extrair texto da mensagem
		const messageText = extractMessageContent(message.message);

		// Buscar ou criar LeadChannel
		let lead = await LeadChannel.findOne({
			whatsapp_remote_jid: remoteJid,
			empresaObjId: empresa._id,
			channel: 'whatsapp'
		});

		if (!lead) {
			// **🔧 BUSCAR FOTO DE PERFIL IMEDIATAMENTE PARA NOVOS LEADS**
			let profilePictureUrl = null;
			console.log(`📸 Novo lead detectado! Buscando foto de perfil para ${pushName || remoteJid}...`);
			
			try {
				const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
				const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);
				const profilePicture = await whatsappInstance.getProfilePicture(remoteJid);
				
				if (profilePicture && profilePicture.profilePictureUrl) {
					profilePictureUrl = profilePicture.profilePictureUrl;
					console.log(`✅ Foto de perfil encontrada para novo lead: ${pushName || remoteJid}`);
				} else {
					console.log(`⚠️ Nenhuma foto de perfil encontrada para novo lead: ${pushName || remoteJid}`);
				}
			} catch (profileError) {
				console.log(`❌ Erro ao buscar foto de perfil para novo lead ${pushName || remoteJid}:`, profileError.message);
			}

			lead = await LeadChannel.create({
				empresaObjId: empresa._id,
				name: pushName || remoteJid.split('@')[0],
				channel: 'whatsapp',
				channel_id: empresa.whatsapp.name || empresa.whatsapp.id,
				mobile_number: remoteJid.split('@')[0],
				whatsapp_remote_jid: remoteJid,
				channel_data: {
					whatsapp: {
						jid: remoteJid,
						pushName: pushName,
						profile_picture: profilePictureUrl, // ✅ Agora já vem com foto!
						profile_picture_updateAt: profilePictureUrl ? new Date() : null
					}
				},
				createdAt: new Date(),
				updatedAt: new Date()
			});
			
			if (profilePictureUrl) {
				console.log(`🎉 Novo lead criado COM foto de perfil: ${lead.name}`);
			} else {
				console.log(`⚠️ Novo lead criado sem foto de perfil: ${lead.name}`);
			}
		}

		// **📌 ATUALIZAR NOME E FOTO DE LEADS EXISTENTES (MELHORADO)**
		if (!fromMe && lead && pushName) {
			let shouldUpdate = false;
			let shouldUpdatePhoto = false;

			// Atualizar se não tem nome ou se o nome é apenas o número
			if (!lead.name || lead.name === lead.mobile_number || lead.name.startsWith('Contato ')) {
				console.log(`📝 Atualizando nome do contato de "${lead.name}" para "${pushName}"`);
				lead.name = pushName;
				shouldUpdate = true;
			}
			// Ou se o pushName é diferente e mais informativo
			else if (lead.name !== pushName && pushName.length > lead.name.length) {
				console.log(`📝 Atualizando nome do contato de "${lead.name}" para "${pushName}" (mais informativo)`);
				lead.name = pushName;
				shouldUpdate = true;
			}

			// **🔧 VERIFICAR SE PRECISA BUSCAR FOTO PARA LEAD EXISTENTE**
			const hasProfilePicture = lead.channel_data?.whatsapp?.profile_picture;
			const hasRecentUpdate = lead.channel_data?.whatsapp?.profile_picture_updateAt && 
									(new Date() - new Date(lead.channel_data.whatsapp.profile_picture_updateAt)) < (7 * 24 * 60 * 60 * 1000);
			
			if (!hasProfilePicture || !hasRecentUpdate) {
				console.log(`📸 Lead existente sem foto recente. Buscando foto para ${lead.name}...`);
				shouldUpdatePhoto = true;
				
				try {
					const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
					const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);
					const profilePicture = await whatsappInstance.getProfilePicture(remoteJid);
					
					if (profilePicture && profilePicture.profilePictureUrl) {
						// Atualizar foto no lead
						if (!lead.channel_data) lead.channel_data = {};
						if (!lead.channel_data.whatsapp) lead.channel_data.whatsapp = {};
						lead.channel_data.whatsapp.profile_picture = profilePicture.profilePictureUrl;
						lead.channel_data.whatsapp.profile_picture_updateAt = new Date();
						shouldUpdate = true;
						console.log(`✅ Foto de perfil atualizada para lead existente: ${lead.name}`);
					} else {
						console.log(`⚠️ Nenhuma foto encontrada para lead existente: ${lead.name}`);
					}
				} catch (profileError) {
					console.log(`❌ Erro ao buscar foto para lead existente ${lead.name}:`, profileError.message);
				}
			}

			if (shouldUpdate) {
				// Atualizar pushName sem sobrescrever profile_picture
				if (!lead.channel_data) lead.channel_data = {};
				if (!lead.channel_data.whatsapp) lead.channel_data.whatsapp = {};
				lead.channel_data.whatsapp.pushName = pushName;
				lead.updatedAt = new Date();
				await lead.save();
				
				if (shouldUpdatePhoto) {
					console.log(`🎉 Lead existente atualizado com nova foto: ${lead.name}`);
				}
			}
		}

		// Verificar se mensagem já existe
		let existingMessage = await Messages.findOne({
			id_message: messageID,
			empresaObjId: empresa._id
		});

		if (!existingMessage) {
			// Timestamp da EvolutionAPI
			const timestamp = message.messageTimestamp || Math.floor(Date.now() / 1000);
			const messageDate = new Date(timestamp * 1000);

			const savedMessage = await Messages.create({
				'id_message': messageID,
				'empresaObjId': empresa._id,
				'channel': 'whatsapp',
				'text': messageText,
				'whatsapp_number': remoteJid,
				fromMe: fromMe,
				channel_id: empresa.whatsapp.name || empresa.whatsapp.id,
				leadChannel: lead._id,
				messageDate,
				createdAt: new Date(),
				statusMessage: 1
			});

			// Processar bot apenas para mensagens recebidas
			if (!fromMe && empresa.status_bot && messageText.trim()) {
				await dataLLm(empresa._id, messageText, lead._id);
			}

			// Emitir via socket para frontend
			if (socketWhatsapp) {
				lead.message = savedMessage.text;
				lead.updatedAt = new Date();
				await lead.save();

				const socketData = {
					"_id": lead._id,
					"empresaObjId": empresa._id,
					"name": lead.name,
					"channel": lead.channel,
					"channel_id": lead.channel_id,
					"mobile_number": lead.mobile_number,
					"channel_data": lead.channel_data,
					"createdAt": lead.createdAt,
					"updatedAt": lead.updatedAt,
					"__v": lead.__v,
					"message": {
						"text": savedMessage.text,
						"message": savedMessage.text,
						"messageDate": savedMessage.messageDate,
						"createdAt": savedMessage.createdAt,
						"fromMe": savedMessage.fromMe
					}
				};

				socketWhatsapp.to(`chats:${empresa._id}`).emit('chats', socketData);
				socketWhatsapp.to(`messages:${lead._id}`).emit('messages', savedMessage.toObject());
				console.log(`✅ Mensagem emitida via socket para empresa ${empresa._id}`);
			}
		}

	} catch (error) {
		console.error('❌ Erro ao processar mensagem:', error);
	}
}

// **📌 PROCESSAMENTO DE STATUS DE CONEXÃO (SÍNCRONO ORIGINAL)**
async function processConnectionUpdate(data, empresa, instanceId, socketWhatsapp) {
	try {
		const connectionState = data.state || data.connection;
		let statusCode = 400; // padrão desconectado

		if (connectionState === 'open') {
			statusCode = 200; // conectado
		} else if (connectionState === 'connecting') {
			statusCode = 100; // conectando
		}

		console.log(`🔄 Processando atualização de conexão - Instância: ${instanceId}, Estado: ${connectionState}, StatusCode: ${statusCode}`);

		let needsUpdate = false;
		let jidUpdated = false;

		// **🔧 SEMPRE VERIFICAR E ATUALIZAR JID QUANDO CONECTADO**
		if (connectionState === 'open') {
			let currentJid = null;

			// Se tiver informação do número no webhook, usar
			if (data.jid || data.number) {
				currentJid = data.jid || data.number;
				console.log(`🔍 JID fornecido via webhook: ${currentJid}`);
			}
			// Senão, buscar manualmente (sempre buscar para garantir que está atualizado)
			else {
				console.log(`🔍 JID não fornecido via webhook. Buscando via fetchProfile...`);
				try {
					const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceId);
					const profile = await whatsappInstance.fetchProfile();
					
					if (profile.data && profile.data.wuid) {
						currentJid = profile.data.wuid;
						console.log(`🔍 JID buscado via fetchProfile: ${currentJid}`);
					} else {
						console.log(`⚠️ Não foi possível buscar o JID via fetchProfile`);
					}
				} catch (profileError) {
					console.error(`❌ Erro ao buscar JID:`, profileError.message);
				}
			}

			// Comparar e atualizar JID se necessário
			if (currentJid) {
				if (empresa.whatsapp.jid !== currentJid) {
					console.log(`🔄 JID mudou! Anterior: ${empresa.whatsapp.jid || 'NENHUM'} → Novo: ${currentJid}`);
					empresa.whatsapp.jid = currentJid;
					jidUpdated = true;
					
					// **🧹 LIMPAR FOTO DE PERFIL ANTIGA QUANDO JID MUDA**
					if (empresa.whatsapp.my_profile_picture) {
						console.log(`🧹 Limpando foto de perfil antiga devido à mudança de JID`);
						empresa.whatsapp.my_profile_picture = null;
						empresa.whatsapp.my_profile_picture_updateAt = null;
					}
				} else {
					console.log(`✅ JID confirmado (sem mudança): ${currentJid}`);
				}
			}
		}

		// Atualizar status se mudou
		if (empresa.whatsapp.instance_status !== statusCode) {
			console.log(`🔄 Atualizando status da instância ${instanceId}: ${connectionState} (${statusCode})`);
			empresa.whatsapp.instance_status = statusCode;
			needsUpdate = true;
		}

		// Salvar mudanças se necessário
		if (needsUpdate || jidUpdated) {
			await empresa.save();
			console.log(`✅ Status da instância ${instanceId} atualizado para ${statusCode}${jidUpdated ? ' e JID atualizado' : ''}`);
		}

		// Emitir evento de status de conexão via Socket.IO (sempre emitir para garantir sincronização)
		if (socketWhatsapp) {
			const socketData = {
				instanceId,
				state: connectionState,
				statusCode,
				empresaID: empresa._id,
				isConnected: connectionState === 'open',
				timestamp: new Date()
			};

			socketWhatsapp.to(`chats:${empresa._id}`).emit('whatsapp_connection_update', socketData);
			console.log(`✅ Status de conexão emitido via socket - Empresa: ${empresa._id}, Estado: ${connectionState}`);

			// Se conectado, emitir evento especial para recarregar chats
			if (connectionState === 'open') {
				socketWhatsapp.to(`chats:${empresa._id}`).emit('whatsapp_connected', {
					instanceId,
					empresaID: empresa._id,
					message: 'WhatsApp conectado com sucesso!',
					timestamp: new Date()
				});
				console.log(`🎉 Evento de conexão estabelecida emitido - Empresa: ${empresa._id}`);
			}
		}

	} catch (error) {
		console.error('❌ Erro ao processar CONNECTION_UPDATE:', error);
	}
}

// **📌 PROCESSAMENTO DE QR CODE (SÍNCRONO ORIGINAL)**
async function processQRCodeUpdate(data, empresa, instanceId, socketWhatsapp) {
	try {
		const qrcode = data.qrcode || data.data?.qrcode || data.base64 || data.code;

		if (qrcode) {
			console.log(`📱 Emitindo QR Code para frontend - Instância: ${instanceId}`);

			// Estrutura do QR code para o frontend
			const qrData = {
				qr: data.base64 || qrcode,
				code: data.code || data.pairingCode,
				ttl: Math.floor(Date.now() / 1000) + 45, // 45 segundos de TTL
				timestamp: new Date()
			};

			// Emitir QR Code para o frontend via Socket.IO
			if (socketWhatsapp) {
				socketWhatsapp.to(`chats:${empresa._id}`).emit('whatsapp_qrcode_update', {
					instanceId,
					qrcode: qrData,
					empresaID: empresa._id,
					timestamp: new Date()
				});
				console.log(`✅ QR Code emitido via socket - Empresa: ${empresa._id}, Instância: ${instanceId}`);
			}
		}

	} catch (error) {
		console.error('❌ Erro ao processar QRCODE_UPDATED:', error);
	}
}

// **📌 FUNÇÃO DE BOT N8N (NOVO PADRÃO PARA TODAS AS EMPRESAS)**
const dataLLm = async (company_id, inputMessage, lead_id) => {
    console.log("🤖 dataLLm Call - Bot N8N:", company_id, inputMessage, lead_id);

    // **Buscar o lead para verificar se bot está pausado**
    const lead = await LeadChannel.findOne({ _id: lead_id });
    if (!lead) {
        console.log("❌ Lead não encontrado");
        return { data: { last_message: "Erro: Lead não encontrado", need_human: false } };
    }

    // **Verificar se o bot está pausado para este lead**
    if (lead.bot_pausado) {
        console.log(`🔴 Bot pausado para o lead ${lead_id}. Não processando mensagem.`);
        return { data: { last_message: null, need_human: true } };
    }

    console.log("🔥🔥🔥 ===== INICIO PROCESSAMENTO BOT N8N ===== 🔥🔥🔥");
    console.log("📊 Dados recebidos:", {
        company_id: company_id.toString(),
        inputMessage,
        lead_id: lead_id.toString(),
        timestamp: new Date().toISOString()
    });
    
    try {
        const webhookUrl = "https://n8n.funil.vip/webhook/agentepedeja";
        const payload = {
            company_id: company_id.toString(),
            inputMessage,
            lead_id: lead_id.toString(),
            timestamp: new Date()
        };

        console.log("🚀 Enviando payload para webhook N8N:", payload);
        console.log("🌐 URL webhook:", webhookUrl);

        // **🔧 INICIAR PRESENCE LONGO DURANTE TODO O PROCESSAMENTO**
        let presenceController = null;
        try {
            const empresa = await Empresa.findOne({ _id: company_id }).select("inativo whatsapp");
            if (empresa && empresa.whatsapp && lead.channel_data.whatsapp.jid) {
                const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
                const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);
                
                console.log("⌨️ Iniciando presence longo durante processamento N8N...");
                presenceController = whatsappInstance.StartLongPresence(
                    lead.channel_data.whatsapp.jid, 
                    "composing", 
                    21000 // Delay de 21 segundos
                );
            }
        } catch (presenceError) {
            console.warn("⚠️ Erro ao iniciar presence longo:", presenceError.message);
        }

        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload)
        });

        console.log("📡 Status da resposta:", response.status);
        console.log("📡 Headers da resposta:", Object.fromEntries(response.headers.entries()));

        const responseData = await response.json();
        console.log("📋 Retorno COMPLETO do webhook N8N:", JSON.stringify(responseData, null, 2));

        // **✅ PRESENCE SERÁ CANCELADO AO ENVIAR MENSAGEM**
		if (presenceController) {
			try {
				console.log("⏹️ Cancelando presence longo...");
				presenceController.stop();
			} catch (presenceError) {
				console.warn("⚠️ Erro ao cancelar presence longo:", presenceError.message);
			}
		}

        // **Buscar dados da empresa para configurar WhatsApp**
        const empresa = await Empresa.findOne({ _id: company_id }).select("inativo whatsapp");
        if (!empresa || !empresa.whatsapp) {
            console.log("❌ Empresa ou configuração WhatsApp não encontrada");
            return { data: { last_message: "Erro: Configuração não encontrada", need_human: false } };
        }

        const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
        const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

        let messageToSend = "";

        // **Preparar mensagem baseada na resposta do webhook**
        if (response.status === 200 && responseData) {
            console.log("✅ Webhook respondeu com sucesso, preparando mensagem de resposta");
            
            // **Extrair mensagem e needHuman do output**
            if (Array.isArray(responseData) && responseData.length > 0 && responseData[0].output) {
                const output = responseData[0].output;
                messageToSend = output.Message || "Mensagem não disponível";
                const needHuman = output.need_human || false;
                
                console.log("📨 Mensagem extraída do output:", messageToSend);
                console.log("🔍 needHuman:", needHuman);
                
                // **Se o cliente pediu atendimento humano, verifica se a empresa permite atendimento**
                if (needHuman) {
                    console.log("🔴 Cliente solicitou atendimento humano!");

                    // Verificar se a empresa tem call_atendente habilitado
                    if (!empresa.call_atendente) {
                        console.log("⚠️ Empresa não permite atendimento humano (call_atendente: false)");

                        // Enviar mensagem automática informando que não há atendentes disponíveis
                        const mensagemSemAtendente = "Desculpe, no momento não temos atendentes disponíveis. Por favor, continue utilizando nosso atendimento automático ou tente novamente mais tarde.";

                        try {
                            await whatsappInstance.SendSimpleTxtMessage(
                                lead.channel_data.whatsapp.jid,
                                mensagemSemAtendente
                            );
                            console.log("✅ Mensagem de 'sem atendentes' enviada com sucesso");
                        } catch (error) {
                            console.error("❌ Erro ao enviar mensagem de 'sem atendentes':", error);
                        }

                        // Não adiciona na fila e não emite WebSocket
                        return;
                    }

                    console.log("✅ Empresa permite atendimento humano. Salvando no banco e disparando WebSocket...");

                    try {
                        // 🔥 PERSISTIR NO BANCO DE DADOS PRIMEIRO (mais confiável)
                        const resultadoFila = await Empresa.updateOne(
                            { _id: empresa._id },
                            {
                                $push: {
                                    fila_atendimento: {
                                        lead_id: lead_id,
                                        nome: lead.name,
                                        celular: lead.channel_data.whatsapp.jid,
                                        mensagem: inputMessage,
                                        timestamp: new Date(),
                                        status: 'pendente'
                                    }
                                }
                            }
                        );

                        console.log("✅ Atendimento salvo na fila do banco:", {
                            empresa_id: empresa._id,
                            lead_id,
                            nome: lead.name,
                            resultado: resultadoFila
                        });

                        // 🔥 EMITIR VIA WEBSOCKET COMO BACKUP
                        const AppExpressPedeja = require('../AppExpressPedeja');
                        const io = AppExpressPedeja.AppExpressPedeja.getSocket();
                        const companyRoom = empresa._id.toString();

                        const payloadAtendimento = {
                            lead_id,
                            company_id,
                            nome: lead.name,
                            celular: lead.channel_data.whatsapp.jid,
                            mensagem: inputMessage,
                            timestamp: new Date(),
                            status: 'pendente'
                        };

                        console.log(`📢 Emitindo evento "atendimento_pendente" para a sala: ${companyRoom}`);
                        console.log("🔹 Payload enviado:", payloadAtendimento);

                        // **Emitir o evento APENAS para a sala da empresa**
                        io.to(companyRoom).emit("atendimento_pendente", payloadAtendimento);

                        console.log("✅ Atendimento humano processado com sucesso!");

                    } catch (error) {
                        console.error("❌ Erro ao salvar atendimento na fila:", error);
                        // Mesmo com erro no banco, tenta emitir via socket
                        try {
                            const AppExpressPedeja = require('../AppExpressPedeja');
                            const io = AppExpressPedeja.AppExpressPedeja.getSocket();
                            const companyRoom = empresa._id.toString();

                            io.to(companyRoom).emit("atendimento_pendente", {
                                lead_id,
                                company_id,
                                nome: lead.name,
                                celular: lead.channel_data.whatsapp.jid,
                                mensagem: inputMessage,
                                timestamp: new Date(),
                                status: 'pendente'
                            });
                            console.log("⚠️ Socket emitido mesmo com erro no banco");
                        } catch (socketError) {
                            console.error("❌ Erro crítico - falha no banco E no socket:", socketError);
                        }
                    }
                }
            } else {
                console.log("❌ Formato de resposta inesperado, usando fallback");
                messageToSend = `🤖 Resposta do sistema:\n\nStatus: ${response.status}\nCódigo: ${responseData.code || 'N/A'}\nDetalhes: ${JSON.stringify(responseData, null, 2)}`;
            }
        } else {
            console.log("❌ Webhook retornou erro, preparando mensagem de erro");
            messageToSend = `❌ Erro no sistema (Status ${response.status}):\n\n${responseData?.message || 'Erro desconhecido'}\n\nCódigo: ${responseData?.code || 'N/A'}`;
        }

        console.log("📤 Mensagem que será enviada via WhatsApp:", messageToSend);

        // **Enviar mensagem para o cliente**
        if (lead && lead.channel_data && lead.channel_data.whatsapp && lead.channel_data.whatsapp.jid) {
            await whatsappInstance.SendSimpleTxtMessage(lead.channel_data.whatsapp.jid, messageToSend);
            console.log("✅ Mensagem enviada via WhatsApp para:", lead.channel_data.whatsapp.jid);
        } else {
            console.error("❌ Não foi possível enviar mensagem - dados do lead incompletos");
        }

        console.log("🔥🔥🔥 ===== FIM PROCESSAMENTO BOT N8N - SUCESSO ===== 🔥🔥🔥");
        
        return responseData;

    } catch (error) {
        console.error("🔥🔥🔥 ===== ERRO NO PROCESSAMENTO BOT N8N ===== 🔥🔥🔥");
        console.error("❌ Erro ao chamar webhook N8N:", error);
        console.error("❌ Stack trace:", error.stack);

        // **Tentar enviar mensagem de erro via WhatsApp**
        try {
            const empresa = await Empresa.findOne({ _id: company_id }).select("inativo whatsapp");
            if (empresa && empresa.whatsapp) {
                const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
                const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

                // **✅ PRESENCE SERÁ CANCELADO AUTOMATICAMENTE PELO WHATSAPP AO ENVIAR MENSAGEM DE ERRO**

                const errorMessage = `❌ Erro no sistema de atendimento:\n\n${error.message || 'Erro desconhecido'}\n\nPor favor, tente novamente em alguns instantes ou entre em contato conosco.`;
                
                console.log("📤 Enviando mensagem de erro via WhatsApp:", errorMessage);

                if (lead && lead.channel_data && lead.channel_data.whatsapp && lead.channel_data.whatsapp.jid) {
                    await whatsappInstance.SendSimpleTxtMessage(lead.channel_data.whatsapp.jid, errorMessage);
                    console.log("✅ Mensagem de erro enviada via WhatsApp para:", lead.channel_data.whatsapp.jid);
                } else {
                    console.error("❌ Não foi possível enviar mensagem de erro - dados do lead incompletos");
                }
            }
        } catch (whatsappError) {
            console.error("❌ Erro ao tentar enviar mensagem de erro via WhatsApp:", whatsappError.message);
        }

        console.log("🔥🔥🔥 ===== FIM PROCESSAMENTO BOT N8N - ERRO ===== 🔥🔥🔥");
        return { data: { last_message: null, need_human: true } };
    }
};

	// **📌 ROTA PARA ESCANEAR QR CODE (COMPATIBILIDADE)**
router.get("/scanQR/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 400, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não configurado para esta empresa. Crie uma instância primeiro.',
				empresaID: empresa._id,
				qrcode: { error: 'no_instance' }
			});
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		// Primeiro verificar se já está conectado
		try {
			const status = await whatsappInstance.getInstanceStatus();
			if (status.data && status.data.instance && status.data.instance.state === 'open') {
				// Atualizar status no banco se necessário
				if (empresa.whatsapp.instance_status !== 200) {
					empresa.whatsapp.instance_status = 200;
					await empresa.save();
				}

				// **🔧 SEMPRE VERIFICAR E ATUALIZAR JID QUANDO CONECTADO**
				console.log(`🔍 WhatsApp conectado. Verificando se JID está atualizado...`);
				try {
					const profile = await whatsappInstance.fetchProfile();
					
					if (profile.data && profile.data.wuid) {
						const currentJid = profile.data.wuid;
						
						if (empresa.whatsapp.jid !== currentJid) {
							console.log(`🔄 JID mudou! Anterior: ${empresa.whatsapp.jid || 'NENHUM'} → Novo: ${currentJid}`);
							empresa.whatsapp.jid = currentJid;
							
							// **🧹 LIMPAR FOTO DE PERFIL ANTIGA QUANDO JID MUDA**
							if (empresa.whatsapp.my_profile_picture) {
								console.log(`🧹 Limpando foto de perfil antiga devido à mudança de JID`);
								empresa.whatsapp.my_profile_picture = null;
								empresa.whatsapp.my_profile_picture_updateAt = null;
							}
							
							await empresa.save();
							console.log(`✅ JID atualizado e salvo: ${currentJid}`);
						} else {
							console.log(`✅ JID confirmado (sem mudança): ${currentJid}`);
						}
					} else {
						console.log(`⚠️ Não foi possível buscar o JID via fetchProfile`);
					}
				} catch (profileError) {
					console.error(`❌ Erro ao buscar JID:`, profileError.message);
				}

				// Emitir evento de conexão estabelecida via Socket.IO
				const AppExpressPedeja = require('../AppExpressPedeja');
				const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();
				if (socketWhatsapp) {
					socketWhatsapp.to(`chats:${empresa._id}`).emit('whatsapp_connected', {
						instanceId: instanceIdentifier,
						empresaID: empresa._id,
						state: 'open',
						message: 'WhatsApp já está conectado!',
						timestamp: new Date()
					});
				}

				return res.status(200).json({
					status: 200,
					empresaID: empresa._id,
					qrcode: { error: 'is_loged' },
					isConnected: true
				});
			}
		} catch (statusError) {
			console.log('Erro ao verificar status, continuando com QR:', statusError.message);
		}

		// Gerar QR Code
		const qrcode = await whatsappInstance.genQR();
		let qrResponse = {};
		if (qrcode.data) {
			qrResponse = {
				qr: qrcode.data.base64 || '',
				code: qrcode.data.code || qrcode.data.pairingCode,
				ttl: Math.floor(Date.now() / 1000) + 45, // 45 segundos de TTL
				timestamp: new Date()
			};

			// Emitir QR Code via Socket.IO para atualização em tempo real
			const AppExpressPedeja = require('../AppExpressPedeja');
			const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();
			if (socketWhatsapp) {
				socketWhatsapp.to(`chats:${empresa._id}`).emit('whatsapp_qrcode_update', {
					instanceId: instanceIdentifier,
					qrcode: qrResponse,
					empresaID: empresa._id,
					timestamp: new Date()
				});
			}
		}

		return res.status(200).json({
			status: 200,
			empresaID: empresa._id,
			qrcode: qrResponse,
			isConnected: false
		});

	} catch (error) {
		console.error('ERROR scanQR:', error);
		return res.status(400).json({
			status: 400,
			error: error.message || error,
			empresaID: req.params.empresaID,
			qrcode: { error: 'api_error', message: error.message }
		});
	}
});

// **📌 ROTA PARA STATUS DA INSTÂNCIA (COMPATIBILIDADE)**
router.get("/instanceStatus/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 400, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(404).json({ status: 400, error: 'whatsapp não encontrado!', empresaID: empresa._id });
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		const status = await whatsappInstance.getInstanceStatus();
		return res.status(200).json({ status: 200, empresaID: empresa._id, instanceStatus: status.data });

	} catch (error) {
		console.error('ERROR:', error);
		return res.status(400).json({ 
			status: 400, 
			error: error.message || error, 
			empresaID: req.params.empresaID, 
			instanceStatus: null 
		});
	}
});

// **📌 ROTA PARA REMOVER SESSÃO**
router.get("/removeSession/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 400, msg: 'Empresa não encontrada!' });
		}

		if (!empresa.whatsapp || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(404).json({ status: 400, msg: 'WhatsApp não configurado!', empresaID: empresa._id });
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);
		const response = await whatsappInstance.removeSession();

		return res.status(200).json({ 
			status: 200, 
			empresaID: empresa._id, 
			msg: 'Sessão do WhatsApp encerrada com sucesso!', 
			response: response.data 
		});

	} catch (error) {
		console.error('ERROR:', error);
		return res.status(400).json({ 
			status: 400, 
			msg: 'Erro ao encerrar a sessão do WhatsApp!', 
			error: error.message || error, 
			empresaID: req.params.empresaID 
		});
	}
});

// **📌 ROTA PARA GERAR QR CODE**
router.get("/qrcode/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		if (currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não configurado para esta empresa.',
				empresaID: empresa._id
			});
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		// Verificar status de conexão primeiro
		const connectionStatusResponse = await whatsappInstance.getInstanceStatus();
		console.log('🔍 Status de conexão:', connectionStatusResponse.data);

		// Verificar se está conectado baseado na resposta da Evolution API
		const isConnected = connectionStatusResponse?.data?.instance?.state === 'open';

		if (isConnected) {
			// Emitir evento de conexão estabelecida via Socket.IO
			const AppExpressPedeja = require('../AppExpressPedeja');
			const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();
			if (socketWhatsapp) {
				socketWhatsapp.to(`chats:${empresa._id}`).emit('whatsapp_connected', {
					instanceId: instanceIdentifier,
					empresaID: empresa._id,
					state: 'open',
					message: 'WhatsApp já está conectado!',
					timestamp: new Date()
				});
			}

			return res.status(200).json({
				status: 200,
				isConnected: true,
				connectionState: 'open',
				message: 'WhatsApp já está conectado!'
			});
		}

		// Se não estiver conectado, gerar QR code
		const qrCodeResponse = await whatsappInstance.genQR();
		console.log('📱 QR Code gerado:', qrCodeResponse.data);

		// Estruturar resposta do QR code
		const qrData = {
			qr: qrCodeResponse.data?.base64 || '',
			code: qrCodeResponse.data?.code || qrCodeResponse.data?.pairingCode,
			ttl: Math.floor(Date.now() / 1000) + 45, // 45 segundos de TTL
			timestamp: new Date()
		};

		// Emitir QR Code via Socket.IO para atualização em tempo real
		const AppExpressPedeja = require('../AppExpressPedeja');
		const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();
		if (socketWhatsapp) {
			socketWhatsapp.to(`chats:${empresa._id}`).emit('whatsapp_qrcode_update', {
				instanceId: instanceIdentifier,
				qrcode: qrData,
				empresaID: empresa._id,
				timestamp: new Date()
			});
		}

		return res.status(200).json({
			status: 200,
			empresaID,
			qrcode: qrData,
			isConnected: false,
			connectionState: connectionStatusResponse?.data?.instance?.state || 'disconnected'
		});

	} catch (error) {
		console.error('❌ Erro ao gerar QR Code:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA RENOVAÇÃO AUTOMÁTICA DE QR CODE**
router.post("/qrcode/renew/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não configurado para esta empresa.',
				empresaID: empresa._id
			});
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		// Verificar se já está conectado
		const connectionStatus = await whatsappInstance.getInstanceStatus();
		if (connectionStatus.data?.instance?.state === 'open') {
			return res.status(200).json({
				status: 200,
				message: 'WhatsApp já está conectado!',
				isConnected: true
			});
		}

		// Gerar novo QR code
		const qrCodeResponse = await whatsappInstance.genQR();
		console.log('🔄 QR Code renovado:', qrCodeResponse.data);

		// Estruturar resposta do QR code
		const qrData = {
			qr: qrCodeResponse.data?.base64 || '',
			code: qrCodeResponse.data?.code || qrCodeResponse.data?.pairingCode,
			ttl: Math.floor(Date.now() / 1000) + 45, // 45 segundos de TTL
			timestamp: new Date()
		};

		// Emitir QR Code renovado via Socket.IO
		const AppExpressPedeja = require('../AppExpressPedeja');
		const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();
		if (socketWhatsapp) {
			socketWhatsapp.to(`chats:${empresa._id}`).emit('whatsapp_qrcode_update', {
				instanceId: instanceIdentifier,
				qrcode: qrData,
				empresaID: empresa._id,
				renewed: true,
				timestamp: new Date()
			});
			console.log(`🔄 QR Code renovado emitido via socket - Empresa: ${empresa._id}`);
		}

		return res.status(200).json({
			status: 200,
			message: 'QR Code renovado com sucesso!',
			qrcode: qrData,
			isConnected: false
		});

	} catch (error) {
		console.error('❌ Erro ao renovar QR Code:', error);
		return res.status(400).json({ 
			status: 400, 
			error: error.message,
			message: 'Erro ao renovar QR Code'
		});
	}
});

// **📌 ROTA PARA VERIFICAR STATUS DE CONEXÃO**
router.get("/connectionStatus/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		if (currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não configurado para esta empresa.',
				empresaID: empresa._id
			});
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		// Verificar status real na Evolution API
		const connectionStatusResponse = await whatsappInstance.getInstanceStatus();
		console.log('🔍 Status de conexão verificado:', connectionStatusResponse.data);

		// Extrair informações da resposta da Evolution API
		const isConnected = connectionStatusResponse?.data?.instance?.state === 'open';
		const state = connectionStatusResponse?.data?.instance?.state || 'disconnected';

		// Atualizar status no banco se necessário
		let statusCode = 400; // padrão desconectado
		if (state === 'open') {
			statusCode = 200;
		} else if (state === 'connecting') {
			statusCode = 100;
		}

		// **🔧 SEMPRE VERIFICAR E ATUALIZAR JID QUANDO CONECTADO**
		let jidUpdated = false;
		if (isConnected) {
			console.log(`🔍 WhatsApp conectado. Verificando se JID está atualizado...`);
			try {
				const profile = await whatsappInstance.fetchProfile();
				
				if (profile.data && profile.data.wuid) {
					const currentJid = profile.data.wuid;
					
					if (empresa.whatsapp.jid !== currentJid) {
						console.log(`🔄 JID mudou! Anterior: ${empresa.whatsapp.jid || 'NENHUM'} → Novo: ${currentJid}`);
						empresa.whatsapp.jid = currentJid;
						jidUpdated = true;
						
						// **🧹 LIMPAR FOTO DE PERFIL ANTIGA QUANDO JID MUDA**
						if (empresa.whatsapp.my_profile_picture) {
							console.log(`🧹 Limpando foto de perfil antiga devido à mudança de JID`);
							empresa.whatsapp.my_profile_picture = null;
							empresa.whatsapp.my_profile_picture_updateAt = null;
						}
						
						console.log(`✅ JID atualizado: ${currentJid}`);
					} else {
						console.log(`✅ JID confirmado (sem mudança): ${currentJid}`);
					}
				} else {
					console.log(`⚠️ Não foi possível buscar o JID via fetchProfile`);
				}
			} catch (profileError) {
				console.error(`❌ Erro ao buscar JID:`, profileError.message);
			}
		}

		// Salvar mudanças se necessário
		if (empresa.whatsapp.instance_status !== statusCode || jidUpdated) {
			empresa.whatsapp.instance_status = statusCode;
			await empresa.save();
			console.log(`✅ Status atualizado para ${statusCode}${jidUpdated ? ' e JID salvo' : ''}`);
		}

		// Emitir evento de status via Socket.IO
		const AppExpressPedeja = require('../AppExpressPedeja');
		const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();
		if (socketWhatsapp) {
			socketWhatsapp.to(`chats:${empresa._id}`).emit('whatsapp_connection_update', {
				instanceId: instanceIdentifier,
				state: state,
				statusCode: statusCode,
				empresaID: empresa._id,
				isConnected: isConnected,
				timestamp: new Date()
			});

			// Se conectado, emitir evento especial
			if (isConnected) {
				socketWhatsapp.to(`chats:${empresa._id}`).emit('whatsapp_connected', {
					instanceId: instanceIdentifier,
					empresaID: empresa._id,
					state: 'open',
					message: 'WhatsApp conectado!',
					timestamp: new Date()
				});
			}
		}

		return res.status(200).json({
			status: 200,
			isConnected: isConnected,
			connectionState: state,
			instanceId: instanceIdentifier,
			statusCode: statusCode,
			jidFound: empresa.whatsapp.jid ? true : false
		});

	} catch (error) {
		console.error('❌ Erro ao verificar status:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA ENVIAR MENSAGEM**
router.post("/sendTextMessage/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { number, message } = req.body;
		const { currentUser } = req.context;

		if (currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não configurado para esta empresa.',
				empresaID: empresa._id
			});
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		// Enviar mensagem
		const result = await whatsappInstance.SendSimpleTxtMessage(number, message);
		console.log('📤 Mensagem enviada:', result.data);

		// **📌 SALVAR MENSAGEM ENVIADA NO BANCO DE DADOS**
		try {
			const cleanNumber = number.replace(/\D/g, '');
			const whatsappJid = `${cleanNumber}@s.whatsapp.net`;

			let lead = await LeadChannel.findOne({
				whatsapp_remote_jid: whatsappJid,
				empresaObjId: empresa._id,
				channel: 'whatsapp'
			});

			if (!lead) {
				// **📌 BUSCAR INFORMAÇÕES DO CONTATO VIA EVOLUTION API**
				let contactName = cleanNumber; // Fallback para número
				let contactProfilePicture = null;

				try {
					console.log(`🔍 Buscando informações do contato ${cleanNumber} via Evolution API...`);
					
					// Tentar buscar perfil do contato usando o método disponível
					const profileInfo = await whatsappInstance.getProfile(whatsappJid);
					
					if (profileInfo) {
						// Usar nome do contato se disponível
						if (profileInfo.pushName) {
							contactName = profileInfo.pushName;
							console.log(`✅ Nome encontrado via Evolution API: ${contactName}`);
						} else if (profileInfo.verifiedName) {
							contactName = profileInfo.verifiedName;
							console.log(`✅ Nome verificado encontrado via Evolution API: ${contactName}`);
						} else if (profileInfo.name) {
							contactName = profileInfo.name;
							console.log(`✅ Nome encontrado via Evolution API: ${contactName}`);
						} else if (profileInfo.notify) {
							contactName = profileInfo.notify;
							console.log(`✅ Nome de notificação encontrado via Evolution API: ${contactName}`);
						}
					}

					// Buscar foto de perfil separadamente
					try {
						const profilePicture = await whatsappInstance.getProfilePicture(whatsappJid);
						if (profilePicture && profilePicture.profilePictureUrl) {
							contactProfilePicture = profilePicture.profilePictureUrl;
							console.log(`✅ Foto de perfil encontrada via Evolution API`);
						}
					} catch (profilePictureError) {
						console.log(`⚠️ Foto de perfil não encontrada para ${cleanNumber}:`, profilePictureError.message);
					}

				} catch (contactError) {
					console.log(`⚠️ Não foi possível buscar informações do contato ${cleanNumber}:`, contactError.message);
					// Usar um nome mais amigável como fallback
					contactName = `Contato ${cleanNumber}`;
				}

				lead = await LeadChannel.create({
					empresaObjId: empresa._id,
					name: contactName,
					channel: 'whatsapp',
					channel_id: instanceIdentifier,
					mobile_number: cleanNumber,
					whatsapp_remote_jid: whatsappJid,
					channel_data: {
						whatsapp: {
							jid: whatsappJid,
							profile_picture: contactProfilePicture,
							profile_picture_updateAt: contactProfilePicture ? new Date() : null
						}
					},
					createdAt: new Date(),
					updatedAt: new Date()
				});

				console.log(`✅ Novo contato criado: ${contactName} (${cleanNumber})`);
			}

			const messageData = {
				id_message: result.data.key?.id || `sent_${Date.now()}`,
				empresaObjId: empresa._id,
				channel: 'whatsapp',
				text: message,
				whatsapp_number: whatsappJid,
				fromMe: true,
				channel_id: instanceIdentifier,
				leadChannel: lead._id,
				messageDate: new Date(),
				createdAt: new Date(),
				statusMessage: 1
			};

			const savedMessage = await Messages.create(messageData);

			// **📌 EMITIR SOCKET PARA ATUALIZAÇÃO EM TEMPO REAL**
			const AppExpressPedeja = require('../AppExpressPedeja');
			const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();

			if (socketWhatsapp) {
				lead.message = savedMessage.text;
				lead.updatedAt = new Date();
				await lead.save();

				const socketData = {
					"_id": lead._id,
					"empresaObjId": empresa._id,
					"name": lead.name,
					"channel": lead.channel,
					"channel_id": lead.channel_id,
					"mobile_number": lead.mobile_number,
					"channel_data": lead.channel_data,
					"createdAt": lead.createdAt,
					"updatedAt": lead.updatedAt,
					"__v": lead.__v,
					"message": {
						"text": savedMessage.text,
						"message": savedMessage.text,
						"messageDate": savedMessage.messageDate,
						"createdAt": savedMessage.createdAt,
						"fromMe": savedMessage.fromMe
					}
				};

				socketWhatsapp.to(`chats:${empresa._id}`).emit('chats', socketData);
				socketWhatsapp.to(`messages:${lead._id}`).emit('messages', savedMessage.toObject());
			}

		} catch (saveError) {
			console.error("❌ Erro ao salvar mensagem enviada:", saveError);
		}

		return res.status(200).json({
			status: 200,
			message: 'Mensagem enviada com sucesso!',
			result: result.data // Usar apenas .data para evitar referências circulares
		});

	} catch (error) {
		console.error('❌ Erro ao enviar mensagem:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA BUSCAR CHATS**
router.get("/chats/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		let { page, pageSize, query } = req.query;
		const { currentUser } = req.context;

		if (currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		page = parseInt(page, 10) || 1;
		pageSize = parseInt(pageSize, 10) || 30;

		// Buscar dados da empresa
		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não configurado para esta empresa.',
				empresaID: empresa._id
			});
		}

		// Buscar chats diretamente do banco local para melhor performance
		const localChats = await LeadChannel.aggregate([
			{
				$match: {
					empresaObjId: new mongoose.mongo.ObjectId(empresaID),
					channel: "whatsapp"
				}
			},
			{
				$lookup: {
					from: "messages",
					let: { leadId: "$_id" },
					pipeline: [
						{
							$match: {
								$expr: {
									$and: [
										{ $eq: ["$leadChannel", "$$leadId"] },
										{ $eq: ["$channel", "whatsapp"] }
									]
								}
							}
						},
						{ $sort: { messageDate: -1 } },
						{ $limit: 1 }
					],
					as: "lastMessage"
				}
			},
			{
				$addFields: {
					lastMessageDate: {
						$ifNull: [{ $arrayElemAt: ["$lastMessage.messageDate", 0] }, "$updatedAt"]
					}
				}
			},
			{ $sort: { lastMessageDate: -1 } }
		]);

		// Processar chats do banco local
		const processedChats = [];

		for (const leadChannel of localChats) {
			try {
				const phoneNumber = leadChannel.mobile_number;
				const remoteJid = leadChannel.whatsapp_remote_jid;

				// Pular se não tiver dados essenciais
				if (!phoneNumber || !remoteJid) {
					continue;
				}

				// Pular grupos
				if (remoteJid.includes('@g.us')) {
					continue;
				}

				// Usar última mensagem que já vem do aggregate
				const lastMessage = leadChannel.lastMessage && leadChannel.lastMessage.length > 0
					? leadChannel.lastMessage[0]
					: null;

				// Criar objeto de chat no formato esperado pelo frontend
				const chatObject = {
					_id: leadChannel._id,
					empresaObjId: leadChannel.empresaObjId,
					name: leadChannel.name,
					channel: leadChannel.channel,
					mobile_number: leadChannel.mobile_number,
					whatsapp_remote_jid: leadChannel.whatsapp_remote_jid,
					createdAt: leadChannel.createdAt,
					updatedAt: leadChannel.updatedAt,
					channel_data: {
						whatsapp: {
							profile_picture: leadChannel.channel_data?.whatsapp?.profile_picture || null
						}
					},
					message: lastMessage ? {
						_id: lastMessage._id,
						message: lastMessage.message || lastMessage.text,
						messageDate: lastMessage.messageDate,
						fromMe: lastMessage.fromMe,
						messageType: lastMessage.messageType || lastMessage.type
					} : null
				};

				// Adicionar à lista processada
				processedChats.push(chatObject);

			} catch (chatError) {
				console.error(`❌ Erro ao processar chat:`, chatError.message);
			}
		}

		// Aplicar filtros de busca se fornecidos
		let filteredChats = processedChats;

		if (query && query.length > 2) {
			filteredChats = processedChats.filter(chat => {
				const nameMatch = chat.name && chat.name.toLowerCase().includes(query.toLowerCase());
				const phoneMatch = chat.mobile_number && chat.mobile_number.includes(query);
				return nameMatch || phoneMatch;
			});
		}

		// Ordenar por data da última mensagem (mais recente primeiro)
		filteredChats.sort((a, b) => {
			const dateA = a.message ? new Date(a.message.messageDate) : new Date(a.updatedAt);
			const dateB = b.message ? new Date(b.message.messageDate) : new Date(b.updatedAt);
			return dateB - dateA;
		});

		// Aplicar paginação
		const totalCount = filteredChats.length;
		const startIndex = (page - 1) * pageSize;
		const paginatedChats = filteredChats.slice(startIndex, startIndex + pageSize);

		// Formatar resposta no formato esperado pelo frontend
		const messages = [
			{
				metadata: [{ totalCount }],
				data: paginatedChats
			}
		];

		return res.status(200).json({ status: 200, messages, page, pageSize, channel: 'whatsapp' });

	} catch (error) {
		console.error('❌ Erro ao buscar chats:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA BUSCAR MENSAGENS DE UM CHAT (FORMATO ORIGINAL)**
router.get("/chats/:empresaID/messages/:leadID", async (req, res) => {
	try {
		const { empresaID, leadID } = req.params;
		let { page, pageSize } = req.query;
		const { currentUser } = req.context;

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		page = parseInt(page, 10) || 1;
		pageSize = parseInt(pageSize, 10) || 30;

		// Buscar dados da empresa
		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não configurado para esta empresa.',
				empresaID: empresa._id
			});
		}

		// Buscar informações do lead
		const leadChannel = await LeadChannel.findOne({
			_id: leadID,
			empresaObjId: new mongoose.mongo.ObjectId(empresaID),
			channel: "whatsapp"
		});

		if (!leadChannel) {
			return res.status(404).json({ status: 404, error: 'Chat não encontrado!' });
		}

		// Usar o nome da instância se disponível, senão usar o ID como fallback
		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		// Buscar mensagens da Evolution API
		try {
			const evolutionMessages = await whatsappInstance.findMessages(leadChannel.whatsapp_remote_jid);

			if (evolutionMessages.data && Array.isArray(evolutionMessages.data) && evolutionMessages.data.length > 0) {
				console.log(`📊 ${evolutionMessages.data.length} mensagens encontradas na Evolution API`);

				// Processar mensagens da Evolution API
				const messagesToProcess = evolutionMessages.data;
				const processedMessages = [];

				for (const msg of messagesToProcess) {
					try {
						// Verificar se temos um ID válido para a mensagem
						if (!msg.key?.id) {
							console.log(`⚠️ Mensagem sem ID válido, pulando...`);
							continue;
						}

						// Verificar se a mensagem já existe no banco
						const existingMessage = await Messages.findOne({
							id_message: msg.key.id,
							empresaObjId: new mongoose.mongo.ObjectId(empresaID)
						});

						if (!existingMessage) {
							// Criar nova mensagem
							const messageData = {
								id_message: msg.key.id,
								empresaObjId: new mongoose.mongo.ObjectId(empresaID),
								leadChannel: new mongoose.mongo.ObjectId(leadID),
								channel: 'whatsapp',
								whatsapp_message_id: msg.key.id,
								messageDate: new Date(msg.messageTimestamp * 1000),
								message: extractMessageContent(msg.message),
								text: extractMessageContent(msg.message),
								fromMe: msg.key.fromMe,
								messageType: msg.message?.conversation ? 'text' : 'media',
								created_at: new Date(),
								updated_at: new Date(),
								createdAt: new Date(),
								statusMessage: 1
							};

							const newMessage = new Messages(messageData);
							await newMessage.save();

							processedMessages.push(newMessage.toObject());
							console.log(`✅ Nova mensagem salva: ${msg.key.id}`);
						} else {
							processedMessages.push(existingMessage.toObject());
						}

					} catch (msgError) {
						console.error(`❌ Erro ao processar mensagem ${msg.key.id}:`, msgError.message);
					}
				}

				// Ordenar por data (mais recente primeiro)
				processedMessages.sort((a, b) => new Date(b.messageDate) - new Date(a.messageDate));

				// Aplicar paginação
				const totalCount = processedMessages.length;
				const startIndex = (page - 1) * pageSize;
				const paginatedMessages = processedMessages.slice(startIndex, startIndex + pageSize);

				// Formatar resposta no formato esperado pelo frontend
				const messages = [
					{
						metadata: [{ totalCount }],
						data: paginatedMessages
					}
				];

				return res.status(200).json({ status: 200, messages, page, pageSize, channel: 'whatsapp' });

			} else {
				console.log(`⚠️ Nenhuma mensagem encontrada na Evolution API`);
			}
		} catch (evolutionError) {
			console.error('❌ Erro ao buscar mensagens da Evolution API:', evolutionError.message);
		}

		// Fallback: buscar mensagens do banco local
		const localMessages = await Messages.aggregate([
			{
				'$match': {
					empresaObjId: new mongoose.mongo.ObjectId(empresaID),
					leadChannel: new mongoose.mongo.ObjectId(leadID),
					channel: 'whatsapp'
				}
			}, { $sort: { messageDate: -1 } },
			{
				$facet: {
					metadata: [{ $count: "totalCount" }],
					data: [{ $skip: (page - 1) * pageSize }, { $limit: pageSize }]
				}
			}
		]);

		return res.status(200).json({ status: 200, messages: localMessages, page, pageSize, channel: 'whatsapp' });

	} catch (error) {
		console.error('❌ Erro ao buscar mensagens:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA BUSCAR MENSAGENS DE UM CHAT (FORMATO SIMPLIFICADO)**
router.get("/chats/:empresaID/messages/:leadID/simple", async (req, res) => {
	try {
		const { empresaID, leadID } = req.params;
		let { page, pageSize } = req.query;
		const { currentUser } = req.context;

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		page = parseInt(page, 10) || 1;
		pageSize = parseInt(pageSize, 10) || 30;

		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		// Buscar mensagens do banco local apenas
		const messages = await Messages.find({
			empresaObjId: empresa._id,
			leadChannel: leadID
		})
		.sort({ messageDate: -1 })
		.skip((page - 1) * pageSize)
		.limit(pageSize)
		.lean();

		return res.status(200).json({
			status: 200,
			data: messages.reverse(), // Reverter para ordem cronológica
			page,
			pageSize,
			total: await Messages.countDocuments({
				empresaObjId: empresa._id,
				leadChannel: leadID
			})
		});

	} catch (error) {
		console.error('❌ Erro ao buscar mensagens:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA MARCAR MENSAGENS COMO LIDAS**
router.put("/chats/:empresaID/messages/:leadID/mark-as-read", async (req, res) => {
	try {
		const { empresaID, leadID } = req.params;
		const { currentUser } = req.context;

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		// Buscar dados da empresa
		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (empresa.inativo) {
			return res.status(403).json({ status: 403, error: 'Empresa inativa!' });
		}

		// Buscar o lead channel
		const leadChannel = await LeadChannel.findOne({ _id: leadID, empresaObjId: empresaID });

		if (!leadChannel) {
			return res.status(404).json({ status: 404, error: 'Chat não encontrado!' });
		}

		// Buscar mensagens não lidas do contato (fromMe = false e statusMessage != 3)
		const unreadMessages = await Messages.find({
			empresaObjId: new mongoose.mongo.ObjectId(empresaID),
			leadChannel: new mongoose.mongo.ObjectId(leadID),
			channel: 'whatsapp',
			fromMe: false,
			statusMessage: { $ne: 3 } // Não lidas
		}).select('id_message whatsapp_message_id statusMessage messageDate');

		if (unreadMessages.length === 0) {
			return res.status(200).json({
				status: 200,
				message: 'Nenhuma mensagem não lida encontrada',
				markedCount: 0
			});
		}

		// Preparar dados para Evolution API
		const readMessages = unreadMessages.map(msg => ({
			remoteJid: leadChannel.whatsapp_remote_jid,
			fromMe: false,
			id: msg.whatsapp_message_id || msg.id_message
		}));

		// Usar o nome da instância se disponível, senão usar o ID como fallback
		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		try {
			// Marcar como lidas na Evolution API
			const evolutionResponse = await whatsappInstance.markMessageAsRead(readMessages);

			// Atualizar status no banco local
			const updateResult = await Messages.updateMany(
				{
					empresaObjId: new mongoose.mongo.ObjectId(empresaID),
					leadChannel: new mongoose.mongo.ObjectId(leadID),
					channel: 'whatsapp',
					fromMe: false,
					statusMessage: { $ne: 3 }
				},
				{ $set: { statusMessage: 3 } } // 3 = lida
			);

			return res.status(200).json({
				status: 200,
				message: 'Mensagens marcadas como lidas com sucesso',
				markedCount: updateResult.modifiedCount,
				evolutionResponse: evolutionResponse.data
			});

		} catch (evolutionError) {
			console.error(`❌ Erro ao marcar mensagens como lidas na Evolution API:`, evolutionError);

			// Mesmo com erro na Evolution API, atualizar banco local
			const updateResult = await Messages.updateMany(
				{
					empresaObjId: new mongoose.mongo.ObjectId(empresaID),
					leadChannel: new mongoose.mongo.ObjectId(leadID),
					channel: 'whatsapp',
					fromMe: false,
					statusMessage: { $ne: 3 }
				},
				{ $set: { statusMessage: 3 } }
			);

			return res.status(200).json({
				status: 200,
				message: 'Mensagens marcadas como lidas localmente (erro na Evolution API)',
				markedCount: updateResult.modifiedCount,
				evolutionError: evolutionError.message
			});
		}

	} catch (error) {
		console.error('❌ Erro ao marcar mensagens como lidas:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA CONTAGEM DE MENSAGENS NÃO LIDAS**
router.get("/chats/:empresaID/unread-count", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		// Buscar dados da empresa
		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (empresa.inativo) {
			return res.status(403).json({ status: 403, error: 'Empresa inativa!' });
		}

		// Buscar contagem de mensagens não lidas por leadChannel
		// statusMessage: 1 = entregue, 2 = entregue, 3 = lida
		// Mensagens não lidas: fromMe = false AND statusMessage != 3
		const unreadCounts = await Messages.aggregate([
			{
				$match: {
					empresaObjId: new mongoose.mongo.ObjectId(empresaID),
					channel: 'whatsapp',
					fromMe: false,
					statusMessage: { $ne: 3 } // Não lidas (diferente de 3)
				}
			},
			{
				$group: {
					_id: '$leadChannel',
					unreadCount: { $sum: 1 }
				}
			}
		]);

		// Converter para formato mais fácil de usar no frontend
		const unreadCountsMap = {};
		unreadCounts.forEach(item => {
			unreadCountsMap[item._id.toString()] = item.unreadCount;
		});

		return res.status(200).json({
			status: 200,
			unreadCounts: unreadCountsMap
		});

	} catch (error) {
		console.error('❌ Erro ao buscar contagem de mensagens não lidas:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA BUSCAR FOTO DE PERFIL**
router.post("/get-profile-picture/:leadChannelID", async (req, res) => {
	try {
		const { leadChannelID } = req.params;
		const { currentUser } = req.context;

		// Buscar o lead
		const lead = await LeadChannel.findOne({ _id: leadChannelID }).select("empresaObjId whatsapp_remote_jid");

		if (!lead) {
			return res.status(404).json({ status: 404, error: 'Lead não encontrado!' });
		}

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== lead.empresaObjId.toString()) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: lead.empresaObjId }).select("whatsapp");

		if (!empresa || !empresa.whatsapp) {
			return res.status(400).json({ status: 400, error: 'WhatsApp não configurado!' });
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		// Buscar foto de perfil
		const profilePicture = await whatsappInstance.getProfilePicture(lead.whatsapp_remote_jid);

		if (profilePicture && profilePicture.profilePictureUrl) {
			// Atualizar no banco de dados
			await LeadChannel.updateOne(
				{ _id: leadChannelID },
				{
					$set: {
						"channel_data.whatsapp.profile_picture": profilePicture.profilePictureUrl,
						"channel_data.whatsapp.profile_picture_updateAt": new Date(),
						updatedAt: new Date()
					}
				}
			);

			return res.status(200).json({
				status: 200,
				profilePictureUrl: profilePicture.profilePictureUrl
			});
		} else {
			return res.status(200).json({
				status: 200,
				profilePictureUrl: null
			});
		}

	} catch (error) {
		console.error('❌ Erro ao buscar foto de perfil:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA BUSCAR MINHA FOTO DE PERFIL**
router.get("/get-my-profile-picture/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("whatsapp name");

		if (!empresa || !empresa.whatsapp) {
			return res.status(400).json({
				status: 400,
				error: "Configuração do WhatsApp não encontrada para esta empresa"
			});
		}

		if (!empresa.whatsapp.jid) {
			return res.status(400).json({
				status: 400,
				error: "JID do WhatsApp não encontrado para esta empresa. Reconecte a instância."
			});
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		try {
			const myProfilePicture = await whatsappInstance.getMyProfilePicture(empresa.whatsapp.jid);

			if (myProfilePicture && myProfilePicture.profilePictureUrl) {
				await Empresa.updateOne(
					{ _id: empresaID },
					{
						$set: {
							"whatsapp.my_profile_picture": myProfilePicture.profilePictureUrl,
							"whatsapp.my_profile_picture_updateAt": new Date(),
							updatedAt: new Date()
						}
					}
				);

				return res.status(200).json({
					status: 200,
					message: "Minha foto de perfil encontrada com sucesso",
					profilePictureUrl: myProfilePicture.profilePictureUrl,
					empresaName: empresa.name
				});

			} else {
				return res.status(200).json({
					status: 200,
					message: "Nenhuma foto de perfil encontrada para sua instância",
					profilePictureUrl: null,
					empresaName: empresa.name
				});
			}
		} catch (profileError) {
			console.error(`❌ Erro ao buscar minha foto de perfil:`, profileError);
			return res.status(400).json({
				status: 400,
				message: "Erro ao buscar sua foto de perfil",
				error: profileError.message,
				empresaName: empresa.name
			});
		}

	} catch (error) {
		console.error('❌ ERROR:', error);
		return res.status(500).json({ status: 500, error: error.message });
	}
});

// **📌 ROTA PARA JOBS DE PERFIL**
router.post("/wp-jobs", async (req, res) => {
	try {
		const data = req.body.data;
		const queue = req.body.queue;
		const leadChannelID = data.leadChannelID;

		let lead = await LeadChannel.findOne({
			_id: leadChannelID
		});

		const leadWpNumber = lead?.channel_data?.whatsapp?.jid;

		const empresa = await Empresa.findOne({ _id: lead.empresaObjId }).select("inativo whatsapp status_bot");

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		try {
			const profilePicture = await whatsappInstance.getProfilePicture(leadWpNumber);

			if (profilePicture && profilePicture.profilePictureUrl) {
				await LeadChannel.updateOne(
					{ _id: leadChannelID },
					{
						$set: {
							"channel_data.whatsapp.profile_picture": profilePicture.profilePictureUrl,
							"channel_data.whatsapp.profile_picture_updateAt": new Date(),
							updatedAt: new Date()
						}
					}
				);

				return res.status(200).json({
					status: 200,
					message: "Foto de perfil atualizada com sucesso",
					profilePictureUrl: profilePicture.profilePictureUrl,
					leadName: lead.name,
					queue
				});

			} else {
				return res.status(200).json({
					status: 200,
					message: "Nenhuma foto de perfil encontrada",
					profilePictureUrl: null,
					leadName: lead.name,
					queue
				});
			}
		} catch (profileError) {
			console.error(`❌ Erro ao buscar foto de perfil:`, profileError);
			return res.status(200).json({
				status: 200,
				message: "Erro ao buscar foto de perfil",
				error: profileError.message,
				leadName: lead.name,
				queue
			});
		}
	} catch (error) {
		console.error('ERROR:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA BOT-LLM**
router.post("/bot-llm", async (req, res) => {
	try {
		const message = await dataLLm(req.body.company_id, req.body.message, req.body.lead_id);
		return res.status(200).json({ status: 200, message });
	} catch (error) {
		console.error('ERROR:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA CONFIGURAR WEBHOOK**
router.post("/webhook/configure/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		if (currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não configurado para esta empresa.',
				empresaID: empresa._id
			});
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		const webhookUrl = process.env.NODE_ENV === 'develop' && process.env.NGROK_URL
			? `${process.env.NGROK_URL}/api/v1/whatsapp/webhook`
			: 'https://api.dev.pedeja.chat/api/v1/whatsapp/webhook';

		console.log(`🔗 Configurando webhook para empresa ${empresaID}:`);
		console.log(`📍 URL do webhook: ${webhookUrl}`);
		console.log(`🏢 Instância: ${instanceIdentifier}`);

		const webhookResult = await whatsappInstance.setWebhook(webhookUrl, [
			'MESSAGES_UPSERT',
			'MESSAGES_UPDATE',
			'CONNECTION_UPDATE'
		]);

		console.log(`✅ Webhook configurado com sucesso:`, webhookResult);

		const webhookStatus = await whatsappInstance.getWebhook();
		console.log(`🔍 Status do webhook:`, webhookStatus);

		return res.status(200).json({
			status: 200,
			message: 'Webhook configurado com sucesso!',
			webhookUrl,
			instanceId: instanceIdentifier,
			webhookResult,
			webhookStatus
		});

	} catch (error) {
		console.error('❌ Erro ao configurar webhook:', error);
		return res.status(400).json({
			status: 400,
			error: error.message || error,
			details: 'Erro ao configurar webhook na Evolution API'
		});
	}
});

// **📌 ROTA PARA ATUALIZAR INFORMAÇÕES DE UM CONTATO ESPECÍFICO**
router.post("/refresh-contact-info/:leadChannelID", async (req, res) => {
	try {
		const { leadChannelID } = req.params;
		const { currentUser } = req.context;

		// Buscar o lead
		const lead = await LeadChannel.findOne({ _id: leadChannelID }).select("empresaObjId whatsapp_remote_jid name mobile_number");

		if (!lead) {
			return res.status(404).json({ status: 404, error: 'Contato não encontrado!' });
		}

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== lead.empresaObjId.toString()) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: lead.empresaObjId }).select("whatsapp");

		if (!empresa || !empresa.whatsapp) {
			return res.status(400).json({ status: 400, error: 'WhatsApp não configurado!' });
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		let updated = false;
		let newName = lead.name;
		let newProfilePicture = null;

		// **📌 BUSCAR INFORMAÇÕES ATUALIZADAS DO CONTATO**
		try {
			console.log(`🔄 Atualizando informações do contato ${lead.name}...`);
			
			// Buscar perfil atualizado
			const profileInfo = await whatsappInstance.getProfile(lead.whatsapp_remote_jid);
			
			if (profileInfo) {
				if (profileInfo.pushName && profileInfo.pushName !== lead.name) {
					newName = profileInfo.pushName;
					updated = true;
					console.log(`✅ Nome atualizado de "${lead.name}" para "${newName}"`);
				} else if (profileInfo.verifiedName && profileInfo.verifiedName !== lead.name) {
					newName = profileInfo.verifiedName;
					updated = true;
					console.log(`✅ Nome verificado atualizado para "${newName}"`);
				} else if (profileInfo.name && profileInfo.name !== lead.name) {
					newName = profileInfo.name;
					updated = true;
					console.log(`✅ Nome atualizado para "${newName}"`);
				}
			}

			// Buscar foto de perfil atualizada
			try {
				const profilePicture = await whatsappInstance.getProfilePicture(lead.whatsapp_remote_jid);
				if (profilePicture && profilePicture.profilePictureUrl) {
					newProfilePicture = profilePicture.profilePictureUrl;
					updated = true;
					console.log(`✅ Foto de perfil atualizada para ${lead.name}`);
				}
			} catch (profilePictureError) {
				console.log(`⚠️ Foto de perfil não encontrada para ${lead.name}`);
			}

			// Salvar atualizações no banco
			if (updated) {
				const updateData = {
					name: newName,
					updatedAt: new Date()
				};

				if (newProfilePicture) {
					updateData['channel_data.whatsapp.profile_picture'] = newProfilePicture;
					updateData['channel_data.whatsapp.profile_picture_updateAt'] = new Date();
				}

				if (profileInfo && profileInfo.pushName) {
					updateData['channel_data.whatsapp.pushName'] = profileInfo.pushName;
				}

				await LeadChannel.updateOne({ _id: leadChannelID }, { $set: updateData });

				return res.status(200).json({
					status: 200,
					message: 'Informações do contato atualizadas com sucesso',
					contactName: newName,
					oldName: lead.name,
					profilePictureUpdated: !!newProfilePicture,
					profilePictureUrl: newProfilePicture
				});
			} else {
				return res.status(200).json({
					status: 200,
					message: 'Informações do contato já estão atualizadas',
					contactName: lead.name
				});
			}

		} catch (error) {
			console.error(`❌ Erro ao atualizar informações do contato ${lead.name}:`, error);
			return res.status(400).json({
				status: 400,
				error: error.message,
				message: 'Erro ao buscar informações atualizadas do contato'
			});
		}

	} catch (error) {
		console.error('❌ Erro ao atualizar informações do contato:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA ATUALIZAR TODAS AS PROFILE PICTURES**
router.post("/refresh-profile-pictures/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		// Verificar autenticação se disponível
		if (currentUser && currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("whatsapp");

		if (!empresa || !empresa.whatsapp) {
			return res.status(400).json({ status: 400, error: 'WhatsApp não configurado!' });
		}

		// Buscar todos os leads sem profile picture ou com picture antiga (mais de 7 dias)
		const sevenDaysAgo = new Date(Date.now() - (7 * 24 * 60 * 60 * 1000));
		
		const leadsToUpdate = await LeadChannel.find({
			empresaObjId: empresaID,
			channel: 'whatsapp',
			$or: [
				{ 'channel_data.whatsapp.profile_picture': { $exists: false } },
				{ 'channel_data.whatsapp.profile_picture': null },
				{ 'channel_data.whatsapp.profile_picture': '' },
				{ 'channel_data.whatsapp.profile_picture_updateAt': { $lt: sevenDaysAgo } },
				{ 'channel_data.whatsapp.profile_picture_updateAt': { $exists: false } }
			]
		}).select("_id name mobile_number whatsapp_remote_jid");

		console.log(`📸 Encontrados ${leadsToUpdate.length} leads para atualizar profile pictures`);

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		let updated = 0;
		let errors = 0;

		// Processar em lotes para não sobrecarregar
		for (const lead of leadsToUpdate) {
			try {
				if (lead.whatsapp_remote_jid) {
					const profilePicture = await whatsappInstance.getProfilePicture(lead.whatsapp_remote_jid);
					
					if (profilePicture && profilePicture.profilePictureUrl) {
						await LeadChannel.updateOne(
							{ _id: lead._id },
							{
								$set: {
									"channel_data.whatsapp.profile_picture": profilePicture.profilePictureUrl,
									"channel_data.whatsapp.profile_picture_updateAt": new Date(),
									updatedAt: new Date()
								}
							}
						);
						updated++;
						console.log(`✅ Profile picture atualizada para ${lead.name || lead.mobile_number}`);
					}
				}
				
				// Pequeno delay para não sobrecarregar a API
				await new Promise(resolve => setTimeout(resolve, 100));
				
			} catch (error) {
				errors++;
				console.error(`❌ Erro ao atualizar profile picture para ${lead.name || lead.mobile_number}:`, error.message);
			}
		}

		return res.status(200).json({
			status: 200,
			message: 'Atualização de profile pictures concluída',
			total: leadsToUpdate.length,
			updated,
			errors
		});

	} catch (error) {
		console.error('❌ Erro ao atualizar profile pictures:', error);
		return res.status(400).json({ status: 400, error: error.message });
	}
});

// **📌 ROTA PARA VERIFICAR STATUS DO WEBHOOK**
router.get("/webhook/status/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		if (currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não configurado para esta empresa.',
				empresaID: empresa._id
			});
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		const webhookStatus = await whatsappInstance.getWebhook();

		const expectedWebhookUrl = process.env.NODE_ENV === 'develop' && process.env.NGROK_URL
			? `${process.env.NGROK_URL}/api/v1/whatsapp/webhook`
			: 'https://api.dev.pedeja.chat/api/v1/whatsapp/webhook';

		console.log(`🔍 Status do webhook para empresa ${empresaID}:`, webhookStatus);

		return res.status(200).json({
			status: 200,
			instanceId: instanceIdentifier,
			expectedWebhookUrl,
			webhookStatus,
			isConfigured: webhookStatus?.data?.webhook?.url === expectedWebhookUrl
		});

	} catch (error) {
		console.error('❌ Erro ao verificar webhook:', error);
		return res.status(400).json({
			status: 400,
			error: error.message || error,
			details: 'Erro ao verificar webhook na Evolution API'
		});
	}
});

// **📌 ROTAS ESPECÍFICAS PARA EVENTOS DA EVOLUTION API**
// Essas rotas processam eventos específicos da Evolution API

router.post("/webhook/messages-upsert", async (req, res) => {
	try {
		console.log('📨 Recebido webhook messages-upsert');
		
		const AppExpressPedeja = require('../AppExpressPedeja');
		const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();

		// EvolutionAPI envia instanceId em diferentes formatos
		const instanceId = req.body.instance ||
						   req.body.instanceId ||
						   req.body.data?.instanceId ||
						   req.body.owner ||
						   req.headers['appname'];

		if (!instanceId) {
			console.log('❌ instanceId não informado');
			return res.status(200).json({ status: 400, error: 'instanceId não informado!' });
		}

		// Buscar empresa
		let empresa = await Empresa.findOne({
			$or: [
				{ 'whatsapp.id': instanceId },
				{ 'whatsapp.name': instanceId }
			]
		}).select("inativo whatsapp status_bot");

		if (!empresa) {
			console.log(`❌ Empresa não encontrada para instanceId: ${instanceId}`);
			return res.status(200).json({ status: 400, error: 'Empresa não encontrada!' });
		}

		console.log(`✅ Empresa encontrada: ${empresa._id}`);

		// Processar mensagens - checar múltiplas estruturas possíveis
		const messages = req.body.messages || 
						 req.body.data?.messages || 
						 (req.body.data ? [req.body.data] : []) ||
						 (req.body.message ? [req.body.message] : []);
		
		console.log(`📨 Processando ${messages.length} mensagens`);

		for (const message of messages) {
			await processMessage(message, empresa, socketWhatsapp);
		}

		return res.status(200).json({ status: 200 });

	} catch (error) {
		console.error('❌ Erro no webhook messages-upsert:', error);
		return res.status(200).json({ status: 500, error: error.message });
	}
});

router.post("/webhook/messages-update", async (req, res) => {
	try {
		console.log('📨 Recebido webhook messages-update');
		
		const instanceId = req.body.instance ||
						   req.body.instanceId ||
						   req.body.data?.instanceId ||
						   req.body.owner ||
						   req.headers['appname'];

		if (!instanceId) {
			console.log('❌ instanceId não informado');
			return res.status(200).json({ status: 400, error: 'instanceId não informado!' });
		}

		// Buscar empresa
		let empresa = await Empresa.findOne({
			$or: [
				{ 'whatsapp.id': instanceId },
				{ 'whatsapp.name': instanceId }
			]
		}).select("inativo whatsapp status_bot");

		if (!empresa) {
			console.log(`❌ Empresa não encontrada para instanceId: ${instanceId}`);
			return res.status(200).json({ status: 400, error: 'Empresa não encontrada!' });
		}

		console.log(`✅ Empresa encontrada: ${empresa._id}`);

		// Processar atualizações de status de mensagem
		const messages = req.body.messages || [req.body];
		
		for (const msgUpdate of messages) {
			// A Evolution API v2 envia os dados em msgUpdate.data
			const messageId = msgUpdate.data?.keyId || msgUpdate.data?.messageId || msgUpdate.key?.id || msgUpdate.id;
			const rawStatus = msgUpdate.data?.status || msgUpdate.update?.status || msgUpdate.status;

			// Converter status da Evolution API para formato numérico
			let status = 1; // padrão
			if (rawStatus && (rawStatus.toUpperCase() === 'READ' || rawStatus.toLowerCase().includes('read'))) {
				status = 3; // lida
			} else if (rawStatus && (rawStatus === 'DELIVERY_ACK' || rawStatus.toLowerCase().includes('deliver'))) {
				status = 2; // entregue
			}

			console.log('🔍 Tentando atualizar mensagem:', { messageId, rawStatus, status });

			if (messageId) {
				const updateResult = await Messages.updateOne(
					{ 
						$or: [
							{ 'id_message': messageId },
							{ 'whatsapp_message_id': messageId }
						],
						empresaObjId: empresa._id
					},
					{ $set: { statusMessage: status } }
				);
				console.log("✅ Status da mensagem ID", messageId, "atualizada para:", rawStatus, "->", status);
				console.log("🔍 Resultado da atualização:", updateResult);

				// **📌 EMITIR SOCKET PARA ATUALIZAR STATUS EM TEMPO REAL**
				const AppExpressPedeja = require('../AppExpressPedeja');
				const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();
				
				if (socketWhatsapp && updateResult.modifiedCount > 0) {
					// Buscar a mensagem atualizada com dados do lead
					const updatedMessage = await Messages.findOne({
						$or: [
							{ 'id_message': messageId },
							{ 'whatsapp_message_id': messageId }
						],
						empresaObjId: empresa._id
					}).populate('leadChannel');

					if (updatedMessage && updatedMessage.leadChannel) {
						// Emitir atualização de status para o chat específico
						socketWhatsapp.to(`messages:${updatedMessage.leadChannel._id}`).emit('messageStatusUpdate', {
							messageId: messageId,
							statusMessage: status,
							rawStatus: rawStatus,
							timestamp: new Date()
						});
						console.log(`✅ Status emitido via socket para lead ${updatedMessage.leadChannel._id}`);
					}
				}
			} else {
				console.log("⚠️ messageId não encontrado:", { msgUpdate });
			}
		}

		return res.status(200).json({ status: 200 });

	} catch (error) {
		console.error('❌ Erro no webhook messages-update:', error);
		return res.status(200).json({ status: 500, error: error.message });
	}
});

router.post("/webhook/connection-update", async (req, res) => {
	try {
		console.log('📨 Recebido webhook connection-update');
		
		const AppExpressPedeja = require('../AppExpressPedeja');
		const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();

		const instanceId = req.body.instance ||
						   req.body.instanceId ||
						   req.body.data?.instanceId ||
						   req.body.owner ||
						   req.headers['appname'];

		if (!instanceId) {
			console.log('❌ instanceId não informado');
			return res.status(200).json({ status: 400, error: 'instanceId não informado!' });
		}

		// Buscar empresa
		let empresa = await Empresa.findOne({
			$or: [
				{ 'whatsapp.id': instanceId },
				{ 'whatsapp.name': instanceId }
			]
		}).select("inativo whatsapp status_bot");

		if (!empresa) {
			console.log(`❌ Empresa não encontrada para instanceId: ${instanceId}`);
			return res.status(200).json({ status: 400, error: 'Empresa não encontrada!' });
		}

		// Processar atualização de conexão
		await processConnectionUpdate(req.body, empresa, instanceId, socketWhatsapp);

		return res.status(200).json({ status: 200 });

	} catch (error) {
		console.error('❌ Erro no webhook connection-update:', error);
		return res.status(200).json({ status: 500, error: error.message });
	}
});

router.post("/webhook/send-message", async (req, res) => {
	try {
		console.log('📨 Recebido webhook send-message');
		
		const AppExpressPedeja = require('../AppExpressPedeja');
		const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();

		const instanceId = req.body.instance ||
						   req.body.instanceId ||
						   req.body.data?.instanceId ||
						   req.body.owner ||
						   req.headers['appname'];

		if (!instanceId) {
			console.log('❌ instanceId não informado');
			return res.status(200).json({ status: 400, error: 'instanceId não informado!' });
		}

		// Buscar empresa
		let empresa = await Empresa.findOne({
			$or: [
				{ 'whatsapp.id': instanceId },
				{ 'whatsapp.name': instanceId }
			]
		}).select("inativo whatsapp status_bot");

		if (!empresa) {
			console.log(`❌ Empresa não encontrada para instanceId: ${instanceId}`);
			return res.status(200).json({ status: 400, error: 'Empresa não encontrada!' });
		}

		// Processar mensagens enviadas
		const messages = req.body.messages || req.body.data?.messages || [req.body.data || req.body];
		console.log(`📨 Processando ${messages.length} mensagens enviadas`);

		for (const message of messages) {
			await processMessage(message, empresa, socketWhatsapp);
		}

		return res.status(200).json({ status: 200 });

	} catch (error) {
		console.error('❌ Erro no webhook send-message:', error);
		return res.status(200).json({ status: 500, error: error.message });
	}
});

router.post("/webhook/qrcode-updated", async (req, res) => {
	try {
		console.log('📨 Recebido webhook qrcode-updated');
		
		const AppExpressPedeja = require('../AppExpressPedeja');
		const socketWhatsapp = AppExpressPedeja.AppExpressPedeja.getSocketWhatsapp();

		const instanceId = req.body.instance ||
						   req.body.instanceId ||
						   req.body.data?.instanceId ||
						   req.body.owner ||
						   req.headers['appname'];

		if (!instanceId) {
			console.log('❌ instanceId não informado');
			return res.status(200).json({ status: 400, error: 'instanceId não informado!' });
		}

		// Buscar empresa
		let empresa = await Empresa.findOne({
			$or: [
				{ 'whatsapp.id': instanceId },
				{ 'whatsapp.name': instanceId }
			]
		}).select("inativo whatsapp status_bot");

		if (!empresa) {
			console.log(`❌ Empresa não encontrada para instanceId: ${instanceId}`);
			return res.status(200).json({ status: 400, error: 'Empresa não encontrada!' });
		}

		// Processar QR Code
		await processQRCodeUpdate(req.body, empresa, instanceId, socketWhatsapp);

		return res.status(200).json({ status: 200 });

	} catch (error) {
		console.error('❌ Erro no webhook qrcode-updated:', error);
		return res.status(200).json({ status: 500, error: error.message });
	}
});

// **📌 ROTA PARA BUSCAR E SALVAR JID MANUALMENTE**
router.post("/fetch-jid/:empresaID", async (req, res) => {
	try {
		const { empresaID } = req.params;
		const { currentUser } = req.context;

		if (currentUser.empresa_id !== empresaID) {
			return res.status(401).json({ msg: 'Acesso negado!' });
		}

		const empresa = await Empresa.findOne({ _id: empresaID }).select("inativo whatsapp name");

		if (!empresa) {
			return res.status(404).json({ status: 404, error: 'Empresa não encontrada!' });
		}

		if (!("whatsapp" in empresa) || !empresa.whatsapp.endpoint || !empresa.whatsapp.token || !empresa.whatsapp.id) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não configurado para esta empresa.',
				empresaID: empresa._id
			});
		}

		const instanceIdentifier = empresa.whatsapp.name || empresa.whatsapp.id;
		const whatsappInstance = new Whatsapp(empresa.whatsapp.endpoint, empresa.whatsapp.token, instanceIdentifier);

		// Verificar se está conectado primeiro
		const connectionStatus = await whatsappInstance.getInstanceStatus();
		const isConnected = connectionStatus?.data?.instance?.state === 'open';

		if (!isConnected) {
			return res.status(400).json({
				status: 400,
				error: 'WhatsApp não está conectado. Conecte primeiro para buscar o JID.',
				isConnected: false
			});
		}

		console.log(`🔍 Buscando JID atual para empresa ${empresa.name}...`);
		console.log(`🔍 JID atual no banco: ${empresa.whatsapp.jid || 'NENHUM'}`);

		try {
			const profile = await whatsappInstance.fetchProfile();
			
			if (profile.data && profile.data.wuid) {
				const currentJid = profile.data.wuid;
				let jidChanged = false;
				
				if (empresa.whatsapp.jid !== currentJid) {
					console.log(`🔄 JID será atualizado! Anterior: ${empresa.whatsapp.jid || 'NENHUM'} → Novo: ${currentJid}`);
					jidChanged = true;
					
					// **🧹 LIMPAR FOTO DE PERFIL ANTIGA QUANDO JID MUDA**
					const updateData = {
						'whatsapp.jid': currentJid,
						'whatsapp.instance_status': 200,
						'updatedAt': new Date()
					};
					
					if (empresa.whatsapp.my_profile_picture) {
						console.log(`🧹 Limpando foto de perfil antiga devido à mudança de JID`);
						updateData['whatsapp.my_profile_picture'] = null;
						updateData['whatsapp.my_profile_picture_updateAt'] = null;
					}
					
					// Atualizar no banco de dados
					await Empresa.updateOne({ _id: empresaID }, { $set: updateData });
					
					console.log(`✅ JID atualizado com sucesso: ${currentJid}`);

					return res.status(200).json({
						status: 200,
						message: jidChanged ? 'JID atualizado com sucesso!' : 'JID confirmado (sem mudança)',
						jid: currentJid,
						previousJid: empresa.whatsapp.jid,
						changed: jidChanged,
						empresaName: empresa.name,
						isConnected: true
					});
					
				} else {
					console.log(`✅ JID confirmado (sem mudança): ${currentJid}`);
					
					return res.status(200).json({
						status: 200,
						message: 'JID confirmado (sem mudança)',
						jid: currentJid,
						changed: false,
						empresaName: empresa.name,
						isConnected: true
					});
				}

			} else {
				console.log(`⚠️ Não foi possível buscar o JID via fetchProfile`);
				return res.status(400).json({
					status: 400,
					error: 'Não foi possível buscar o JID da instância conectada',
					profileResponse: profile
				});
			}

		} catch (profileError) {
			console.error(`❌ Erro ao buscar JID:`, profileError);
			return res.status(400).json({
				status: 400,
				message: 'Erro ao buscar JID da instância',
				error: profileError.message,
				empresaName: empresa.name
			});
		}

	} catch (error) {
		console.error('❌ Erro na rota fetch-jid:', error);
		return res.status(500).json({ status: 500, error: error.message });
	}
});

// 🔥 ===== ENDPOINTS PARA GERENCIAMENTO DA FILA DE ATENDIMENTO ===== 🔥

/**
 * GET /fila-atendimento/:empresaId
 * Busca todos os atendimentos pendentes de uma empresa
 */
router.get('/fila-atendimento/:empresaId', async (req, res) => {
	try {
		const { empresaId } = req.params;
		
		console.log('🔍 Buscando fila de atendimento para empresa:', empresaId);

		const empresa = await Empresa.findById(empresaId).select('fila_atendimento');
		
		if (!empresa) {
			return res.status(404).json({ 
				status: 404, 
				error: 'Empresa não encontrada' 
			});
		}

		// Filtrar apenas os atendimentos pendentes e ordenar por timestamp
		const atendimentosPendentes = empresa.fila_atendimento
			.filter(atendimento => atendimento.status === 'pendente')
			.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

		console.log('✅ Fila de atendimento encontrada:', {
			empresa_id: empresaId,
			total_pendentes: atendimentosPendentes.length
		});

		return res.status(200).json({
			status: 200,
			data: {
				empresa_id: empresaId,
				total_pendentes: atendimentosPendentes.length,
				atendimentos: atendimentosPendentes
			}
		});

	} catch (error) {
		console.error('❌ Erro ao buscar fila de atendimento:', error);
		return res.status(500).json({ 
			status: 500, 
			error: error.message 
		});
	}
});

/**
 * POST /fila-atendimento/:empresaId/iniciar/:atendimentoId
 * Marca um atendimento como "em_atendimento"
 */
router.post('/fila-atendimento/:empresaId/iniciar/:atendimentoId', async (req, res) => {
	try {
		const { empresaId, atendimentoId } = req.params;
		const { user_id, user_name } = req.body;

		console.log('🔄 Iniciando atendimento:', {
			empresa_id: empresaId,
			atendimento_id: atendimentoId,
			atendido_por: { user_id, user_name }
		});

		const resultado = await Empresa.updateOne(
			{ 
				_id: empresaId,
				'fila_atendimento._id': atendimentoId,
				'fila_atendimento.status': 'pendente'
			},
			{ 
				$set: { 
					'fila_atendimento.$.status': 'em_atendimento',
					'fila_atendimento.$.atendido_por': { user_id, user_name },
					'fila_atendimento.$.data_atendimento': new Date()
				}
			}
		);

		if (resultado.matchedCount === 0) {
			return res.status(404).json({
				status: 404,
				error: 'Atendimento não encontrado ou não está pendente'
			});
		}

		console.log('✅ Atendimento iniciado com sucesso');

		// Emitir via socket para atualizar o frontend em tempo real
		try {
			const AppExpressPedeja = require('../AppExpressPedeja');
			const io = AppExpressPedeja.AppExpressPedeja.getSocket();
			io.to(empresaId).emit("atendimento_iniciado", {
				atendimento_id: atendimentoId,
				atendido_por: { user_id, user_name },
				timestamp: new Date()
			});
		} catch (socketError) {
			console.log('⚠️ Erro ao emitir socket (não crítico):', socketError);
		}

		return res.status(200).json({
			status: 200,
			message: 'Atendimento iniciado com sucesso'
		});

	} catch (error) {
		console.error('❌ Erro ao iniciar atendimento:', error);
		return res.status(500).json({ 
			status: 500, 
			error: error.message 
		});
	}
});

/**
 * POST /fila-atendimento/:empresaId/finalizar/:atendimentoId
 * Marca um atendimento como "finalizado"
 */
router.post('/fila-atendimento/:empresaId/finalizar/:atendimentoId', async (req, res) => {
	try {
		const { empresaId, atendimentoId } = req.params;
		const { observacoes } = req.body;

		console.log('✅ Finalizando atendimento:', {
			empresa_id: empresaId,
			atendimento_id: atendimentoId,
			observacoes
		});

		const resultado = await Empresa.updateOne(
			{ 
				_id: empresaId,
				'fila_atendimento._id': atendimentoId,
				'fila_atendimento.status': 'em_atendimento'
			},
			{ 
				$set: { 
					'fila_atendimento.$.status': 'finalizado',
					'fila_atendimento.$.data_finalizacao': new Date(),
					'fila_atendimento.$.observacoes': observacoes || ''
				}
			}
		);

		if (resultado.matchedCount === 0) {
			return res.status(404).json({
				status: 404,
				error: 'Atendimento não encontrado ou não está em atendimento'
			});
		}

		console.log('✅ Atendimento finalizado com sucesso');

		// Emitir via socket para atualizar o frontend em tempo real
		try {
			const AppExpressPedeja = require('../AppExpressPedeja');
			const io = AppExpressPedeja.AppExpressPedeja.getSocket();
			io.to(empresaId).emit("atendimento_finalizado", {
				atendimento_id: atendimentoId,
				timestamp: new Date()
			});
		} catch (socketError) {
			console.log('⚠️ Erro ao emitir socket (não crítico):', socketError);
		}

		return res.status(200).json({
			status: 200,
			message: 'Atendimento finalizado com sucesso'
		});

	} catch (error) {
		console.error('❌ Erro ao finalizar atendimento:', error);
		return res.status(500).json({ 
			status: 500, 
			error: error.message 
		});
	}
});

/**
 * DELETE /fila-atendimento/:empresaId/cancelar/:atendimentoId
 * Remove/cancela um atendimento da fila
 */
router.delete('/fila-atendimento/:empresaId/cancelar/:atendimentoId', async (req, res) => {
	try {
		const { empresaId, atendimentoId } = req.params;
		const { motivo } = req.body;

		console.log('❌ Cancelando atendimento:', {
			empresa_id: empresaId,
			atendimento_id: atendimentoId,
			motivo
		});

		const resultado = await Empresa.updateOne(
			{ 
				_id: empresaId,
				'fila_atendimento._id': atendimentoId
			},
			{ 
				$set: { 
					'fila_atendimento.$.status': 'cancelado',
					'fila_atendimento.$.data_finalizacao': new Date(),
					'fila_atendimento.$.observacoes': motivo || 'Cancelado'
				}
			}
		);

		if (resultado.matchedCount === 0) {
			return res.status(404).json({
				status: 404,
				error: 'Atendimento não encontrado'
			});
		}

		console.log('✅ Atendimento cancelado com sucesso');

		// Emitir via socket para atualizar o frontend em tempo real
		try {
			const AppExpressPedeja = require('../AppExpressPedeja');
			const io = AppExpressPedeja.AppExpressPedeja.getSocket();
			io.to(empresaId).emit("atendimento_cancelado", {
				atendimento_id: atendimentoId,
				timestamp: new Date()
			});
		} catch (socketError) {
			console.log('⚠️ Erro ao emitir socket (não crítico):', socketError);
		}

		return res.status(200).json({
			status: 200,
			message: 'Atendimento cancelado com sucesso'
		});

	} catch (error) {
		console.error('❌ Erro ao cancelar atendimento:', error);
		return res.status(500).json({ 
			status: 500, 
			error: error.message 
		});
	}
});

/**
 * POST /fila-atendimento/:empresaId/limpar-finalizados
 * Remove todos os atendimentos finalizados/cancelados (limpeza)
 */
router.post('/fila-atendimento/:empresaId/limpar-finalizados', async (req, res) => {
	try {
		const { empresaId } = req.params;

		console.log('🧹 Limpando atendimentos finalizados para empresa:', empresaId);

		const resultado = await Empresa.updateOne(
			{ _id: empresaId },
			{ 
				$pull: { 
					fila_atendimento: { 
						status: { $in: ['finalizado', 'cancelado'] } 
					} 
				} 
			}
		);

		console.log('✅ Limpeza de atendimentos finalizada:', resultado);

		return res.status(200).json({
			status: 200,
			message: 'Atendimentos finalizados removidos com sucesso',
			resultado
		});

	} catch (error) {
		console.error('❌ Erro ao limpar atendimentos:', error);
		return res.status(500).json({ 
			status: 500, 
			error: error.message 
		});
	}
});

module.exports = router;