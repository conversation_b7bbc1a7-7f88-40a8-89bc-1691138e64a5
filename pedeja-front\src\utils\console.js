/**
 * 🚀 Console Wrapper - Remove logs automaticamente em produção
 * 
 * USO:
 * import { log, warn, error, info } from '../utils/console';
 * 
 * log('Debug info');     // ❌ Removido em produção
 * warn('Aviso');         // ✅ Mantido em produção  
 * error('Erro');         // ✅ Mantido em produção
 * info('Informação');    // ✅ Mantido em produção
 */

const isDevelopment = process.env.NODE_ENV === 'development';

// 🔧 WRAPPER INTELIGENTE
export const log = isDevelopment ? console.log.bind(console) : () => {};
export const debug = isDevelopment ? console.debug.bind(console) : () => {};
export const table = isDevelopment ? console.table.bind(console) : () => {};
export const time = isDevelopment ? console.time.bind(console) : () => {};
export const timeEnd = isDevelopment ? console.timeEnd.bind(console) : () => {};
export const trace = isDevelopment ? console.trace.bind(console) : () => {};

// ✅ SEMPRE MANTIDOS (importantes)
export const warn = console.warn.bind(console);
export const error = console.error.bind(console);
export const info = console.info.bind(console);

// 🎯 CONSOLE COLORIDO PARA DESENVOLVIMENTO
export const success = isDevelopment 
  ? (message, ...args) => console.log(`%c✅ ${message}`, 'color: #10B981; font-weight: bold;', ...args)
  : () => {};

export const warning = isDevelopment 
  ? (message, ...args) => console.log(`%c⚠️ ${message}`, 'color: #F59E0B; font-weight: bold;', ...args)
  : () => {};

export const danger = isDevelopment 
  ? (message, ...args) => console.log(`%c❌ ${message}`, 'color: #EF4444; font-weight: bold;', ...args)
  : () => {};

export const api = isDevelopment 
  ? (message, ...args) => console.log(`%c🔗 ${message}`, 'color: #3B82F6; font-weight: bold;', ...args)
  : () => {};

export const socket = isDevelopment 
  ? (message, ...args) => console.log(`%c📡 ${message}`, 'color: #8B5CF6; font-weight: bold;', ...args)
  : () => {};

// 🎨 CONSOLE COM CONTEXTO
export const whatsapp = isDevelopment 
  ? (message, ...args) => console.log(`%c💬 WhatsApp: ${message}`, 'color: #25D366; font-weight: bold;', ...args)
  : () => {};

export const pedido = isDevelopment 
  ? (message, ...args) => console.log(`%c🛒 Pedido: ${message}`, 'color: #F97316; font-weight: bold;', ...args)
  : () => {};

export const auth = isDevelopment 
  ? (message, ...args) => console.log(`%c🔐 Auth: ${message}`, 'color: #DC2626; font-weight: bold;', ...args)
  : () => {};

// 📊 GRUPO DE LOGS
export const group = isDevelopment 
  ? (title, callback) => {
      console.group(`🔍 ${title}`);
      callback?.();
      console.groupEnd();
    }
  : (title, callback) => callback?.();

// 🔥 DEFAULT EXPORT PARA USO SIMPLES
export default {
  log,
  debug,
  table,
  time,
  timeEnd,
  trace,
  warn,
  error,
  info,
  success,
  warning,
  danger,
  api,
  socket,
  whatsapp,
  pedido,
  auth,
  group
}; 