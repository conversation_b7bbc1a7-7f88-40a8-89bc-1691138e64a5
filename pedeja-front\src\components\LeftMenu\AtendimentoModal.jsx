import React, { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "../../contexts/auth";
import { toast } from "react-toastify";
import ConfirmDialog from "../ConfirmDialog";

const AtendimentoModal = ({ 
    atendimentosPendentes, 
    removerAtendimento, 
    iniciarAtendimento,
    cancelarTodosAtendimentos,
    modalOpen, 
    setModalOpen 
}) => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const [atendimentoIniciando, setAtendimentoIniciando] = useState(null);
    
    // Estados para modal de confirmação individual
    const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
    const [atendimentoParaRemover, setAtendimentoParaRemover] = useState(null);
    
    // Estados para confirmação de "Marcar Todos como Resolvidos"
    const [confirmResolverTodosOpen, setConfirmResolverTodosOpen] = useState(false);
    const [resolvendoTodos, setResolvendoTodos] = useState(false);

    if (!modalOpen) return null;

    // Função para abrir confirmação individual
    const handleConfirmarRemocao = (atendimento) => {
        setAtendimentoParaRemover(atendimento);
        setConfirmDialogOpen(true);
    };

    // Função para executar remoção individual
    const executarRemocao = () => {
        if (atendimentoParaRemover) {
            removerAtendimento(atendimentoParaRemover.atendimento_id);
            setAtendimentoParaRemover(null);
        }
    };

    // Função para resolver todos os atendimentos
    const handleResolverTodos = async () => {
        if (atendimentosPendentes.length === 0) return;
        
        const totalAtendimentos = atendimentosPendentes.length;
        setResolvendoTodos(true);
        
        try {
            // Usar função específica para operação em lote (sem toasts individuais)
            const resultado = await cancelarTodosAtendimentos();
            
            if (resultado.sucesso) {
                toast(`✅ Todos os ${resultado.total} atendimentos foram resolvidos!`, { 
                    type: 'success',
                    autoClose: 3000
                });
            } else if (resultado.cancelados > 0) {
                toast(`⚠️ ${resultado.cancelados} de ${resultado.total} atendimentos foram resolvidos. ${resultado.falharam} falharam.`, { 
                    type: 'warning',
                    autoClose: 5000
                });
            } else {
                toast(`❌ Erro ao resolver os atendimentos. Tente novamente.`, { 
                    type: 'error',
                    autoClose: 5000
                });
            }
        } catch (error) {
            console.error('Erro ao resolver todos os atendimentos:', error);
            toast(`❌ Erro inesperado ao resolver ${totalAtendimentos} atendimentos`, { type: 'error' });
        } finally {
            setResolvendoTodos(false);
        }
    };

    return (
        <div className="atendimento-modal">
            <div className="atendimento-modal-content">
                <div className="atendimento-modal-header">
                    <h3>📢 Atendimentos Pendentes</h3>
                    {atendimentosPendentes.length > 0 && (
                        <div className="atendimento-counter-badge">
                            {atendimentosPendentes.length}
                        </div>
                    )}
                </div>

                {atendimentosPendentes.length === 0 ? (
                    <div className="no-atendimentos">
                        <div className="no-atendimentos-icon">✅</div>
                        <p>Nenhum atendimento pendente</p>
                        <small>Quando houver solicitações de atendimento, elas aparecerão aqui.</small>
                    </div>
                ) : (
                    <>
                        <div className="atendimentos-list">
                            {atendimentosPendentes.map((atendimento) => {
                                // **📌 Normalizar número de telefone**
                                const telefone = atendimento.celular.split("@")[0]; // Remove o domínio
                                const telefoneFormatado = `+${telefone.slice(0, 2)} ${telefone.slice(2, 4)} ${telefone.slice(4, 8)}-${telefone.slice(8)}`;

                                // **📌 Função para iniciar atendimento**
                                const handleIniciarAtendimento = async () => {
                                    if (!user) {
                                        toast('Erro: dados do usuário não disponíveis', { type: 'error' });
                                        return;
                                    }

                                    setAtendimentoIniciando(atendimento.atendimento_id);
                                    
                                    try {
                                        const sucesso = await iniciarAtendimento(atendimento.atendimento_id, {
                                            user_id: user._id,
                                            user_name: user.name
                                        });

                                        if (sucesso) {
                                            // Navegar para o WhatsApp e fechar modal
                                            setModalOpen(false);
                                            navigate("/whatsapp", { 
                                                state: { 
                                                    lead_id: atendimento.lead_id,
                                                    // Passar dados do contato para fallback
                                                    nome: atendimento.nome,
                                                    celular: atendimento.celular?.split('@')[0], // Remove domínio @s.whatsapp.net
                                                    mensagem: atendimento.mensagem,
                                                    timestamp: atendimento.timestamp
                                                } 
                                            });
                                        }
                                    } catch (error) {
                                        console.error('Erro ao iniciar atendimento:', error);
                                    } finally {
                                        setAtendimentoIniciando(null);
                                    }
                                };

                                // **📌 Função para direcionar ao WhatsApp (atualizada)**
                                const direcionarParaWhatsApp = () => {
                                    handleIniciarAtendimento(); // Usar nova lógica com persistência
                                };

                                const isIniciando = atendimentoIniciando === atendimento.atendimento_id;

                                return (
                                    <div key={atendimento.atendimento_id} className="atendimento-card">
                                        <div className="atendimento-info">
                                            <div className="atendimento-cliente">
                                                <span className="cliente-icon">👤</span>
                                                <div>
                                                    <strong>{atendimento.nome}</strong>
                                                    <small>{telefoneFormatado}</small>
                                                </div>
                                            </div>
                                            
                                            <div className="atendimento-mensagem">
                                                <span className="mensagem-icon">💬</span>
                                                <p>{atendimento.mensagem}</p>
                                            </div>
                                            
                                            <div className="atendimento-horario">
                                                <span className="horario-icon">⏰</span>
                                                <small>{new Date(atendimento.timestamp).toLocaleString()}</small>
                                            </div>
                                        </div>

                                        <div className="atendimento-buttons">
                                            <button 
                                                className="atendimento-whatsapp-btn" 
                                                onClick={direcionarParaWhatsApp}
                                                disabled={isIniciando}
                                                style={{ opacity: isIniciando ? 0.7 : 1 }}
                                            >
                                                {isIniciando ? '⏳ Iniciando...' : '📲 Responder no WhatsApp'}
                                            </button>
                                            <button 
                                                className="atendimento-resolvido-btn" 
                                                onClick={() => handleConfirmarRemocao(atendimento)}
                                                disabled={isIniciando}
                                            >
                                                ✅ Resolvido
                                            </button>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>

                        {/* Botão para resolver todos */}
                        <div className="atendimento-actions">
                            <button 
                                className="atendimento-resolver-todos-btn" 
                                onClick={() => setConfirmResolverTodosOpen(true)}
                                disabled={resolvendoTodos}
                            >
                                {resolvendoTodos ? '⏳ Resolvendo...' : '✅ Marcar Todos como Resolvidos'}
                            </button>
                        </div>
                    </>
                )}

                <div className="atendimento-modal-footer">
                    <button 
                        className="atendimento-fechar-btn" 
                        onClick={() => setModalOpen(false)}
                    >
                        ❌ Fechar
                    </button>
                </div>
            </div>

            {/* Modal de confirmação individual */}
            <ConfirmDialog
                title="Confirmar Resolução"
                open={confirmDialogOpen}
                setOpen={setConfirmDialogOpen}
                onConfirm={executarRemocao}
            >
                <p>Tem certeza que deseja marcar este atendimento como resolvido?</p>
                {atendimentoParaRemover && (
                    <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                        <strong>Cliente:</strong> {atendimentoParaRemover.nome}<br/>
                        <strong>Mensagem:</strong> {atendimentoParaRemover.mensagem}
                    </div>
                )}
            </ConfirmDialog>

            {/* Modal de confirmação para resolver todos */}
            <ConfirmDialog
                title="Resolver Todos os Atendimentos"
                open={confirmResolverTodosOpen}
                setOpen={setConfirmResolverTodosOpen}
                onConfirm={handleResolverTodos}
            >
                <p>Tem certeza que deseja marcar <strong>todos os {atendimentosPendentes.length} atendimento(s)</strong> como resolvidos?</p>
                <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#fff3cd', borderRadius: '4px', border: '1px solid #ffeaa7' }}>
                    <strong>⚠️ Atenção:</strong> Esta ação não pode ser desfeita. Todos os atendimentos da fila serão removidos.
                </div>
            </ConfirmDialog>
        </div>
    );
};

export default AtendimentoModal;