const middlewareAuth = require("../lib/middlewareAuth");
const GetUsersByRoleUseCase = require("../application/users/useCases/getUsersByRole");
const CreateUserUseCase = require("../application/users/useCases/createUser");

const UserDAO = require("../DAOS/UserDAO");
const User = require("../models/User");
const EmpresaDAO = require("../DAOS/EmpresaDAO");
const Empresa = require("../models/Empresa");
const CounterDAO = require("../DAOS/CounterDAO");
const Counter = require("../models/Counter");

const router = require("express").Router();

router.get("/list-users-by-role/:role", middlewareAuth, async (req, res) => {
  const role = req.params.role;
  const { vinculo_empresa } = req.context.currentUser;

  console.log("vinculo", vinculo_empresa);

  const roles = ["garcom", "Admin", "empresa", "revenda"];

  if (!roles.includes(role)) {
    return res.status(400).json({ message: "Cargo inválido." });
  }

  const userDAO = new UserDAO(User);
  const getUserByRoleUseCase = new GetUsersByRoleUseCase(userDAO);
  const result = await getUserByRoleUseCase.execute(vinculo_empresa, role);

  if (!result.status) {
    return res.status(400).json({ message: result.message });
  }

  return res.json(result.data);
});

router.post("/users/garcom", middlewareAuth, async (req, res) => {
  const { name, email, password, number } = req.body;

  //validacoes
  if (!name) {
    return res.status(422).json({ msg: "O nome é obrigatório!" });
  }
  if (!number) {
    return res.status(422).json({ msg: "O número é obrigatório!" });
  }

  if (!email) {
    return res.status(422).json({ msg: "O email é obrigatório!" });
  }
  if (!password) {
    return res.status(422).json({ msg: "A senha é obrigatória!" });
  }

  const userDAO = new UserDAO(User);
  const companyDAO = new EmpresaDAO(Empresa);
  const counterDAO = new CounterDAO(Counter);
  const createUserUseCase = new CreateUserUseCase(
    userDAO,
    companyDAO,
    counterDAO
  );

  const result = await createUserUseCase.execute({
    email,
    name,
    password,
    empresaId: req.context.currentUser.vinculo_empresa,
    role: "garcom",
    createdBy: req.context.currentUser.id,
    number,
  });

  if (!result.status) {
    return res.status(400).json({ message: result.message });
  }

  return res.status(201).json(result.data);
});

module.exports = router;
