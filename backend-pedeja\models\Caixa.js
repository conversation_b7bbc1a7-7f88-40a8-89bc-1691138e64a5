const mongoose = require('mongoose')

const CaixaSchema = new mongoose.Schema({
empresaObjId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Empresa',
    required: true,
},
id_caixa: Number,
id_empresa: Number,
createdBy: String,
data_abertura: Date,
data_fechamento: Date,
saldo_inicial: Number,
saldo_final: Number,
status_caixa: Boolean,
lancamentos_caixa: [{
    descricao: String,
    tipo_lancamento: String,
    valor: Number,
    id_pedido: String,
    createdAt: Date,
    updatedAt: Date,
    deletedAt: Date
}],
valor_informado_dinheiro: Number,
valor_informado_cartao: Number,
valor_informado_pix: Number,
inativo:Boolean,
bloqueado:Boolean,

createdAt: Date,
updatedAt: Date,
deletedAt: Date

})

const Caixa = mongoose.model('Caixa', CaixaSchema)

module.exports = Caixa