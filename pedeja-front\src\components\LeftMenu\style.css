/* Google Fonts - Poppins */
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  --font-family: "Open Sans", sans-serif;
  font-family: "Poppins", sans-serif;
}

@supports (scrollbar-color: #999 #f5f5f5) {
  * {
    scrollbar-color: #999 #f5f5f5;
    scrollbar-width: thin;
  }
}

.container {
  width: 100%;
  height: 100vh;
  display: flex;
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.212);
}

.boxLeftMenuScroll {
  scrollbar-color: auto;
  scrollbar-width: auto;
}

/* Estiliza a barra de rolagem */
.boxLeftMenuScroll::-webkit-scrollbar {
  width: 7px;
  /* Largura da barra de rolagem */
}

/* Estiliza a parte interna da barra de rolagem */
.boxLeftMenuScroll::-webkit-scrollbar-track {
  background: #f0f0f0;
  /* Cor do fundo da barra de rolagem */
  border-radius: 10px;
  /* Cantos arredondados */
}

/* Estiliza a "thumb" da barra de rolagem */
.boxLeftMenuScroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  /* Cor da "thumb" */
  border-radius: 10px;
  /* Cantos arredondados */
}

/* Cor ao passar o mouse sobre a "thumb" */
.boxLeftMenuScroll::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
  /* Cor mais escura ao passar o mouse */
}

/* Remove as setas */
.boxLeftMenuScroll::-webkit-scrollbar-button {
  display: none;
  /* Remove as setas padrão */
}

/*
nav {
  position: fixed;
  top: 0;
  left: 0;
  height: 70px;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
}*/
.info-licenca {
  background: rgba(255, 0, 0, 1);
  --background: linear-gradient(to left, #4281ff, #51d2ff);
  --left: 250px;
  -webkit-transition: 150ms;
  transition: 150ms;
  /* padding-right: 250px; */
  height: 50px;
  --width: calc(100% - 250px);
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  --justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  --border-bottom: 2px solid #0000001c;
  position: relative;
  z-index: 1;
  /* box-shadow: 1px 1px 6px rgb(180, 180, 180); */
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: 600;
}

/* Estilos antigos de loading/error movidos para a seção moderna abaixo */

nav .logo {
  display: flex;
  align-items: center;
  margin: 0 24px;
}

.logo .menu-icon {
  color: #333;
  font-size: 24px;
  margin-right: 14px;
  cursor: pointer;
}

.logo .logo-name {
  color: #333;
  font-size: 22px;
  font-weight: 500;
}

nav .sidebar {
  position: fixed;
  top: 0;
  left: -100%;
  height: 100%;
  width: 17%;
  padding: 20px 0;
  --background-color: #fff;
  background-color: rgb(247, 247, 247) !important;
  box-shadow: 0 5px 1px rgba(0, 0, 0, 0.3);
  transition: all 0.4s ease;
}

nav.open .sidebar {
  left: 0;
}

.sidebar .sidebar-content {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
  padding: 30px 16px;
}

.sidebar-content .list {
  list-style: none;
}

.list .nav-link {
  --display: flex;
  display: list-item;
  align-items: center;
  margin: 8px 0;
  padding: 14px 12px;
  border-radius: 8px;
  text-decoration: none;
  cursor: pointer;
}

.lists .nav-link:hover {
  background: linear-gradient(to left, #2d74ff, #07bfff);
}

.nav-link .icon {
  margin-right: 14px;
  font-size: 20px;
  color: #707070;
}

.nav-link .link {
  font-size: 16px;
  color: #707070;
  font-weight: 400;
}

.lists .nav-link:hover .icon,
.lists .nav-link:hover .link {
  color: #fff;
}

.overlay {
  position: fixed;
  top: 0;
  left: -100%;
  height: 1000vh;
  width: 200%;
  opacity: 0;
  pointer-events: none;
  transition: all 0.4s ease;
  background: rgba(0, 0, 0, 0.3);
}

nav.open~.overlay {
  opacity: 1;
  left: 260px;
  pointer-events: auto;
}

.subMenu {
  top: -39px;
  --background: transparent !important;
}

.menu-anchor {
  background: transparent !important;
}

.test:hover {
  background: #fff !important;
}

.sub-menu-content {
  background: transparent !important;
}

.menu-item.sub-menu {
  height: 50px;
}

.menu-item.sub-menu.open {
  height: 150px;
}

.collapseDiv {
  display: flex;
  justify-content: right;
}

.userCircleImg {
  display: flex;
  justify-content: center;
}

.userImg {
  border-radius: 100%;
  padding: 0px;
  border: solid 3px #4281ff;
}

.menu-dropdownShow {
  position: absolute;
  background: white;
  display: flex;
  top: 84px;
  border: 1px solid #e1e1e1;
  width: 218px;
}

.menu-dropdownClosed {
  position: relative;
  left: 169px;
  background: white;
  display: none;
  top: 124px;
  border: 1px solid #e1e1e1;
  width: 218px;
}

.divStatusPrintNavBar {
  display: flex;
  justify-content: center;
  background: #07c670;
  color: white;
  padding: 1px;
  border-radius: 4px;

  span {
    border: 1px solid white;
    padding: 0px 4px 0px 4px;
    border-radius: 4px;
    font-size: 12px;
    width: 100%;
    text-align: center;
  }
}

.divStatusPrintNavBarOffline {
  display: flex;
  justify-content: center;
  background: #ff0000b5;
  color: white;
  padding: 1px;
  border-radius: 4px;

  span {
    border: 1px solid white;
    padding: 0px 4px 0px 4px;
    border-radius: 4px;
    font-size: 12px;
    width: 100%;
    text-align: center;
  }
}

.option.bottom {
  display: inline-flex;
  width: 100%;
  justify-content: space-between;
  padding: 8px;
  height: 40px;
  border-bottom: 1px solid lightgray;
}

.option.bottom-last {
  display: inline-flex;
  width: 100%;
  justify-content: space-between;
  padding: 8px;
  height: 40px;
}

.description {
  padding-left: 5px;
}

/* Switch 1 Specific Styles Start */

.wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  /*--width: 400px;
	--margin: 50vh auto 0;
	-ms-flex-wrap: wrap;
	    flex-wrap: wrap;
	-webkit-transform: translateY(-50%);
	        transform: translateY(-50%);
  */
}

.switch_box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  --max-width: 200px;
  --min-width: 200px;
  --height: 200px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  /*-webkit-box-flex: 1;
	    -ms-flex: 1;
	        flex: 1;*/
}

.box_1 {
  background: transparent;
}

input[type="checkbox"].switch_1 {
  font-size: 15px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 3.5em;
  height: 1.5em;
  background: #ddd;
  border-radius: 3em;
  position: relative;
  cursor: pointer;
  outline: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

input[type="checkbox"].switch_1:checked {
  background: #0ebeff;
}

input[type="checkbox"].switch_1:after {
  position: absolute;
  content: "";
  width: 1.5em;
  height: 1.5em;
  border-radius: 50%;
  background: #fff;
  -webkit-box-shadow: 0 0 0.25em rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 0.25em rgba(0, 0, 0, 0.3);
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
  left: 0;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

input[type="checkbox"].switch_1:checked:after {
  left: calc(100% - 1.5em);
}

/* Switch 1 Specific Style End */

/* Switch Aceite automatico Start */
.wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.switch_box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  --max-width: 200px;
  --min-width: 200px;
  --height: 200px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.box_1 {
  background: transparent;
}

input[type="checkbox"].switch_aceiteAutomatico {
  font-size: 15px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 3.5em;
  height: 1.5em;
  background: #ddd;
  border-radius: 3em;
  position: relative;
  cursor: pointer;
  outline: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

input[type="checkbox"].switch_aceiteAutomatico:checked {
  background: #0ebeff;
}

input[type="checkbox"].switch_aceiteAutomatico:after {
  position: absolute;
  content: "";
  width: 1.5em;
  height: 1.5em;
  border-radius: 50%;
  background: #fff;
  -webkit-box-shadow: 0 0 0.25em rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 0.25em rgba(0, 0, 0, 0.3);
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
  left: 0;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

input[type="checkbox"].switch_aceiteAutomatico:checked:after {
  left: calc(100% - 1.5em);
}

/* Switch Aceite automatico End */

.clickNavigateToPlanos {
  text-decoration: underline;
  cursor: pointer;
  font-weight: 700;
  transition: all .2s;
}

.clickNavigateToPlanos:hover {
  color: black;
}

@media (max-width: 1440px) {
  nav .sidebar {
    width: 100%;
    left: -100%;
  }

  nav.open .sidebar {
    left: 0;
  }

  nav .logo {
    margin: 0 16px;
  }

  .overlay {
    left: 100%;
  }

  nav.open~.overlay {
    left: 0;
  }

  div.hidden-mobile {
    display: none !important;
  }
}

@media (max-width: 780px) {

  .hidden-sm-mobile {
    display: none !important;
  }
}


@media (min-width: 880px) {
  .desktop-hidden {
    display: none;
  }
}

/* Estilos antigos removidos - agora usando os novos estilos modernos acima */

/* Animação antiga "shaking" removida - agora usando "waving-realistic" mais suave */

.atendimento-modal {
  position: fixed;
  top: 88px;
  right: 62px;
  background: white;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 0;
  border-radius: 16px;
  z-index: 1000;
  max-height: 550px;
  overflow: hidden;
  max-width: 380px;
  min-width: 360px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Header do modal */
.atendimento-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%);
  color: white;
  border-radius: 16px 16px 0 0;
}

.atendimento-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.atendimento-counter-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

/* Container principal do conteúdo */
.atendimento-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Lista de atendimentos */
.atendimentos-list {
  max-height: 320px;
  overflow-y: auto;
  padding: 16px 0;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f0f0f0;
}

.atendimentos-list::-webkit-scrollbar {
  width: 6px;
}

.atendimentos-list::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 10px;
}

.atendimentos-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.atendimentos-list::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Card de atendimento */
.atendimento-card {
  margin: 0 24px 16px;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.atendimento-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.atendimento-card:last-child {
  margin-bottom: 0;
}

/* Informações do atendimento */
.atendimento-info {
  margin-bottom: 16px;
}

.atendimento-cliente {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f1f3f4;
}

.cliente-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  flex-shrink: 0;
}

.atendimento-cliente div {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.atendimento-cliente strong {
  font-size: 15px;
  color: #2c3e50;
  font-weight: 600;
}

.atendimento-cliente small {
  font-size: 13px;
  color: #6c757d;
  font-weight: 400;
}

.atendimento-mensagem {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 12px;
}

.mensagem-icon {
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.atendimento-mensagem p {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
  color: #495057;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.atendimento-horario {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6c757d;
}

.horario-icon {
  font-size: 14px;
}

.atendimento-horario small {
  font-size: 12px;
}

/* Botões dos atendimentos */
.atendimento-buttons {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.atendimento-buttons button {
  flex: 1;
  border: none;
  padding: 10px 16px;
  cursor: pointer;
  font-weight: 600;
  font-size: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.atendimento-whatsapp-btn {
  background: linear-gradient(135deg, #25d366 0%, #20c65a 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);
}

.atendimento-whatsapp-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
}

.atendimento-resolvido-btn {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.atendimento-resolvido-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.atendimento-buttons button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Seção sem atendimentos */
.no-atendimentos {
  text-align: center;
  padding: 40px 24px;
  color: #6c757d;
}

.no-atendimentos-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.7;
}

.no-atendimentos p {
  font-size: 16px;
  margin: 0 0 8px;
  color: #495057;
  font-weight: 500;
}

.no-atendimentos small {
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

/* Ações do modal */
.atendimento-actions {
  padding: 16px 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.atendimento-resolver-todos-btn {
  width: 100%;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 5px 16px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.atendimento-resolver-todos-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.atendimento-resolver-todos-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Footer do modal */
.atendimento-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e9ecef;
  background: white;
  border-radius: 0 0 16px 16px;
}

.atendimento-fechar-btn {
  width: 100%;
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  border: none;
  padding: 5px 16px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.atendimento-fechar-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

/* Responsividade para mobile */
@media (max-width: 768px) {
  .atendimento-modal {
    top: 20px;
    right: 20px;
    left: 20px;
    max-width: none;
    min-width: auto;
    width: calc(100% - 40px);
  }
  
  .atendimento-modal-header,
  .atendimento-actions,
  .atendimento-modal-footer {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .atendimentos-list {
    padding: 12px 0;
  }
  
  .atendimento-card {
    margin-left: 20px;
    margin-right: 20px;
    padding: 12px;
  }
  
  .atendimento-buttons {
    flex-direction: column;
    gap: 8px;
  }
  
  .atendimento-buttons button {
    padding: 12px 16px;
  }
}

/* Animação de entrada */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.atendimento-modal {
  animation: modalFadeIn 0.3s ease-out;
}

/* Remover estilos antigos que foram substituídos */
.atendimento-modal-content h3 {
  margin-top: 0;
  padding-bottom: 0;
}

.atendimento-modal-content p {
  font-size: 14px;
  margin-bottom: 8px !important;
}

/* Estiliza a barra de rolagem */
.atendimento-modal::-webkit-scrollbar {
  width: 7px;
  /* Largura da barra de rolagem */
}

/* Estiliza a parte interna da barra de rolagem */
.atendimento-modal::-webkit-scrollbar-track {
  background: #f0f0f0;
  /* Cor do fundo da barra de rolagem */
  border-radius: 10px;
  /* Cantos arredondados */
}

/* Estiliza a "thumb" da barra de rolagem */
.atendimento-modal::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  /* Cor da "thumb" */
  border-radius: 10px;
  /* Cantos arredondados */
}

/* Cor ao passar o mouse sobre a "thumb" */
.atendimento-modal::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
  /* Cor mais escura ao passar o mouse */
}

/* Remove as setas */
.atendimento-modal::-webkit-scrollbar-button {
  display: none;
  /* Remove as setas padrão */
}

.atendimento-notification {
  background: red;
  color: white;
  padding: 5px;
  border-radius: 50%;
  font-size: 12px;
  margin-left: 5px;
}

.atendimento-teste-btn {
  color: black;
  background-color: #e1e1e1;
  border: 1px solid gray;
  max-width: 120px;
}

.collapseInLeftMenuBtn {
  color: gray;
  right: -8px;
  cursor: pointer;
  position: absolute;
  font-size: 20px;
  width: 30px;
  height: 30px;
  padding: 4px;
  background: white;
  border-radius: 50%;
  border: 1px solid gray;
  transition: all .2s;
}

.collapseOutLeftMenuBtn {
  color: gray;
  cursor: pointer;
  position: absolute;
  font-size: 20px;
  border-radius: 50%;
  right: -8px;
  background: white;
  border: 1px solid gray;
  padding: 4px;
  width: 30px;
  height: 30px;
  transition: all .2s;
}

.collapseInLeftMenuBtn:hover {
  font-size: 25px;
  width: 35px;
  height: 35px;
}

.collapseOutLeftMenuBtn:hover{
  font-size: 25px;
  width: 35px;
  height: 35px;
}

/* Estilos para o componente "Comece por aqui" */
.comece-por-aqui-container {
  position: relative;
  overflow: hidden;
}

.comece-por-aqui-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.comece-por-aqui-container:hover::before {
  left: 100%;
}

/* Responsividade para dispositivos menores */
@media (max-width: 768px) {
  .comece-por-aqui-container {
    margin: 8px !important;
    padding: 12px !important;
  }
}

/* ======================== DROPDOWN STATUS ROBO MELHORADO ======================== */
/* Estilos modernos e responsivos para o dropdown do Status Robo */

.menu-dropdownShow {
  position: absolute !important;
  background: white !important;
  display: flex !important;
  flex-direction: column !important;
  top: 82px !important;
  right: 0 !important;
  left: auto !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 12px !important;
  width: 250px !important;
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  z-index: 1000 !important;
  transform: translateY(0) !important;
  opacity: 1 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) !important;
  overflow: hidden !important;
}

.menu-dropdownClosed {
  position: absolute !important;
  background: white !important;
  display: none !important;
  flex-direction: column !important;
  top: 82px !important;
  right: 0 !important;
  left: auto !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 12px !important;
  width: 250px !important;
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.08) !important;
  z-index: 1000 !important;
  transform: translateY(-10px) !important;
  opacity: 0 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
}

/* Container dos botões melhorado */
.menu-options {
  display: flex !important;
  flex-direction: column !important;
  padding: 8px !important;
  background: white !important;
  border-radius: 12px !important;
}

/* Opções individuais modernizadas */
.option.bottom {
  display: flex !important;
  width: 100% !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 12px 16px !important;
  height: auto !important;
  min-height: 50px !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06) !important;
  border-radius: 8px !important;
  margin-bottom: 4px !important;
  transition: all 0.3s ease !important;
  background: rgba(249, 250, 251, 0.5) !important;
}

.option.bottom:hover {
  background: rgba(49, 140, 213, 0.05) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.option.bottom-last {
  display: flex !important;
  width: 100% !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 12px 16px !important;
  height: auto !important;
  min-height: 50px !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  background: rgba(249, 250, 251, 0.5) !important;
}

.option.bottom-last:hover {
  background: rgba(49, 140, 213, 0.05) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Informações da opção */
.info-option {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  flex: 1 !important;
}

.status-option {
  width: 10px !important;
  height: 10px !important;
  border-radius: 50% !important;
  background: #07c670 !important;
  box-shadow: 0 0 0 2px rgba(7, 198, 112, 0.2) !important;
  flex-shrink: 0 !important;
}

.label-option {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  flex: 1 !important;
}

.label-option svg {
  flex-shrink: 0 !important;
  transition: transform 0.3s ease !important;
}

.option:hover .label-option svg {
  transform: scale(1.1) !important;
}

.description {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #374151 !important;
  line-height: 1.4 !important;
  margin: 0 !important;
  padding: 0 !important;
  transition: all 0.3s ease !important;
}

/* Efeito hover para textos clicáveis */
.option .description[style*="cursor: pointer"],
.option div[style*="cursor: pointer"] .description {
  transition: all 0.3s ease !important;
}

.option .description[style*="cursor: pointer"]:hover,
.option div[style*="cursor: pointer"]:hover .description {
  color: #318CD5 !important;
  font-weight: 600 !important;
  transform: translateX(2px) !important;
}

/* Estilo específico para elementos clicáveis */
.option [style*="cursor: pointer"] {
  transition: all 0.3s ease !important;
}

.option [style*="cursor: pointer"]:hover {
  color: #318CD5 !important;
}

/* Destaque para label-option clicável */
.label-option[style*="cursor: pointer"] {
  border-radius: 6px !important;
  padding: 4px 6px !important;
  margin: -4px -6px !important;
  transition: all 0.3s ease !important;
}

.label-option[style*="cursor: pointer"]:hover {
  background: rgba(49, 140, 213, 0.08) !important;
  transform: translateX(3px) !important;
}

/* Efeito de ripple sutil ao clicar */
.label-option[style*="cursor: pointer"]:active {
  transform: translateX(1px) scale(0.98) !important;
  transition: all 0.1s ease !important;
}

/* Indicador visual para elementos interativos */
.label-option[style*="cursor: pointer"]::before {
  content: '' !important;
  position: absolute !important;
  left: -8px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 3px !important;
  height: 0 !important;
  background: #318CD5 !important;
  border-radius: 2px !important;
  transition: height 0.3s ease !important;
}

.label-option[style*="cursor: pointer"]:hover::before {
  height: 20px !important;
}

/* Switch melhorado */
.wrapper {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.switch_box {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

input[type="checkbox"].switch_1 {
  font-size: 15px !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  width: 3.2em !important;
  height: 1.8em !important;
  background: #e5e7eb !important;
  border-radius: 2em !important;
  position: relative !important;
  cursor: pointer !important;
  outline: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: 2px solid transparent !important;
}

input[type="checkbox"].switch_1:checked {
  background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%) !important;
  box-shadow: 
    0 4px 12px rgba(49, 140, 213, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

input[type="checkbox"].switch_1:after {
  position: absolute !important;
  content: "" !important;
  width: 1.4em !important;
  height: 1.4em !important;
  border-radius: 50% !important;
  background: white !important;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.15),
    0 1px 4px rgba(0, 0, 0, 0.1) !important;
  transform: scale(1) !important;
  left: 0.2em !important;
  top: 50% !important;
  margin-top: -0.7em !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

input[type="checkbox"].switch_1:checked:after {
  left: calc(100% - 1.6em) !important;
  box-shadow: 
    0 3px 12px rgba(49, 140, 213, 0.2),
    0 1px 4px rgba(0, 0, 0, 0.1) !important;
}

input[type="checkbox"].switch_1:hover {
  transform: scale(1.05) !important;
}

input[type="checkbox"].switch_1:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(49, 140, 213, 0.2) !important;
}

/* Responsividade */
@media (max-width: 768px) {
  .menu-dropdownShow {
    width: 280px !important;
    right: auto !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    top: 85px !important;
  }
  
  .menu-dropdownClosed {
    width: 280px !important;
    right: auto !important;
    left: 50% !important;
    transform: translateX(-50%) translateY(-10px) !important;
    top: 85px !important;
  }
  
  .option.bottom,
  .option.bottom-last {
    padding: 10px 12px !important;
    min-height: 45px !important;
  }
  
  .description {
    font-size: 13px !important;
  }
}

@media (max-width: 480px) {
  .menu-dropdownShow {
    width: 260px !important;
    right: auto !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    top: 88px !important;
    max-width: calc(100vw - 20px) !important;
    margin-left: 10px !important;
    margin-right: 10px !important;
  }
  
  .menu-dropdownClosed {
    width: 260px !important;
    right: auto !important;
    left: 50% !important;
    transform: translateX(-50%) translateY(-10px) !important;
    top: 88px !important;
    max-width: calc(100vw - 20px) !important;
    margin-left: 10px !important;
    margin-right: 10px !important;
  }
  
  .option.bottom,
  .option.bottom-last {
    padding: 8px 10px !important;
    min-height: 42px !important;
  }
  
  .description {
    font-size: 12px !important;
  }
  
  input[type="checkbox"].switch_1 {
    width: 2.8em !important;
    height: 1.6em !important;
  }
}

/* Para telas muito pequenas */
@media (max-width: 320px) {
  .menu-dropdownShow {
    width: clamp(200px, 90vw, 240px) !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    max-width: calc(100vw - 16px) !important;
    margin-left: 8px !important;
    margin-right: 8px !important;
  }
  
  .menu-dropdownClosed {
    width: clamp(200px, 90vw, 240px) !important;
    left: 50% !important;
    transform: translateX(-50%) translateY(-10px) !important;
    max-width: calc(100vw - 16px) !important;
    margin-left: 8px !important;
    margin-right: 8px !important;
  }
  
  .option.bottom,
  .option.bottom-last {
    padding: 6px 8px !important;
    min-height: 40px !important;
  }
  
  .description {
    font-size: 11px !important;
  }
}



/* Garantir que o container pai não corte o dropdown */
@media (max-width: 768px) {
  .d-flex {
    position: relative !important;
    overflow: visible !important;
  }
  
  /* Container específico do dropdown */
  .d-flex > div[style*="position: relative"] {
    overflow: visible !important;
  }
}

/* Animação de entrada suave */
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes dropdownFadeInMobile {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

.menu-dropdownShow {
  animation: dropdownFadeIn 0.3s ease-out !important;
}

/* Animação específica para mobile */
@media (max-width: 768px) {
  .menu-dropdownShow {
    animation: dropdownFadeInMobile 0.3s ease-out !important;
  }
}

/* Overlay para fechar ao clicar fora */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 999;
  display: none;
}

.dropdown-overlay.active {
  display: block;
}

/* 🎨 NOVO: Estilos modernos para o ícone de atendimentos */

/* Container do ícone */
.item-menu-help-info {
  display: inline-grid;
  justify-content: center;
  justify-items: center;
  align-items: center;
  width: 62px;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e9ecef;
  position: relative;
  overflow: hidden;
}

.item-menu-help-info:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.item-menu-help-info-active {
  display: inline-grid;
  justify-content: center;
  justify-items: center;
  align-items: center;
  width: 62px;
  height: 100%;
  background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #318CD5;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(49, 140, 213, 0.3);
}

.item-menu-help-info-active:hover {
  background: linear-gradient(135deg, #2c7bbf 0%, #4facfe 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 25px rgba(49, 140, 213, 0.4);
}

/* Estilos do SVG */
.atendimento-icon-inactive {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: none;
}

.atendimento-icon-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(49, 140, 213, 0.2));
}

/* Adicionar sombra sutil à pessoa principal quando ativo */
.atendimento-icon-active .main-person {
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.2));
}

/* 🌊 ANIMAÇÃO DE ACENAR REALISTA */
@keyframes waving-realistic {
  0% { 
    transform: rotate(0deg) translateY(0px);
    opacity: 1;
  }
  15% { 
    transform: rotate(-15deg) translateY(-1px);
    opacity: 0.9;
  }
  25% { 
    transform: rotate(12deg) translateY(-2px);
    opacity: 1;
  }
  35% { 
    transform: rotate(-10deg) translateY(-1px);
    opacity: 0.9;
  }
  45% { 
    transform: rotate(8deg) translateY(-2px);
    opacity: 1;
  }
  55% { 
    transform: rotate(-6deg) translateY(-1px);
    opacity: 0.95;
  }
  65% { 
    transform: rotate(4deg) translateY(-1px);
    opacity: 1;
  }
  75% { 
    transform: rotate(-2deg) translateY(0px);
    opacity: 0.98;
  }
  85% { 
    transform: rotate(1deg) translateY(0px);
    opacity: 1;
  }
  100% { 
    transform: rotate(0deg) translateY(0px);
    opacity: 1;
  }
}

/* Aplicar animação no braço */
#waving-animation .waving-arm {
  animation: waving-realistic 2s ease-in-out infinite;
  animation-delay: 0.5s;
}

/* Animação da mão para adicionar mais realismo */
@keyframes hand-wave {
  0%, 100% { 
    transform: scale(1) rotate(0deg);
  }
  25% { 
    transform: scale(1.1) rotate(-5deg);
  }
  50% { 
    transform: scale(1.05) rotate(3deg);
  }
  75% { 
    transform: scale(1.08) rotate(-2deg);
  }
}

#waving-animation .waving-hand {
  animation: hand-wave 2s ease-in-out infinite;
  animation-delay: 0.7s;
}

/* 🎭 ANIMAÇÕES DAS PARTÍCULAS DE ATENÇÃO */
@keyframes particle-float-1 {
  0%, 100% { 
    transform: translateY(0px) scale(1);
    opacity: 0.9;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
  33% { 
    transform: translateY(-3px) scale(1.2);
    opacity: 1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
  }
  66% { 
    transform: translateY(-6px) scale(0.9);
    opacity: 0.7;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
}

@keyframes particle-float-2 {
  0%, 100% { 
    transform: translateX(0px) scale(1);
    opacity: 0.7;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
  25% { 
    transform: translateX(-2px) scale(1.3);
    opacity: 0.9;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
  }
  75% { 
    transform: translateX(2px) scale(0.8);
    opacity: 0.5;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
}

@keyframes particle-float-3 {
  0%, 100% { 
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.6;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
  50% { 
    transform: translateY(-4px) translateX(-2px) scale(1.5);
    opacity: 0.8;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
  }
}

.particle-1 {
  animation: particle-float-1 3s ease-in-out infinite;
  animation-delay: 0.2s;
}

.particle-2 {
  animation: particle-float-2 2.5s ease-in-out infinite;
  animation-delay: 0.8s;
}

.particle-3 {
  animation: particle-float-3 3.5s ease-in-out infinite;
  animation-delay: 1.2s;
}

/* 💫 EFEITO PULSO SUTIL NO CONTAINER ATIVO */
@keyframes gentle-pulse {
  0%, 100% { 
    box-shadow: 0 4px 20px rgba(49, 140, 213, 0.3);
  }
  50% { 
    box-shadow: 0 4px 25px rgba(49, 140, 213, 0.4);
  }
}

.item-menu-help-info-active {
  animation: gentle-pulse 4s ease-in-out infinite;
}

/* ✨ EFEITO BRILHO NAS BORDAS QUANDO ATIVO */
.item-menu-help-info-active::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #ffffff, #e9ecef, #ffffff, #e9ecef);
  border-radius: 12px;
  z-index: -1;
  opacity: 0.3;
  animation: borderShine 3s linear infinite;
}

@keyframes borderShine {
  0% { 
    background-position: 0% 50%;
  }
  100% { 
    background-position: 200% 50%;
  }
}

/* 🎯 MELHORAR O CONTADOR DE NÚMEROS */
.number-box.number-box--active {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  color: #318CD5;
  font-size: 12px;
  font-weight: 700;
  border-radius: 12px;
  width: 100%;
  display: flex;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  margin-top: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.number-box.number-box--active:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

  /* 🎨 ESTADOS ESPECIAIS MELHORADOS */
  .number-box--loading {
    /*background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%) !important;*/
    animation: loading-pulse 1.5s ease-in-out infinite, hourglass-spin 2s linear infinite;
    color: #318CD5;
    font-size: 12px;
    /*box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);*/
  }

@keyframes loading-pulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.7;
    transform: scale(1.3);
  }
}

@keyframes hourglass-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



/* 🔄 TRANSIÇÕES SUAVES ENTRE ESTADOS */
.item-menu-help-info,
.item-menu-help-info-active {
  transform-origin: center;
}

.item-menu-help-info svg,
.item-menu-help-info-active svg {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 📱 RESPONSIVIDADE PARA MOBILE */
@media (max-width: 768px) {
  .item-menu-help-info,
  .item-menu-help-info-active {
    width: 58px;
    border-radius: 10px;
  }
  
  .number-box.number-box--active {
    font-size: 11px;
    border-radius: 10px;
  }
  
  /* Reduzir intensidade das animações no mobile para performance */
  #waving-animation .waving-arm {
    animation-duration: 2.5s;
  }
  
  .particle-1,
  .particle-2,
  .particle-3 {
    animation-duration: 4s;
  }
}