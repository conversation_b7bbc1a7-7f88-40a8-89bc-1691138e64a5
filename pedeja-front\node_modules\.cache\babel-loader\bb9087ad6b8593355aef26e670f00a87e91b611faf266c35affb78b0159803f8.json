{"ast": null, "code": "/**\n * Utilitários para detecção de dispositivos e navegadores\n */\n\n/**\n * Detecta se o dispositivo é iOS (iPhone, iPad, iPod)\n * @returns {boolean} true se for iOS, false caso contrário\n */\nexport const isIOS = () => {\n  // Verifica se estamos no navegador\n  if (typeof window === 'undefined' || !window.navigator) {\n    return false;\n  }\n  const userAgent = window.navigator.userAgent;\n  const platform = window.navigator.platform;\n\n  // Detecta iOS através do userAgent\n  const isIOSUserAgent = /iPad|iPhone|iPod/.test(userAgent);\n\n  // Detecta iOS através da platform (mais confiável)\n  const isIOSPlatform = /iPad|iPhone|iPod/.test(platform);\n\n  // Detecta iOS 13+ no iPad (que pode reportar como Mac)\n  const isIPadOS = platform === 'MacIntel' && window.navigator.maxTouchPoints > 1;\n  return isIOSUserAgent || isIOSPlatform || isIPadOS;\n};\n\n/**\n * Detecta se o navegador é Safari\n * @returns {boolean} true se for Safari, false caso contrário\n */\nexport const isSafari = () => {\n  if (typeof window === 'undefined' || !window.navigator) {\n    return false;\n  }\n  const userAgent = window.navigator.userAgent;\n\n  // Safari tem 'Safari' no userAgent mas não 'Chrome' nem 'Chromium'\n  return /Safari/.test(userAgent) && !/Chrome|Chromium/.test(userAgent);\n};\n\n/**\n * Detecta se é um dispositivo móvel iOS\n * @returns {boolean} true se for iPhone ou iPod, false caso contrário\n */\nexport const isIOSMobile = () => {\n  if (typeof window === 'undefined' || !window.navigator) {\n    return false;\n  }\n  const userAgent = window.navigator.userAgent;\n  return /iPhone|iPod/.test(userAgent);\n};\n\n/**\n * Detecta se é um tablet iOS (iPad)\n * @returns {boolean} true se for iPad, false caso contrário\n */\nexport const isIOSTablet = () => {\n  if (typeof window === 'undefined' || !window.navigator) {\n    return false;\n  }\n  const userAgent = window.navigator.userAgent;\n  const platform = window.navigator.platform;\n\n  // iPad tradicional\n  const isIPadUserAgent = /iPad/.test(userAgent);\n\n  // iPad com iOS 13+ (reporta como Mac)\n  const isIPadOS = platform === 'MacIntel' && window.navigator.maxTouchPoints > 1;\n  return isIPadUserAgent || isIPadOS;\n};\n\n/**\n * Detecta se o dispositivo suporta notificações push de forma confiável\n * @returns {boolean} true se suporta notificações de forma confiável, false caso contrário\n */\nexport const supportsReliableNotifications = () => {\n  // Verifica se a API de notificações está disponível\n  if (!('Notification' in window)) {\n    return false;\n  }\n\n  // iOS tem suporte limitado e problemático para notificações web\n  // Especialmente no Safari, as notificações podem não funcionar corretamente\n  if (isIOS()) {\n    return false;\n  }\n  return true;\n};\n\n/**\n * Detecta se é um dispositivo móvel (baseado na largura da tela)\n * @param {number} breakpoint - Largura máxima para considerar móvel (padrão: 768px)\n * @returns {boolean} true se for móvel, false caso contrário\n */\nexport const isMobileScreen = (breakpoint = 768) => {\n  if (typeof window === 'undefined') {\n    return false;\n  }\n  return window.innerWidth <= breakpoint;\n};\n\n/**\n * Detecta se é um dispositivo móvel (combinando detecção de tela e userAgent)\n * @returns {boolean} true se for móvel, false caso contrário\n */\nexport const isMobileDevice = () => {\n  if (typeof window === 'undefined' || !window.navigator) {\n    return false;\n  }\n  const userAgent = window.navigator.userAgent;\n\n  // Detecta através do userAgent\n  const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n\n  // Detecta através da largura da tela\n  const isMobileScreen = window.innerWidth <= 768;\n  return isMobileUserAgent || isMobileScreen;\n};\n\n/**\n * Obtém informações detalhadas sobre o dispositivo\n * @returns {object} Objeto com informações do dispositivo\n */\nexport const getDeviceInfo = () => {\n  return {\n    isIOS: isIOS(),\n    isSafari: isSafari(),\n    isIOSMobile: isIOSMobile(),\n    isIOSTablet: isIOSTablet(),\n    isMobileDevice: isMobileDevice(),\n    isMobileScreen: isMobileScreen(),\n    supportsReliableNotifications: supportsReliableNotifications(),\n    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : '',\n    platform: typeof window !== 'undefined' ? window.navigator.platform : '',\n    screenWidth: typeof window !== 'undefined' ? window.innerWidth : 0,\n    screenHeight: typeof window !== 'undefined' ? window.innerHeight : 0\n  };\n};", "map": {"version": 3, "names": ["isIOS", "window", "navigator", "userAgent", "platform", "isIOSUserAgent", "test", "isIOSPlatform", "isIPadOS", "maxTouchPoints", "<PERSON><PERSON><PERSON><PERSON>", "isIOSMobile", "isIOSTablet", "isIPadUserAgent", "supportsReliableNotifications", "isMobileScreen", "breakpoint", "innerWidth", "isMobileDevice", "isMobileUserAgent", "getDeviceInfo", "screenWidth", "screenHeight", "innerHeight"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/utils/deviceDetection.js"], "sourcesContent": ["/**\n * Utilitários para detecção de dispositivos e navegadores\n */\n\n/**\n * Detecta se o dispositivo é iOS (iPhone, iPad, iPod)\n * @returns {boolean} true se for iOS, false caso contrário\n */\nexport const isIOS = () => {\n  // Verifica se estamos no navegador\n  if (typeof window === 'undefined' || !window.navigator) {\n    return false;\n  }\n\n  const userAgent = window.navigator.userAgent;\n  const platform = window.navigator.platform;\n\n  // Detecta iOS através do userAgent\n  const isIOSUserAgent = /iPad|iPhone|iPod/.test(userAgent);\n  \n  // Detecta iOS através da platform (mais confiável)\n  const isIOSPlatform = /iPad|iPhone|iPod/.test(platform);\n  \n  // Detecta iOS 13+ no iPad (que pode reportar como Mac)\n  const isIPadOS = platform === 'MacIntel' && window.navigator.maxTouchPoints > 1;\n  \n  return isIOSUserAgent || isIOSPlatform || isIPadOS;\n};\n\n/**\n * Detecta se o navegador é Safari\n * @returns {boolean} true se for Safari, false caso contrário\n */\nexport const isSafari = () => {\n  if (typeof window === 'undefined' || !window.navigator) {\n    return false;\n  }\n\n  const userAgent = window.navigator.userAgent;\n  \n  // Safari tem 'Safari' no userAgent mas não 'Chrome' nem 'Chromium'\n  return /Safari/.test(userAgent) && !/Chrome|Chromium/.test(userAgent);\n};\n\n/**\n * Detecta se é um dispositivo móvel iOS\n * @returns {boolean} true se for iPhone ou iPod, false caso contrário\n */\nexport const isIOSMobile = () => {\n  if (typeof window === 'undefined' || !window.navigator) {\n    return false;\n  }\n\n  const userAgent = window.navigator.userAgent;\n  return /iPhone|iPod/.test(userAgent);\n};\n\n/**\n * Detecta se é um tablet iOS (iPad)\n * @returns {boolean} true se for iPad, false caso contrário\n */\nexport const isIOSTablet = () => {\n  if (typeof window === 'undefined' || !window.navigator) {\n    return false;\n  }\n\n  const userAgent = window.navigator.userAgent;\n  const platform = window.navigator.platform;\n  \n  // iPad tradicional\n  const isIPadUserAgent = /iPad/.test(userAgent);\n  \n  // iPad com iOS 13+ (reporta como Mac)\n  const isIPadOS = platform === 'MacIntel' && window.navigator.maxTouchPoints > 1;\n  \n  return isIPadUserAgent || isIPadOS;\n};\n\n/**\n * Detecta se o dispositivo suporta notificações push de forma confiável\n * @returns {boolean} true se suporta notificações de forma confiável, false caso contrário\n */\nexport const supportsReliableNotifications = () => {\n  // Verifica se a API de notificações está disponível\n  if (!('Notification' in window)) {\n    return false;\n  }\n\n  // iOS tem suporte limitado e problemático para notificações web\n  // Especialmente no Safari, as notificações podem não funcionar corretamente\n  if (isIOS()) {\n    return false;\n  }\n\n  return true;\n};\n\n/**\n * Detecta se é um dispositivo móvel (baseado na largura da tela)\n * @param {number} breakpoint - Largura máxima para considerar móvel (padrão: 768px)\n * @returns {boolean} true se for móvel, false caso contrário\n */\nexport const isMobileScreen = (breakpoint = 768) => {\n  if (typeof window === 'undefined') {\n    return false;\n  }\n\n  return window.innerWidth <= breakpoint;\n};\n\n/**\n * Detecta se é um dispositivo móvel (combinando detecção de tela e userAgent)\n * @returns {boolean} true se for móvel, false caso contrário\n */\nexport const isMobileDevice = () => {\n  if (typeof window === 'undefined' || !window.navigator) {\n    return false;\n  }\n\n  const userAgent = window.navigator.userAgent;\n  \n  // Detecta através do userAgent\n  const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n  \n  // Detecta através da largura da tela\n  const isMobileScreen = window.innerWidth <= 768;\n  \n  return isMobileUserAgent || isMobileScreen;\n};\n\n/**\n * Obtém informações detalhadas sobre o dispositivo\n * @returns {object} Objeto com informações do dispositivo\n */\nexport const getDeviceInfo = () => {\n  return {\n    isIOS: isIOS(),\n    isSafari: isSafari(),\n    isIOSMobile: isIOSMobile(),\n    isIOSTablet: isIOSTablet(),\n    isMobileDevice: isMobileDevice(),\n    isMobileScreen: isMobileScreen(),\n    supportsReliableNotifications: supportsReliableNotifications(),\n    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : '',\n    platform: typeof window !== 'undefined' ? window.navigator.platform : '',\n    screenWidth: typeof window !== 'undefined' ? window.innerWidth : 0,\n    screenHeight: typeof window !== 'undefined' ? window.innerHeight : 0,\n  };\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMA,KAAK,GAAGA,CAAA,KAAM;EACzB;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,SAAS,EAAE;IACtD,OAAO,KAAK;EACd;EAEA,MAAMC,SAAS,GAAGF,MAAM,CAACC,SAAS,CAACC,SAAS;EAC5C,MAAMC,QAAQ,GAAGH,MAAM,CAACC,SAAS,CAACE,QAAQ;;EAE1C;EACA,MAAMC,cAAc,GAAG,kBAAkB,CAACC,IAAI,CAACH,SAAS,CAAC;;EAEzD;EACA,MAAMI,aAAa,GAAG,kBAAkB,CAACD,IAAI,CAACF,QAAQ,CAAC;;EAEvD;EACA,MAAMI,QAAQ,GAAGJ,QAAQ,KAAK,UAAU,IAAIH,MAAM,CAACC,SAAS,CAACO,cAAc,GAAG,CAAC;EAE/E,OAAOJ,cAAc,IAAIE,aAAa,IAAIC,QAAQ;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAME,QAAQ,GAAGA,CAAA,KAAM;EAC5B,IAAI,OAAOT,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,SAAS,EAAE;IACtD,OAAO,KAAK;EACd;EAEA,MAAMC,SAAS,GAAGF,MAAM,CAACC,SAAS,CAACC,SAAS;;EAE5C;EACA,OAAO,QAAQ,CAACG,IAAI,CAACH,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAACG,IAAI,CAACH,SAAS,CAAC;AACvE,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMQ,WAAW,GAAGA,CAAA,KAAM;EAC/B,IAAI,OAAOV,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,SAAS,EAAE;IACtD,OAAO,KAAK;EACd;EAEA,MAAMC,SAAS,GAAGF,MAAM,CAACC,SAAS,CAACC,SAAS;EAC5C,OAAO,aAAa,CAACG,IAAI,CAACH,SAAS,CAAC;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMS,WAAW,GAAGA,CAAA,KAAM;EAC/B,IAAI,OAAOX,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,SAAS,EAAE;IACtD,OAAO,KAAK;EACd;EAEA,MAAMC,SAAS,GAAGF,MAAM,CAACC,SAAS,CAACC,SAAS;EAC5C,MAAMC,QAAQ,GAAGH,MAAM,CAACC,SAAS,CAACE,QAAQ;;EAE1C;EACA,MAAMS,eAAe,GAAG,MAAM,CAACP,IAAI,CAACH,SAAS,CAAC;;EAE9C;EACA,MAAMK,QAAQ,GAAGJ,QAAQ,KAAK,UAAU,IAAIH,MAAM,CAACC,SAAS,CAACO,cAAc,GAAG,CAAC;EAE/E,OAAOI,eAAe,IAAIL,QAAQ;AACpC,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMM,6BAA6B,GAAGA,CAAA,KAAM;EACjD;EACA,IAAI,EAAE,cAAc,IAAIb,MAAM,CAAC,EAAE;IAC/B,OAAO,KAAK;EACd;;EAEA;EACA;EACA,IAAID,KAAK,CAAC,CAAC,EAAE;IACX,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMe,cAAc,GAAGA,CAACC,UAAU,GAAG,GAAG,KAAK;EAClD,IAAI,OAAOf,MAAM,KAAK,WAAW,EAAE;IACjC,OAAO,KAAK;EACd;EAEA,OAAOA,MAAM,CAACgB,UAAU,IAAID,UAAU;AACxC,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAME,cAAc,GAAGA,CAAA,KAAM;EAClC,IAAI,OAAOjB,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,SAAS,EAAE;IACtD,OAAO,KAAK;EACd;EAEA,MAAMC,SAAS,GAAGF,MAAM,CAACC,SAAS,CAACC,SAAS;;EAE5C;EACA,MAAMgB,iBAAiB,GAAG,gEAAgE,CAACb,IAAI,CAACH,SAAS,CAAC;;EAE1G;EACA,MAAMY,cAAc,GAAGd,MAAM,CAACgB,UAAU,IAAI,GAAG;EAE/C,OAAOE,iBAAiB,IAAIJ,cAAc;AAC5C,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMK,aAAa,GAAGA,CAAA,KAAM;EACjC,OAAO;IACLpB,KAAK,EAAEA,KAAK,CAAC,CAAC;IACdU,QAAQ,EAAEA,QAAQ,CAAC,CAAC;IACpBC,WAAW,EAAEA,WAAW,CAAC,CAAC;IAC1BC,WAAW,EAAEA,WAAW,CAAC,CAAC;IAC1BM,cAAc,EAAEA,cAAc,CAAC,CAAC;IAChCH,cAAc,EAAEA,cAAc,CAAC,CAAC;IAChCD,6BAA6B,EAAEA,6BAA6B,CAAC,CAAC;IAC9DX,SAAS,EAAE,OAAOF,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACC,SAAS,CAACC,SAAS,GAAG,EAAE;IAC1EC,QAAQ,EAAE,OAAOH,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACC,SAAS,CAACE,QAAQ,GAAG,EAAE;IACxEiB,WAAW,EAAE,OAAOpB,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACgB,UAAU,GAAG,CAAC;IAClEK,YAAY,EAAE,OAAOrB,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACsB,WAAW,GAAG;EACrE,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}