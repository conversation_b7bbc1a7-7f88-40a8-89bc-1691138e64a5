import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  CognitoIdentityClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CognitoIdentityClient";
import {
  GetCredentialsForIdentityInput,
  GetCredentialsForIdentityResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface GetCredentialsForIdentityCommandInput
  extends GetCredentialsForIdentityInput {}
export interface GetCredentialsForIdentityCommandOutput
  extends GetCredentialsForIdentityResponse,
    __MetadataBearer {}
declare const GetCredentialsForIdentityCommand_base: {
  new (
    input: GetCredentialsForIdentityCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetCredentialsForIdentityCommandInput,
    GetCredentialsForIdentityCommandOutput,
    CognitoIdentityClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: GetCredentialsForIdentityCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetCredentialsForIdentityCommandInput,
    GetCredentialsForIdentityCommandOutput,
    CognitoIdentityClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetCredentialsForIdentityCommand extends GetCredentialsForIdentityCommand_base {}
