const jwt = require('jsonwebtoken')
require('dotenv').config()

const checkToken = async (req, res, next) => {

	if (!(/\/api\/v1/gm.test(req.path))) {
	 return next()
	}

	if (/(.*)\/docs/gm.test(req.path)) {
	 return next();
	}

	if (/(.*)\/webhook/gm.test(req.path)) {
		return next();
	}

	if (/(.*)\/bot-llm/gm.test(req.path)) {
		return next();
	}

	if (/(.*)\/wp-jobs/gm.test(req.path)) {
		return next();
	}

	if (req.path === "/api/v1/auth/login") {
		return next();
	}


	const authHeader = req.headers['authorization']
	const token = authHeader && authHeader.split(" ")[1]

	if (!token) {
		return res.status(401).json({ msg: `<PERSON>sso negado! ${req.path}` })
	}

	try {
		const secret = process.env.SECRET
		jwt.verify(token, secret)
		const currentUser = jwt.decode(token)
		//console.log('currentUser', currentUser);
		req.context = {
			currentUser,
		};
		next()
	} catch (error) {
		return res.status(400).json({ msg: "Token inválido!" })
	}
}

module.exports = checkToken