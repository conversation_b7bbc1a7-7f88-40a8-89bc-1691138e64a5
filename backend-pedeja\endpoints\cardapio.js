const express = require("express");
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configuração do multer para upload de arquivos PDF
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadPath = path.join(__dirname, '../uploads/cardapios');
        
        // Criar diretório se não existir
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }
        
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        // Gerar nome único para o arquivo
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const empresaId = req.body.idEmpresa || 'empresa';
        cb(null, `cardapio-${empresaId}-${uniqueSuffix}.pdf`);
    }
});

const fileFilter = (req, file, cb) => {
    // Aceitar apenas arquivos PDF
    if (file.mimetype === 'application/pdf') {
        cb(null, true);
    } else {
        cb(new Error('Apenas arquivos PDF são permitidos!'), false);
    }
};

const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024 // Limite de 10MB
    }
});

// Modelo para armazenar dados de cardápio enviados pela equipe
// TODO: Implementar com MongoDB/database real
let cardapiosPendentes = [];

/**
 * @route POST /api/cardapio/enviar-para-equipe
 * @desc Receber cardápio do cliente para que a equipe processe
 * @access Private
 */
router.post('/enviar-para-equipe', upload.single('pdfFile'), async (req, res) => {
    try {
        const { empresaId, idEmpresa, userId, customLink, observacoes } = req.body;
        
        // Validar dados obrigatórios
        if (!empresaId || !idEmpresa || !userId) {
            return res.status(400).json({
                success: false,
                message: 'Dados da empresa são obrigatórios'
            });
        }

        // Verificar se pelo menos um tipo de cardápio foi fornecido
        if (!customLink && !req.file) {
            return res.status(400).json({
                success: false,
                message: 'É necessário fornecer um link ou enviar um arquivo PDF'
            });
        }

        // Dados do cardápio a ser processado
        const cardapioData = {
            id: Date.now().toString(), // ID temporário
            empresaId,
            idEmpresa,
            userId,
            customLink: customLink || null,
            observacoes: observacoes || null,
            pdfFile: req.file ? {
                filename: req.file.filename,
                originalName: req.file.originalname,
                path: req.file.path,
                size: req.file.size
            } : null,
            status: 'pendente', // pendente, processando, concluido, erro
            dataCriacao: new Date(),
            dataAtualizacao: new Date(),
            processadoPor: null,
            observacoesEquipe: null
        };

        // Salvar na lista temporária (em produção, salvar no banco de dados)
        cardapiosPendentes.push(cardapioData);

        console.log('📋 Novo cardápio recebido para processamento:', {
            empresaId,
            idEmpresa,
            temLink: !!customLink,
            temPDF: !!req.file,
            observacoes: observacoes || 'Nenhuma'
        });

        // TODO: Implementar integração com sistema de notificação da equipe
        // - Enviar email para equipe de suporte
        // - Criar notificação no sistema administrativo
        // - Integrar com sistema de tickets (se houver)

        res.status(200).json({
            success: true,
            message: 'Cardápio recebido com sucesso! Nossa equipe entrará em contato em até 24 horas.',
            data: {
                id: cardapioData.id,
                status: cardapioData.status,
                dataCriacao: cardapioData.dataCriacao
            }
        });

    } catch (error) {
        console.error('Erro ao processar cardápio da equipe:', error);
        
        // Se houve erro e um arquivo foi enviado, remover o arquivo
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(500).json({
            success: false,
            message: 'Erro interno do servidor ao processar cardápio'
        });
    }
});

/**
 * @route GET /api/cardapio/pendentes
 * @desc Listar cardápios pendentes para processamento pela equipe
 * @access Admin
 */
router.get('/pendentes', async (req, res) => {
    try {
        // TODO: Implementar autenticação de admin
        
        // Filtrar apenas cardápios pendentes
        const pendentes = cardapiosPendentes.filter(c => c.status === 'pendente');
        
        // Retornar dados sem informações sensíveis do arquivo
        const cardapiosFormatados = pendentes.map(cardapio => ({
            id: cardapio.id,
            empresaId: cardapio.empresaId,
            idEmpresa: cardapio.idEmpresa,
            userId: cardapio.userId,
            customLink: cardapio.customLink,
            observacoes: cardapio.observacoes,
            temPDF: !!cardapio.pdfFile,
            nomeArquivoPDF: cardapio.pdfFile?.originalName || null,
            status: cardapio.status,
            dataCriacao: cardapio.dataCriacao,
            dataAtualizacao: cardapio.dataAtualizacao
        }));

        res.status(200).json({
            success: true,
            data: cardapiosFormatados,
            total: cardapiosFormatados.length
        });

    } catch (error) {
        console.error('Erro ao listar cardápios pendentes:', error);
        res.status(500).json({
            success: false,
            message: 'Erro interno do servidor'
        });
    }
});

/**
 * @route PATCH /api/cardapio/:id/status
 * @desc Atualizar status de processamento do cardápio
 * @access Admin
 */
router.patch('/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const { status, observacoesEquipe, processadoPor } = req.body;

        // Validar status
        const statusValidos = ['pendente', 'processando', 'concluido', 'erro'];
        if (!statusValidos.includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Status inválido'
            });
        }

        // Encontrar cardápio
        const cardapioIndex = cardapiosPendentes.findIndex(c => c.id === id);
        if (cardapioIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Cardápio não encontrado'
            });
        }

        // Atualizar dados
        cardapiosPendentes[cardapioIndex] = {
            ...cardapiosPendentes[cardapioIndex],
            status,
            observacoesEquipe: observacoesEquipe || cardapiosPendentes[cardapioIndex].observacoesEquipe,
            processadoPor: processadoPor || cardapiosPendentes[cardapioIndex].processadoPor,
            dataAtualizacao: new Date()
        };

        console.log(`📋 Status do cardápio ${id} atualizado para: ${status}`);

        res.status(200).json({
            success: true,
            message: 'Status atualizado com sucesso',
            data: {
                id,
                status,
                dataAtualizacao: cardapiosPendentes[cardapioIndex].dataAtualizacao
            }
        });

    } catch (error) {
        console.error('Erro ao atualizar status do cardápio:', error);
        res.status(500).json({
            success: false,
            message: 'Erro interno do servidor'
        });
    }
});

/**
 * @route GET /api/cardapio/:id/download
 * @desc Download do arquivo PDF do cardápio
 * @access Admin
 */
router.get('/:id/download', async (req, res) => {
    try {
        const { id } = req.params;

        // Encontrar cardápio
        const cardapio = cardapiosPendentes.find(c => c.id === id);
        if (!cardapio) {
            return res.status(404).json({
                success: false,
                message: 'Cardápio não encontrado'
            });
        }

        // Verificar se tem arquivo PDF
        if (!cardapio.pdfFile) {
            return res.status(404).json({
                success: false,
                message: 'Arquivo PDF não encontrado para este cardápio'
            });
        }

        // Verificar se arquivo existe no sistema
        if (!fs.existsSync(cardapio.pdfFile.path)) {
            return res.status(404).json({
                success: false,
                message: 'Arquivo não encontrado no servidor'
            });
        }

        // Enviar arquivo para download
        res.download(cardapio.pdfFile.path, cardapio.pdfFile.originalName, (err) => {
            if (err) {
                console.error('Erro ao fazer download do arquivo:', err);
                res.status(500).json({
                    success: false,
                    message: 'Erro ao baixar arquivo'
                });
            }
        });

    } catch (error) {
        console.error('Erro ao processar download:', error);
        res.status(500).json({
            success: false,
            message: 'Erro interno do servidor'
        });
    }
});

module.exports = router; 