class CreateMesasUseCase {
    constructor(mesasDAO, empresaDAO){
        this.mesasDAO = mesasDAO
        this.empresaDAO = empresaDAO
    }

    async execute({ qr_code, vinculo_empresa, name }) {
        try {
            let status = 'free';
            let number = 1;

            const empresa = await this.empresaDAO.getEmpresaById(vinculo_empresa);

            const lastMesa = await this.mesasDAO.findLastNumber({ empresa_id: empresa._id });
            if(lastMesa){
                number = Number(lastMesa.number) + 1;
            }
            const mesas = await this.mesasDAO.createMesas({ qr_code, empresa_id: empresa._id, name, status, number, total: 0, total_payed: 0 });
            return mesas;
        } catch(error){
            console.error(error);
            return {
                status: false,
                message: "Error on get mesas"
            }
        }
    }
}

module.exports = CreateMesasUseCase;