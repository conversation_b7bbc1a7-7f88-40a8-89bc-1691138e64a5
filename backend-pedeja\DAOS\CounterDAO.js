class CounterDAO {
  constructor(counterModel) {
    this.model = counterModel;
  }

  async findOneAndUpdate(id_empresa) {
    try {
      const counterData = await this.model.findOneAndUpdate(
        { id: `empresa:${id_empresa};id_pedido` },
        { $inc: { seq: 1 } },
        { new: true }
      );
      return counterData;
    } catch (error) {
      console.log("error", error);
      return {
        status: false,
        message: "Erro ao atualizar contador de pedidos",
      };
    }
  }

  async createCounter(id_empresa) {
    try {
      const newVal = new this.model({
        id: `empresa:${id_empresa};id_pedido`,
        seq: 1,
      });
      await newVal.save();
    } catch (error) {
      console.log("error", error);
      return {
        status: false,
        message: "Erro ao criar contador de pedidos",
      };
    }
  }

  async findLastUserCounter() {
    try {
      const counterData = await this.model.findOneAndUpdate(
        { id: "id_user" },
        { $inc: { seq: 1 } },
        { new: true }
      );
      return counterData;
    } catch (error) {
      console.log("error", error);
      throw new Error("It was not possible to get last user counter.");
    }
  }

  async createCounterUser() {
    try {
      const newVal = this.model({ id: "id_user", seq: 1 });
      newVal.save();
    } catch (error) {
      console.log("error", error);
      throw new Error("It was not possible to get last user counter.");
    }
  }
}

module.exports = CounterDAO;
