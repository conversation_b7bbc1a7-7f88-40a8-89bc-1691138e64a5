import { useState, useEffect } from 'react';
import { 
  isIOS, 
  isSafari, 
  isIOSMobile, 
  isIOSTablet, 
  isMobileDevice, 
  isMobileScreen, 
  supportsReliableNotifications,
  getDeviceInfo 
} from '../utils/deviceDetection';

/**
 * Hook personalizado para detecção de dispositivos
 * Fornece informações sobre o dispositivo e navegador do usuário
 * 
 * @param {number} mobileBreakpoint - Largura máxima para considerar móvel (padrão: 768px)
 * @returns {object} Objeto com informações do dispositivo e funções utilitárias
 */
export const useDeviceDetection = (mobileBreakpoint = 768) => {
  const [deviceInfo, setDeviceInfo] = useState(() => getDeviceInfo());
  const [screenWidth, setScreenWidth] = useState(() => 
    typeof window !== 'undefined' ? window.innerWidth : 0
  );

  // Atualiza informações quando a tela é redimensionada
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const newWidth = window.innerWidth;
      setScreenWidth(newWidth);
      
      // Atualiza informações do dispositivo
      setDeviceInfo(getDeviceInfo());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    // Informações básicas do dispositivo
    isIOS: deviceInfo.isIOS,
    isSafari: deviceInfo.isSafari,
    isIOSMobile: deviceInfo.isIOSMobile,
    isIOSTablet: deviceInfo.isIOSTablet,
    isMobileDevice: deviceInfo.isMobileDevice,
    
    // Informações da tela
    screenWidth,
    screenHeight: deviceInfo.screenHeight,
    isMobileScreen: screenWidth <= mobileBreakpoint,
    
    // Suporte a funcionalidades
    supportsReliableNotifications: deviceInfo.supportsReliableNotifications,
    
    // Informações técnicas
    userAgent: deviceInfo.userAgent,
    platform: deviceInfo.platform,
    
    // Objeto completo com todas as informações
    deviceInfo,
    
    // Funções utilitárias
    shouldShowNotifications: () => supportsReliableNotifications(),
    shouldUseAlternativeNotification: () => !supportsReliableNotifications(),
    getDeviceType: () => {
      if (deviceInfo.isIOSMobile) return 'ios-mobile';
      if (deviceInfo.isIOSTablet) return 'ios-tablet';
      if (deviceInfo.isMobileDevice) return 'mobile';
      return 'desktop';
    }
  };
};

/**
 * Hook simplificado para detecção de dispositivos móveis
 * @param {number} breakpoint - Largura máxima para considerar móvel (padrão: 768px)
 * @returns {boolean} true se for dispositivo móvel
 */
export const useIsMobile = (breakpoint = 768) => {
  const [isMobile, setIsMobile] = useState(() => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth <= breakpoint;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setIsMobile(window.innerWidth <= breakpoint);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [breakpoint]);

  return isMobile;
};

/**
 * Hook para verificar se deve mostrar notificações
 * @returns {object} Objeto com informações sobre notificações
 */
export const useNotificationSupport = () => {
  const [notificationPermission, setNotificationPermission] = useState(() => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return 'unsupported';
    }
    return Notification.permission;
  });

  const deviceInfo = useDeviceDetection();

  useEffect(() => {
    if (typeof window === 'undefined' || !('Notification' in window)) return;

    // Monitora mudanças na permissão de notificação
    const checkPermission = () => {
      setNotificationPermission(Notification.permission);
    };

    // Verifica periodicamente (algumas vezes a permissão pode mudar)
    const interval = setInterval(checkPermission, 1000);
    
    return () => clearInterval(interval);
  }, []);

  const requestNotificationPermission = async () => {
    if (!deviceInfo.supportsReliableNotifications) {
      console.log('📱 Notificações não são suportadas de forma confiável neste dispositivo');
      return 'denied';
    }

    if (typeof window === 'undefined' || !('Notification' in window)) {
      return 'unsupported';
    }

    try {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission);
      return permission;
    } catch (error) {
      console.error('Erro ao solicitar permissão para notificações:', error);
      return 'denied';
    }
  };

  return {
    // Estado atual
    permission: notificationPermission,
    isSupported: deviceInfo.supportsReliableNotifications,
    canRequest: deviceInfo.supportsReliableNotifications && notificationPermission === 'default',
    canShow: deviceInfo.supportsReliableNotifications && notificationPermission === 'granted',
    
    // Informações do dispositivo
    isIOS: deviceInfo.isIOS,
    deviceType: deviceInfo.getDeviceType(),
    
    // Funções
    requestPermission: requestNotificationPermission,
    
    // Mensagens explicativas
    getStatusMessage: () => {
      if (!deviceInfo.supportsReliableNotifications) {
        if (deviceInfo.isIOS) {
          return 'Notificações web não são suportadas de forma confiável em dispositivos iOS';
        }
        return 'Notificações não são suportadas neste navegador';
      }
      
      switch (notificationPermission) {
        case 'granted':
          return 'Notificações permitidas';
        case 'denied':
          return 'Notificações bloqueadas pelo usuário';
        case 'default':
          return 'Permissão para notificações não solicitada';
        default:
          return 'Status de notificação desconhecido';
      }
    }
  };
};
