class PedidosDAO {
    constructor(pedidoModel){
        this.model = pedidoModel;
    }


    async createPedido({ seqId, createdBy, id_empresa,  status_pedido, garcomName, itens, celular_cliente, nome_cliente, valor_total, descricao,  counterPedido, status_print, createdAt, entrega, counter_qtd_pedido, id_pedido_counter  }){
        try {
            const pedido = new this.model({
                id_pedido: seqId,
                createdBy,
                id_empresa,
                status_pedido,
                garcom_name: garcomName,
                itens,
                celular_cliente,
                nome_cliente,
                valor_total,
                descricao,
                counter_qtd_pedido: counterPedido.counter_qtd_pedido,
                status_print,
                createdAt,
                entrega,
                counter_qtd_pedido, 
                id_pedido_counter
            });
        
            // Save the new order (pedido)
            await pedido.save();

            return pedido;
        } catch(error){
            console.log('error', error);
            return {
                status: false,
                message: "<PERSON>rro ao criar contador de pedidos"
            }
        }
    }

    async findPedidoById({ pedido_id}){
        try {
            const pedido = await this.model.findOne({ _id: pedido_id });
            return pedido;
        } catch(error){
            console.log('error', error);
        }
    }

}

module.exports = PedidosDAO;