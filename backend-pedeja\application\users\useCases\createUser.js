const bcrypt = require("bcrypt");
const moment = require("moment");
const Whatsapp = require("../../../lib/whatsapp");

class CreateUserUseCase {
  constructor(userDAO, companyDAO, counterDAO) {
    this.userDAO = userDAO;
    this.companyDAO = companyDAO;
    this.counterDAO = counterDAO;
  }

  async execute({ email, name, password, empresaId, role, createdBy, number }) {
    try {
      const userExists = await this.userDAO.getUserByEmail(email);

      if (userExists) {
        return {
          status: false,
          message: "Usuário já existe, utilize outro e-mail!",
        };
      }

      const company = await this.companyDAO.getEmpresaById(empresaId);

      if (!company) {
        return {
          status: false,
          message: "Empresa não encontrada!",
        };
      }
      //Criar senha
      const salt = await bcrypt.genSalt(12);
      const passwordHash = await bcrypt.hash(password, salt);
      //Verificar SeqID e atribuir ao id_user

      const lastCounterUser = await this.counterDAO.findLastUserCounter();
      let seqId;
      if (lastCounterUser == null) {
        await this.counterDAO.createCounterUser();
        seqId = 1;
      } else {
        seqId = lastCounterUser.seq;
      }

      var data = moment().format();

      const user = {
        id_user: seqId,
        name,
        email,
        password: passwordHash,
        vinculo_empresa: company.cnpj,
        role,
        createdAt: data,
        inativo: false,
        bloqueado: false,
        createdBy,
      };

      const newUser = await this.userDAO.createUser(user);

      if(!newUser) {
        return {
          status: false,
          message: "Não foi possível criar o usuário.",
        };
      }

      if("whatsapp" in company && (!company.whatsapp.endpoint || !company.whatsapp.token)){
        return res.status(404).json({status: 400, error: 'whatsapp não encontrado!', empresaID: company._id})
      }

      let appLink = 'https://garcom-pedeja.vercel.app/'

      let message = `Olá ${name}, seja bem-vindo ao ${company.name}! Para acesso o aplicativo de garcom, utilize a plataforma: ${appLink}  \n\n Seu login é: ${email} \n Sua senha é: ${password} \n\n Atenciosamente, equipe ${company.name}`;
      
      const whatsappInstance = new Whatsapp(company.whatsapp.endpoint, company.whatsapp.token);

      let formatedNumber = number.replace(/\D/g, '');

      try {
        await whatsappInstance.SendSimpleTxtMessage(formatedNumber, message)
      } catch (error) {
        console.log('error ao enviar mensagem de boas vindas:', error)
      }
  

      return {
        status: true,
        data: newUser,
      };
    } catch (error) {
      console.log(error);
      return {
        status: false,
        message: "Não foi possivel criar seu usuário.",
      };
    }
  }
}

module.exports = CreateUserUseCase;
