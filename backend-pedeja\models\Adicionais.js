const mongoose = require('mongoose')
const { image } = require('pdfkit')

const AdicionaisSchema = new mongoose.Schema({
empresaObjId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Empresa',
    required: true,
},
id_grupo_adicional: Number,
id_empresa: Number,
createdBy: String,

item_id: String,
item_title: String,
title: String,
min: Number,
max: Number,
mandatory: Boolean,
out: Boolean,
adicionais: [{
    id_adicional: String,
    title: String,
    price: Number,
    price_salao: Number,
    out: Boolean,
    out_salao: Boolean,
    image: String,
    createdAt: Date,
    updatedAt: Date,
    deletedAt: Date
}],

image: String, //url da imagem
calcular_media: Boolean,
calcular_maior_valor: Boolean,
precificacao: String,
type: String,
//order: Number,
//disponibilidade: String,
//dia_horario_disponibilidade: Array,

//import_category_id: Number,

inativo:Boolean,
bloqueado:Boolean,

createdAt: Date,
updatedAt: Date,
deletedAt: Date

})

const Adicionais = mongoose.model('Adicionais', AdicionaisSchema)

module.exports = Adicionais