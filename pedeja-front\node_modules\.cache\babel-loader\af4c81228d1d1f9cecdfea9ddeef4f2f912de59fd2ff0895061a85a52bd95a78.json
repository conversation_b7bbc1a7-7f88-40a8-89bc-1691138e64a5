{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\components\\\\LeftMenu\\\\index.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useContext, useRef, useEffect, useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport styled from \"styled-components\";\nimport * as HiIcons from \"react-icons/hi\";\nimport { SidebarData } from \"./SidebarData\";\nimport SubMenu from \"./SubMenu\";\nimport { IconContext } from \"react-icons/lib\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport \"./style.css\";\nimport userDiv from \"../../img/userDiv2.png\";\nimport defaultUserImg from \"../../img/defaultUserImg.png\";\nimport LogoP from \"../../img/logoP.png\";\nimport logoImg from \"../../img/logoBlue.png\";\nimport audioNotify from \"../../assets/audio/soundNotify.mp3\";\nimport { MdStorefront } from \"react-icons/md\";\nimport { RiArrowDropDownLine } from \"react-icons/ri\";\nimport { FaRobot, FaCheck, FaArrowRight } from \"react-icons/fa\";\nimport CryptoJS from \"crypto-js\";\nimport io from \"socket.io-client\";\nimport { ImPrinter } from \"react-icons/im\";\nimport ModalUserImg from \"../ModalAddUserImg\";\nimport ModalEditUser from \"../../components/ModalEditUser\";\nimport ModalLinkCardapio from \"../ModalLinkCardapio\";\nimport ModalLinkCardapioSalao from \"../ModalLinkCardapioSalao\";\nimport { RiMenuFoldFill } from \"react-icons/ri\";\nimport { RiMenuUnfoldFill } from \"react-icons/ri\";\nimport { GiHamburgerMenu } from \"react-icons/gi\";\nimport { MdExitToApp } from \"react-icons/md\";\nimport roundTable from \"../../img/round-table.png\";\nimport entregadorIco from \"../../img/entregador.png\";\nimport { v4 as uuidv4 } from \"uuid\"; // Biblioteca para gerar IDs únicos\nimport { SidebarContext } from \"../../AppRoutes\";\nimport { AuthContext } from \"../../contexts/auth\";\nimport { getUser, getVinculoEmpresa, updateStatusBotEmpresa, changeStatusLoja, getDaysToExpireLicense, getEmpresaWithObjId } from \"../../services/api\";\nimport { Tooltip, Drawer } from \"antd\";\nimport AtendimentoModal from \"./AtendimentoModal\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Nav = styled.div`\n  background: white;\n  --background: linear-gradient(to left, #4281ff, #51d2ff);\n  left: ${({\n  sidebar\n}) => sidebar ? \"250px\" : \"100px\"};\n  transition: 150ms;\n  height: 80px;\n  width: ${({\n  sidebar\n}) => sidebar ? \"calc(100% - 250px)\" : \"calc(100% - 100px)\"};\n  display: flex;\n  --justify-content: flex-start;\n  align-items: center;\n  --border-bottom: 2px solid #0000001c;\n  position: relative;\n  z-index: 10;\n  box-shadow: 1px 1px 6px rgb(180, 180, 180);\n\n  @media (max-width: 880px){\n    left: 0;\n    width: 100%;\n  }\n`;\n\n//const NavIcon = styled(Link)`\n_c = Nav;\nconst NavIcon = styled.div`\n  justify-content: center;\n  font-size: 13px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  color: white;\n  text-decoration: unset;\n  transition: font-size 150ms;\n\n`;\n_c2 = NavIcon;\nconst SidebarNav = styled.nav`\n  background: white;\n  width: ${({\n  sidebar\n}) => sidebar ? \"250px\" : \"100px\"};\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  position: fixed;\n  top: 0;\n  left: 0;\n  transition: width 150ms cubic-bezier(0.4, 0, 0.2, 1); // Ajustado para uma curva bezier comum para movimento\n  z-index: 10;\n  border-right: solid 1px rgb(240, 240, 240);\n\n  span {\n    transition-delay: 200ms;\n    transition-property: font-size, visibility, opacity; // Adicionando propriedades específicas\n    visibility: ${({\n  sidebar\n}) => sidebar ? \"visible\" : \"hidden\"};\n    opacity: ${({\n  sidebar\n}) => sidebar ? \"1\" : \"0\"};\n    font-size: ${({\n  sidebar\n}) => sidebar ? \"14px\" : \"0px\"};\n    transition: visibility 0s, opacity 0.5s,\n      ${({\n  sidebar\n}) => sidebar ? \"font-size 250ms linear\" : \"font-size 100ms linear\"};\n  }\n\n  @media (max-width: 880px) {\n    display: none;\n  }\n`;\n_c3 = SidebarNav;\nconst ModalUserOptions = styled.div`\n  font-size: 14px;\n  position: absolute;\n  top: 82px;\n  left: 150px;\n  display: ${({\n  showOptions\n}) => showOptions ? \"none\" : \"\"};\n  float: left;\n  min-width: 160px;\n  margin: 2px 0 0;\n  padding: 5px 0;\n  list-style: none;\n  text-align: left;\n  border: 1px solid #ccc;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 4px;\n  background-color: #fff;\n  --background-color: rgb(247, 247, 247) !important;\n  background-clip: padding-box;\n  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n  z-index: 25;\n\n  li {\n    font-weight: 400;\n    line-height: 1.42857143;\n    display: block;\n    clear: both;\n    padding: 3px 20px;\n    white-space: nowrap;\n    color: #58595b;\n  }\n  li:hover {\n    background: #f5f5f5;\n  }\n`;\n\n/*\r\nconst OrdercolumnPrint = styled.div`\r\n    min-width: 600px;\r\n    max-width: 600px; \r\n    min-height: 1300px;\r\n    max-height: 1300px;       \r\n    position: absolute;\r\n    top: 10%;\r\n    left: 50%;\r\n    font-size:26px;\r\n    z-index:500\r\n`;*/\n_c4 = ModalUserOptions;\nconst SidebarWrap = styled.div`\n  width: 100%;\n`;\n_c5 = SidebarWrap;\nconst MenuAjuda = styled.div`\n    position: absolute;\n    top: 90px;\n    right: 0px;\n    background: white;\n    height: 60px;\n    width: max-content;\n    border-radius: 10px;\n    box-shadow: 1px 1px 5px 2px lightgray;\n    display: inline-grid;\n    justify-content: center;\n    align-items: center;\n    justify-items: center;\n    z-index:2;\n`;\n_c6 = MenuAjuda;\nconst HelpButtonContainer = styled.div`\n  position: fixed;\n  bottom: 80px;\n  right: 10px;\n  z-index: 14;\n`;\n_c7 = HelpButtonContainer;\nconst HelpButton = styled.button`\n  background: #007bff;\n  color: white;\n  font-size: 24px;\n  border: none;\n  border-radius: 50%;\n  width: 50px;\n  height: 50px;\n  cursor: pointer;\n  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);\n  transition: transform 0.2s;\n  \n  &:hover {\n    transform: scale(1.1);\n  }\n`;\n_c8 = HelpButton;\nconst HelpModal = styled.div`\n  position: absolute;\n  bottom: 60px;\n  right: 0;\n  background: white;\n  width: 220px;\n  padding: 10px;\n  border-radius: 10px;\n  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);\n  text-align: center;\n  display: ${({\n  show\n}) => show ? \"block\" : \"none\"};\n`;\n_c9 = HelpModal;\nconst SupportButton = styled.button`\n  background: #28a745;\n  color: white;\n  border: none;\n  padding: 8px 12px;\n  border-radius: 5px;\n  margin-top: 10px;\n  cursor: pointer;\n  width: 100%;\n  font-size: 14px;\n\n  &:hover {\n    background: #218838;\n  }\n`;\n\n// Hook para detectar mobile\n_c0 = SupportButton;\nfunction useIsMobile() {\n  _s();\n  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);\n  React.useEffect(() => {\n    const onResize = () => setIsMobile(window.innerWidth <= 768);\n    window.addEventListener('resize', onResize);\n    return () => window.removeEventListener('resize', onResize);\n  }, []);\n  return isMobile;\n}\n_s(useIsMobile, \"IPgBv7VuYiSHCPyFLng5ZxH+1OA=\");\nconst HelpWidget = () => {\n  _s2();\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const handleSupportClick = () => {\n    window.open(\"https://api.whatsapp.com/send?phone=+5562999677687&text=Olá, preciso de suporte!\", \"_blank\");\n  };\n  return /*#__PURE__*/_jsxDEV(HelpButtonContainer, {\n    children: [/*#__PURE__*/_jsxDEV(HelpButton, {\n      onClick: () => setIsModalOpen(!isModalOpen),\n      children: \"?\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HelpModal, {\n      show: isModalOpen,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"closeModalPedido\",\n        onClick: () => setIsModalOpen(!isModalOpen),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          iconname: \"x\",\n          iconcolor: \"#2B2B2B\",\n          iconsize: 18,\n          className: \"iconCancel\",\n          style: {\n            height: 18,\n            display: \"flex\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: 18,\n            height: 18,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"#2B2B2B\",\n            strokeWidth: 2,\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            className: \"feather feather-x\",\n            children: [/*#__PURE__*/_jsxDEV(\"line\", {\n              x1: 18,\n              y1: 6,\n              x2: 6,\n              y2: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: 6,\n              y1: 6,\n              x2: 18,\n              y2: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Necessita de Ajuda?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SupportButton, {\n        onClick: handleSupportClick,\n        children: \"Falar com o suporte\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 250,\n    columnNumber: 5\n  }, this);\n};\n_s2(HelpWidget, \"mLsII5HRP5G63IA/8vjZ5YHXWr8=\");\n_c1 = HelpWidget;\nconst CrispChat = () => {\n  _s3();\n  const {\n    user\n  } = useContext(AuthContext);\n  useEffect(() => {\n    if (!window.$crisp) {\n      window.$crisp = [];\n      window.CRISP_WEBSITE_ID = \"b90fc97a-9b0e-45d4-bf0b-94597c4c9f1e\";\n      const script = document.createElement(\"script\");\n      script.src = \"https://client.crisp.chat/l.js\";\n      script.async = true;\n      document.head.appendChild(script);\n      script.onload = () => {\n        console.log(\"✅ Crisp Chat carregado\");\n\n        // 🔹 Define a posição do botão (bottom-right)\n        window.$crisp.push([\"config\", \"position\", [\"bottom\", \"right\"]]);\n\n        // 🔹 Ajusta o z-index do widget Crisp\n        const crispButton = document.querySelector(\".crisp-client\");\n        if (crispButton) {\n          crispButton.style.zIndex = \"14\";\n        }\n\n        // 🔹 Identifica o usuário no Crisp\n        if (user) {\n          console.log(\"📌 Definindo usuário no Crisp:\", user.name, user.email);\n          window.$crisp.push([\"set\", \"user:nickname\", [user.name]]);\n          window.$crisp.push([\"set\", \"user:email\", [user.email]]);\n        }\n      };\n    }\n  }, []);\n  return null; // 🔹 Não renderiza NENHUM botão extra, apenas embute o Crisp Chat\n};\n_s3(CrispChat, \"0ttrc2NAmbTIqftZMPF4nLu0yHA=\");\n_c10 = CrispChat;\nconst LeftMenu = (/*{setSidebar , sidebar}*/\n) => {\n  _s4();\n  var _userParse$user_img;\n  const {\n    sidebar,\n    setSidebar\n  } = useContext(SidebarContext);\n  const socketRef = useRef(null);\n  const [showEditUser, setEditUser] = useState(true);\n  const [_idUserEdit, set_idUserEdit] = useState(\"\");\n  const [usernameEdit, setUsernameEdit] = useState(\"\");\n  const [emailEdit, setEmailEdit] = useState(\"\");\n  const [roleEdit, setRoleEdit] = useState(\"\");\n  const [refresh, setRefresh] = useState(false);\n  const [open, setOpen] = useState(false);\n  const [modalOpen, setModalOpen] = useState(false);\n  const isMobile = useIsMobile();\n  const showDrawer = () => {\n    setOpen(true);\n  };\n  const onClose = () => {\n    setOpen(false);\n  };\n  const INITIAL_DATA = {\n    value: \"\",\n    label: \"Selecione uma empresa\"\n  };\n  const [selectData, setselectData] = useState(INITIAL_DATA);\n  const navigate = useNavigate();\n  const [dropstatusrobo, setDropStatusRobo] = useState(false);\n  const [statusLojaTemporario, setStatusLojaTemporario] = useState(false);\n  const {\n    logout\n  } = useContext(AuthContext);\n  const secretKey = \"my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be\";\n  const userEncrypted = localStorage.getItem(\"user\");\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  const userParse = JSON.parse(user);\n  //console.log(userParse)\n  //const userParse = user;\n  const userID = userParse._id;\n  const userName = userParse.name;\n  const userEmail = userParse.email;\n  var userImg = null;\n  if (((_userParse$user_img = userParse.user_img) === null || _userParse$user_img === void 0 ? void 0 : _userParse$user_img.length) > 0) {\n    userImg = userParse.user_img[0];\n  }\n  const empresa = localStorage.getItem(\"empresa\");\n  const empresaParse = JSON.parse(empresa);\n  const idEmpresa = empresaParse.id_empresa;\n  const objIdEmpresa = empresaParse._id;\n  console.log(\"🔍 Debug - IDs da empresa:\", {\n    idEmpresa: empresaParse.id_empresa,\n    objIdEmpresa: empresaParse._id,\n    empresaCompleta: empresaParse\n  });\n  const cnpj = empresaParse.cnpj;\n  const razao = empresaParse.razao;\n  const nomeEmpresa = empresaParse.name;\n  const nomeEmpresaForUrl = nomeEmpresa.replace(/\\s+/g, \"-\").replace(/[A-Z]/g, c => c.toLowerCase());\n  const [showLinkCardapio, setShowLinkCardapio] = useState(false);\n  const [showLinkCardapioSalao, setShowLinkCardapioSalao] = useState(false);\n  const [showOptions, setUserOptions] = useState(true);\n  const [showModalImg, setModalImg] = useState(true);\n  const userOptionsRef = useRef();\n  const leftMenuRef = useRef();\n  const menuStatusRoboRef = useRef();\n  const menuStatusRoboRef_ = useRef();\n  const [statusBot, setStatusBot] = useState(false);\n  var imageDataURL = null;\n  const [atendimentosPendentes, setAtendimentosPendentes] = useState([]);\n  const userCreatedAt = new Date(userParse.createdAt);\n  const agora = new Date();\n  const diferencaEmDias = (agora - userCreatedAt) / (1000 * 60 * 60 * 24);\n  // Calcular dias de teste restantes (7 dias - diferença desde a criação)\n  const diasTesteRestantes = Math.max(Math.ceil(7 - diferencaEmDias), 0);\n  const [carregandoCheckLicense, setCarregandoCheckLicense] = useState(true);\n  const toggleSidebar = () => {\n    setSidebar(!sidebar);\n  };\n  const handleLogout = () => {\n    logout();\n  };\n  const handleSwitchChange = async () => {\n    const newStatus = !statusBot;\n    setStatusBot(newStatus);\n    console.log(\"Status atualizado:\", newStatus);\n    try {\n      const response = await updateStatusBotEmpresa(objIdEmpresa, newStatus);\n      console.log(\"Resposta do servidor:\", response);\n    } catch (error) {\n      console.error(\"Erro ao atualizar status_bot:\", error);\n    }\n  };\n\n  // Verifique se o navegador suporta notificações\n  if (!(\"Notification\" in window)) {\n    alert(\"Este navegador não suporta notificações de sistema.\");\n  } else if (Notification.permission !== \"denied\") {\n    // Pede permissão ao usuário\n    Notification.requestPermission();\n  }\n  const handleEdit = async idToEdit => {\n    setEditUser(!showEditUser);\n    setUserOptions(!showOptions);\n    const response = await getUser(idToEdit);\n    //console.log(\"Infos do Edit:\",response.data.user)\n    //console.log(idToEdit,\"----\");\n    if (showEditUser) {\n      set_idUserEdit(idToEdit);\n      setUsernameEdit(response.data.user.name);\n      setEmailEdit(response.data.user.email);\n      setRoleEdit(response.data.user.role);\n      if (response.data.user.vinculo_empresa) {\n        //console.log(\"TEM EMPRESA VINCULADA!\")\n        const responseVinculo = await getVinculoEmpresa(idToEdit);\n        if (responseVinculo.data.vinculo) {\n          setselectData({\n            value: responseVinculo.data.vinculo.id_empresa,\n            label: responseVinculo.data.vinculo.id_empresa + \" - \" + responseVinculo.data.vinculo.cnpj + \" - \" + responseVinculo.data.vinculo.name\n          });\n        }\n        if (!responseVinculo.data.vinculo) {\n          //console.log(\"MSG:\", responseVinculo.data.msg)\n        }\n      } else {\n        //console.log(\"NÃO TEM EMPRESA VINCULADA!\")\n        setselectData({\n          value: \"\",\n          label: \"\"\n        });\n      }\n    }\n  };\n  const showUserOptions = () => {\n    setUserOptions(!showOptions);\n  };\n  const showModalAddUserImg = () => {\n    setUserOptions(!showOptions);\n    setModalImg(!showModalImg);\n  };\n\n  //const [pedidos, setPedidos] = useState([])\n  const [statusPrinter, setStatusPrinter] = useState(\"\");\n  const [daysToExpire, setDaysToExpire] = useState(\"\");\n  const [planType, setPlanType] = useState(\"\");\n  const [semCobrancaOuInvoice, setSemCobrancaOuInvoice] = useState(false);\n\n  // Estados para \"Comece por aqui\"\n  const [progressoConfiguracaoInicial, setProgressoConfiguracaoInicial] = useState(0);\n  const [totalEtapasConfiguracaoInicial, setTotalEtapasConfiguracaoInicial] = useState(9);\n  const [mostrarComecePorAqui, setMostrarComecePorAqui] = useState(false);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log(\"🔄 Atualizando informações do vínculo da empresa...\");\n\n        // Obtendo o vínculo da empresa\n        const response = await getVinculoEmpresa(userID);\n        setStatusLojaTemporario(response.data.vinculo.fechamento_temporario);\n        setStatusBot(response.data.vinculo.status_bot);\n        setStatusPrinter(response.data.vinculo.status_printer);\n\n        // Verificar último check armazenado no localStorage\n        const lastCheckEncrypted = localStorage.getItem(\"ldc\");\n        const lastCheck = lastCheckEncrypted ? CryptoJS.AES.decrypt(lastCheckEncrypted, secretKey).toString(CryptoJS.enc.Utf8) : null;\n        const now = Date.now();\n\n        // Obtendo os dias restantes para expiração da licença\n        const responseCheckLicense = await getDaysToExpireLicense(response.data.vinculo._id);\n        console.log(\"responseCheckLicense:\", responseCheckLicense);\n        if (responseCheckLicense.status === 204) {\n          setSemCobrancaOuInvoice(true);\n        }\n        setDaysToExpire(responseCheckLicense.data.daysRemaining || \"\"); // Definir valor padrão vazio\n        setPlanType(responseCheckLicense.data.plan_type || \"\"); // Definir valor padrão vazio\n\n        // Atualiza o timestamp da última verificação no localStorage\n        const nowEncrypted = CryptoJS.AES.encrypt(JSON.stringify(now), secretKey).toString();\n        localStorage.setItem(\"ldc\", nowEncrypted);\n        setCarregandoCheckLicense(false);\n      } catch (error) {\n        console.error(\"❌ Erro ao obter dados:\", error);\n        setCarregandoCheckLicense(false);\n      }\n    };\n\n    // Chama a função imediatamente ao montar o componente\n    fetchData();\n\n    // Configura o intervalo para executar a cada 1 hora (3600000ms)\n    const intervalId = setInterval(fetchData, 3600000); // 1 hora\n\n    // Cleanup para limpar o intervalo ao desmontar o componente\n    return () => clearInterval(intervalId);\n\n    // eslint-disable-next-line\n  }, []);\n\n  // UseEffect para verificar progresso da configuração inicial\n  useEffect(() => {\n    if (!objIdEmpresa) return;\n    const verificarProgressoConfiguracaoInicial = async () => {\n      try {\n        const response = await getEmpresaWithObjId(objIdEmpresa);\n        const empresa = response.data.empresa;\n        let progressoAtual = 0;\n\n        // Verificar etapas concluídas usando a estrutura correta do banco\n        if (empresa.configuracao_inicial && empresa.configuracao_inicial.etapas_completas) {\n          progressoAtual = empresa.configuracao_inicial.etapas_completas.length;\n        } else if (empresa.progresso_configuracao_inicial) {\n          // Fallback para estrutura antiga\n          progressoAtual = empresa.progresso_configuracao_inicial;\n        }\n        console.log('Progresso configuração inicial:', {\n          progressoAtual,\n          totalEtapas: totalEtapasConfiguracaoInicial,\n          configuracaoInicial: empresa.configuracao_inicial\n        });\n        setProgressoConfiguracaoInicial(progressoAtual);\n\n        // Mostrar \"Comece por aqui\" apenas se não concluiu todas as etapas\n        const configuracaoCompleta = empresa.configuracao_inicial && empresa.configuracao_inicial.finalizada;\n        setMostrarComecePorAqui(!configuracaoCompleta && progressoAtual < totalEtapasConfiguracaoInicial);\n      } catch (error) {\n        console.error(\"Erro ao verificar progresso da configuração inicial:\", error);\n      }\n    };\n    verificarProgressoConfiguracaoInicial();\n  }, [objIdEmpresa, totalEtapasConfiguracaoInicial]);\n\n  /*useEffect(() => {\r\n    ////const intervalId = setInterval(() => {\r\n    ////  fetchData().then(newPedidos => setPedidos(newPedidos));\r\n    ////}, 5 * 1000); // Atualizar a cada 5 segundo\r\n      var i;\r\n    if(pedidos){\r\n      for(i=0; i<pedidos.length; i++){\r\n          //console.log(\"tipoImpressao>\",tipoImpressao);\r\n          if(pedidos[i].status_pedido=='2' && tipoImpressao == 'automatico'){\r\n              //console.log(pedidos[i])\r\n              //console.log(\"CHGEOU AUQI?\");\r\n              const orderElement = document.getElementById(`${pedidos[i].id_pedido}`);\r\n                //console.log(orderElement);\r\n              if (orderElement && orderElement.getAttribute('data-status') == \"true\") {\r\n                updateStatusPrint(userID, pedidos[i]._id, pedidos[i].id_pedido).then(printPdf(pedidos[i].id_pedido));\r\n              }\r\n          }\r\n      }\r\n    }\r\n    ////return () => clearInterval(intervalId);\r\n  }, [pedidos]); // Sem dependências, então o efeito será executado apenas uma vez*/\n\n  //const [statusImpressora, setStatusImpressora] = useState('');\n  const isDevelopment = window.location.hostname === \"localhost\";\n  const apiUrl = isDevelopment ? process.env.REACT_APP_SERVER_URL_DEV : process.env.REACT_APP_SERVER_URL_PROD;\n\n  /*useEffect(() => {\r\n    const wsUrl = apiUrl;\r\n    const socket = io(wsUrl, {\r\n      withCredentials: true,\r\n      transports: [\"websocket\"],\r\n      auth: { token: localStorage.getItem(\"token\") },\r\n    });\r\n      // **Entrar na sala da empresa correta**\r\n    socket.emit(\"joinCompanyRoom\", {\r\n      companyId: idEmpresa.toString(),\r\n      clientId: \"reactClient\",\r\n    });\r\n      // **Verificar conexão**\r\n    socket.on(\"connect\", () => {\r\n      console.log(`✅ Conectado ao WebSocket! Socket ID: ${socket.id}`);\r\n    });\r\n      console.log(`📢 Entrando na sala da empresa: ${objIdEmpresa.toString()}`);\r\n      // **Monitorar todos os eventos recebidos no socket**\r\n    socket.onAny((event, data) => {\r\n      console.log(`📥 Evento recebido no frontend: ${event}`, data);\r\n    });\r\n      // **Escutando novos pedidos**\r\n    socket.on(\"novoPedido\", (data) => {\r\n      console.log(\"📦 NOVO PEDIDO RECEBIDO!\");\r\n      handleNotify();\r\n    });\r\n      // **Escutando status da impressora**\r\n    socket.on(\"statusUpdate\", ({ companyId: updatedCompanyId, status }) => {\r\n      if (objIdEmpresa.toString() === updatedCompanyId) {\r\n        console.log(`🖨️ Status atualizado da impressora: ${status}`);\r\n        setStatusPrinter(status === \"Online\" ? \"Online\" : \"Offline\");\r\n      }\r\n    });\r\n      // **Escutando solicitações de atendimento humano**\r\n    socket.on(\"atendimento_pendente\", (data) => {\r\n      console.log(\"🚨 Novo atendimento solicitado! Dados recebidos:\", data);\r\n        // **Verificar se a empresa corresponde**\r\n      console.log(`Comparando company_id recebido (${data.company_id}) com idEmpresa (${idEmpresa.toString()})`);\r\n        // **📌 Gerar um ID único para cada solicitação**\r\n      const atendimentoComID = {\r\n        ...data,\r\n        atendimento_id: uuidv4(), // Gerando um ID único para cada solicitação\r\n      };\r\n        setAtendimentosPendentes((prev) => [...prev, atendimentoComID]);\r\n    });\r\n      return () => {\r\n      socket.off(\"novoPedido\");\r\n      socket.off(\"statusUpdate\");\r\n      socket.off(\"atendimento_pendente\");\r\n      socket.disconnect();\r\n    };\r\n  }, [idEmpresa]);*/\n  useEffect(() => {\n    if (!objIdEmpresa) return;\n\n    // Se já existir uma conexão WebSocket, desconecta antes de criar outra\n    if (socketRef.current) {\n      console.log(\"🔄 Desconectando socket anterior...\");\n      socketRef.current.disconnect();\n    }\n    console.log(`🔌 Conectando ao WebSocket para a empresa ${objIdEmpresa.toString()}...`);\n\n    // Criar a conexão WebSocket com lógica de reconexão\n    const socket = io(apiUrl, {\n      withCredentials: true,\n      transports: [\"websocket\"],\n      auth: {\n        token: localStorage.getItem(\"token\")\n      },\n      reconnection: true,\n      // Ativa a reconexão automática\n      reconnectionAttempts: 10,\n      // Máximo de 10 tentativas\n      reconnectionDelay: 5000 // Intervalo de 5 segundos entre tentativas\n    });\n\n    // Armazena a referência globalmente\n    socketRef.current = socket;\n\n    // **Entrar na sala da empresa**\n    socket.emit(\"joinCompanyRoom\", {\n      companyId: objIdEmpresa.toString(),\n      clientId: \"reactClient\"\n    });\n\n    // **Solicitar status da impressora ao conectar**\n    socket.on(\"connect\", () => {\n      console.log(`✅ Conectado ao WebSocket! Socket ID: ${socket.id}`);\n      socket.emit(\"statusRequest\", {\n        companyId: objIdEmpresa.toString()\n      }); // 🔹 Garante que o status seja atualizado\n    });\n\n    // **Monitorar tentativas de reconexão**\n    socket.on(\"reconnect_attempt\", attempt => {\n      console.log(`🔄 Tentativa de reconexão (${attempt}/10)...`);\n    });\n    socket.on(\"reconnect\", attempt => {\n      console.log(`✅ Reconectado ao WebSocket após ${attempt} tentativas!`);\n      socket.emit(\"statusRequest\", {\n        companyId: objIdEmpresa.toString()\n      }); // 🔹 Atualiza status após reconectar\n    });\n\n    // **Monitorar erro de conexão**\n    socket.on(\"connect_error\", error => {\n      console.error(\"❌ Erro na conexão WebSocket:\", error);\n    });\n\n    // **Monitorar eventos recebidos**\n    socket.onAny((event, data) => {\n      //console.log(`📥 Evento recebido: ${event}`, data);\n    });\n\n    // **Escutando novos pedidos**\n    socket.on(\"novoPedido\", () => {\n      console.log(\"📦 NOVO PEDIDO RECEBIDO!\");\n      handleNotify();\n    });\n\n    // **Escutando status da impressora**\n    socket.on(\"statusUpdate\", ({\n      companyId: updatedCompanyId,\n      status\n    }) => {\n      if (objIdEmpresa.toString() === updatedCompanyId) {\n        console.log(`🖨️ Status atualizado da impressora: ${status}`);\n        setStatusPrinter(status === \"Online\" ? \"Online\" : \"Offline\");\n      }\n    });\n\n    // **Escutando solicitações de atendimento humano**\n    socket.on(\"atendimento_pendente\", data => {\n      console.log(\"🚨 Novo atendimento solicitado! Dados recebidos:\", data);\n\n      // **Verificar se a empresa corresponde**\n      console.log(`Comparando company_id recebido (${data.company_id}) com objIdEmpresa (${objIdEmpresa})`);\n      if (data.company_id === objIdEmpresa.toString()) {\n        console.log(`✔️ Atendimento pertence à empresa ${objIdEmpresa}`);\n\n        // **📌 Gerar um ID único para cada solicitação**\n        const atendimentoComID = {\n          ...data,\n          atendimento_id: uuidv4()\n        };\n        setAtendimentosPendentes(prev => [...prev, atendimentoComID]);\n      } else {\n        console.log(`❌ Atendimento não pertence à empresa atual. Recebido: ${data.company_id}, Atual: ${objIdEmpresa}`);\n      }\n    });\n\n    // **Lógica de cleanup ao desmontar ou atualizar empresa**\n    return () => {\n      console.log(\"🛑 Desconectando WebSocket ao desmontar ou mudar empresa...\");\n      socket.off(\"novoPedido\");\n      socket.off(\"statusUpdate\");\n      socket.off(\"atendimento_pendente\");\n      socket.off(\"reconnect_attempt\");\n      socket.off(\"reconnect\");\n      socket.off(\"connect_error\");\n      socket.disconnect();\n      socketRef.current = null;\n    };\n  }, [objIdEmpresa]);\n\n  // **📌 Função para remover uma solicitação específica**\n  const removerAtendimento = atendimento_id => {\n    setAtendimentosPendentes(prev => prev.filter(item => item.atendimento_id !== atendimento_id));\n  };\n\n  // **📌 Simular chegada de um atendimento manualmente (apenas para teste local)**\n  const simularAtendimento = () => {\n    const atendimentoFake = {\n      lead_id: \"6636337cc37488bfcfdcfa82\",\n      company_id: objIdEmpresa.toString(),\n      nome: \"Matheus\",\n      celular: \"<EMAIL>\",\n      mensagem: \"Quero falar com atendente\",\n      timestamp: new Date().toISOString()\n    };\n    const atendimentoComID = {\n      ...atendimentoFake,\n      atendimento_id: uuidv4() // Gerando um ID único para cada solicitação\n    };\n    setAtendimentosPendentes(prev => [...prev, atendimentoComID]);\n  };\n  const sendNotification = (title, options) => {\n    // Verifica se o usuário aceitou receber notificações\n    if (Notification.permission === \"granted\") {\n      const notification = new Notification(title, {\n        ...options,\n        icon: LogoP // Certifique-se de que o caminho para o ícone está correto\n      });\n\n      // Toca um som quando a notificação é exibida\n      notification.onshow = () => {\n        const audio = new Audio(audioNotify);\n        audio.play().catch(error => console.log(\"Erro ao reproduzir o som da notificação:\", error));\n      };\n    }\n  };\n  const handleNotify = () => {\n    const title = \"Pede Já - Novo Pedido\";\n    const options = {\n      body: \"Você recebeu um novo pedido.\",\n      // O campo 'sound' ainda não é amplamente suportado\n      sound: audioNotify // caminho para o arquivo de áudio\n    };\n    sendNotification(title, options);\n  };\n  const handleOpenCloseLoja = async () => {\n    console.log(\"Fechar Loja\");\n    const newStatus = !statusLojaTemporario;\n    try {\n      const response = await changeStatusLoja(objIdEmpresa, newStatus);\n      if (response.status === 200) {\n        setStatusLojaTemporario(newStatus);\n      } else {\n        console.error(\"Falha ao atualizar o status da loja:\", response);\n      }\n    } catch (error) {\n      console.error(\"Erro ao chamar a API:\", error);\n    }\n  };\n  useEffect(() => {\n    // add when mounted\n    document.addEventListener(\"mousedown\", handleClickOutsideOptions);\n    // return function to be called when unmounted\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutsideOptions);\n    };\n  }, []);\n  const handleClickOutsideOptions = y => {\n    if (userOptionsRef.current.contains(y.target)) {\n      return;\n    } else {\n      setUserOptions(true);\n    }\n  };\n  useEffect(() => {\n    // add when mounted\n    //document.addEventListener(\"mouseover\", handleMouseOverLeftMenu);\n    //document.addEventListener(\"mouseout\", handleMouseOutLeftMenu);\n\n    window.addEventListener(\"resize\", handleResize);\n    // return function to be called when unmounted\n    return () => {\n      //document.removeEventListener(\"mouseover\", handleMouseOverLeftMenu);\n      //document.removeEventListener(\"mouseout\", handleMouseOutLeftMenu);\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n  const handleResize = () => {\n    const currentWidth = window.innerWidth;\n    if (currentWidth < 1300) {\n      setSidebar(false);\n    }\n  };\n  const handleMouseOverStatusRobo = y => {\n    if (menuStatusRoboRef.current && menuStatusRoboRef.current.contains(y.target) || menuStatusRoboRef_.current && menuStatusRoboRef_.current.contains(y.target)) {\n      setDropStatusRobo(true);\n      return;\n    }\n  };\n  const handleMouseOutStatusRobo = y => {\n    if (menuStatusRoboRef.current && !menuStatusRoboRef.current.contains(y.target) || menuStatusRoboRef_.current && !menuStatusRoboRef_.current.contains(y.target)) {\n      setDropStatusRobo(false);\n      return;\n    }\n  };\n  const handleToggleStatusRobo = () => {\n    setDropStatusRobo(previous => !previous);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(ModalUserImg, {\n      setModalImg: setModalImg,\n      showModalImg: showModalImg,\n      userID: userID\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 927,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalEditUser, {\n      setEditUser: setEditUser,\n      showEditUser: showEditUser,\n      setRefresh: setRefresh,\n      selectData: selectData,\n      setselectData: setselectData,\n      _idUserEdit: _idUserEdit,\n      usernameEdit: usernameEdit,\n      emailEdit: emailEdit,\n      roleEdit: roleEdit,\n      editPerfil: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 933,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalLinkCardapio, {\n      showLinkCardapio: showLinkCardapio,\n      setShowLinkCardapio: setShowLinkCardapio,\n      nomeEmpresaForUrl: nomeEmpresaForUrl\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 946,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalLinkCardapioSalao, {\n      showLinkCardapioSalao: showLinkCardapioSalao,\n      setShowLinkCardapioSalao: setShowLinkCardapioSalao,\n      nomeEmpresaForUrl: nomeEmpresaForUrl\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 952,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IconContext.Provider, {\n      value: {\n        color: \"#fff\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Nav, {\n        sidebar: sidebar,\n        style: {\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex ms-3 align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: showDrawer,\n            className: \"desktop-hidden me-4\",\n            style: {\n              cursor: \"pointer\"\n            },\n            children: /*#__PURE__*/_jsxDEV(GiHamburgerMenu, {\n              color: \"black\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"userCircleImg\",\n            style: {\n              paddingTop: \"4px\",\n              paddingBottom: \"2px\",\n              display: isMobile ? \"none\" : \"block\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: logoImg,\n              alt: \"pede-ja logo\",\n              width: 120,\n              height: 40,\n              className: \"logoImg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 960,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            ref: menuStatusRoboRef_,\n            className: dropstatusrobo ? \"menu-dropdownShow\" : \"menu-dropdownClosed\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"menu-options\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option bottom\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"info-option\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"status-option online\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"label-option\",\n                    children: [/*#__PURE__*/_jsxDEV(\"pedeja-icon\", {\n                      iconname: \"whatsapp\",\n                      iconstroke: 2,\n                      iconcolor: \"#797878\",\n                      iconsize: 18,\n                      \"_nghost-ng-c3181319476\": \"\",\n                      style: {\n                        height: 18\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: 18,\n                        height: 18,\n                        fill: \"#797878\",\n                        viewBox: \"0 0 13 13\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M9.477 7.79066C9.31409 7.70819 8.52069 7.32103 8.37241 7.26578C8.22412 7.21297 8.11647 7.18534 8.00841 7.34825C7.90278 7.50709 7.59159 7.87069 7.49653 7.97672C7.40147 8.08275 7.30803 8.09088 7.14756 8.01897C6.98466 7.9365 6.46425 7.76709 5.84634 7.21297C5.36372 6.78356 5.04237 6.25462 4.94691 6.09172C4.85184 5.93084 4.93634 5.83984 5.01678 5.75941C5.09072 5.68547 5.17969 5.57334 5.26216 5.47584C5.34056 5.37834 5.36575 5.31294 5.42303 5.20731C5.47584 5.09316 5.44862 5.00419 5.40841 4.92375C5.36819 4.84331 5.04441 4.04584 4.90912 3.72816C4.77994 3.41291 4.64466 3.45312 4.54512 3.45312C4.45209 3.44459 4.34403 3.44459 4.23638 3.44459C4.12872 3.44459 3.95281 3.48481 3.80453 3.63919C3.65625 3.80209 3.23741 4.19128 3.23741 4.97859C3.23741 5.76794 3.81712 6.53169 3.89756 6.64584C3.98003 6.75147 5.03791 8.37647 6.66087 9.07481C7.04803 9.23772 7.34866 9.33522 7.58347 9.41566C7.97062 9.53834 8.32406 9.52128 8.60316 9.48106C8.91191 9.43028 9.55947 9.08944 9.69516 8.70878C9.83288 8.32569 9.83288 8.00841 9.79266 7.9365C9.75244 7.86256 9.64681 7.82234 9.48391 7.75044L9.477 7.79066ZM6.53372 11.7812H6.52519C5.56441 11.7812 4.61459 11.5208 3.78503 11.0342L3.59044 10.918L1.55919 11.4469L2.10519 9.4705L1.97397 9.26738C1.4375 8.41439 1.1529 7.42722 1.15294 6.41956C1.15294 3.47019 3.56728 1.06437 6.53778 1.06437C7.97672 1.06437 9.32669 1.625 10.3423 2.64062C10.8433 3.13568 11.2407 3.72555 11.5114 4.37582C11.782 5.0261 11.9204 5.72376 11.9186 6.42809C11.9145 9.37544 9.50219 11.7812 6.53575 11.7812H6.53372ZM11.1146 1.86834C9.87878 0.674781 8.25378 0 6.52519 0C2.95994 0 0.056875 2.89047 0.0548438 6.44272C0.0548438 7.57697 0.351 8.68359 0.918125 9.66306L0 13L3.432 12.105C4.38275 12.6176 5.44547 12.8872 6.52559 12.8899H6.52763C10.0949 12.8899 12.998 9.99944 13 6.44475C13 4.72469 12.3293 3.10578 11.1065 1.88906L11.1146 1.86834Z\",\n                          fill: \"#797878\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1003,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 996,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 988,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"description\",\n                      children: \"Whatsapp\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1009,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 987,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"wrapper\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"switch_box box_1\",\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      className: \"switch_1\",\n                      checked: statusBot,\n                      onChange: handleSwitchChange\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1023,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1022,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"option bottom-last\",\n                style: {\n                  marginBottom: \"5px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"info-option\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"status-option online\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"label-option\",\n                    style: {\n                      display: \"inline-flex\",\n                      alignItems: \"center\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"pedeja-icon\", {\n                      iconname: \"users\",\n                      iconstroke: 2,\n                      iconcolor: \"#797878\",\n                      iconsize: 18,\n                      \"_nghost-ng-c3181319476\": \"\",\n                      style: {\n                        height: 18\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        width: 18,\n                        height: 18,\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        stroke: \"#797878\",\n                        strokeWidth: 2,\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        className: \"feather feather-users\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1062,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: 9,\n                          cy: 7,\n                          r: 4\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1063,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M23 21v-2a4 4 0 0 0-3-3.87\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1064,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M16 3.13a4 4 0 0 1 0 7.75\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1065,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1050,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1042,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: \"inline-grid\",\n                        lineHeight: \"17px\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"description\",\n                        children: \"Chamar\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1069,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"description\",\n                        children: \"Atendente\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1070,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1068,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1038,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"wrapper\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"switch_box box_1\",\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      className: \"switch_1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1077,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1076,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1075,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1032,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 977,\n            columnNumber: 13\n          }, this), isDevelopment && !isMobile ? /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"atendimento-teste-btn\",\n            onClick: simularAtendimento,\n            children: \"\\uD83D\\uDE80 Simular Atendimento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1086,\n            columnNumber: 15\n          }, this) : undefined, /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: \"5px 24px 5px 24px\",\n              borderLeft: \"1px solid rgb(208, 209, 209)\",\n              height: 80,\n              flexDirection: \"column\",\n              justifyContent: \"center\",\n              alignItems: \"center\",\n              gap: \"2px\",\n              display: \"inline-flex\"\n            },\n            ref: menuStatusRoboRef,\n            onClick: handleToggleStatusRobo\n            //dropstatusrobo={dropstatusrobo}\n            //className={dropstatusrobo ? 'open' : 'closed'}\n            ,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden-mobile\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: 15,\n                  fontWeight: 500,\n                  marginLeft: 5\n                },\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1111,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRobot, {\n              style: {\n                color: \"black\",\n                fontSize: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden-mobile\",\n              style: {\n                fontWeight: \"bold\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                fontSize: 15,\n                position: \"relative\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Robo Pede J\\xE1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(RiArrowDropDownLine, {\n                style: {\n                  color: \"black\",\n                  fontSize: 24\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1094,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"bottom\",\n            title: `Impressora: ${statusPrinter === \"Offline\" ? 'Offline' : 'Conectada'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: \"5px 24px 5px 24px\",\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\n                height: 80,\n                flexDirection: \"row\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: \"5px\",\n                display: \"inline-flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(ImPrinter, {\n                color: statusPrinter === \"Offline\" ? 'red' : '#07c670',\n                fontSize: 30\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden-mobile\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: 15,\n                      fontWeight: 500,\n                      marginLeft: 5\n                    },\n                    children: \"Impressora\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1149,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1148,\n                  columnNumber: 19\n                }, this), statusPrinter && statusPrinter === \"Offline\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"divStatusPrintNavBarOffline\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Offline\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1155,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1154,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"divStatusPrintNavBar\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Conectada\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1159,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1158,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"bottom\",\n            title: `Cardápio Delivery`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: \"5px 24px\",\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\n                height: 80,\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: \"2px\",\n                display: \"inline-flex\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => setShowLinkCardapio(true),\n                style: {\n                  cursor: \"pointer\",\n                  display: \"flex\",\n                  alignItems: \"center\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: entregadorIco,\n                  height: 30,\n                  width: 30\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex flex-column hidden-mobile\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: 14,\n                      fontWeight: 400,\n                      marginLeft: 5\n                    },\n                    children: \"Link\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1191,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: 14,\n                      fontWeight: 700,\n                      marginLeft: 5\n                    },\n                    children: \"Card\\xE1pio Delivery\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1194,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1190,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1181,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1169,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"bottom\",\n            title: `Cardápio Salão`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: \"5px 24px\",\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\n                height: 80,\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: \"2px\",\n                display: \"inline-flex\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => setShowLinkCardapioSalao(true),\n                style: {\n                  cursor: \"pointer\",\n                  display: \"flex\",\n                  alignItems: \"center\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: roundTable,\n                  height: 30,\n                  width: 30\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex flex-column hidden-mobile\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: 14,\n                      fontWeight: 400,\n                      marginLeft: 5\n                    },\n                    children: \"Link\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1228,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: 14,\n                      fontWeight: 700,\n                      marginLeft: 5\n                    },\n                    children: \"Card\\xE1pio Sal\\xE3o\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1231,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1227,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1218,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: \"5px 24px 5px 24px\",\n              height: 80,\n              flexDirection: \"column\",\n              justifyContent: \"center\",\n              alignItems: \"center\",\n              gap: \"2px\",\n              display: \"inline-flex\",\n              borderLeft: \"1px solid rgb(208, 209, 209)\"\n            },\n            className: \"hidden-sm-mobile\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(MdStorefront, {\n                style: {\n                  color: \"black\",\n                  fontSize: 20\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1253,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: 15,\n                  fontWeight: 500,\n                  marginLeft: 5\n                },\n                children: [\"Loja\", !statusLojaTemporario ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: 70,\n                    height: 18,\n                    borderRadius: 10,\n                    marginLeft: 10,\n                    background: \"#9CE8C6\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    gap: 8,\n                    display: \"inline-flex\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Aberta\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1270,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1257,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: 80,\n                    height: 18,\n                    borderRadius: 10,\n                    marginLeft: 10,\n                    background: \"#ff0000b5\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    gap: 8,\n                    color: \"white\",\n                    fontSize: 13,\n                    display: \"inline-flex\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Fechada\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1288,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1273,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1254,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: \"bold\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                fontSize: 15\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: nomeEmpresa\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1301,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1293,\n              columnNumber: 15\n            }, this), statusLojaTemporario ? /*#__PURE__*/_jsxDEV(\"div\", {\n              type: \"button\",\n              onClick: () => handleOpenCloseLoja(),\n              style: {\n                width: 150,\n                height: 20,\n                borderRadius: 5,\n                background: \"#318CD5\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: 8,\n                display: \"inline-flex\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                style: {\n                  color: \"white\",\n                  fontSize: 10\n                },\n                children: \"Abrir Loja\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1318,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1304,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              type: \"button\",\n              onClick: () => handleOpenCloseLoja(),\n              style: {\n                width: 150,\n                height: 20,\n                borderRadius: 5,\n                background: \"#318CD5\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: 8,\n                display: \"inline-flex\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                style: {\n                  color: \"white\",\n                  fontSize: 10\n                },\n                children: \"Fechar Loja\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1321,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden-mobile\",\n            style: {\n              width: 128,\n              height: 80,\n              paddingLeft: 24,\n              paddingRight: 24,\n              borderLeft: '1px #D0D1D1 solid',\n              flexDirection: 'column',\n              justifyContent: 'center',\n              alignItems: 'center',\n              gap: 8,\n              display: 'inline-flex'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: handleLogout,\n              style: {\n                textAlign: 'center',\n                cursor: \"pointer\",\n                color: '#001B30',\n                fontSize: 18,\n                fontWeight: '700',\n                letterSpacing: 0.48,\n                wordWrap: 'break-word'\n              },\n              children: [/*#__PURE__*/_jsxDEV(MdExitToApp, {\n                style: {\n                  color: 'black',\n                  fontSize: 25\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1341,\n                columnNumber: 17\n              }, this), \"Sair\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1340,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 959,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n        title: \"Navega\\xE7\\xE3o\",\n        onClose: onClose,\n        open: open,\n        placement: \"left\",\n        bodyStyle: {\n          padding: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-column justify-content-between h-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"boxLeftMenuScroll\",\n            style: {\n              overflowY: \"scroll\",\n              height: \"calc(100vh - 120px)\"\n            },\n            children: SidebarData.map((item, index) => {\n              return /*#__PURE__*/_jsxDEV(PermissionGate, {\n                permissions: [SidebarData[index].permission],\n                children: /*#__PURE__*/_jsxDEV(SubMenu, {\n                  item: item,\n                  style: {\n                    background: \"black\"\n                  }\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1358,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1354,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1348,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex flex-column\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: \"5px 24px 5px 24px\",\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\n                height: 80,\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: \"2px\",\n                display: \"inline-flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(MdStorefront, {\n                  style: {\n                    color: \"black\",\n                    fontSize: 20\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1381,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: 15,\n                    fontWeight: 500,\n                    marginLeft: 5\n                  },\n                  children: [\"Loja\", !statusLojaTemporario ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: 70,\n                      height: 18,\n                      borderRadius: 10,\n                      marginLeft: 10,\n                      background: \"#9CE8C6\",\n                      justifyContent: \"center\",\n                      alignItems: \"center\",\n                      gap: 8,\n                      display: \"inline-flex\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Aberta\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1398,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1385,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: 80,\n                      height: 18,\n                      borderRadius: 10,\n                      marginLeft: 10,\n                      background: \"#ff0000b5\",\n                      justifyContent: \"center\",\n                      alignItems: \"center\",\n                      gap: 8,\n                      color: \"white\",\n                      fontSize: 13,\n                      display: \"inline-flex\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Fechada\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1416,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1401,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1382,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1380,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: \"bold\",\n                  display: \"flex\",\n                  justifyContent: \"center\",\n                  fontSize: 15\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: nomeEmpresa\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1429,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1421,\n                columnNumber: 17\n              }, this), statusLojaTemporario ? /*#__PURE__*/_jsxDEV(\"div\", {\n                type: \"button\",\n                onClick: () => handleOpenCloseLoja(),\n                style: {\n                  width: 150,\n                  height: 20,\n                  borderRadius: 5,\n                  background: \"#318CD5\",\n                  justifyContent: \"center\",\n                  alignItems: \"center\",\n                  gap: 8,\n                  display: \"inline-flex\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  style: {\n                    color: \"white\",\n                    fontSize: 10\n                  },\n                  children: \"Abrir Loja\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1446,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1432,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                type: \"button\",\n                onClick: () => handleOpenCloseLoja(),\n                style: {\n                  width: 150,\n                  height: 20,\n                  borderRadius: 5,\n                  background: \"#318CD5\",\n                  justifyContent: \"center\",\n                  alignItems: \"center\",\n                  gap: 8,\n                  display: \"inline-flex\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  style: {\n                    color: \"white\",\n                    fontSize: 10\n                  },\n                  children: \"Fechar Loja\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1463,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1449,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden-mobile\",\n              style: {\n                width: 128,\n                height: 80,\n                paddingLeft: 24,\n                paddingRight: 24,\n                borderLeft: '1px #D0D1D1 solid',\n                flexDirection: 'column',\n                justifyContent: 'center',\n                alignItems: 'center',\n                gap: 8,\n                display: 'inline-flex'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: handleLogout,\n                style: {\n                  textAlign: 'center',\n                  cursor: \"pointer\",\n                  color: '#001B30',\n                  fontSize: 18,\n                  fontWeight: '700',\n                  letterSpacing: 0.48,\n                  wordWrap: 'break-word'\n                },\n                children: [/*#__PURE__*/_jsxDEV(MdExitToApp, {\n                  style: {\n                    color: 'black',\n                    fontSize: 25\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1469,\n                  columnNumber: 19\n                }, this), \"Sair\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1468,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1467,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1367,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1347,\n          columnNumber: 11\n        }, this)\n      }, \"left\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 1346,\n        columnNumber: 9\n      }, this), (!planType || planType === \"\" || daysToExpire == 0) && diasTesteRestantes > 0 ? !semCobrancaOuInvoice && !carregandoCheckLicense ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"info-licenca\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Dias de teste restantes: \", diasTesteRestantes, \" dias\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1478,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1477,\n        columnNumber: 13\n      }, this) : null : null, planType && planType !== \"free_trial\" && daysToExpire <= 3 && diferencaEmDias > 7 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"info-licenca\",\n        children: daysToExpire <= 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Hoje seu plano ir\\xE1 expirar, para realizar o pagamento da fatura\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"clickNavigateToPlanos\",\n            onClick: () => navigate(\"/planos\"),\n            children: \"CLIQUE AQUI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1488,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1486,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Faltam apenas \", daysToExpire, \" dias para sua assinatura expirar\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1496,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1484,\n        columnNumber: 11\n      }, this) : null, /*#__PURE__*/_jsxDEV(MenuAjuda, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: atendimentosPendentes.length > 0 ? \"item-menu-help-info-active\" : \"item-menu-help-info\",\n          onClick: () => setModalOpen(true),\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: 30,\n            height: 30,\n            viewBox: \"0 0 30 28\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            id: atendimentosPendentes.length > 0 ? \"shaking\" : undefined,\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M28.9167 24.4441V22.2775C28.916 21.3173 28.5965 20.3846 28.0082 19.6258C27.42 18.867 26.5964 18.325 25.6667 18.085\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1.3\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1515,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M22.4166 24.4443V22.2777C22.4166 21.1284 21.96 20.0262 21.1474 19.2135C20.3347 18.4009 19.2325 17.9443 18.0833 17.9443H9.41658C8.26731 17.9443 7.16511 18.4009 6.35246 19.2135C5.5398 20.0262 5.08325 21.1284 5.08325 22.2777V24.4443\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1.3\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1522,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M21.3333 5.08496C22.2654 5.32362 23.0915 5.86572 23.6815 6.6258C24.2715 7.38587 24.5917 8.32069 24.5917 9.28288C24.5917 10.2451 24.2715 11.1799 23.6815 11.94C23.0915 12.7 22.2654 13.2421 21.3333 13.4808\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1.3\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1529,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M13.7501 13.611C16.1433 13.611 18.0834 11.6709 18.0834 9.27767C18.0834 6.88444 16.1433 4.94434 13.7501 4.94434C11.3568 4.94434 9.41675 6.88444 9.41675 9.27767C9.41675 11.6709 11.3568 13.611 13.7501 13.611Z\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1.3\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1536,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8.02771 1.81393C6.65952 2.40269 4.00254 4.64734 4.32012 8.9159M3.20673 1.3753C2.33013 2.1057 1.18146 3.37006 1.36262 5.9846\",\n              stroke: \"currentColor\",\n              strokeWidth: \"1.3\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1543,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1507,\n            columnNumber: 13\n          }, this), atendimentosPendentes.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"number-box number-box--active\",\n            children: atendimentosPendentes.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1552,\n            columnNumber: 15\n          }, this) : undefined]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1504,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1503,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AtendimentoModal, {\n        atendimentosPendentes: atendimentosPendentes,\n        removerAtendimento: removerAtendimento,\n        modalOpen: modalOpen,\n        setModalOpen: setModalOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1559,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CrispChat, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1566,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SidebarNav, {\n        ref: leftMenuRef,\n        sidebar: sidebar,\n        className: sidebar ? \"open\" : \"closed\",\n        style: {\n          boxShadow: \"1px 1px 6px lightgray\"\n        },\n        children: /*#__PURE__*/_jsxDEV(SidebarWrap, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"collapseDiv\",\n            children: sidebar ? /*#__PURE__*/_jsxDEV(RiMenuFoldFill, {\n              onClick: toggleSidebar,\n              fill: \"gray\",\n              className: \"collapseInLeftMenuBtn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1580,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(RiMenuUnfoldFill, {\n              onClick: toggleSidebar,\n              fill: \"gray\",\n              className: \"collapseOutLeftMenuBtn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1586,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1578,\n            columnNumber: 13\n          }, this), sidebar ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              borderBottom: \"3px solid #4281FF\",\n              backgroundImage: `url(${userDiv})` /*background:\"rgba(0,0,0,0.8)\"*/\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"userCircleImg\",\n              style: {\n                paddingTop: \"4px\",\n                paddingBottom: \"2px\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: userImg !== null && userImg.length > 0 ? userImg : defaultUserImg,\n                width: 60,\n                height: 60,\n                className: \"userImg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1607,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1601,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavIcon /*to='#'*/, {\n              style: {\n                marginLeft: \"30px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  maxWidth: \"80%\",\n                  overflow: \"hidden\",\n                  whiteSpace: \"nowrap\"\n                },\n                children: userName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1619,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: userOptionsRef,\n                children: [/*#__PURE__*/_jsxDEV(HiIcons.HiOutlineDotsVertical, {\n                  onClick: showUserOptions,\n                  style: {\n                    color: \"white\",\n                    cursor: \"pointer\",\n                    marginLeft: \"10px\",\n                    fontSize: \"22px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1629,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ModalUserOptions, {\n                  showOptions: showOptions,\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: e => handleEdit(userID),\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      children: \"Editar Perfil\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1643,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1639,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: showModalAddUserImg,\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      children: \"Editar Imagem\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1649,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1645,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: handleLogout,\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      children: \"Sair\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1652,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1651,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1638,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1628,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1618,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                paddingBottom: \"2px\",\n                justifyContent: \"center\",\n                display: \"flex\",\n                fontSize: \"12px\",\n                color: \"white\"\n              },\n              children: userEmail\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1657,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1595,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              boxShadow: \"-1px 1px 1px 0px #0000001c\",\n              borderBottom: \"3px solid transparent\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"userCircleImg\",\n              style: {\n                paddingTop: 15,\n                paddingBottom: 5,\n                paddingRight: 3,\n                paddingBottom: 11\n              },\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: userImg !== null && userImg.length > 0 ? userImg : defaultUserImg,\n                width: 80,\n                height: 80,\n                className: \"userImg\",\n                style: {\n                  padding: \"0px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1686,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1676,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavIcon, {\n              to: \"#\",\n              style: {\n                /*display:\"none\",*/fontSize: \"0px\",\n                height: \"0px\" /*visibility:\"hidden\"*/\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  maxWidth: \"80%\",\n                  overflow: \"hidden\",\n                  whiteSpace: \"nowrap\"\n                },\n                children: userName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1705,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: userOptionsRef,\n                style: {\n                  display: \"none\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1714,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1698,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                paddingBottom: \"10px\",\n                justifyContent: \"center\",\n                display: \"flex\",\n                fontSize: \"12px\",\n                display: \"none\"\n              },\n              children: userEmail\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1716,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1670,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"boxLeftMenuScroll\",\n            style: {\n              overflowY: \"scroll\",\n              height: \"calc(100vh - 120px)\"\n            },\n            children: [mostrarComecePorAqui && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"comece-por-aqui-container\",\n              onClick: () => navigate('/configuracao-inicial'),\n              style: {\n                background: 'linear-gradient(135deg, #4299e1 0%, #667eea 100%)',\n                margin: '10px',\n                padding: '16px',\n                borderRadius: '12px',\n                cursor: 'pointer',\n                color: 'white',\n                transition: 'all 0.3s ease',\n                border: '2px solid transparent',\n                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)'\n              },\n              onMouseEnter: e => {\n                e.target.style.transform = 'translateY(-2px)';\n                e.target.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.15)';\n              },\n              onMouseLeave: e => {\n                e.target.style.transform = 'translateY(0)';\n                e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    borderRadius: '8px',\n                    padding: '8px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    minWidth: '32px',\n                    height: '32px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(FaArrowRight, {\n                    style: {\n                      fontSize: '16px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1770,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1760,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '16px',\n                      fontWeight: '600',\n                      marginBottom: '4px',\n                      display: sidebar ? 'block' : 'none'\n                    },\n                    children: \"Comece por aqui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1773,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '12px',\n                      opacity: '0.9',\n                      display: sidebar ? 'block' : 'none'\n                    },\n                    children: [totalEtapasConfiguracaoInicial - progressoConfiguracaoInicial, \" etapas restantes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1781,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1772,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1759,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '12px',\n                  display: sidebar ? 'block' : 'none'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    borderRadius: '8px',\n                    height: '6px',\n                    overflow: 'hidden'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'white',\n                      height: '100%',\n                      borderRadius: '8px',\n                      width: `${progressoConfiguracaoInicial / totalEtapasConfiguracaoInicial * 100}%`,\n                      transition: 'width 0.3s ease'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1802,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1796,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    marginTop: '6px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '11px',\n                      opacity: '0.8'\n                    },\n                    children: [progressoConfiguracaoInicial, \"/\", totalEtapasConfiguracaoInicial, \" conclu\\xEDdas\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1816,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '11px',\n                      opacity: '0.8'\n                    },\n                    children: [Math.round(progressoConfiguracaoInicial / totalEtapasConfiguracaoInicial * 100), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1819,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1810,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1792,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1736,\n              columnNumber: 17\n            }, this), SidebarData.map((item, index) => {\n              return /*#__PURE__*/_jsxDEV(PermissionGate, {\n                permissions: [SidebarData[index].permission],\n                children: /*#__PURE__*/_jsxDEV(SubMenu, {\n                  item: item,\n                  style: {\n                    background: \"black\"\n                  }\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1833,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1829,\n                columnNumber: 19\n              }, this);\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1730,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1577,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1571,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 958,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s4(LeftMenu, \"OhdpyIDHk/lKIUtfRpmMevjX3tw=\", false, function () {\n  return [useIsMobile, useNavigate];\n});\n_c11 = LeftMenu;\nexport default LeftMenu;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11;\n$RefreshReg$(_c, \"Nav\");\n$RefreshReg$(_c2, \"NavIcon\");\n$RefreshReg$(_c3, \"SidebarNav\");\n$RefreshReg$(_c4, \"ModalUserOptions\");\n$RefreshReg$(_c5, \"SidebarWrap\");\n$RefreshReg$(_c6, \"MenuAjuda\");\n$RefreshReg$(_c7, \"HelpButtonContainer\");\n$RefreshReg$(_c8, \"HelpButton\");\n$RefreshReg$(_c9, \"HelpModal\");\n$RefreshReg$(_c0, \"SupportButton\");\n$RefreshReg$(_c1, \"HelpWidget\");\n$RefreshReg$(_c10, \"CrispChat\");\n$RefreshReg$(_c11, \"LeftMenu\");", "map": {"version": 3, "names": ["React", "useContext", "useRef", "useEffect", "useState", "useNavigate", "styled", "HiIcons", "SidebarData", "SubMenu", "IconContext", "PermissionGate", "userDiv", "defaultUserImg", "LogoP", "logoImg", "audioNotify", "MdStorefront", "RiArrowDropDownLine", "FaRobot", "FaCheck", "FaArrowRight", "CryptoJS", "io", "ImPrinter", "ModalUserImg", "ModalEditUser", "ModalLinkCardapio", "ModalLinkCardapioSalao", "RiMenuFoldFill", "RiMenuUnfoldFill", "GiHamburgerMenu", "MdExitToApp", "roundTable", "entregadorIco", "v4", "uuidv4", "SidebarContext", "AuthContext", "getUser", "getVinculoEmpresa", "updateStatusBotEmpresa", "changeStatusLoja", "getDaysToExpireLicense", "getEmpresaWithObjId", "<PERSON><PERSON><PERSON>", "Drawer", "AtendimentoModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Nav", "div", "sidebar", "_c", "NavIcon", "_c2", "SidebarNav", "nav", "_c3", "ModalUserOptions", "showOptions", "_c4", "SidebarWrap", "_c5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c6", "HelpButtonContainer", "_c7", "HelpButton", "button", "_c8", "HelpModal", "show", "_c9", "SupportButton", "_c0", "useIsMobile", "_s", "isMobile", "setIsMobile", "window", "innerWidth", "onResize", "addEventListener", "removeEventListener", "HelpWidget", "_s2", "isModalOpen", "setIsModalOpen", "handleSupportClick", "open", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "iconname", "iconcolor", "iconsize", "style", "height", "display", "xmlns", "width", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x1", "y1", "x2", "y2", "_c1", "CrispChat", "_s3", "user", "$crisp", "CRISP_WEBSITE_ID", "script", "document", "createElement", "src", "async", "head", "append<PERSON><PERSON><PERSON>", "onload", "console", "log", "push", "crispButton", "querySelector", "zIndex", "name", "email", "_c10", "LeftMenu", "_s4", "_userParse$user_img", "setSidebar", "socketRef", "showEditUser", "setEditUser", "_idUserEdit", "set_idUserEdit", "usernameEdit", "setUsernameEdit", "emailEdit", "setEmailEdit", "roleEdit", "setRoleEdit", "refresh", "setRefresh", "<PERSON><PERSON><PERSON>", "modalOpen", "setModalOpen", "showDrawer", "onClose", "INITIAL_DATA", "value", "label", "selectData", "setselectData", "navigate", "dropstatus<PERSON><PERSON>", "setDropStatusRobo", "statusLojaTemporario", "setStatusLojaTemporario", "logout", "secret<PERSON>ey", "userEncrypted", "localStorage", "getItem", "AES", "decrypt", "toString", "enc", "Utf8", "userParse", "JSON", "parse", "userID", "_id", "userName", "userEmail", "userImg", "user_img", "length", "empresa", "empresaParse", "idEmpresa", "id_empresa", "objIdEmpresa", "empresaCompleta", "cnpj", "razao", "nomeEmpresa", "nomeEmpresaForUrl", "replace", "c", "toLowerCase", "showLinkCardapio", "setShowLinkCardapio", "showLinkCardapioSalao", "setShowLinkCardapioSalao", "setUserOptions", "showModalImg", "setModalImg", "userOptionsRef", "leftMenuRef", "menuStatusRoboRef", "menuStatusRoboRef_", "statusBot", "setStatusBot", "imageDataURL", "atendimentosPendentes", "setAtendimentosPendentes", "userCreatedAt", "Date", "createdAt", "agora", "diferencaEmDias", "diasTesteRestantes", "Math", "max", "ceil", "carregandoCheckLicense", "setCarregandoCheckLicense", "toggleSidebar", "handleLogout", "handleSwitchChange", "newStatus", "response", "error", "alert", "Notification", "permission", "requestPermission", "handleEdit", "idToEdit", "data", "role", "vinculo_empresa", "responseVinculo", "vinculo", "showUserOptions", "showModalAddUserImg", "statusPrinter", "setStatusPrinter", "daysToExpire", "setDaysToExpire", "planType", "setPlanType", "semCobrancaOuInvoice", "setSemCobrancaOuInvoice", "progressoConfiguracaoInicial", "setProgressoConfiguracaoInicial", "totalEtapasConfiguracaoInicial", "setTotalEtapasConfiguracaoInicial", "mostrarComecePorAqui", "setMostrarComecePorAqui", "fetchData", "fechamento_temporario", "status_bot", "status_printer", "lastCheckEncrypted", "<PERSON><PERSON><PERSON><PERSON>", "now", "responseCheckLicense", "status", "daysRemaining", "plan_type", "nowEncrypted", "encrypt", "stringify", "setItem", "intervalId", "setInterval", "clearInterval", "verificarProgressoConfiguracaoInicial", "progressoAtual", "configuracao_inicial", "etapas_completas", "progresso_configuracao_inicial", "totalEtapas", "configuracaoInicial", "configuracaoComple<PERSON>", "finalizada", "isDevelopment", "location", "hostname", "apiUrl", "process", "env", "REACT_APP_SERVER_URL_DEV", "REACT_APP_SERVER_URL_PROD", "current", "disconnect", "socket", "withCredentials", "transports", "auth", "token", "reconnection", "reconnectionAttempts", "reconnectionDelay", "emit", "companyId", "clientId", "on", "id", "attempt", "onAny", "event", "handleNotify", "updatedCompanyId", "company_id", "atendimentoComID", "atendimento_id", "prev", "off", "remover<PERSON><PERSON><PERSON><PERSON>", "filter", "item", "simularAtendimento", "atendimentoFake", "lead_id", "nome", "celular", "mensagem", "timestamp", "toISOString", "sendNotification", "title", "options", "notification", "icon", "onshow", "audio", "Audio", "play", "catch", "body", "sound", "handleOpenCloseLoja", "handleClickOutsideOptions", "y", "contains", "target", "handleResize", "currentWidth", "handleMouseOverStatusRobo", "handleMouseOutStatusRobo", "handleToggleStatusRobo", "previous", "editPerfil", "Provider", "color", "justifyContent", "cursor", "paddingTop", "paddingBottom", "alt", "ref", "iconstroke", "d", "type", "checked", "onChange", "marginBottom", "alignItems", "cx", "cy", "r", "lineHeight", "undefined", "padding", "borderLeft", "flexDirection", "gap", "fontSize", "fontWeight", "marginLeft", "position", "placement", "borderRadius", "background", "paddingLeft", "paddingRight", "textAlign", "letterSpacing", "wordWrap", "bodyStyle", "overflowY", "map", "index", "permissions", "boxShadow", "borderBottom", "backgroundImage", "max<PERSON><PERSON><PERSON>", "overflow", "whiteSpace", "HiOutlineDotsVertical", "e", "to", "margin", "transition", "border", "onMouseEnter", "transform", "onMouseLeave", "min<PERSON><PERSON><PERSON>", "flex", "opacity", "marginTop", "round", "_c11", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/components/LeftMenu/index.jsx"], "sourcesContent": ["import React, { useContext, useRef, useEffect, useState } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport styled from \"styled-components\";\r\nimport * as HiIcons from \"react-icons/hi\";\r\nimport { SidebarData } from \"./SidebarData\";\r\nimport SubMenu from \"./SubMenu\";\r\nimport { IconContext } from \"react-icons/lib\";\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport \"./style.css\";\r\nimport userDiv from \"../../img/userDiv2.png\";\r\nimport defaultUserImg from \"../../img/defaultUserImg.png\";\r\nimport LogoP from \"../../img/logoP.png\";\r\nimport logoImg from \"../../img/logoBlue.png\";\r\nimport audioNotify from \"../../assets/audio/soundNotify.mp3\";\r\nimport { MdStorefront } from \"react-icons/md\";\r\nimport { RiArrowDropDownLine } from \"react-icons/ri\";\r\nimport { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>rrow<PERSON>ight } from \"react-icons/fa\";\r\nimport CryptoJS from \"crypto-js\";\r\nimport io from \"socket.io-client\";\r\nimport { ImPrinter } from \"react-icons/im\";\r\nimport ModalUserImg from \"../ModalAddUserImg\";\r\nimport ModalEditUser from \"../../components/ModalEditUser\";\r\nimport ModalLinkCardapio from \"../ModalLinkCardapio\";\r\nimport ModalLinkCardapioSalao from \"../ModalLinkCardapioSalao\";\r\nimport { RiMenuFoldFill } from \"react-icons/ri\";\r\nimport { RiMenuUnfoldFill } from \"react-icons/ri\";\r\nimport { GiHamburgerMenu } from \"react-icons/gi\";\r\nimport { MdExitToApp } from \"react-icons/md\";\r\nimport roundTable from \"../../img/round-table.png\"\r\nimport entregadorIco from \"../../img/entregador.png\"\r\nimport { v4 as uuidv4 } from \"uuid\"; // Biblioteca para gerar IDs únicos\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport { AuthContext } from \"../../contexts/auth\";\r\nimport {\r\n  getUser,\r\n  getVinculoEmpresa,\r\n  updateStatusBotEmpresa,\r\n  changeStatusLoja,\r\n  getDaysToExpireLicense,\r\n  getEmpresaWithObjId,\r\n} from \"../../services/api\";\r\nimport { Tooltip, Drawer } from \"antd\";\r\nimport AtendimentoModal from \"./AtendimentoModal\";\r\n\r\nconst Nav = styled.div`\r\n  background: white;\r\n  --background: linear-gradient(to left, #4281ff, #51d2ff);\r\n  left: ${({ sidebar }) => (sidebar ? \"250px\" : \"100px\")};\r\n  transition: 150ms;\r\n  height: 80px;\r\n  width: ${({ sidebar }) =>\r\n    sidebar ? \"calc(100% - 250px)\" : \"calc(100% - 100px)\"};\r\n  display: flex;\r\n  --justify-content: flex-start;\r\n  align-items: center;\r\n  --border-bottom: 2px solid #0000001c;\r\n  position: relative;\r\n  z-index: 10;\r\n  box-shadow: 1px 1px 6px rgb(180, 180, 180);\r\n\r\n  @media (max-width: 880px){\r\n    left: 0;\r\n    width: 100%;\r\n  }\r\n`;\r\n\r\n//const NavIcon = styled(Link)`\r\nconst NavIcon = styled.div`\r\n  justify-content: center;\r\n  font-size: 13px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  color: white;\r\n  text-decoration: unset;\r\n  transition: font-size 150ms;\r\n\r\n`;\r\n\r\nconst SidebarNav = styled.nav`\r\n  background: white;\r\n  width: ${({ sidebar }) => (sidebar ? \"250px\" : \"100px\")};\r\n  height: 100vh;\r\n  display: flex;\r\n  justify-content: center;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  transition: width 150ms cubic-bezier(0.4, 0, 0.2, 1); // Ajustado para uma curva bezier comum para movimento\r\n  z-index: 10;\r\n  border-right: solid 1px rgb(240, 240, 240);\r\n\r\n  span {\r\n    transition-delay: 200ms;\r\n    transition-property: font-size, visibility, opacity; // Adicionando propriedades específicas\r\n    visibility: ${({ sidebar }) => (sidebar ? \"visible\" : \"hidden\")};\r\n    opacity: ${({ sidebar }) => (sidebar ? \"1\" : \"0\")};\r\n    font-size: ${({ sidebar }) => (sidebar ? \"14px\" : \"0px\")};\r\n    transition: visibility 0s, opacity 0.5s,\r\n      ${({ sidebar }) =>\r\n    sidebar ? \"font-size 250ms linear\" : \"font-size 100ms linear\"};\r\n  }\r\n\r\n  @media (max-width: 880px) {\r\n    display: none;\r\n  }\r\n`;\r\n\r\nconst ModalUserOptions = styled.div`\r\n  font-size: 14px;\r\n  position: absolute;\r\n  top: 82px;\r\n  left: 150px;\r\n  display: ${({ showOptions }) => (showOptions ? \"none\" : \"\")};\r\n  float: left;\r\n  min-width: 160px;\r\n  margin: 2px 0 0;\r\n  padding: 5px 0;\r\n  list-style: none;\r\n  text-align: left;\r\n  border: 1px solid #ccc;\r\n  border: 1px solid rgba(0, 0, 0, 0.15);\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  --background-color: rgb(247, 247, 247) !important;\r\n  background-clip: padding-box;\r\n  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\r\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\r\n  z-index: 25;\r\n\r\n  li {\r\n    font-weight: 400;\r\n    line-height: 1.42857143;\r\n    display: block;\r\n    clear: both;\r\n    padding: 3px 20px;\r\n    white-space: nowrap;\r\n    color: #58595b;\r\n  }\r\n  li:hover {\r\n    background: #f5f5f5;\r\n  }\r\n`;\r\n\r\n/*\r\nconst OrdercolumnPrint = styled.div`\r\n    min-width: 600px;\r\n    max-width: 600px; \r\n    min-height: 1300px;\r\n    max-height: 1300px;       \r\n    position: absolute;\r\n    top: 10%;\r\n    left: 50%;\r\n    font-size:26px;\r\n    z-index:500\r\n`;*/\r\n\r\nconst SidebarWrap = styled.div`\r\n  width: 100%;\r\n`;\r\n\r\nconst MenuAjuda = styled.div`\r\n    position: absolute;\r\n    top: 90px;\r\n    right: 0px;\r\n    background: white;\r\n    height: 60px;\r\n    width: max-content;\r\n    border-radius: 10px;\r\n    box-shadow: 1px 1px 5px 2px lightgray;\r\n    display: inline-grid;\r\n    justify-content: center;\r\n    align-items: center;\r\n    justify-items: center;\r\n    z-index:2;\r\n`;\r\n\r\nconst HelpButtonContainer = styled.div`\r\n  position: fixed;\r\n  bottom: 80px;\r\n  right: 10px;\r\n  z-index: 14;\r\n`;\r\n\r\nconst HelpButton = styled.button`\r\n  background: #007bff;\r\n  color: white;\r\n  font-size: 24px;\r\n  border: none;\r\n  border-radius: 50%;\r\n  width: 50px;\r\n  height: 50px;\r\n  cursor: pointer;\r\n  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);\r\n  transition: transform 0.2s;\r\n  \r\n  &:hover {\r\n    transform: scale(1.1);\r\n  }\r\n`;\r\n\r\nconst HelpModal = styled.div`\r\n  position: absolute;\r\n  bottom: 60px;\r\n  right: 0;\r\n  background: white;\r\n  width: 220px;\r\n  padding: 10px;\r\n  border-radius: 10px;\r\n  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);\r\n  text-align: center;\r\n  display: ${({ show }) => (show ? \"block\" : \"none\")};\r\n`;\r\n\r\nconst SupportButton = styled.button`\r\n  background: #28a745;\r\n  color: white;\r\n  border: none;\r\n  padding: 8px 12px;\r\n  border-radius: 5px;\r\n  margin-top: 10px;\r\n  cursor: pointer;\r\n  width: 100%;\r\n  font-size: 14px;\r\n\r\n  &:hover {\r\n    background: #218838;\r\n  }\r\n`;\r\n\r\n// Hook para detectar mobile\r\nfunction useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);\r\n  React.useEffect(() => {\r\n    const onResize = () => setIsMobile(window.innerWidth <= 768);\r\n    window.addEventListener('resize', onResize);\r\n    return () => window.removeEventListener('resize', onResize);\r\n  }, []);\r\n  return isMobile;\r\n}\r\n\r\nconst HelpWidget = () => {\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  const handleSupportClick = () => {\r\n    window.open(\"https://api.whatsapp.com/send?phone=+5562999677687&text=Olá, preciso de suporte!\", \"_blank\");\r\n  };\r\n\r\n  return (\r\n    <HelpButtonContainer>\r\n      <HelpButton onClick={() => setIsModalOpen(!isModalOpen)}>?</HelpButton>\r\n      <HelpModal show={isModalOpen}>\r\n        <div className=\"closeModalPedido\" onClick={() => setIsModalOpen(!isModalOpen)}>\r\n          <div\r\n            iconname=\"x\"\r\n            iconcolor=\"#2B2B2B\"\r\n            iconsize={18}\r\n            className=\"iconCancel\"\r\n            style={{ height: 18, display: \"flex\" }}\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width={18}\r\n              height={18}\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"#2B2B2B\"\r\n              strokeWidth={2}\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              className=\"feather feather-x\"\r\n            >\r\n              <line x1={18} y1={6} x2={6} y2={18} />\r\n              <line x1={6} y1={6} x2={18} y2={18} />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n        <p>Necessita de Ajuda?</p>\r\n        <SupportButton onClick={handleSupportClick}>Falar com o suporte</SupportButton>\r\n      </HelpModal>\r\n    </HelpButtonContainer>\r\n  );\r\n};\r\n\r\nconst CrispChat = () => {\r\n  const { user } = useContext(AuthContext);\r\n  useEffect(() => {\r\n    if (!window.$crisp) {\r\n      window.$crisp = [];\r\n      window.CRISP_WEBSITE_ID = \"b90fc97a-9b0e-45d4-bf0b-94597c4c9f1e\";\r\n\r\n      const script = document.createElement(\"script\");\r\n      script.src = \"https://client.crisp.chat/l.js\";\r\n      script.async = true;\r\n      document.head.appendChild(script);\r\n\r\n      script.onload = () => {\r\n        console.log(\"✅ Crisp Chat carregado\");\r\n\r\n        // 🔹 Define a posição do botão (bottom-right)\r\n        window.$crisp.push([\"config\", \"position\", [\"bottom\", \"right\"]]);\r\n\r\n        // 🔹 Ajusta o z-index do widget Crisp\r\n        const crispButton = document.querySelector(\".crisp-client\");\r\n        if (crispButton) {\r\n          crispButton.style.zIndex = \"14\";\r\n        }\r\n\r\n        // 🔹 Identifica o usuário no Crisp\r\n        if (user) {\r\n          console.log(\"📌 Definindo usuário no Crisp:\", user.name, user.email);\r\n          window.$crisp.push([\"set\", \"user:nickname\", [user.name]]);\r\n          window.$crisp.push([\"set\", \"user:email\", [user.email]]);\r\n        }\r\n      };\r\n    }\r\n\r\n  }, []);\r\n\r\n  return null; // 🔹 Não renderiza NENHUM botão extra, apenas embute o Crisp Chat\r\n};\r\n\r\nconst LeftMenu = (/*{setSidebar , sidebar}*/) => {\r\n  const { sidebar, setSidebar } = useContext(SidebarContext);\r\n  const socketRef = useRef(null);\r\n  const [showEditUser, setEditUser] = useState(true);\r\n  const [_idUserEdit, set_idUserEdit] = useState(\"\");\r\n  const [usernameEdit, setUsernameEdit] = useState(\"\");\r\n  const [emailEdit, setEmailEdit] = useState(\"\");\r\n  const [roleEdit, setRoleEdit] = useState(\"\");\r\n  const [refresh, setRefresh] = useState(false);\r\n  const [open, setOpen] = useState(false);\r\n  const [modalOpen, setModalOpen] = useState(false);\r\n\r\n  const isMobile = useIsMobile();\r\n\r\n  const showDrawer = () => {\r\n    setOpen(true);\r\n  };\r\n\r\n  const onClose = () => {\r\n    setOpen(false);\r\n  };\r\n\r\n  const INITIAL_DATA = {\r\n    value: \"\",\r\n    label: \"Selecione uma empresa\",\r\n  };\r\n  const [selectData, setselectData] = useState(INITIAL_DATA);\r\n  const navigate = useNavigate();\r\n\r\n  const [dropstatusrobo, setDropStatusRobo] = useState(false);\r\n  const [statusLojaTemporario, setStatusLojaTemporario] = useState(false);\r\n\r\n  const { logout } = useContext(AuthContext);\r\n  const secretKey =\r\n    \"my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be\";\r\n\r\n  const userEncrypted = localStorage.getItem(\"user\");\r\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(\r\n    CryptoJS.enc.Utf8\r\n  );\r\n  const userParse = JSON.parse(user);\r\n  //console.log(userParse)\r\n  //const userParse = user;\r\n  const userID = userParse._id;\r\n  const userName = userParse.name;\r\n  const userEmail = userParse.email;\r\n  var userImg = null;\r\n  if (userParse.user_img?.length > 0) {\r\n    userImg = userParse.user_img[0];\r\n  }\r\n\r\n  const empresa = localStorage.getItem(\"empresa\");\r\n  const empresaParse = JSON.parse(empresa);\r\n  const idEmpresa = empresaParse.id_empresa;\r\n  const objIdEmpresa = empresaParse._id;\r\n  console.log(\"🔍 Debug - IDs da empresa:\", {\r\n    idEmpresa: empresaParse.id_empresa,\r\n    objIdEmpresa: empresaParse._id,\r\n    empresaCompleta: empresaParse\r\n  });\r\n  const cnpj = empresaParse.cnpj;\r\n  const razao = empresaParse.razao;\r\n  const nomeEmpresa = empresaParse.name;\r\n  const nomeEmpresaForUrl = nomeEmpresa\r\n    .replace(/\\s+/g, \"-\")\r\n    .replace(/[A-Z]/g, (c) => c.toLowerCase());\r\n\r\n  const [showLinkCardapio, setShowLinkCardapio] = useState(false);\r\n  const [showLinkCardapioSalao, setShowLinkCardapioSalao] = useState(false);\r\n  const [showOptions, setUserOptions] = useState(true);\r\n  const [showModalImg, setModalImg] = useState(true);\r\n  const userOptionsRef = useRef();\r\n  const leftMenuRef = useRef();\r\n  const menuStatusRoboRef = useRef();\r\n  const menuStatusRoboRef_ = useRef();\r\n  const [statusBot, setStatusBot] = useState(false);\r\n  var imageDataURL = null;\r\n  const [atendimentosPendentes, setAtendimentosPendentes] = useState([]);\r\n\r\n  const userCreatedAt = new Date(userParse.createdAt);\r\n  const agora = new Date();\r\n  const diferencaEmDias = (agora - userCreatedAt) / (1000 * 60 * 60 * 24);\r\n  // Calcular dias de teste restantes (7 dias - diferença desde a criação)\r\n  const diasTesteRestantes = Math.max(Math.ceil(7 - diferencaEmDias), 0);\r\n  const [carregandoCheckLicense, setCarregandoCheckLicense] = useState(true);\r\n\r\n  const toggleSidebar = () => {\r\n    setSidebar(!sidebar);\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    logout();\r\n  };\r\n\r\n  const handleSwitchChange = async () => {\r\n    const newStatus = !statusBot;\r\n    setStatusBot(newStatus);\r\n    console.log(\"Status atualizado:\", newStatus);\r\n    try {\r\n      const response = await updateStatusBotEmpresa(objIdEmpresa, newStatus);\r\n      console.log(\"Resposta do servidor:\", response);\r\n    } catch (error) {\r\n      console.error(\"Erro ao atualizar status_bot:\", error);\r\n    }\r\n  };\r\n\r\n  // Verifique se o navegador suporta notificações\r\n  if (!(\"Notification\" in window)) {\r\n    alert(\"Este navegador não suporta notificações de sistema.\");\r\n  } else if (Notification.permission !== \"denied\") {\r\n    // Pede permissão ao usuário\r\n    Notification.requestPermission();\r\n  }\r\n\r\n  const handleEdit = async (idToEdit) => {\r\n    setEditUser(!showEditUser);\r\n    setUserOptions(!showOptions);\r\n    const response = await getUser(idToEdit);\r\n    //console.log(\"Infos do Edit:\",response.data.user)\r\n    //console.log(idToEdit,\"----\");\r\n    if (showEditUser) {\r\n      set_idUserEdit(idToEdit);\r\n      setUsernameEdit(response.data.user.name);\r\n      setEmailEdit(response.data.user.email);\r\n      setRoleEdit(response.data.user.role);\r\n\r\n      if (response.data.user.vinculo_empresa) {\r\n        //console.log(\"TEM EMPRESA VINCULADA!\")\r\n        const responseVinculo = await getVinculoEmpresa(idToEdit);\r\n        if (responseVinculo.data.vinculo) {\r\n          setselectData({\r\n            value: responseVinculo.data.vinculo.id_empresa,\r\n            label:\r\n              responseVinculo.data.vinculo.id_empresa +\r\n              \" - \" +\r\n              responseVinculo.data.vinculo.cnpj +\r\n              \" - \" +\r\n              responseVinculo.data.vinculo.name,\r\n          });\r\n        }\r\n        if (!responseVinculo.data.vinculo) {\r\n          //console.log(\"MSG:\", responseVinculo.data.msg)\r\n        }\r\n      } else {\r\n        //console.log(\"NÃO TEM EMPRESA VINCULADA!\")\r\n        setselectData({\r\n          value: \"\",\r\n          label: \"\",\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  const showUserOptions = () => {\r\n    setUserOptions(!showOptions);\r\n  };\r\n\r\n  const showModalAddUserImg = () => {\r\n    setUserOptions(!showOptions);\r\n    setModalImg(!showModalImg);\r\n  };\r\n\r\n  //const [pedidos, setPedidos] = useState([])\r\n  const [statusPrinter, setStatusPrinter] = useState(\"\");\r\n  const [daysToExpire, setDaysToExpire] = useState(\"\");\r\n  const [planType, setPlanType] = useState(\"\");\r\n  const [semCobrancaOuInvoice, setSemCobrancaOuInvoice] = useState(false);\r\n  \r\n  // Estados para \"Comece por aqui\"\r\n  const [progressoConfiguracaoInicial, setProgressoConfiguracaoInicial] = useState(0);\r\n  const [totalEtapasConfiguracaoInicial, setTotalEtapasConfiguracaoInicial] = useState(9);\r\n  const [mostrarComecePorAqui, setMostrarComecePorAqui] = useState(false);\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        console.log(\"🔄 Atualizando informações do vínculo da empresa...\");\r\n\r\n        // Obtendo o vínculo da empresa\r\n        const response = await getVinculoEmpresa(userID);\r\n        setStatusLojaTemporario(response.data.vinculo.fechamento_temporario);\r\n        setStatusBot(response.data.vinculo.status_bot);\r\n        setStatusPrinter(response.data.vinculo.status_printer);\r\n\r\n        // Verificar último check armazenado no localStorage\r\n        const lastCheckEncrypted = localStorage.getItem(\"ldc\");\r\n        const lastCheck = lastCheckEncrypted\r\n          ? CryptoJS.AES.decrypt(lastCheckEncrypted, secretKey).toString(\r\n            CryptoJS.enc.Utf8\r\n          )\r\n          : null;\r\n        const now = Date.now();\r\n\r\n        // Obtendo os dias restantes para expiração da licença\r\n        const responseCheckLicense = await getDaysToExpireLicense(\r\n          response.data.vinculo._id\r\n        );\r\n\r\n        console.log(\"responseCheckLicense:\", responseCheckLicense);\r\n        if (responseCheckLicense.status === 204) {\r\n          setSemCobrancaOuInvoice(true);\r\n        }\r\n\r\n        setDaysToExpire(responseCheckLicense.data.daysRemaining || \"\"); // Definir valor padrão vazio\r\n        setPlanType(responseCheckLicense.data.plan_type || \"\"); // Definir valor padrão vazio\r\n\r\n        // Atualiza o timestamp da última verificação no localStorage\r\n        const nowEncrypted = CryptoJS.AES.encrypt(\r\n          JSON.stringify(now),\r\n          secretKey\r\n        ).toString();\r\n        localStorage.setItem(\"ldc\", nowEncrypted);\r\n\r\n        setCarregandoCheckLicense(false);\r\n      } catch (error) {\r\n        console.error(\"❌ Erro ao obter dados:\", error);\r\n        setCarregandoCheckLicense(false);\r\n      }\r\n    };\r\n\r\n    // Chama a função imediatamente ao montar o componente\r\n    fetchData();\r\n\r\n    // Configura o intervalo para executar a cada 1 hora (3600000ms)\r\n    const intervalId = setInterval(fetchData, 3600000); // 1 hora\r\n\r\n    // Cleanup para limpar o intervalo ao desmontar o componente\r\n    return () => clearInterval(intervalId);\r\n\r\n    // eslint-disable-next-line\r\n  }, []);\r\n\r\n  // UseEffect para verificar progresso da configuração inicial\r\n  useEffect(() => {\r\n    if (!objIdEmpresa) return;\r\n\r\n    const verificarProgressoConfiguracaoInicial = async () => {\r\n      try {\r\n        const response = await getEmpresaWithObjId(objIdEmpresa);\r\n        const empresa = response.data.empresa;\r\n        \r\n        let progressoAtual = 0;\r\n        \r\n        // Verificar etapas concluídas usando a estrutura correta do banco\r\n        if (empresa.configuracao_inicial && empresa.configuracao_inicial.etapas_completas) {\r\n          progressoAtual = empresa.configuracao_inicial.etapas_completas.length;\r\n        } else if (empresa.progresso_configuracao_inicial) {\r\n          // Fallback para estrutura antiga\r\n          progressoAtual = empresa.progresso_configuracao_inicial;\r\n        }\r\n        \r\n        console.log('Progresso configuração inicial:', {\r\n          progressoAtual,\r\n          totalEtapas: totalEtapasConfiguracaoInicial,\r\n          configuracaoInicial: empresa.configuracao_inicial\r\n        });\r\n        \r\n        setProgressoConfiguracaoInicial(progressoAtual);\r\n        \r\n        // Mostrar \"Comece por aqui\" apenas se não concluiu todas as etapas\r\n        const configuracaoCompleta = empresa.configuracao_inicial && empresa.configuracao_inicial.finalizada;\r\n        setMostrarComecePorAqui(!configuracaoCompleta && progressoAtual < totalEtapasConfiguracaoInicial);\r\n      } catch (error) {\r\n        console.error(\"Erro ao verificar progresso da configuração inicial:\", error);\r\n      }\r\n    };\r\n\r\n    verificarProgressoConfiguracaoInicial();\r\n  }, [objIdEmpresa, totalEtapasConfiguracaoInicial]);\r\n\r\n  /*useEffect(() => {\r\n    ////const intervalId = setInterval(() => {\r\n    ////  fetchData().then(newPedidos => setPedidos(newPedidos));\r\n    ////}, 5 * 1000); // Atualizar a cada 5 segundo\r\n\r\n    var i;\r\n    if(pedidos){\r\n      for(i=0; i<pedidos.length; i++){\r\n          //console.log(\"tipoImpressao>\",tipoImpressao);\r\n          if(pedidos[i].status_pedido=='2' && tipoImpressao == 'automatico'){\r\n              //console.log(pedidos[i])\r\n              //console.log(\"CHGEOU AUQI?\");\r\n              const orderElement = document.getElementById(`${pedidos[i].id_pedido}`);\r\n\r\n              //console.log(orderElement);\r\n              if (orderElement && orderElement.getAttribute('data-status') == \"true\") {\r\n                updateStatusPrint(userID, pedidos[i]._id, pedidos[i].id_pedido).then(printPdf(pedidos[i].id_pedido));\r\n              }\r\n          }\r\n      }\r\n    }\r\n    ////return () => clearInterval(intervalId);\r\n  }, [pedidos]); // Sem dependências, então o efeito será executado apenas uma vez*/\r\n\r\n  //const [statusImpressora, setStatusImpressora] = useState('');\r\n  const isDevelopment = window.location.hostname === \"localhost\";\r\n  const apiUrl = isDevelopment\r\n    ? process.env.REACT_APP_SERVER_URL_DEV\r\n    : process.env.REACT_APP_SERVER_URL_PROD;\r\n\r\n  /*useEffect(() => {\r\n    const wsUrl = apiUrl;\r\n    const socket = io(wsUrl, {\r\n      withCredentials: true,\r\n      transports: [\"websocket\"],\r\n      auth: { token: localStorage.getItem(\"token\") },\r\n    });\r\n\r\n    // **Entrar na sala da empresa correta**\r\n    socket.emit(\"joinCompanyRoom\", {\r\n      companyId: idEmpresa.toString(),\r\n      clientId: \"reactClient\",\r\n    });\r\n\r\n    // **Verificar conexão**\r\n    socket.on(\"connect\", () => {\r\n      console.log(`✅ Conectado ao WebSocket! Socket ID: ${socket.id}`);\r\n    });\r\n\r\n    console.log(`📢 Entrando na sala da empresa: ${objIdEmpresa.toString()}`);\r\n\r\n    // **Monitorar todos os eventos recebidos no socket**\r\n    socket.onAny((event, data) => {\r\n      console.log(`📥 Evento recebido no frontend: ${event}`, data);\r\n    });\r\n\r\n    // **Escutando novos pedidos**\r\n    socket.on(\"novoPedido\", (data) => {\r\n      console.log(\"📦 NOVO PEDIDO RECEBIDO!\");\r\n      handleNotify();\r\n    });\r\n\r\n    // **Escutando status da impressora**\r\n    socket.on(\"statusUpdate\", ({ companyId: updatedCompanyId, status }) => {\r\n      if (objIdEmpresa.toString() === updatedCompanyId) {\r\n        console.log(`🖨️ Status atualizado da impressora: ${status}`);\r\n        setStatusPrinter(status === \"Online\" ? \"Online\" : \"Offline\");\r\n      }\r\n    });\r\n\r\n    // **Escutando solicitações de atendimento humano**\r\n    socket.on(\"atendimento_pendente\", (data) => {\r\n      console.log(\"🚨 Novo atendimento solicitado! Dados recebidos:\", data);\r\n\r\n      // **Verificar se a empresa corresponde**\r\n      console.log(`Comparando company_id recebido (${data.company_id}) com idEmpresa (${idEmpresa.toString()})`);\r\n\r\n      // **📌 Gerar um ID único para cada solicitação**\r\n      const atendimentoComID = {\r\n        ...data,\r\n        atendimento_id: uuidv4(), // Gerando um ID único para cada solicitação\r\n      };\r\n\r\n      setAtendimentosPendentes((prev) => [...prev, atendimentoComID]);\r\n    });\r\n\r\n    return () => {\r\n      socket.off(\"novoPedido\");\r\n      socket.off(\"statusUpdate\");\r\n      socket.off(\"atendimento_pendente\");\r\n      socket.disconnect();\r\n    };\r\n  }, [idEmpresa]);*/\r\n  useEffect(() => {\r\n    if (!objIdEmpresa) return;\r\n\r\n    // Se já existir uma conexão WebSocket, desconecta antes de criar outra\r\n    if (socketRef.current) {\r\n      console.log(\"🔄 Desconectando socket anterior...\");\r\n      socketRef.current.disconnect();\r\n    }\r\n\r\n    console.log(`🔌 Conectando ao WebSocket para a empresa ${objIdEmpresa.toString()}...`);\r\n\r\n    // Criar a conexão WebSocket com lógica de reconexão\r\n    const socket = io(apiUrl, {\r\n      withCredentials: true,\r\n      transports: [\"websocket\"],\r\n      auth: { token: localStorage.getItem(\"token\") },\r\n      reconnection: true, // Ativa a reconexão automática\r\n      reconnectionAttempts: 10, // Máximo de 10 tentativas\r\n      reconnectionDelay: 5000, // Intervalo de 5 segundos entre tentativas\r\n    });\r\n\r\n    // Armazena a referência globalmente\r\n    socketRef.current = socket;\r\n\r\n    // **Entrar na sala da empresa**\r\n    socket.emit(\"joinCompanyRoom\", {\r\n      companyId: objIdEmpresa.toString(),\r\n      clientId: \"reactClient\",\r\n    });\r\n\r\n    // **Solicitar status da impressora ao conectar**\r\n    socket.on(\"connect\", () => {\r\n      console.log(`✅ Conectado ao WebSocket! Socket ID: ${socket.id}`);\r\n      socket.emit(\"statusRequest\", { companyId: objIdEmpresa.toString() }); // 🔹 Garante que o status seja atualizado\r\n    });\r\n\r\n    // **Monitorar tentativas de reconexão**\r\n    socket.on(\"reconnect_attempt\", (attempt) => {\r\n      console.log(`🔄 Tentativa de reconexão (${attempt}/10)...`);\r\n    });\r\n\r\n    socket.on(\"reconnect\", (attempt) => {\r\n      console.log(`✅ Reconectado ao WebSocket após ${attempt} tentativas!`);\r\n      socket.emit(\"statusRequest\", { companyId: objIdEmpresa.toString() }); // 🔹 Atualiza status após reconectar\r\n    });\r\n\r\n    // **Monitorar erro de conexão**\r\n    socket.on(\"connect_error\", (error) => {\r\n      console.error(\"❌ Erro na conexão WebSocket:\", error);\r\n    });\r\n\r\n    // **Monitorar eventos recebidos**\r\n    socket.onAny((event, data) => {\r\n      //console.log(`📥 Evento recebido: ${event}`, data);\r\n    });\r\n\r\n    // **Escutando novos pedidos**\r\n    socket.on(\"novoPedido\", () => {\r\n      console.log(\"📦 NOVO PEDIDO RECEBIDO!\");\r\n      handleNotify();\r\n    });\r\n\r\n    // **Escutando status da impressora**\r\n    socket.on(\"statusUpdate\", ({ companyId: updatedCompanyId, status }) => {\r\n      if (objIdEmpresa.toString() === updatedCompanyId) {\r\n        console.log(`🖨️ Status atualizado da impressora: ${status}`);\r\n        setStatusPrinter(status === \"Online\" ? \"Online\" : \"Offline\");\r\n      }\r\n    });\r\n\r\n    // **Escutando solicitações de atendimento humano**\r\n    socket.on(\"atendimento_pendente\", (data) => {\r\n      console.log(\"🚨 Novo atendimento solicitado! Dados recebidos:\", data);\r\n\r\n      // **Verificar se a empresa corresponde**\r\n      console.log(`Comparando company_id recebido (${data.company_id}) com objIdEmpresa (${objIdEmpresa})`);\r\n      \r\n      if (data.company_id === objIdEmpresa.toString()) {\r\n        console.log(`✔️ Atendimento pertence à empresa ${objIdEmpresa}`);\r\n\r\n        // **📌 Gerar um ID único para cada solicitação**\r\n        const atendimentoComID = {\r\n          ...data,\r\n          atendimento_id: uuidv4(),\r\n        };\r\n\r\n        setAtendimentosPendentes((prev) => [...prev, atendimentoComID]);\r\n      } else {\r\n        console.log(`❌ Atendimento não pertence à empresa atual. Recebido: ${data.company_id}, Atual: ${objIdEmpresa}`);\r\n      }\r\n    });\r\n\r\n    // **Lógica de cleanup ao desmontar ou atualizar empresa**\r\n    return () => {\r\n      console.log(\"🛑 Desconectando WebSocket ao desmontar ou mudar empresa...\");\r\n      socket.off(\"novoPedido\");\r\n      socket.off(\"statusUpdate\");\r\n      socket.off(\"atendimento_pendente\");\r\n      socket.off(\"reconnect_attempt\");\r\n      socket.off(\"reconnect\");\r\n      socket.off(\"connect_error\");\r\n      socket.disconnect();\r\n      socketRef.current = null;\r\n    };\r\n  }, [objIdEmpresa]);\r\n\r\n\r\n  // **📌 Função para remover uma solicitação específica**\r\n  const removerAtendimento = (atendimento_id) => {\r\n    setAtendimentosPendentes((prev) => prev.filter((item) => item.atendimento_id !== atendimento_id));\r\n  };\r\n\r\n  // **📌 Simular chegada de um atendimento manualmente (apenas para teste local)**\r\n  const simularAtendimento = () => {\r\n    const atendimentoFake = {\r\n      lead_id: \"6636337cc37488bfcfdcfa82\",\r\n      company_id: objIdEmpresa.toString(),\r\n      nome: \"Matheus\",\r\n      celular: \"<EMAIL>\",\r\n      mensagem: \"Quero falar com atendente\",\r\n      timestamp: new Date().toISOString(),\r\n    };\r\n    const atendimentoComID = {\r\n      ...atendimentoFake,\r\n      atendimento_id: uuidv4(), // Gerando um ID único para cada solicitação\r\n    };\r\n    setAtendimentosPendentes((prev) => [...prev, atendimentoComID]);\r\n  };\r\n\r\n  const sendNotification = (title, options) => {\r\n    // Verifica se o usuário aceitou receber notificações\r\n    if (Notification.permission === \"granted\") {\r\n      const notification = new Notification(title, {\r\n        ...options,\r\n        icon: LogoP, // Certifique-se de que o caminho para o ícone está correto\r\n      });\r\n\r\n      // Toca um som quando a notificação é exibida\r\n      notification.onshow = () => {\r\n        const audio = new Audio(audioNotify);\r\n        audio\r\n          .play()\r\n          .catch((error) =>\r\n            console.log(\"Erro ao reproduzir o som da notificação:\", error)\r\n          );\r\n      };\r\n    }\r\n  };\r\n\r\n  const handleNotify = () => {\r\n    const title = \"Pede Já - Novo Pedido\";\r\n    const options = {\r\n      body: \"Você recebeu um novo pedido.\",\r\n      // O campo 'sound' ainda não é amplamente suportado\r\n      sound: audioNotify, // caminho para o arquivo de áudio\r\n    };\r\n    sendNotification(title, options);\r\n  };\r\n\r\n  const handleOpenCloseLoja = async () => {\r\n    console.log(\"Fechar Loja\");\r\n    const newStatus = !statusLojaTemporario;\r\n    try {\r\n      const response = await changeStatusLoja(objIdEmpresa, newStatus);\r\n      if (response.status === 200) {\r\n        setStatusLojaTemporario(newStatus);\r\n      } else {\r\n        console.error(\"Falha ao atualizar o status da loja:\", response);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao chamar a API:\", error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // add when mounted\r\n    document.addEventListener(\"mousedown\", handleClickOutsideOptions);\r\n    // return function to be called when unmounted\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutsideOptions);\r\n    };\r\n  }, []);\r\n\r\n  const handleClickOutsideOptions = (y) => {\r\n    if (userOptionsRef.current.contains(y.target)) {\r\n      return;\r\n    } else {\r\n      setUserOptions(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // add when mounted\r\n    //document.addEventListener(\"mouseover\", handleMouseOverLeftMenu);\r\n    //document.addEventListener(\"mouseout\", handleMouseOutLeftMenu);\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    // return function to be called when unmounted\r\n    return () => {\r\n      //document.removeEventListener(\"mouseover\", handleMouseOverLeftMenu);\r\n      //document.removeEventListener(\"mouseout\", handleMouseOutLeftMenu);\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const handleResize = () => {\r\n    const currentWidth = window.innerWidth;\r\n    if (currentWidth < 1300) {\r\n      setSidebar(false);\r\n    }\r\n  }\r\n\r\n  const handleMouseOverStatusRobo = (y) => {\r\n    if (\r\n      (menuStatusRoboRef.current &&\r\n        menuStatusRoboRef.current.contains(y.target)) ||\r\n      (menuStatusRoboRef_.current &&\r\n        menuStatusRoboRef_.current.contains(y.target))\r\n    ) {\r\n      setDropStatusRobo(true);\r\n      return;\r\n    }\r\n  };\r\n\r\n  const handleMouseOutStatusRobo = (y) => {\r\n    if (\r\n      (menuStatusRoboRef.current &&\r\n        !menuStatusRoboRef.current.contains(y.target)) ||\r\n      (menuStatusRoboRef_.current &&\r\n        !menuStatusRoboRef_.current.contains(y.target))\r\n    ) {\r\n      setDropStatusRobo(false);\r\n      return;\r\n    }\r\n  };\r\n\r\n  const handleToggleStatusRobo = () => {\r\n    setDropStatusRobo((previous) => !previous);\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <ModalUserImg\r\n        setModalImg={setModalImg}\r\n        showModalImg={showModalImg}\r\n        userID={userID}\r\n      />\r\n\r\n      <ModalEditUser\r\n        setEditUser={setEditUser}\r\n        showEditUser={showEditUser}\r\n        setRefresh={setRefresh}\r\n        selectData={selectData}\r\n        setselectData={setselectData}\r\n        _idUserEdit={_idUserEdit}\r\n        usernameEdit={usernameEdit}\r\n        emailEdit={emailEdit}\r\n        roleEdit={roleEdit}\r\n        editPerfil={true}\r\n      />\r\n\r\n      <ModalLinkCardapio\r\n        showLinkCardapio={showLinkCardapio}\r\n        setShowLinkCardapio={setShowLinkCardapio}\r\n        nomeEmpresaForUrl={nomeEmpresaForUrl}\r\n      />\r\n\r\n      <ModalLinkCardapioSalao\r\n        showLinkCardapioSalao={showLinkCardapioSalao}\r\n        setShowLinkCardapioSalao={setShowLinkCardapioSalao}\r\n        nomeEmpresaForUrl={nomeEmpresaForUrl}\r\n      />\r\n\r\n      <IconContext.Provider value={{ color: \"#fff\" }}>\r\n        <Nav sidebar={sidebar} style={{ justifyContent: \"space-between\" }}>\r\n          <div className=\"d-flex ms-3 align-items-center\">\r\n            <div onClick={showDrawer} className=\"desktop-hidden me-4\" style={{ cursor: \"pointer\" }}>\r\n              <GiHamburgerMenu color=\"black\" />\r\n            </div>\r\n            <div\r\n              className=\"userCircleImg\"\r\n              style={{\r\n                paddingTop: \"4px\",\r\n                paddingBottom: \"2px\",\r\n                display: isMobile ? \"none\" : \"block\",\r\n              }}\r\n            >\r\n              <img src={logoImg} alt=\"pede-ja logo\" width={120} height={40} className=\"logoImg\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"d-flex\">\r\n            {/* Dropdown Menu Status Robo */}\r\n            <div\r\n              ref={menuStatusRoboRef_}\r\n              className={\r\n                dropstatusrobo ? \"menu-dropdownShow\" : \"menu-dropdownClosed\"\r\n              }\r\n            >\r\n              <div className=\"menu-options\">\r\n                <div className=\"option bottom\">\r\n                  <div className=\"info-option\">\r\n                    <div className=\"status-option online\" />\r\n                    <div className=\"label-option\">\r\n                      <pedeja-icon\r\n                        iconname=\"whatsapp\"\r\n                        iconstroke={2}\r\n                        iconcolor=\"#797878\"\r\n                        iconsize={18}\r\n                        _nghost-ng-c3181319476=\"\"\r\n                        style={{ height: 18 }}\r\n                      >\r\n                        <svg\r\n                          width={18}\r\n                          height={18}\r\n                          fill=\"#797878\"\r\n                          viewBox=\"0 0 13 13\"\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                        >\r\n                          <path\r\n                            d=\"M9.477 7.79066C9.31409 7.70819 8.52069 7.32103 8.37241 7.26578C8.22412 7.21297 8.11647 7.18534 8.00841 7.34825C7.90278 7.50709 7.59159 7.87069 7.49653 7.97672C7.40147 8.08275 7.30803 8.09088 7.14756 8.01897C6.98466 7.9365 6.46425 7.76709 5.84634 7.21297C5.36372 6.78356 5.04237 6.25462 4.94691 6.09172C4.85184 5.93084 4.93634 5.83984 5.01678 5.75941C5.09072 5.68547 5.17969 5.57334 5.26216 5.47584C5.34056 5.37834 5.36575 5.31294 5.42303 5.20731C5.47584 5.09316 5.44862 5.00419 5.40841 4.92375C5.36819 4.84331 5.04441 4.04584 4.90912 3.72816C4.77994 3.41291 4.64466 3.45312 4.54512 3.45312C4.45209 3.44459 4.34403 3.44459 4.23638 3.44459C4.12872 3.44459 3.95281 3.48481 3.80453 3.63919C3.65625 3.80209 3.23741 4.19128 3.23741 4.97859C3.23741 5.76794 3.81712 6.53169 3.89756 6.64584C3.98003 6.75147 5.03791 8.37647 6.66087 9.07481C7.04803 9.23772 7.34866 9.33522 7.58347 9.41566C7.97062 9.53834 8.32406 9.52128 8.60316 9.48106C8.91191 9.43028 9.55947 9.08944 9.69516 8.70878C9.83288 8.32569 9.83288 8.00841 9.79266 7.9365C9.75244 7.86256 9.64681 7.82234 9.48391 7.75044L9.477 7.79066ZM6.53372 11.7812H6.52519C5.56441 11.7812 4.61459 11.5208 3.78503 11.0342L3.59044 10.918L1.55919 11.4469L2.10519 9.4705L1.97397 9.26738C1.4375 8.41439 1.1529 7.42722 1.15294 6.41956C1.15294 3.47019 3.56728 1.06437 6.53778 1.06437C7.97672 1.06437 9.32669 1.625 10.3423 2.64062C10.8433 3.13568 11.2407 3.72555 11.5114 4.37582C11.782 5.0261 11.9204 5.72376 11.9186 6.42809C11.9145 9.37544 9.50219 11.7812 6.53575 11.7812H6.53372ZM11.1146 1.86834C9.87878 0.674781 8.25378 0 6.52519 0C2.95994 0 0.056875 2.89047 0.0548438 6.44272C0.0548438 7.57697 0.351 8.68359 0.918125 9.66306L0 13L3.432 12.105C4.38275 12.6176 5.44547 12.8872 6.52559 12.8899H6.52763C10.0949 12.8899 12.998 9.99944 13 6.44475C13 4.72469 12.3293 3.10578 11.1065 1.88906L11.1146 1.86834Z\"\r\n                            fill=\"#797878\"\r\n                          />\r\n                        </svg>\r\n                      </pedeja-icon>\r\n                      <span className=\"description\">Whatsapp</span>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Switch Personalized */}\r\n                  {/*\r\n                    <div className=\"wrapper\">\r\n                        <div className=\"switch_box box_1\">\r\n                          <input type=\"checkbox\" className=\"switch_1\"/>\r\n                        </div>\r\n                    </div>\r\n                    */}\r\n                  <div className=\"wrapper\">\r\n                    <div className=\"switch_box box_1\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        className=\"switch_1\"\r\n                        checked={statusBot}\r\n                        onChange={handleSwitchChange}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  className=\"option bottom-last\"\r\n                  style={{ marginBottom: \"5px\" }}\r\n                >\r\n                  <div className=\"info-option\">\r\n                    <div className=\"status-option online\" />\r\n                    <div\r\n                      className=\"label-option\"\r\n                      style={{ display: \"inline-flex\", alignItems: \"center\" }}\r\n                    >\r\n                      <pedeja-icon\r\n                        iconname=\"users\"\r\n                        iconstroke={2}\r\n                        iconcolor=\"#797878\"\r\n                        iconsize={18}\r\n                        _nghost-ng-c3181319476=\"\"\r\n                        style={{ height: 18 }}\r\n                      >\r\n                        <svg\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          width={18}\r\n                          height={18}\r\n                          viewBox=\"0 0 24 24\"\r\n                          fill=\"none\"\r\n                          stroke=\"#797878\"\r\n                          strokeWidth={2}\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          className=\"feather feather-users\"\r\n                        >\r\n                          <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\" />\r\n                          <circle cx={9} cy={7} r={4} />\r\n                          <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\" />\r\n                          <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                        </svg>\r\n                      </pedeja-icon>\r\n                      <div style={{ display: \"inline-grid\", lineHeight: \"17px\" }}>\r\n                        <span className=\"description\">Chamar</span>\r\n                        <span className=\"description\">Atendente</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  {/* Switch Personalized */}\r\n                  <div className=\"wrapper\">\r\n                    <div className=\"switch_box box_1\">\r\n                      <input type=\"checkbox\" className=\"switch_1\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              {/**/}\r\n            </div>\r\n\r\n            {isDevelopment && !isMobile ?\r\n              <button className=\"atendimento-teste-btn\" onClick={simularAtendimento}>\r\n                🚀 Simular Atendimento\r\n              </button>\r\n              :\r\n              undefined\r\n            }\r\n\r\n            {/* Fim Dropdown Menu Status Robo */}\r\n            <div\r\n              style={{\r\n                padding: \"5px 24px 5px 24px\",\r\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\r\n                height: 80,\r\n                flexDirection: \"column\",\r\n                justifyContent: \"center\",\r\n                alignItems: \"center\",\r\n                gap: \"2px\",\r\n                display: \"inline-flex\",\r\n              }}\r\n              ref={menuStatusRoboRef}\r\n              onClick={handleToggleStatusRobo}\r\n            //dropstatusrobo={dropstatusrobo}\r\n            //className={dropstatusrobo ? 'open' : 'closed'}\r\n            >\r\n              <div className=\"hidden-mobile\">\r\n                <span style={{ fontSize: 15, fontWeight: 500, marginLeft: 5 }}>\r\n                  Status\r\n                </span>\r\n              </div>\r\n              <FaRobot style={{ color: \"black\", fontSize: 24 }} />\r\n              <div\r\n                className=\"hidden-mobile\"\r\n                style={{\r\n                  fontWeight: \"bold\",\r\n                  display: \"flex\",\r\n                  justifyContent: \"center\",\r\n                  fontSize: 15,\r\n                  position: \"relative\"\r\n                }}\r\n              >\r\n                <span>Robo Pede Já</span>\r\n                <RiArrowDropDownLine style={{ color: \"black\", fontSize: 24 }} />\r\n              </div>\r\n            </div>\r\n\r\n\r\n            <Tooltip\r\n              placement=\"bottom\"\r\n              title={`Impressora: ${statusPrinter === \"Offline\" ? 'Offline' : 'Conectada'}`}\r\n            >\r\n              <div style={{\r\n                padding: \"5px 24px 5px 24px\",\r\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\r\n                height: 80,\r\n                flexDirection: \"row\",\r\n                justifyContent: \"center\",\r\n                alignItems: \"center\",\r\n                gap: \"5px\",\r\n                display: \"inline-flex\",\r\n              }}>\r\n                <ImPrinter color={statusPrinter === \"Offline\" ? 'red' : '#07c670'} fontSize={30} />\r\n                <div className=\"hidden-mobile\">\r\n                  <div>\r\n                    <span style={{ fontSize: 15, fontWeight: 500, marginLeft: 5 }}>\r\n                      Impressora\r\n                    </span>\r\n                  </div>\r\n                  {statusPrinter && statusPrinter === \"Offline\" ? (\r\n                    <div className=\"divStatusPrintNavBarOffline\">\r\n                      <span>Offline</span>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"divStatusPrintNavBar\">\r\n                      <span>Conectada</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </Tooltip>\r\n            <Tooltip\r\n              placement=\"bottom\"\r\n              title={`Cardápio Delivery`}\r\n            >\r\n              <div\r\n                style={{\r\n                  padding: \"5px 24px\",\r\n                  borderLeft: \"1px solid rgb(208, 209, 209)\",\r\n                  height: 80,\r\n                  flexDirection: \"column\",\r\n                  justifyContent: \"center\",\r\n                  alignItems: \"center\",\r\n                  gap: \"2px\",\r\n                  display: \"inline-flex\",\r\n                }}\r\n              >\r\n                <div\r\n                  onClick={() => setShowLinkCardapio(true)}\r\n                  style={{\r\n                    cursor: \"pointer\",\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                  }}\r\n                >\r\n                  <img src={entregadorIco} height={30} width={30} />\r\n                  <div className=\"d-flex flex-column hidden-mobile\">\r\n                    <span style={{ fontSize: 14, fontWeight: 400, marginLeft: 5 }}>\r\n                      Link\r\n                    </span>\r\n                    <span style={{ fontSize: 14, fontWeight: 700, marginLeft: 5 }}>\r\n                      Cardápio Delivery\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </Tooltip>\r\n\r\n            <Tooltip\r\n              placement=\"bottom\"\r\n              title={`Cardápio Salão`}\r\n            >\r\n              <div\r\n                style={{\r\n                  padding: \"5px 24px\",\r\n                  borderLeft: \"1px solid rgb(208, 209, 209)\",\r\n                  height: 80,\r\n                  flexDirection: \"column\",\r\n                  justifyContent: \"center\",\r\n                  alignItems: \"center\",\r\n                  gap: \"2px\",\r\n                  display: \"inline-flex\",\r\n                }}\r\n              >\r\n                <div\r\n                  onClick={() => setShowLinkCardapioSalao(true)}\r\n                  style={{\r\n                    cursor: \"pointer\",\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                  }}\r\n                >\r\n                  <img src={roundTable} height={30} width={30} />\r\n                  <div className=\"d-flex flex-column hidden-mobile\">\r\n                    <span style={{ fontSize: 14, fontWeight: 400, marginLeft: 5 }}>\r\n                      Link\r\n                    </span>\r\n                    <span style={{ fontSize: 14, fontWeight: 700, marginLeft: 5 }}>\r\n                      Cardápio Salão\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </Tooltip>\r\n\r\n            <div\r\n              style={{\r\n                padding: \"5px 24px 5px 24px\",\r\n                height: 80,\r\n                flexDirection: \"column\",\r\n                justifyContent: \"center\",\r\n                alignItems: \"center\",\r\n                gap: \"2px\",\r\n                display: \"inline-flex\",\r\n                borderLeft: \"1px solid rgb(208, 209, 209)\"\r\n              }}\r\n              className=\"hidden-sm-mobile\"\r\n            >\r\n              <div>\r\n                <MdStorefront style={{ color: \"black\", fontSize: 20 }} />\r\n                <span style={{ fontSize: 15, fontWeight: 500, marginLeft: 5 }}>\r\n                  Loja\r\n                  {!statusLojaTemporario ? (\r\n                    <div\r\n                      style={{\r\n                        width: 70,\r\n                        height: 18,\r\n                        borderRadius: 10,\r\n                        marginLeft: 10,\r\n                        background: \"#9CE8C6\",\r\n                        justifyContent: \"center\",\r\n                        alignItems: \"center\",\r\n                        gap: 8,\r\n                        display: \"inline-flex\",\r\n                      }}\r\n                    >\r\n                      <span>Aberta</span>\r\n                    </div>\r\n                  ) : (\r\n                    <div\r\n                      style={{\r\n                        width: 80,\r\n                        height: 18,\r\n                        borderRadius: 10,\r\n                        marginLeft: 10,\r\n                        background: \"#ff0000b5\",\r\n                        justifyContent: \"center\",\r\n                        alignItems: \"center\",\r\n                        gap: 8,\r\n                        color: \"white\",\r\n                        fontSize: 13,\r\n                        display: \"inline-flex\",\r\n                      }}\r\n                    >\r\n                      <span>Fechada</span>\r\n                    </div>\r\n                  )}\r\n                </span>\r\n              </div>\r\n              <div\r\n                style={{\r\n                  fontWeight: \"bold\",\r\n                  display: \"flex\",\r\n                  justifyContent: \"center\",\r\n                  fontSize: 15,\r\n                }}\r\n              >\r\n                <span>{nomeEmpresa}</span>\r\n              </div>\r\n              {statusLojaTemporario ? (\r\n                <div\r\n                  type=\"button\"\r\n                  onClick={() => handleOpenCloseLoja()}\r\n                  style={{\r\n                    width: 150,\r\n                    height: 20,\r\n                    borderRadius: 5,\r\n                    background: \"#318CD5\",\r\n                    justifyContent: \"center\",\r\n                    alignItems: \"center\",\r\n                    gap: 8,\r\n                    display: \"inline-flex\",\r\n                  }}\r\n                >\r\n                  <a style={{ color: \"white\", fontSize: 10 }}>Abrir Loja</a>\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  type=\"button\"\r\n                  onClick={() => handleOpenCloseLoja()}\r\n                  style={{\r\n                    width: 150,\r\n                    height: 20,\r\n                    borderRadius: 5,\r\n                    background: \"#318CD5\",\r\n                    justifyContent: \"center\",\r\n                    alignItems: \"center\",\r\n                    gap: 8,\r\n                    display: \"inline-flex\",\r\n                  }}\r\n                >\r\n                  <a style={{ color: \"white\", fontSize: 10 }}>Fechar Loja</a>\r\n                </div>\r\n              )}\r\n            </div>\r\n            <div className=\"hidden-mobile\" style={{ width: 128, height: 80, paddingLeft: 24, paddingRight: 24, borderLeft: '1px #D0D1D1 solid', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'inline-flex' }}>\r\n              <div onClick={handleLogout} style={{ textAlign: 'center', cursor: \"pointer\", color: '#001B30', fontSize: 18, fontWeight: '700', letterSpacing: 0.48, wordWrap: 'break-word' }}>\r\n                <MdExitToApp style={{ color: 'black', fontSize: 25 }} />Sair\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Nav>\r\n        <Drawer title=\"Navegação\" onClose={onClose} open={open} key=\"left\" placement=\"left\" bodyStyle={{ padding: 0 }}>\r\n          <div className=\"d-flex flex-column justify-content-between h-100\">\r\n            <div\r\n              className=\"boxLeftMenuScroll\"\r\n              style={{ overflowY: \"scroll\", height: \"calc(100vh - 120px)\" }}\r\n            >\r\n              {SidebarData.map((item, index) => {\r\n                return (\r\n                  <PermissionGate\r\n                    key={index}\r\n                    permissions={[SidebarData[index].permission]}\r\n                  >\r\n                    <SubMenu\r\n                      item={item}\r\n                      key={index}\r\n                      style={{ background: \"black\" }}\r\n                    />\r\n                  </PermissionGate>\r\n                );\r\n              })}\r\n            </div>\r\n            <div className=\"d-flex flex-column\">\r\n              <div\r\n                style={{\r\n                  padding: \"5px 24px 5px 24px\",\r\n                  borderLeft: \"1px solid rgb(208, 209, 209)\",\r\n                  height: 80,\r\n                  flexDirection: \"column\",\r\n                  justifyContent: \"center\",\r\n                  alignItems: \"center\",\r\n                  gap: \"2px\",\r\n                  display: \"inline-flex\",\r\n                }}\r\n              >\r\n                <div>\r\n                  <MdStorefront style={{ color: \"black\", fontSize: 20 }} />\r\n                  <span style={{ fontSize: 15, fontWeight: 500, marginLeft: 5 }}>\r\n                    Loja\r\n                    {!statusLojaTemporario ? (\r\n                      <div\r\n                        style={{\r\n                          width: 70,\r\n                          height: 18,\r\n                          borderRadius: 10,\r\n                          marginLeft: 10,\r\n                          background: \"#9CE8C6\",\r\n                          justifyContent: \"center\",\r\n                          alignItems: \"center\",\r\n                          gap: 8,\r\n                          display: \"inline-flex\",\r\n                        }}\r\n                      >\r\n                        <span>Aberta</span>\r\n                      </div>\r\n                    ) : (\r\n                      <div\r\n                        style={{\r\n                          width: 80,\r\n                          height: 18,\r\n                          borderRadius: 10,\r\n                          marginLeft: 10,\r\n                          background: \"#ff0000b5\",\r\n                          justifyContent: \"center\",\r\n                          alignItems: \"center\",\r\n                          gap: 8,\r\n                          color: \"white\",\r\n                          fontSize: 13,\r\n                          display: \"inline-flex\",\r\n                        }}\r\n                      >\r\n                        <span>Fechada</span>\r\n                      </div>\r\n                    )}\r\n                  </span>\r\n                </div>\r\n                <div\r\n                  style={{\r\n                    fontWeight: \"bold\",\r\n                    display: \"flex\",\r\n                    justifyContent: \"center\",\r\n                    fontSize: 15,\r\n                  }}\r\n                >\r\n                  <span>{nomeEmpresa}</span>\r\n                </div>\r\n                {statusLojaTemporario ? (\r\n                  <div\r\n                    type=\"button\"\r\n                    onClick={() => handleOpenCloseLoja()}\r\n                    style={{\r\n                      width: 150,\r\n                      height: 20,\r\n                      borderRadius: 5,\r\n                      background: \"#318CD5\",\r\n                      justifyContent: \"center\",\r\n                      alignItems: \"center\",\r\n                      gap: 8,\r\n                      display: \"inline-flex\",\r\n                    }}\r\n                  >\r\n                    <a style={{ color: \"white\", fontSize: 10 }}>Abrir Loja</a>\r\n                  </div>\r\n                ) : (\r\n                  <div\r\n                    type=\"button\"\r\n                    onClick={() => handleOpenCloseLoja()}\r\n                    style={{\r\n                      width: 150,\r\n                      height: 20,\r\n                      borderRadius: 5,\r\n                      background: \"#318CD5\",\r\n                      justifyContent: \"center\",\r\n                      alignItems: \"center\",\r\n                      gap: 8,\r\n                      display: \"inline-flex\",\r\n                    }}\r\n                  >\r\n                    <a style={{ color: \"white\", fontSize: 10 }}>Fechar Loja</a>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <div className=\"hidden-mobile\" style={{ width: 128, height: 80, paddingLeft: 24, paddingRight: 24, borderLeft: '1px #D0D1D1 solid', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'inline-flex' }}>\r\n                <div onClick={handleLogout} style={{ textAlign: 'center', cursor: \"pointer\", color: '#001B30', fontSize: 18, fontWeight: '700', letterSpacing: 0.48, wordWrap: 'break-word' }}>\r\n                  <MdExitToApp style={{ color: 'black', fontSize: 25 }} />Sair\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Drawer>\r\n        {(!planType || planType === \"\" || daysToExpire == 0) && diasTesteRestantes > 0 ?\r\n          !semCobrancaOuInvoice && !carregandoCheckLicense ?\r\n            <div className=\"info-licenca\">\r\n              <span>Dias de teste restantes: {diasTesteRestantes} dias</span>\r\n            </div>\r\n            :\r\n            null\r\n          : null}\r\n        {planType && planType !== \"free_trial\" && daysToExpire <= 3 && diferencaEmDias > 7 ? (\r\n          <div className=\"info-licenca\">\r\n            {daysToExpire <= 0 ? (\r\n              <span>\r\n                Hoje seu plano irá expirar, para realizar o pagamento da fatura{\" \"}\r\n                <span\r\n                  className=\"clickNavigateToPlanos\"\r\n                  onClick={() => navigate(\"/planos\")}\r\n                >\r\n                  CLIQUE AQUI\r\n                </span>\r\n              </span>\r\n            ) : (\r\n              <span>\r\n                Faltam apenas {daysToExpire} dias para sua assinatura expirar\r\n              </span>\r\n            )}\r\n          </div>\r\n        ) : null}\r\n\r\n        <MenuAjuda>\r\n          <div className={atendimentosPendentes.length > 0 ? \"item-menu-help-info-active\" : \"item-menu-help-info\"}\r\n            onClick={() => setModalOpen(true)}\r\n          >\r\n            <svg\r\n              width={30}\r\n              height={30}\r\n              viewBox=\"0 0 30 28\"\r\n              fill=\"none\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              id={atendimentosPendentes.length > 0 ? \"shaking\" : undefined}\r\n            >\r\n              <path\r\n                d=\"M28.9167 24.4441V22.2775C28.916 21.3173 28.5965 20.3846 28.0082 19.6258C27.42 18.867 26.5964 18.325 25.6667 18.085\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"1.3\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n              />\r\n              <path\r\n                d=\"M22.4166 24.4443V22.2777C22.4166 21.1284 21.96 20.0262 21.1474 19.2135C20.3347 18.4009 19.2325 17.9443 18.0833 17.9443H9.41658C8.26731 17.9443 7.16511 18.4009 6.35246 19.2135C5.5398 20.0262 5.08325 21.1284 5.08325 22.2777V24.4443\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"1.3\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n              />\r\n              <path\r\n                d=\"M21.3333 5.08496C22.2654 5.32362 23.0915 5.86572 23.6815 6.6258C24.2715 7.38587 24.5917 8.32069 24.5917 9.28288C24.5917 10.2451 24.2715 11.1799 23.6815 11.94C23.0915 12.7 22.2654 13.2421 21.3333 13.4808\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"1.3\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n              />\r\n              <path\r\n                d=\"M13.7501 13.611C16.1433 13.611 18.0834 11.6709 18.0834 9.27767C18.0834 6.88444 16.1433 4.94434 13.7501 4.94434C11.3568 4.94434 9.41675 6.88444 9.41675 9.27767C9.41675 11.6709 11.3568 13.611 13.7501 13.611Z\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"1.3\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n              />\r\n              <path\r\n                d=\"M8.02771 1.81393C6.65952 2.40269 4.00254 4.64734 4.32012 8.9159M3.20673 1.3753C2.33013 2.1057 1.18146 3.37006 1.36262 5.9846\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"1.3\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n              />\r\n            </svg>\r\n            {atendimentosPendentes.length > 0 ? (\r\n              <div className=\"number-box number-box--active\">\r\n                {atendimentosPendentes.length}\r\n              </div>\r\n            ) : undefined}\r\n          </div>\r\n        </MenuAjuda>\r\n        {/* 📌 Modal de Atendimentos Pendentes */}\r\n        <AtendimentoModal\r\n          atendimentosPendentes={atendimentosPendentes}\r\n          removerAtendimento={removerAtendimento}\r\n          modalOpen={modalOpen}\r\n          setModalOpen={setModalOpen}\r\n        />\r\n\r\n        <CrispChat />\r\n\r\n        {/*<HelpWidget />*/}\r\n\r\n\r\n        <SidebarNav\r\n          ref={leftMenuRef}\r\n          sidebar={sidebar}\r\n          className={sidebar ? \"open\" : \"closed\"}\r\n          style={{ boxShadow: \"1px 1px 6px lightgray\" }}\r\n        >\r\n          <SidebarWrap>\r\n            <div className=\"collapseDiv\">\r\n              {sidebar ? (\r\n                <RiMenuFoldFill\r\n                  onClick={toggleSidebar}\r\n                  fill=\"gray\"\r\n                  className=\"collapseInLeftMenuBtn\"\r\n                />\r\n              ) : (\r\n                <RiMenuUnfoldFill\r\n                  onClick={toggleSidebar}\r\n                  fill=\"gray\"\r\n                  className=\"collapseOutLeftMenuBtn\"\r\n                />\r\n              )}\r\n            </div>\r\n\r\n            {sidebar ? (\r\n              <div\r\n                style={{\r\n                  borderBottom: \"3px solid #4281FF\",\r\n                  backgroundImage: `url(${userDiv})` /*background:\"rgba(0,0,0,0.8)\"*/,\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"userCircleImg\"\r\n                  style={{ paddingTop: \"4px\", paddingBottom: \"2px\" }}\r\n                >\r\n                  {/*<HiIcons.HiUserCircle style={{color:\"rgb(200,200,200)\", fontSize:\"60px\", padding:\"4px\"}}/>\r\n                <img style={{backgroundImage:`url(${userImg})`}}/>*/}\r\n                  <img\r\n                    src={\r\n                      userImg !== null && userImg.length > 0\r\n                        ? userImg\r\n                        : defaultUserImg\r\n                    }\r\n                    width={60}\r\n                    height={60}\r\n                    className=\"userImg\"\r\n                  />\r\n                </div>\r\n                <NavIcon /*to='#'*/ style={{ marginLeft: \"30px\" }}>\r\n                  <label\r\n                    style={{\r\n                      maxWidth: \"80%\",\r\n                      overflow: \"hidden\",\r\n                      whiteSpace: \"nowrap\",\r\n                    }}\r\n                  >\r\n                    {userName}\r\n                  </label>\r\n                  <div ref={userOptionsRef}>\r\n                    <HiIcons.HiOutlineDotsVertical\r\n                      onClick={showUserOptions}\r\n                      style={{\r\n                        color: \"white\",\r\n                        cursor: \"pointer\",\r\n                        marginLeft: \"10px\",\r\n                        fontSize: \"22px\",\r\n                      }}\r\n                    />\r\n                    <ModalUserOptions showOptions={showOptions}>\r\n                      <li\r\n                        onClick={(e) => handleEdit(userID)}\r\n                        style={{ cursor: \"pointer\" }}\r\n                      >\r\n                        <a>Editar Perfil</a>\r\n                      </li>\r\n                      <li\r\n                        onClick={showModalAddUserImg}\r\n                        style={{ cursor: \"pointer\" }}\r\n                      >\r\n                        <a>Editar Imagem</a>\r\n                      </li>\r\n                      <li onClick={handleLogout} style={{ cursor: \"pointer\" }}>\r\n                        <a>Sair</a>\r\n                      </li>\r\n                    </ModalUserOptions>\r\n                  </div>\r\n                </NavIcon>\r\n                <div\r\n                  style={{\r\n                    paddingBottom: \"2px\",\r\n                    justifyContent: \"center\",\r\n                    display: \"flex\",\r\n                    fontSize: \"12px\",\r\n                    color: \"white\",\r\n                  }}\r\n                >\r\n                  {userEmail}\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div\r\n                style={{\r\n                  boxShadow: \"-1px 1px 1px 0px #0000001c\",\r\n                  borderBottom: \"3px solid transparent\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"userCircleImg\"\r\n                  style={{\r\n                    paddingTop: 15,\r\n                    paddingBottom: 5,\r\n                    paddingRight: 3,\r\n                    paddingBottom: 11,\r\n                  }}\r\n                >\r\n                  {/*<HiIcons.HiUserCircle style={{color:\"rgb(180,180,180)\", fontSize:\"108px\", padding:\"4px\"}}/>*/}\r\n                  <img\r\n                    src={\r\n                      userImg !== null && userImg.length > 0\r\n                        ? userImg\r\n                        : defaultUserImg\r\n                    }\r\n                    width={80}\r\n                    height={80}\r\n                    className=\"userImg\"\r\n                    style={{ padding: \"0px\" }}\r\n                  />\r\n                </div>\r\n                <NavIcon\r\n                  to=\"#\"\r\n                  style={{\r\n                    /*display:\"none\",*/ fontSize: \"0px\",\r\n                    height: \"0px\" /*visibility:\"hidden\"*/,\r\n                  }}\r\n                >\r\n                  <label\r\n                    style={{\r\n                      maxWidth: \"80%\",\r\n                      overflow: \"hidden\",\r\n                      whiteSpace: \"nowrap\",\r\n                    }}\r\n                  >\r\n                    {userName}\r\n                  </label>\r\n                  <div ref={userOptionsRef} style={{ display: \"none\" }} />\r\n                </NavIcon>\r\n                <div\r\n                  style={{\r\n                    paddingBottom: \"10px\",\r\n                    justifyContent: \"center\",\r\n                    display: \"flex\",\r\n                    fontSize: \"12px\",\r\n                    display: \"none\",\r\n                  }}\r\n                >\r\n                  {userEmail}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div\r\n              className=\"boxLeftMenuScroll\"\r\n              style={{ overflowY: \"scroll\", height: \"calc(100vh - 120px)\" }}\r\n            >\r\n              {/* Componente \"Comece por aqui\" */}\r\n              {mostrarComecePorAqui && (\r\n                <div \r\n                  className=\"comece-por-aqui-container\"\r\n                  onClick={() => navigate('/configuracao-inicial')}\r\n                  style={{\r\n                    background: 'linear-gradient(135deg, #4299e1 0%, #667eea 100%)',\r\n                    margin: '10px',\r\n                    padding: '16px',\r\n                    borderRadius: '12px',\r\n                    cursor: 'pointer',\r\n                    color: 'white',\r\n                    transition: 'all 0.3s ease',\r\n                    border: '2px solid transparent',\r\n                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.target.style.transform = 'translateY(-2px)';\r\n                    e.target.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.15)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.target.style.transform = 'translateY(0)';\r\n                    e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';\r\n                  }}\r\n                >\r\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\r\n                    <div style={{\r\n                      background: 'rgba(255, 255, 255, 0.2)',\r\n                      borderRadius: '8px',\r\n                      padding: '8px',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      minWidth: '32px',\r\n                      height: '32px',\r\n                    }}>\r\n                      <FaArrowRight style={{ fontSize: '16px' }} />\r\n                    </div>\r\n                    <div style={{ flex: 1 }}>\r\n                      <div style={{ \r\n                        fontSize: '16px', \r\n                        fontWeight: '600', \r\n                        marginBottom: '4px',\r\n                        display: sidebar ? 'block' : 'none'\r\n                      }}>\r\n                        Comece por aqui\r\n                      </div>\r\n                      <div style={{ \r\n                        fontSize: '12px', \r\n                        opacity: '0.9',\r\n                        display: sidebar ? 'block' : 'none'\r\n                      }}>\r\n                        {totalEtapasConfiguracaoInicial - progressoConfiguracaoInicial} etapas restantes\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {/* Barra de progresso */}\r\n                  <div style={{ \r\n                    marginTop: '12px',\r\n                    display: sidebar ? 'block' : 'none'\r\n                  }}>\r\n                    <div style={{\r\n                      background: 'rgba(255, 255, 255, 0.2)',\r\n                      borderRadius: '8px',\r\n                      height: '6px',\r\n                      overflow: 'hidden',\r\n                    }}>\r\n                      <div style={{\r\n                        background: 'white',\r\n                        height: '100%',\r\n                        borderRadius: '8px',\r\n                        width: `${(progressoConfiguracaoInicial / totalEtapasConfiguracaoInicial) * 100}%`,\r\n                        transition: 'width 0.3s ease',\r\n                      }} />\r\n                    </div>\r\n                    <div style={{\r\n                      display: 'flex',\r\n                      justifyContent: 'space-between',\r\n                      alignItems: 'center',\r\n                      marginTop: '6px',\r\n                    }}>\r\n                      <span style={{ fontSize: '11px', opacity: '0.8' }}>\r\n                        {progressoConfiguracaoInicial}/{totalEtapasConfiguracaoInicial} concluídas\r\n                      </span>\r\n                      <span style={{ fontSize: '11px', opacity: '0.8' }}>\r\n                        {Math.round((progressoConfiguracaoInicial / totalEtapasConfiguracaoInicial) * 100)}%\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n              \r\n              {SidebarData.map((item, index) => {\r\n                return (\r\n                  <PermissionGate\r\n                    key={index}\r\n                    permissions={[SidebarData[index].permission]}\r\n                  >\r\n                    <SubMenu\r\n                      item={item}\r\n                      key={index}\r\n                      style={{ background: \"black\" }}\r\n                    />\r\n                  </PermissionGate>\r\n                );\r\n              })}\r\n            </div>\r\n          </SidebarWrap>\r\n        </SidebarNav>\r\n      </IconContext.Provider>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default LeftMenu;\r\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACtE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAO,aAAa;AACpB,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,OAAO,EAAEC,OAAO,EAAEC,YAAY,QAAQ,gBAAgB;AAC/D,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM,CAAC,CAAC;AACrC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SACEC,OAAO,EACPC,iBAAiB,EACjBC,sBAAsB,EACtBC,gBAAgB,EAChBC,sBAAsB,EACtBC,mBAAmB,QACd,oBAAoB;AAC3B,SAASC,OAAO,EAAEC,MAAM,QAAQ,MAAM;AACtC,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,GAAG,GAAG9C,MAAM,CAAC+C,GAAG;AACtB;AACA;AACA,UAAU,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AACxD;AACA;AACA,WAAW,CAAC;EAAEA;AAAQ,CAAC,KACnBA,OAAO,GAAG,oBAAoB,GAAG,oBAAoB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GAtBMH,GAAG;AAuBT,MAAMI,OAAO,GAAGlD,MAAM,CAAC+C,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACI,GAAA,GAVID,OAAO;AAYb,MAAME,UAAU,GAAGpD,MAAM,CAACqD,GAAG;AAC7B;AACA,WAAW,CAAC;EAAEL;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;EAAEA;AAAQ,CAAC,KAAMA,OAAO,GAAG,SAAS,GAAG,QAAS;AACnE,eAAe,CAAC;EAAEA;AAAQ,CAAC,KAAMA,OAAO,GAAG,GAAG,GAAG,GAAI;AACrD,iBAAiB,CAAC;EAAEA;AAAQ,CAAC,KAAMA,OAAO,GAAG,MAAM,GAAG,KAAM;AAC5D;AACA,QAAQ,CAAC;EAAEA;AAAQ,CAAC,KAChBA,OAAO,GAAG,wBAAwB,GAAG,wBAAwB;AACjE;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GA3BIF,UAAU;AA6BhB,MAAMG,gBAAgB,GAAGvD,MAAM,CAAC+C,GAAG;AACnC;AACA;AACA;AACA;AACA,aAAa,CAAC;EAAES;AAAY,CAAC,KAAMA,WAAW,GAAG,MAAM,GAAG,EAAG;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXAC,GAAA,GApCMF,gBAAgB;AAiDtB,MAAMG,WAAW,GAAG1D,MAAM,CAAC+C,GAAG;AAC9B;AACA,CAAC;AAACY,GAAA,GAFID,WAAW;AAIjB,MAAME,SAAS,GAAG5D,MAAM,CAAC+C,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAdID,SAAS;AAgBf,MAAME,mBAAmB,GAAG9D,MAAM,CAAC+C,GAAG;AACtC;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GALID,mBAAmB;AAOzB,MAAME,UAAU,GAAGhE,MAAM,CAACiE,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,UAAU;AAiBhB,MAAMG,SAAS,GAAGnE,MAAM,CAAC+C,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,CAAC;EAAEqB;AAAK,CAAC,KAAMA,IAAI,GAAG,OAAO,GAAG,MAAO;AACpD,CAAC;AAACC,GAAA,GAXIF,SAAS;AAaf,MAAMG,aAAa,GAAGtE,MAAM,CAACiE,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAM,GAAA,GAhBMD,aAAa;AAiBnB,SAASE,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjF,KAAK,CAACI,QAAQ,CAAC8E,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EACxEnF,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,MAAMiF,QAAQ,GAAGA,CAAA,KAAMH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5DD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAAC;IAC3C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;EAC7D,CAAC,EAAE,EAAE,CAAC;EACN,OAAOJ,QAAQ;AACjB;AAACD,EAAA,CARQD,WAAW;AAUpB,MAAMS,UAAU,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMuF,kBAAkB,GAAGA,CAAA,KAAM;IAC/BT,MAAM,CAACU,IAAI,CAAC,kFAAkF,EAAE,QAAQ,CAAC;EAC3G,CAAC;EAED,oBACE3C,OAAA,CAACmB,mBAAmB;IAAAyB,QAAA,gBAClB5C,OAAA,CAACqB,UAAU;MAACwB,OAAO,EAAEA,CAAA,KAAMJ,cAAc,CAAC,CAACD,WAAW,CAAE;MAAAI,QAAA,EAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACvEjD,OAAA,CAACwB,SAAS;MAACC,IAAI,EAAEe,WAAY;MAAAI,QAAA,gBAC3B5C,OAAA;QAAKkD,SAAS,EAAC,kBAAkB;QAACL,OAAO,EAAEA,CAAA,KAAMJ,cAAc,CAAC,CAACD,WAAW,CAAE;QAAAI,QAAA,eAC5E5C,OAAA;UACEmD,QAAQ,EAAC,GAAG;UACZC,SAAS,EAAC,SAAS;UACnBC,QAAQ,EAAE,EAAG;UACbH,SAAS,EAAC,YAAY;UACtBI,KAAK,EAAE;YAAEC,MAAM,EAAE,EAAE;YAAEC,OAAO,EAAE;UAAO,CAAE;UAAAZ,QAAA,eAEvC5C,OAAA;YACEyD,KAAK,EAAC,4BAA4B;YAClCC,KAAK,EAAE,EAAG;YACVH,MAAM,EAAE,EAAG;YACXI,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,SAAS;YAChBC,WAAW,EAAE,CAAE;YACfC,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC,OAAO;YACtBd,SAAS,EAAC,mBAAmB;YAAAN,QAAA,gBAE7B5C,OAAA;cAAMiE,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtCjD,OAAA;cAAMiE,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNjD,OAAA;QAAA4C,QAAA,EAAG;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC1BjD,OAAA,CAAC2B,aAAa;QAACkB,OAAO,EAAEH,kBAAmB;QAAAE,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAE1B,CAAC;AAACV,GAAA,CAzCID,UAAU;AAAA+B,GAAA,GAAV/B,UAAU;AA2ChB,MAAMgC,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGxH,UAAU,CAACqC,WAAW,CAAC;EACxCnC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+E,MAAM,CAACwC,MAAM,EAAE;MAClBxC,MAAM,CAACwC,MAAM,GAAG,EAAE;MAClBxC,MAAM,CAACyC,gBAAgB,GAAG,sCAAsC;MAEhE,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,GAAG,GAAG,gCAAgC;MAC7CH,MAAM,CAACI,KAAK,GAAG,IAAI;MACnBH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,MAAM,CAAC;MAEjCA,MAAM,CAACO,MAAM,GAAG,MAAM;QACpBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;;QAErC;QACAnD,MAAM,CAACwC,MAAM,CAACY,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;;QAE/D;QACA,MAAMC,WAAW,GAAGV,QAAQ,CAACW,aAAa,CAAC,eAAe,CAAC;QAC3D,IAAID,WAAW,EAAE;UACfA,WAAW,CAAChC,KAAK,CAACkC,MAAM,GAAG,IAAI;QACjC;;QAEA;QACA,IAAIhB,IAAI,EAAE;UACRW,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEZ,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAACkB,KAAK,CAAC;UACpEzD,MAAM,CAACwC,MAAM,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,eAAe,EAAE,CAACb,IAAI,CAACiB,IAAI,CAAC,CAAC,CAAC;UACzDxD,MAAM,CAACwC,MAAM,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,YAAY,EAAE,CAACb,IAAI,CAACkB,KAAK,CAAC,CAAC,CAAC;QACzD;MACF,CAAC;IACH;EAEF,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO,IAAI,CAAC,CAAC;AACf,CAAC;AAACnB,GAAA,CApCID,SAAS;AAAAqB,IAAA,GAATrB,SAAS;AAsCf,MAAMsB,QAAQ,GAAGA,CAAC;AAAA,KAA+B;EAAAC,GAAA;EAAA,IAAAC,mBAAA;EAC/C,MAAM;IAAEzF,OAAO;IAAE0F;EAAW,CAAC,GAAG/I,UAAU,CAACoC,cAAc,CAAC;EAC1D,MAAM4G,SAAS,GAAG/I,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACgJ,YAAY,EAAEC,WAAW,CAAC,GAAG/I,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACgJ,WAAW,EAAEC,cAAc,CAAC,GAAGjJ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkJ,YAAY,EAAEC,eAAe,CAAC,GAAGnJ,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoJ,SAAS,EAAEC,YAAY,CAAC,GAAGrJ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsJ,QAAQ,EAAEC,WAAW,CAAC,GAAGvJ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwJ,OAAO,EAAEC,UAAU,CAAC,GAAGzJ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwF,IAAI,EAAEkE,OAAO,CAAC,GAAG1J,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAAC2J,SAAS,EAAEC,YAAY,CAAC,GAAG5J,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM4E,QAAQ,GAAGF,WAAW,CAAC,CAAC;EAE9B,MAAMmF,UAAU,GAAGA,CAAA,KAAM;IACvBH,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMI,OAAO,GAAGA,CAAA,KAAM;IACpBJ,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAED,MAAMK,YAAY,GAAG;IACnBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnK,QAAQ,CAAC+J,YAAY,CAAC;EAC1D,MAAMK,QAAQ,GAAGnK,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACoK,cAAc,EAAEC,iBAAiB,CAAC,GAAGtK,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuK,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxK,QAAQ,CAAC,KAAK,CAAC;EAEvE,MAAM;IAAEyK;EAAO,CAAC,GAAG5K,UAAU,CAACqC,WAAW,CAAC;EAC1C,MAAMwI,SAAS,GACb,uFAAuF;EAEzF,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMxD,IAAI,GAAGnG,QAAQ,CAAC4J,GAAG,CAACC,OAAO,CAACJ,aAAa,EAAED,SAAS,CAAC,CAACM,QAAQ,CAClE9J,QAAQ,CAAC+J,GAAG,CAACC,IACf,CAAC;EACD,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAChE,IAAI,CAAC;EAClC;EACA;EACA,MAAMiE,MAAM,GAAGH,SAAS,CAACI,GAAG;EAC5B,MAAMC,QAAQ,GAAGL,SAAS,CAAC7C,IAAI;EAC/B,MAAMmD,SAAS,GAAGN,SAAS,CAAC5C,KAAK;EACjC,IAAImD,OAAO,GAAG,IAAI;EAClB,IAAI,EAAA/C,mBAAA,GAAAwC,SAAS,CAACQ,QAAQ,cAAAhD,mBAAA,uBAAlBA,mBAAA,CAAoBiD,MAAM,IAAG,CAAC,EAAE;IAClCF,OAAO,GAAGP,SAAS,CAACQ,QAAQ,CAAC,CAAC,CAAC;EACjC;EAEA,MAAME,OAAO,GAAGjB,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMiB,YAAY,GAAGV,IAAI,CAACC,KAAK,CAACQ,OAAO,CAAC;EACxC,MAAME,SAAS,GAAGD,YAAY,CAACE,UAAU;EACzC,MAAMC,YAAY,GAAGH,YAAY,CAACP,GAAG;EACrCvD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;IACxC8D,SAAS,EAAED,YAAY,CAACE,UAAU;IAClCC,YAAY,EAAEH,YAAY,CAACP,GAAG;IAC9BW,eAAe,EAAEJ;EACnB,CAAC,CAAC;EACF,MAAMK,IAAI,GAAGL,YAAY,CAACK,IAAI;EAC9B,MAAMC,KAAK,GAAGN,YAAY,CAACM,KAAK;EAChC,MAAMC,WAAW,GAAGP,YAAY,CAACxD,IAAI;EACrC,MAAMgE,iBAAiB,GAAGD,WAAW,CAClCE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,QAAQ,EAAGC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAE5C,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3M,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4M,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7M,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC0D,WAAW,EAAEoJ,cAAc,CAAC,GAAG9M,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+M,YAAY,EAAEC,WAAW,CAAC,GAAGhN,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAMiN,cAAc,GAAGnN,MAAM,CAAC,CAAC;EAC/B,MAAMoN,WAAW,GAAGpN,MAAM,CAAC,CAAC;EAC5B,MAAMqN,iBAAiB,GAAGrN,MAAM,CAAC,CAAC;EAClC,MAAMsN,kBAAkB,GAAGtN,MAAM,CAAC,CAAC;EACnC,MAAM,CAACuN,SAAS,EAAEC,YAAY,CAAC,GAAGtN,QAAQ,CAAC,KAAK,CAAC;EACjD,IAAIuN,YAAY,GAAG,IAAI;EACvB,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzN,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM0N,aAAa,GAAG,IAAIC,IAAI,CAACxC,SAAS,CAACyC,SAAS,CAAC;EACnD,MAAMC,KAAK,GAAG,IAAIF,IAAI,CAAC,CAAC;EACxB,MAAMG,eAAe,GAAG,CAACD,KAAK,GAAGH,aAAa,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACvE;EACA,MAAMK,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,IAAI,CAAC,CAAC,GAAGJ,eAAe,CAAC,EAAE,CAAC,CAAC;EACtE,MAAM,CAACK,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpO,QAAQ,CAAC,IAAI,CAAC;EAE1E,MAAMqO,aAAa,GAAGA,CAAA,KAAM;IAC1BzF,UAAU,CAAC,CAAC1F,OAAO,CAAC;EACtB,CAAC;EAED,MAAMoL,YAAY,GAAGA,CAAA,KAAM;IACzB7D,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAM8D,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,SAAS,GAAG,CAACnB,SAAS;IAC5BC,YAAY,CAACkB,SAAS,CAAC;IACvBxG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEuG,SAAS,CAAC;IAC5C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpM,sBAAsB,CAAC4J,YAAY,EAAEuC,SAAS,CAAC;MACtExG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEwG,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd1G,OAAO,CAAC0G,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,IAAI,EAAE,cAAc,IAAI5J,MAAM,CAAC,EAAE;IAC/B6J,KAAK,CAAC,qDAAqD,CAAC;EAC9D,CAAC,MAAM,IAAIC,YAAY,CAACC,UAAU,KAAK,QAAQ,EAAE;IAC/C;IACAD,YAAY,CAACE,iBAAiB,CAAC,CAAC;EAClC;EAEA,MAAMC,UAAU,GAAG,MAAOC,QAAQ,IAAK;IACrCjG,WAAW,CAAC,CAACD,YAAY,CAAC;IAC1BgE,cAAc,CAAC,CAACpJ,WAAW,CAAC;IAC5B,MAAM+K,QAAQ,GAAG,MAAMtM,OAAO,CAAC6M,QAAQ,CAAC;IACxC;IACA;IACA,IAAIlG,YAAY,EAAE;MAChBG,cAAc,CAAC+F,QAAQ,CAAC;MACxB7F,eAAe,CAACsF,QAAQ,CAACQ,IAAI,CAAC5H,IAAI,CAACiB,IAAI,CAAC;MACxCe,YAAY,CAACoF,QAAQ,CAACQ,IAAI,CAAC5H,IAAI,CAACkB,KAAK,CAAC;MACtCgB,WAAW,CAACkF,QAAQ,CAACQ,IAAI,CAAC5H,IAAI,CAAC6H,IAAI,CAAC;MAEpC,IAAIT,QAAQ,CAACQ,IAAI,CAAC5H,IAAI,CAAC8H,eAAe,EAAE;QACtC;QACA,MAAMC,eAAe,GAAG,MAAMhN,iBAAiB,CAAC4M,QAAQ,CAAC;QACzD,IAAII,eAAe,CAACH,IAAI,CAACI,OAAO,EAAE;UAChClF,aAAa,CAAC;YACZH,KAAK,EAAEoF,eAAe,CAACH,IAAI,CAACI,OAAO,CAACrD,UAAU;YAC9C/B,KAAK,EACHmF,eAAe,CAACH,IAAI,CAACI,OAAO,CAACrD,UAAU,GACvC,KAAK,GACLoD,eAAe,CAACH,IAAI,CAACI,OAAO,CAAClD,IAAI,GACjC,KAAK,GACLiD,eAAe,CAACH,IAAI,CAACI,OAAO,CAAC/G;UACjC,CAAC,CAAC;QACJ;QACA,IAAI,CAAC8G,eAAe,CAACH,IAAI,CAACI,OAAO,EAAE;UACjC;QAAA;MAEJ,CAAC,MAAM;QACL;QACAlF,aAAa,CAAC;UACZH,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMqF,eAAe,GAAGA,CAAA,KAAM;IAC5BxC,cAAc,CAAC,CAACpJ,WAAW,CAAC;EAC9B,CAAC;EAED,MAAM6L,mBAAmB,GAAGA,CAAA,KAAM;IAChCzC,cAAc,CAAC,CAACpJ,WAAW,CAAC;IAC5BsJ,WAAW,CAAC,CAACD,YAAY,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzP,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0P,YAAY,EAAEC,eAAe,CAAC,GAAG3P,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4P,QAAQ,EAAEC,WAAW,CAAC,GAAG7P,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8P,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/P,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACgQ,4BAA4B,EAAEC,+BAA+B,CAAC,GAAGjQ,QAAQ,CAAC,CAAC,CAAC;EACnF,MAAM,CAACkQ,8BAA8B,EAAEC,iCAAiC,CAAC,GAAGnQ,QAAQ,CAAC,CAAC,CAAC;EACvF,MAAM,CAACoQ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrQ,QAAQ,CAAC,KAAK,CAAC;EACvED,SAAS,CAAC,MAAM;IACd,MAAMuQ,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFtI,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;QAElE;QACA,MAAMwG,QAAQ,GAAG,MAAMrM,iBAAiB,CAACkJ,MAAM,CAAC;QAChDd,uBAAuB,CAACiE,QAAQ,CAACQ,IAAI,CAACI,OAAO,CAACkB,qBAAqB,CAAC;QACpEjD,YAAY,CAACmB,QAAQ,CAACQ,IAAI,CAACI,OAAO,CAACmB,UAAU,CAAC;QAC9Cf,gBAAgB,CAAChB,QAAQ,CAACQ,IAAI,CAACI,OAAO,CAACoB,cAAc,CAAC;;QAEtD;QACA,MAAMC,kBAAkB,GAAG9F,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC;QACtD,MAAM8F,SAAS,GAAGD,kBAAkB,GAChCxP,QAAQ,CAAC4J,GAAG,CAACC,OAAO,CAAC2F,kBAAkB,EAAEhG,SAAS,CAAC,CAACM,QAAQ,CAC5D9J,QAAQ,CAAC+J,GAAG,CAACC,IACf,CAAC,GACC,IAAI;QACR,MAAM0F,GAAG,GAAGjD,IAAI,CAACiD,GAAG,CAAC,CAAC;;QAEtB;QACA,MAAMC,oBAAoB,GAAG,MAAMtO,sBAAsB,CACvDkM,QAAQ,CAACQ,IAAI,CAACI,OAAO,CAAC9D,GACxB,CAAC;QAEDvD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4I,oBAAoB,CAAC;QAC1D,IAAIA,oBAAoB,CAACC,MAAM,KAAK,GAAG,EAAE;UACvCf,uBAAuB,CAAC,IAAI,CAAC;QAC/B;QAEAJ,eAAe,CAACkB,oBAAoB,CAAC5B,IAAI,CAAC8B,aAAa,IAAI,EAAE,CAAC,CAAC,CAAC;QAChElB,WAAW,CAACgB,oBAAoB,CAAC5B,IAAI,CAAC+B,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;;QAExD;QACA,MAAMC,YAAY,GAAG/P,QAAQ,CAAC4J,GAAG,CAACoG,OAAO,CACvC9F,IAAI,CAAC+F,SAAS,CAACP,GAAG,CAAC,EACnBlG,SACF,CAAC,CAACM,QAAQ,CAAC,CAAC;QACZJ,YAAY,CAACwG,OAAO,CAAC,KAAK,EAAEH,YAAY,CAAC;QAEzC7C,yBAAyB,CAAC,KAAK,CAAC;MAClC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACd1G,OAAO,CAAC0G,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CN,yBAAyB,CAAC,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACAkC,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMe,UAAU,GAAGC,WAAW,CAAChB,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;;IAEpD;IACA,OAAO,MAAMiB,aAAa,CAACF,UAAU,CAAC;;IAEtC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtR,SAAS,CAAC,MAAM;IACd,IAAI,CAACkM,YAAY,EAAE;IAEnB,MAAMuF,qCAAqC,GAAG,MAAAA,CAAA,KAAY;MACxD,IAAI;QACF,MAAM/C,QAAQ,GAAG,MAAMjM,mBAAmB,CAACyJ,YAAY,CAAC;QACxD,MAAMJ,OAAO,GAAG4C,QAAQ,CAACQ,IAAI,CAACpD,OAAO;QAErC,IAAI4F,cAAc,GAAG,CAAC;;QAEtB;QACA,IAAI5F,OAAO,CAAC6F,oBAAoB,IAAI7F,OAAO,CAAC6F,oBAAoB,CAACC,gBAAgB,EAAE;UACjFF,cAAc,GAAG5F,OAAO,CAAC6F,oBAAoB,CAACC,gBAAgB,CAAC/F,MAAM;QACvE,CAAC,MAAM,IAAIC,OAAO,CAAC+F,8BAA8B,EAAE;UACjD;UACAH,cAAc,GAAG5F,OAAO,CAAC+F,8BAA8B;QACzD;QAEA5J,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7CwJ,cAAc;UACdI,WAAW,EAAE3B,8BAA8B;UAC3C4B,mBAAmB,EAAEjG,OAAO,CAAC6F;QAC/B,CAAC,CAAC;QAEFzB,+BAA+B,CAACwB,cAAc,CAAC;;QAE/C;QACA,MAAMM,oBAAoB,GAAGlG,OAAO,CAAC6F,oBAAoB,IAAI7F,OAAO,CAAC6F,oBAAoB,CAACM,UAAU;QACpG3B,uBAAuB,CAAC,CAAC0B,oBAAoB,IAAIN,cAAc,GAAGvB,8BAA8B,CAAC;MACnG,CAAC,CAAC,OAAOxB,KAAK,EAAE;QACd1G,OAAO,CAAC0G,KAAK,CAAC,sDAAsD,EAAEA,KAAK,CAAC;MAC9E;IACF,CAAC;IAED8C,qCAAqC,CAAC,CAAC;EACzC,CAAC,EAAE,CAACvF,YAAY,EAAEiE,8BAA8B,CAAC,CAAC;;EAElD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAIE;EACA,MAAM+B,aAAa,GAAGnN,MAAM,CAACoN,QAAQ,CAACC,QAAQ,KAAK,WAAW;EAC9D,MAAMC,MAAM,GAAGH,aAAa,GACxBI,OAAO,CAACC,GAAG,CAACC,wBAAwB,GACpCF,OAAO,CAACC,GAAG,CAACE,yBAAyB;;EAEzC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAYEzS,SAAS,CAAC,MAAM;IACd,IAAI,CAACkM,YAAY,EAAE;;IAEnB;IACA,IAAIpD,SAAS,CAAC4J,OAAO,EAAE;MACrBzK,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClDY,SAAS,CAAC4J,OAAO,CAACC,UAAU,CAAC,CAAC;IAChC;IAEA1K,OAAO,CAACC,GAAG,CAAC,6CAA6CgE,YAAY,CAACjB,QAAQ,CAAC,CAAC,KAAK,CAAC;;IAEtF;IACA,MAAM2H,MAAM,GAAGxR,EAAE,CAACiR,MAAM,EAAE;MACxBQ,eAAe,EAAE,IAAI;MACrBC,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,IAAI,EAAE;QAAEC,KAAK,EAAEnI,YAAY,CAACC,OAAO,CAAC,OAAO;MAAE,CAAC;MAC9CmI,YAAY,EAAE,IAAI;MAAE;MACpBC,oBAAoB,EAAE,EAAE;MAAE;MAC1BC,iBAAiB,EAAE,IAAI,CAAE;IAC3B,CAAC,CAAC;;IAEF;IACArK,SAAS,CAAC4J,OAAO,GAAGE,MAAM;;IAE1B;IACAA,MAAM,CAACQ,IAAI,CAAC,iBAAiB,EAAE;MAC7BC,SAAS,EAAEnH,YAAY,CAACjB,QAAQ,CAAC,CAAC;MAClCqI,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACAV,MAAM,CAACW,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBtL,OAAO,CAACC,GAAG,CAAC,wCAAwC0K,MAAM,CAACY,EAAE,EAAE,CAAC;MAChEZ,MAAM,CAACQ,IAAI,CAAC,eAAe,EAAE;QAAEC,SAAS,EAAEnH,YAAY,CAACjB,QAAQ,CAAC;MAAE,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC,CAAC;;IAEF;IACA2H,MAAM,CAACW,EAAE,CAAC,mBAAmB,EAAGE,OAAO,IAAK;MAC1CxL,OAAO,CAACC,GAAG,CAAC,8BAA8BuL,OAAO,SAAS,CAAC;IAC7D,CAAC,CAAC;IAEFb,MAAM,CAACW,EAAE,CAAC,WAAW,EAAGE,OAAO,IAAK;MAClCxL,OAAO,CAACC,GAAG,CAAC,mCAAmCuL,OAAO,cAAc,CAAC;MACrEb,MAAM,CAACQ,IAAI,CAAC,eAAe,EAAE;QAAEC,SAAS,EAAEnH,YAAY,CAACjB,QAAQ,CAAC;MAAE,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC,CAAC;;IAEF;IACA2H,MAAM,CAACW,EAAE,CAAC,eAAe,EAAG5E,KAAK,IAAK;MACpC1G,OAAO,CAAC0G,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,CAAC;;IAEF;IACAiE,MAAM,CAACc,KAAK,CAAC,CAACC,KAAK,EAAEzE,IAAI,KAAK;MAC5B;IAAA,CACD,CAAC;;IAEF;IACA0D,MAAM,CAACW,EAAE,CAAC,YAAY,EAAE,MAAM;MAC5BtL,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC0L,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;;IAEF;IACAhB,MAAM,CAACW,EAAE,CAAC,cAAc,EAAE,CAAC;MAAEF,SAAS,EAAEQ,gBAAgB;MAAE9C;IAAO,CAAC,KAAK;MACrE,IAAI7E,YAAY,CAACjB,QAAQ,CAAC,CAAC,KAAK4I,gBAAgB,EAAE;QAChD5L,OAAO,CAACC,GAAG,CAAC,wCAAwC6I,MAAM,EAAE,CAAC;QAC7DrB,gBAAgB,CAACqB,MAAM,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;MAC9D;IACF,CAAC,CAAC;;IAEF;IACA6B,MAAM,CAACW,EAAE,CAAC,sBAAsB,EAAGrE,IAAI,IAAK;MAC1CjH,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEgH,IAAI,CAAC;;MAErE;MACAjH,OAAO,CAACC,GAAG,CAAC,mCAAmCgH,IAAI,CAAC4E,UAAU,uBAAuB5H,YAAY,GAAG,CAAC;MAErG,IAAIgD,IAAI,CAAC4E,UAAU,KAAK5H,YAAY,CAACjB,QAAQ,CAAC,CAAC,EAAE;QAC/ChD,OAAO,CAACC,GAAG,CAAC,qCAAqCgE,YAAY,EAAE,CAAC;;QAEhE;QACA,MAAM6H,gBAAgB,GAAG;UACvB,GAAG7E,IAAI;UACP8E,cAAc,EAAE/R,MAAM,CAAC;QACzB,CAAC;QAEDyL,wBAAwB,CAAEuG,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEF,gBAAgB,CAAC,CAAC;MACjE,CAAC,MAAM;QACL9L,OAAO,CAACC,GAAG,CAAC,yDAAyDgH,IAAI,CAAC4E,UAAU,YAAY5H,YAAY,EAAE,CAAC;MACjH;IACF,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXjE,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1E0K,MAAM,CAACsB,GAAG,CAAC,YAAY,CAAC;MACxBtB,MAAM,CAACsB,GAAG,CAAC,cAAc,CAAC;MAC1BtB,MAAM,CAACsB,GAAG,CAAC,sBAAsB,CAAC;MAClCtB,MAAM,CAACsB,GAAG,CAAC,mBAAmB,CAAC;MAC/BtB,MAAM,CAACsB,GAAG,CAAC,WAAW,CAAC;MACvBtB,MAAM,CAACsB,GAAG,CAAC,eAAe,CAAC;MAC3BtB,MAAM,CAACD,UAAU,CAAC,CAAC;MACnB7J,SAAS,CAAC4J,OAAO,GAAG,IAAI;IAC1B,CAAC;EACH,CAAC,EAAE,CAACxG,YAAY,CAAC,CAAC;;EAGlB;EACA,MAAMiI,kBAAkB,GAAIH,cAAc,IAAK;IAC7CtG,wBAAwB,CAAEuG,IAAI,IAAKA,IAAI,CAACG,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACL,cAAc,KAAKA,cAAc,CAAC,CAAC;EACnG,CAAC;;EAED;EACA,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,eAAe,GAAG;MACtBC,OAAO,EAAE,0BAA0B;MACnCV,UAAU,EAAE5H,YAAY,CAACjB,QAAQ,CAAC,CAAC;MACnCwJ,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,6BAA6B;MACtCC,QAAQ,EAAE,2BAA2B;MACrCC,SAAS,EAAE,IAAIhH,IAAI,CAAC,CAAC,CAACiH,WAAW,CAAC;IACpC,CAAC;IACD,MAAMd,gBAAgB,GAAG;MACvB,GAAGQ,eAAe;MAClBP,cAAc,EAAE/R,MAAM,CAAC,CAAC,CAAE;IAC5B,CAAC;IACDyL,wBAAwB,CAAEuG,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEF,gBAAgB,CAAC,CAAC;EACjE,CAAC;EAED,MAAMe,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC3C;IACA,IAAInG,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACzC,MAAMmG,YAAY,GAAG,IAAIpG,YAAY,CAACkG,KAAK,EAAE;QAC3C,GAAGC,OAAO;QACVE,IAAI,EAAEvU,KAAK,CAAE;MACf,CAAC,CAAC;;MAEF;MACAsU,YAAY,CAACE,MAAM,GAAG,MAAM;QAC1B,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACxU,WAAW,CAAC;QACpCuU,KAAK,CACFE,IAAI,CAAC,CAAC,CACNC,KAAK,CAAE5G,KAAK,IACX1G,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEyG,KAAK,CAC/D,CAAC;MACL,CAAC;IACH;EACF,CAAC;EAED,MAAMiF,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMmB,KAAK,GAAG,uBAAuB;IACrC,MAAMC,OAAO,GAAG;MACdQ,IAAI,EAAE,8BAA8B;MACpC;MACAC,KAAK,EAAE5U,WAAW,CAAE;IACtB,CAAC;IACDiU,gBAAgB,CAACC,KAAK,EAAEC,OAAO,CAAC;EAClC,CAAC;EAED,MAAMU,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCzN,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1B,MAAMuG,SAAS,GAAG,CAACjE,oBAAoB;IACvC,IAAI;MACF,MAAMkE,QAAQ,GAAG,MAAMnM,gBAAgB,CAAC2J,YAAY,EAAEuC,SAAS,CAAC;MAChE,IAAIC,QAAQ,CAACqC,MAAM,KAAK,GAAG,EAAE;QAC3BtG,uBAAuB,CAACgE,SAAS,CAAC;MACpC,CAAC,MAAM;QACLxG,OAAO,CAAC0G,KAAK,CAAC,sCAAsC,EAAED,QAAQ,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd1G,OAAO,CAAC0G,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED3O,SAAS,CAAC,MAAM;IACd;IACA0H,QAAQ,CAACxC,gBAAgB,CAAC,WAAW,EAAEyQ,yBAAyB,CAAC;IACjE;IACA,OAAO,MAAM;MACXjO,QAAQ,CAACvC,mBAAmB,CAAC,WAAW,EAAEwQ,yBAAyB,CAAC;IACtE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,yBAAyB,GAAIC,CAAC,IAAK;IACvC,IAAI1I,cAAc,CAACwF,OAAO,CAACmD,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAC,EAAE;MAC7C;IACF,CAAC,MAAM;MACL/I,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC;EAED/M,SAAS,CAAC,MAAM;IACd;IACA;IACA;;IAEA+E,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE6Q,YAAY,CAAC;IAC/C;IACA,OAAO,MAAM;MACX;MACA;MACAhR,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE4Q,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,YAAY,GAAGjR,MAAM,CAACC,UAAU;IACtC,IAAIgR,YAAY,GAAG,IAAI,EAAE;MACvBnN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoN,yBAAyB,GAAIL,CAAC,IAAK;IACvC,IACGxI,iBAAiB,CAACsF,OAAO,IACxBtF,iBAAiB,CAACsF,OAAO,CAACmD,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAC,IAC7CzI,kBAAkB,CAACqF,OAAO,IACzBrF,kBAAkB,CAACqF,OAAO,CAACmD,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAE,EAChD;MACAvL,iBAAiB,CAAC,IAAI,CAAC;MACvB;IACF;EACF,CAAC;EAED,MAAM2L,wBAAwB,GAAIN,CAAC,IAAK;IACtC,IACGxI,iBAAiB,CAACsF,OAAO,IACxB,CAACtF,iBAAiB,CAACsF,OAAO,CAACmD,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAC,IAC9CzI,kBAAkB,CAACqF,OAAO,IACzB,CAACrF,kBAAkB,CAACqF,OAAO,CAACmD,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAE,EACjD;MACAvL,iBAAiB,CAAC,KAAK,CAAC;MACxB;IACF;EACF,CAAC;EAED,MAAM4L,sBAAsB,GAAGA,CAAA,KAAM;IACnC5L,iBAAiB,CAAE6L,QAAQ,IAAK,CAACA,QAAQ,CAAC;EAC5C,CAAC;EAED,oBACEtT,OAAA,CAAAE,SAAA;IAAA0C,QAAA,gBACE5C,OAAA,CAACxB,YAAY;MACX2L,WAAW,EAAEA,WAAY;MACzBD,YAAY,EAAEA,YAAa;MAC3BzB,MAAM,EAAEA;IAAO;MAAA3F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAEFjD,OAAA,CAACvB,aAAa;MACZyH,WAAW,EAAEA,WAAY;MACzBD,YAAY,EAAEA,YAAa;MAC3BW,UAAU,EAAEA,UAAW;MACvBS,UAAU,EAAEA,UAAW;MACvBC,aAAa,EAAEA,aAAc;MAC7BnB,WAAW,EAAEA,WAAY;MACzBE,YAAY,EAAEA,YAAa;MAC3BE,SAAS,EAAEA,SAAU;MACrBE,QAAQ,EAAEA,QAAS;MACnB8M,UAAU,EAAE;IAAK;MAAAzQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAEFjD,OAAA,CAACtB,iBAAiB;MAChBmL,gBAAgB,EAAEA,gBAAiB;MACnCC,mBAAmB,EAAEA,mBAAoB;MACzCL,iBAAiB,EAAEA;IAAkB;MAAA3G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAEFjD,OAAA,CAACrB,sBAAsB;MACrBoL,qBAAqB,EAAEA,qBAAsB;MAC7CC,wBAAwB,EAAEA,wBAAyB;MACnDP,iBAAiB,EAAEA;IAAkB;MAAA3G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAEFjD,OAAA,CAACvC,WAAW,CAAC+V,QAAQ;MAACrM,KAAK,EAAE;QAAEsM,KAAK,EAAE;MAAO,CAAE;MAAA7Q,QAAA,gBAC7C5C,OAAA,CAACG,GAAG;QAACE,OAAO,EAAEA,OAAQ;QAACiD,KAAK,EAAE;UAAEoQ,cAAc,EAAE;QAAgB,CAAE;QAAA9Q,QAAA,gBAChE5C,OAAA;UAAKkD,SAAS,EAAC,gCAAgC;UAAAN,QAAA,gBAC7C5C,OAAA;YAAK6C,OAAO,EAAEmE,UAAW;YAAC9D,SAAS,EAAC,qBAAqB;YAACI,KAAK,EAAE;cAAEqQ,MAAM,EAAE;YAAU,CAAE;YAAA/Q,QAAA,eACrF5C,OAAA,CAAClB,eAAe;cAAC2U,KAAK,EAAC;YAAO;cAAA3Q,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACNjD,OAAA;YACEkD,SAAS,EAAC,eAAe;YACzBI,KAAK,EAAE;cACLsQ,UAAU,EAAE,KAAK;cACjBC,aAAa,EAAE,KAAK;cACpBrQ,OAAO,EAAEzB,QAAQ,GAAG,MAAM,GAAG;YAC/B,CAAE;YAAAa,QAAA,eAEF5C,OAAA;cAAK8E,GAAG,EAAEhH,OAAQ;cAACgW,GAAG,EAAC,cAAc;cAACpQ,KAAK,EAAE,GAAI;cAACH,MAAM,EAAE,EAAG;cAACL,SAAS,EAAC;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjD,OAAA;UAAKkD,SAAS,EAAC,QAAQ;UAAAN,QAAA,gBAErB5C,OAAA;YACE+T,GAAG,EAAExJ,kBAAmB;YACxBrH,SAAS,EACPsE,cAAc,GAAG,mBAAmB,GAAG,qBACxC;YAAA5E,QAAA,eAED5C,OAAA;cAAKkD,SAAS,EAAC,cAAc;cAAAN,QAAA,gBAC3B5C,OAAA;gBAAKkD,SAAS,EAAC,eAAe;gBAAAN,QAAA,gBAC5B5C,OAAA;kBAAKkD,SAAS,EAAC,aAAa;kBAAAN,QAAA,gBAC1B5C,OAAA;oBAAKkD,SAAS,EAAC;kBAAsB;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxCjD,OAAA;oBAAKkD,SAAS,EAAC,cAAc;oBAAAN,QAAA,gBAC3B5C,OAAA;sBACEmD,QAAQ,EAAC,UAAU;sBACnB6Q,UAAU,EAAE,CAAE;sBACd5Q,SAAS,EAAC,SAAS;sBACnBC,QAAQ,EAAE,EAAG;sBACb,0BAAuB,EAAE;sBACzBC,KAAK,EAAE;wBAAEC,MAAM,EAAE;sBAAG,CAAE;sBAAAX,QAAA,eAEtB5C,OAAA;wBACE0D,KAAK,EAAE,EAAG;wBACVH,MAAM,EAAE,EAAG;wBACXK,IAAI,EAAC,SAAS;wBACdD,OAAO,EAAC,WAAW;wBACnBF,KAAK,EAAC,4BAA4B;wBAAAb,QAAA,eAElC5C,OAAA;0BACEiU,CAAC,EAAC,yyDAAyyD;0BAC3yDrQ,IAAI,EAAC;wBAAS;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC,eACdjD,OAAA;sBAAMkD,SAAS,EAAC,aAAa;sBAAAN,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAUNjD,OAAA;kBAAKkD,SAAS,EAAC,SAAS;kBAAAN,QAAA,eACtB5C,OAAA;oBAAKkD,SAAS,EAAC,kBAAkB;oBAAAN,QAAA,eAC/B5C,OAAA;sBACEkU,IAAI,EAAC,UAAU;sBACfhR,SAAS,EAAC,UAAU;sBACpBiR,OAAO,EAAE3J,SAAU;sBACnB4J,QAAQ,EAAE1I;oBAAmB;sBAAA5I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNjD,OAAA;gBACEkD,SAAS,EAAC,oBAAoB;gBAC9BI,KAAK,EAAE;kBAAE+Q,YAAY,EAAE;gBAAM,CAAE;gBAAAzR,QAAA,gBAE/B5C,OAAA;kBAAKkD,SAAS,EAAC,aAAa;kBAAAN,QAAA,gBAC1B5C,OAAA;oBAAKkD,SAAS,EAAC;kBAAsB;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxCjD,OAAA;oBACEkD,SAAS,EAAC,cAAc;oBACxBI,KAAK,EAAE;sBAAEE,OAAO,EAAE,aAAa;sBAAE8Q,UAAU,EAAE;oBAAS,CAAE;oBAAA1R,QAAA,gBAExD5C,OAAA;sBACEmD,QAAQ,EAAC,OAAO;sBAChB6Q,UAAU,EAAE,CAAE;sBACd5Q,SAAS,EAAC,SAAS;sBACnBC,QAAQ,EAAE,EAAG;sBACb,0BAAuB,EAAE;sBACzBC,KAAK,EAAE;wBAAEC,MAAM,EAAE;sBAAG,CAAE;sBAAAX,QAAA,eAEtB5C,OAAA;wBACEyD,KAAK,EAAC,4BAA4B;wBAClCC,KAAK,EAAE,EAAG;wBACVH,MAAM,EAAE,EAAG;wBACXI,OAAO,EAAC,WAAW;wBACnBC,IAAI,EAAC,MAAM;wBACXC,MAAM,EAAC,SAAS;wBAChBC,WAAW,EAAE,CAAE;wBACfC,aAAa,EAAC,OAAO;wBACrBC,cAAc,EAAC,OAAO;wBACtBd,SAAS,EAAC,uBAAuB;wBAAAN,QAAA,gBAEjC5C,OAAA;0BAAMiU,CAAC,EAAC;wBAA2C;0BAAAnR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACtDjD,OAAA;0BAAQuU,EAAE,EAAE,CAAE;0BAACC,EAAE,EAAE,CAAE;0BAACC,CAAC,EAAE;wBAAE;0BAAA3R,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9BjD,OAAA;0BAAMiU,CAAC,EAAC;wBAA4B;0BAAAnR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACvCjD,OAAA;0BAAMiU,CAAC,EAAC;wBAA2B;0BAAAnR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC,eACdjD,OAAA;sBAAKsD,KAAK,EAAE;wBAAEE,OAAO,EAAE,aAAa;wBAAEkR,UAAU,EAAE;sBAAO,CAAE;sBAAA9R,QAAA,gBACzD5C,OAAA;wBAAMkD,SAAS,EAAC,aAAa;wBAAAN,QAAA,EAAC;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3CjD,OAAA;wBAAMkD,SAAS,EAAC,aAAa;wBAAAN,QAAA,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENjD,OAAA;kBAAKkD,SAAS,EAAC,SAAS;kBAAAN,QAAA,eACtB5C,OAAA;oBAAKkD,SAAS,EAAC,kBAAkB;oBAAAN,QAAA,eAC/B5C,OAAA;sBAAOkU,IAAI,EAAC,UAAU;sBAAChR,SAAS,EAAC;oBAAU;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEH,CAAC,EAELmM,aAAa,IAAI,CAACrN,QAAQ,gBACzB/B,OAAA;YAAQkD,SAAS,EAAC,uBAAuB;YAACL,OAAO,EAAE2O,kBAAmB;YAAA5O,QAAA,EAAC;UAEvE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,GAET0R,SAAS,eAIX3U,OAAA;YACEsD,KAAK,EAAE;cACLsR,OAAO,EAAE,mBAAmB;cAC5BC,UAAU,EAAE,8BAA8B;cAC1CtR,MAAM,EAAE,EAAE;cACVuR,aAAa,EAAE,QAAQ;cACvBpB,cAAc,EAAE,QAAQ;cACxBY,UAAU,EAAE,QAAQ;cACpBS,GAAG,EAAE,KAAK;cACVvR,OAAO,EAAE;YACX,CAAE;YACFuQ,GAAG,EAAEzJ,iBAAkB;YACvBzH,OAAO,EAAEwQ;YACX;YACA;YAAA;YAAAzQ,QAAA,gBAEE5C,OAAA;cAAKkD,SAAS,EAAC,eAAe;cAAAN,QAAA,eAC5B5C,OAAA;gBAAMsD,KAAK,EAAE;kBAAE0R,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEC,UAAU,EAAE;gBAAE,CAAE;gBAAAtS,QAAA,EAAC;cAE/D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNjD,OAAA,CAAC9B,OAAO;cAACoF,KAAK,EAAE;gBAAEmQ,KAAK,EAAE,OAAO;gBAAEuB,QAAQ,EAAE;cAAG;YAAE;cAAAlS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDjD,OAAA;cACEkD,SAAS,EAAC,eAAe;cACzBI,KAAK,EAAE;gBACL2R,UAAU,EAAE,MAAM;gBAClBzR,OAAO,EAAE,MAAM;gBACfkQ,cAAc,EAAE,QAAQ;gBACxBsB,QAAQ,EAAE,EAAE;gBACZG,QAAQ,EAAE;cACZ,CAAE;cAAAvS,QAAA,gBAEF5C,OAAA;gBAAA4C,QAAA,EAAM;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBjD,OAAA,CAAC/B,mBAAmB;gBAACqF,KAAK,EAAE;kBAAEmQ,KAAK,EAAE,OAAO;kBAAEuB,QAAQ,EAAE;gBAAG;cAAE;gBAAAlS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjD,OAAA,CAACJ,OAAO;YACNwV,SAAS,EAAC,QAAQ;YAClBnD,KAAK,EAAE,eAAetF,aAAa,KAAK,SAAS,GAAG,SAAS,GAAG,WAAW,EAAG;YAAA/J,QAAA,eAE9E5C,OAAA;cAAKsD,KAAK,EAAE;gBACVsR,OAAO,EAAE,mBAAmB;gBAC5BC,UAAU,EAAE,8BAA8B;gBAC1CtR,MAAM,EAAE,EAAE;gBACVuR,aAAa,EAAE,KAAK;gBACpBpB,cAAc,EAAE,QAAQ;gBACxBY,UAAU,EAAE,QAAQ;gBACpBS,GAAG,EAAE,KAAK;gBACVvR,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,gBACA5C,OAAA,CAACzB,SAAS;gBAACkV,KAAK,EAAE9G,aAAa,KAAK,SAAS,GAAG,KAAK,GAAG,SAAU;gBAACqI,QAAQ,EAAE;cAAG;gBAAAlS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnFjD,OAAA;gBAAKkD,SAAS,EAAC,eAAe;gBAAAN,QAAA,gBAC5B5C,OAAA;kBAAA4C,QAAA,eACE5C,OAAA;oBAAMsD,KAAK,EAAE;sBAAE0R,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE,GAAG;sBAAEC,UAAU,EAAE;oBAAE,CAAE;oBAAAtS,QAAA,EAAC;kBAE/D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACL0J,aAAa,IAAIA,aAAa,KAAK,SAAS,gBAC3C3M,OAAA;kBAAKkD,SAAS,EAAC,6BAA6B;kBAAAN,QAAA,eAC1C5C,OAAA;oBAAA4C,QAAA,EAAM;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,gBAENjD,OAAA;kBAAKkD,SAAS,EAAC,sBAAsB;kBAAAN,QAAA,eACnC5C,OAAA;oBAAA4C,QAAA,EAAM;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACVjD,OAAA,CAACJ,OAAO;YACNwV,SAAS,EAAC,QAAQ;YAClBnD,KAAK,EAAE,mBAAoB;YAAArP,QAAA,eAE3B5C,OAAA;cACEsD,KAAK,EAAE;gBACLsR,OAAO,EAAE,UAAU;gBACnBC,UAAU,EAAE,8BAA8B;gBAC1CtR,MAAM,EAAE,EAAE;gBACVuR,aAAa,EAAE,QAAQ;gBACvBpB,cAAc,EAAE,QAAQ;gBACxBY,UAAU,EAAE,QAAQ;gBACpBS,GAAG,EAAE,KAAK;gBACVvR,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,eAEF5C,OAAA;gBACE6C,OAAO,EAAEA,CAAA,KAAMiH,mBAAmB,CAAC,IAAI,CAAE;gBACzCxG,KAAK,EAAE;kBACLqQ,MAAM,EAAE,SAAS;kBACjBnQ,OAAO,EAAE,MAAM;kBACf8Q,UAAU,EAAE;gBACd,CAAE;gBAAA1R,QAAA,gBAEF5C,OAAA;kBAAK8E,GAAG,EAAE7F,aAAc;kBAACsE,MAAM,EAAE,EAAG;kBAACG,KAAK,EAAE;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClDjD,OAAA;kBAAKkD,SAAS,EAAC,kCAAkC;kBAAAN,QAAA,gBAC/C5C,OAAA;oBAAMsD,KAAK,EAAE;sBAAE0R,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE,GAAG;sBAAEC,UAAU,EAAE;oBAAE,CAAE;oBAAAtS,QAAA,EAAC;kBAE/D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPjD,OAAA;oBAAMsD,KAAK,EAAE;sBAAE0R,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE,GAAG;sBAAEC,UAAU,EAAE;oBAAE,CAAE;oBAAAtS,QAAA,EAAC;kBAE/D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEVjD,OAAA,CAACJ,OAAO;YACNwV,SAAS,EAAC,QAAQ;YAClBnD,KAAK,EAAE,gBAAiB;YAAArP,QAAA,eAExB5C,OAAA;cACEsD,KAAK,EAAE;gBACLsR,OAAO,EAAE,UAAU;gBACnBC,UAAU,EAAE,8BAA8B;gBAC1CtR,MAAM,EAAE,EAAE;gBACVuR,aAAa,EAAE,QAAQ;gBACvBpB,cAAc,EAAE,QAAQ;gBACxBY,UAAU,EAAE,QAAQ;gBACpBS,GAAG,EAAE,KAAK;gBACVvR,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,eAEF5C,OAAA;gBACE6C,OAAO,EAAEA,CAAA,KAAMmH,wBAAwB,CAAC,IAAI,CAAE;gBAC9C1G,KAAK,EAAE;kBACLqQ,MAAM,EAAE,SAAS;kBACjBnQ,OAAO,EAAE,MAAM;kBACf8Q,UAAU,EAAE;gBACd,CAAE;gBAAA1R,QAAA,gBAEF5C,OAAA;kBAAK8E,GAAG,EAAE9F,UAAW;kBAACuE,MAAM,EAAE,EAAG;kBAACG,KAAK,EAAE;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CjD,OAAA;kBAAKkD,SAAS,EAAC,kCAAkC;kBAAAN,QAAA,gBAC/C5C,OAAA;oBAAMsD,KAAK,EAAE;sBAAE0R,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE,GAAG;sBAAEC,UAAU,EAAE;oBAAE,CAAE;oBAAAtS,QAAA,EAAC;kBAE/D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPjD,OAAA;oBAAMsD,KAAK,EAAE;sBAAE0R,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE,GAAG;sBAAEC,UAAU,EAAE;oBAAE,CAAE;oBAAAtS,QAAA,EAAC;kBAE/D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEVjD,OAAA;YACEsD,KAAK,EAAE;cACLsR,OAAO,EAAE,mBAAmB;cAC5BrR,MAAM,EAAE,EAAE;cACVuR,aAAa,EAAE,QAAQ;cACvBpB,cAAc,EAAE,QAAQ;cACxBY,UAAU,EAAE,QAAQ;cACpBS,GAAG,EAAE,KAAK;cACVvR,OAAO,EAAE,aAAa;cACtBqR,UAAU,EAAE;YACd,CAAE;YACF3R,SAAS,EAAC,kBAAkB;YAAAN,QAAA,gBAE5B5C,OAAA;cAAA4C,QAAA,gBACE5C,OAAA,CAAChC,YAAY;gBAACsF,KAAK,EAAE;kBAAEmQ,KAAK,EAAE,OAAO;kBAAEuB,QAAQ,EAAE;gBAAG;cAAE;gBAAAlS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDjD,OAAA;gBAAMsD,KAAK,EAAE;kBAAE0R,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEC,UAAU,EAAE;gBAAE,CAAE;gBAAAtS,QAAA,GAAC,MAE7D,EAAC,CAAC8E,oBAAoB,gBACpB1H,OAAA;kBACEsD,KAAK,EAAE;oBACLI,KAAK,EAAE,EAAE;oBACTH,MAAM,EAAE,EAAE;oBACV8R,YAAY,EAAE,EAAE;oBAChBH,UAAU,EAAE,EAAE;oBACdI,UAAU,EAAE,SAAS;oBACrB5B,cAAc,EAAE,QAAQ;oBACxBY,UAAU,EAAE,QAAQ;oBACpBS,GAAG,EAAE,CAAC;oBACNvR,OAAO,EAAE;kBACX,CAAE;kBAAAZ,QAAA,eAEF5C,OAAA;oBAAA4C,QAAA,EAAM;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,gBAENjD,OAAA;kBACEsD,KAAK,EAAE;oBACLI,KAAK,EAAE,EAAE;oBACTH,MAAM,EAAE,EAAE;oBACV8R,YAAY,EAAE,EAAE;oBAChBH,UAAU,EAAE,EAAE;oBACdI,UAAU,EAAE,WAAW;oBACvB5B,cAAc,EAAE,QAAQ;oBACxBY,UAAU,EAAE,QAAQ;oBACpBS,GAAG,EAAE,CAAC;oBACNtB,KAAK,EAAE,OAAO;oBACduB,QAAQ,EAAE,EAAE;oBACZxR,OAAO,EAAE;kBACX,CAAE;kBAAAZ,QAAA,eAEF5C,OAAA;oBAAA4C,QAAA,EAAM;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNjD,OAAA;cACEsD,KAAK,EAAE;gBACL2R,UAAU,EAAE,MAAM;gBAClBzR,OAAO,EAAE,MAAM;gBACfkQ,cAAc,EAAE,QAAQ;gBACxBsB,QAAQ,EAAE;cACZ,CAAE;cAAApS,QAAA,eAEF5C,OAAA;gBAAA4C,QAAA,EAAO4G;cAAW;gBAAA1G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACLyE,oBAAoB,gBACnB1H,OAAA;cACEkU,IAAI,EAAC,QAAQ;cACbrR,OAAO,EAAEA,CAAA,KAAM+P,mBAAmB,CAAC,CAAE;cACrCtP,KAAK,EAAE;gBACLI,KAAK,EAAE,GAAG;gBACVH,MAAM,EAAE,EAAE;gBACV8R,YAAY,EAAE,CAAC;gBACfC,UAAU,EAAE,SAAS;gBACrB5B,cAAc,EAAE,QAAQ;gBACxBY,UAAU,EAAE,QAAQ;gBACpBS,GAAG,EAAE,CAAC;gBACNvR,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,eAEF5C,OAAA;gBAAGsD,KAAK,EAAE;kBAAEmQ,KAAK,EAAE,OAAO;kBAAEuB,QAAQ,EAAE;gBAAG,CAAE;gBAAApS,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,gBAENjD,OAAA;cACEkU,IAAI,EAAC,QAAQ;cACbrR,OAAO,EAAEA,CAAA,KAAM+P,mBAAmB,CAAC,CAAE;cACrCtP,KAAK,EAAE;gBACLI,KAAK,EAAE,GAAG;gBACVH,MAAM,EAAE,EAAE;gBACV8R,YAAY,EAAE,CAAC;gBACfC,UAAU,EAAE,SAAS;gBACrB5B,cAAc,EAAE,QAAQ;gBACxBY,UAAU,EAAE,QAAQ;gBACpBS,GAAG,EAAE,CAAC;gBACNvR,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,eAEF5C,OAAA;gBAAGsD,KAAK,EAAE;kBAAEmQ,KAAK,EAAE,OAAO;kBAAEuB,QAAQ,EAAE;gBAAG,CAAE;gBAAApS,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNjD,OAAA;YAAKkD,SAAS,EAAC,eAAe;YAACI,KAAK,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,MAAM,EAAE,EAAE;cAAEgS,WAAW,EAAE,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEX,UAAU,EAAE,mBAAmB;cAAEC,aAAa,EAAE,QAAQ;cAAEpB,cAAc,EAAE,QAAQ;cAAEY,UAAU,EAAE,QAAQ;cAAES,GAAG,EAAE,CAAC;cAAEvR,OAAO,EAAE;YAAc,CAAE;YAAAZ,QAAA,eAC5O5C,OAAA;cAAK6C,OAAO,EAAE4I,YAAa;cAACnI,KAAK,EAAE;gBAAEmS,SAAS,EAAE,QAAQ;gBAAE9B,MAAM,EAAE,SAAS;gBAAEF,KAAK,EAAE,SAAS;gBAAEuB,QAAQ,EAAE,EAAE;gBAAEC,UAAU,EAAE,KAAK;gBAAES,aAAa,EAAE,IAAI;gBAAEC,QAAQ,EAAE;cAAa,CAAE;cAAA/S,QAAA,gBAC5K5C,OAAA,CAACjB,WAAW;gBAACuE,KAAK,EAAE;kBAAEmQ,KAAK,EAAE,OAAO;kBAAEuB,QAAQ,EAAE;gBAAG;cAAE;gBAAAlS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAC1D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNjD,OAAA,CAACH,MAAM;QAACoS,KAAK,EAAC,iBAAW;QAAChL,OAAO,EAAEA,OAAQ;QAACtE,IAAI,EAAEA,IAAK;QAAYyS,SAAS,EAAC,MAAM;QAACQ,SAAS,EAAE;UAAEhB,OAAO,EAAE;QAAE,CAAE;QAAAhS,QAAA,eAC5G5C,OAAA;UAAKkD,SAAS,EAAC,kDAAkD;UAAAN,QAAA,gBAC/D5C,OAAA;YACEkD,SAAS,EAAC,mBAAmB;YAC7BI,KAAK,EAAE;cAAEuS,SAAS,EAAE,QAAQ;cAAEtS,MAAM,EAAE;YAAsB,CAAE;YAAAX,QAAA,EAE7DrF,WAAW,CAACuY,GAAG,CAAC,CAACvE,IAAI,EAAEwE,KAAK,KAAK;cAChC,oBACE/V,OAAA,CAACtC,cAAc;gBAEbsY,WAAW,EAAE,CAACzY,WAAW,CAACwY,KAAK,CAAC,CAAC/J,UAAU,CAAE;gBAAApJ,QAAA,eAE7C5C,OAAA,CAACxC,OAAO;kBACN+T,IAAI,EAAEA,IAAK;kBAEXjO,KAAK,EAAE;oBAAEgS,UAAU,EAAE;kBAAQ;gBAAE,GAD1BS,KAAK;kBAAAjT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEX;cAAC,GAPG8S,KAAK;gBAAAjT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQI,CAAC;YAErB,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNjD,OAAA;YAAKkD,SAAS,EAAC,oBAAoB;YAAAN,QAAA,gBACjC5C,OAAA;cACEsD,KAAK,EAAE;gBACLsR,OAAO,EAAE,mBAAmB;gBAC5BC,UAAU,EAAE,8BAA8B;gBAC1CtR,MAAM,EAAE,EAAE;gBACVuR,aAAa,EAAE,QAAQ;gBACvBpB,cAAc,EAAE,QAAQ;gBACxBY,UAAU,EAAE,QAAQ;gBACpBS,GAAG,EAAE,KAAK;gBACVvR,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,gBAEF5C,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA,CAAChC,YAAY;kBAACsF,KAAK,EAAE;oBAAEmQ,KAAK,EAAE,OAAO;oBAAEuB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAlS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzDjD,OAAA;kBAAMsD,KAAK,EAAE;oBAAE0R,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE,GAAG;oBAAEC,UAAU,EAAE;kBAAE,CAAE;kBAAAtS,QAAA,GAAC,MAE7D,EAAC,CAAC8E,oBAAoB,gBACpB1H,OAAA;oBACEsD,KAAK,EAAE;sBACLI,KAAK,EAAE,EAAE;sBACTH,MAAM,EAAE,EAAE;sBACV8R,YAAY,EAAE,EAAE;sBAChBH,UAAU,EAAE,EAAE;sBACdI,UAAU,EAAE,SAAS;sBACrB5B,cAAc,EAAE,QAAQ;sBACxBY,UAAU,EAAE,QAAQ;sBACpBS,GAAG,EAAE,CAAC;sBACNvR,OAAO,EAAE;oBACX,CAAE;oBAAAZ,QAAA,eAEF5C,OAAA;sBAAA4C,QAAA,EAAM;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,gBAENjD,OAAA;oBACEsD,KAAK,EAAE;sBACLI,KAAK,EAAE,EAAE;sBACTH,MAAM,EAAE,EAAE;sBACV8R,YAAY,EAAE,EAAE;sBAChBH,UAAU,EAAE,EAAE;sBACdI,UAAU,EAAE,WAAW;sBACvB5B,cAAc,EAAE,QAAQ;sBACxBY,UAAU,EAAE,QAAQ;sBACpBS,GAAG,EAAE,CAAC;sBACNtB,KAAK,EAAE,OAAO;sBACduB,QAAQ,EAAE,EAAE;sBACZxR,OAAO,EAAE;oBACX,CAAE;oBAAAZ,QAAA,eAEF5C,OAAA;sBAAA4C,QAAA,EAAM;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNjD,OAAA;gBACEsD,KAAK,EAAE;kBACL2R,UAAU,EAAE,MAAM;kBAClBzR,OAAO,EAAE,MAAM;kBACfkQ,cAAc,EAAE,QAAQ;kBACxBsB,QAAQ,EAAE;gBACZ,CAAE;gBAAApS,QAAA,eAEF5C,OAAA;kBAAA4C,QAAA,EAAO4G;gBAAW;kBAAA1G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,EACLyE,oBAAoB,gBACnB1H,OAAA;gBACEkU,IAAI,EAAC,QAAQ;gBACbrR,OAAO,EAAEA,CAAA,KAAM+P,mBAAmB,CAAC,CAAE;gBACrCtP,KAAK,EAAE;kBACLI,KAAK,EAAE,GAAG;kBACVH,MAAM,EAAE,EAAE;kBACV8R,YAAY,EAAE,CAAC;kBACfC,UAAU,EAAE,SAAS;kBACrB5B,cAAc,EAAE,QAAQ;kBACxBY,UAAU,EAAE,QAAQ;kBACpBS,GAAG,EAAE,CAAC;kBACNvR,OAAO,EAAE;gBACX,CAAE;gBAAAZ,QAAA,eAEF5C,OAAA;kBAAGsD,KAAK,EAAE;oBAAEmQ,KAAK,EAAE,OAAO;oBAAEuB,QAAQ,EAAE;kBAAG,CAAE;kBAAApS,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,gBAENjD,OAAA;gBACEkU,IAAI,EAAC,QAAQ;gBACbrR,OAAO,EAAEA,CAAA,KAAM+P,mBAAmB,CAAC,CAAE;gBACrCtP,KAAK,EAAE;kBACLI,KAAK,EAAE,GAAG;kBACVH,MAAM,EAAE,EAAE;kBACV8R,YAAY,EAAE,CAAC;kBACfC,UAAU,EAAE,SAAS;kBACrB5B,cAAc,EAAE,QAAQ;kBACxBY,UAAU,EAAE,QAAQ;kBACpBS,GAAG,EAAE,CAAC;kBACNvR,OAAO,EAAE;gBACX,CAAE;gBAAAZ,QAAA,eAEF5C,OAAA;kBAAGsD,KAAK,EAAE;oBAAEmQ,KAAK,EAAE,OAAO;oBAAEuB,QAAQ,EAAE;kBAAG,CAAE;kBAAApS,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNjD,OAAA;cAAKkD,SAAS,EAAC,eAAe;cAACI,KAAK,EAAE;gBAAEI,KAAK,EAAE,GAAG;gBAAEH,MAAM,EAAE,EAAE;gBAAEgS,WAAW,EAAE,EAAE;gBAAEC,YAAY,EAAE,EAAE;gBAAEX,UAAU,EAAE,mBAAmB;gBAAEC,aAAa,EAAE,QAAQ;gBAAEpB,cAAc,EAAE,QAAQ;gBAAEY,UAAU,EAAE,QAAQ;gBAAES,GAAG,EAAE,CAAC;gBAAEvR,OAAO,EAAE;cAAc,CAAE;cAAAZ,QAAA,eAC5O5C,OAAA;gBAAK6C,OAAO,EAAE4I,YAAa;gBAACnI,KAAK,EAAE;kBAAEmS,SAAS,EAAE,QAAQ;kBAAE9B,MAAM,EAAE,SAAS;kBAAEF,KAAK,EAAE,SAAS;kBAAEuB,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE,KAAK;kBAAES,aAAa,EAAE,IAAI;kBAAEC,QAAQ,EAAE;gBAAa,CAAE;gBAAA/S,QAAA,gBAC5K5C,OAAA,CAACjB,WAAW;kBAACuE,KAAK,EAAE;oBAAEmQ,KAAK,EAAE,OAAO;oBAAEuB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAlS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,QAC1D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA/HoD,MAAM;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgI1D,CAAC,EACR,CAAC,CAAC8J,QAAQ,IAAIA,QAAQ,KAAK,EAAE,IAAIF,YAAY,IAAI,CAAC,KAAK3B,kBAAkB,GAAG,CAAC,GAC5E,CAAC+B,oBAAoB,IAAI,CAAC3B,sBAAsB,gBAC9CtL,OAAA;QAAKkD,SAAS,EAAC,cAAc;QAAAN,QAAA,eAC3B5C,OAAA;UAAA4C,QAAA,GAAM,2BAAyB,EAACsI,kBAAkB,EAAC,OAAK;QAAA;UAAApI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,GAEN,IAAI,GACJ,IAAI,EACP8J,QAAQ,IAAIA,QAAQ,KAAK,YAAY,IAAIF,YAAY,IAAI,CAAC,IAAI5B,eAAe,GAAG,CAAC,gBAChFjL,OAAA;QAAKkD,SAAS,EAAC,cAAc;QAAAN,QAAA,EAC1BiK,YAAY,IAAI,CAAC,gBAChB7M,OAAA;UAAA4C,QAAA,GAAM,oEAC2D,EAAC,GAAG,eACnE5C,OAAA;YACEkD,SAAS,EAAC,uBAAuB;YACjCL,OAAO,EAAEA,CAAA,KAAM0E,QAAQ,CAAC,SAAS,CAAE;YAAA3E,QAAA,EACpC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEPjD,OAAA;UAAA4C,QAAA,GAAM,gBACU,EAACiK,YAAY,EAAC,mCAC9B;QAAA;UAAA/J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,GACJ,IAAI,eAERjD,OAAA,CAACiB,SAAS;QAAA2B,QAAA,eACR5C,OAAA;UAAKkD,SAAS,EAAEyH,qBAAqB,CAAC5B,MAAM,GAAG,CAAC,GAAG,4BAA4B,GAAG,qBAAsB;UACtGlG,OAAO,EAAEA,CAAA,KAAMkE,YAAY,CAAC,IAAI,CAAE;UAAAnE,QAAA,gBAElC5C,OAAA;YACE0D,KAAK,EAAE,EAAG;YACVH,MAAM,EAAE,EAAG;YACXI,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXH,KAAK,EAAC,4BAA4B;YAClCiN,EAAE,EAAE/F,qBAAqB,CAAC5B,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG4L,SAAU;YAAA/R,QAAA,gBAE7D5C,OAAA;cACEiU,CAAC,EAAC,oHAAoH;cACtHpQ,MAAM,EAAC,cAAc;cACrBC,WAAW,EAAC,KAAK;cACjBC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC;YAAO;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFjD,OAAA;cACEiU,CAAC,EAAC,uOAAuO;cACzOpQ,MAAM,EAAC,cAAc;cACrBC,WAAW,EAAC,KAAK;cACjBC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC;YAAO;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFjD,OAAA;cACEiU,CAAC,EAAC,4MAA4M;cAC9MpQ,MAAM,EAAC,cAAc;cACrBC,WAAW,EAAC,KAAK;cACjBC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC;YAAO;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFjD,OAAA;cACEiU,CAAC,EAAC,+MAA+M;cACjNpQ,MAAM,EAAC,cAAc;cACrBC,WAAW,EAAC,KAAK;cACjBC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC;YAAO;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFjD,OAAA;cACEiU,CAAC,EAAC,8HAA8H;cAChIpQ,MAAM,EAAC,cAAc;cACrBC,WAAW,EAAC,KAAK;cACjBC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC;YAAO;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACL0H,qBAAqB,CAAC5B,MAAM,GAAG,CAAC,gBAC/B/I,OAAA;YAAKkD,SAAS,EAAC,+BAA+B;YAAAN,QAAA,EAC3C+H,qBAAqB,CAAC5B;UAAM;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,GACJ0R,SAAS;QAAA;UAAA7R,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEZjD,OAAA,CAACF,gBAAgB;QACf6K,qBAAqB,EAAEA,qBAAsB;QAC7C0G,kBAAkB,EAAEA,kBAAmB;QACvCvK,SAAS,EAAEA,SAAU;QACrBC,YAAY,EAAEA;MAAa;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAEFjD,OAAA,CAACsE,SAAS;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAKbjD,OAAA,CAACS,UAAU;QACTsT,GAAG,EAAE1J,WAAY;QACjBhK,OAAO,EAAEA,OAAQ;QACjB6C,SAAS,EAAE7C,OAAO,GAAG,MAAM,GAAG,QAAS;QACvCiD,KAAK,EAAE;UAAE2S,SAAS,EAAE;QAAwB,CAAE;QAAArT,QAAA,eAE9C5C,OAAA,CAACe,WAAW;UAAA6B,QAAA,gBACV5C,OAAA;YAAKkD,SAAS,EAAC,aAAa;YAAAN,QAAA,EACzBvC,OAAO,gBACNL,OAAA,CAACpB,cAAc;cACbiE,OAAO,EAAE2I,aAAc;cACvB5H,IAAI,EAAC,MAAM;cACXV,SAAS,EAAC;YAAuB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,gBAEFjD,OAAA,CAACnB,gBAAgB;cACfgE,OAAO,EAAE2I,aAAc;cACvB5H,IAAI,EAAC,MAAM;cACXV,SAAS,EAAC;YAAwB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEL5C,OAAO,gBACNL,OAAA;YACEsD,KAAK,EAAE;cACL4S,YAAY,EAAE,mBAAmB;cACjCC,eAAe,EAAE,OAAOxY,OAAO,GAAG,CAAC;YACrC,CAAE;YAAAiF,QAAA,gBAEF5C,OAAA;cACEkD,SAAS,EAAC,eAAe;cACzBI,KAAK,EAAE;gBAAEsQ,UAAU,EAAE,KAAK;gBAAEC,aAAa,EAAE;cAAM,CAAE;cAAAjR,QAAA,eAInD5C,OAAA;gBACE8E,GAAG,EACD+D,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACE,MAAM,GAAG,CAAC,GAClCF,OAAO,GACPjL,cACL;gBACD8F,KAAK,EAAE,EAAG;gBACVH,MAAM,EAAE,EAAG;gBACXL,SAAS,EAAC;cAAS;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjD,OAAA,CAACO,OAAO,CAAC;cAAW+C,KAAK,EAAE;gBAAE4R,UAAU,EAAE;cAAO,CAAE;cAAAtS,QAAA,gBAChD5C,OAAA;gBACEsD,KAAK,EAAE;kBACL8S,QAAQ,EAAE,KAAK;kBACfC,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE;gBACd,CAAE;gBAAA1T,QAAA,EAED+F;cAAQ;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACRjD,OAAA;gBAAK+T,GAAG,EAAE3J,cAAe;gBAAAxH,QAAA,gBACvB5C,OAAA,CAAC1C,OAAO,CAACiZ,qBAAqB;kBAC5B1T,OAAO,EAAE4J,eAAgB;kBACzBnJ,KAAK,EAAE;oBACLmQ,KAAK,EAAE,OAAO;oBACdE,MAAM,EAAE,SAAS;oBACjBuB,UAAU,EAAE,MAAM;oBAClBF,QAAQ,EAAE;kBACZ;gBAAE;kBAAAlS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFjD,OAAA,CAACY,gBAAgB;kBAACC,WAAW,EAAEA,WAAY;kBAAA+B,QAAA,gBACzC5C,OAAA;oBACE6C,OAAO,EAAG2T,CAAC,IAAKtK,UAAU,CAACzD,MAAM,CAAE;oBACnCnF,KAAK,EAAE;sBAAEqQ,MAAM,EAAE;oBAAU,CAAE;oBAAA/Q,QAAA,eAE7B5C,OAAA;sBAAA4C,QAAA,EAAG;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACLjD,OAAA;oBACE6C,OAAO,EAAE6J,mBAAoB;oBAC7BpJ,KAAK,EAAE;sBAAEqQ,MAAM,EAAE;oBAAU,CAAE;oBAAA/Q,QAAA,eAE7B5C,OAAA;sBAAA4C,QAAA,EAAG;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACLjD,OAAA;oBAAI6C,OAAO,EAAE4I,YAAa;oBAACnI,KAAK,EAAE;sBAAEqQ,MAAM,EAAE;oBAAU,CAAE;oBAAA/Q,QAAA,eACtD5C,OAAA;sBAAA4C,QAAA,EAAG;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACVjD,OAAA;cACEsD,KAAK,EAAE;gBACLuQ,aAAa,EAAE,KAAK;gBACpBH,cAAc,EAAE,QAAQ;gBACxBlQ,OAAO,EAAE,MAAM;gBACfwR,QAAQ,EAAE,MAAM;gBAChBvB,KAAK,EAAE;cACT,CAAE;cAAA7Q,QAAA,EAEDgG;YAAS;cAAA9F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENjD,OAAA;YACEsD,KAAK,EAAE;cACL2S,SAAS,EAAE,4BAA4B;cACvCC,YAAY,EAAE;YAChB,CAAE;YAAAtT,QAAA,gBAEF5C,OAAA;cACEkD,SAAS,EAAC,eAAe;cACzBI,KAAK,EAAE;gBACLsQ,UAAU,EAAE,EAAE;gBACdC,aAAa,EAAE,CAAC;gBAChB2B,YAAY,EAAE,CAAC;gBACf3B,aAAa,EAAE;cACjB,CAAE;cAAAjR,QAAA,eAGF5C,OAAA;gBACE8E,GAAG,EACD+D,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACE,MAAM,GAAG,CAAC,GAClCF,OAAO,GACPjL,cACL;gBACD8F,KAAK,EAAE,EAAG;gBACVH,MAAM,EAAE,EAAG;gBACXL,SAAS,EAAC,SAAS;gBACnBI,KAAK,EAAE;kBAAEsR,OAAO,EAAE;gBAAM;cAAE;gBAAA9R,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjD,OAAA,CAACO,OAAO;cACNkW,EAAE,EAAC,GAAG;cACNnT,KAAK,EAAE;gBACL,mBAAoB0R,QAAQ,EAAE,KAAK;gBACnCzR,MAAM,EAAE,KAAK,CAAC;cAChB,CAAE;cAAAX,QAAA,gBAEF5C,OAAA;gBACEsD,KAAK,EAAE;kBACL8S,QAAQ,EAAE,KAAK;kBACfC,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE;gBACd,CAAE;gBAAA1T,QAAA,EAED+F;cAAQ;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACRjD,OAAA;gBAAK+T,GAAG,EAAE3J,cAAe;gBAAC9G,KAAK,EAAE;kBAAEE,OAAO,EAAE;gBAAO;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACVjD,OAAA;cACEsD,KAAK,EAAE;gBACLuQ,aAAa,EAAE,MAAM;gBACrBH,cAAc,EAAE,QAAQ;gBACxBlQ,OAAO,EAAE,MAAM;gBACfwR,QAAQ,EAAE,MAAM;gBAChBxR,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,EAEDgG;YAAS;cAAA9F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDjD,OAAA;YACEkD,SAAS,EAAC,mBAAmB;YAC7BI,KAAK,EAAE;cAAEuS,SAAS,EAAE,QAAQ;cAAEtS,MAAM,EAAE;YAAsB,CAAE;YAAAX,QAAA,GAG7D2K,oBAAoB,iBACnBvN,OAAA;cACEkD,SAAS,EAAC,2BAA2B;cACrCL,OAAO,EAAEA,CAAA,KAAM0E,QAAQ,CAAC,uBAAuB,CAAE;cACjDjE,KAAK,EAAE;gBACLgS,UAAU,EAAE,mDAAmD;gBAC/DoB,MAAM,EAAE,MAAM;gBACd9B,OAAO,EAAE,MAAM;gBACfS,YAAY,EAAE,MAAM;gBACpB1B,MAAM,EAAE,SAAS;gBACjBF,KAAK,EAAE,OAAO;gBACdkD,UAAU,EAAE,eAAe;gBAC3BC,MAAM,EAAE,uBAAuB;gBAC/BX,SAAS,EAAE;cACb,CAAE;cACFY,YAAY,EAAGL,CAAC,IAAK;gBACnBA,CAAC,CAACxD,MAAM,CAAC1P,KAAK,CAACwT,SAAS,GAAG,kBAAkB;gBAC7CN,CAAC,CAACxD,MAAM,CAAC1P,KAAK,CAAC2S,SAAS,GAAG,gCAAgC;cAC7D,CAAE;cACFc,YAAY,EAAGP,CAAC,IAAK;gBACnBA,CAAC,CAACxD,MAAM,CAAC1P,KAAK,CAACwT,SAAS,GAAG,eAAe;gBAC1CN,CAAC,CAACxD,MAAM,CAAC1P,KAAK,CAAC2S,SAAS,GAAG,8BAA8B;cAC3D,CAAE;cAAArT,QAAA,gBAEF5C,OAAA;gBAAKsD,KAAK,EAAE;kBAAEE,OAAO,EAAE,MAAM;kBAAE8Q,UAAU,EAAE,QAAQ;kBAAES,GAAG,EAAE;gBAAO,CAAE;gBAAAnS,QAAA,gBACjE5C,OAAA;kBAAKsD,KAAK,EAAE;oBACVgS,UAAU,EAAE,0BAA0B;oBACtCD,YAAY,EAAE,KAAK;oBACnBT,OAAO,EAAE,KAAK;oBACdpR,OAAO,EAAE,MAAM;oBACf8Q,UAAU,EAAE,QAAQ;oBACpBZ,cAAc,EAAE,QAAQ;oBACxBsD,QAAQ,EAAE,MAAM;oBAChBzT,MAAM,EAAE;kBACV,CAAE;kBAAAX,QAAA,eACA5C,OAAA,CAAC5B,YAAY;oBAACkF,KAAK,EAAE;sBAAE0R,QAAQ,EAAE;oBAAO;kBAAE;oBAAAlS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACNjD,OAAA;kBAAKsD,KAAK,EAAE;oBAAE2T,IAAI,EAAE;kBAAE,CAAE;kBAAArU,QAAA,gBACtB5C,OAAA;oBAAKsD,KAAK,EAAE;sBACV0R,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,KAAK;sBACjBZ,YAAY,EAAE,KAAK;sBACnB7Q,OAAO,EAAEnD,OAAO,GAAG,OAAO,GAAG;oBAC/B,CAAE;oBAAAuC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNjD,OAAA;oBAAKsD,KAAK,EAAE;sBACV0R,QAAQ,EAAE,MAAM;sBAChBkC,OAAO,EAAE,KAAK;sBACd1T,OAAO,EAAEnD,OAAO,GAAG,OAAO,GAAG;oBAC/B,CAAE;oBAAAuC,QAAA,GACCyK,8BAA8B,GAAGF,4BAA4B,EAAC,mBACjE;kBAAA;oBAAArK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNjD,OAAA;gBAAKsD,KAAK,EAAE;kBACV6T,SAAS,EAAE,MAAM;kBACjB3T,OAAO,EAAEnD,OAAO,GAAG,OAAO,GAAG;gBAC/B,CAAE;gBAAAuC,QAAA,gBACA5C,OAAA;kBAAKsD,KAAK,EAAE;oBACVgS,UAAU,EAAE,0BAA0B;oBACtCD,YAAY,EAAE,KAAK;oBACnB9R,MAAM,EAAE,KAAK;oBACb8S,QAAQ,EAAE;kBACZ,CAAE;kBAAAzT,QAAA,eACA5C,OAAA;oBAAKsD,KAAK,EAAE;sBACVgS,UAAU,EAAE,OAAO;sBACnB/R,MAAM,EAAE,MAAM;sBACd8R,YAAY,EAAE,KAAK;sBACnB3R,KAAK,EAAE,GAAIyJ,4BAA4B,GAAGE,8BAA8B,GAAI,GAAG,GAAG;sBAClFsJ,UAAU,EAAE;oBACd;kBAAE;oBAAA7T,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNjD,OAAA;kBAAKsD,KAAK,EAAE;oBACVE,OAAO,EAAE,MAAM;oBACfkQ,cAAc,EAAE,eAAe;oBAC/BY,UAAU,EAAE,QAAQ;oBACpB6C,SAAS,EAAE;kBACb,CAAE;kBAAAvU,QAAA,gBACA5C,OAAA;oBAAMsD,KAAK,EAAE;sBAAE0R,QAAQ,EAAE,MAAM;sBAAEkC,OAAO,EAAE;oBAAM,CAAE;oBAAAtU,QAAA,GAC/CuK,4BAA4B,EAAC,GAAC,EAACE,8BAA8B,EAAC,gBACjE;kBAAA;oBAAAvK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPjD,OAAA;oBAAMsD,KAAK,EAAE;sBAAE0R,QAAQ,EAAE,MAAM;sBAAEkC,OAAO,EAAE;oBAAM,CAAE;oBAAAtU,QAAA,GAC/CuI,IAAI,CAACiM,KAAK,CAAEjK,4BAA4B,GAAGE,8BAA8B,GAAI,GAAG,CAAC,EAAC,GACrF;kBAAA;oBAAAvK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAEA1F,WAAW,CAACuY,GAAG,CAAC,CAACvE,IAAI,EAAEwE,KAAK,KAAK;cAChC,oBACE/V,OAAA,CAACtC,cAAc;gBAEbsY,WAAW,EAAE,CAACzY,WAAW,CAACwY,KAAK,CAAC,CAAC/J,UAAU,CAAE;gBAAApJ,QAAA,eAE7C5C,OAAA,CAACxC,OAAO;kBACN+T,IAAI,EAAEA,IAAK;kBAEXjO,KAAK,EAAE;oBAAEgS,UAAU,EAAE;kBAAQ;gBAAE,GAD1BS,KAAK;kBAAAjT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEX;cAAC,GAPG8S,KAAK;gBAAAjT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQI,CAAC;YAErB,CAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA,eACvB,CAAC;AAEP,CAAC;AAAC4C,GAAA,CAp/CID,QAAQ;EAAA,QAYK/D,WAAW,EAeXzE,WAAW;AAAA;AAAAia,IAAA,GA3BxBzR,QAAQ;AAs/Cd,eAAeA,QAAQ;AAAC,IAAAtF,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAyC,GAAA,EAAAsB,IAAA,EAAA0R,IAAA;AAAAC,YAAA,CAAAhX,EAAA;AAAAgX,YAAA,CAAA9W,GAAA;AAAA8W,YAAA,CAAA3W,GAAA;AAAA2W,YAAA,CAAAxW,GAAA;AAAAwW,YAAA,CAAAtW,GAAA;AAAAsW,YAAA,CAAApW,GAAA;AAAAoW,YAAA,CAAAlW,GAAA;AAAAkW,YAAA,CAAA/V,GAAA;AAAA+V,YAAA,CAAA5V,GAAA;AAAA4V,YAAA,CAAA1V,GAAA;AAAA0V,YAAA,CAAAjT,GAAA;AAAAiT,YAAA,CAAA3R,IAAA;AAAA2R,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}