{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\components\\\\LeftMenu\\\\index.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useContext, useRef, useEffect, useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport styled from \"styled-components\";\nimport * as HiIcons from \"react-icons/hi\";\nimport { SidebarData } from \"./SidebarData\";\nimport SubMenu from \"./SubMenu\";\nimport { IconContext } from \"react-icons/lib\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport \"./style.css\";\nimport userDiv from \"../../img/userDiv2.png\";\nimport defaultUserImg from \"../../img/defaultUserImg.png\";\nimport LogoP from \"../../img/logoP.png\";\nimport logoImg from \"../../img/logoBlue.png\";\nimport audioNotify from \"../../assets/audio/soundNotify.mp3\";\nimport { MdStorefront } from \"react-icons/md\";\nimport { RiArrowDropDownLine } from \"react-icons/ri\";\nimport { FaRobot, FaCheck, FaArrowRight } from \"react-icons/fa\";\nimport CryptoJS from \"crypto-js\";\nimport io from \"socket.io-client\";\nimport { ImPrinter } from \"react-icons/im\";\nimport ModalUserImg from \"../ModalAddUserImg\";\nimport ModalEditUser from \"../../components/ModalEditUser\";\nimport ModalLinkCardapio from \"../ModalLinkCardapio\";\nimport ModalLinkCardapioSalao from \"../ModalLinkCardapioSalao\";\nimport { RiMenuFoldFill } from \"react-icons/ri\";\nimport { RiMenuUnfoldFill } from \"react-icons/ri\";\nimport { GiHamburgerMenu } from \"react-icons/gi\";\nimport { MdExitToApp } from \"react-icons/md\";\nimport roundTable from \"../../img/round-table.png\";\nimport entregadorIco from \"../../img/entregador.png\";\nimport { supportsReliableNotifications, isIOS, getDeviceInfo } from \"../../utils/deviceDetection\";\nimport { v4 as uuidv4 } from \"uuid\"; // Biblioteca para gerar IDs únicos\nimport { SidebarContext } from \"../../AppRoutes\";\nimport { AuthContext } from \"../../contexts/auth\";\nimport { getUser, getVinculoEmpresa, updateStatusBotEmpresa, updateCallAtendenteEmpresa, changeStatusLoja, getDaysToExpireLicense, getEmpresaWithObjId } from \"../../services/api\";\nimport { Tooltip, Drawer } from \"antd\";\nimport AtendimentoModal from \"./AtendimentoModal\";\nimport useFilaAtendimento from \"../../hooks/useFilaAtendimento\"; // 🔥 NOVO: Hook de fila de atendimento\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Nav = styled.div`\n  background: white;\n  --background: linear-gradient(to left, #4281ff, #51d2ff);\n  left: ${({\n  sidebar\n}) => sidebar ? \"250px\" : \"100px\"};\n  transition: 150ms;\n  height: 80px;\n  width: ${({\n  sidebar\n}) => sidebar ? \"calc(100% - 250px)\" : \"calc(100% - 100px)\"};\n  display: flex;\n  --justify-content: flex-start;\n  align-items: center;\n  --border-bottom: 2px solid #0000001c;\n  position: relative;\n  z-index: 10;\n  box-shadow: 1px 1px 6px rgb(180, 180, 180);\n\n  @media (max-width: 880px){\n    left: 0;\n    width: 100%;\n  }\n`;\n\n//const NavIcon = styled(Link)`\n_c = Nav;\nconst NavIcon = styled.div`\n  justify-content: center;\n  font-size: 13px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  color: white;\n  text-decoration: unset;\n  transition: font-size 150ms;\n\n`;\n_c2 = NavIcon;\nconst SidebarNav = styled.nav`\n  background: white;\n  width: ${({\n  sidebar\n}) => sidebar ? \"250px\" : \"100px\"};\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  position: fixed;\n  top: 0;\n  left: 0;\n  transition: width 150ms cubic-bezier(0.4, 0, 0.2, 1); // Ajustado para uma curva bezier comum para movimento\n  z-index: 10;\n  border-right: solid 1px rgb(240, 240, 240);\n\n  span {\n    transition-delay: 200ms;\n    transition-property: font-size, visibility, opacity; // Adicionando propriedades específicas\n    visibility: ${({\n  sidebar\n}) => sidebar ? \"visible\" : \"hidden\"};\n    opacity: ${({\n  sidebar\n}) => sidebar ? \"1\" : \"0\"};\n    font-size: ${({\n  sidebar\n}) => sidebar ? \"14px\" : \"0px\"};\n    transition: visibility 0s, opacity 0.5s,\n      ${({\n  sidebar\n}) => sidebar ? \"font-size 250ms linear\" : \"font-size 100ms linear\"};\n  }\n\n  @media (max-width: 880px) {\n    display: none;\n  }\n`;\n_c3 = SidebarNav;\nconst ModalUserOptions = styled.div`\n  font-size: 14px;\n  position: absolute;\n  top: 82px;\n  left: 150px;\n  display: ${({\n  showOptions\n}) => showOptions ? \"none\" : \"\"};\n  float: left;\n  min-width: 160px;\n  margin: 2px 0 0;\n  padding: 5px 0;\n  list-style: none;\n  text-align: left;\n  border: 1px solid #ccc;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 4px;\n  background-color: #fff;\n  --background-color: rgb(247, 247, 247) !important;\n  background-clip: padding-box;\n  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n  z-index: 25;\n\n  li {\n    font-weight: 400;\n    line-height: 1.42857143;\n    display: block;\n    clear: both;\n    padding: 3px 20px;\n    white-space: nowrap;\n    color: #58595b;\n  }\n  li:hover {\n    background: #f5f5f5;\n  }\n`;\n\n/*\r\nconst OrdercolumnPrint = styled.div`\r\n    min-width: 600px;\r\n    max-width: 600px; \r\n    min-height: 1300px;\r\n    max-height: 1300px;       \r\n    position: absolute;\r\n    top: 10%;\r\n    left: 50%;\r\n    font-size:26px;\r\n    z-index:500\r\n`;*/\n_c4 = ModalUserOptions;\nconst SidebarWrap = styled.div`\n  width: 100%;\n`;\n_c5 = SidebarWrap;\nconst MenuAjuda = styled.div`\n    position: absolute;\n    top: 90px;\n    right: 0px;\n    background: white;\n    height: 60px;\n    width: max-content;\n    border-radius: 10px;\n    box-shadow: 1px 1px 5px 2px lightgray;\n    display: inline-grid;\n    justify-content: center;\n    align-items: center;\n    justify-items: center;\n    z-index:2;\n`;\n_c6 = MenuAjuda;\nconst HelpButtonContainer = styled.div`\n  position: fixed;\n  bottom: 80px;\n  right: 10px;\n  z-index: 14;\n`;\n_c7 = HelpButtonContainer;\nconst HelpButton = styled.button`\n  background: #007bff;\n  color: white;\n  font-size: 24px;\n  border: none;\n  border-radius: 50%;\n  width: 50px;\n  height: 50px;\n  cursor: pointer;\n  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);\n  transition: transform 0.2s;\n  \n  &:hover {\n    transform: scale(1.1);\n  }\n`;\n_c8 = HelpButton;\nconst HelpModal = styled.div`\n  position: absolute;\n  bottom: 60px;\n  right: 0;\n  background: white;\n  width: 220px;\n  padding: 10px;\n  border-radius: 10px;\n  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);\n  text-align: center;\n  display: ${({\n  show\n}) => show ? \"block\" : \"none\"};\n`;\n_c9 = HelpModal;\nconst SupportButton = styled.button`\n  background: #28a745;\n  color: white;\n  border: none;\n  padding: 8px 12px;\n  border-radius: 5px;\n  margin-top: 10px;\n  cursor: pointer;\n  width: 100%;\n  font-size: 14px;\n\n  &:hover {\n    background: #218838;\n  }\n`;\n\n// Hook para detectar mobile\n_c0 = SupportButton;\nfunction useIsMobile() {\n  _s();\n  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);\n  React.useEffect(() => {\n    const onResize = () => setIsMobile(window.innerWidth <= 768);\n    window.addEventListener('resize', onResize);\n    return () => window.removeEventListener('resize', onResize);\n  }, []);\n  return isMobile;\n}\n_s(useIsMobile, \"IPgBv7VuYiSHCPyFLng5ZxH+1OA=\");\nconst HelpWidget = () => {\n  _s2();\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const handleSupportClick = () => {\n    window.open(\"https://api.whatsapp.com/send?phone=+5562999677687&text=Olá, preciso de suporte!\", \"_blank\");\n  };\n  return /*#__PURE__*/_jsxDEV(HelpButtonContainer, {\n    children: [/*#__PURE__*/_jsxDEV(HelpButton, {\n      onClick: () => setIsModalOpen(!isModalOpen),\n      children: \"?\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HelpModal, {\n      show: isModalOpen,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"closeModalPedido\",\n        onClick: () => setIsModalOpen(!isModalOpen),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          iconname: \"x\",\n          iconcolor: \"#2B2B2B\",\n          iconsize: 18,\n          className: \"iconCancel\",\n          style: {\n            height: 18,\n            display: \"flex\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: 18,\n            height: 18,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"#2B2B2B\",\n            strokeWidth: 2,\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            className: \"feather feather-x\",\n            children: [/*#__PURE__*/_jsxDEV(\"line\", {\n              x1: 18,\n              y1: 6,\n              x2: 6,\n              y2: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: 6,\n              y1: 6,\n              x2: 18,\n              y2: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Necessita de Ajuda?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SupportButton, {\n        onClick: handleSupportClick,\n        children: \"Falar com o suporte\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 253,\n    columnNumber: 5\n  }, this);\n};\n_s2(HelpWidget, \"mLsII5HRP5G63IA/8vjZ5YHXWr8=\");\n_c1 = HelpWidget;\nconst CrispChat = () => {\n  _s3();\n  const {\n    user\n  } = useContext(AuthContext);\n  useEffect(() => {\n    if (!window.$crisp) {\n      window.$crisp = [];\n      window.CRISP_WEBSITE_ID = \"b90fc97a-9b0e-45d4-bf0b-94597c4c9f1e\";\n      const script = document.createElement(\"script\");\n      script.src = \"https://client.crisp.chat/l.js\";\n      script.async = true;\n      document.head.appendChild(script);\n      script.onload = () => {\n        console.log(\"✅ Crisp Chat carregado\");\n\n        // 🔹 Define a posição do botão (bottom-right)\n        window.$crisp.push([\"config\", \"position\", [\"bottom\", \"right\"]]);\n\n        // 🔹 Ajusta o z-index do widget Crisp\n        const crispButton = document.querySelector(\".crisp-client\");\n        if (crispButton) {\n          crispButton.style.zIndex = \"14\";\n        }\n\n        // 🔹 Identifica o usuário no Crisp\n        if (user) {\n          console.log(\"📌 Definindo usuário no Crisp:\", user.name, user.email);\n          window.$crisp.push([\"set\", \"user:nickname\", [user.name]]);\n          window.$crisp.push([\"set\", \"user:email\", [user.email]]);\n        }\n      };\n    }\n  }, []);\n  return null; // 🔹 Não renderiza NENHUM botão extra, apenas embute o Crisp Chat\n};\n_s3(CrispChat, \"0ttrc2NAmbTIqftZMPF4nLu0yHA=\");\n_c10 = CrispChat;\nconst LeftMenu = (/*{setSidebar , sidebar}*/\n) => {\n  _s4();\n  var _userParse$user_img;\n  const {\n    sidebar,\n    setSidebar\n  } = useContext(SidebarContext);\n  const socketRef = useRef(null);\n  const [showEditUser, setEditUser] = useState(true);\n  const [_idUserEdit, set_idUserEdit] = useState(\"\");\n  const [usernameEdit, setUsernameEdit] = useState(\"\");\n  const [emailEdit, setEmailEdit] = useState(\"\");\n  const [roleEdit, setRoleEdit] = useState(\"\");\n  const [refresh, setRefresh] = useState(false);\n  const [open, setOpen] = useState(false);\n  const [modalOpen, setModalOpen] = useState(false);\n  const isMobile = useIsMobile();\n  const showDrawer = () => {\n    setOpen(true);\n  };\n  const onClose = () => {\n    setOpen(false);\n  };\n  const INITIAL_DATA = {\n    value: \"\",\n    label: \"Selecione uma empresa\"\n  };\n  const [selectData, setselectData] = useState(INITIAL_DATA);\n  const navigate = useNavigate();\n  const [dropstatusrobo, setDropStatusRobo] = useState(false);\n  const [statusLojaTemporario, setStatusLojaTemporario] = useState(false);\n  const {\n    logout\n  } = useContext(AuthContext);\n  const secretKey = \"my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be\";\n  const userEncrypted = localStorage.getItem(\"user\");\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  const userParse = JSON.parse(user);\n  //console.log(userParse)\n  //const userParse = user;\n  const userID = userParse._id;\n  const userName = userParse.name;\n  const userEmail = userParse.email;\n  var userImg = null;\n  if (((_userParse$user_img = userParse.user_img) === null || _userParse$user_img === void 0 ? void 0 : _userParse$user_img.length) > 0) {\n    userImg = userParse.user_img[0];\n  }\n  const empresa = localStorage.getItem(\"empresa\");\n  const empresaParse = JSON.parse(empresa);\n  const idEmpresa = empresaParse.id_empresa;\n  const objIdEmpresa = empresaParse._id;\n  //console.log(\"🔍 Debug - IDs da empresa:\", {\n  //  idEmpresa: empresaParse.id_empresa,\n  //  objIdEmpresa: empresaParse._id,\n  //  empresaCompleta: empresaParse\n  //});\n  const cnpj = empresaParse.cnpj;\n  const razao = empresaParse.razao;\n  const nomeEmpresa = empresaParse.name;\n  const nomeEmpresaForUrl = nomeEmpresa.replace(/\\s+/g, \"-\").replace(/[A-Z]/g, c => c.toLowerCase());\n  const [showLinkCardapio, setShowLinkCardapio] = useState(false);\n  const [showLinkCardapioSalao, setShowLinkCardapioSalao] = useState(false);\n  const [showOptions, setUserOptions] = useState(true);\n  const [showModalImg, setModalImg] = useState(true);\n  const userOptionsRef = useRef();\n  const leftMenuRef = useRef();\n  const menuStatusRoboRef = useRef();\n  const menuStatusRoboRef_ = useRef();\n  const [statusBot, setStatusBot] = useState(false);\n  const [callAtendente, setCallAtendente] = useState(false);\n  var imageDataURL = null;\n\n  // 🔥 SUBSTITUÍDO: Usando hook customizado para fila de atendimento\n  // const [atendimentosPendentes, setAtendimentosPendentes] = useState([]);\n\n  // 🔥 NOVO: Hook de fila de atendimento com persistência\n  const {\n    atendimentosPendentes,\n    isLoading: isLoadingAtendimentos,\n    error: errorAtendimentos,\n    totalPendentes,\n    iniciarAtendimento,\n    finalizarAtendimento,\n    cancelarAtendimento,\n    cancelarTodosAtendimentos,\n    atualizarFila,\n    removerAtendimento: removerAtendimentoHook\n  } = useFilaAtendimento(objIdEmpresa, socketRef.current);\n\n  // 🔥 Log de debug para verificar funcionamento\n  useEffect(() => {\n    if (atendimentosPendentes.length > 0) {\n      console.log('🎯 LeftMenu: Fila de atendimento atualizada:', {\n        total: totalPendentes,\n        isLoading: isLoadingAtendimentos,\n        error: errorAtendimentos\n      });\n    }\n  }, [totalPendentes, isLoadingAtendimentos, errorAtendimentos]);\n  const userCreatedAt = new Date(userParse.createdAt);\n  const agora = new Date();\n  const diferencaEmDias = (agora - userCreatedAt) / (1000 * 60 * 60 * 24);\n  // Calcular dias de teste restantes (7 dias - diferença desde a criação)\n  const diasTesteRestantes = Math.max(Math.ceil(7 - diferencaEmDias), 0);\n  const [carregandoCheckLicense, setCarregandoCheckLicense] = useState(true);\n  const toggleSidebar = () => {\n    setSidebar(!sidebar);\n  };\n  const handleLogout = () => {\n    logout();\n  };\n  const handleSwitchChange = async () => {\n    const newStatus = !statusBot;\n    setStatusBot(newStatus);\n    console.log(\"Status atualizado:\", newStatus);\n    try {\n      const response = await updateStatusBotEmpresa(objIdEmpresa, newStatus);\n      console.log(\"Resposta do servidor:\", response);\n    } catch (error) {\n      console.error(\"Erro ao atualizar status_bot:\", error);\n    }\n  };\n  const handleCallAtendenteChange = async () => {\n    const newStatus = !callAtendente;\n    setCallAtendente(newStatus);\n    console.log(\"Call atendente atualizado:\", newStatus);\n    try {\n      const response = await updateCallAtendenteEmpresa(objIdEmpresa, newStatus);\n      console.log(\"Resposta do servidor:\", response);\n    } catch (error) {\n      console.error(\"Erro ao atualizar call_atendente:\", error);\n    }\n  };\n\n  // Verifique se o navegador suporta notificações\n  if (!(\"Notification\" in window)) {\n    alert(\"Este navegador não suporta notificações de sistema.\");\n  } else if (Notification.permission !== \"denied\") {\n    // Pede permissão ao usuário\n    Notification.requestPermission();\n  }\n  const handleEdit = async idToEdit => {\n    setEditUser(!showEditUser);\n    setUserOptions(!showOptions);\n    const response = await getUser(idToEdit);\n    //console.log(\"Infos do Edit:\",response.data.user)\n    //console.log(idToEdit,\"----\");\n    if (showEditUser) {\n      set_idUserEdit(idToEdit);\n      setUsernameEdit(response.data.user.name);\n      setEmailEdit(response.data.user.email);\n      setRoleEdit(response.data.user.role);\n      if (response.data.user.vinculo_empresa) {\n        //console.log(\"TEM EMPRESA VINCULADA!\")\n        const responseVinculo = await getVinculoEmpresa(idToEdit);\n        if (responseVinculo.data.vinculo) {\n          setselectData({\n            value: responseVinculo.data.vinculo.id_empresa,\n            label: responseVinculo.data.vinculo.id_empresa + \" - \" + responseVinculo.data.vinculo.cnpj + \" - \" + responseVinculo.data.vinculo.name\n          });\n        }\n        if (!responseVinculo.data.vinculo) {\n          //console.log(\"MSG:\", responseVinculo.data.msg)\n        }\n      } else {\n        //console.log(\"NÃO TEM EMPRESA VINCULADA!\")\n        setselectData({\n          value: \"\",\n          label: \"\"\n        });\n      }\n    }\n  };\n  const showUserOptions = () => {\n    setUserOptions(!showOptions);\n  };\n  const showModalAddUserImg = () => {\n    setUserOptions(!showOptions);\n    setModalImg(!showModalImg);\n  };\n\n  //const [pedidos, setPedidos] = useState([])\n  const [statusPrinter, setStatusPrinter] = useState(\"\");\n  const [daysToExpire, setDaysToExpire] = useState(\"\");\n  const [planType, setPlanType] = useState(\"\");\n  const [semCobrancaOuInvoice, setSemCobrancaOuInvoice] = useState(false);\n\n  // Estados para controlar visibilidade dos avisos de licença\n  const [showTrialAlert, setShowTrialAlert] = useState(true);\n  const [showExpirationAlert, setShowExpirationAlert] = useState(true);\n  const [isTrialAlertFading, setIsTrialAlertFading] = useState(false);\n  const [isExpirationAlertFading, setIsExpirationAlertFading] = useState(false);\n\n  // Estados para \"Comece por aqui\"\n  const [progressoConfiguracaoInicial, setProgressoConfiguracaoInicial] = useState(0);\n  const [totalEtapasConfiguracaoInicial, setTotalEtapasConfiguracaoInicial] = useState(9);\n  const [mostrarComecePorAqui, setMostrarComecePorAqui] = useState(false);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log(\"🔄 Atualizando informações do vínculo da empresa...\");\n\n        // Obtendo o vínculo da empresa\n        const response = await getVinculoEmpresa(userID);\n        setStatusLojaTemporario(response.data.vinculo.fechamento_temporario);\n        setStatusBot(response.data.vinculo.status_bot);\n        setCallAtendente(response.data.vinculo.call_atendente);\n        setStatusPrinter(response.data.vinculo.status_printer);\n\n        // Verificar último check armazenado no localStorage\n        const lastCheckEncrypted = localStorage.getItem(\"ldc\");\n        const lastCheck = lastCheckEncrypted ? CryptoJS.AES.decrypt(lastCheckEncrypted, secretKey).toString(CryptoJS.enc.Utf8) : null;\n        const now = Date.now();\n\n        // Obtendo os dias restantes para expiração da licença\n        const responseCheckLicense = await getDaysToExpireLicense(response.data.vinculo._id);\n        console.log(\"responseCheckLicense:\", responseCheckLicense);\n        if (responseCheckLicense.status === 204) {\n          setSemCobrancaOuInvoice(true);\n        }\n        setDaysToExpire(responseCheckLicense.data.daysRemaining || \"\"); // Definir valor padrão vazio\n        setPlanType(responseCheckLicense.data.plan_type || \"\"); // Definir valor padrão vazio\n\n        // Atualiza o timestamp da última verificação no localStorage\n        const nowEncrypted = CryptoJS.AES.encrypt(JSON.stringify(now), secretKey).toString();\n        localStorage.setItem(\"ldc\", nowEncrypted);\n        setCarregandoCheckLicense(false);\n      } catch (error) {\n        console.error(\"❌ Erro ao obter dados:\", error);\n        setCarregandoCheckLicense(false);\n      }\n    };\n\n    // Chama a função imediatamente ao montar o componente\n    fetchData();\n\n    // Configura o intervalo para executar a cada 1 hora (3600000ms)\n    const intervalId = setInterval(fetchData, 3600000); // 1 hora\n\n    // Cleanup para limpar o intervalo ao desmontar o componente\n    return () => clearInterval(intervalId);\n\n    // eslint-disable-next-line\n  }, []);\n\n  // UseEffect para verificar progresso da configuração inicial\n  useEffect(() => {\n    if (!objIdEmpresa) return;\n    const verificarProgressoConfiguracaoInicial = async () => {\n      try {\n        const response = await getEmpresaWithObjId(objIdEmpresa);\n        const empresa = response.data.empresa;\n        let progressoAtual = 0;\n\n        // Verificar etapas concluídas usando a estrutura correta do banco\n        if (empresa.configuracao_inicial && empresa.configuracao_inicial.etapas_completas) {\n          progressoAtual = empresa.configuracao_inicial.etapas_completas.length;\n        } else if (empresa.progresso_configuracao_inicial) {\n          // Fallback para estrutura antiga\n          progressoAtual = empresa.progresso_configuracao_inicial;\n        }\n        console.log('Progresso configuração inicial:', {\n          progressoAtual,\n          totalEtapas: totalEtapasConfiguracaoInicial,\n          configuracaoInicial: empresa.configuracao_inicial\n        });\n        setProgressoConfiguracaoInicial(progressoAtual);\n\n        // Mostrar \"Comece por aqui\" apenas se não concluiu todas as etapas\n        const configuracaoCompleta = empresa.configuracao_inicial && empresa.configuracao_inicial.finalizada;\n        setMostrarComecePorAqui(!configuracaoCompleta && progressoAtual < totalEtapasConfiguracaoInicial);\n      } catch (error) {\n        console.error(\"Erro ao verificar progresso da configuração inicial:\", error);\n      }\n    };\n    verificarProgressoConfiguracaoInicial();\n  }, [objIdEmpresa, totalEtapasConfiguracaoInicial]);\n\n  // 🔧 Resetar visibilidade dos avisos quando os dados de licença mudarem\n  useEffect(() => {\n    setShowTrialAlert(true);\n    setShowExpirationAlert(true);\n    setIsTrialAlertFading(false);\n    setIsExpirationAlertFading(false);\n  }, [planType, daysToExpire, diasTesteRestantes]);\n\n  // 🔧 Timer para esconder aviso de teste após 5 segundos com fade-out\n  useEffect(() => {\n    if ((!planType || planType === \"\" || daysToExpire == 0) && diasTesteRestantes > 0 && !semCobrancaOuInvoice && !carregandoCheckLicense && showTrialAlert) {\n      const fadeTimer = setTimeout(() => {\n        setIsTrialAlertFading(true);\n      }, 4500); // Começar fade após 4.5 segundos\n\n      const hideTimer = setTimeout(() => {\n        setShowTrialAlert(false);\n        setIsTrialAlertFading(false);\n      }, 5000); // Esconder completamente após 5 segundos\n\n      return () => {\n        clearTimeout(fadeTimer);\n        clearTimeout(hideTimer);\n      };\n    }\n  }, [planType, daysToExpire, diasTesteRestantes, semCobrancaOuInvoice, carregandoCheckLicense, showTrialAlert]);\n\n  // 🔧 Timer para esconder aviso de expiração após 5 segundos com fade-out\n  useEffect(() => {\n    if (planType && planType !== \"free_trial\" && daysToExpire <= 3 && diferencaEmDias > 7 && showExpirationAlert) {\n      const fadeTimer = setTimeout(() => {\n        setIsExpirationAlertFading(true);\n      }, 4500); // Começar fade após 4.5 segundos\n\n      const hideTimer = setTimeout(() => {\n        setShowExpirationAlert(false);\n        setIsExpirationAlertFading(false);\n      }, 5000); // Esconder completamente após 5 segundos\n\n      return () => {\n        clearTimeout(fadeTimer);\n        clearTimeout(hideTimer);\n      };\n    }\n  }, [planType, daysToExpire, diferencaEmDias, showExpirationAlert]);\n\n  // 🔧 Função para fechar manualmente o aviso de teste com animação\n  const closeTrialAlert = () => {\n    setIsTrialAlertFading(true);\n    setTimeout(() => {\n      setShowTrialAlert(false);\n      setIsTrialAlertFading(false);\n    }, 300); // 300ms para a animação\n  };\n\n  // 🔧 Função para fechar manualmente o aviso de expiração com animação\n  const closeExpirationAlert = () => {\n    setIsExpirationAlertFading(true);\n    setTimeout(() => {\n      setShowExpirationAlert(false);\n      setIsExpirationAlertFading(false);\n    }, 300); // 300ms para a animação\n  };\n\n  /*useEffect(() => {\r\n    ////const intervalId = setInterval(() => {\r\n    ////  fetchData().then(newPedidos => setPedidos(newPedidos));\r\n    ////}, 5 * 1000); // Atualizar a cada 5 segundo\r\n      var i;\r\n    if(pedidos){\r\n      for(i=0; i<pedidos.length; i++){\r\n          //console.log(\"tipoImpressao>\",tipoImpressao);\r\n          if(pedidos[i].status_pedido=='2' && tipoImpressao == 'automatico'){\r\n              //console.log(pedidos[i])\r\n              //console.log(\"CHGEOU AUQI?\");\r\n              const orderElement = document.getElementById(`${pedidos[i].id_pedido}`);\r\n                //console.log(orderElement);\r\n              if (orderElement && orderElement.getAttribute('data-status') == \"true\") {\r\n                updateStatusPrint(userID, pedidos[i]._id, pedidos[i].id_pedido).then(printPdf(pedidos[i].id_pedido));\r\n              }\r\n          }\r\n      }\r\n    }\r\n    ////return () => clearInterval(intervalId);\r\n  }, [pedidos]); // Sem dependências, então o efeito será executado apenas uma vez*/\n\n  //const [statusImpressora, setStatusImpressora] = useState('');\n  const isDevelopment = window.location.hostname === \"localhost\";\n  const apiUrl = isDevelopment ? process.env.REACT_APP_SERVER_URL_DEV : process.env.REACT_APP_SERVER_URL_PROD;\n\n  /*useEffect(() => {\r\n    const wsUrl = apiUrl;\r\n    const socket = io(wsUrl, {\r\n      withCredentials: true,\r\n      transports: [\"websocket\"],\r\n      auth: { token: localStorage.getItem(\"token\") },\r\n    });\r\n      // **Entrar na sala da empresa correta**\r\n    socket.emit(\"joinCompanyRoom\", {\r\n      companyId: idEmpresa.toString(),\r\n      clientId: \"reactClient\",\r\n    });\r\n      // **Verificar conexão**\r\n    socket.on(\"connect\", () => {\r\n      console.log(`✅ Conectado ao WebSocket! Socket ID: ${socket.id}`);\r\n    });\r\n      console.log(`📢 Entrando na sala da empresa: ${objIdEmpresa.toString()}`);\r\n      // **Monitorar todos os eventos recebidos no socket**\r\n    socket.onAny((event, data) => {\r\n      console.log(`📥 Evento recebido no frontend: ${event}`, data);\r\n    });\r\n      // **Escutando novos pedidos**\r\n    socket.on(\"novoPedido\", (data) => {\r\n      console.log(\"📦 NOVO PEDIDO RECEBIDO!\");\r\n      handleNotify();\r\n    });\r\n      // **Escutando status da impressora**\r\n    socket.on(\"statusUpdate\", ({ companyId: updatedCompanyId, status }) => {\r\n      if (objIdEmpresa.toString() === updatedCompanyId) {\r\n        console.log(`🖨️ Status atualizado da impressora: ${status}`);\r\n        setStatusPrinter(status === \"Online\" ? \"Online\" : \"Offline\");\r\n      }\r\n    });\r\n      // **Escutando solicitações de atendimento humano**\r\n    socket.on(\"atendimento_pendente\", (data) => {\r\n      console.log(\"🚨 Novo atendimento solicitado! Dados recebidos:\", data);\r\n        // **Verificar se a empresa corresponde**\r\n      console.log(`Comparando company_id recebido (${data.company_id}) com idEmpresa (${idEmpresa.toString()})`);\r\n        // **📌 Gerar um ID único para cada solicitação**\r\n      const atendimentoComID = {\r\n        ...data,\r\n        atendimento_id: uuidv4(), // Gerando um ID único para cada solicitação\r\n      };\r\n        setAtendimentosPendentes((prev) => [...prev, atendimentoComID]);\r\n    });\r\n      return () => {\r\n      socket.off(\"novoPedido\");\r\n      socket.off(\"statusUpdate\");\r\n      socket.off(\"atendimento_pendente\");\r\n      socket.disconnect();\r\n    };\r\n  }, [idEmpresa]);*/\n  useEffect(() => {\n    if (!objIdEmpresa) return;\n\n    // Se já existir uma conexão WebSocket, desconecta antes de criar outra\n    if (socketRef.current) {\n      console.log(\"🔄 Desconectando socket anterior...\");\n      socketRef.current.disconnect();\n    }\n    console.log(`🔌 Conectando ao WebSocket para a empresa ${objIdEmpresa.toString()}...`);\n\n    // Criar a conexão WebSocket com lógica de reconexão\n    const socket = io(apiUrl, {\n      withCredentials: true,\n      transports: [\"websocket\"],\n      auth: {\n        token: localStorage.getItem(\"token\")\n      },\n      reconnection: true,\n      // Ativa a reconexão automática\n      reconnectionAttempts: 10,\n      // Máximo de 10 tentativas\n      reconnectionDelay: 5000 // Intervalo de 5 segundos entre tentativas\n    });\n\n    // Armazena a referência globalmente\n    socketRef.current = socket;\n\n    // **Entrar na sala da empresa**\n    socket.emit(\"joinCompanyRoom\", {\n      companyId: objIdEmpresa.toString(),\n      clientId: \"reactClient\"\n    });\n\n    // **Solicitar status da impressora ao conectar**\n    socket.on(\"connect\", () => {\n      console.log(`✅ Conectado ao WebSocket! Socket ID: ${socket.id}`);\n      socket.emit(\"statusRequest\", {\n        companyId: objIdEmpresa.toString()\n      }); // 🔹 Garante que o status seja atualizado\n    });\n\n    // **Monitorar tentativas de reconexão**\n    socket.on(\"reconnect_attempt\", attempt => {\n      console.log(`🔄 Tentativa de reconexão (${attempt}/10)...`);\n    });\n    socket.on(\"reconnect\", attempt => {\n      console.log(`✅ Reconectado ao WebSocket após ${attempt} tentativas!`);\n      socket.emit(\"statusRequest\", {\n        companyId: objIdEmpresa.toString()\n      }); // 🔹 Atualiza status após reconectar\n    });\n\n    // **Monitorar erro de conexão**\n    socket.on(\"connect_error\", error => {\n      console.error(\"❌ Erro na conexão WebSocket:\", error);\n    });\n\n    // **Monitorar eventos recebidos**\n    socket.onAny((event, data) => {\n      //console.log(`📥 Evento recebido: ${event}`, data);\n    });\n\n    // **Escutando novos pedidos**\n    socket.on(\"novoPedido\", () => {\n      console.log(\"📦 NOVO PEDIDO RECEBIDO!\");\n      handleNotify();\n    });\n\n    // **Escutando status da impressora**\n    socket.on(\"statusUpdate\", ({\n      companyId: updatedCompanyId,\n      status\n    }) => {\n      if (objIdEmpresa.toString() === updatedCompanyId) {\n        console.log(`🖨️ Status atualizado da impressora: ${status}`);\n        setStatusPrinter(status === \"Online\" ? \"Online\" : \"Offline\");\n      }\n    });\n\n    // 🔥 DESABILITADO: Hook customizado já gerencia atendimentos via socket + persistência\n    // **Escutando solicitações de atendimento humano**\n    // socket.on(\"atendimento_pendente\", (data) => {\n    //   console.log(\"🚨 Novo atendimento solicitado! Dados recebidos:\", data);\n\n    //   // **Verificar se a empresa corresponde**\n    //   console.log(`Comparando company_id recebido (${data.company_id}) com objIdEmpresa (${objIdEmpresa})`);\n\n    //   if (data.company_id === objIdEmpresa.toString()) {\n    //     console.log(`✔️ Atendimento pertence à empresa ${objIdEmpresa}`);\n\n    //     // **📌 Gerar um ID único para cada solicitação**\n    //     const atendimentoComID = {\n    //       ...data,\n    //       atendimento_id: uuidv4(),\n    //     };\n\n    //     setAtendimentosPendentes((prev) => [...prev, atendimentoComID]);\n    //   } else {\n    //     console.log(`❌ Atendimento não pertence à empresa atual. Recebido: ${data.company_id}, Atual: ${objIdEmpresa}`);\n    //   }\n    // });\n\n    // **Lógica de cleanup ao desmontar ou atualizar empresa**\n    return () => {\n      console.log(\"🛑 Desconectando WebSocket ao desmontar ou mudar empresa...\");\n      socket.off(\"novoPedido\");\n      socket.off(\"statusUpdate\");\n      // socket.off(\"atendimento_pendente\"); // 🔥 DESABILITADO: Hook gerencia isso\n      socket.off(\"reconnect_attempt\");\n      socket.off(\"reconnect\");\n      socket.off(\"connect_error\");\n      socket.disconnect();\n      socketRef.current = null;\n    };\n  }, [objIdEmpresa]);\n\n  // **📌 Função para remover uma solicitação específica (agora usa o hook)**\n  const removerAtendimento = atendimento_id => {\n    // 🔥 NOVO: Usar função do hook que persiste no banco\n    removerAtendimentoHook(atendimento_id, 'Removido pelo usuário via interface');\n    console.log('📌 Atendimento removido:', atendimento_id);\n  };\n\n  // **📌 Função de simulação removida - hook gerencia automaticamente**\n  // const simularAtendimento = () => {\n  //   // Não mais necessária - hook useFilaAtendimento gerencia via API + Socket\n  // };\n\n  const sendNotification = (title, options) => {\n    // Verifica se o usuário aceitou receber notificações\n    if (Notification.permission === \"granted\") {\n      const notification = new Notification(title, {\n        ...options,\n        icon: LogoP // Certifique-se de que o caminho para o ícone está correto\n      });\n\n      // Toca um som quando a notificação é exibida\n      notification.onshow = () => {\n        const audio = new Audio(audioNotify);\n        audio.play().catch(error => console.log(\"Erro ao reproduzir o som da notificação:\", error));\n      };\n    }\n  };\n  const handleNotify = () => {\n    const title = \"Pede Já - Novo Pedido\";\n    const options = {\n      body: \"Você recebeu um novo pedido.\",\n      // O campo 'sound' ainda não é amplamente suportado\n      sound: audioNotify // caminho para o arquivo de áudio\n    };\n    sendNotification(title, options);\n  };\n  const handleOpenCloseLoja = async () => {\n    console.log(\"Fechar Loja\");\n    const newStatus = !statusLojaTemporario;\n    try {\n      const response = await changeStatusLoja(objIdEmpresa, newStatus);\n      if (response.status === 200) {\n        setStatusLojaTemporario(newStatus);\n      } else {\n        console.error(\"Falha ao atualizar o status da loja:\", response);\n      }\n    } catch (error) {\n      console.error(\"Erro ao chamar a API:\", error);\n    }\n  };\n  useEffect(() => {\n    // add when mounted\n    document.addEventListener(\"mousedown\", handleClickOutsideOptions);\n    document.addEventListener(\"mousedown\", handleClickOutsideStatusRobo);\n    // return function to be called when unmounted\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutsideOptions);\n      document.removeEventListener(\"mousedown\", handleClickOutsideStatusRobo);\n    };\n  }, []);\n  const handleClickOutsideOptions = y => {\n    if (!y.target || !userOptionsRef.current) return;\n    if (userOptionsRef.current.contains(y.target)) {\n      return;\n    } else {\n      setUserOptions(true);\n    }\n  };\n  const handleClickOutsideStatusRobo = y => {\n    // Verifica se o event target existe\n    if (!y.target) return;\n\n    // Verifica se o clique foi dentro do botão que abre o dropdown\n    const isClickOnButton = menuStatusRoboRef.current && menuStatusRoboRef.current.contains(y.target);\n\n    // Verifica se o clique foi dentro do dropdown\n    const isClickOnDropdown = menuStatusRoboRef_.current && menuStatusRoboRef_.current.contains(y.target);\n\n    // Se clicou no botão, não faz nada (o toggle já foi tratado)\n    if (isClickOnButton) {\n      return;\n    }\n\n    // Se clicou dentro do dropdown, NUNCA fecha\n    if (isClickOnDropdown) {\n      return; // SEMPRE mantém aberto se clicar dentro do dropdown\n    }\n\n    // Se clicou fora de ambos, fecha o dropdown\n    setDropStatusRobo(false);\n  };\n  useEffect(() => {\n    // add when mounted\n    //document.addEventListener(\"mouseover\", handleMouseOverLeftMenu);\n    //document.addEventListener(\"mouseout\", handleMouseOutLeftMenu);\n\n    window.addEventListener(\"resize\", handleResize);\n    // return function to be called when unmounted\n    return () => {\n      //document.removeEventListener(\"mouseover\", handleMouseOverLeftMenu);\n      //document.removeEventListener(\"mouseout\", handleMouseOutLeftMenu);\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n  const handleResize = () => {\n    const currentWidth = window.innerWidth;\n    if (currentWidth < 1300) {\n      setSidebar(false);\n    }\n  };\n  const handleToggleStatusRobo = () => {\n    setDropStatusRobo(previous => !previous);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(ModalUserImg, {\n      setModalImg: setModalImg,\n      showModalImg: showModalImg,\n      userID: userID\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1038,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalEditUser, {\n      setEditUser: setEditUser,\n      showEditUser: showEditUser,\n      setRefresh: setRefresh,\n      selectData: selectData,\n      setselectData: setselectData,\n      _idUserEdit: _idUserEdit,\n      usernameEdit: usernameEdit,\n      emailEdit: emailEdit,\n      roleEdit: roleEdit,\n      editPerfil: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1044,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalLinkCardapio, {\n      showLinkCardapio: showLinkCardapio,\n      setShowLinkCardapio: setShowLinkCardapio,\n      nomeEmpresaForUrl: nomeEmpresaForUrl\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1057,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalLinkCardapioSalao, {\n      showLinkCardapioSalao: showLinkCardapioSalao,\n      setShowLinkCardapioSalao: setShowLinkCardapioSalao,\n      nomeEmpresaForUrl: nomeEmpresaForUrl\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1063,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IconContext.Provider, {\n      value: {\n        color: \"#fff\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Nav, {\n        sidebar: sidebar,\n        style: {\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex ms-3 align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: showDrawer,\n            className: \"desktop-hidden me-4\",\n            style: {\n              cursor: \"pointer\"\n            },\n            children: /*#__PURE__*/_jsxDEV(GiHamburgerMenu, {\n              color: \"black\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"userCircleImg\",\n            style: {\n              paddingTop: \"4px\",\n              paddingBottom: \"2px\",\n              display: isMobile ? \"none\" : \"block\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: logoImg,\n              alt: \"pede-ja logo\",\n              width: 120,\n              height: 40,\n              className: \"logoImg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1083,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1075,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1071,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: \"5px 24px 5px 24px\",\n              borderLeft: \"1px solid rgb(208, 209, 209)\",\n              height: 80,\n              flexDirection: \"column\",\n              justifyContent: \"center\",\n              alignItems: \"center\",\n              gap: \"2px\",\n              display: \"inline-flex\",\n              position: \"relative\",\n              cursor: \"pointer\"\n            },\n            ref: menuStatusRoboRef,\n            onClick: handleToggleStatusRobo\n            //dropstatusrobo={dropstatusrobo}\n            //className={dropstatusrobo ? 'open' : 'closed'}\n            ,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden-mobile\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: 15,\n                  fontWeight: 500,\n                  marginLeft: 5\n                },\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRobot, {\n              style: {\n                color: \"black\",\n                fontSize: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden-mobile\",\n              style: {\n                fontWeight: \"bold\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                fontSize: 15,\n                position: \"relative\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Robo Pede J\\xE1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(RiArrowDropDownLine, {\n                style: {\n                  color: \"black\",\n                  fontSize: 24\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1131,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: menuStatusRoboRef_,\n              className: dropstatusrobo ? \"menu-dropdownShow\" : \"menu-dropdownClosed\",\n              style: {\n                position: 'absolute'\n              },\n              onClick: e => {\n                e.stopPropagation();\n                e.preventDefault();\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"menu-options\",\n                onClick: e => e.stopPropagation(),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"option bottom\",\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-option\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"status-option online\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1149,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"label-option\",\n                      onClick: handleSwitchChange,\n                      style: {\n                        cursor: 'pointer'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"pedeja-icon\", {\n                        iconname: \"whatsapp\",\n                        iconstroke: 2,\n                        iconcolor: \"#797878\",\n                        iconsize: 18,\n                        \"_nghost-ng-c3181319476\": \"\",\n                        style: {\n                          height: 18\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: 18,\n                          height: 18,\n                          fill: \"#797878\",\n                          viewBox: \"0 0 13 13\",\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M9.477 7.79066C9.31409 7.70819 8.52069 7.32103 8.37241 7.26578C8.22412 7.21297 8.11647 7.18534 8.00841 7.34825C7.90278 7.50709 7.59159 7.87069 7.49653 7.97672C7.40147 8.08275 7.30803 8.09088 7.14756 8.01897C6.98466 7.9365 6.46425 7.76709 5.84634 7.21297C5.36372 6.78356 5.04237 6.25462 4.94691 6.09172C4.85184 5.93084 4.93634 5.83984 5.01678 5.75941C5.09072 5.68547 5.17969 5.57334 5.26216 5.47584C5.34056 5.37834 5.36575 5.31294 5.42303 5.20731C5.47584 5.09316 5.44862 5.00419 5.40841 4.92375C5.36819 4.84331 5.04441 4.04584 4.90912 3.72816C4.77994 3.41291 4.64466 3.45312 4.54512 3.45312C4.45209 3.44459 4.34403 3.44459 4.23638 3.44459C4.12872 3.44459 3.95281 3.48481 3.80453 3.63919C3.65625 3.80209 3.23741 4.19128 3.23741 4.97859C3.23741 5.76794 3.81712 6.53169 3.89756 6.64584C3.98003 6.75147 5.03791 8.37647 6.66087 9.07481C7.04803 9.23772 7.34866 9.33522 7.58347 9.41566C7.97062 9.53834 8.32406 9.52128 8.60316 9.48106C8.91191 9.43028 9.55947 9.08944 9.69516 8.70878C9.83288 8.32569 9.83288 8.00841 9.79266 7.9365C9.75244 7.86256 9.64681 7.82234 9.48391 7.75044L9.477 7.79066ZM6.53372 11.7812H6.52519C5.56441 11.7812 4.61459 11.5208 3.78503 11.0342L3.59044 10.918L1.55919 11.4469L2.10519 9.4705L1.97397 9.26738C1.4375 8.41439 1.1529 7.42722 1.15294 6.41956C1.15294 3.47019 3.56728 1.06437 6.53778 1.06437C7.97672 1.06437 9.32669 1.625 10.3423 2.64062C10.8433 3.13568 11.2407 3.72555 11.5114 4.37582C11.782 5.0261 11.9204 5.72376 11.9186 6.42809C11.9145 9.37544 9.50219 11.7812 6.53575 11.7812H6.53372ZM11.1146 1.86834C9.87878 0.674781 8.25378 0 6.52519 0C2.95994 0 0.056875 2.89047 0.0548438 6.44272C0.0548438 7.57697 0.351 8.68359 0.918125 9.66306L0 13L3.432 12.105C4.38275 12.6176 5.44547 12.8872 6.52559 12.8899H6.52763C10.0949 12.8899 12.998 9.99944 13 6.44475C13 4.72469 12.3293 3.10578 11.1065 1.88906L11.1146 1.86834Z\",\n                            fill: \"#797878\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1166,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1159,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1151,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"description\",\n                        children: \"Whatsapp\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1172,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1150,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1148,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"switch_box box_1\",\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        className: \"switch_1\",\n                        checked: statusBot,\n                        onChange: handleSwitchChange\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1180,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1179,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1178,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"option bottom-last\",\n                  style: {\n                    marginBottom: \"5px\"\n                  },\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-option\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"status-option online\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1195,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"label-option\",\n                      onClick: handleCallAtendenteChange,\n                      style: {\n                        display: \"inline-flex\",\n                        alignItems: \"center\",\n                        cursor: 'pointer'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"pedeja-icon\", {\n                        iconname: \"users\",\n                        iconstroke: 2,\n                        iconcolor: \"#797878\",\n                        iconsize: 18,\n                        \"_nghost-ng-c3181319476\": \"\",\n                        style: {\n                          height: 18\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          width: 18,\n                          height: 18,\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          stroke: \"#797878\",\n                          strokeWidth: 2,\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          className: \"feather feather-users\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1221,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                            cx: 9,\n                            cy: 7,\n                            r: 4\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1222,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M23 21v-2a4 4 0 0 0-3-3.87\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1223,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M16 3.13a4 4 0 0 1 0 7.75\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1224,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1209,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1201,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: \"inline-grid\",\n                          lineHeight: \"17px\"\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"description\",\n                          children: \"Chamar\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1228,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"description\",\n                          children: \"Atendente\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1229,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1227,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1196,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1194,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"switch_box box_1\",\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        className: \"switch_1\",\n                        checked: callAtendente,\n                        onChange: () => handleCallAtendenteChange()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1235,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1234,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1233,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1096,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"bottom\",\n            title: `Impressora: ${statusPrinter === \"Offline\" ? 'Offline' : 'Conectada'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: \"5px 24px 5px 24px\",\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\n                height: 80,\n                flexDirection: \"row\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: \"5px\",\n                display: \"inline-flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(ImPrinter, {\n                color: statusPrinter === \"Offline\" ? 'red' : '#07c670',\n                fontSize: 30\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden-mobile\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: 15,\n                      fontWeight: 500,\n                      marginLeft: 5\n                    },\n                    children: \"Impressora\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1266,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1265,\n                  columnNumber: 19\n                }, this), statusPrinter && statusPrinter === \"Offline\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"divStatusPrintNavBarOffline\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Offline\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1272,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1271,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"divStatusPrintNavBar\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Conectada\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1276,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1275,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1253,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"bottom\",\n            title: `Cardápio Delivery`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: \"5px 24px\",\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\n                height: 80,\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: \"2px\",\n                display: \"inline-flex\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => setShowLinkCardapio(true),\n                style: {\n                  cursor: \"pointer\",\n                  display: \"flex\",\n                  alignItems: \"center\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: entregadorIco,\n                  height: 30,\n                  width: 30\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1306,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex flex-column hidden-mobile\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: 14,\n                      fontWeight: 400,\n                      marginLeft: 5\n                    },\n                    children: \"Link\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1308,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: 14,\n                      fontWeight: 700,\n                      marginLeft: 5\n                    },\n                    children: \"Card\\xE1pio Delivery\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1311,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1307,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1298,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"bottom\",\n            title: `Cardápio Salão`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: \"5px 24px\",\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\n                height: 80,\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: \"2px\",\n                display: \"inline-flex\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => setShowLinkCardapioSalao(true),\n                style: {\n                  cursor: \"pointer\",\n                  display: \"flex\",\n                  alignItems: \"center\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: roundTable,\n                  height: 30,\n                  width: 30\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1343,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex flex-column hidden-mobile\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: 14,\n                      fontWeight: 400,\n                      marginLeft: 5\n                    },\n                    children: \"Link\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1345,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: 14,\n                      fontWeight: 700,\n                      marginLeft: 5\n                    },\n                    children: \"Card\\xE1pio Sal\\xE3o\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1348,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1344,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1323,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: \"5px 24px 5px 24px\",\n              height: 80,\n              flexDirection: \"column\",\n              justifyContent: \"center\",\n              alignItems: \"center\",\n              gap: \"2px\",\n              display: \"inline-flex\",\n              borderLeft: \"1px solid rgb(208, 209, 209)\"\n            },\n            className: \"hidden-sm-mobile\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(MdStorefront, {\n                style: {\n                  color: \"black\",\n                  fontSize: 20\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: 15,\n                  fontWeight: 500,\n                  marginLeft: 5\n                },\n                children: [\"Loja\", !statusLojaTemporario ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: 70,\n                    height: 18,\n                    borderRadius: 10,\n                    marginLeft: 10,\n                    background: \"#9CE8C6\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    gap: 8,\n                    display: \"inline-flex\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Aberta\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1387,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1374,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: 80,\n                    height: 18,\n                    borderRadius: 10,\n                    marginLeft: 10,\n                    background: \"#ff0000b5\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    gap: 8,\n                    color: \"white\",\n                    fontSize: 13,\n                    display: \"inline-flex\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Fechada\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1405,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1390,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1371,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: \"bold\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                fontSize: 15\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: nomeEmpresa\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1418,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1410,\n              columnNumber: 15\n            }, this), statusLojaTemporario ? /*#__PURE__*/_jsxDEV(\"div\", {\n              type: \"button\",\n              onClick: () => handleOpenCloseLoja(),\n              style: {\n                width: 150,\n                height: 20,\n                borderRadius: 5,\n                background: \"#318CD5\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: 8,\n                display: \"inline-flex\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                style: {\n                  color: \"white\",\n                  fontSize: 10\n                },\n                children: \"Abrir Loja\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1435,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1421,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              type: \"button\",\n              onClick: () => handleOpenCloseLoja(),\n              style: {\n                width: 150,\n                height: 20,\n                borderRadius: 5,\n                background: \"#318CD5\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: 8,\n                display: \"inline-flex\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                style: {\n                  color: \"white\",\n                  fontSize: 10\n                },\n                children: \"Fechar Loja\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1452,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1438,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden-mobile\",\n            style: {\n              width: 128,\n              height: 80,\n              paddingLeft: 24,\n              paddingRight: 24,\n              borderLeft: '1px #D0D1D1 solid',\n              flexDirection: 'column',\n              justifyContent: 'center',\n              alignItems: 'center',\n              gap: 8,\n              display: 'inline-flex'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: handleLogout,\n              style: {\n                textAlign: 'center',\n                cursor: \"pointer\",\n                color: '#001B30',\n                fontSize: 18,\n                fontWeight: '700',\n                letterSpacing: 0.48,\n                wordWrap: 'break-word'\n              },\n              children: [/*#__PURE__*/_jsxDEV(MdExitToApp, {\n                style: {\n                  color: 'black',\n                  fontSize: 25\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1458,\n                columnNumber: 17\n              }, this), \"Sair\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1457,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1456,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1086,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1070,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n        title: \"Navega\\xE7\\xE3o\",\n        onClose: onClose,\n        open: open,\n        placement: \"left\",\n        bodyStyle: {\n          padding: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-column justify-content-between h-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"boxLeftMenuScroll\",\n            style: {\n              overflowY: \"scroll\",\n              height: \"calc(100vh - 120px)\"\n            },\n            children: SidebarData.map((item, index) => {\n              return /*#__PURE__*/_jsxDEV(PermissionGate, {\n                permissions: [SidebarData[index].permission],\n                children: /*#__PURE__*/_jsxDEV(SubMenu, {\n                  item: item,\n                  style: {\n                    background: \"black\"\n                  }\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1475,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1471,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex flex-column\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: \"5px 24px 5px 24px\",\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\n                height: 80,\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                gap: \"2px\",\n                display: \"inline-flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(MdStorefront, {\n                  style: {\n                    color: \"black\",\n                    fontSize: 20\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1498,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: 15,\n                    fontWeight: 500,\n                    marginLeft: 5\n                  },\n                  children: [\"Loja\", !statusLojaTemporario ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: 70,\n                      height: 18,\n                      borderRadius: 10,\n                      marginLeft: 10,\n                      background: \"#9CE8C6\",\n                      justifyContent: \"center\",\n                      alignItems: \"center\",\n                      gap: 8,\n                      display: \"inline-flex\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Aberta\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1515,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1502,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: 80,\n                      height: 18,\n                      borderRadius: 10,\n                      marginLeft: 10,\n                      background: \"#ff0000b5\",\n                      justifyContent: \"center\",\n                      alignItems: \"center\",\n                      gap: 8,\n                      color: \"white\",\n                      fontSize: 13,\n                      display: \"inline-flex\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Fechada\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1533,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1518,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1499,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1497,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: \"bold\",\n                  display: \"flex\",\n                  justifyContent: \"center\",\n                  fontSize: 15\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: nomeEmpresa\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1546,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1538,\n                columnNumber: 17\n              }, this), statusLojaTemporario ? /*#__PURE__*/_jsxDEV(\"div\", {\n                type: \"button\",\n                onClick: () => handleOpenCloseLoja(),\n                style: {\n                  width: 150,\n                  height: 20,\n                  borderRadius: 5,\n                  background: \"#318CD5\",\n                  justifyContent: \"center\",\n                  alignItems: \"center\",\n                  gap: 8,\n                  display: \"inline-flex\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  style: {\n                    color: \"white\",\n                    fontSize: 10\n                  },\n                  children: \"Abrir Loja\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1563,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1549,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                type: \"button\",\n                onClick: () => handleOpenCloseLoja(),\n                style: {\n                  width: 150,\n                  height: 20,\n                  borderRadius: 5,\n                  background: \"#318CD5\",\n                  justifyContent: \"center\",\n                  alignItems: \"center\",\n                  gap: 8,\n                  display: \"inline-flex\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  style: {\n                    color: \"white\",\n                    fontSize: 10\n                  },\n                  children: \"Fechar Loja\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1580,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1566,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden-mobile\",\n              style: {\n                width: 128,\n                height: 80,\n                paddingLeft: 24,\n                paddingRight: 24,\n                borderLeft: '1px #D0D1D1 solid',\n                flexDirection: 'column',\n                justifyContent: 'center',\n                alignItems: 'center',\n                gap: 8,\n                display: 'inline-flex'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: handleLogout,\n                style: {\n                  textAlign: 'center',\n                  cursor: \"pointer\",\n                  color: '#001B30',\n                  fontSize: 18,\n                  fontWeight: '700',\n                  letterSpacing: 0.48,\n                  wordWrap: 'break-word'\n                },\n                children: [/*#__PURE__*/_jsxDEV(MdExitToApp, {\n                  style: {\n                    color: 'black',\n                    fontSize: 25\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1586,\n                  columnNumber: 19\n                }, this), \"Sair\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1585,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1584,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1484,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1464,\n          columnNumber: 11\n        }, this)\n      }, \"left\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 1463,\n        columnNumber: 9\n      }, this), (!planType || planType === \"\" || daysToExpire == 0) && diasTesteRestantes && showTrialAlert ? !semCobrancaOuInvoice && !carregandoCheckLicense ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"info-licenca\",\n        style: {\n          position: 'relative',\n          opacity: isTrialAlertFading ? 0 : 1,\n          transform: isTrialAlertFading ? 'translateY(-10px)' : 'translateY(0)',\n          transition: 'opacity 0.3s ease, transform 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          // Para dar espaço ao botão\n          paddingLeft: '20px',\n          paddingRight: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [\"Dias de teste restantes: \", diasTesteRestantes, \" dias\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1608,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: closeTrialAlert,\n          style: {\n            background: 'rgba(255,255,255,0.9)',\n            border: '1px solid rgba(255,255,255,0.3)',\n            borderRadius: '50%',\n            width: '24px',\n            height: '24px',\n            cursor: 'pointer',\n            fontSize: '16px',\n            fontWeight: 'bold',\n            color: '#333',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            transition: 'all 0.2s ease',\n            marginLeft: '10px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',\n            flexShrink: 0\n          },\n          onMouseOver: e => {\n            e.target.style.background = 'rgba(255,255,255,1)';\n            e.target.style.color = '#000';\n            e.target.style.transform = 'scale(1.1)';\n          },\n          onMouseOut: e => {\n            e.target.style.background = 'rgba(255,255,255,0.9)';\n            e.target.style.color = '#333';\n            e.target.style.transform = 'scale(1)';\n          },\n          title: \"Fechar aviso\",\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1611,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1594,\n        columnNumber: 13\n      }, this) : null : null, planType && planType !== \"free_trial\" && daysToExpire <= 3 && diferencaEmDias > 7 && showExpirationAlert ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"info-licenca\",\n        style: {\n          position: 'relative',\n          opacity: isExpirationAlertFading ? 0 : 1,\n          transform: isExpirationAlertFading ? 'translateY(-10px)' : 'translateY(0)',\n          transition: 'opacity 0.3s ease, transform 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          // Para dar espaço ao botão\n          paddingLeft: '20px',\n          paddingRight: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            textAlign: 'center'\n          },\n          children: daysToExpire <= 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Hoje seu plano ir\\xE1 expirar, para realizar o pagamento da fatura\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"clickNavigateToPlanos\",\n              onClick: () => navigate(\"/planos\"),\n              children: \"CLIQUE AQUI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1668,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1666,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Faltam apenas \", daysToExpire, \" dias para sua assinatura expirar\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1676,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1664,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: closeExpirationAlert,\n          style: {\n            background: 'rgba(255,255,255,0.9)',\n            border: '1px solid rgba(255,255,255,0.3)',\n            borderRadius: '50%',\n            width: '24px',\n            height: '24px',\n            cursor: 'pointer',\n            fontSize: '16px',\n            fontWeight: 'bold',\n            color: '#333',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            transition: 'all 0.2s ease',\n            marginLeft: '10px',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',\n            flexShrink: 0\n          },\n          onMouseOver: e => {\n            e.target.style.background = 'rgba(255,255,255,1)';\n            e.target.style.color = '#000';\n            e.target.style.transform = 'scale(1.1)';\n          },\n          onMouseOut: e => {\n            e.target.style.background = 'rgba(255,255,255,0.9)';\n            e.target.style.color = '#333';\n            e.target.style.transform = 'scale(1)';\n          },\n          title: \"Fechar aviso\",\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1681,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1650,\n        columnNumber: 11\n      }, this) : null, /*#__PURE__*/_jsxDEV(MenuAjuda, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: atendimentosPendentes.length > 0 ? \"item-menu-help-info-active\" : \"item-menu-help-info\",\n          onClick: () => setModalOpen(true),\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: 30,\n            height: 30,\n            viewBox: \"0 0 30 30\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: atendimentosPendentes.length > 0 ? \"atendimento-icon-active\" : \"atendimento-icon-inactive\",\n            id: atendimentosPendentes.length > 0 ? \"waving-animation\" : undefined,\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"15\",\n              cy: \"15\",\n              r: \"14\",\n              fill: atendimentosPendentes.length > 0 ? \"url(#activeGradient)\" : \"url(#inactiveGradient)\",\n              stroke: atendimentosPendentes.length > 0 ? \"#318CD5\" : \"#e1e1e1\",\n              strokeWidth: \"1\",\n              opacity: atendimentosPendentes.length > 0 ? \"1\" : \"0.7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1732,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"defs\", {\n              children: [/*#__PURE__*/_jsxDEV(\"linearGradient\", {\n                id: \"activeGradient\",\n                x1: \"0%\",\n                y1: \"0%\",\n                x2: \"100%\",\n                y2: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"0%\",\n                  style: {\n                    stopColor: \"#318CD5\",\n                    stopOpacity: 0.1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1745,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"100%\",\n                  style: {\n                    stopColor: \"#4facfe\",\n                    stopOpacity: 0.2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1746,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1744,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n                id: \"inactiveGradient\",\n                x1: \"0%\",\n                y1: \"0%\",\n                x2: \"100%\",\n                y2: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"0%\",\n                  style: {\n                    stopColor: \"#f8f9fa\",\n                    stopOpacity: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1749,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"100%\",\n                  style: {\n                    stopColor: \"#e9ecef\",\n                    stopOpacity: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1750,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1748,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n                id: \"personGradient\",\n                x1: \"0%\",\n                y1: \"0%\",\n                x2: \"0%\",\n                y2: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"0%\",\n                  style: {\n                    stopColor: atendimentosPendentes.length > 0 ? \"#ffffff\" : \"#6c757d\",\n                    stopOpacity: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1753,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"100%\",\n                  style: {\n                    stopColor: atendimentosPendentes.length > 0 ? \"#f8f9fa\" : \"#adb5bd\",\n                    stopOpacity: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1754,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1752,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1743,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n              className: \"main-person\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"15\",\n                cy: \"11\",\n                r: \"3.5\",\n                fill: \"url(#personGradient)\",\n                stroke: atendimentosPendentes.length > 0 ? \"#e9ecef\" : \"#6c757d\",\n                strokeWidth: \"0.8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1761,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M15 14.5C12.5 14.5 9.5 16 9.5 19V22.5C9.5 23 9.8 23.5 10.5 23.5H19.5C20.2 23.5 20.5 23 20.5 22.5V19C20.5 16 17.5 14.5 15 14.5Z\",\n                fill: \"url(#personGradient)\",\n                stroke: atendimentosPendentes.length > 0 ? \"#e9ecef\" : \"#6c757d\",\n                strokeWidth: \"0.8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1771,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n                className: \"waving-arm\",\n                style: {\n                  transformOrigin: \"18px 16px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M18 16L21 13C21.5 12.5 22 12.5 22.5 13C23 13.5 23 14 22.5 14.5L20 17\",\n                  fill: \"none\",\n                  stroke: \"url(#personGradient)\",\n                  strokeWidth: \"1.5\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1780,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"22\",\n                  cy: \"13.5\",\n                  r: \"1.2\",\n                  fill: \"url(#personGradient)\",\n                  className: \"waving-hand\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1789,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1779,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1759,\n              columnNumber: 15\n            }, this), atendimentosPendentes.length > 2 && /*#__PURE__*/_jsxDEV(\"g\", {\n              className: \"extra-person-left\",\n              opacity: \"0.6\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"8\",\n                cy: \"10\",\n                r: \"2.5\",\n                fill: \"url(#personGradient)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1802,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M8 12.5C6.5 12.5 4.5 13.5 4.5 16V19C4.5 19.3 4.7 19.5 5 19.5H11C11.3 19.5 11.5 19.3 11.5 19V16C11.5 13.5 9.5 12.5 8 12.5Z\",\n                fill: \"url(#personGradient)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1803,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1801,\n              columnNumber: 17\n            }, this), atendimentosPendentes.length > 1 && /*#__PURE__*/_jsxDEV(\"g\", {\n              className: \"extra-person-right\",\n              opacity: \"0.7\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"22\",\n                cy: \"20\",\n                r: \"2.5\",\n                fill: \"url(#personGradient)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1813,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M22 22.5C20.5 22.5 18.5 23.5 18.5 26V28.5C18.5 28.8 18.7 29 19 29H25C25.3 29 25.5 28.8 25.5 28.5V26C25.5 23.5 23.5 22.5 22 22.5Z\",\n                fill: \"url(#personGradient)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1814,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1812,\n              columnNumber: 17\n            }, this), atendimentosPendentes.length > 0 && /*#__PURE__*/_jsxDEV(\"g\", {\n              className: \"attention-particles\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"24\",\n                cy: \"8\",\n                r: \"1\",\n                fill: \"#ffffff\",\n                stroke: \"#e9ecef\",\n                strokeWidth: \"0.3\",\n                opacity: \"0.9\",\n                className: \"particle-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1824,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"6\",\n                cy: \"6\",\n                r: \"0.8\",\n                fill: \"#ffffff\",\n                stroke: \"#e9ecef\",\n                strokeWidth: \"0.3\",\n                opacity: \"0.7\",\n                className: \"particle-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1825,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"26\",\n                cy: \"24\",\n                r: \"0.6\",\n                fill: \"#ffffff\",\n                stroke: \"#e9ecef\",\n                strokeWidth: \"0.3\",\n                opacity: \"0.6\",\n                className: \"particle-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1826,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1823,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1722,\n            columnNumber: 13\n          }, this), atendimentosPendentes.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"number-box number-box--active\",\n            children: atendimentosPendentes.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1832,\n            columnNumber: 15\n          }, this) : isLoadingAtendimentos ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"number-box number-box--loading\",\n            title: \"Carregando atendimentos...\",\n            children: \"\\u23F3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1836,\n            columnNumber: 15\n          }, this) : undefined]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1719,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1718,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AtendimentoModal, {\n        atendimentosPendentes: atendimentosPendentes,\n        removerAtendimento: removerAtendimento,\n        iniciarAtendimento: iniciarAtendimento,\n        cancelarTodosAtendimentos: cancelarTodosAtendimentos,\n        modalOpen: modalOpen,\n        setModalOpen: setModalOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1843,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CrispChat, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1852,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SidebarNav, {\n        ref: leftMenuRef,\n        sidebar: sidebar,\n        className: sidebar ? \"open\" : \"closed\",\n        style: {\n          boxShadow: \"1px 1px 6px lightgray\"\n        },\n        children: /*#__PURE__*/_jsxDEV(SidebarWrap, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"collapseDiv\",\n            children: sidebar ? /*#__PURE__*/_jsxDEV(RiMenuFoldFill, {\n              onClick: toggleSidebar,\n              fill: \"gray\",\n              className: \"collapseInLeftMenuBtn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1866,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(RiMenuUnfoldFill, {\n              onClick: toggleSidebar,\n              fill: \"gray\",\n              className: \"collapseOutLeftMenuBtn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1872,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1864,\n            columnNumber: 13\n          }, this), sidebar ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              borderBottom: \"3px solid #4281FF\",\n              backgroundImage: `url(${userDiv})` /*background:\"rgba(0,0,0,0.8)\"*/\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"userCircleImg\",\n              style: {\n                paddingTop: \"4px\",\n                paddingBottom: \"2px\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: userImg !== null && userImg.length > 0 ? userImg : defaultUserImg,\n                width: 60,\n                height: 60,\n                className: \"userImg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1893,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1887,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavIcon /*to='#'*/, {\n              style: {\n                marginLeft: \"30px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  maxWidth: \"80%\",\n                  overflow: \"hidden\",\n                  whiteSpace: \"nowrap\"\n                },\n                children: userName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1905,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: userOptionsRef,\n                children: [/*#__PURE__*/_jsxDEV(HiIcons.HiOutlineDotsVertical, {\n                  onClick: showUserOptions,\n                  style: {\n                    color: \"white\",\n                    cursor: \"pointer\",\n                    marginLeft: \"10px\",\n                    fontSize: \"22px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1915,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ModalUserOptions, {\n                  showOptions: showOptions,\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: e => handleEdit(userID),\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      children: \"Editar Perfil\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1929,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1925,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: showModalAddUserImg,\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      children: \"Editar Imagem\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1935,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1931,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    onClick: handleLogout,\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      children: \"Sair\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1938,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1937,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1924,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1914,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1904,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                paddingBottom: \"2px\",\n                justifyContent: \"center\",\n                display: \"flex\",\n                fontSize: \"12px\",\n                color: \"white\"\n              },\n              children: userEmail\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1943,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1881,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              boxShadow: \"-1px 1px 1px 0px #0000001c\",\n              borderBottom: \"3px solid transparent\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"userCircleImg\",\n              style: {\n                paddingTop: 15,\n                paddingBottom: 5,\n                paddingRight: 3,\n                paddingBottom: 11\n              },\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: userImg !== null && userImg.length > 0 ? userImg : defaultUserImg,\n                width: 80,\n                height: 80,\n                className: \"userImg\",\n                style: {\n                  padding: \"0px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1972,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1962,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavIcon, {\n              to: \"#\",\n              style: {\n                /*display:\"none\",*/fontSize: \"0px\",\n                height: \"0px\" /*visibility:\"hidden\"*/\n              },\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  maxWidth: \"80%\",\n                  overflow: \"hidden\",\n                  whiteSpace: \"nowrap\"\n                },\n                children: userName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1991,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1984,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                paddingBottom: \"10px\",\n                justifyContent: \"center\",\n                display: \"flex\",\n                fontSize: \"12px\",\n                display: \"none\"\n              },\n              children: userEmail\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2001,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1956,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"boxLeftMenuScroll\",\n            style: {\n              overflowY: \"scroll\",\n              height: \"calc(100vh - 120px)\"\n            },\n            children: [mostrarComecePorAqui && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"comece-por-aqui-container\",\n              onClick: () => navigate('/configuracao-inicial'),\n              style: {\n                background: 'linear-gradient(135deg, #4299e1 0%, #667eea 100%)',\n                margin: '10px',\n                padding: '16px',\n                borderRadius: '12px',\n                cursor: 'pointer',\n                color: 'white',\n                transition: 'all 0.3s ease',\n                border: '2px solid transparent',\n                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)'\n              },\n              onMouseEnter: e => {\n                e.target.style.transform = 'translateY(-2px)';\n                e.target.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.15)';\n              },\n              onMouseLeave: e => {\n                e.target.style.transform = 'translateY(0)';\n                e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    borderRadius: '8px',\n                    padding: '8px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    minWidth: '32px',\n                    height: '32px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(FaArrowRight, {\n                    style: {\n                      fontSize: '16px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2055,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2045,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '16px',\n                      fontWeight: '600',\n                      marginBottom: '4px',\n                      display: sidebar ? 'block' : 'none'\n                    },\n                    children: \"Comece por aqui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2058,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '12px',\n                      opacity: '0.9',\n                      display: sidebar ? 'block' : 'none'\n                    },\n                    children: [totalEtapasConfiguracaoInicial - progressoConfiguracaoInicial, \" etapas restantes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2066,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2057,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2044,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '12px',\n                  display: sidebar ? 'block' : 'none'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    borderRadius: '8px',\n                    height: '6px',\n                    overflow: 'hidden'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'white',\n                      height: '100%',\n                      borderRadius: '8px',\n                      width: `${progressoConfiguracaoInicial / totalEtapasConfiguracaoInicial * 100}%`,\n                      transition: 'width 0.3s ease'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2087,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2081,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    marginTop: '6px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '11px',\n                      opacity: '0.8'\n                    },\n                    children: [progressoConfiguracaoInicial, \"/\", totalEtapasConfiguracaoInicial, \" conclu\\xEDdas\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2101,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: '11px',\n                      opacity: '0.8'\n                    },\n                    children: [Math.round(progressoConfiguracaoInicial / totalEtapasConfiguracaoInicial * 100), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2104,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2095,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2077,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2021,\n              columnNumber: 17\n            }, this), SidebarData.map((item, index) => {\n              return /*#__PURE__*/_jsxDEV(PermissionGate, {\n                permissions: [SidebarData[index].permission],\n                children: /*#__PURE__*/_jsxDEV(SubMenu, {\n                  item: item,\n                  style: {\n                    background: \"black\"\n                  }\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2118,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2114,\n                columnNumber: 19\n              }, this);\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2015,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1863,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1857,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1069,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s4(LeftMenu, \"b3g3E4YiPm0BiL9yvzaxDxq+f/E=\", false, function () {\n  return [useIsMobile, useNavigate, useFilaAtendimento];\n});\n_c11 = LeftMenu;\nexport default LeftMenu;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11;\n$RefreshReg$(_c, \"Nav\");\n$RefreshReg$(_c2, \"NavIcon\");\n$RefreshReg$(_c3, \"SidebarNav\");\n$RefreshReg$(_c4, \"ModalUserOptions\");\n$RefreshReg$(_c5, \"SidebarWrap\");\n$RefreshReg$(_c6, \"MenuAjuda\");\n$RefreshReg$(_c7, \"HelpButtonContainer\");\n$RefreshReg$(_c8, \"HelpButton\");\n$RefreshReg$(_c9, \"HelpModal\");\n$RefreshReg$(_c0, \"SupportButton\");\n$RefreshReg$(_c1, \"HelpWidget\");\n$RefreshReg$(_c10, \"CrispChat\");\n$RefreshReg$(_c11, \"LeftMenu\");", "map": {"version": 3, "names": ["React", "useContext", "useRef", "useEffect", "useState", "useNavigate", "styled", "HiIcons", "SidebarData", "SubMenu", "IconContext", "PermissionGate", "userDiv", "defaultUserImg", "LogoP", "logoImg", "audioNotify", "MdStorefront", "RiArrowDropDownLine", "FaRobot", "FaCheck", "FaArrowRight", "CryptoJS", "io", "ImPrinter", "ModalUserImg", "ModalEditUser", "ModalLinkCardapio", "ModalLinkCardapioSalao", "RiMenuFoldFill", "RiMenuUnfoldFill", "GiHamburgerMenu", "MdExitToApp", "roundTable", "entregadorIco", "supportsReliableNotifications", "isIOS", "getDeviceInfo", "v4", "uuidv4", "SidebarContext", "AuthContext", "getUser", "getVinculoEmpresa", "updateStatusBotEmpresa", "updateCallAtendenteEmpresa", "changeStatusLoja", "getDaysToExpireLicense", "getEmpresaWithObjId", "<PERSON><PERSON><PERSON>", "Drawer", "AtendimentoModal", "useFilaAtendimento", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Nav", "div", "sidebar", "_c", "NavIcon", "_c2", "SidebarNav", "nav", "_c3", "ModalUserOptions", "showOptions", "_c4", "SidebarWrap", "_c5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c6", "HelpButtonContainer", "_c7", "HelpButton", "button", "_c8", "HelpModal", "show", "_c9", "SupportButton", "_c0", "useIsMobile", "_s", "isMobile", "setIsMobile", "window", "innerWidth", "onResize", "addEventListener", "removeEventListener", "HelpWidget", "_s2", "isModalOpen", "setIsModalOpen", "handleSupportClick", "open", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "iconname", "iconcolor", "iconsize", "style", "height", "display", "xmlns", "width", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "x1", "y1", "x2", "y2", "_c1", "CrispChat", "_s3", "user", "$crisp", "CRISP_WEBSITE_ID", "script", "document", "createElement", "src", "async", "head", "append<PERSON><PERSON><PERSON>", "onload", "console", "log", "push", "crispButton", "querySelector", "zIndex", "name", "email", "_c10", "LeftMenu", "_s4", "_userParse$user_img", "setSidebar", "socketRef", "showEditUser", "setEditUser", "_idUserEdit", "set_idUserEdit", "usernameEdit", "setUsernameEdit", "emailEdit", "setEmailEdit", "roleEdit", "setRoleEdit", "refresh", "setRefresh", "<PERSON><PERSON><PERSON>", "modalOpen", "setModalOpen", "showDrawer", "onClose", "INITIAL_DATA", "value", "label", "selectData", "setselectData", "navigate", "dropstatus<PERSON><PERSON>", "setDropStatusRobo", "statusLojaTemporario", "setStatusLojaTemporario", "logout", "secret<PERSON>ey", "userEncrypted", "localStorage", "getItem", "AES", "decrypt", "toString", "enc", "Utf8", "userParse", "JSON", "parse", "userID", "_id", "userName", "userEmail", "userImg", "user_img", "length", "empresa", "empresaParse", "idEmpresa", "id_empresa", "objIdEmpresa", "cnpj", "razao", "nomeEmpresa", "nomeEmpresaForUrl", "replace", "c", "toLowerCase", "showLinkCardapio", "setShowLinkCardapio", "showLinkCardapioSalao", "setShowLinkCardapioSalao", "setUserOptions", "showModalImg", "setModalImg", "userOptionsRef", "leftMenuRef", "menuStatusRoboRef", "menuStatusRoboRef_", "statusBot", "setStatusBot", "callAtendente", "setCallAtendente", "imageDataURL", "atendimentosPendentes", "isLoading", "isLoadingAtendimentos", "error", "errorAtendimentos", "totalPendentes", "iniciarAtendimento", "finalizarA<PERSON>imento", "cancelarAtendimento", "cancelarTodosAtendimentos", "atualizarFila", "remover<PERSON><PERSON><PERSON><PERSON>", "removerA<PERSON>imentoHook", "current", "total", "userCreatedAt", "Date", "createdAt", "agora", "diferencaEmDias", "diasTesteRestantes", "Math", "max", "ceil", "carregandoCheckLicense", "setCarregandoCheckLicense", "toggleSidebar", "handleLogout", "handleSwitchChange", "newStatus", "response", "handleCallAtendenteChange", "alert", "Notification", "permission", "requestPermission", "handleEdit", "idToEdit", "data", "role", "vinculo_empresa", "responseVinculo", "vinculo", "showUserOptions", "showModalAddUserImg", "statusPrinter", "setStatusPrinter", "daysToExpire", "setDaysToExpire", "planType", "setPlanType", "semCobrancaOuInvoice", "setSemCobrancaOuInvoice", "showTrialAlert", "setShowTrialAlert", "showExpirationAlert", "setShowExpirationAlert", "isTrialAlertFading", "setIsTrialAlertFading", "isExpirationAlertFading", "setIsExpirationAlertFading", "progressoConfiguracaoInicial", "setProgressoConfiguracaoInicial", "totalEtapasConfiguracaoInicial", "setTotalEtapasConfiguracaoInicial", "mostrarComecePorAqui", "setMostrarComecePorAqui", "fetchData", "fechamento_temporario", "status_bot", "call_atendente", "status_printer", "lastCheckEncrypted", "<PERSON><PERSON><PERSON><PERSON>", "now", "responseCheckLicense", "status", "daysRemaining", "plan_type", "nowEncrypted", "encrypt", "stringify", "setItem", "intervalId", "setInterval", "clearInterval", "verificarProgressoConfiguracaoInicial", "progressoAtual", "configuracao_inicial", "etapas_completas", "progresso_configuracao_inicial", "totalEtapas", "configuracaoInicial", "configuracaoComple<PERSON>", "finalizada", "fadeTimer", "setTimeout", "hide<PERSON><PERSON>r", "clearTimeout", "closeTrialAlert", "closeExpirationAlert", "isDevelopment", "location", "hostname", "apiUrl", "process", "env", "REACT_APP_SERVER_URL_DEV", "REACT_APP_SERVER_URL_PROD", "disconnect", "socket", "withCredentials", "transports", "auth", "token", "reconnection", "reconnectionAttempts", "reconnectionDelay", "emit", "companyId", "clientId", "on", "id", "attempt", "onAny", "event", "handleNotify", "updatedCompanyId", "off", "atendimento_id", "sendNotification", "title", "options", "notification", "icon", "onshow", "audio", "Audio", "play", "catch", "body", "sound", "handleOpenCloseLoja", "handleClickOutsideOptions", "handleClickOutsideStatusRobo", "y", "target", "contains", "isClickOnButton", "isClickOnDropdown", "handleResize", "currentWidth", "handleToggleStatusRobo", "previous", "editPerfil", "Provider", "color", "justifyContent", "cursor", "paddingTop", "paddingBottom", "alt", "padding", "borderLeft", "flexDirection", "alignItems", "gap", "position", "ref", "fontSize", "fontWeight", "marginLeft", "e", "stopPropagation", "preventDefault", "iconstroke", "d", "type", "checked", "onChange", "marginBottom", "cx", "cy", "r", "lineHeight", "placement", "borderRadius", "background", "paddingLeft", "paddingRight", "textAlign", "letterSpacing", "wordWrap", "bodyStyle", "overflowY", "map", "item", "index", "permissions", "opacity", "transform", "transition", "border", "boxShadow", "flexShrink", "onMouseOver", "onMouseOut", "flex", "undefined", "offset", "stopColor", "stopOpacity", "transform<PERSON><PERSON>in", "borderBottom", "backgroundImage", "max<PERSON><PERSON><PERSON>", "overflow", "whiteSpace", "HiOutlineDotsVertical", "to", "margin", "onMouseEnter", "onMouseLeave", "min<PERSON><PERSON><PERSON>", "marginTop", "round", "_c11", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/components/LeftMenu/index.jsx"], "sourcesContent": ["import React, { useContext, useRef, useEffect, useState } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport styled from \"styled-components\";\r\nimport * as HiIcons from \"react-icons/hi\";\r\nimport { SidebarData } from \"./SidebarData\";\r\nimport SubMenu from \"./SubMenu\";\r\nimport { IconContext } from \"react-icons/lib\";\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport \"./style.css\";\r\nimport userDiv from \"../../img/userDiv2.png\";\r\nimport defaultUserImg from \"../../img/defaultUserImg.png\";\r\nimport LogoP from \"../../img/logoP.png\";\r\nimport logoImg from \"../../img/logoBlue.png\";\r\nimport audioNotify from \"../../assets/audio/soundNotify.mp3\";\r\nimport { MdStorefront } from \"react-icons/md\";\r\nimport { RiArrowDropDownLine } from \"react-icons/ri\";\r\nimport { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>rrow<PERSON>ight } from \"react-icons/fa\";\r\nimport CryptoJS from \"crypto-js\";\r\nimport io from \"socket.io-client\";\r\nimport { ImPrinter } from \"react-icons/im\";\r\nimport ModalUserImg from \"../ModalAddUserImg\";\r\nimport ModalEditUser from \"../../components/ModalEditUser\";\r\nimport ModalLinkCardapio from \"../ModalLinkCardapio\";\r\nimport ModalLinkCardapioSalao from \"../ModalLinkCardapioSalao\";\r\nimport { RiMenuFoldFill } from \"react-icons/ri\";\r\nimport { RiMenuUnfoldFill } from \"react-icons/ri\";\r\nimport { GiHamburgerMenu } from \"react-icons/gi\";\r\nimport { MdExitToApp } from \"react-icons/md\";\r\nimport roundTable from \"../../img/round-table.png\"\r\nimport entregadorIco from \"../../img/entregador.png\"\r\nimport { supportsReliableNotifications, isIOS, getDeviceInfo } from \"../../utils/deviceDetection\";\r\nimport { v4 as uuidv4 } from \"uuid\"; // Biblioteca para gerar IDs únicos\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport { AuthContext } from \"../../contexts/auth\";\r\nimport {\r\n  getUser,\r\n  getVinculoEmpresa,\r\n  updateStatusBotEmpresa,\r\n  updateCallAtendenteEmpresa,\r\n  changeStatusLoja,\r\n  getDaysToExpireLicense,\r\n  getEmpresaWithObjId,\r\n} from \"../../services/api\";\r\nimport { Tooltip, Drawer } from \"antd\";\r\nimport AtendimentoModal from \"./AtendimentoModal\";\r\nimport useFilaAtendimento from \"../../hooks/useFilaAtendimento\"; // 🔥 NOVO: Hook de fila de atendimento\r\n\r\nconst Nav = styled.div`\r\n  background: white;\r\n  --background: linear-gradient(to left, #4281ff, #51d2ff);\r\n  left: ${({ sidebar }) => (sidebar ? \"250px\" : \"100px\")};\r\n  transition: 150ms;\r\n  height: 80px;\r\n  width: ${({ sidebar }) =>\r\n    sidebar ? \"calc(100% - 250px)\" : \"calc(100% - 100px)\"};\r\n  display: flex;\r\n  --justify-content: flex-start;\r\n  align-items: center;\r\n  --border-bottom: 2px solid #0000001c;\r\n  position: relative;\r\n  z-index: 10;\r\n  box-shadow: 1px 1px 6px rgb(180, 180, 180);\r\n\r\n  @media (max-width: 880px){\r\n    left: 0;\r\n    width: 100%;\r\n  }\r\n`;\r\n\r\n//const NavIcon = styled(Link)`\r\nconst NavIcon = styled.div`\r\n  justify-content: center;\r\n  font-size: 13px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  color: white;\r\n  text-decoration: unset;\r\n  transition: font-size 150ms;\r\n\r\n`;\r\n\r\nconst SidebarNav = styled.nav`\r\n  background: white;\r\n  width: ${({ sidebar }) => (sidebar ? \"250px\" : \"100px\")};\r\n  height: 100vh;\r\n  display: flex;\r\n  justify-content: center;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  transition: width 150ms cubic-bezier(0.4, 0, 0.2, 1); // Ajustado para uma curva bezier comum para movimento\r\n  z-index: 10;\r\n  border-right: solid 1px rgb(240, 240, 240);\r\n\r\n  span {\r\n    transition-delay: 200ms;\r\n    transition-property: font-size, visibility, opacity; // Adicionando propriedades específicas\r\n    visibility: ${({ sidebar }) => (sidebar ? \"visible\" : \"hidden\")};\r\n    opacity: ${({ sidebar }) => (sidebar ? \"1\" : \"0\")};\r\n    font-size: ${({ sidebar }) => (sidebar ? \"14px\" : \"0px\")};\r\n    transition: visibility 0s, opacity 0.5s,\r\n      ${({ sidebar }) =>\r\n    sidebar ? \"font-size 250ms linear\" : \"font-size 100ms linear\"};\r\n  }\r\n\r\n  @media (max-width: 880px) {\r\n    display: none;\r\n  }\r\n`;\r\n\r\nconst ModalUserOptions = styled.div`\r\n  font-size: 14px;\r\n  position: absolute;\r\n  top: 82px;\r\n  left: 150px;\r\n  display: ${({ showOptions }) => (showOptions ? \"none\" : \"\")};\r\n  float: left;\r\n  min-width: 160px;\r\n  margin: 2px 0 0;\r\n  padding: 5px 0;\r\n  list-style: none;\r\n  text-align: left;\r\n  border: 1px solid #ccc;\r\n  border: 1px solid rgba(0, 0, 0, 0.15);\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  --background-color: rgb(247, 247, 247) !important;\r\n  background-clip: padding-box;\r\n  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\r\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\r\n  z-index: 25;\r\n\r\n  li {\r\n    font-weight: 400;\r\n    line-height: 1.42857143;\r\n    display: block;\r\n    clear: both;\r\n    padding: 3px 20px;\r\n    white-space: nowrap;\r\n    color: #58595b;\r\n  }\r\n  li:hover {\r\n    background: #f5f5f5;\r\n  }\r\n`;\r\n\r\n/*\r\nconst OrdercolumnPrint = styled.div`\r\n    min-width: 600px;\r\n    max-width: 600px; \r\n    min-height: 1300px;\r\n    max-height: 1300px;       \r\n    position: absolute;\r\n    top: 10%;\r\n    left: 50%;\r\n    font-size:26px;\r\n    z-index:500\r\n`;*/\r\n\r\nconst SidebarWrap = styled.div`\r\n  width: 100%;\r\n`;\r\n\r\nconst MenuAjuda = styled.div`\r\n    position: absolute;\r\n    top: 90px;\r\n    right: 0px;\r\n    background: white;\r\n    height: 60px;\r\n    width: max-content;\r\n    border-radius: 10px;\r\n    box-shadow: 1px 1px 5px 2px lightgray;\r\n    display: inline-grid;\r\n    justify-content: center;\r\n    align-items: center;\r\n    justify-items: center;\r\n    z-index:2;\r\n`;\r\n\r\nconst HelpButtonContainer = styled.div`\r\n  position: fixed;\r\n  bottom: 80px;\r\n  right: 10px;\r\n  z-index: 14;\r\n`;\r\n\r\nconst HelpButton = styled.button`\r\n  background: #007bff;\r\n  color: white;\r\n  font-size: 24px;\r\n  border: none;\r\n  border-radius: 50%;\r\n  width: 50px;\r\n  height: 50px;\r\n  cursor: pointer;\r\n  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);\r\n  transition: transform 0.2s;\r\n  \r\n  &:hover {\r\n    transform: scale(1.1);\r\n  }\r\n`;\r\n\r\nconst HelpModal = styled.div`\r\n  position: absolute;\r\n  bottom: 60px;\r\n  right: 0;\r\n  background: white;\r\n  width: 220px;\r\n  padding: 10px;\r\n  border-radius: 10px;\r\n  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);\r\n  text-align: center;\r\n  display: ${({ show }) => (show ? \"block\" : \"none\")};\r\n`;\r\n\r\nconst SupportButton = styled.button`\r\n  background: #28a745;\r\n  color: white;\r\n  border: none;\r\n  padding: 8px 12px;\r\n  border-radius: 5px;\r\n  margin-top: 10px;\r\n  cursor: pointer;\r\n  width: 100%;\r\n  font-size: 14px;\r\n\r\n  &:hover {\r\n    background: #218838;\r\n  }\r\n`;\r\n\r\n// Hook para detectar mobile\r\nfunction useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);\r\n  React.useEffect(() => {\r\n    const onResize = () => setIsMobile(window.innerWidth <= 768);\r\n    window.addEventListener('resize', onResize);\r\n    return () => window.removeEventListener('resize', onResize);\r\n  }, []);\r\n  return isMobile;\r\n}\r\n\r\nconst HelpWidget = () => {\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  const handleSupportClick = () => {\r\n    window.open(\"https://api.whatsapp.com/send?phone=+5562999677687&text=Olá, preciso de suporte!\", \"_blank\");\r\n  };\r\n\r\n  return (\r\n    <HelpButtonContainer>\r\n      <HelpButton onClick={() => setIsModalOpen(!isModalOpen)}>?</HelpButton>\r\n      <HelpModal show={isModalOpen}>\r\n        <div className=\"closeModalPedido\" onClick={() => setIsModalOpen(!isModalOpen)}>\r\n          <div\r\n            iconname=\"x\"\r\n            iconcolor=\"#2B2B2B\"\r\n            iconsize={18}\r\n            className=\"iconCancel\"\r\n            style={{ height: 18, display: \"flex\" }}\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width={18}\r\n              height={18}\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"#2B2B2B\"\r\n              strokeWidth={2}\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              className=\"feather feather-x\"\r\n            >\r\n              <line x1={18} y1={6} x2={6} y2={18} />\r\n              <line x1={6} y1={6} x2={18} y2={18} />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n        <p>Necessita de Ajuda?</p>\r\n        <SupportButton onClick={handleSupportClick}>Falar com o suporte</SupportButton>\r\n      </HelpModal>\r\n    </HelpButtonContainer>\r\n  );\r\n};\r\n\r\nconst CrispChat = () => {\r\n  const { user } = useContext(AuthContext);\r\n  useEffect(() => {\r\n    if (!window.$crisp) {\r\n      window.$crisp = [];\r\n      window.CRISP_WEBSITE_ID = \"b90fc97a-9b0e-45d4-bf0b-94597c4c9f1e\";\r\n\r\n      const script = document.createElement(\"script\");\r\n      script.src = \"https://client.crisp.chat/l.js\";\r\n      script.async = true;\r\n      document.head.appendChild(script);\r\n\r\n      script.onload = () => {\r\n        console.log(\"✅ Crisp Chat carregado\");\r\n\r\n        // 🔹 Define a posição do botão (bottom-right)\r\n        window.$crisp.push([\"config\", \"position\", [\"bottom\", \"right\"]]);\r\n\r\n        // 🔹 Ajusta o z-index do widget Crisp\r\n        const crispButton = document.querySelector(\".crisp-client\");\r\n        if (crispButton) {\r\n          crispButton.style.zIndex = \"14\";\r\n        }\r\n\r\n        // 🔹 Identifica o usuário no Crisp\r\n        if (user) {\r\n          console.log(\"📌 Definindo usuário no Crisp:\", user.name, user.email);\r\n          window.$crisp.push([\"set\", \"user:nickname\", [user.name]]);\r\n          window.$crisp.push([\"set\", \"user:email\", [user.email]]);\r\n        }\r\n      };\r\n    }\r\n\r\n  }, []);\r\n\r\n  return null; // 🔹 Não renderiza NENHUM botão extra, apenas embute o Crisp Chat\r\n};\r\n\r\nconst LeftMenu = (/*{setSidebar , sidebar}*/) => {\r\n  const { sidebar, setSidebar } = useContext(SidebarContext);\r\n  const socketRef = useRef(null);\r\n  const [showEditUser, setEditUser] = useState(true);\r\n  const [_idUserEdit, set_idUserEdit] = useState(\"\");\r\n  const [usernameEdit, setUsernameEdit] = useState(\"\");\r\n  const [emailEdit, setEmailEdit] = useState(\"\");\r\n  const [roleEdit, setRoleEdit] = useState(\"\");\r\n  const [refresh, setRefresh] = useState(false);\r\n  const [open, setOpen] = useState(false);\r\n  const [modalOpen, setModalOpen] = useState(false);\r\n\r\n  const isMobile = useIsMobile();\r\n\r\n  const showDrawer = () => {\r\n    setOpen(true);\r\n  };\r\n\r\n  const onClose = () => {\r\n    setOpen(false);\r\n  };\r\n\r\n  const INITIAL_DATA = {\r\n    value: \"\",\r\n    label: \"Selecione uma empresa\",\r\n  };\r\n  const [selectData, setselectData] = useState(INITIAL_DATA);\r\n  const navigate = useNavigate();\r\n\r\n  const [dropstatusrobo, setDropStatusRobo] = useState(false);\r\n  const [statusLojaTemporario, setStatusLojaTemporario] = useState(false);\r\n\r\n  const { logout } = useContext(AuthContext);\r\n  const secretKey =\r\n    \"my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be\";\r\n\r\n  const userEncrypted = localStorage.getItem(\"user\");\r\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(\r\n    CryptoJS.enc.Utf8\r\n  );\r\n  const userParse = JSON.parse(user);\r\n  //console.log(userParse)\r\n  //const userParse = user;\r\n  const userID = userParse._id;\r\n  const userName = userParse.name;\r\n  const userEmail = userParse.email;\r\n  var userImg = null;\r\n  if (userParse.user_img?.length > 0) {\r\n    userImg = userParse.user_img[0];\r\n  }\r\n\r\n  const empresa = localStorage.getItem(\"empresa\");\r\n  const empresaParse = JSON.parse(empresa);\r\n  const idEmpresa = empresaParse.id_empresa;\r\n  const objIdEmpresa = empresaParse._id;\r\n  //console.log(\"🔍 Debug - IDs da empresa:\", {\r\n  //  idEmpresa: empresaParse.id_empresa,\r\n  //  objIdEmpresa: empresaParse._id,\r\n  //  empresaCompleta: empresaParse\r\n  //});\r\n  const cnpj = empresaParse.cnpj;\r\n  const razao = empresaParse.razao;\r\n  const nomeEmpresa = empresaParse.name;\r\n  const nomeEmpresaForUrl = nomeEmpresa\r\n    .replace(/\\s+/g, \"-\")\r\n    .replace(/[A-Z]/g, (c) => c.toLowerCase());\r\n\r\n  const [showLinkCardapio, setShowLinkCardapio] = useState(false);\r\n  const [showLinkCardapioSalao, setShowLinkCardapioSalao] = useState(false);\r\n  const [showOptions, setUserOptions] = useState(true);\r\n  const [showModalImg, setModalImg] = useState(true);\r\n  const userOptionsRef = useRef();\r\n  const leftMenuRef = useRef();\r\n  const menuStatusRoboRef = useRef();\r\n  const menuStatusRoboRef_ = useRef();\r\n  const [statusBot, setStatusBot] = useState(false);\r\n  const [callAtendente, setCallAtendente] = useState(false);\r\n  var imageDataURL = null;\r\n\r\n  // 🔥 SUBSTITUÍDO: Usando hook customizado para fila de atendimento\r\n  // const [atendimentosPendentes, setAtendimentosPendentes] = useState([]);\r\n\r\n  // 🔥 NOVO: Hook de fila de atendimento com persistência\r\n  const {\r\n    atendimentosPendentes,\r\n    isLoading: isLoadingAtendimentos,\r\n    error: errorAtendimentos,\r\n    totalPendentes,\r\n    iniciarAtendimento,\r\n    finalizarAtendimento,\r\n    cancelarAtendimento,\r\n    cancelarTodosAtendimentos,\r\n    atualizarFila,\r\n    removerAtendimento: removerAtendimentoHook\r\n  } = useFilaAtendimento(objIdEmpresa, socketRef.current);\r\n\r\n  // 🔥 Log de debug para verificar funcionamento\r\n  useEffect(() => {\r\n    if (atendimentosPendentes.length > 0) {\r\n      console.log('🎯 LeftMenu: Fila de atendimento atualizada:', {\r\n        total: totalPendentes,\r\n        isLoading: isLoadingAtendimentos,\r\n        error: errorAtendimentos\r\n      });\r\n    }\r\n  }, [totalPendentes, isLoadingAtendimentos, errorAtendimentos]);\r\n\r\n  const userCreatedAt = new Date(userParse.createdAt);\r\n  const agora = new Date();\r\n  const diferencaEmDias = (agora - userCreatedAt) / (1000 * 60 * 60 * 24);\r\n  // Calcular dias de teste restantes (7 dias - diferença desde a criação)\r\n  const diasTesteRestantes = Math.max(Math.ceil(7 - diferencaEmDias), 0);\r\n  const [carregandoCheckLicense, setCarregandoCheckLicense] = useState(true);\r\n\r\n  const toggleSidebar = () => {\r\n    setSidebar(!sidebar);\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    logout();\r\n  };\r\n\r\n  const handleSwitchChange = async () => {\r\n    const newStatus = !statusBot;\r\n    setStatusBot(newStatus);\r\n    console.log(\"Status atualizado:\", newStatus);\r\n    try {\r\n      const response = await updateStatusBotEmpresa(objIdEmpresa, newStatus);\r\n      console.log(\"Resposta do servidor:\", response);\r\n    } catch (error) {\r\n      console.error(\"Erro ao atualizar status_bot:\", error);\r\n    }\r\n  };\r\n\r\n  const handleCallAtendenteChange = async () => {\r\n    const newStatus = !callAtendente;\r\n    setCallAtendente(newStatus);\r\n    console.log(\"Call atendente atualizado:\", newStatus);\r\n    try {\r\n      const response = await updateCallAtendenteEmpresa(objIdEmpresa, newStatus);\r\n      console.log(\"Resposta do servidor:\", response);\r\n    } catch (error) {\r\n      console.error(\"Erro ao atualizar call_atendente:\", error);\r\n    }\r\n  };\r\n\r\n  // Verifique se o navegador suporta notificações\r\n  if (!(\"Notification\" in window)) {\r\n    alert(\"Este navegador não suporta notificações de sistema.\");\r\n  } else if (Notification.permission !== \"denied\") {\r\n    // Pede permissão ao usuário\r\n    Notification.requestPermission();\r\n  }\r\n\r\n  const handleEdit = async (idToEdit) => {\r\n    setEditUser(!showEditUser);\r\n    setUserOptions(!showOptions);\r\n    const response = await getUser(idToEdit);\r\n    //console.log(\"Infos do Edit:\",response.data.user)\r\n    //console.log(idToEdit,\"----\");\r\n    if (showEditUser) {\r\n      set_idUserEdit(idToEdit);\r\n      setUsernameEdit(response.data.user.name);\r\n      setEmailEdit(response.data.user.email);\r\n      setRoleEdit(response.data.user.role);\r\n\r\n      if (response.data.user.vinculo_empresa) {\r\n        //console.log(\"TEM EMPRESA VINCULADA!\")\r\n        const responseVinculo = await getVinculoEmpresa(idToEdit);\r\n        if (responseVinculo.data.vinculo) {\r\n          setselectData({\r\n            value: responseVinculo.data.vinculo.id_empresa,\r\n            label:\r\n              responseVinculo.data.vinculo.id_empresa +\r\n              \" - \" +\r\n              responseVinculo.data.vinculo.cnpj +\r\n              \" - \" +\r\n              responseVinculo.data.vinculo.name,\r\n          });\r\n        }\r\n        if (!responseVinculo.data.vinculo) {\r\n          //console.log(\"MSG:\", responseVinculo.data.msg)\r\n        }\r\n      } else {\r\n        //console.log(\"NÃO TEM EMPRESA VINCULADA!\")\r\n        setselectData({\r\n          value: \"\",\r\n          label: \"\",\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  const showUserOptions = () => {\r\n    setUserOptions(!showOptions);\r\n  };\r\n\r\n  const showModalAddUserImg = () => {\r\n    setUserOptions(!showOptions);\r\n    setModalImg(!showModalImg);\r\n  };\r\n\r\n  //const [pedidos, setPedidos] = useState([])\r\n  const [statusPrinter, setStatusPrinter] = useState(\"\");\r\n  const [daysToExpire, setDaysToExpire] = useState(\"\");\r\n  const [planType, setPlanType] = useState(\"\");\r\n  const [semCobrancaOuInvoice, setSemCobrancaOuInvoice] = useState(false);\r\n\r\n  // Estados para controlar visibilidade dos avisos de licença\r\n  const [showTrialAlert, setShowTrialAlert] = useState(true);\r\n  const [showExpirationAlert, setShowExpirationAlert] = useState(true);\r\n  const [isTrialAlertFading, setIsTrialAlertFading] = useState(false);\r\n  const [isExpirationAlertFading, setIsExpirationAlertFading] = useState(false);\r\n\r\n  // Estados para \"Comece por aqui\"\r\n  const [progressoConfiguracaoInicial, setProgressoConfiguracaoInicial] = useState(0);\r\n  const [totalEtapasConfiguracaoInicial, setTotalEtapasConfiguracaoInicial] = useState(9);\r\n  const [mostrarComecePorAqui, setMostrarComecePorAqui] = useState(false);\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        console.log(\"🔄 Atualizando informações do vínculo da empresa...\");\r\n\r\n        // Obtendo o vínculo da empresa\r\n        const response = await getVinculoEmpresa(userID);\r\n        setStatusLojaTemporario(response.data.vinculo.fechamento_temporario);\r\n        setStatusBot(response.data.vinculo.status_bot);\r\n        setCallAtendente(response.data.vinculo.call_atendente);\r\n        setStatusPrinter(response.data.vinculo.status_printer);\r\n\r\n        // Verificar último check armazenado no localStorage\r\n        const lastCheckEncrypted = localStorage.getItem(\"ldc\");\r\n        const lastCheck = lastCheckEncrypted\r\n          ? CryptoJS.AES.decrypt(lastCheckEncrypted, secretKey).toString(\r\n            CryptoJS.enc.Utf8\r\n          )\r\n          : null;\r\n        const now = Date.now();\r\n\r\n        // Obtendo os dias restantes para expiração da licença\r\n        const responseCheckLicense = await getDaysToExpireLicense(\r\n          response.data.vinculo._id\r\n        );\r\n\r\n        console.log(\"responseCheckLicense:\", responseCheckLicense);\r\n        if (responseCheckLicense.status === 204) {\r\n          setSemCobrancaOuInvoice(true);\r\n        }\r\n\r\n        setDaysToExpire(responseCheckLicense.data.daysRemaining || \"\"); // Definir valor padrão vazio\r\n        setPlanType(responseCheckLicense.data.plan_type || \"\"); // Definir valor padrão vazio\r\n\r\n        // Atualiza o timestamp da última verificação no localStorage\r\n        const nowEncrypted = CryptoJS.AES.encrypt(\r\n          JSON.stringify(now),\r\n          secretKey\r\n        ).toString();\r\n        localStorage.setItem(\"ldc\", nowEncrypted);\r\n\r\n        setCarregandoCheckLicense(false);\r\n      } catch (error) {\r\n        console.error(\"❌ Erro ao obter dados:\", error);\r\n        setCarregandoCheckLicense(false);\r\n      }\r\n    };\r\n\r\n    // Chama a função imediatamente ao montar o componente\r\n    fetchData();\r\n\r\n    // Configura o intervalo para executar a cada 1 hora (3600000ms)\r\n    const intervalId = setInterval(fetchData, 3600000); // 1 hora\r\n\r\n    // Cleanup para limpar o intervalo ao desmontar o componente\r\n    return () => clearInterval(intervalId);\r\n\r\n    // eslint-disable-next-line\r\n  }, []);\r\n\r\n  // UseEffect para verificar progresso da configuração inicial\r\n  useEffect(() => {\r\n    if (!objIdEmpresa) return;\r\n\r\n    const verificarProgressoConfiguracaoInicial = async () => {\r\n      try {\r\n        const response = await getEmpresaWithObjId(objIdEmpresa);\r\n        const empresa = response.data.empresa;\r\n\r\n        let progressoAtual = 0;\r\n\r\n        // Verificar etapas concluídas usando a estrutura correta do banco\r\n        if (empresa.configuracao_inicial && empresa.configuracao_inicial.etapas_completas) {\r\n          progressoAtual = empresa.configuracao_inicial.etapas_completas.length;\r\n        } else if (empresa.progresso_configuracao_inicial) {\r\n          // Fallback para estrutura antiga\r\n          progressoAtual = empresa.progresso_configuracao_inicial;\r\n        }\r\n\r\n        console.log('Progresso configuração inicial:', {\r\n          progressoAtual,\r\n          totalEtapas: totalEtapasConfiguracaoInicial,\r\n          configuracaoInicial: empresa.configuracao_inicial\r\n        });\r\n\r\n        setProgressoConfiguracaoInicial(progressoAtual);\r\n\r\n        // Mostrar \"Comece por aqui\" apenas se não concluiu todas as etapas\r\n        const configuracaoCompleta = empresa.configuracao_inicial && empresa.configuracao_inicial.finalizada;\r\n        setMostrarComecePorAqui(!configuracaoCompleta && progressoAtual < totalEtapasConfiguracaoInicial);\r\n      } catch (error) {\r\n        console.error(\"Erro ao verificar progresso da configuração inicial:\", error);\r\n      }\r\n    };\r\n\r\n    verificarProgressoConfiguracaoInicial();\r\n  }, [objIdEmpresa, totalEtapasConfiguracaoInicial]);\r\n\r\n  // 🔧 Resetar visibilidade dos avisos quando os dados de licença mudarem\r\n  useEffect(() => {\r\n    setShowTrialAlert(true);\r\n    setShowExpirationAlert(true);\r\n    setIsTrialAlertFading(false);\r\n    setIsExpirationAlertFading(false);\r\n  }, [planType, daysToExpire, diasTesteRestantes]);\r\n\r\n  // 🔧 Timer para esconder aviso de teste após 5 segundos com fade-out\r\n  useEffect(() => {\r\n    if ((!planType || planType === \"\" || daysToExpire == 0) && diasTesteRestantes > 0 && !semCobrancaOuInvoice && !carregandoCheckLicense && showTrialAlert) {\r\n      const fadeTimer = setTimeout(() => {\r\n        setIsTrialAlertFading(true);\r\n      }, 4500); // Começar fade após 4.5 segundos\r\n\r\n      const hideTimer = setTimeout(() => {\r\n        setShowTrialAlert(false);\r\n        setIsTrialAlertFading(false);\r\n      }, 5000); // Esconder completamente após 5 segundos\r\n\r\n      return () => {\r\n        clearTimeout(fadeTimer);\r\n        clearTimeout(hideTimer);\r\n      };\r\n    }\r\n  }, [planType, daysToExpire, diasTesteRestantes, semCobrancaOuInvoice, carregandoCheckLicense, showTrialAlert]);\r\n\r\n  // 🔧 Timer para esconder aviso de expiração após 5 segundos com fade-out\r\n  useEffect(() => {\r\n    if (planType && planType !== \"free_trial\" && daysToExpire <= 3 && diferencaEmDias > 7 && showExpirationAlert) {\r\n      const fadeTimer = setTimeout(() => {\r\n        setIsExpirationAlertFading(true);\r\n      }, 4500); // Começar fade após 4.5 segundos\r\n\r\n      const hideTimer = setTimeout(() => {\r\n        setShowExpirationAlert(false);\r\n        setIsExpirationAlertFading(false);\r\n      }, 5000); // Esconder completamente após 5 segundos\r\n\r\n      return () => {\r\n        clearTimeout(fadeTimer);\r\n        clearTimeout(hideTimer);\r\n      };\r\n    }\r\n  }, [planType, daysToExpire, diferencaEmDias, showExpirationAlert]);\r\n\r\n  // 🔧 Função para fechar manualmente o aviso de teste com animação\r\n  const closeTrialAlert = () => {\r\n    setIsTrialAlertFading(true);\r\n    setTimeout(() => {\r\n      setShowTrialAlert(false);\r\n      setIsTrialAlertFading(false);\r\n    }, 300); // 300ms para a animação\r\n  };\r\n\r\n  // 🔧 Função para fechar manualmente o aviso de expiração com animação\r\n  const closeExpirationAlert = () => {\r\n    setIsExpirationAlertFading(true);\r\n    setTimeout(() => {\r\n      setShowExpirationAlert(false);\r\n      setIsExpirationAlertFading(false);\r\n    }, 300); // 300ms para a animação\r\n  };\r\n\r\n  /*useEffect(() => {\r\n    ////const intervalId = setInterval(() => {\r\n    ////  fetchData().then(newPedidos => setPedidos(newPedidos));\r\n    ////}, 5 * 1000); // Atualizar a cada 5 segundo\r\n\r\n    var i;\r\n    if(pedidos){\r\n      for(i=0; i<pedidos.length; i++){\r\n          //console.log(\"tipoImpressao>\",tipoImpressao);\r\n          if(pedidos[i].status_pedido=='2' && tipoImpressao == 'automatico'){\r\n              //console.log(pedidos[i])\r\n              //console.log(\"CHGEOU AUQI?\");\r\n              const orderElement = document.getElementById(`${pedidos[i].id_pedido}`);\r\n\r\n              //console.log(orderElement);\r\n              if (orderElement && orderElement.getAttribute('data-status') == \"true\") {\r\n                updateStatusPrint(userID, pedidos[i]._id, pedidos[i].id_pedido).then(printPdf(pedidos[i].id_pedido));\r\n              }\r\n          }\r\n      }\r\n    }\r\n    ////return () => clearInterval(intervalId);\r\n  }, [pedidos]); // Sem dependências, então o efeito será executado apenas uma vez*/\r\n\r\n  //const [statusImpressora, setStatusImpressora] = useState('');\r\n  const isDevelopment = window.location.hostname === \"localhost\";\r\n  const apiUrl = isDevelopment\r\n    ? process.env.REACT_APP_SERVER_URL_DEV\r\n    : process.env.REACT_APP_SERVER_URL_PROD;\r\n\r\n  /*useEffect(() => {\r\n    const wsUrl = apiUrl;\r\n    const socket = io(wsUrl, {\r\n      withCredentials: true,\r\n      transports: [\"websocket\"],\r\n      auth: { token: localStorage.getItem(\"token\") },\r\n    });\r\n\r\n    // **Entrar na sala da empresa correta**\r\n    socket.emit(\"joinCompanyRoom\", {\r\n      companyId: idEmpresa.toString(),\r\n      clientId: \"reactClient\",\r\n    });\r\n\r\n    // **Verificar conexão**\r\n    socket.on(\"connect\", () => {\r\n      console.log(`✅ Conectado ao WebSocket! Socket ID: ${socket.id}`);\r\n    });\r\n\r\n    console.log(`📢 Entrando na sala da empresa: ${objIdEmpresa.toString()}`);\r\n\r\n    // **Monitorar todos os eventos recebidos no socket**\r\n    socket.onAny((event, data) => {\r\n      console.log(`📥 Evento recebido no frontend: ${event}`, data);\r\n    });\r\n\r\n    // **Escutando novos pedidos**\r\n    socket.on(\"novoPedido\", (data) => {\r\n      console.log(\"📦 NOVO PEDIDO RECEBIDO!\");\r\n      handleNotify();\r\n    });\r\n\r\n    // **Escutando status da impressora**\r\n    socket.on(\"statusUpdate\", ({ companyId: updatedCompanyId, status }) => {\r\n      if (objIdEmpresa.toString() === updatedCompanyId) {\r\n        console.log(`🖨️ Status atualizado da impressora: ${status}`);\r\n        setStatusPrinter(status === \"Online\" ? \"Online\" : \"Offline\");\r\n      }\r\n    });\r\n\r\n    // **Escutando solicitações de atendimento humano**\r\n    socket.on(\"atendimento_pendente\", (data) => {\r\n      console.log(\"🚨 Novo atendimento solicitado! Dados recebidos:\", data);\r\n\r\n      // **Verificar se a empresa corresponde**\r\n      console.log(`Comparando company_id recebido (${data.company_id}) com idEmpresa (${idEmpresa.toString()})`);\r\n\r\n      // **📌 Gerar um ID único para cada solicitação**\r\n      const atendimentoComID = {\r\n        ...data,\r\n        atendimento_id: uuidv4(), // Gerando um ID único para cada solicitação\r\n      };\r\n\r\n      setAtendimentosPendentes((prev) => [...prev, atendimentoComID]);\r\n    });\r\n\r\n    return () => {\r\n      socket.off(\"novoPedido\");\r\n      socket.off(\"statusUpdate\");\r\n      socket.off(\"atendimento_pendente\");\r\n      socket.disconnect();\r\n    };\r\n  }, [idEmpresa]);*/\r\n  useEffect(() => {\r\n    if (!objIdEmpresa) return;\r\n\r\n    // Se já existir uma conexão WebSocket, desconecta antes de criar outra\r\n    if (socketRef.current) {\r\n      console.log(\"🔄 Desconectando socket anterior...\");\r\n      socketRef.current.disconnect();\r\n    }\r\n\r\n    console.log(`🔌 Conectando ao WebSocket para a empresa ${objIdEmpresa.toString()}...`);\r\n\r\n    // Criar a conexão WebSocket com lógica de reconexão\r\n    const socket = io(apiUrl, {\r\n      withCredentials: true,\r\n      transports: [\"websocket\"],\r\n      auth: { token: localStorage.getItem(\"token\") },\r\n      reconnection: true, // Ativa a reconexão automática\r\n      reconnectionAttempts: 10, // Máximo de 10 tentativas\r\n      reconnectionDelay: 5000, // Intervalo de 5 segundos entre tentativas\r\n    });\r\n\r\n    // Armazena a referência globalmente\r\n    socketRef.current = socket;\r\n\r\n    // **Entrar na sala da empresa**\r\n    socket.emit(\"joinCompanyRoom\", {\r\n      companyId: objIdEmpresa.toString(),\r\n      clientId: \"reactClient\",\r\n    });\r\n\r\n    // **Solicitar status da impressora ao conectar**\r\n    socket.on(\"connect\", () => {\r\n      console.log(`✅ Conectado ao WebSocket! Socket ID: ${socket.id}`);\r\n      socket.emit(\"statusRequest\", { companyId: objIdEmpresa.toString() }); // 🔹 Garante que o status seja atualizado\r\n    });\r\n\r\n    // **Monitorar tentativas de reconexão**\r\n    socket.on(\"reconnect_attempt\", (attempt) => {\r\n      console.log(`🔄 Tentativa de reconexão (${attempt}/10)...`);\r\n    });\r\n\r\n    socket.on(\"reconnect\", (attempt) => {\r\n      console.log(`✅ Reconectado ao WebSocket após ${attempt} tentativas!`);\r\n      socket.emit(\"statusRequest\", { companyId: objIdEmpresa.toString() }); // 🔹 Atualiza status após reconectar\r\n    });\r\n\r\n    // **Monitorar erro de conexão**\r\n    socket.on(\"connect_error\", (error) => {\r\n      console.error(\"❌ Erro na conexão WebSocket:\", error);\r\n    });\r\n\r\n    // **Monitorar eventos recebidos**\r\n    socket.onAny((event, data) => {\r\n      //console.log(`📥 Evento recebido: ${event}`, data);\r\n    });\r\n\r\n    // **Escutando novos pedidos**\r\n    socket.on(\"novoPedido\", () => {\r\n      console.log(\"📦 NOVO PEDIDO RECEBIDO!\");\r\n      handleNotify();\r\n    });\r\n\r\n    // **Escutando status da impressora**\r\n    socket.on(\"statusUpdate\", ({ companyId: updatedCompanyId, status }) => {\r\n      if (objIdEmpresa.toString() === updatedCompanyId) {\r\n        console.log(`🖨️ Status atualizado da impressora: ${status}`);\r\n        setStatusPrinter(status === \"Online\" ? \"Online\" : \"Offline\");\r\n      }\r\n    });\r\n\r\n    // 🔥 DESABILITADO: Hook customizado já gerencia atendimentos via socket + persistência\r\n    // **Escutando solicitações de atendimento humano**\r\n    // socket.on(\"atendimento_pendente\", (data) => {\r\n    //   console.log(\"🚨 Novo atendimento solicitado! Dados recebidos:\", data);\r\n\r\n    //   // **Verificar se a empresa corresponde**\r\n    //   console.log(`Comparando company_id recebido (${data.company_id}) com objIdEmpresa (${objIdEmpresa})`);\r\n\r\n    //   if (data.company_id === objIdEmpresa.toString()) {\r\n    //     console.log(`✔️ Atendimento pertence à empresa ${objIdEmpresa}`);\r\n\r\n    //     // **📌 Gerar um ID único para cada solicitação**\r\n    //     const atendimentoComID = {\r\n    //       ...data,\r\n    //       atendimento_id: uuidv4(),\r\n    //     };\r\n\r\n    //     setAtendimentosPendentes((prev) => [...prev, atendimentoComID]);\r\n    //   } else {\r\n    //     console.log(`❌ Atendimento não pertence à empresa atual. Recebido: ${data.company_id}, Atual: ${objIdEmpresa}`);\r\n    //   }\r\n    // });\r\n\r\n    // **Lógica de cleanup ao desmontar ou atualizar empresa**\r\n    return () => {\r\n      console.log(\"🛑 Desconectando WebSocket ao desmontar ou mudar empresa...\");\r\n      socket.off(\"novoPedido\");\r\n      socket.off(\"statusUpdate\");\r\n      // socket.off(\"atendimento_pendente\"); // 🔥 DESABILITADO: Hook gerencia isso\r\n      socket.off(\"reconnect_attempt\");\r\n      socket.off(\"reconnect\");\r\n      socket.off(\"connect_error\");\r\n      socket.disconnect();\r\n      socketRef.current = null;\r\n    };\r\n  }, [objIdEmpresa]);\r\n\r\n\r\n  // **📌 Função para remover uma solicitação específica (agora usa o hook)**\r\n  const removerAtendimento = (atendimento_id) => {\r\n    // 🔥 NOVO: Usar função do hook que persiste no banco\r\n    removerAtendimentoHook(atendimento_id, 'Removido pelo usuário via interface');\r\n    console.log('📌 Atendimento removido:', atendimento_id);\r\n  };\r\n\r\n  // **📌 Função de simulação removida - hook gerencia automaticamente**\r\n  // const simularAtendimento = () => {\r\n  //   // Não mais necessária - hook useFilaAtendimento gerencia via API + Socket\r\n  // };\r\n\r\n  const sendNotification = (title, options) => {\r\n    // Verifica se o usuário aceitou receber notificações\r\n    if (Notification.permission === \"granted\") {\r\n      const notification = new Notification(title, {\r\n        ...options,\r\n        icon: LogoP, // Certifique-se de que o caminho para o ícone está correto\r\n      });\r\n\r\n      // Toca um som quando a notificação é exibida\r\n      notification.onshow = () => {\r\n        const audio = new Audio(audioNotify);\r\n        audio\r\n          .play()\r\n          .catch((error) =>\r\n            console.log(\"Erro ao reproduzir o som da notificação:\", error)\r\n          );\r\n      };\r\n    }\r\n  };\r\n\r\n  const handleNotify = () => {\r\n    const title = \"Pede Já - Novo Pedido\";\r\n    const options = {\r\n      body: \"Você recebeu um novo pedido.\",\r\n      // O campo 'sound' ainda não é amplamente suportado\r\n      sound: audioNotify, // caminho para o arquivo de áudio\r\n    };\r\n    sendNotification(title, options);\r\n  };\r\n\r\n  const handleOpenCloseLoja = async () => {\r\n    console.log(\"Fechar Loja\");\r\n    const newStatus = !statusLojaTemporario;\r\n    try {\r\n      const response = await changeStatusLoja(objIdEmpresa, newStatus);\r\n      if (response.status === 200) {\r\n        setStatusLojaTemporario(newStatus);\r\n      } else {\r\n        console.error(\"Falha ao atualizar o status da loja:\", response);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao chamar a API:\", error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // add when mounted\r\n    document.addEventListener(\"mousedown\", handleClickOutsideOptions);\r\n    document.addEventListener(\"mousedown\", handleClickOutsideStatusRobo);\r\n    // return function to be called when unmounted\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutsideOptions);\r\n      document.removeEventListener(\"mousedown\", handleClickOutsideStatusRobo);\r\n    };\r\n  }, []);\r\n\r\n  const handleClickOutsideOptions = (y) => {\r\n    if (!y.target || !userOptionsRef.current) return;\r\n\r\n    if (userOptionsRef.current.contains(y.target)) {\r\n      return;\r\n    } else {\r\n      setUserOptions(true);\r\n    }\r\n  };\r\n\r\n  const handleClickOutsideStatusRobo = (y) => {\r\n    // Verifica se o event target existe\r\n    if (!y.target) return;\r\n\r\n    // Verifica se o clique foi dentro do botão que abre o dropdown\r\n    const isClickOnButton = menuStatusRoboRef.current && menuStatusRoboRef.current.contains(y.target);\r\n\r\n    // Verifica se o clique foi dentro do dropdown\r\n    const isClickOnDropdown = menuStatusRoboRef_.current && menuStatusRoboRef_.current.contains(y.target);\r\n\r\n    // Se clicou no botão, não faz nada (o toggle já foi tratado)\r\n    if (isClickOnButton) {\r\n      return;\r\n    }\r\n\r\n    // Se clicou dentro do dropdown, NUNCA fecha\r\n    if (isClickOnDropdown) {\r\n      return; // SEMPRE mantém aberto se clicar dentro do dropdown\r\n    }\r\n\r\n    // Se clicou fora de ambos, fecha o dropdown\r\n    setDropStatusRobo(false);\r\n  };\r\n\r\n  useEffect(() => {\r\n    // add when mounted\r\n    //document.addEventListener(\"mouseover\", handleMouseOverLeftMenu);\r\n    //document.addEventListener(\"mouseout\", handleMouseOutLeftMenu);\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    // return function to be called when unmounted\r\n    return () => {\r\n      //document.removeEventListener(\"mouseover\", handleMouseOverLeftMenu);\r\n      //document.removeEventListener(\"mouseout\", handleMouseOutLeftMenu);\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const handleResize = () => {\r\n    const currentWidth = window.innerWidth;\r\n    if (currentWidth < 1300) {\r\n      setSidebar(false);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  const handleToggleStatusRobo = () => {\r\n    setDropStatusRobo((previous) => !previous);\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <ModalUserImg\r\n        setModalImg={setModalImg}\r\n        showModalImg={showModalImg}\r\n        userID={userID}\r\n      />\r\n\r\n      <ModalEditUser\r\n        setEditUser={setEditUser}\r\n        showEditUser={showEditUser}\r\n        setRefresh={setRefresh}\r\n        selectData={selectData}\r\n        setselectData={setselectData}\r\n        _idUserEdit={_idUserEdit}\r\n        usernameEdit={usernameEdit}\r\n        emailEdit={emailEdit}\r\n        roleEdit={roleEdit}\r\n        editPerfil={true}\r\n      />\r\n\r\n      <ModalLinkCardapio\r\n        showLinkCardapio={showLinkCardapio}\r\n        setShowLinkCardapio={setShowLinkCardapio}\r\n        nomeEmpresaForUrl={nomeEmpresaForUrl}\r\n      />\r\n\r\n      <ModalLinkCardapioSalao\r\n        showLinkCardapioSalao={showLinkCardapioSalao}\r\n        setShowLinkCardapioSalao={setShowLinkCardapioSalao}\r\n        nomeEmpresaForUrl={nomeEmpresaForUrl}\r\n      />\r\n\r\n      <IconContext.Provider value={{ color: \"#fff\" }}>\r\n        <Nav sidebar={sidebar} style={{ justifyContent: \"space-between\" }}>\r\n          <div className=\"d-flex ms-3 align-items-center\">\r\n            <div onClick={showDrawer} className=\"desktop-hidden me-4\" style={{ cursor: \"pointer\" }}>\r\n              <GiHamburgerMenu color=\"black\" />\r\n            </div>\r\n            <div\r\n              className=\"userCircleImg\"\r\n              style={{\r\n                paddingTop: \"4px\",\r\n                paddingBottom: \"2px\",\r\n                display: isMobile ? \"none\" : \"block\",\r\n              }}\r\n            >\r\n              <img src={logoImg} alt=\"pede-ja logo\" width={120} height={40} className=\"logoImg\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"d-flex\">\r\n            {/*isDevelopment && !isMobile ?\r\n              <button className=\"atendimento-teste-btn\" onClick={simularAtendimento}>\r\n                🚀 Simular Atendimento\r\n              </button>\r\n              :\r\n              undefined\r\n            */}\r\n\r\n            {/* Fim Dropdown Menu Status Robo */}\r\n            <div\r\n              style={{\r\n                padding: \"5px 24px 5px 24px\",\r\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\r\n                height: 80,\r\n                flexDirection: \"column\",\r\n                justifyContent: \"center\",\r\n                alignItems: \"center\",\r\n                gap: \"2px\",\r\n                display: \"inline-flex\",\r\n                position: \"relative\",\r\n                cursor: \"pointer\"\r\n              }}\r\n              ref={menuStatusRoboRef}\r\n              onClick={handleToggleStatusRobo}\r\n            //dropstatusrobo={dropstatusrobo}\r\n            //className={dropstatusrobo ? 'open' : 'closed'}\r\n            >\r\n              <div className=\"hidden-mobile\">\r\n                <span style={{ fontSize: 15, fontWeight: 500, marginLeft: 5 }}>\r\n                  Status\r\n                </span>\r\n              </div>\r\n              <FaRobot style={{ color: \"black\", fontSize: 24 }} />\r\n              <div\r\n                className=\"hidden-mobile\"\r\n                style={{\r\n                  fontWeight: \"bold\",\r\n                  display: \"flex\",\r\n                  justifyContent: \"center\",\r\n                  fontSize: 15,\r\n                  position: \"relative\"\r\n                }}\r\n              >\r\n                <span>Robo Pede Já</span>\r\n                <RiArrowDropDownLine style={{ color: \"black\", fontSize: 24 }} />\r\n              </div>\r\n\r\n              {/* Dropdown Menu Status Robo */}\r\n              <div\r\n                ref={menuStatusRoboRef_}\r\n                className={\r\n                  dropstatusrobo ? \"menu-dropdownShow\" : \"menu-dropdownClosed\"\r\n                }\r\n                style={{ position: 'absolute' }}\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  e.preventDefault();\r\n                }}\r\n              >\r\n                <div className=\"menu-options\" onClick={(e) => e.stopPropagation()}>\r\n                  <div className=\"option bottom\" onClick={(e) => e.stopPropagation()}>\r\n                    <div className=\"info-option\">\r\n                      <div className=\"status-option online\" />\r\n                      <div className=\"label-option\" onClick={handleSwitchChange} style={{ cursor: 'pointer' }}>\r\n                        <pedeja-icon\r\n                          iconname=\"whatsapp\"\r\n                          iconstroke={2}\r\n                          iconcolor=\"#797878\"\r\n                          iconsize={18}\r\n                          _nghost-ng-c3181319476=\"\"\r\n                          style={{ height: 18 }}\r\n                        >\r\n                          <svg\r\n                            width={18}\r\n                            height={18}\r\n                            fill=\"#797878\"\r\n                            viewBox=\"0 0 13 13\"\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                          >\r\n                            <path\r\n                              d=\"M9.477 7.79066C9.31409 7.70819 8.52069 7.32103 8.37241 7.26578C8.22412 7.21297 8.11647 7.18534 8.00841 7.34825C7.90278 7.50709 7.59159 7.87069 7.49653 7.97672C7.40147 8.08275 7.30803 8.09088 7.14756 8.01897C6.98466 7.9365 6.46425 7.76709 5.84634 7.21297C5.36372 6.78356 5.04237 6.25462 4.94691 6.09172C4.85184 5.93084 4.93634 5.83984 5.01678 5.75941C5.09072 5.68547 5.17969 5.57334 5.26216 5.47584C5.34056 5.37834 5.36575 5.31294 5.42303 5.20731C5.47584 5.09316 5.44862 5.00419 5.40841 4.92375C5.36819 4.84331 5.04441 4.04584 4.90912 3.72816C4.77994 3.41291 4.64466 3.45312 4.54512 3.45312C4.45209 3.44459 4.34403 3.44459 4.23638 3.44459C4.12872 3.44459 3.95281 3.48481 3.80453 3.63919C3.65625 3.80209 3.23741 4.19128 3.23741 4.97859C3.23741 5.76794 3.81712 6.53169 3.89756 6.64584C3.98003 6.75147 5.03791 8.37647 6.66087 9.07481C7.04803 9.23772 7.34866 9.33522 7.58347 9.41566C7.97062 9.53834 8.32406 9.52128 8.60316 9.48106C8.91191 9.43028 9.55947 9.08944 9.69516 8.70878C9.83288 8.32569 9.83288 8.00841 9.79266 7.9365C9.75244 7.86256 9.64681 7.82234 9.48391 7.75044L9.477 7.79066ZM6.53372 11.7812H6.52519C5.56441 11.7812 4.61459 11.5208 3.78503 11.0342L3.59044 10.918L1.55919 11.4469L2.10519 9.4705L1.97397 9.26738C1.4375 8.41439 1.1529 7.42722 1.15294 6.41956C1.15294 3.47019 3.56728 1.06437 6.53778 1.06437C7.97672 1.06437 9.32669 1.625 10.3423 2.64062C10.8433 3.13568 11.2407 3.72555 11.5114 4.37582C11.782 5.0261 11.9204 5.72376 11.9186 6.42809C11.9145 9.37544 9.50219 11.7812 6.53575 11.7812H6.53372ZM11.1146 1.86834C9.87878 0.674781 8.25378 0 6.52519 0C2.95994 0 0.056875 2.89047 0.0548438 6.44272C0.0548438 7.57697 0.351 8.68359 0.918125 9.66306L0 13L3.432 12.105C4.38275 12.6176 5.44547 12.8872 6.52559 12.8899H6.52763C10.0949 12.8899 12.998 9.99944 13 6.44475C13 4.72469 12.3293 3.10578 11.1065 1.88906L11.1146 1.86834Z\"\r\n                              fill=\"#797878\"\r\n                            />\r\n                          </svg>\r\n                        </pedeja-icon>\r\n                        <span className=\"description\">\r\n                          Whatsapp\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"wrapper\">\r\n                      <div className=\"switch_box box_1\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          className=\"switch_1\"\r\n                          checked={statusBot}\r\n                          onChange={handleSwitchChange}\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div\r\n                    className=\"option bottom-last\"\r\n                    style={{ marginBottom: \"5px\" }}\r\n                    onClick={(e) => e.stopPropagation()}\r\n                  >\r\n                    <div className=\"info-option\">\r\n                      <div className=\"status-option online\" />\r\n                      <div\r\n                        className=\"label-option\"\r\n                        onClick={handleCallAtendenteChange}\r\n                        style={{ display: \"inline-flex\", alignItems: \"center\", cursor: 'pointer' }}\r\n                      >\r\n                        <pedeja-icon\r\n                          iconname=\"users\"\r\n                          iconstroke={2}\r\n                          iconcolor=\"#797878\"\r\n                          iconsize={18}\r\n                          _nghost-ng-c3181319476=\"\"\r\n                          style={{ height: 18 }}\r\n                        >\r\n                          <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            width={18}\r\n                            height={18}\r\n                            viewBox=\"0 0 24 24\"\r\n                            fill=\"none\"\r\n                            stroke=\"#797878\"\r\n                            strokeWidth={2}\r\n                            strokeLinecap=\"round\"\r\n                            strokeLinejoin=\"round\"\r\n                            className=\"feather feather-users\"\r\n                          >\r\n                            <path d=\"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\" />\r\n                            <circle cx={9} cy={7} r={4} />\r\n                            <path d=\"M23 21v-2a4 4 0 0 0-3-3.87\" />\r\n                            <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\r\n                          </svg>\r\n                        </pedeja-icon>\r\n                        <div style={{ display: \"inline-grid\", lineHeight: \"17px\" }}>\r\n                          <span className=\"description\">Chamar</span>\r\n                          <span className=\"description\">Atendente</span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"wrapper\">\r\n                      <div className=\"switch_box box_1\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          className=\"switch_1\"\r\n                          checked={callAtendente}\r\n                          onChange={() => handleCallAtendenteChange()}\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n\r\n            <Tooltip\r\n              placement=\"bottom\"\r\n              title={`Impressora: ${statusPrinter === \"Offline\" ? 'Offline' : 'Conectada'}`}\r\n            >\r\n              <div style={{\r\n                padding: \"5px 24px 5px 24px\",\r\n                borderLeft: \"1px solid rgb(208, 209, 209)\",\r\n                height: 80,\r\n                flexDirection: \"row\",\r\n                justifyContent: \"center\",\r\n                alignItems: \"center\",\r\n                gap: \"5px\",\r\n                display: \"inline-flex\",\r\n              }}>\r\n                <ImPrinter color={statusPrinter === \"Offline\" ? 'red' : '#07c670'} fontSize={30} />\r\n                <div className=\"hidden-mobile\">\r\n                  <div>\r\n                    <span style={{ fontSize: 15, fontWeight: 500, marginLeft: 5 }}>\r\n                      Impressora\r\n                    </span>\r\n                  </div>\r\n                  {statusPrinter && statusPrinter === \"Offline\" ? (\r\n                    <div className=\"divStatusPrintNavBarOffline\">\r\n                      <span>Offline</span>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"divStatusPrintNavBar\">\r\n                      <span>Conectada</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </Tooltip>\r\n            <Tooltip\r\n              placement=\"bottom\"\r\n              title={`Cardápio Delivery`}\r\n            >\r\n              <div\r\n                style={{\r\n                  padding: \"5px 24px\",\r\n                  borderLeft: \"1px solid rgb(208, 209, 209)\",\r\n                  height: 80,\r\n                  flexDirection: \"column\",\r\n                  justifyContent: \"center\",\r\n                  alignItems: \"center\",\r\n                  gap: \"2px\",\r\n                  display: \"inline-flex\",\r\n                }}\r\n              >\r\n                <div\r\n                  onClick={() => setShowLinkCardapio(true)}\r\n                  style={{\r\n                    cursor: \"pointer\",\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                  }}\r\n                >\r\n                  <img src={entregadorIco} height={30} width={30} />\r\n                  <div className=\"d-flex flex-column hidden-mobile\">\r\n                    <span style={{ fontSize: 14, fontWeight: 400, marginLeft: 5 }}>\r\n                      Link\r\n                    </span>\r\n                    <span style={{ fontSize: 14, fontWeight: 700, marginLeft: 5 }}>\r\n                      Cardápio Delivery\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </Tooltip>\r\n\r\n            <Tooltip\r\n              placement=\"bottom\"\r\n              title={`Cardápio Salão`}\r\n            >\r\n              <div\r\n                style={{\r\n                  padding: \"5px 24px\",\r\n                  borderLeft: \"1px solid rgb(208, 209, 209)\",\r\n                  height: 80,\r\n                  flexDirection: \"column\",\r\n                  justifyContent: \"center\",\r\n                  alignItems: \"center\",\r\n                  gap: \"2px\",\r\n                  display: \"inline-flex\",\r\n                }}\r\n              >\r\n                <div\r\n                  onClick={() => setShowLinkCardapioSalao(true)}\r\n                  style={{\r\n                    cursor: \"pointer\",\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                  }}\r\n                >\r\n                  <img src={roundTable} height={30} width={30} />\r\n                  <div className=\"d-flex flex-column hidden-mobile\">\r\n                    <span style={{ fontSize: 14, fontWeight: 400, marginLeft: 5 }}>\r\n                      Link\r\n                    </span>\r\n                    <span style={{ fontSize: 14, fontWeight: 700, marginLeft: 5 }}>\r\n                      Cardápio Salão\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </Tooltip>\r\n\r\n            <div\r\n              style={{\r\n                padding: \"5px 24px 5px 24px\",\r\n                height: 80,\r\n                flexDirection: \"column\",\r\n                justifyContent: \"center\",\r\n                alignItems: \"center\",\r\n                gap: \"2px\",\r\n                display: \"inline-flex\",\r\n                borderLeft: \"1px solid rgb(208, 209, 209)\"\r\n              }}\r\n              className=\"hidden-sm-mobile\"\r\n            >\r\n              <div>\r\n                <MdStorefront style={{ color: \"black\", fontSize: 20 }} />\r\n                <span style={{ fontSize: 15, fontWeight: 500, marginLeft: 5 }}>\r\n                  Loja\r\n                  {!statusLojaTemporario ? (\r\n                    <div\r\n                      style={{\r\n                        width: 70,\r\n                        height: 18,\r\n                        borderRadius: 10,\r\n                        marginLeft: 10,\r\n                        background: \"#9CE8C6\",\r\n                        justifyContent: \"center\",\r\n                        alignItems: \"center\",\r\n                        gap: 8,\r\n                        display: \"inline-flex\",\r\n                      }}\r\n                    >\r\n                      <span>Aberta</span>\r\n                    </div>\r\n                  ) : (\r\n                    <div\r\n                      style={{\r\n                        width: 80,\r\n                        height: 18,\r\n                        borderRadius: 10,\r\n                        marginLeft: 10,\r\n                        background: \"#ff0000b5\",\r\n                        justifyContent: \"center\",\r\n                        alignItems: \"center\",\r\n                        gap: 8,\r\n                        color: \"white\",\r\n                        fontSize: 13,\r\n                        display: \"inline-flex\",\r\n                      }}\r\n                    >\r\n                      <span>Fechada</span>\r\n                    </div>\r\n                  )}\r\n                </span>\r\n              </div>\r\n              <div\r\n                style={{\r\n                  fontWeight: \"bold\",\r\n                  display: \"flex\",\r\n                  justifyContent: \"center\",\r\n                  fontSize: 15,\r\n                }}\r\n              >\r\n                <span>{nomeEmpresa}</span>\r\n              </div>\r\n              {statusLojaTemporario ? (\r\n                <div\r\n                  type=\"button\"\r\n                  onClick={() => handleOpenCloseLoja()}\r\n                  style={{\r\n                    width: 150,\r\n                    height: 20,\r\n                    borderRadius: 5,\r\n                    background: \"#318CD5\",\r\n                    justifyContent: \"center\",\r\n                    alignItems: \"center\",\r\n                    gap: 8,\r\n                    display: \"inline-flex\",\r\n                  }}\r\n                >\r\n                  <a style={{ color: \"white\", fontSize: 10 }}>Abrir Loja</a>\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  type=\"button\"\r\n                  onClick={() => handleOpenCloseLoja()}\r\n                  style={{\r\n                    width: 150,\r\n                    height: 20,\r\n                    borderRadius: 5,\r\n                    background: \"#318CD5\",\r\n                    justifyContent: \"center\",\r\n                    alignItems: \"center\",\r\n                    gap: 8,\r\n                    display: \"inline-flex\",\r\n                  }}\r\n                >\r\n                  <a style={{ color: \"white\", fontSize: 10 }}>Fechar Loja</a>\r\n                </div>\r\n              )}\r\n            </div>\r\n            <div className=\"hidden-mobile\" style={{ width: 128, height: 80, paddingLeft: 24, paddingRight: 24, borderLeft: '1px #D0D1D1 solid', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'inline-flex' }}>\r\n              <div onClick={handleLogout} style={{ textAlign: 'center', cursor: \"pointer\", color: '#001B30', fontSize: 18, fontWeight: '700', letterSpacing: 0.48, wordWrap: 'break-word' }}>\r\n                <MdExitToApp style={{ color: 'black', fontSize: 25 }} />Sair\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Nav>\r\n        <Drawer title=\"Navegação\" onClose={onClose} open={open} key=\"left\" placement=\"left\" bodyStyle={{ padding: 0 }}>\r\n          <div className=\"d-flex flex-column justify-content-between h-100\">\r\n            <div\r\n              className=\"boxLeftMenuScroll\"\r\n              style={{ overflowY: \"scroll\", height: \"calc(100vh - 120px)\" }}\r\n            >\r\n              {SidebarData.map((item, index) => {\r\n                return (\r\n                  <PermissionGate\r\n                    key={index}\r\n                    permissions={[SidebarData[index].permission]}\r\n                  >\r\n                    <SubMenu\r\n                      item={item}\r\n                      key={index}\r\n                      style={{ background: \"black\" }}\r\n                    />\r\n                  </PermissionGate>\r\n                );\r\n              })}\r\n            </div>\r\n            <div className=\"d-flex flex-column\">\r\n              <div\r\n                style={{\r\n                  padding: \"5px 24px 5px 24px\",\r\n                  borderLeft: \"1px solid rgb(208, 209, 209)\",\r\n                  height: 80,\r\n                  flexDirection: \"column\",\r\n                  justifyContent: \"center\",\r\n                  alignItems: \"center\",\r\n                  gap: \"2px\",\r\n                  display: \"inline-flex\",\r\n                }}\r\n              >\r\n                <div>\r\n                  <MdStorefront style={{ color: \"black\", fontSize: 20 }} />\r\n                  <span style={{ fontSize: 15, fontWeight: 500, marginLeft: 5 }}>\r\n                    Loja\r\n                    {!statusLojaTemporario ? (\r\n                      <div\r\n                        style={{\r\n                          width: 70,\r\n                          height: 18,\r\n                          borderRadius: 10,\r\n                          marginLeft: 10,\r\n                          background: \"#9CE8C6\",\r\n                          justifyContent: \"center\",\r\n                          alignItems: \"center\",\r\n                          gap: 8,\r\n                          display: \"inline-flex\",\r\n                        }}\r\n                      >\r\n                        <span>Aberta</span>\r\n                      </div>\r\n                    ) : (\r\n                      <div\r\n                        style={{\r\n                          width: 80,\r\n                          height: 18,\r\n                          borderRadius: 10,\r\n                          marginLeft: 10,\r\n                          background: \"#ff0000b5\",\r\n                          justifyContent: \"center\",\r\n                          alignItems: \"center\",\r\n                          gap: 8,\r\n                          color: \"white\",\r\n                          fontSize: 13,\r\n                          display: \"inline-flex\",\r\n                        }}\r\n                      >\r\n                        <span>Fechada</span>\r\n                      </div>\r\n                    )}\r\n                  </span>\r\n                </div>\r\n                <div\r\n                  style={{\r\n                    fontWeight: \"bold\",\r\n                    display: \"flex\",\r\n                    justifyContent: \"center\",\r\n                    fontSize: 15,\r\n                  }}\r\n                >\r\n                  <span>{nomeEmpresa}</span>\r\n                </div>\r\n                {statusLojaTemporario ? (\r\n                  <div\r\n                    type=\"button\"\r\n                    onClick={() => handleOpenCloseLoja()}\r\n                    style={{\r\n                      width: 150,\r\n                      height: 20,\r\n                      borderRadius: 5,\r\n                      background: \"#318CD5\",\r\n                      justifyContent: \"center\",\r\n                      alignItems: \"center\",\r\n                      gap: 8,\r\n                      display: \"inline-flex\",\r\n                    }}\r\n                  >\r\n                    <a style={{ color: \"white\", fontSize: 10 }}>Abrir Loja</a>\r\n                  </div>\r\n                ) : (\r\n                  <div\r\n                    type=\"button\"\r\n                    onClick={() => handleOpenCloseLoja()}\r\n                    style={{\r\n                      width: 150,\r\n                      height: 20,\r\n                      borderRadius: 5,\r\n                      background: \"#318CD5\",\r\n                      justifyContent: \"center\",\r\n                      alignItems: \"center\",\r\n                      gap: 8,\r\n                      display: \"inline-flex\",\r\n                    }}\r\n                  >\r\n                    <a style={{ color: \"white\", fontSize: 10 }}>Fechar Loja</a>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <div className=\"hidden-mobile\" style={{ width: 128, height: 80, paddingLeft: 24, paddingRight: 24, borderLeft: '1px #D0D1D1 solid', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'inline-flex' }}>\r\n                <div onClick={handleLogout} style={{ textAlign: 'center', cursor: \"pointer\", color: '#001B30', fontSize: 18, fontWeight: '700', letterSpacing: 0.48, wordWrap: 'break-word' }}>\r\n                  <MdExitToApp style={{ color: 'black', fontSize: 25 }} />Sair\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Drawer>\r\n        {(!planType || planType === \"\" || daysToExpire == 0) && diasTesteRestantes && showTrialAlert ?\r\n          !semCobrancaOuInvoice && !carregandoCheckLicense ?\r\n            <div\r\n              className=\"info-licenca\"\r\n              style={{\r\n                position: 'relative',\r\n                opacity: isTrialAlertFading ? 0 : 1,\r\n                transform: isTrialAlertFading ? 'translateY(-10px)' : 'translateY(0)',\r\n                transition: 'opacity 0.3s ease, transform 0.3s ease',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center', // Para dar espaço ao botão\r\n                paddingLeft: '20px',\r\n                paddingRight: '20px'\r\n              }}\r\n            >\r\n              <span style={{ textAlign: 'center' }}>\r\n                Dias de teste restantes: {diasTesteRestantes} dias\r\n              </span>\r\n              <button\r\n                onClick={closeTrialAlert}\r\n                style={{\r\n                  background: 'rgba(255,255,255,0.9)',\r\n                  border: '1px solid rgba(255,255,255,0.3)',\r\n                  borderRadius: '50%',\r\n                  width: '24px',\r\n                  height: '24px',\r\n                  cursor: 'pointer',\r\n                  fontSize: '16px',\r\n                  fontWeight: 'bold',\r\n                  color: '#333',\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center',\r\n                  transition: 'all 0.2s ease',\r\n                  marginLeft: '10px',\r\n                  boxShadow: '0 2px 4px rgba(0,0,0,0.2)',\r\n                  flexShrink: 0\r\n                }}\r\n                onMouseOver={(e) => {\r\n                  e.target.style.background = 'rgba(255,255,255,1)';\r\n                  e.target.style.color = '#000';\r\n                  e.target.style.transform = 'scale(1.1)';\r\n                }}\r\n                onMouseOut={(e) => {\r\n                  e.target.style.background = 'rgba(255,255,255,0.9)';\r\n                  e.target.style.color = '#333';\r\n                  e.target.style.transform = 'scale(1)';\r\n                }}\r\n                title=\"Fechar aviso\"\r\n              >\r\n                ✕\r\n              </button>\r\n            </div>\r\n            :\r\n            null\r\n          : null}\r\n        {planType && planType !== \"free_trial\" && daysToExpire <= 3 && diferencaEmDias > 7 && showExpirationAlert ? (\r\n          <div\r\n            className=\"info-licenca\"\r\n            style={{\r\n              position: 'relative',\r\n              opacity: isExpirationAlertFading ? 0 : 1,\r\n              transform: isExpirationAlertFading ? 'translateY(-10px)' : 'translateY(0)',\r\n              transition: 'opacity 0.3s ease, transform 0.3s ease',\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center', // Para dar espaço ao botão\r\n              paddingLeft: '20px',\r\n              paddingRight: '20px'\r\n            }}\r\n          >\r\n            <div style={{ flex: 1, textAlign: 'center' }}>\r\n              {daysToExpire <= 0 ? (\r\n                <span>\r\n                  Hoje seu plano irá expirar, para realizar o pagamento da fatura{\" \"}\r\n                  <span\r\n                    className=\"clickNavigateToPlanos\"\r\n                    onClick={() => navigate(\"/planos\")}\r\n                  >\r\n                    CLIQUE AQUI\r\n                  </span>\r\n                </span>\r\n              ) : (\r\n                <span>\r\n                  Faltam apenas {daysToExpire} dias para sua assinatura expirar\r\n                </span>\r\n              )}\r\n            </div>\r\n            <button\r\n              onClick={closeExpirationAlert}\r\n              style={{\r\n                background: 'rgba(255,255,255,0.9)',\r\n                border: '1px solid rgba(255,255,255,0.3)',\r\n                borderRadius: '50%',\r\n                width: '24px',\r\n                height: '24px',\r\n                cursor: 'pointer',\r\n                fontSize: '16px',\r\n                fontWeight: 'bold',\r\n                color: '#333',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                transition: 'all 0.2s ease',\r\n                marginLeft: '10px',\r\n                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',\r\n                flexShrink: 0\r\n              }}\r\n              onMouseOver={(e) => {\r\n                e.target.style.background = 'rgba(255,255,255,1)';\r\n                e.target.style.color = '#000';\r\n                e.target.style.transform = 'scale(1.1)';\r\n              }}\r\n              onMouseOut={(e) => {\r\n                e.target.style.background = 'rgba(255,255,255,0.9)';\r\n                e.target.style.color = '#333';\r\n                e.target.style.transform = 'scale(1)';\r\n              }}\r\n              title=\"Fechar aviso\"\r\n            >\r\n              ✕\r\n            </button>\r\n          </div>\r\n        ) : null}\r\n\r\n        <MenuAjuda>\r\n          <div className={atendimentosPendentes.length > 0 ? \"item-menu-help-info-active\" : \"item-menu-help-info\"}\r\n            onClick={() => setModalOpen(true)}\r\n          >\r\n            <svg\r\n              width={30}\r\n              height={30}\r\n              viewBox=\"0 0 30 30\"\r\n              fill=\"none\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              className={atendimentosPendentes.length > 0 ? \"atendimento-icon-active\" : \"atendimento-icon-inactive\"}\r\n              id={atendimentosPendentes.length > 0 ? \"waving-animation\" : undefined}\r\n            >\r\n              {/* Círculo de fundo com gradiente sutil */}\r\n              <circle\r\n                cx=\"15\"\r\n                cy=\"15\"\r\n                r=\"14\"\r\n                fill={atendimentosPendentes.length > 0 ? \"url(#activeGradient)\" : \"url(#inactiveGradient)\"}\r\n                stroke={atendimentosPendentes.length > 0 ? \"#318CD5\" : \"#e1e1e1\"}\r\n                strokeWidth=\"1\"\r\n                opacity={atendimentosPendentes.length > 0 ? \"1\" : \"0.7\"}\r\n              />\r\n\r\n              {/* Definições de gradientes */}\r\n              <defs>\r\n                <linearGradient id=\"activeGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n                  <stop offset=\"0%\" style={{ stopColor: \"#318CD5\", stopOpacity: 0.1 }} />\r\n                  <stop offset=\"100%\" style={{ stopColor: \"#4facfe\", stopOpacity: 0.2 }} />\r\n                </linearGradient>\r\n                <linearGradient id=\"inactiveGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n                  <stop offset=\"0%\" style={{ stopColor: \"#f8f9fa\", stopOpacity: 1 }} />\r\n                  <stop offset=\"100%\" style={{ stopColor: \"#e9ecef\", stopOpacity: 1 }} />\r\n                </linearGradient>\r\n                <linearGradient id=\"personGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\r\n                  <stop offset=\"0%\" style={{ stopColor: atendimentosPendentes.length > 0 ? \"#ffffff\" : \"#6c757d\", stopOpacity: 1 }} />\r\n                  <stop offset=\"100%\" style={{ stopColor: atendimentosPendentes.length > 0 ? \"#f8f9fa\" : \"#adb5bd\", stopOpacity: 1 }} />\r\n                </linearGradient>\r\n              </defs>\r\n\r\n              {/* Pessoa principal no centro */}\r\n              <g className=\"main-person\">\r\n                {/* Cabeça */}\r\n                <circle\r\n                  cx=\"15\"\r\n                  cy=\"11\"\r\n                  r=\"3.5\"\r\n                  fill=\"url(#personGradient)\"\r\n                  stroke={atendimentosPendentes.length > 0 ? \"#e9ecef\" : \"#6c757d\"}\r\n                  strokeWidth=\"0.8\"\r\n                />\r\n\r\n                {/* Corpo */}\r\n                <path\r\n                  d=\"M15 14.5C12.5 14.5 9.5 16 9.5 19V22.5C9.5 23 9.8 23.5 10.5 23.5H19.5C20.2 23.5 20.5 23 20.5 22.5V19C20.5 16 17.5 14.5 15 14.5Z\"\r\n                  fill=\"url(#personGradient)\"\r\n                  stroke={atendimentosPendentes.length > 0 ? \"#e9ecef\" : \"#6c757d\"}\r\n                  strokeWidth=\"0.8\"\r\n                />\r\n\r\n                {/* Braço acenando (animado quando ativo) */}\r\n                <g className=\"waving-arm\" style={{ transformOrigin: \"18px 16px\" }}>\r\n                  <path\r\n                    d=\"M18 16L21 13C21.5 12.5 22 12.5 22.5 13C23 13.5 23 14 22.5 14.5L20 17\"\r\n                    fill=\"none\"\r\n                    stroke=\"url(#personGradient)\"\r\n                    strokeWidth=\"1.5\"\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                  />\r\n                  {/* Mão acenando */}\r\n                  <circle\r\n                    cx=\"22\"\r\n                    cy=\"13.5\"\r\n                    r=\"1.2\"\r\n                    fill=\"url(#personGradient)\"\r\n                    className=\"waving-hand\"\r\n                  />\r\n                </g>\r\n              </g>\r\n\r\n              {/* Pessoa adicional à esquerda (quando há muitos atendimentos) */}\r\n              {atendimentosPendentes.length > 2 && (\r\n                <g className=\"extra-person-left\" opacity=\"0.6\">\r\n                  <circle cx=\"8\" cy=\"10\" r=\"2.5\" fill=\"url(#personGradient)\" />\r\n                  <path\r\n                    d=\"M8 12.5C6.5 12.5 4.5 13.5 4.5 16V19C4.5 19.3 4.7 19.5 5 19.5H11C11.3 19.5 11.5 19.3 11.5 19V16C11.5 13.5 9.5 12.5 8 12.5Z\"\r\n                    fill=\"url(#personGradient)\"\r\n                  />\r\n                </g>\r\n              )}\r\n\r\n              {/* Pessoa adicional à direita (quando há muitos atendimentos) */}\r\n              {atendimentosPendentes.length > 1 && (\r\n                <g className=\"extra-person-right\" opacity=\"0.7\">\r\n                  <circle cx=\"22\" cy=\"20\" r=\"2.5\" fill=\"url(#personGradient)\" />\r\n                  <path\r\n                    d=\"M22 22.5C20.5 22.5 18.5 23.5 18.5 26V28.5C18.5 28.8 18.7 29 19 29H25C25.3 29 25.5 28.8 25.5 28.5V26C25.5 23.5 23.5 22.5 22 22.5Z\"\r\n                    fill=\"url(#personGradient)\"\r\n                  />\r\n                </g>\r\n              )}\r\n\r\n              {/* Partículas de atenção quando ativo */}\r\n              {atendimentosPendentes.length > 0 && (\r\n                <g className=\"attention-particles\">\r\n                  <circle cx=\"24\" cy=\"8\" r=\"1\" fill=\"#ffffff\" stroke=\"#e9ecef\" strokeWidth=\"0.3\" opacity=\"0.9\" className=\"particle-1\" />\r\n                  <circle cx=\"6\" cy=\"6\" r=\"0.8\" fill=\"#ffffff\" stroke=\"#e9ecef\" strokeWidth=\"0.3\" opacity=\"0.7\" className=\"particle-2\" />\r\n                  <circle cx=\"26\" cy=\"24\" r=\"0.6\" fill=\"#ffffff\" stroke=\"#e9ecef\" strokeWidth=\"0.3\" opacity=\"0.6\" className=\"particle-3\" />\r\n                </g>\r\n              )}\r\n            </svg>\r\n\r\n            {atendimentosPendentes.length > 0 ? (\r\n              <div className=\"number-box number-box--active\">\r\n                {atendimentosPendentes.length}\r\n              </div>\r\n            ) : isLoadingAtendimentos ? (\r\n              <div className=\"number-box number-box--loading\" title=\"Carregando atendimentos...\">\r\n                ⏳\r\n              </div>\r\n            ) : undefined}\r\n          </div>\r\n        </MenuAjuda>\r\n        {/* 📌 Modal de Atendimentos Pendentes */}\r\n        <AtendimentoModal\r\n          atendimentosPendentes={atendimentosPendentes}\r\n          removerAtendimento={removerAtendimento}\r\n          iniciarAtendimento={iniciarAtendimento}\r\n          cancelarTodosAtendimentos={cancelarTodosAtendimentos}\r\n          modalOpen={modalOpen}\r\n          setModalOpen={setModalOpen}\r\n        />\r\n\r\n        <CrispChat />\r\n\r\n        {/*<HelpWidget />*/}\r\n\r\n\r\n        <SidebarNav\r\n          ref={leftMenuRef}\r\n          sidebar={sidebar}\r\n          className={sidebar ? \"open\" : \"closed\"}\r\n          style={{ boxShadow: \"1px 1px 6px lightgray\" }}\r\n        >\r\n          <SidebarWrap>\r\n            <div className=\"collapseDiv\">\r\n              {sidebar ? (\r\n                <RiMenuFoldFill\r\n                  onClick={toggleSidebar}\r\n                  fill=\"gray\"\r\n                  className=\"collapseInLeftMenuBtn\"\r\n                />\r\n              ) : (\r\n                <RiMenuUnfoldFill\r\n                  onClick={toggleSidebar}\r\n                  fill=\"gray\"\r\n                  className=\"collapseOutLeftMenuBtn\"\r\n                />\r\n              )}\r\n            </div>\r\n\r\n            {sidebar ? (\r\n              <div\r\n                style={{\r\n                  borderBottom: \"3px solid #4281FF\",\r\n                  backgroundImage: `url(${userDiv})` /*background:\"rgba(0,0,0,0.8)\"*/,\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"userCircleImg\"\r\n                  style={{ paddingTop: \"4px\", paddingBottom: \"2px\" }}\r\n                >\r\n                  {/*<HiIcons.HiUserCircle style={{color:\"rgb(200,200,200)\", fontSize:\"60px\", padding:\"4px\"}}/>\r\n                <img style={{backgroundImage:`url(${userImg})`}}/>*/}\r\n                  <img\r\n                    src={\r\n                      userImg !== null && userImg.length > 0\r\n                        ? userImg\r\n                        : defaultUserImg\r\n                    }\r\n                    width={60}\r\n                    height={60}\r\n                    className=\"userImg\"\r\n                  />\r\n                </div>\r\n                <NavIcon /*to='#'*/ style={{ marginLeft: \"30px\" }}>\r\n                  <label\r\n                    style={{\r\n                      maxWidth: \"80%\",\r\n                      overflow: \"hidden\",\r\n                      whiteSpace: \"nowrap\",\r\n                    }}\r\n                  >\r\n                    {userName}\r\n                  </label>\r\n                  <div ref={userOptionsRef}>\r\n                    <HiIcons.HiOutlineDotsVertical\r\n                      onClick={showUserOptions}\r\n                      style={{\r\n                        color: \"white\",\r\n                        cursor: \"pointer\",\r\n                        marginLeft: \"10px\",\r\n                        fontSize: \"22px\",\r\n                      }}\r\n                    />\r\n                    <ModalUserOptions showOptions={showOptions}>\r\n                      <li\r\n                        onClick={(e) => handleEdit(userID)}\r\n                        style={{ cursor: \"pointer\" }}\r\n                      >\r\n                        <a>Editar Perfil</a>\r\n                      </li>\r\n                      <li\r\n                        onClick={showModalAddUserImg}\r\n                        style={{ cursor: \"pointer\" }}\r\n                      >\r\n                        <a>Editar Imagem</a>\r\n                      </li>\r\n                      <li onClick={handleLogout} style={{ cursor: \"pointer\" }}>\r\n                        <a>Sair</a>\r\n                      </li>\r\n                    </ModalUserOptions>\r\n                  </div>\r\n                </NavIcon>\r\n                <div\r\n                  style={{\r\n                    paddingBottom: \"2px\",\r\n                    justifyContent: \"center\",\r\n                    display: \"flex\",\r\n                    fontSize: \"12px\",\r\n                    color: \"white\",\r\n                  }}\r\n                >\r\n                  {userEmail}\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div\r\n                style={{\r\n                  boxShadow: \"-1px 1px 1px 0px #0000001c\",\r\n                  borderBottom: \"3px solid transparent\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"userCircleImg\"\r\n                  style={{\r\n                    paddingTop: 15,\r\n                    paddingBottom: 5,\r\n                    paddingRight: 3,\r\n                    paddingBottom: 11,\r\n                  }}\r\n                >\r\n                  {/*<HiIcons.HiUserCircle style={{color:\"rgb(180,180,180)\", fontSize:\"108px\", padding:\"4px\"}}/>*/}\r\n                  <img\r\n                    src={\r\n                      userImg !== null && userImg.length > 0\r\n                        ? userImg\r\n                        : defaultUserImg\r\n                    }\r\n                    width={80}\r\n                    height={80}\r\n                    className=\"userImg\"\r\n                    style={{ padding: \"0px\" }}\r\n                  />\r\n                </div>\r\n                <NavIcon\r\n                  to=\"#\"\r\n                  style={{\r\n                    /*display:\"none\",*/ fontSize: \"0px\",\r\n                    height: \"0px\" /*visibility:\"hidden\"*/,\r\n                  }}\r\n                >\r\n                  <label\r\n                    style={{\r\n                      maxWidth: \"80%\",\r\n                      overflow: \"hidden\",\r\n                      whiteSpace: \"nowrap\",\r\n                    }}\r\n                  >\r\n                    {userName}\r\n                  </label>\r\n                </NavIcon>\r\n                <div\r\n                  style={{\r\n                    paddingBottom: \"10px\",\r\n                    justifyContent: \"center\",\r\n                    display: \"flex\",\r\n                    fontSize: \"12px\",\r\n                    display: \"none\",\r\n                  }}\r\n                >\r\n                  {userEmail}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div\r\n              className=\"boxLeftMenuScroll\"\r\n              style={{ overflowY: \"scroll\", height: \"calc(100vh - 120px)\" }}\r\n            >\r\n              {/* Componente \"Comece por aqui\" */}\r\n              {mostrarComecePorAqui && (\r\n                <div\r\n                  className=\"comece-por-aqui-container\"\r\n                  onClick={() => navigate('/configuracao-inicial')}\r\n                  style={{\r\n                    background: 'linear-gradient(135deg, #4299e1 0%, #667eea 100%)',\r\n                    margin: '10px',\r\n                    padding: '16px',\r\n                    borderRadius: '12px',\r\n                    cursor: 'pointer',\r\n                    color: 'white',\r\n                    transition: 'all 0.3s ease',\r\n                    border: '2px solid transparent',\r\n                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.target.style.transform = 'translateY(-2px)';\r\n                    e.target.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.15)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.target.style.transform = 'translateY(0)';\r\n                    e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';\r\n                  }}\r\n                >\r\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\r\n                    <div style={{\r\n                      background: 'rgba(255, 255, 255, 0.2)',\r\n                      borderRadius: '8px',\r\n                      padding: '8px',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      minWidth: '32px',\r\n                      height: '32px',\r\n                    }}>\r\n                      <FaArrowRight style={{ fontSize: '16px' }} />\r\n                    </div>\r\n                    <div style={{ flex: 1 }}>\r\n                      <div style={{\r\n                        fontSize: '16px',\r\n                        fontWeight: '600',\r\n                        marginBottom: '4px',\r\n                        display: sidebar ? 'block' : 'none'\r\n                      }}>\r\n                        Comece por aqui\r\n                      </div>\r\n                      <div style={{\r\n                        fontSize: '12px',\r\n                        opacity: '0.9',\r\n                        display: sidebar ? 'block' : 'none'\r\n                      }}>\r\n                        {totalEtapasConfiguracaoInicial - progressoConfiguracaoInicial} etapas restantes\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Barra de progresso */}\r\n                  <div style={{\r\n                    marginTop: '12px',\r\n                    display: sidebar ? 'block' : 'none'\r\n                  }}>\r\n                    <div style={{\r\n                      background: 'rgba(255, 255, 255, 0.2)',\r\n                      borderRadius: '8px',\r\n                      height: '6px',\r\n                      overflow: 'hidden',\r\n                    }}>\r\n                      <div style={{\r\n                        background: 'white',\r\n                        height: '100%',\r\n                        borderRadius: '8px',\r\n                        width: `${(progressoConfiguracaoInicial / totalEtapasConfiguracaoInicial) * 100}%`,\r\n                        transition: 'width 0.3s ease',\r\n                      }} />\r\n                    </div>\r\n                    <div style={{\r\n                      display: 'flex',\r\n                      justifyContent: 'space-between',\r\n                      alignItems: 'center',\r\n                      marginTop: '6px',\r\n                    }}>\r\n                      <span style={{ fontSize: '11px', opacity: '0.8' }}>\r\n                        {progressoConfiguracaoInicial}/{totalEtapasConfiguracaoInicial} concluídas\r\n                      </span>\r\n                      <span style={{ fontSize: '11px', opacity: '0.8' }}>\r\n                        {Math.round((progressoConfiguracaoInicial / totalEtapasConfiguracaoInicial) * 100)}%\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {SidebarData.map((item, index) => {\r\n                return (\r\n                  <PermissionGate\r\n                    key={index}\r\n                    permissions={[SidebarData[index].permission]}\r\n                  >\r\n                    <SubMenu\r\n                      item={item}\r\n                      key={index}\r\n                      style={{ background: \"black\" }}\r\n                    />\r\n                  </PermissionGate>\r\n                );\r\n              })}\r\n            </div>\r\n          </SidebarWrap>\r\n        </SidebarNav>\r\n      </IconContext.Provider>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default LeftMenu;\r\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACtE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAO,aAAa;AACpB,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,OAAO,EAAEC,OAAO,EAAEC,YAAY,QAAQ,gBAAgB;AAC/D,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,6BAA6B,EAAEC,KAAK,EAAEC,aAAa,QAAQ,6BAA6B;AACjG,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM,CAAC,CAAC;AACrC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SACEC,OAAO,EACPC,iBAAiB,EACjBC,sBAAsB,EACtBC,0BAA0B,EAC1BC,gBAAgB,EAChBC,sBAAsB,EACtBC,mBAAmB,QACd,oBAAoB;AAC3B,SAASC,OAAO,EAAEC,MAAM,QAAQ,MAAM;AACtC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,kBAAkB,MAAM,gCAAgC,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjE,MAAMC,GAAG,GAAGnD,MAAM,CAACoD,GAAG;AACtB;AACA;AACA,UAAU,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AACxD;AACA;AACA,WAAW,CAAC;EAAEA;AAAQ,CAAC,KACnBA,OAAO,GAAG,oBAAoB,GAAG,oBAAoB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GAtBMH,GAAG;AAuBT,MAAMI,OAAO,GAAGvD,MAAM,CAACoD,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACI,GAAA,GAVID,OAAO;AAYb,MAAME,UAAU,GAAGzD,MAAM,CAAC0D,GAAG;AAC7B;AACA,WAAW,CAAC;EAAEL;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;EAAEA;AAAQ,CAAC,KAAMA,OAAO,GAAG,SAAS,GAAG,QAAS;AACnE,eAAe,CAAC;EAAEA;AAAQ,CAAC,KAAMA,OAAO,GAAG,GAAG,GAAG,GAAI;AACrD,iBAAiB,CAAC;EAAEA;AAAQ,CAAC,KAAMA,OAAO,GAAG,MAAM,GAAG,KAAM;AAC5D;AACA,QAAQ,CAAC;EAAEA;AAAQ,CAAC,KAChBA,OAAO,GAAG,wBAAwB,GAAG,wBAAwB;AACjE;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GA3BIF,UAAU;AA6BhB,MAAMG,gBAAgB,GAAG5D,MAAM,CAACoD,GAAG;AACnC;AACA;AACA;AACA;AACA,aAAa,CAAC;EAAES;AAAY,CAAC,KAAMA,WAAW,GAAG,MAAM,GAAG,EAAG;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXAC,GAAA,GApCMF,gBAAgB;AAiDtB,MAAMG,WAAW,GAAG/D,MAAM,CAACoD,GAAG;AAC9B;AACA,CAAC;AAACY,GAAA,GAFID,WAAW;AAIjB,MAAME,SAAS,GAAGjE,MAAM,CAACoD,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAdID,SAAS;AAgBf,MAAME,mBAAmB,GAAGnE,MAAM,CAACoD,GAAG;AACtC;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GALID,mBAAmB;AAOzB,MAAME,UAAU,GAAGrE,MAAM,CAACsE,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,UAAU;AAiBhB,MAAMG,SAAS,GAAGxE,MAAM,CAACoD,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,CAAC;EAAEqB;AAAK,CAAC,KAAMA,IAAI,GAAG,OAAO,GAAG,MAAO;AACpD,CAAC;AAACC,GAAA,GAXIF,SAAS;AAaf,MAAMG,aAAa,GAAG3E,MAAM,CAACsE,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAM,GAAA,GAhBMD,aAAa;AAiBnB,SAASE,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtF,KAAK,CAACI,QAAQ,CAACmF,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EACxExF,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,MAAMsF,QAAQ,GAAGA,CAAA,KAAMH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5DD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAAC;IAC3C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;EAC7D,CAAC,EAAE,EAAE,CAAC;EACN,OAAOJ,QAAQ;AACjB;AAACD,EAAA,CARQD,WAAW;AAUpB,MAAMS,UAAU,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM4F,kBAAkB,GAAGA,CAAA,KAAM;IAC/BT,MAAM,CAACU,IAAI,CAAC,kFAAkF,EAAE,QAAQ,CAAC;EAC3G,CAAC;EAED,oBACE3C,OAAA,CAACmB,mBAAmB;IAAAyB,QAAA,gBAClB5C,OAAA,CAACqB,UAAU;MAACwB,OAAO,EAAEA,CAAA,KAAMJ,cAAc,CAAC,CAACD,WAAW,CAAE;MAAAI,QAAA,EAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACvEjD,OAAA,CAACwB,SAAS;MAACC,IAAI,EAAEe,WAAY;MAAAI,QAAA,gBAC3B5C,OAAA;QAAKkD,SAAS,EAAC,kBAAkB;QAACL,OAAO,EAAEA,CAAA,KAAMJ,cAAc,CAAC,CAACD,WAAW,CAAE;QAAAI,QAAA,eAC5E5C,OAAA;UACEmD,QAAQ,EAAC,GAAG;UACZC,SAAS,EAAC,SAAS;UACnBC,QAAQ,EAAE,EAAG;UACbH,SAAS,EAAC,YAAY;UACtBI,KAAK,EAAE;YAAEC,MAAM,EAAE,EAAE;YAAEC,OAAO,EAAE;UAAO,CAAE;UAAAZ,QAAA,eAEvC5C,OAAA;YACEyD,KAAK,EAAC,4BAA4B;YAClCC,KAAK,EAAE,EAAG;YACVH,MAAM,EAAE,EAAG;YACXI,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,SAAS;YAChBC,WAAW,EAAE,CAAE;YACfC,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC,OAAO;YACtBd,SAAS,EAAC,mBAAmB;YAAAN,QAAA,gBAE7B5C,OAAA;cAAMiE,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtCjD,OAAA;cAAMiE,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNjD,OAAA;QAAA4C,QAAA,EAAG;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC1BjD,OAAA,CAAC2B,aAAa;QAACkB,OAAO,EAAEH,kBAAmB;QAAAE,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAE1B,CAAC;AAACV,GAAA,CAzCID,UAAU;AAAA+B,GAAA,GAAV/B,UAAU;AA2ChB,MAAMgC,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAG7H,UAAU,CAACwC,WAAW,CAAC;EACxCtC,SAAS,CAAC,MAAM;IACd,IAAI,CAACoF,MAAM,CAACwC,MAAM,EAAE;MAClBxC,MAAM,CAACwC,MAAM,GAAG,EAAE;MAClBxC,MAAM,CAACyC,gBAAgB,GAAG,sCAAsC;MAEhE,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,GAAG,GAAG,gCAAgC;MAC7CH,MAAM,CAACI,KAAK,GAAG,IAAI;MACnBH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,MAAM,CAAC;MAEjCA,MAAM,CAACO,MAAM,GAAG,MAAM;QACpBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;;QAErC;QACAnD,MAAM,CAACwC,MAAM,CAACY,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;;QAE/D;QACA,MAAMC,WAAW,GAAGV,QAAQ,CAACW,aAAa,CAAC,eAAe,CAAC;QAC3D,IAAID,WAAW,EAAE;UACfA,WAAW,CAAChC,KAAK,CAACkC,MAAM,GAAG,IAAI;QACjC;;QAEA;QACA,IAAIhB,IAAI,EAAE;UACRW,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEZ,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAACkB,KAAK,CAAC;UACpEzD,MAAM,CAACwC,MAAM,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,eAAe,EAAE,CAACb,IAAI,CAACiB,IAAI,CAAC,CAAC,CAAC;UACzDxD,MAAM,CAACwC,MAAM,CAACY,IAAI,CAAC,CAAC,KAAK,EAAE,YAAY,EAAE,CAACb,IAAI,CAACkB,KAAK,CAAC,CAAC,CAAC;QACzD;MACF,CAAC;IACH;EAEF,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO,IAAI,CAAC,CAAC;AACf,CAAC;AAACnB,GAAA,CApCID,SAAS;AAAAqB,IAAA,GAATrB,SAAS;AAsCf,MAAMsB,QAAQ,GAAGA,CAAC;AAAA,KAA+B;EAAAC,GAAA;EAAA,IAAAC,mBAAA;EAC/C,MAAM;IAAEzF,OAAO;IAAE0F;EAAW,CAAC,GAAGpJ,UAAU,CAACuC,cAAc,CAAC;EAC1D,MAAM8G,SAAS,GAAGpJ,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACqJ,YAAY,EAAEC,WAAW,CAAC,GAAGpJ,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACqJ,WAAW,EAAEC,cAAc,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuJ,YAAY,EAAEC,eAAe,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyJ,SAAS,EAAEC,YAAY,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2J,QAAQ,EAAEC,WAAW,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6J,OAAO,EAAEC,UAAU,CAAC,GAAG9J,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6F,IAAI,EAAEkE,OAAO,CAAC,GAAG/J,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACgK,SAAS,EAAEC,YAAY,CAAC,GAAGjK,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMiF,QAAQ,GAAGF,WAAW,CAAC,CAAC;EAE9B,MAAMmF,UAAU,GAAGA,CAAA,KAAM;IACvBH,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMI,OAAO,GAAGA,CAAA,KAAM;IACpBJ,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAED,MAAMK,YAAY,GAAG;IACnBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxK,QAAQ,CAACoK,YAAY,CAAC;EAC1D,MAAMK,QAAQ,GAAGxK,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACyK,cAAc,EAAEC,iBAAiB,CAAC,GAAG3K,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4K,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7K,QAAQ,CAAC,KAAK,CAAC;EAEvE,MAAM;IAAE8K;EAAO,CAAC,GAAGjL,UAAU,CAACwC,WAAW,CAAC;EAC1C,MAAM0I,SAAS,GACb,uFAAuF;EAEzF,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMxD,IAAI,GAAGxG,QAAQ,CAACiK,GAAG,CAACC,OAAO,CAACJ,aAAa,EAAED,SAAS,CAAC,CAACM,QAAQ,CAClEnK,QAAQ,CAACoK,GAAG,CAACC,IACf,CAAC;EACD,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAChE,IAAI,CAAC;EAClC;EACA;EACA,MAAMiE,MAAM,GAAGH,SAAS,CAACI,GAAG;EAC5B,MAAMC,QAAQ,GAAGL,SAAS,CAAC7C,IAAI;EAC/B,MAAMmD,SAAS,GAAGN,SAAS,CAAC5C,KAAK;EACjC,IAAImD,OAAO,GAAG,IAAI;EAClB,IAAI,EAAA/C,mBAAA,GAAAwC,SAAS,CAACQ,QAAQ,cAAAhD,mBAAA,uBAAlBA,mBAAA,CAAoBiD,MAAM,IAAG,CAAC,EAAE;IAClCF,OAAO,GAAGP,SAAS,CAACQ,QAAQ,CAAC,CAAC,CAAC;EACjC;EAEA,MAAME,OAAO,GAAGjB,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMiB,YAAY,GAAGV,IAAI,CAACC,KAAK,CAACQ,OAAO,CAAC;EACxC,MAAME,SAAS,GAAGD,YAAY,CAACE,UAAU;EACzC,MAAMC,YAAY,GAAGH,YAAY,CAACP,GAAG;EACrC;EACA;EACA;EACA;EACA;EACA,MAAMW,IAAI,GAAGJ,YAAY,CAACI,IAAI;EAC9B,MAAMC,KAAK,GAAGL,YAAY,CAACK,KAAK;EAChC,MAAMC,WAAW,GAAGN,YAAY,CAACxD,IAAI;EACrC,MAAM+D,iBAAiB,GAAGD,WAAW,CAClCE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,QAAQ,EAAGC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAE5C,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/M,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgN,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjN,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC+D,WAAW,EAAEmJ,cAAc,CAAC,GAAGlN,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmN,YAAY,EAAEC,WAAW,CAAC,GAAGpN,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAMqN,cAAc,GAAGvN,MAAM,CAAC,CAAC;EAC/B,MAAMwN,WAAW,GAAGxN,MAAM,CAAC,CAAC;EAC5B,MAAMyN,iBAAiB,GAAGzN,MAAM,CAAC,CAAC;EAClC,MAAM0N,kBAAkB,GAAG1N,MAAM,CAAC,CAAC;EACnC,MAAM,CAAC2N,SAAS,EAAEC,YAAY,CAAC,GAAG1N,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2N,aAAa,EAAEC,gBAAgB,CAAC,GAAG5N,QAAQ,CAAC,KAAK,CAAC;EACzD,IAAI6N,YAAY,GAAG,IAAI;;EAEvB;EACA;;EAEA;EACA,MAAM;IACJC,qBAAqB;IACrBC,SAAS,EAAEC,qBAAqB;IAChCC,KAAK,EAAEC,iBAAiB;IACxBC,cAAc;IACdC,kBAAkB;IAClBC,oBAAoB;IACpBC,mBAAmB;IACnBC,yBAAyB;IACzBC,aAAa;IACbC,kBAAkB,EAAEC;EACtB,CAAC,GAAG1L,kBAAkB,CAACsJ,YAAY,EAAEpD,SAAS,CAACyF,OAAO,CAAC;;EAEvD;EACA5O,SAAS,CAAC,MAAM;IACd,IAAI+N,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,EAAE;MACpC5D,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;QAC1DsG,KAAK,EAAET,cAAc;QACrBJ,SAAS,EAAEC,qBAAqB;QAChCC,KAAK,EAAEC;MACT,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACC,cAAc,EAAEH,qBAAqB,EAAEE,iBAAiB,CAAC,CAAC;EAE9D,MAAMW,aAAa,GAAG,IAAIC,IAAI,CAACtD,SAAS,CAACuD,SAAS,CAAC;EACnD,MAAMC,KAAK,GAAG,IAAIF,IAAI,CAAC,CAAC;EACxB,MAAMG,eAAe,GAAG,CAACD,KAAK,GAAGH,aAAa,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EACvE;EACA,MAAMK,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,IAAI,CAAC,CAAC,GAAGJ,eAAe,CAAC,EAAE,CAAC,CAAC;EACtE,MAAM,CAACK,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvP,QAAQ,CAAC,IAAI,CAAC;EAE1E,MAAMwP,aAAa,GAAGA,CAAA,KAAM;IAC1BvG,UAAU,CAAC,CAAC1F,OAAO,CAAC;EACtB,CAAC;EAED,MAAMkM,YAAY,GAAGA,CAAA,KAAM;IACzB3E,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAM4E,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,SAAS,GAAG,CAAClC,SAAS;IAC5BC,YAAY,CAACiC,SAAS,CAAC;IACvBtH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqH,SAAS,CAAC;IAC5C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpN,sBAAsB,CAAC8J,YAAY,EAAEqD,SAAS,CAAC;MACtEtH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsH,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACd5F,OAAO,CAAC4F,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAM4B,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,MAAMF,SAAS,GAAG,CAAChC,aAAa;IAChCC,gBAAgB,CAAC+B,SAAS,CAAC;IAC3BtH,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqH,SAAS,CAAC;IACpD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnN,0BAA0B,CAAC6J,YAAY,EAAEqD,SAAS,CAAC;MAC1EtH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsH,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACd5F,OAAO,CAAC4F,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;;EAED;EACA,IAAI,EAAE,cAAc,IAAI9I,MAAM,CAAC,EAAE;IAC/B2K,KAAK,CAAC,qDAAqD,CAAC;EAC9D,CAAC,MAAM,IAAIC,YAAY,CAACC,UAAU,KAAK,QAAQ,EAAE;IAC/C;IACAD,YAAY,CAACE,iBAAiB,CAAC,CAAC;EAClC;EAEA,MAAMC,UAAU,GAAG,MAAOC,QAAQ,IAAK;IACrC/G,WAAW,CAAC,CAACD,YAAY,CAAC;IAC1B+D,cAAc,CAAC,CAACnJ,WAAW,CAAC;IAC5B,MAAM6L,QAAQ,GAAG,MAAMtN,OAAO,CAAC6N,QAAQ,CAAC;IACxC;IACA;IACA,IAAIhH,YAAY,EAAE;MAChBG,cAAc,CAAC6G,QAAQ,CAAC;MACxB3G,eAAe,CAACoG,QAAQ,CAACQ,IAAI,CAAC1I,IAAI,CAACiB,IAAI,CAAC;MACxCe,YAAY,CAACkG,QAAQ,CAACQ,IAAI,CAAC1I,IAAI,CAACkB,KAAK,CAAC;MACtCgB,WAAW,CAACgG,QAAQ,CAACQ,IAAI,CAAC1I,IAAI,CAAC2I,IAAI,CAAC;MAEpC,IAAIT,QAAQ,CAACQ,IAAI,CAAC1I,IAAI,CAAC4I,eAAe,EAAE;QACtC;QACA,MAAMC,eAAe,GAAG,MAAMhO,iBAAiB,CAAC4N,QAAQ,CAAC;QACzD,IAAII,eAAe,CAACH,IAAI,CAACI,OAAO,EAAE;UAChChG,aAAa,CAAC;YACZH,KAAK,EAAEkG,eAAe,CAACH,IAAI,CAACI,OAAO,CAACnE,UAAU;YAC9C/B,KAAK,EACHiG,eAAe,CAACH,IAAI,CAACI,OAAO,CAACnE,UAAU,GACvC,KAAK,GACLkE,eAAe,CAACH,IAAI,CAACI,OAAO,CAACjE,IAAI,GACjC,KAAK,GACLgE,eAAe,CAACH,IAAI,CAACI,OAAO,CAAC7H;UACjC,CAAC,CAAC;QACJ;QACA,IAAI,CAAC4H,eAAe,CAACH,IAAI,CAACI,OAAO,EAAE;UACjC;QAAA;MAEJ,CAAC,MAAM;QACL;QACAhG,aAAa,CAAC;UACZH,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMmG,eAAe,GAAGA,CAAA,KAAM;IAC5BvD,cAAc,CAAC,CAACnJ,WAAW,CAAC;EAC9B,CAAC;EAED,MAAM2M,mBAAmB,GAAGA,CAAA,KAAM;IAChCxD,cAAc,CAAC,CAACnJ,WAAW,CAAC;IAC5BqJ,WAAW,CAAC,CAACD,YAAY,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAG5Q,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6Q,YAAY,EAAEC,eAAe,CAAC,GAAG9Q,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+Q,QAAQ,EAAEC,WAAW,CAAC,GAAGhR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiR,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlR,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACmR,cAAc,EAAEC,iBAAiB,CAAC,GAAGpR,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqR,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtR,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACuR,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxR,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyR,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG1R,QAAQ,CAAC,KAAK,CAAC;;EAE7E;EACA,MAAM,CAAC2R,4BAA4B,EAAEC,+BAA+B,CAAC,GAAG5R,QAAQ,CAAC,CAAC,CAAC;EACnF,MAAM,CAAC6R,8BAA8B,EAAEC,iCAAiC,CAAC,GAAG9R,QAAQ,CAAC,CAAC,CAAC;EACvF,MAAM,CAAC+R,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhS,QAAQ,CAAC,KAAK,CAAC;EACvED,SAAS,CAAC,MAAM;IACd,MAAMkS,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF5J,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;QAElE;QACA,MAAMsH,QAAQ,GAAG,MAAMrN,iBAAiB,CAACoJ,MAAM,CAAC;QAChDd,uBAAuB,CAAC+E,QAAQ,CAACQ,IAAI,CAACI,OAAO,CAAC0B,qBAAqB,CAAC;QACpExE,YAAY,CAACkC,QAAQ,CAACQ,IAAI,CAACI,OAAO,CAAC2B,UAAU,CAAC;QAC9CvE,gBAAgB,CAACgC,QAAQ,CAACQ,IAAI,CAACI,OAAO,CAAC4B,cAAc,CAAC;QACtDxB,gBAAgB,CAAChB,QAAQ,CAACQ,IAAI,CAACI,OAAO,CAAC6B,cAAc,CAAC;;QAEtD;QACA,MAAMC,kBAAkB,GAAGrH,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC;QACtD,MAAMqH,SAAS,GAAGD,kBAAkB,GAChCpR,QAAQ,CAACiK,GAAG,CAACC,OAAO,CAACkH,kBAAkB,EAAEvH,SAAS,CAAC,CAACM,QAAQ,CAC5DnK,QAAQ,CAACoK,GAAG,CAACC,IACf,CAAC,GACC,IAAI;QACR,MAAMiH,GAAG,GAAG1D,IAAI,CAAC0D,GAAG,CAAC,CAAC;;QAEtB;QACA,MAAMC,oBAAoB,GAAG,MAAM9P,sBAAsB,CACvDiN,QAAQ,CAACQ,IAAI,CAACI,OAAO,CAAC5E,GACxB,CAAC;QAEDvD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmK,oBAAoB,CAAC;QAC1D,IAAIA,oBAAoB,CAACC,MAAM,KAAK,GAAG,EAAE;UACvCxB,uBAAuB,CAAC,IAAI,CAAC;QAC/B;QAEAJ,eAAe,CAAC2B,oBAAoB,CAACrC,IAAI,CAACuC,aAAa,IAAI,EAAE,CAAC,CAAC,CAAC;QAChE3B,WAAW,CAACyB,oBAAoB,CAACrC,IAAI,CAACwC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;;QAExD;QACA,MAAMC,YAAY,GAAG3R,QAAQ,CAACiK,GAAG,CAAC2H,OAAO,CACvCrH,IAAI,CAACsH,SAAS,CAACP,GAAG,CAAC,EACnBzH,SACF,CAAC,CAACM,QAAQ,CAAC,CAAC;QACZJ,YAAY,CAAC+H,OAAO,CAAC,KAAK,EAAEH,YAAY,CAAC;QAEzCtD,yBAAyB,CAAC,KAAK,CAAC;MAClC,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACd5F,OAAO,CAAC4F,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CsB,yBAAyB,CAAC,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA0C,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMgB,UAAU,GAAGC,WAAW,CAACjB,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;;IAEpD;IACA,OAAO,MAAMkB,aAAa,CAACF,UAAU,CAAC;;IAEtC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlT,SAAS,CAAC,MAAM;IACd,IAAI,CAACuM,YAAY,EAAE;IAEnB,MAAM8G,qCAAqC,GAAG,MAAAA,CAAA,KAAY;MACxD,IAAI;QACF,MAAMxD,QAAQ,GAAG,MAAMhN,mBAAmB,CAAC0J,YAAY,CAAC;QACxD,MAAMJ,OAAO,GAAG0D,QAAQ,CAACQ,IAAI,CAAClE,OAAO;QAErC,IAAImH,cAAc,GAAG,CAAC;;QAEtB;QACA,IAAInH,OAAO,CAACoH,oBAAoB,IAAIpH,OAAO,CAACoH,oBAAoB,CAACC,gBAAgB,EAAE;UACjFF,cAAc,GAAGnH,OAAO,CAACoH,oBAAoB,CAACC,gBAAgB,CAACtH,MAAM;QACvE,CAAC,MAAM,IAAIC,OAAO,CAACsH,8BAA8B,EAAE;UACjD;UACAH,cAAc,GAAGnH,OAAO,CAACsH,8BAA8B;QACzD;QAEAnL,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7C+K,cAAc;UACdI,WAAW,EAAE5B,8BAA8B;UAC3C6B,mBAAmB,EAAExH,OAAO,CAACoH;QAC/B,CAAC,CAAC;QAEF1B,+BAA+B,CAACyB,cAAc,CAAC;;QAE/C;QACA,MAAMM,oBAAoB,GAAGzH,OAAO,CAACoH,oBAAoB,IAAIpH,OAAO,CAACoH,oBAAoB,CAACM,UAAU;QACpG5B,uBAAuB,CAAC,CAAC2B,oBAAoB,IAAIN,cAAc,GAAGxB,8BAA8B,CAAC;MACnG,CAAC,CAAC,OAAO5D,KAAK,EAAE;QACd5F,OAAO,CAAC4F,KAAK,CAAC,sDAAsD,EAAEA,KAAK,CAAC;MAC9E;IACF,CAAC;IAEDmF,qCAAqC,CAAC,CAAC;EACzC,CAAC,EAAE,CAAC9G,YAAY,EAAEuF,8BAA8B,CAAC,CAAC;;EAElD;EACA9R,SAAS,CAAC,MAAM;IACdqR,iBAAiB,CAAC,IAAI,CAAC;IACvBE,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,qBAAqB,CAAC,KAAK,CAAC;IAC5BE,0BAA0B,CAAC,KAAK,CAAC;EACnC,CAAC,EAAE,CAACX,QAAQ,EAAEF,YAAY,EAAE3B,kBAAkB,CAAC,CAAC;;EAEhD;EACAnP,SAAS,CAAC,MAAM;IACd,IAAI,CAAC,CAACgR,QAAQ,IAAIA,QAAQ,KAAK,EAAE,IAAIF,YAAY,IAAI,CAAC,KAAK3B,kBAAkB,GAAG,CAAC,IAAI,CAAC+B,oBAAoB,IAAI,CAAC3B,sBAAsB,IAAI6B,cAAc,EAAE;MACvJ,MAAM0C,SAAS,GAAGC,UAAU,CAAC,MAAM;QACjCtC,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,MAAMuC,SAAS,GAAGD,UAAU,CAAC,MAAM;QACjC1C,iBAAiB,CAAC,KAAK,CAAC;QACxBI,qBAAqB,CAAC,KAAK,CAAC;MAC9B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAM;QACXwC,YAAY,CAACH,SAAS,CAAC;QACvBG,YAAY,CAACD,SAAS,CAAC;MACzB,CAAC;IACH;EACF,CAAC,EAAE,CAAChD,QAAQ,EAAEF,YAAY,EAAE3B,kBAAkB,EAAE+B,oBAAoB,EAAE3B,sBAAsB,EAAE6B,cAAc,CAAC,CAAC;;EAE9G;EACApR,SAAS,CAAC,MAAM;IACd,IAAIgR,QAAQ,IAAIA,QAAQ,KAAK,YAAY,IAAIF,YAAY,IAAI,CAAC,IAAI5B,eAAe,GAAG,CAAC,IAAIoC,mBAAmB,EAAE;MAC5G,MAAMwC,SAAS,GAAGC,UAAU,CAAC,MAAM;QACjCpC,0BAA0B,CAAC,IAAI,CAAC;MAClC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,MAAMqC,SAAS,GAAGD,UAAU,CAAC,MAAM;QACjCxC,sBAAsB,CAAC,KAAK,CAAC;QAC7BI,0BAA0B,CAAC,KAAK,CAAC;MACnC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAM;QACXsC,YAAY,CAACH,SAAS,CAAC;QACvBG,YAAY,CAACD,SAAS,CAAC;MACzB,CAAC;IACH;EACF,CAAC,EAAE,CAAChD,QAAQ,EAAEF,YAAY,EAAE5B,eAAe,EAAEoC,mBAAmB,CAAC,CAAC;;EAElE;EACA,MAAM4C,eAAe,GAAGA,CAAA,KAAM;IAC5BzC,qBAAqB,CAAC,IAAI,CAAC;IAC3BsC,UAAU,CAAC,MAAM;MACf1C,iBAAiB,CAAC,KAAK,CAAC;MACxBI,qBAAqB,CAAC,KAAK,CAAC;IAC9B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAM0C,oBAAoB,GAAGA,CAAA,KAAM;IACjCxC,0BAA0B,CAAC,IAAI,CAAC;IAChCoC,UAAU,CAAC,MAAM;MACfxC,sBAAsB,CAAC,KAAK,CAAC;MAC7BI,0BAA0B,CAAC,KAAK,CAAC;IACnC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAIE;EACA,MAAMyC,aAAa,GAAGhP,MAAM,CAACiP,QAAQ,CAACC,QAAQ,KAAK,WAAW;EAC9D,MAAMC,MAAM,GAAGH,aAAa,GACxBI,OAAO,CAACC,GAAG,CAACC,wBAAwB,GACpCF,OAAO,CAACC,GAAG,CAACE,yBAAyB;;EAEzC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAYE3U,SAAS,CAAC,MAAM;IACd,IAAI,CAACuM,YAAY,EAAE;;IAEnB;IACA,IAAIpD,SAAS,CAACyF,OAAO,EAAE;MACrBtG,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClDY,SAAS,CAACyF,OAAO,CAACgG,UAAU,CAAC,CAAC;IAChC;IAEAtM,OAAO,CAACC,GAAG,CAAC,6CAA6CgE,YAAY,CAACjB,QAAQ,CAAC,CAAC,KAAK,CAAC;;IAEtF;IACA,MAAMuJ,MAAM,GAAGzT,EAAE,CAACmT,MAAM,EAAE;MACxBO,eAAe,EAAE,IAAI;MACrBC,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,IAAI,EAAE;QAAEC,KAAK,EAAE/J,YAAY,CAACC,OAAO,CAAC,OAAO;MAAE,CAAC;MAC9C+J,YAAY,EAAE,IAAI;MAAE;MACpBC,oBAAoB,EAAE,EAAE;MAAE;MAC1BC,iBAAiB,EAAE,IAAI,CAAE;IAC3B,CAAC,CAAC;;IAEF;IACAjM,SAAS,CAACyF,OAAO,GAAGiG,MAAM;;IAE1B;IACAA,MAAM,CAACQ,IAAI,CAAC,iBAAiB,EAAE;MAC7BC,SAAS,EAAE/I,YAAY,CAACjB,QAAQ,CAAC,CAAC;MAClCiK,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACAV,MAAM,CAACW,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBlN,OAAO,CAACC,GAAG,CAAC,wCAAwCsM,MAAM,CAACY,EAAE,EAAE,CAAC;MAChEZ,MAAM,CAACQ,IAAI,CAAC,eAAe,EAAE;QAAEC,SAAS,EAAE/I,YAAY,CAACjB,QAAQ,CAAC;MAAE,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC,CAAC;;IAEF;IACAuJ,MAAM,CAACW,EAAE,CAAC,mBAAmB,EAAGE,OAAO,IAAK;MAC1CpN,OAAO,CAACC,GAAG,CAAC,8BAA8BmN,OAAO,SAAS,CAAC;IAC7D,CAAC,CAAC;IAEFb,MAAM,CAACW,EAAE,CAAC,WAAW,EAAGE,OAAO,IAAK;MAClCpN,OAAO,CAACC,GAAG,CAAC,mCAAmCmN,OAAO,cAAc,CAAC;MACrEb,MAAM,CAACQ,IAAI,CAAC,eAAe,EAAE;QAAEC,SAAS,EAAE/I,YAAY,CAACjB,QAAQ,CAAC;MAAE,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC,CAAC;;IAEF;IACAuJ,MAAM,CAACW,EAAE,CAAC,eAAe,EAAGtH,KAAK,IAAK;MACpC5F,OAAO,CAAC4F,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,CAAC;;IAEF;IACA2G,MAAM,CAACc,KAAK,CAAC,CAACC,KAAK,EAAEvF,IAAI,KAAK;MAC5B;IAAA,CACD,CAAC;;IAEF;IACAwE,MAAM,CAACW,EAAE,CAAC,YAAY,EAAE,MAAM;MAC5BlN,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvCsN,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;;IAEF;IACAhB,MAAM,CAACW,EAAE,CAAC,cAAc,EAAE,CAAC;MAAEF,SAAS,EAAEQ,gBAAgB;MAAEnD;IAAO,CAAC,KAAK;MACrE,IAAIpG,YAAY,CAACjB,QAAQ,CAAC,CAAC,KAAKwK,gBAAgB,EAAE;QAChDxN,OAAO,CAACC,GAAG,CAAC,wCAAwCoK,MAAM,EAAE,CAAC;QAC7D9B,gBAAgB,CAAC8B,MAAM,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;MAC9D;IACF,CAAC,CAAC;;IAEF;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA,OAAO,MAAM;MACXrK,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1EsM,MAAM,CAACkB,GAAG,CAAC,YAAY,CAAC;MACxBlB,MAAM,CAACkB,GAAG,CAAC,cAAc,CAAC;MAC1B;MACAlB,MAAM,CAACkB,GAAG,CAAC,mBAAmB,CAAC;MAC/BlB,MAAM,CAACkB,GAAG,CAAC,WAAW,CAAC;MACvBlB,MAAM,CAACkB,GAAG,CAAC,eAAe,CAAC;MAC3BlB,MAAM,CAACD,UAAU,CAAC,CAAC;MACnBzL,SAAS,CAACyF,OAAO,GAAG,IAAI;IAC1B,CAAC;EACH,CAAC,EAAE,CAACrC,YAAY,CAAC,CAAC;;EAGlB;EACA,MAAMmC,kBAAkB,GAAIsH,cAAc,IAAK;IAC7C;IACArH,sBAAsB,CAACqH,cAAc,EAAE,qCAAqC,CAAC;IAC7E1N,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEyN,cAAc,CAAC;EACzD,CAAC;;EAED;EACA;EACA;EACA;;EAEA,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC3C;IACA,IAAInG,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACzC,MAAMmG,YAAY,GAAG,IAAIpG,YAAY,CAACkG,KAAK,EAAE;QAC3C,GAAGC,OAAO;QACVE,IAAI,EAAE1V,KAAK,CAAE;MACf,CAAC,CAAC;;MAEF;MACAyV,YAAY,CAACE,MAAM,GAAG,MAAM;QAC1B,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC3V,WAAW,CAAC;QACpC0V,KAAK,CACFE,IAAI,CAAC,CAAC,CACNC,KAAK,CAAExI,KAAK,IACX5F,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE2F,KAAK,CAC/D,CAAC;MACL,CAAC;IACH;EACF,CAAC;EAED,MAAM2H,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMK,KAAK,GAAG,uBAAuB;IACrC,MAAMC,OAAO,GAAG;MACdQ,IAAI,EAAE,8BAA8B;MACpC;MACAC,KAAK,EAAE/V,WAAW,CAAE;IACtB,CAAC;IACDoV,gBAAgB,CAACC,KAAK,EAAEC,OAAO,CAAC;EAClC,CAAC;EAED,MAAMU,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCvO,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1B,MAAMqH,SAAS,GAAG,CAAC/E,oBAAoB;IACvC,IAAI;MACF,MAAMgF,QAAQ,GAAG,MAAMlN,gBAAgB,CAAC4J,YAAY,EAAEqD,SAAS,CAAC;MAChE,IAAIC,QAAQ,CAAC8C,MAAM,KAAK,GAAG,EAAE;QAC3B7H,uBAAuB,CAAC8E,SAAS,CAAC;MACpC,CAAC,MAAM;QACLtH,OAAO,CAAC4F,KAAK,CAAC,sCAAsC,EAAE2B,QAAQ,CAAC;MACjE;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACd5F,OAAO,CAAC4F,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAEDlO,SAAS,CAAC,MAAM;IACd;IACA+H,QAAQ,CAACxC,gBAAgB,CAAC,WAAW,EAAEuR,yBAAyB,CAAC;IACjE/O,QAAQ,CAACxC,gBAAgB,CAAC,WAAW,EAAEwR,4BAA4B,CAAC;IACpE;IACA,OAAO,MAAM;MACXhP,QAAQ,CAACvC,mBAAmB,CAAC,WAAW,EAAEsR,yBAAyB,CAAC;MACpE/O,QAAQ,CAACvC,mBAAmB,CAAC,WAAW,EAAEuR,4BAA4B,CAAC;IACzE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,yBAAyB,GAAIE,CAAC,IAAK;IACvC,IAAI,CAACA,CAAC,CAACC,MAAM,IAAI,CAAC3J,cAAc,CAACsB,OAAO,EAAE;IAE1C,IAAItB,cAAc,CAACsB,OAAO,CAACsI,QAAQ,CAACF,CAAC,CAACC,MAAM,CAAC,EAAE;MAC7C;IACF,CAAC,MAAM;MACL9J,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC;EAED,MAAM4J,4BAA4B,GAAIC,CAAC,IAAK;IAC1C;IACA,IAAI,CAACA,CAAC,CAACC,MAAM,EAAE;;IAEf;IACA,MAAME,eAAe,GAAG3J,iBAAiB,CAACoB,OAAO,IAAIpB,iBAAiB,CAACoB,OAAO,CAACsI,QAAQ,CAACF,CAAC,CAACC,MAAM,CAAC;;IAEjG;IACA,MAAMG,iBAAiB,GAAG3J,kBAAkB,CAACmB,OAAO,IAAInB,kBAAkB,CAACmB,OAAO,CAACsI,QAAQ,CAACF,CAAC,CAACC,MAAM,CAAC;;IAErG;IACA,IAAIE,eAAe,EAAE;MACnB;IACF;;IAEA;IACA,IAAIC,iBAAiB,EAAE;MACrB,OAAO,CAAC;IACV;;IAEA;IACAxM,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED5K,SAAS,CAAC,MAAM;IACd;IACA;IACA;;IAEAoF,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE8R,YAAY,CAAC;IAC/C;IACA,OAAO,MAAM;MACX;MACA;MACAjS,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE6R,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,YAAY,GAAGlS,MAAM,CAACC,UAAU;IACtC,IAAIiS,YAAY,GAAG,IAAI,EAAE;MACvBpO,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAID,MAAMqO,sBAAsB,GAAGA,CAAA,KAAM;IACnC3M,iBAAiB,CAAE4M,QAAQ,IAAK,CAACA,QAAQ,CAAC;EAC5C,CAAC;EAED,oBACErU,OAAA,CAAAE,SAAA;IAAA0C,QAAA,gBACE5C,OAAA,CAAC7B,YAAY;MACX+L,WAAW,EAAEA,WAAY;MACzBD,YAAY,EAAEA,YAAa;MAC3BxB,MAAM,EAAEA;IAAO;MAAA3F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAEFjD,OAAA,CAAC5B,aAAa;MACZ8H,WAAW,EAAEA,WAAY;MACzBD,YAAY,EAAEA,YAAa;MAC3BW,UAAU,EAAEA,UAAW;MACvBS,UAAU,EAAEA,UAAW;MACvBC,aAAa,EAAEA,aAAc;MAC7BnB,WAAW,EAAEA,WAAY;MACzBE,YAAY,EAAEA,YAAa;MAC3BE,SAAS,EAAEA,SAAU;MACrBE,QAAQ,EAAEA,QAAS;MACnB6N,UAAU,EAAE;IAAK;MAAAxR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAEFjD,OAAA,CAAC3B,iBAAiB;MAChBuL,gBAAgB,EAAEA,gBAAiB;MACnCC,mBAAmB,EAAEA,mBAAoB;MACzCL,iBAAiB,EAAEA;IAAkB;MAAA1G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAEFjD,OAAA,CAAC1B,sBAAsB;MACrBwL,qBAAqB,EAAEA,qBAAsB;MAC7CC,wBAAwB,EAAEA,wBAAyB;MACnDP,iBAAiB,EAAEA;IAAkB;MAAA1G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAEFjD,OAAA,CAAC5C,WAAW,CAACmX,QAAQ;MAACpN,KAAK,EAAE;QAAEqN,KAAK,EAAE;MAAO,CAAE;MAAA5R,QAAA,gBAC7C5C,OAAA,CAACG,GAAG;QAACE,OAAO,EAAEA,OAAQ;QAACiD,KAAK,EAAE;UAAEmR,cAAc,EAAE;QAAgB,CAAE;QAAA7R,QAAA,gBAChE5C,OAAA;UAAKkD,SAAS,EAAC,gCAAgC;UAAAN,QAAA,gBAC7C5C,OAAA;YAAK6C,OAAO,EAAEmE,UAAW;YAAC9D,SAAS,EAAC,qBAAqB;YAACI,KAAK,EAAE;cAAEoR,MAAM,EAAE;YAAU,CAAE;YAAA9R,QAAA,eACrF5C,OAAA,CAACvB,eAAe;cAAC+V,KAAK,EAAC;YAAO;cAAA1R,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACNjD,OAAA;YACEkD,SAAS,EAAC,eAAe;YACzBI,KAAK,EAAE;cACLqR,UAAU,EAAE,KAAK;cACjBC,aAAa,EAAE,KAAK;cACpBpR,OAAO,EAAEzB,QAAQ,GAAG,MAAM,GAAG;YAC/B,CAAE;YAAAa,QAAA,eAEF5C,OAAA;cAAK8E,GAAG,EAAErH,OAAQ;cAACoX,GAAG,EAAC,cAAc;cAACnR,KAAK,EAAE,GAAI;cAACH,MAAM,EAAE,EAAG;cAACL,SAAS,EAAC;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjD,OAAA;UAAKkD,SAAS,EAAC,QAAQ;UAAAN,QAAA,gBAUrB5C,OAAA;YACEsD,KAAK,EAAE;cACLwR,OAAO,EAAE,mBAAmB;cAC5BC,UAAU,EAAE,8BAA8B;cAC1CxR,MAAM,EAAE,EAAE;cACVyR,aAAa,EAAE,QAAQ;cACvBP,cAAc,EAAE,QAAQ;cACxBQ,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,KAAK;cACV1R,OAAO,EAAE,aAAa;cACtB2R,QAAQ,EAAE,UAAU;cACpBT,MAAM,EAAE;YACV,CAAE;YACFU,GAAG,EAAE/K,iBAAkB;YACvBxH,OAAO,EAAEuR;YACX;YACA;YAAA;YAAAxR,QAAA,gBAEE5C,OAAA;cAAKkD,SAAS,EAAC,eAAe;cAAAN,QAAA,eAC5B5C,OAAA;gBAAMsD,KAAK,EAAE;kBAAE+R,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEC,UAAU,EAAE;gBAAE,CAAE;gBAAA3S,QAAA,EAAC;cAE/D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNjD,OAAA,CAACnC,OAAO;cAACyF,KAAK,EAAE;gBAAEkR,KAAK,EAAE,OAAO;gBAAEa,QAAQ,EAAE;cAAG;YAAE;cAAAvS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDjD,OAAA;cACEkD,SAAS,EAAC,eAAe;cACzBI,KAAK,EAAE;gBACLgS,UAAU,EAAE,MAAM;gBAClB9R,OAAO,EAAE,MAAM;gBACfiR,cAAc,EAAE,QAAQ;gBACxBY,QAAQ,EAAE,EAAE;gBACZF,QAAQ,EAAE;cACZ,CAAE;cAAAvS,QAAA,gBAEF5C,OAAA;gBAAA4C,QAAA,EAAM;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBjD,OAAA,CAACpC,mBAAmB;gBAAC0F,KAAK,EAAE;kBAAEkR,KAAK,EAAE,OAAO;kBAAEa,QAAQ,EAAE;gBAAG;cAAE;gBAAAvS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAGNjD,OAAA;cACEoV,GAAG,EAAE9K,kBAAmB;cACxBpH,SAAS,EACPsE,cAAc,GAAG,mBAAmB,GAAG,qBACxC;cACDlE,KAAK,EAAE;gBAAE6R,QAAQ,EAAE;cAAW,CAAE;cAChCtS,OAAO,EAAG2S,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;cACpB,CAAE;cAAA9S,QAAA,eAEF5C,OAAA;gBAAKkD,SAAS,EAAC,cAAc;gBAACL,OAAO,EAAG2S,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;gBAAA7S,QAAA,gBAChE5C,OAAA;kBAAKkD,SAAS,EAAC,eAAe;kBAACL,OAAO,EAAG2S,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;kBAAA7S,QAAA,gBACjE5C,OAAA;oBAAKkD,SAAS,EAAC,aAAa;oBAAAN,QAAA,gBAC1B5C,OAAA;sBAAKkD,SAAS,EAAC;oBAAsB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxCjD,OAAA;sBAAKkD,SAAS,EAAC,cAAc;sBAACL,OAAO,EAAE2J,kBAAmB;sBAAClJ,KAAK,EAAE;wBAAEoR,MAAM,EAAE;sBAAU,CAAE;sBAAA9R,QAAA,gBACtF5C,OAAA;wBACEmD,QAAQ,EAAC,UAAU;wBACnBwS,UAAU,EAAE,CAAE;wBACdvS,SAAS,EAAC,SAAS;wBACnBC,QAAQ,EAAE,EAAG;wBACb,0BAAuB,EAAE;wBACzBC,KAAK,EAAE;0BAAEC,MAAM,EAAE;wBAAG,CAAE;wBAAAX,QAAA,eAEtB5C,OAAA;0BACE0D,KAAK,EAAE,EAAG;0BACVH,MAAM,EAAE,EAAG;0BACXK,IAAI,EAAC,SAAS;0BACdD,OAAO,EAAC,WAAW;0BACnBF,KAAK,EAAC,4BAA4B;0BAAAb,QAAA,eAElC5C,OAAA;4BACE4V,CAAC,EAAC,yyDAAyyD;4BAC3yDhS,IAAI,EAAC;0BAAS;4BAAAd,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACf;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CAAC,eACdjD,OAAA;wBAAMkD,SAAS,EAAC,aAAa;wBAAAN,QAAA,EAAC;sBAE9B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENjD,OAAA;oBAAKkD,SAAS,EAAC,SAAS;oBAAAN,QAAA,eACtB5C,OAAA;sBAAKkD,SAAS,EAAC,kBAAkB;sBAAAN,QAAA,eAC/B5C,OAAA;wBACE6V,IAAI,EAAC,UAAU;wBACf3S,SAAS,EAAC,UAAU;wBACpB4S,OAAO,EAAEvL,SAAU;wBACnBwL,QAAQ,EAAEvJ;sBAAmB;wBAAA1J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjD,OAAA;kBACEkD,SAAS,EAAC,oBAAoB;kBAC9BI,KAAK,EAAE;oBAAE0S,YAAY,EAAE;kBAAM,CAAE;kBAC/BnT,OAAO,EAAG2S,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;kBAAA7S,QAAA,gBAEpC5C,OAAA;oBAAKkD,SAAS,EAAC,aAAa;oBAAAN,QAAA,gBAC1B5C,OAAA;sBAAKkD,SAAS,EAAC;oBAAsB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxCjD,OAAA;sBACEkD,SAAS,EAAC,cAAc;sBACxBL,OAAO,EAAE8J,yBAA0B;sBACnCrJ,KAAK,EAAE;wBAAEE,OAAO,EAAE,aAAa;wBAAEyR,UAAU,EAAE,QAAQ;wBAAEP,MAAM,EAAE;sBAAU,CAAE;sBAAA9R,QAAA,gBAE3E5C,OAAA;wBACEmD,QAAQ,EAAC,OAAO;wBAChBwS,UAAU,EAAE,CAAE;wBACdvS,SAAS,EAAC,SAAS;wBACnBC,QAAQ,EAAE,EAAG;wBACb,0BAAuB,EAAE;wBACzBC,KAAK,EAAE;0BAAEC,MAAM,EAAE;wBAAG,CAAE;wBAAAX,QAAA,eAEtB5C,OAAA;0BACEyD,KAAK,EAAC,4BAA4B;0BAClCC,KAAK,EAAE,EAAG;0BACVH,MAAM,EAAE,EAAG;0BACXI,OAAO,EAAC,WAAW;0BACnBC,IAAI,EAAC,MAAM;0BACXC,MAAM,EAAC,SAAS;0BAChBC,WAAW,EAAE,CAAE;0BACfC,aAAa,EAAC,OAAO;0BACrBC,cAAc,EAAC,OAAO;0BACtBd,SAAS,EAAC,uBAAuB;0BAAAN,QAAA,gBAEjC5C,OAAA;4BAAM4V,CAAC,EAAC;0BAA2C;4BAAA9S,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACtDjD,OAAA;4BAAQiW,EAAE,EAAE,CAAE;4BAACC,EAAE,EAAE,CAAE;4BAACC,CAAC,EAAE;0BAAE;4BAAArT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC9BjD,OAAA;4BAAM4V,CAAC,EAAC;0BAA4B;4BAAA9S,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvCjD,OAAA;4BAAM4V,CAAC,EAAC;0BAA2B;4BAAA9S,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CAAC,eACdjD,OAAA;wBAAKsD,KAAK,EAAE;0BAAEE,OAAO,EAAE,aAAa;0BAAE4S,UAAU,EAAE;wBAAO,CAAE;wBAAAxT,QAAA,gBACzD5C,OAAA;0BAAMkD,SAAS,EAAC,aAAa;0BAAAN,QAAA,EAAC;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC3CjD,OAAA;0BAAMkD,SAAS,EAAC,aAAa;0BAAAN,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNjD,OAAA;oBAAKkD,SAAS,EAAC,SAAS;oBAAAN,QAAA,eACtB5C,OAAA;sBAAKkD,SAAS,EAAC,kBAAkB;sBAAAN,QAAA,eAC/B5C,OAAA;wBACE6V,IAAI,EAAC,UAAU;wBACf3S,SAAS,EAAC,UAAU;wBACpB4S,OAAO,EAAErL,aAAc;wBACvBsL,QAAQ,EAAEA,CAAA,KAAMpJ,yBAAyB,CAAC;sBAAE;wBAAA7J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjD,OAAA,CAACL,OAAO;YACN0W,SAAS,EAAC,QAAQ;YAClBtD,KAAK,EAAE,eAAetF,aAAa,KAAK,SAAS,GAAG,SAAS,GAAG,WAAW,EAAG;YAAA7K,QAAA,eAE9E5C,OAAA;cAAKsD,KAAK,EAAE;gBACVwR,OAAO,EAAE,mBAAmB;gBAC5BC,UAAU,EAAE,8BAA8B;gBAC1CxR,MAAM,EAAE,EAAE;gBACVyR,aAAa,EAAE,KAAK;gBACpBP,cAAc,EAAE,QAAQ;gBACxBQ,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,KAAK;gBACV1R,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,gBACA5C,OAAA,CAAC9B,SAAS;gBAACsW,KAAK,EAAE/G,aAAa,KAAK,SAAS,GAAG,KAAK,GAAG,SAAU;gBAAC4H,QAAQ,EAAE;cAAG;gBAAAvS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnFjD,OAAA;gBAAKkD,SAAS,EAAC,eAAe;gBAAAN,QAAA,gBAC5B5C,OAAA;kBAAA4C,QAAA,eACE5C,OAAA;oBAAMsD,KAAK,EAAE;sBAAE+R,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE,GAAG;sBAAEC,UAAU,EAAE;oBAAE,CAAE;oBAAA3S,QAAA,EAAC;kBAE/D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACLwK,aAAa,IAAIA,aAAa,KAAK,SAAS,gBAC3CzN,OAAA;kBAAKkD,SAAS,EAAC,6BAA6B;kBAAAN,QAAA,eAC1C5C,OAAA;oBAAA4C,QAAA,EAAM;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,gBAENjD,OAAA;kBAAKkD,SAAS,EAAC,sBAAsB;kBAAAN,QAAA,eACnC5C,OAAA;oBAAA4C,QAAA,EAAM;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACVjD,OAAA,CAACL,OAAO;YACN0W,SAAS,EAAC,QAAQ;YAClBtD,KAAK,EAAE,mBAAoB;YAAAnQ,QAAA,eAE3B5C,OAAA;cACEsD,KAAK,EAAE;gBACLwR,OAAO,EAAE,UAAU;gBACnBC,UAAU,EAAE,8BAA8B;gBAC1CxR,MAAM,EAAE,EAAE;gBACVyR,aAAa,EAAE,QAAQ;gBACvBP,cAAc,EAAE,QAAQ;gBACxBQ,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,KAAK;gBACV1R,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,eAEF5C,OAAA;gBACE6C,OAAO,EAAEA,CAAA,KAAMgH,mBAAmB,CAAC,IAAI,CAAE;gBACzCvG,KAAK,EAAE;kBACLoR,MAAM,EAAE,SAAS;kBACjBlR,OAAO,EAAE,MAAM;kBACfyR,UAAU,EAAE;gBACd,CAAE;gBAAArS,QAAA,gBAEF5C,OAAA;kBAAK8E,GAAG,EAAElG,aAAc;kBAAC2E,MAAM,EAAE,EAAG;kBAACG,KAAK,EAAE;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClDjD,OAAA;kBAAKkD,SAAS,EAAC,kCAAkC;kBAAAN,QAAA,gBAC/C5C,OAAA;oBAAMsD,KAAK,EAAE;sBAAE+R,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE,GAAG;sBAAEC,UAAU,EAAE;oBAAE,CAAE;oBAAA3S,QAAA,EAAC;kBAE/D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPjD,OAAA;oBAAMsD,KAAK,EAAE;sBAAE+R,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE,GAAG;sBAAEC,UAAU,EAAE;oBAAE,CAAE;oBAAA3S,QAAA,EAAC;kBAE/D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEVjD,OAAA,CAACL,OAAO;YACN0W,SAAS,EAAC,QAAQ;YAClBtD,KAAK,EAAE,gBAAiB;YAAAnQ,QAAA,eAExB5C,OAAA;cACEsD,KAAK,EAAE;gBACLwR,OAAO,EAAE,UAAU;gBACnBC,UAAU,EAAE,8BAA8B;gBAC1CxR,MAAM,EAAE,EAAE;gBACVyR,aAAa,EAAE,QAAQ;gBACvBP,cAAc,EAAE,QAAQ;gBACxBQ,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,KAAK;gBACV1R,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,eAEF5C,OAAA;gBACE6C,OAAO,EAAEA,CAAA,KAAMkH,wBAAwB,CAAC,IAAI,CAAE;gBAC9CzG,KAAK,EAAE;kBACLoR,MAAM,EAAE,SAAS;kBACjBlR,OAAO,EAAE,MAAM;kBACfyR,UAAU,EAAE;gBACd,CAAE;gBAAArS,QAAA,gBAEF5C,OAAA;kBAAK8E,GAAG,EAAEnG,UAAW;kBAAC4E,MAAM,EAAE,EAAG;kBAACG,KAAK,EAAE;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CjD,OAAA;kBAAKkD,SAAS,EAAC,kCAAkC;kBAAAN,QAAA,gBAC/C5C,OAAA;oBAAMsD,KAAK,EAAE;sBAAE+R,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE,GAAG;sBAAEC,UAAU,EAAE;oBAAE,CAAE;oBAAA3S,QAAA,EAAC;kBAE/D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPjD,OAAA;oBAAMsD,KAAK,EAAE;sBAAE+R,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE,GAAG;sBAAEC,UAAU,EAAE;oBAAE,CAAE;oBAAA3S,QAAA,EAAC;kBAE/D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEVjD,OAAA;YACEsD,KAAK,EAAE;cACLwR,OAAO,EAAE,mBAAmB;cAC5BvR,MAAM,EAAE,EAAE;cACVyR,aAAa,EAAE,QAAQ;cACvBP,cAAc,EAAE,QAAQ;cACxBQ,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,KAAK;cACV1R,OAAO,EAAE,aAAa;cACtBuR,UAAU,EAAE;YACd,CAAE;YACF7R,SAAS,EAAC,kBAAkB;YAAAN,QAAA,gBAE5B5C,OAAA;cAAA4C,QAAA,gBACE5C,OAAA,CAACrC,YAAY;gBAAC2F,KAAK,EAAE;kBAAEkR,KAAK,EAAE,OAAO;kBAAEa,QAAQ,EAAE;gBAAG;cAAE;gBAAAvS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDjD,OAAA;gBAAMsD,KAAK,EAAE;kBAAE+R,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE,GAAG;kBAAEC,UAAU,EAAE;gBAAE,CAAE;gBAAA3S,QAAA,GAAC,MAE7D,EAAC,CAAC8E,oBAAoB,gBACpB1H,OAAA;kBACEsD,KAAK,EAAE;oBACLI,KAAK,EAAE,EAAE;oBACTH,MAAM,EAAE,EAAE;oBACV+S,YAAY,EAAE,EAAE;oBAChBf,UAAU,EAAE,EAAE;oBACdgB,UAAU,EAAE,SAAS;oBACrB9B,cAAc,EAAE,QAAQ;oBACxBQ,UAAU,EAAE,QAAQ;oBACpBC,GAAG,EAAE,CAAC;oBACN1R,OAAO,EAAE;kBACX,CAAE;kBAAAZ,QAAA,eAEF5C,OAAA;oBAAA4C,QAAA,EAAM;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,gBAENjD,OAAA;kBACEsD,KAAK,EAAE;oBACLI,KAAK,EAAE,EAAE;oBACTH,MAAM,EAAE,EAAE;oBACV+S,YAAY,EAAE,EAAE;oBAChBf,UAAU,EAAE,EAAE;oBACdgB,UAAU,EAAE,WAAW;oBACvB9B,cAAc,EAAE,QAAQ;oBACxBQ,UAAU,EAAE,QAAQ;oBACpBC,GAAG,EAAE,CAAC;oBACNV,KAAK,EAAE,OAAO;oBACda,QAAQ,EAAE,EAAE;oBACZ7R,OAAO,EAAE;kBACX,CAAE;kBAAAZ,QAAA,eAEF5C,OAAA;oBAAA4C,QAAA,EAAM;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNjD,OAAA;cACEsD,KAAK,EAAE;gBACLgS,UAAU,EAAE,MAAM;gBAClB9R,OAAO,EAAE,MAAM;gBACfiR,cAAc,EAAE,QAAQ;gBACxBY,QAAQ,EAAE;cACZ,CAAE;cAAAzS,QAAA,eAEF5C,OAAA;gBAAA4C,QAAA,EAAO2G;cAAW;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACLyE,oBAAoB,gBACnB1H,OAAA;cACE6V,IAAI,EAAC,QAAQ;cACbhT,OAAO,EAAEA,CAAA,KAAM6Q,mBAAmB,CAAC,CAAE;cACrCpQ,KAAK,EAAE;gBACLI,KAAK,EAAE,GAAG;gBACVH,MAAM,EAAE,EAAE;gBACV+S,YAAY,EAAE,CAAC;gBACfC,UAAU,EAAE,SAAS;gBACrB9B,cAAc,EAAE,QAAQ;gBACxBQ,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,CAAC;gBACN1R,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,eAEF5C,OAAA;gBAAGsD,KAAK,EAAE;kBAAEkR,KAAK,EAAE,OAAO;kBAAEa,QAAQ,EAAE;gBAAG,CAAE;gBAAAzS,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,gBAENjD,OAAA;cACE6V,IAAI,EAAC,QAAQ;cACbhT,OAAO,EAAEA,CAAA,KAAM6Q,mBAAmB,CAAC,CAAE;cACrCpQ,KAAK,EAAE;gBACLI,KAAK,EAAE,GAAG;gBACVH,MAAM,EAAE,EAAE;gBACV+S,YAAY,EAAE,CAAC;gBACfC,UAAU,EAAE,SAAS;gBACrB9B,cAAc,EAAE,QAAQ;gBACxBQ,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,CAAC;gBACN1R,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,eAEF5C,OAAA;gBAAGsD,KAAK,EAAE;kBAAEkR,KAAK,EAAE,OAAO;kBAAEa,QAAQ,EAAE;gBAAG,CAAE;gBAAAzS,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNjD,OAAA;YAAKkD,SAAS,EAAC,eAAe;YAACI,KAAK,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,MAAM,EAAE,EAAE;cAAEiT,WAAW,EAAE,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAE1B,UAAU,EAAE,mBAAmB;cAAEC,aAAa,EAAE,QAAQ;cAAEP,cAAc,EAAE,QAAQ;cAAEQ,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE,CAAC;cAAE1R,OAAO,EAAE;YAAc,CAAE;YAAAZ,QAAA,eAC5O5C,OAAA;cAAK6C,OAAO,EAAE0J,YAAa;cAACjJ,KAAK,EAAE;gBAAEoT,SAAS,EAAE,QAAQ;gBAAEhC,MAAM,EAAE,SAAS;gBAAEF,KAAK,EAAE,SAAS;gBAAEa,QAAQ,EAAE,EAAE;gBAAEC,UAAU,EAAE,KAAK;gBAAEqB,aAAa,EAAE,IAAI;gBAAEC,QAAQ,EAAE;cAAa,CAAE;cAAAhU,QAAA,gBAC5K5C,OAAA,CAACtB,WAAW;gBAAC4E,KAAK,EAAE;kBAAEkR,KAAK,EAAE,OAAO;kBAAEa,QAAQ,EAAE;gBAAG;cAAE;gBAAAvS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAC1D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNjD,OAAA,CAACJ,MAAM;QAACmT,KAAK,EAAC,iBAAW;QAAC9L,OAAO,EAAEA,OAAQ;QAACtE,IAAI,EAAEA,IAAK;QAAY0T,SAAS,EAAC,MAAM;QAACQ,SAAS,EAAE;UAAE/B,OAAO,EAAE;QAAE,CAAE;QAAAlS,QAAA,eAC5G5C,OAAA;UAAKkD,SAAS,EAAC,kDAAkD;UAAAN,QAAA,gBAC/D5C,OAAA;YACEkD,SAAS,EAAC,mBAAmB;YAC7BI,KAAK,EAAE;cAAEwT,SAAS,EAAE,QAAQ;cAAEvT,MAAM,EAAE;YAAsB,CAAE;YAAAX,QAAA,EAE7D1F,WAAW,CAAC6Z,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;cAChC,oBACEjX,OAAA,CAAC3C,cAAc;gBAEb6Z,WAAW,EAAE,CAACha,WAAW,CAAC+Z,KAAK,CAAC,CAACnK,UAAU,CAAE;gBAAAlK,QAAA,eAE7C5C,OAAA,CAAC7C,OAAO;kBACN6Z,IAAI,EAAEA,IAAK;kBAEX1T,KAAK,EAAE;oBAAEiT,UAAU,EAAE;kBAAQ;gBAAE,GAD1BU,KAAK;kBAAAnU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEX;cAAC,GAPGgU,KAAK;gBAAAnU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQI,CAAC;YAErB,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNjD,OAAA;YAAKkD,SAAS,EAAC,oBAAoB;YAAAN,QAAA,gBACjC5C,OAAA;cACEsD,KAAK,EAAE;gBACLwR,OAAO,EAAE,mBAAmB;gBAC5BC,UAAU,EAAE,8BAA8B;gBAC1CxR,MAAM,EAAE,EAAE;gBACVyR,aAAa,EAAE,QAAQ;gBACvBP,cAAc,EAAE,QAAQ;gBACxBQ,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE,KAAK;gBACV1R,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,gBAEF5C,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA,CAACrC,YAAY;kBAAC2F,KAAK,EAAE;oBAAEkR,KAAK,EAAE,OAAO;oBAAEa,QAAQ,EAAE;kBAAG;gBAAE;kBAAAvS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzDjD,OAAA;kBAAMsD,KAAK,EAAE;oBAAE+R,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE,GAAG;oBAAEC,UAAU,EAAE;kBAAE,CAAE;kBAAA3S,QAAA,GAAC,MAE7D,EAAC,CAAC8E,oBAAoB,gBACpB1H,OAAA;oBACEsD,KAAK,EAAE;sBACLI,KAAK,EAAE,EAAE;sBACTH,MAAM,EAAE,EAAE;sBACV+S,YAAY,EAAE,EAAE;sBAChBf,UAAU,EAAE,EAAE;sBACdgB,UAAU,EAAE,SAAS;sBACrB9B,cAAc,EAAE,QAAQ;sBACxBQ,UAAU,EAAE,QAAQ;sBACpBC,GAAG,EAAE,CAAC;sBACN1R,OAAO,EAAE;oBACX,CAAE;oBAAAZ,QAAA,eAEF5C,OAAA;sBAAA4C,QAAA,EAAM;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,gBAENjD,OAAA;oBACEsD,KAAK,EAAE;sBACLI,KAAK,EAAE,EAAE;sBACTH,MAAM,EAAE,EAAE;sBACV+S,YAAY,EAAE,EAAE;sBAChBf,UAAU,EAAE,EAAE;sBACdgB,UAAU,EAAE,WAAW;sBACvB9B,cAAc,EAAE,QAAQ;sBACxBQ,UAAU,EAAE,QAAQ;sBACpBC,GAAG,EAAE,CAAC;sBACNV,KAAK,EAAE,OAAO;sBACda,QAAQ,EAAE,EAAE;sBACZ7R,OAAO,EAAE;oBACX,CAAE;oBAAAZ,QAAA,eAEF5C,OAAA;sBAAA4C,QAAA,EAAM;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNjD,OAAA;gBACEsD,KAAK,EAAE;kBACLgS,UAAU,EAAE,MAAM;kBAClB9R,OAAO,EAAE,MAAM;kBACfiR,cAAc,EAAE,QAAQ;kBACxBY,QAAQ,EAAE;gBACZ,CAAE;gBAAAzS,QAAA,eAEF5C,OAAA;kBAAA4C,QAAA,EAAO2G;gBAAW;kBAAAzG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,EACLyE,oBAAoB,gBACnB1H,OAAA;gBACE6V,IAAI,EAAC,QAAQ;gBACbhT,OAAO,EAAEA,CAAA,KAAM6Q,mBAAmB,CAAC,CAAE;gBACrCpQ,KAAK,EAAE;kBACLI,KAAK,EAAE,GAAG;kBACVH,MAAM,EAAE,EAAE;kBACV+S,YAAY,EAAE,CAAC;kBACfC,UAAU,EAAE,SAAS;kBACrB9B,cAAc,EAAE,QAAQ;kBACxBQ,UAAU,EAAE,QAAQ;kBACpBC,GAAG,EAAE,CAAC;kBACN1R,OAAO,EAAE;gBACX,CAAE;gBAAAZ,QAAA,eAEF5C,OAAA;kBAAGsD,KAAK,EAAE;oBAAEkR,KAAK,EAAE,OAAO;oBAAEa,QAAQ,EAAE;kBAAG,CAAE;kBAAAzS,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,gBAENjD,OAAA;gBACE6V,IAAI,EAAC,QAAQ;gBACbhT,OAAO,EAAEA,CAAA,KAAM6Q,mBAAmB,CAAC,CAAE;gBACrCpQ,KAAK,EAAE;kBACLI,KAAK,EAAE,GAAG;kBACVH,MAAM,EAAE,EAAE;kBACV+S,YAAY,EAAE,CAAC;kBACfC,UAAU,EAAE,SAAS;kBACrB9B,cAAc,EAAE,QAAQ;kBACxBQ,UAAU,EAAE,QAAQ;kBACpBC,GAAG,EAAE,CAAC;kBACN1R,OAAO,EAAE;gBACX,CAAE;gBAAAZ,QAAA,eAEF5C,OAAA;kBAAGsD,KAAK,EAAE;oBAAEkR,KAAK,EAAE,OAAO;oBAAEa,QAAQ,EAAE;kBAAG,CAAE;kBAAAzS,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNjD,OAAA;cAAKkD,SAAS,EAAC,eAAe;cAACI,KAAK,EAAE;gBAAEI,KAAK,EAAE,GAAG;gBAAEH,MAAM,EAAE,EAAE;gBAAEiT,WAAW,EAAE,EAAE;gBAAEC,YAAY,EAAE,EAAE;gBAAE1B,UAAU,EAAE,mBAAmB;gBAAEC,aAAa,EAAE,QAAQ;gBAAEP,cAAc,EAAE,QAAQ;gBAAEQ,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE,CAAC;gBAAE1R,OAAO,EAAE;cAAc,CAAE;cAAAZ,QAAA,eAC5O5C,OAAA;gBAAK6C,OAAO,EAAE0J,YAAa;gBAACjJ,KAAK,EAAE;kBAAEoT,SAAS,EAAE,QAAQ;kBAAEhC,MAAM,EAAE,SAAS;kBAAEF,KAAK,EAAE,SAAS;kBAAEa,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE,KAAK;kBAAEqB,aAAa,EAAE,IAAI;kBAAEC,QAAQ,EAAE;gBAAa,CAAE;gBAAAhU,QAAA,gBAC5K5C,OAAA,CAACtB,WAAW;kBAAC4E,KAAK,EAAE;oBAAEkR,KAAK,EAAE,OAAO;oBAAEa,QAAQ,EAAE;kBAAG;gBAAE;kBAAAvS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,QAC1D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA/HoD,MAAM;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgI1D,CAAC,EACR,CAAC,CAAC4K,QAAQ,IAAIA,QAAQ,KAAK,EAAE,IAAIF,YAAY,IAAI,CAAC,KAAK3B,kBAAkB,IAAIiC,cAAc,GAC1F,CAACF,oBAAoB,IAAI,CAAC3B,sBAAsB,gBAC9CpM,OAAA;QACEkD,SAAS,EAAC,cAAc;QACxBI,KAAK,EAAE;UACL6R,QAAQ,EAAE,UAAU;UACpBgC,OAAO,EAAE9I,kBAAkB,GAAG,CAAC,GAAG,CAAC;UACnC+I,SAAS,EAAE/I,kBAAkB,GAAG,mBAAmB,GAAG,eAAe;UACrEgJ,UAAU,EAAE,wCAAwC;UACpD7T,OAAO,EAAE,MAAM;UACfyR,UAAU,EAAE,QAAQ;UACpBR,cAAc,EAAE,QAAQ;UAAE;UAC1B+B,WAAW,EAAE,MAAM;UACnBC,YAAY,EAAE;QAChB,CAAE;QAAA7T,QAAA,gBAEF5C,OAAA;UAAMsD,KAAK,EAAE;YAAEoT,SAAS,EAAE;UAAS,CAAE;UAAA9T,QAAA,GAAC,2BACX,EAACoJ,kBAAkB,EAAC,OAC/C;QAAA;UAAAlJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPjD,OAAA;UACE6C,OAAO,EAAEkO,eAAgB;UACzBzN,KAAK,EAAE;YACLiT,UAAU,EAAE,uBAAuB;YACnCe,MAAM,EAAE,iCAAiC;YACzChB,YAAY,EAAE,KAAK;YACnB5S,KAAK,EAAE,MAAM;YACbH,MAAM,EAAE,MAAM;YACdmR,MAAM,EAAE,SAAS;YACjBW,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,MAAM;YAClBd,KAAK,EAAE,MAAM;YACbhR,OAAO,EAAE,MAAM;YACfyR,UAAU,EAAE,QAAQ;YACpBR,cAAc,EAAE,QAAQ;YACxB4C,UAAU,EAAE,eAAe;YAC3B9B,UAAU,EAAE,MAAM;YAClBgC,SAAS,EAAE,2BAA2B;YACtCC,UAAU,EAAE;UACd,CAAE;UACFC,WAAW,EAAGjC,CAAC,IAAK;YAClBA,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAACiT,UAAU,GAAG,qBAAqB;YACjDf,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAACkR,KAAK,GAAG,MAAM;YAC7BgB,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAAC8T,SAAS,GAAG,YAAY;UACzC,CAAE;UACFM,UAAU,EAAGlC,CAAC,IAAK;YACjBA,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAACiT,UAAU,GAAG,uBAAuB;YACnDf,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAACkR,KAAK,GAAG,MAAM;YAC7BgB,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAAC8T,SAAS,GAAG,UAAU;UACvC,CAAE;UACFrE,KAAK,EAAC,cAAc;UAAAnQ,QAAA,EACrB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GAEN,IAAI,GACJ,IAAI,EACP4K,QAAQ,IAAIA,QAAQ,KAAK,YAAY,IAAIF,YAAY,IAAI,CAAC,IAAI5B,eAAe,GAAG,CAAC,IAAIoC,mBAAmB,gBACvGnO,OAAA;QACEkD,SAAS,EAAC,cAAc;QACxBI,KAAK,EAAE;UACL6R,QAAQ,EAAE,UAAU;UACpBgC,OAAO,EAAE5I,uBAAuB,GAAG,CAAC,GAAG,CAAC;UACxC6I,SAAS,EAAE7I,uBAAuB,GAAG,mBAAmB,GAAG,eAAe;UAC1E8I,UAAU,EAAE,wCAAwC;UACpD7T,OAAO,EAAE,MAAM;UACfyR,UAAU,EAAE,QAAQ;UACpBR,cAAc,EAAE,QAAQ;UAAE;UAC1B+B,WAAW,EAAE,MAAM;UACnBC,YAAY,EAAE;QAChB,CAAE;QAAA7T,QAAA,gBAEF5C,OAAA;UAAKsD,KAAK,EAAE;YAAEqU,IAAI,EAAE,CAAC;YAAEjB,SAAS,EAAE;UAAS,CAAE;UAAA9T,QAAA,EAC1C+K,YAAY,IAAI,CAAC,gBAChB3N,OAAA;YAAA4C,QAAA,GAAM,oEAC2D,EAAC,GAAG,eACnE5C,OAAA;cACEkD,SAAS,EAAC,uBAAuB;cACjCL,OAAO,EAAEA,CAAA,KAAM0E,QAAQ,CAAC,SAAS,CAAE;cAAA3E,QAAA,EACpC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEPjD,OAAA;YAAA4C,QAAA,GAAM,gBACU,EAAC+K,YAAY,EAAC,mCAC9B;UAAA;YAAA7K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNjD,OAAA;UACE6C,OAAO,EAAEmO,oBAAqB;UAC9B1N,KAAK,EAAE;YACLiT,UAAU,EAAE,uBAAuB;YACnCe,MAAM,EAAE,iCAAiC;YACzChB,YAAY,EAAE,KAAK;YACnB5S,KAAK,EAAE,MAAM;YACbH,MAAM,EAAE,MAAM;YACdmR,MAAM,EAAE,SAAS;YACjBW,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,MAAM;YAClBd,KAAK,EAAE,MAAM;YACbhR,OAAO,EAAE,MAAM;YACfyR,UAAU,EAAE,QAAQ;YACpBR,cAAc,EAAE,QAAQ;YACxB4C,UAAU,EAAE,eAAe;YAC3B9B,UAAU,EAAE,MAAM;YAClBgC,SAAS,EAAE,2BAA2B;YACtCC,UAAU,EAAE;UACd,CAAE;UACFC,WAAW,EAAGjC,CAAC,IAAK;YAClBA,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAACiT,UAAU,GAAG,qBAAqB;YACjDf,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAACkR,KAAK,GAAG,MAAM;YAC7BgB,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAAC8T,SAAS,GAAG,YAAY;UACzC,CAAE;UACFM,UAAU,EAAGlC,CAAC,IAAK;YACjBA,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAACiT,UAAU,GAAG,uBAAuB;YACnDf,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAACkR,KAAK,GAAG,MAAM;YAC7BgB,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAAC8T,SAAS,GAAG,UAAU;UACvC,CAAE;UACFrE,KAAK,EAAC,cAAc;UAAAnQ,QAAA,EACrB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ,IAAI,eAERjD,OAAA,CAACiB,SAAS;QAAA2B,QAAA,eACR5C,OAAA;UAAKkD,SAAS,EAAE0H,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,GAAG,4BAA4B,GAAG,qBAAsB;UACtGlG,OAAO,EAAEA,CAAA,KAAMkE,YAAY,CAAC,IAAI,CAAE;UAAAnE,QAAA,gBAElC5C,OAAA;YACE0D,KAAK,EAAE,EAAG;YACVH,MAAM,EAAE,EAAG;YACXI,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXH,KAAK,EAAC,4BAA4B;YAClCP,SAAS,EAAE0H,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,GAAG,yBAAyB,GAAG,2BAA4B;YACtGuJ,EAAE,EAAE1H,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG6O,SAAU;YAAAhV,QAAA,gBAGtE5C,OAAA;cACEiW,EAAE,EAAC,IAAI;cACPC,EAAE,EAAC,IAAI;cACPC,CAAC,EAAC,IAAI;cACNvS,IAAI,EAAEgH,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,GAAG,sBAAsB,GAAG,wBAAyB;cAC3FlF,MAAM,EAAE+G,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAU;cACjEjF,WAAW,EAAC,GAAG;cACfqT,OAAO,EAAEvM,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG;YAAM;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eAGFjD,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAgBsS,EAAE,EAAC,gBAAgB;gBAACrO,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC,MAAM;gBAAAxB,QAAA,gBACrE5C,OAAA;kBAAM6X,MAAM,EAAC,IAAI;kBAACvU,KAAK,EAAE;oBAAEwU,SAAS,EAAE,SAAS;oBAAEC,WAAW,EAAE;kBAAI;gBAAE;kBAAAjV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvEjD,OAAA;kBAAM6X,MAAM,EAAC,MAAM;kBAACvU,KAAK,EAAE;oBAAEwU,SAAS,EAAE,SAAS;oBAAEC,WAAW,EAAE;kBAAI;gBAAE;kBAAAjV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACjBjD,OAAA;gBAAgBsS,EAAE,EAAC,kBAAkB;gBAACrO,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC,MAAM;gBAAAxB,QAAA,gBACvE5C,OAAA;kBAAM6X,MAAM,EAAC,IAAI;kBAACvU,KAAK,EAAE;oBAAEwU,SAAS,EAAE,SAAS;oBAAEC,WAAW,EAAE;kBAAE;gBAAE;kBAAAjV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrEjD,OAAA;kBAAM6X,MAAM,EAAC,MAAM;kBAACvU,KAAK,EAAE;oBAAEwU,SAAS,EAAE,SAAS;oBAAEC,WAAW,EAAE;kBAAE;gBAAE;kBAAAjV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACjBjD,OAAA;gBAAgBsS,EAAE,EAAC,gBAAgB;gBAACrO,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,MAAM;gBAAAxB,QAAA,gBACnE5C,OAAA;kBAAM6X,MAAM,EAAC,IAAI;kBAACvU,KAAK,EAAE;oBAAEwU,SAAS,EAAElN,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;oBAAEgP,WAAW,EAAE;kBAAE;gBAAE;kBAAAjV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpHjD,OAAA;kBAAM6X,MAAM,EAAC,MAAM;kBAACvU,KAAK,EAAE;oBAAEwU,SAAS,EAAElN,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;oBAAEgP,WAAW,EAAE;kBAAE;gBAAE;kBAAAjV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAGPjD,OAAA;cAAGkD,SAAS,EAAC,aAAa;cAAAN,QAAA,gBAExB5C,OAAA;gBACEiW,EAAE,EAAC,IAAI;gBACPC,EAAE,EAAC,IAAI;gBACPC,CAAC,EAAC,KAAK;gBACPvS,IAAI,EAAC,sBAAsB;gBAC3BC,MAAM,EAAE+G,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAU;gBACjEjF,WAAW,EAAC;cAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eAGFjD,OAAA;gBACE4V,CAAC,EAAC,gIAAgI;gBAClIhS,IAAI,EAAC,sBAAsB;gBAC3BC,MAAM,EAAE+G,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAU;gBACjEjF,WAAW,EAAC;cAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eAGFjD,OAAA;gBAAGkD,SAAS,EAAC,YAAY;gBAACI,KAAK,EAAE;kBAAE0U,eAAe,EAAE;gBAAY,CAAE;gBAAApV,QAAA,gBAChE5C,OAAA;kBACE4V,CAAC,EAAC,sEAAsE;kBACxEhS,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,sBAAsB;kBAC7BC,WAAW,EAAC,KAAK;kBACjBC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC;gBAAO;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eAEFjD,OAAA;kBACEiW,EAAE,EAAC,IAAI;kBACPC,EAAE,EAAC,MAAM;kBACTC,CAAC,EAAC,KAAK;kBACPvS,IAAI,EAAC,sBAAsB;kBAC3BV,SAAS,EAAC;gBAAa;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGH2H,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,iBAC/B/I,OAAA;cAAGkD,SAAS,EAAC,mBAAmB;cAACiU,OAAO,EAAC,KAAK;cAAAvU,QAAA,gBAC5C5C,OAAA;gBAAQiW,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,KAAK;gBAACvS,IAAI,EAAC;cAAsB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DjD,OAAA;gBACE4V,CAAC,EAAC,2HAA2H;gBAC7HhS,IAAI,EAAC;cAAsB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACJ,EAGA2H,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,iBAC/B/I,OAAA;cAAGkD,SAAS,EAAC,oBAAoB;cAACiU,OAAO,EAAC,KAAK;cAAAvU,QAAA,gBAC7C5C,OAAA;gBAAQiW,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,KAAK;gBAACvS,IAAI,EAAC;cAAsB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DjD,OAAA;gBACE4V,CAAC,EAAC,kIAAkI;gBACpIhS,IAAI,EAAC;cAAsB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACJ,EAGA2H,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,iBAC/B/I,OAAA;cAAGkD,SAAS,EAAC,qBAAqB;cAAAN,QAAA,gBAChC5C,OAAA;gBAAQiW,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC,GAAG;gBAACvS,IAAI,EAAC,SAAS;gBAACC,MAAM,EAAC,SAAS;gBAACC,WAAW,EAAC,KAAK;gBAACqT,OAAO,EAAC,KAAK;gBAACjU,SAAS,EAAC;cAAY;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtHjD,OAAA;gBAAQiW,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC,KAAK;gBAACvS,IAAI,EAAC,SAAS;gBAACC,MAAM,EAAC,SAAS;gBAACC,WAAW,EAAC,KAAK;gBAACqT,OAAO,EAAC,KAAK;gBAACjU,SAAS,EAAC;cAAY;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvHjD,OAAA;gBAAQiW,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,KAAK;gBAACvS,IAAI,EAAC,SAAS;gBAACC,MAAM,EAAC,SAAS;gBAACC,WAAW,EAAC,KAAK;gBAACqT,OAAO,EAAC,KAAK;gBAACjU,SAAS,EAAC;cAAY;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxH,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEL2H,qBAAqB,CAAC7B,MAAM,GAAG,CAAC,gBAC/B/I,OAAA;YAAKkD,SAAS,EAAC,+BAA+B;YAAAN,QAAA,EAC3CgI,qBAAqB,CAAC7B;UAAM;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,GACJ6H,qBAAqB,gBACvB9K,OAAA;YAAKkD,SAAS,EAAC,gCAAgC;YAAC6P,KAAK,EAAC,4BAA4B;YAAAnQ,QAAA,EAAC;UAEnF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GACJ2U,SAAS;QAAA;UAAA9U,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEZjD,OAAA,CAACH,gBAAgB;QACf+K,qBAAqB,EAAEA,qBAAsB;QAC7CW,kBAAkB,EAAEA,kBAAmB;QACvCL,kBAAkB,EAAEA,kBAAmB;QACvCG,yBAAyB,EAAEA,yBAA0B;QACrDvE,SAAS,EAAEA,SAAU;QACrBC,YAAY,EAAEA;MAAa;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAEFjD,OAAA,CAACsE,SAAS;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAKbjD,OAAA,CAACS,UAAU;QACT2U,GAAG,EAAEhL,WAAY;QACjB/J,OAAO,EAAEA,OAAQ;QACjB6C,SAAS,EAAE7C,OAAO,GAAG,MAAM,GAAG,QAAS;QACvCiD,KAAK,EAAE;UAAEiU,SAAS,EAAE;QAAwB,CAAE;QAAA3U,QAAA,eAE9C5C,OAAA,CAACe,WAAW;UAAA6B,QAAA,gBACV5C,OAAA;YAAKkD,SAAS,EAAC,aAAa;YAAAN,QAAA,EACzBvC,OAAO,gBACNL,OAAA,CAACzB,cAAc;cACbsE,OAAO,EAAEyJ,aAAc;cACvB1I,IAAI,EAAC,MAAM;cACXV,SAAS,EAAC;YAAuB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,gBAEFjD,OAAA,CAACxB,gBAAgB;cACfqE,OAAO,EAAEyJ,aAAc;cACvB1I,IAAI,EAAC,MAAM;cACXV,SAAS,EAAC;YAAwB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEL5C,OAAO,gBACNL,OAAA;YACEsD,KAAK,EAAE;cACL2U,YAAY,EAAE,mBAAmB;cACjCC,eAAe,EAAE,OAAO5a,OAAO,GAAG,CAAC;YACrC,CAAE;YAAAsF,QAAA,gBAEF5C,OAAA;cACEkD,SAAS,EAAC,eAAe;cACzBI,KAAK,EAAE;gBAAEqR,UAAU,EAAE,KAAK;gBAAEC,aAAa,EAAE;cAAM,CAAE;cAAAhS,QAAA,eAInD5C,OAAA;gBACE8E,GAAG,EACD+D,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACE,MAAM,GAAG,CAAC,GAClCF,OAAO,GACPtL,cACL;gBACDmG,KAAK,EAAE,EAAG;gBACVH,MAAM,EAAE,EAAG;gBACXL,SAAS,EAAC;cAAS;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjD,OAAA,CAACO,OAAO,CAAC;cAAW+C,KAAK,EAAE;gBAAEiS,UAAU,EAAE;cAAO,CAAE;cAAA3S,QAAA,gBAChD5C,OAAA;gBACEsD,KAAK,EAAE;kBACL6U,QAAQ,EAAE,KAAK;kBACfC,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE;gBACd,CAAE;gBAAAzV,QAAA,EAED+F;cAAQ;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACRjD,OAAA;gBAAKoV,GAAG,EAAEjL,cAAe;gBAAAvH,QAAA,gBACvB5C,OAAA,CAAC/C,OAAO,CAACqb,qBAAqB;kBAC5BzV,OAAO,EAAE0K,eAAgB;kBACzBjK,KAAK,EAAE;oBACLkR,KAAK,EAAE,OAAO;oBACdE,MAAM,EAAE,SAAS;oBACjBa,UAAU,EAAE,MAAM;oBAClBF,QAAQ,EAAE;kBACZ;gBAAE;kBAAAvS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFjD,OAAA,CAACY,gBAAgB;kBAACC,WAAW,EAAEA,WAAY;kBAAA+B,QAAA,gBACzC5C,OAAA;oBACE6C,OAAO,EAAG2S,CAAC,IAAKxI,UAAU,CAACvE,MAAM,CAAE;oBACnCnF,KAAK,EAAE;sBAAEoR,MAAM,EAAE;oBAAU,CAAE;oBAAA9R,QAAA,eAE7B5C,OAAA;sBAAA4C,QAAA,EAAG;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACLjD,OAAA;oBACE6C,OAAO,EAAE2K,mBAAoB;oBAC7BlK,KAAK,EAAE;sBAAEoR,MAAM,EAAE;oBAAU,CAAE;oBAAA9R,QAAA,eAE7B5C,OAAA;sBAAA4C,QAAA,EAAG;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACLjD,OAAA;oBAAI6C,OAAO,EAAE0J,YAAa;oBAACjJ,KAAK,EAAE;sBAAEoR,MAAM,EAAE;oBAAU,CAAE;oBAAA9R,QAAA,eACtD5C,OAAA;sBAAA4C,QAAA,EAAG;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACVjD,OAAA;cACEsD,KAAK,EAAE;gBACLsR,aAAa,EAAE,KAAK;gBACpBH,cAAc,EAAE,QAAQ;gBACxBjR,OAAO,EAAE,MAAM;gBACf6R,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAE;cACT,CAAE;cAAA5R,QAAA,EAEDgG;YAAS;cAAA9F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENjD,OAAA;YACEsD,KAAK,EAAE;cACLiU,SAAS,EAAE,4BAA4B;cACvCU,YAAY,EAAE;YAChB,CAAE;YAAArV,QAAA,gBAEF5C,OAAA;cACEkD,SAAS,EAAC,eAAe;cACzBI,KAAK,EAAE;gBACLqR,UAAU,EAAE,EAAE;gBACdC,aAAa,EAAE,CAAC;gBAChB6B,YAAY,EAAE,CAAC;gBACf7B,aAAa,EAAE;cACjB,CAAE;cAAAhS,QAAA,eAGF5C,OAAA;gBACE8E,GAAG,EACD+D,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACE,MAAM,GAAG,CAAC,GAClCF,OAAO,GACPtL,cACL;gBACDmG,KAAK,EAAE,EAAG;gBACVH,MAAM,EAAE,EAAG;gBACXL,SAAS,EAAC,SAAS;gBACnBI,KAAK,EAAE;kBAAEwR,OAAO,EAAE;gBAAM;cAAE;gBAAAhS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjD,OAAA,CAACO,OAAO;cACNgY,EAAE,EAAC,GAAG;cACNjV,KAAK,EAAE;gBACL,mBAAoB+R,QAAQ,EAAE,KAAK;gBACnC9R,MAAM,EAAE,KAAK,CAAC;cAChB,CAAE;cAAAX,QAAA,eAEF5C,OAAA;gBACEsD,KAAK,EAAE;kBACL6U,QAAQ,EAAE,KAAK;kBACfC,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE;gBACd,CAAE;gBAAAzV,QAAA,EAED+F;cAAQ;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACVjD,OAAA;cACEsD,KAAK,EAAE;gBACLsR,aAAa,EAAE,MAAM;gBACrBH,cAAc,EAAE,QAAQ;gBACxBjR,OAAO,EAAE,MAAM;gBACf6R,QAAQ,EAAE,MAAM;gBAChB7R,OAAO,EAAE;cACX,CAAE;cAAAZ,QAAA,EAEDgG;YAAS;cAAA9F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDjD,OAAA;YACEkD,SAAS,EAAC,mBAAmB;YAC7BI,KAAK,EAAE;cAAEwT,SAAS,EAAE,QAAQ;cAAEvT,MAAM,EAAE;YAAsB,CAAE;YAAAX,QAAA,GAG7DiM,oBAAoB,iBACnB7O,OAAA;cACEkD,SAAS,EAAC,2BAA2B;cACrCL,OAAO,EAAEA,CAAA,KAAM0E,QAAQ,CAAC,uBAAuB,CAAE;cACjDjE,KAAK,EAAE;gBACLiT,UAAU,EAAE,mDAAmD;gBAC/DiC,MAAM,EAAE,MAAM;gBACd1D,OAAO,EAAE,MAAM;gBACfwB,YAAY,EAAE,MAAM;gBACpB5B,MAAM,EAAE,SAAS;gBACjBF,KAAK,EAAE,OAAO;gBACd6C,UAAU,EAAE,eAAe;gBAC3BC,MAAM,EAAE,uBAAuB;gBAC/BC,SAAS,EAAE;cACb,CAAE;cACFkB,YAAY,EAAGjD,CAAC,IAAK;gBACnBA,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAAC8T,SAAS,GAAG,kBAAkB;gBAC7C5B,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAACiU,SAAS,GAAG,gCAAgC;cAC7D,CAAE;cACFmB,YAAY,EAAGlD,CAAC,IAAK;gBACnBA,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAAC8T,SAAS,GAAG,eAAe;gBAC1C5B,CAAC,CAAC1B,MAAM,CAACxQ,KAAK,CAACiU,SAAS,GAAG,8BAA8B;cAC3D,CAAE;cAAA3U,QAAA,gBAEF5C,OAAA;gBAAKsD,KAAK,EAAE;kBAAEE,OAAO,EAAE,MAAM;kBAAEyR,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAO,CAAE;gBAAAtS,QAAA,gBACjE5C,OAAA;kBAAKsD,KAAK,EAAE;oBACViT,UAAU,EAAE,0BAA0B;oBACtCD,YAAY,EAAE,KAAK;oBACnBxB,OAAO,EAAE,KAAK;oBACdtR,OAAO,EAAE,MAAM;oBACfyR,UAAU,EAAE,QAAQ;oBACpBR,cAAc,EAAE,QAAQ;oBACxBkE,QAAQ,EAAE,MAAM;oBAChBpV,MAAM,EAAE;kBACV,CAAE;kBAAAX,QAAA,eACA5C,OAAA,CAACjC,YAAY;oBAACuF,KAAK,EAAE;sBAAE+R,QAAQ,EAAE;oBAAO;kBAAE;oBAAAvS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACNjD,OAAA;kBAAKsD,KAAK,EAAE;oBAAEqU,IAAI,EAAE;kBAAE,CAAE;kBAAA/U,QAAA,gBACtB5C,OAAA;oBAAKsD,KAAK,EAAE;sBACV+R,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,KAAK;sBACjBU,YAAY,EAAE,KAAK;sBACnBxS,OAAO,EAAEnD,OAAO,GAAG,OAAO,GAAG;oBAC/B,CAAE;oBAAAuC,QAAA,EAAC;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNjD,OAAA;oBAAKsD,KAAK,EAAE;sBACV+R,QAAQ,EAAE,MAAM;sBAChB8B,OAAO,EAAE,KAAK;sBACd3T,OAAO,EAAEnD,OAAO,GAAG,OAAO,GAAG;oBAC/B,CAAE;oBAAAuC,QAAA,GACC+L,8BAA8B,GAAGF,4BAA4B,EAAC,mBACjE;kBAAA;oBAAA3L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNjD,OAAA;gBAAKsD,KAAK,EAAE;kBACVsV,SAAS,EAAE,MAAM;kBACjBpV,OAAO,EAAEnD,OAAO,GAAG,OAAO,GAAG;gBAC/B,CAAE;gBAAAuC,QAAA,gBACA5C,OAAA;kBAAKsD,KAAK,EAAE;oBACViT,UAAU,EAAE,0BAA0B;oBACtCD,YAAY,EAAE,KAAK;oBACnB/S,MAAM,EAAE,KAAK;oBACb6U,QAAQ,EAAE;kBACZ,CAAE;kBAAAxV,QAAA,eACA5C,OAAA;oBAAKsD,KAAK,EAAE;sBACViT,UAAU,EAAE,OAAO;sBACnBhT,MAAM,EAAE,MAAM;sBACd+S,YAAY,EAAE,KAAK;sBACnB5S,KAAK,EAAE,GAAI+K,4BAA4B,GAAGE,8BAA8B,GAAI,GAAG,GAAG;sBAClF0I,UAAU,EAAE;oBACd;kBAAE;oBAAAvU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNjD,OAAA;kBAAKsD,KAAK,EAAE;oBACVE,OAAO,EAAE,MAAM;oBACfiR,cAAc,EAAE,eAAe;oBAC/BQ,UAAU,EAAE,QAAQ;oBACpB2D,SAAS,EAAE;kBACb,CAAE;kBAAAhW,QAAA,gBACA5C,OAAA;oBAAMsD,KAAK,EAAE;sBAAE+R,QAAQ,EAAE,MAAM;sBAAE8B,OAAO,EAAE;oBAAM,CAAE;oBAAAvU,QAAA,GAC/C6L,4BAA4B,EAAC,GAAC,EAACE,8BAA8B,EAAC,gBACjE;kBAAA;oBAAA7L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPjD,OAAA;oBAAMsD,KAAK,EAAE;sBAAE+R,QAAQ,EAAE,MAAM;sBAAE8B,OAAO,EAAE;oBAAM,CAAE;oBAAAvU,QAAA,GAC/CqJ,IAAI,CAAC4M,KAAK,CAAEpK,4BAA4B,GAAGE,8BAA8B,GAAI,GAAG,CAAC,EAAC,GACrF;kBAAA;oBAAA7L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAEA/F,WAAW,CAAC6Z,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;cAChC,oBACEjX,OAAA,CAAC3C,cAAc;gBAEb6Z,WAAW,EAAE,CAACha,WAAW,CAAC+Z,KAAK,CAAC,CAACnK,UAAU,CAAE;gBAAAlK,QAAA,eAE7C5C,OAAA,CAAC7C,OAAO;kBACN6Z,IAAI,EAAEA,IAAK;kBAEX1T,KAAK,EAAE;oBAAEiT,UAAU,EAAE;kBAAQ;gBAAE,GAD1BU,KAAK;kBAAAnU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEX;cAAC,GAPGgU,KAAK;gBAAAnU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQI,CAAC;YAErB,CAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA,eACvB,CAAC;AAEP,CAAC;AAAC4C,GAAA,CA9wDID,QAAQ;EAAA,QAYK/D,WAAW,EAeX9E,WAAW,EAmExB+C,kBAAkB;AAAA;AAAAgZ,IAAA,GA9FlBlT,QAAQ;AAgxDd,eAAeA,QAAQ;AAAC,IAAAtF,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAyC,GAAA,EAAAsB,IAAA,EAAAmT,IAAA;AAAAC,YAAA,CAAAzY,EAAA;AAAAyY,YAAA,CAAAvY,GAAA;AAAAuY,YAAA,CAAApY,GAAA;AAAAoY,YAAA,CAAAjY,GAAA;AAAAiY,YAAA,CAAA/X,GAAA;AAAA+X,YAAA,CAAA7X,GAAA;AAAA6X,YAAA,CAAA3X,GAAA;AAAA2X,YAAA,CAAAxX,GAAA;AAAAwX,YAAA,CAAArX,GAAA;AAAAqX,YAAA,CAAAnX,GAAA;AAAAmX,YAAA,CAAA1U,GAAA;AAAA0U,YAAA,CAAApT,IAAA;AAAAoT,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}