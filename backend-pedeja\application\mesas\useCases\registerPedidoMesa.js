const moment = require("moment");
const momentTz = require("moment-timezone");
const Cliente = require("../../../models/Cliente");
const Pedidos = require("../../../models/Pedidos");
const { gerarPDF, enviarParaImpressao } = require("../../../index");

class RegisterPedidoMesaUseCase {
    constructor(userDAO, mesaDAO, empresaDAO, clienteDAO, counterDAO, pedidosDAO, io) {
        this.userDAO = userDAO;
        this.mesaDAO = mesaDAO;
        this.empresaDAO = empresaDAO;
        this.clienteDAO = clienteDAO;
        this.counterDAO = counterDAO;
        this.pedidosDAO = pedidosDAO;
        this.io = io;
    }

    async execute({ id_empresa, user_id, itens, celular_cliente, nome_cliente, valor_total, descricao, mesaId }) {
        try {
            const empresa = await this.empresaDAO.getEmpresaById(id_empresa);
            if (!empresa) return { status: false, message: "Empresa não encontrada" };

            const garcom = await this.userDAO.getUserById(user_id);
            const mesa = await this.mesaDAO.getWithPedidos({ id_empresa: empresa._id, mesaId });
            if (!mesa) return { status: false, message: "Mesa não encontrada" };

            let status_pedido = "1";
            let status_print = false;

            if (empresa.tipo_impressao === "automatico") {
                status_pedido = "2";
                status_print = true;
            }

            let counterPedidoCliente = await Cliente.findOneAndUpdate(
                { id_empresa: empresa.id_empresa, telefone: celular_cliente },
                { $inc: { counter_qtd_pedido: 1 } },
                { new: true }
            );
            if (!counterPedidoCliente) {
                const newCliente = new Cliente({
                    id_empresa: empresa.id_empresa,
                    nome: nome_cliente,
                    telefone: celular_cliente,
                    vinculo_empresa: empresa.cnpj,
                    counter_qtd_pedido: 1,
                });
                await newCliente.save();
                counterPedidoCliente = { counter_qtd_pedido: 1 };
            }

            const lastPedido = await Pedidos.findOne({ id_empresa: empresa.id_empresa })
                .sort({ createdAt: -1 })
                .select("id_pedido_counter createdAt");

            let newIdPedidoCounter = 1;
            if (
                empresa.last_reset_date &&
                (!lastPedido ||
                    !lastPedido.id_pedido_counter ||
                    lastPedido.createdAt < empresa.last_reset_date)
            ) {
                newIdPedidoCounter = 1;
            } else if (lastPedido) {
                newIdPedidoCounter = (lastPedido.id_pedido_counter || 0) + 1;
            }

            const counterPedido = await this.clienteDAO.findAndUpdateCounterPedido({
                id_empresa: empresa.id_empresa,
                celular_cliente,
            }) || { counter_qtd_pedido: 1 };

            const counterData = await this.counterDAO.findOneAndUpdate(empresa.id_empresa);
            const seqId = counterData ? counterData.seq : (await this.counterDAO.createCounter(id_empresa)).seq;

            const data = moment().format();

            const entrega = {
                tipo_entrega: "Mesa",
                endereco: mesa.name,
                entrega: 0,
            };

            const pedido = await this.pedidosDAO.createPedido({
                seqId,
                createdBy: mesa.name,
                garcomName: garcom.name,
                id_empresa: empresa.id_empresa,
                status_pedido,
                itens,
                celular_cliente,
                nome_cliente,
                valor_total,
                descricao,
                counterPedido,
                status_print,
                createdAt: data,
                entrega,
                counter_qtd_pedido: counterPedido.counter_qtd_pedido,
                id_pedido_counter: newIdPedidoCounter,
            });

            if (status_pedido === "2" && status_print === true) {
                const dataFormatada = momentTz.tz(data, "America/Sao_Paulo").format("DD/MM/YYYY HH:mm");

                const dadosComanda = {
                    createdBy: mesa.name,
                    tipoPedido: entrega.tipo_entrega,
                    dataPedido: dataFormatada,
                    nomeLoja: empresa.name,
                    numeroPedido: newIdPedidoCounter,
                    itens,
                    cliente: {
                        nome: nome_cliente || "",
                        telefone: celular_cliente || "",
                        qtdPedidos: counterPedido.counter_qtd_pedido || 1,
                    },
                    entrega: {
                        endereco: `Mesa ${mesa.name}`,
                        referencia: "-",
                        bairroAndCity: "-",
                        cep: "-"
                    },
                    pagamento: {
                        forma: "A definir",
                        subtotal: valor_total,
                        taxaEntrega: 0,
                        total: valor_total
                    },
                    observacoes: descricao || "",
                    troco: 0
                };

                try {
                    const caminhoArquivo = await gerarPDF(dadosComanda);
                    console.log('Comanda gerada com sucesso:', caminhoArquivo);
                    await enviarParaImpressao(caminhoArquivo, empresa._id);
                } catch (erro) {
                    console.error('Erro ao gerar a comanda:', erro);
                }
            }

            this.io.to(empresa._id.toString()).emit('novoPedido', {
                msg: 'Pedido gerado com sucesso!',
                pedido,
            });

            let mesaPedido = {
                id_pedido: pedido._id,
                id_garcom: garcom.id,
                name_garcom: garcom.name,
                closed: false,
                updatedAt: data,
            };
            mesa.pedidos.push(mesaPedido);
            mesa.status = "occupied";
            mesa.total = Number(valor_total) + (mesa.total || 0);
            await mesa.save();

            return {
                status: true,
                msg: "Pedido registrado com sucesso",
                mesa,
            };
        } catch (error) {
            console.error(error);
            return {
                status: false,
                msg: "Erro ao registrar pedido",
            };
        }
    }
}

module.exports = RegisterPedidoMesaUseCase;
