{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\Impressora\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport './style.css';\nimport PermissionGate from \"../../services/PermissionGate\";\nimport LeftMenu from \"../../components/LeftMenu\";\nimport styled from 'styled-components';\nimport QRCode from 'qrcode.react';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport * as AiIcons from 'react-icons/ai';\nimport * as SlIcons from 'react-icons/sl';\nimport * as MdIcons from 'react-icons/md';\nimport * as FaIcons from 'react-icons/fa';\nimport { testeImpressao } from \"../../services/api\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Teste = styled.div`\n\n    display: flex;\n    margin-left:  ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'}; \n    height:auto;\n    width:auto;\n    transition: 150ms;\n    background-color:rgb(247,247,247)!important;\n    overflow: initial;\n    z-Index: 9;\n\n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\n_c = Teste;\nconst ConfigPrinter = () => {\n  _s();\n  const {\n    sidebar,\n    setSidebar\n  } = useContext(SidebarContext);\n  ; //TESTANDO CONST NO AppROUTES\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [currentStep, setCurrentStep] = useState(1);\n  const navigate = useNavigate();\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const idEmpresa = empresaParse.id_empresa;\n  const objIdEmpresa = empresaParse._id;\n  const qrCodeData = 'https://www.example.com';\n  const handleBack = () => {\n    //setSidebar(!sidebar)\n    navigate(\"/list-item\");\n  };\n  const downloadInstallationPackage = async () => {\n    try {\n      // Endpoint da GitHub API para obter as releases\n      const releasesUrl = 'https://api.github.com/repos/PedeJaSoftware/PedeJaPrint-Release/releases/latest';\n\n      // Obtém a última release\n      const response = await fetch(releasesUrl);\n      if (!response.ok) {\n        throw new Error('Não foi possível obter as releases do GitHub');\n      }\n      const data = await response.json();\n\n      // Encontra o link para o arquivo PedeJaPrintSetup.exe na última release\n      const setupAsset = data.assets.find(asset => asset.name === 'PedeJaPrintSetup.exe');\n      if (!setupAsset) {\n        throw new Error('Arquivo PedeJaPrintSetup.exe não encontrado na última release');\n      }\n\n      // URL para download do arquivo\n      const downloadUrl = setupAsset.browser_download_url;\n\n      // Inicia o download\n      const link = document.createElement('a');\n      link.href = downloadUrl;\n      link.download = 'PedeJaPrintSetup.exe'; // Nome do arquivo ao salvar\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      console.log('Download iniciado para:', downloadUrl);\n      setCurrentStep(2); // Avança para o próximo passo\n    } catch (error) {\n      console.error('Erro ao baixar o pacote de instalação:', error);\n    }\n  };\n  const handlePrinterTest = async () => {\n    const response = await testeImpressao(objIdEmpresa);\n    console.log(\"Resposta do print test:\", response);\n    if (response && response.success) {\n      setCurrentStep(3); // Avança para finalização\n    }\n  };\n  const handleAlreadyDownloaded = () => {\n    setCurrentStep(2); // Avança para a etapa de teste\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: /*#__PURE__*/_jsxDEV(Teste, {\n        sidebar: sidebar,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-100 p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"printer-config-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-header\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"title\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  children: [/*#__PURE__*/_jsxDEV(MdIcons.MdPrint, {\n                    className: \"header-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 37\n                  }, this), \"Configurar Impressora de Comandas\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"subtitle\",\n                  children: \"Configure sua impressora para receber automaticamente os pedidos dos seus clientes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 33\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-banner\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-content\",\n                children: [/*#__PURE__*/_jsxDEV(AiIcons.AiOutlineInfoCircle, {\n                  className: \"info-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Por que preciso do software cliente?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Nosso software cliente conecta sua impressora de comandas diretamente ao sistema PedeJ\\xE1. Isso permite que os pedidos sejam impressos automaticamente, sem necessidade de interven\\xE7\\xE3o manual.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 33\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"steps-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `step-card ${currentStep >= 1 ? 'active' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `step-number ${currentStep > 1 ? 'completed' : ''}`,\n                    children: currentStep > 1 ? /*#__PURE__*/_jsxDEV(FaIcons.FaCheck, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 129,\n                      columnNumber: 60\n                    }, this) : '1'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    children: \"Baixar o Software Cliente\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Primeiro, voc\\xEA precisa baixar e instalar nosso software cliente que far\\xE1 a conex\\xE3o entre o sistema PedeJ\\xE1 e sua impressora.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"step-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: downloadInstallationPackage,\n                      className: \"download-button\",\n                      type: \"button\",\n                      children: [/*#__PURE__*/_jsxDEV(AiIcons.AiOutlineDownload, {\n                        className: \"button-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 141,\n                        columnNumber: 45\n                      }, this), \"Baixar PedeJ\\xE1 Print\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 41\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: handleAlreadyDownloaded,\n                      className: \"already-downloaded-button\",\n                      type: \"button\",\n                      children: [/*#__PURE__*/_jsxDEV(AiIcons.AiOutlineCheckCircle, {\n                        className: \"button-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 149,\n                        columnNumber: 45\n                      }, this), \"J\\xE1 Baixei o Software\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 41\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"step-info\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      children: [/*#__PURE__*/_jsxDEV(AiIcons.AiOutlineWindows, {\n                        className: \"os-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 155,\n                        columnNumber: 45\n                      }, this), \"Compat\\xEDvel com Windows 10 ou superior\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 41\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 33\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `step-card ${currentStep >= 2 ? 'active' : 'disabled'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `step-number ${currentStep > 2 ? 'completed' : ''}`,\n                    children: currentStep > 2 ? /*#__PURE__*/_jsxDEV(FaIcons.FaCheck, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 60\n                    }, this) : '2'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    children: \"Instalar, Autenticar e Configurar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Execute o arquivo baixado e realize o login com o seu email e senha do PedeJ\\xE1. O software ir\\xE1 listar suas impressoras e voc\\xEA deve selecionar a impressora que deseja usar.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"installation-tips\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Dicas de instala\\xE7\\xE3o:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 172,\n                      columnNumber: 41\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"Certifique-se de que sua impressora est\\xE1 conectada\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 174,\n                        columnNumber: 45\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"Execute como administrador se necess\\xE1rio\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 175,\n                        columnNumber: 45\n                      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                        children: \"Mantenha o software sempre aberto para receber pedidos\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 176,\n                        columnNumber: 45\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 41\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 33\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `step-card ${currentStep >= 2 ? 'active' : 'disabled'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `step-number ${currentStep > 2 ? 'completed' : ''}`,\n                    children: currentStep > 2 ? /*#__PURE__*/_jsxDEV(FaIcons.FaCheck, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 60\n                    }, this) : '3'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    children: \"Testar Impress\\xE3o\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 33\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Ap\\xF3s instalar o software, teste a conex\\xE3o com sua impressora para garantir que tudo est\\xE1 funcionando corretamente.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"step-actions\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: handlePrinterTest,\n                      className: \"test-button\",\n                      disabled: currentStep < 2,\n                      children: [/*#__PURE__*/_jsxDEV(MdIcons.MdPrint, {\n                        className: \"button-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 198,\n                        columnNumber: 45\n                      }, this), \"Testar Impressora\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 41\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"step-info\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      children: [/*#__PURE__*/_jsxDEV(AiIcons.AiOutlineCheckCircle, {\n                        className: \"check-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 45\n                      }, this), \"Um comando de teste ser\\xE1 impresso em sua impressora\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 41\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 33\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 29\n              }, this), currentStep >= 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-card\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"success-content\",\n                  children: [/*#__PURE__*/_jsxDEV(FaIcons.FaCheckCircle, {\n                    className: \"success-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    children: \"Configura\\xE7\\xE3o Conclu\\xEDda!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Sua impressora est\\xE1 configurada e pronta para receber pedidos automaticamente. Os comandos ser\\xE3o impressos assim que os pedidos forem confirmados.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(ConfigPrinter, \"WmeBatuyKl1pTMd60m4dgfVpEsQ=\", false, function () {\n  return [useNavigate];\n});\n_c2 = ConfigPrinter;\nexport default ConfigPrinter;\nvar _c, _c2;\n$RefreshReg$(_c, \"Teste\");\n$RefreshReg$(_c2, \"ConfigPrinter\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "PermissionGate", "LeftMenu", "styled", "QRCode", "SidebarContext", "AiIcons", "SlIcons", "MdIcons", "FaIcons", "testeImpressao", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON>e", "div", "sidebar", "_c", "ConfigPrinter", "_s", "setSidebar", "isSubmitting", "setIsSubmitting", "currentStep", "setCurrentStep", "navigate", "empresa", "localStorage", "getItem", "empresaParse", "JSON", "parse", "idEmpresa", "id_empresa", "objIdEmpresa", "_id", "qrCodeData", "handleBack", "downloadInstallationPackage", "releasesUrl", "response", "fetch", "ok", "Error", "data", "json", "setupAsset", "assets", "find", "asset", "name", "downloadUrl", "browser_download_url", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "error", "handlePrinterTest", "success", "handleAlreadyDownloaded", "children", "permissions", "className", "MdPrint", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "AiOutlineInfoCircle", "FaCheck", "onClick", "type", "AiOutlineDownload", "AiOutlineCheckCircle", "AiOutlineWindows", "disabled", "FaCheckCircle", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/Impressora/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport './style.css'\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\nimport styled from 'styled-components';\r\nimport QRCode from 'qrcode.react';\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport * as AiIcons from 'react-icons/ai'\r\nimport * as SlIcons from 'react-icons/sl'\r\nimport * as MdIcons from 'react-icons/md'\r\nimport * as FaIcons from 'react-icons/fa'\r\nimport { testeImpressao } from \"../../services/api\";\r\n\r\nconst Teste = styled.div`\r\n\r\n    display: flex;\r\n    margin-left:  ${({ sidebar }) => (sidebar ? '250px' : '100px')}; \r\n    height:auto;\r\n    width:auto;\r\n    transition: 150ms;\r\n    background-color:rgb(247,247,247)!important;\r\n    overflow: initial;\r\n    z-Index: 9;\r\n\r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\nconst ConfigPrinter = () => {\r\n\r\n    const { sidebar, setSidebar } = useContext(SidebarContext);; //TESTANDO CONST NO AppROUTES\r\n    const [isSubmitting, setIsSubmitting] = useState(false);\r\n    const [currentStep, setCurrentStep] = useState(1);\r\n    const navigate = useNavigate();\r\n\r\n    const empresa = localStorage.getItem('empresa')\r\n    const empresaParse = JSON.parse(empresa)\r\n    const idEmpresa = empresaParse.id_empresa;\r\n    const objIdEmpresa = empresaParse._id;\r\n\r\n    const qrCodeData = 'https://www.example.com';\r\n\r\n    const handleBack = () => {\r\n        //setSidebar(!sidebar)\r\n        navigate(\"/list-item\");\r\n    }\r\n\r\n    const downloadInstallationPackage = async () => {\r\n        try {\r\n            // Endpoint da GitHub API para obter as releases\r\n            const releasesUrl = 'https://api.github.com/repos/PedeJaSoftware/PedeJaPrint-Release/releases/latest';\r\n            \r\n            // Obtém a última release\r\n            const response = await fetch(releasesUrl);\r\n            if (!response.ok) {\r\n                throw new Error('Não foi possível obter as releases do GitHub');\r\n            }\r\n            const data = await response.json();\r\n    \r\n            // Encontra o link para o arquivo PedeJaPrintSetup.exe na última release\r\n            const setupAsset = data.assets.find(asset => asset.name === 'PedeJaPrintSetup.exe');\r\n            if (!setupAsset) {\r\n                throw new Error('Arquivo PedeJaPrintSetup.exe não encontrado na última release');\r\n            }\r\n    \r\n            // URL para download do arquivo\r\n            const downloadUrl = setupAsset.browser_download_url;\r\n    \r\n            // Inicia o download\r\n            const link = document.createElement('a');\r\n            link.href = downloadUrl;\r\n            link.download = 'PedeJaPrintSetup.exe'; // Nome do arquivo ao salvar\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link);\r\n            \r\n            console.log('Download iniciado para:', downloadUrl);\r\n            setCurrentStep(2); // Avança para o próximo passo\r\n        } catch (error) {\r\n            console.error('Erro ao baixar o pacote de instalação:', error);\r\n        }\r\n    };\r\n\r\n    const handlePrinterTest = async () => {\r\n        const response = await testeImpressao(objIdEmpresa)\r\n        console.log(\"Resposta do print test:\",response)\r\n        if (response && response.success) {\r\n            setCurrentStep(3); // Avança para finalização\r\n        }\r\n    }\r\n\r\n    const handleAlreadyDownloaded = () => {\r\n        setCurrentStep(2); // Avança para a etapa de teste\r\n    }\r\n\r\n    return (\r\n        <>\r\n        <PermissionGate permissions={['default']}>\r\n    \r\n\r\n            {/*<LeftMenu setSidebar={setSidebar} sidebar={sidebar} />*/}\r\n\r\n            <Teste sidebar={sidebar}>\r\n                <div className=\"w-100 p-4\">\r\n                    <div className=\"printer-config-container\">\r\n                        <div className=\"form-header\">\r\n                            <div className=\"title\">\r\n                                <h2><MdIcons.MdPrint className=\"header-icon\" />Configurar Impressora de Comandas</h2>\r\n                                <p className=\"subtitle\">Configure sua impressora para receber automaticamente os pedidos dos seus clientes</p>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"info-banner\">\r\n                            <div className=\"info-content\">\r\n                                <AiIcons.AiOutlineInfoCircle className=\"info-icon\" />\r\n                                <div>\r\n                                    <h3>Por que preciso do software cliente?</h3>\r\n                                    <p>Nosso software cliente conecta sua impressora de comandas diretamente ao sistema PedeJá. Isso permite que os pedidos sejam impressos automaticamente, sem necessidade de intervenção manual.</p>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"steps-container\">\r\n                            <div className={`step-card ${currentStep >= 1 ? 'active' : ''}`}>\r\n                                <div className=\"step-header\">\r\n                                    <div className={`step-number ${currentStep > 1 ? 'completed' : ''}`}>\r\n                                        {currentStep > 1 ? <FaIcons.FaCheck /> : '1'}\r\n                                    </div>\r\n                                    <h2>Baixar o Software Cliente</h2>\r\n                                </div>\r\n                                <div className=\"step-content\">\r\n                                    <p>Primeiro, você precisa baixar e instalar nosso software cliente que fará a conexão entre o sistema PedeJá e sua impressora.</p>\r\n                                    <div className=\"step-actions\">\r\n                                        <button \r\n                                            onClick={downloadInstallationPackage} \r\n                                            className=\"download-button\"\r\n                                            type=\"button\"\r\n                                        >\r\n                                            <AiIcons.AiOutlineDownload className=\"button-icon\" />\r\n                                            Baixar PedeJá Print\r\n                                        </button>\r\n                                        <button \r\n                                            onClick={handleAlreadyDownloaded} \r\n                                            className=\"already-downloaded-button\"\r\n                                            type=\"button\"\r\n                                        >\r\n                                            <AiIcons.AiOutlineCheckCircle className=\"button-icon\" />\r\n                                            Já Baixei o Software\r\n                                        </button>\r\n                                    </div>\r\n                                    <div className=\"step-info\">\r\n                                        <small>\r\n                                            <AiIcons.AiOutlineWindows className=\"os-icon\" />\r\n                                            Compatível com Windows 10 ou superior\r\n                                        </small>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className={`step-card ${currentStep >= 2 ? 'active' : 'disabled'}`}>\r\n                                <div className=\"step-header\">\r\n                                    <div className={`step-number ${currentStep > 2 ? 'completed' : ''}`}>\r\n                                        {currentStep > 2 ? <FaIcons.FaCheck /> : '2'}\r\n                                    </div>\r\n                                    <h2>Instalar, Autenticar e Configurar</h2>\r\n                                </div>\r\n                                <div className=\"step-content\">\r\n                                    <p>Execute o arquivo baixado e realize o login com o seu email e senha do PedeJá. O software irá listar suas impressoras e você deve selecionar a impressora que deseja usar.</p>\r\n                                    <div className=\"installation-tips\">\r\n                                        <h4>Dicas de instalação:</h4>\r\n                                        <ul>\r\n                                            <li>Certifique-se de que sua impressora está conectada</li>\r\n                                            <li>Execute como administrador se necessário</li>\r\n                                            <li>Mantenha o software sempre aberto para receber pedidos</li>\r\n                                        </ul>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className={`step-card ${currentStep >= 2 ? 'active' : 'disabled'}`}>\r\n                                <div className=\"step-header\">\r\n                                    <div className={`step-number ${currentStep > 2 ? 'completed' : ''}`}>\r\n                                        {currentStep > 2 ? <FaIcons.FaCheck /> : '3'}\r\n                                    </div>\r\n                                    <h2>Testar Impressão</h2>\r\n                                </div>\r\n                                <div className=\"step-content\">\r\n                                    <p>Após instalar o software, teste a conexão com sua impressora para garantir que tudo está funcionando corretamente.</p>\r\n                                    <div className=\"step-actions\">\r\n                                        <button \r\n                                            type=\"button\" \r\n                                            onClick={handlePrinterTest}\r\n                                            className=\"test-button\"\r\n                                            disabled={currentStep < 2}\r\n                                        >\r\n                                            <MdIcons.MdPrint className=\"button-icon\" />\r\n                                            Testar Impressora\r\n                                        </button>\r\n                                    </div>\r\n                                    <div className=\"step-info\">\r\n                                        <small>\r\n                                            <AiIcons.AiOutlineCheckCircle className=\"check-icon\" />\r\n                                            Um comando de teste será impresso em sua impressora\r\n                                        </small>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                            {currentStep >= 3 && (\r\n                                <div className=\"success-card\">\r\n                                    <div className=\"success-content\">\r\n                                        <FaIcons.FaCheckCircle className=\"success-icon\" />\r\n                                        <h2>Configuração Concluída!</h2>\r\n                                        <p>Sua impressora está configurada e pronta para receber pedidos automaticamente. Os comandos serão impressos assim que os pedidos forem confirmados.</p>\r\n                                    </div>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n\r\n                    </div>\r\n                </div>\r\n            </Teste>\r\n                \r\n        </PermissionGate>       \r\n        </>\r\n    )\r\n}\r\n\r\nexport default ConfigPrinter;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,aAAa;AACpB,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,SAASC,cAAc,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,KAAK,GAAGZ,MAAM,CAACa,GAAG;AACxB;AACA;AACA,oBAAoB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAdIH,KAAK;AAgBX,MAAMI,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAExB,MAAM;IAAEH,OAAO;IAAEI;EAAW,CAAC,GAAGvB,UAAU,CAACO,cAAc,CAAC;EAAC,CAAC,CAAC;EAC7D,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM6B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,MAAM2B,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,CAAC;EACxC,MAAMM,SAAS,GAAGH,YAAY,CAACI,UAAU;EACzC,MAAMC,YAAY,GAAGL,YAAY,CAACM,GAAG;EAErC,MAAMC,UAAU,GAAG,yBAAyB;EAE5C,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrB;IACAZ,QAAQ,CAAC,YAAY,CAAC;EAC1B,CAAC;EAED,MAAMa,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACA;MACA,MAAMC,WAAW,GAAG,iFAAiF;;MAErG;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACF,WAAW,CAAC;MACzC,IAAI,CAACC,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;MACnE;MACA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;;MAElC;MACA,MAAMC,UAAU,GAAGF,IAAI,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,KAAK,sBAAsB,CAAC;MACnF,IAAI,CAACJ,UAAU,EAAE;QACb,MAAM,IAAIH,KAAK,CAAC,+DAA+D,CAAC;MACpF;;MAEA;MACA,MAAMQ,WAAW,GAAGL,UAAU,CAACM,oBAAoB;;MAEnD;MACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGL,WAAW;MACvBE,IAAI,CAACI,QAAQ,GAAG,sBAAsB,CAAC,CAAC;MACxCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAE/BS,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEZ,WAAW,CAAC;MACnD3B,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACZF,OAAO,CAACE,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAClE;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,MAAMzB,QAAQ,GAAG,MAAM/B,cAAc,CAACyB,YAAY,CAAC;IACnD4B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAACvB,QAAQ,CAAC;IAC/C,IAAIA,QAAQ,IAAIA,QAAQ,CAAC0B,OAAO,EAAE;MAC9B1C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;EACJ,CAAC;EAED,MAAM2C,uBAAuB,GAAGA,CAAA,KAAM;IAClC3C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;EAED,oBACIb,OAAA,CAAAE,SAAA;IAAAuD,QAAA,eACAzD,OAAA,CAACX,cAAc;MAACqE,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAD,QAAA,eAKrCzD,OAAA,CAACG,KAAK;QAACE,OAAO,EAAEA,OAAQ;QAAAoD,QAAA,eACpBzD,OAAA;UAAK2D,SAAS,EAAC,WAAW;UAAAF,QAAA,eACtBzD,OAAA;YAAK2D,SAAS,EAAC,0BAA0B;YAAAF,QAAA,gBACrCzD,OAAA;cAAK2D,SAAS,EAAC,aAAa;cAAAF,QAAA,eACxBzD,OAAA;gBAAK2D,SAAS,EAAC,OAAO;gBAAAF,QAAA,gBAClBzD,OAAA;kBAAAyD,QAAA,gBAAIzD,OAAA,CAACJ,OAAO,CAACgE,OAAO;oBAACD,SAAS,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qCAAiC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrFhE,OAAA;kBAAG2D,SAAS,EAAC,UAAU;kBAAAF,QAAA,EAAC;gBAAkF;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7G;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,aAAa;cAAAF,QAAA,eACxBzD,OAAA;gBAAK2D,SAAS,EAAC,cAAc;gBAAAF,QAAA,gBACzBzD,OAAA,CAACN,OAAO,CAACuE,mBAAmB;kBAACN,SAAS,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrDhE,OAAA;kBAAAyD,QAAA,gBACIzD,OAAA;oBAAAyD,QAAA,EAAI;kBAAoC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7ChE,OAAA;oBAAAyD,QAAA,EAAG;kBAA4L;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAF,QAAA,gBAC5BzD,OAAA;gBAAK2D,SAAS,EAAE,aAAa/C,WAAW,IAAI,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAAA6C,QAAA,gBAC5DzD,OAAA;kBAAK2D,SAAS,EAAC,aAAa;kBAAAF,QAAA,gBACxBzD,OAAA;oBAAK2D,SAAS,EAAE,eAAe/C,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,EAAG;oBAAA6C,QAAA,EAC/D7C,WAAW,GAAG,CAAC,gBAAGZ,OAAA,CAACH,OAAO,CAACqE,OAAO;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,GAAG;kBAAG;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACNhE,OAAA;oBAAAyD,QAAA,EAAI;kBAAyB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACNhE,OAAA;kBAAK2D,SAAS,EAAC,cAAc;kBAAAF,QAAA,gBACzBzD,OAAA;oBAAAyD,QAAA,EAAG;kBAA2H;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAClIhE,OAAA;oBAAK2D,SAAS,EAAC,cAAc;oBAAAF,QAAA,gBACzBzD,OAAA;sBACImE,OAAO,EAAExC,2BAA4B;sBACrCgC,SAAS,EAAC,iBAAiB;sBAC3BS,IAAI,EAAC,QAAQ;sBAAAX,QAAA,gBAEbzD,OAAA,CAACN,OAAO,CAAC2E,iBAAiB;wBAACV,SAAS,EAAC;sBAAa;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,0BAEzD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACThE,OAAA;sBACImE,OAAO,EAAEX,uBAAwB;sBACjCG,SAAS,EAAC,2BAA2B;sBACrCS,IAAI,EAAC,QAAQ;sBAAAX,QAAA,gBAEbzD,OAAA,CAACN,OAAO,CAAC4E,oBAAoB;wBAACX,SAAS,EAAC;sBAAa;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,2BAE5D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNhE,OAAA;oBAAK2D,SAAS,EAAC,WAAW;oBAAAF,QAAA,eACtBzD,OAAA;sBAAAyD,QAAA,gBACIzD,OAAA,CAACN,OAAO,CAAC6E,gBAAgB;wBAACZ,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,4CAEpD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENhE,OAAA;gBAAK2D,SAAS,EAAE,aAAa/C,WAAW,IAAI,CAAC,GAAG,QAAQ,GAAG,UAAU,EAAG;gBAAA6C,QAAA,gBACpEzD,OAAA;kBAAK2D,SAAS,EAAC,aAAa;kBAAAF,QAAA,gBACxBzD,OAAA;oBAAK2D,SAAS,EAAE,eAAe/C,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,EAAG;oBAAA6C,QAAA,EAC/D7C,WAAW,GAAG,CAAC,gBAAGZ,OAAA,CAACH,OAAO,CAACqE,OAAO;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,GAAG;kBAAG;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACNhE,OAAA;oBAAAyD,QAAA,EAAI;kBAAiC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNhE,OAAA;kBAAK2D,SAAS,EAAC,cAAc;kBAAAF,QAAA,gBACzBzD,OAAA;oBAAAyD,QAAA,EAAG;kBAA0K;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACjLhE,OAAA;oBAAK2D,SAAS,EAAC,mBAAmB;oBAAAF,QAAA,gBAC9BzD,OAAA;sBAAAyD,QAAA,EAAI;oBAAoB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7BhE,OAAA;sBAAAyD,QAAA,gBACIzD,OAAA;wBAAAyD,QAAA,EAAI;sBAAkD;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC3DhE,OAAA;wBAAAyD,QAAA,EAAI;sBAAwC;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjDhE,OAAA;wBAAAyD,QAAA,EAAI;sBAAsD;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENhE,OAAA;gBAAK2D,SAAS,EAAE,aAAa/C,WAAW,IAAI,CAAC,GAAG,QAAQ,GAAG,UAAU,EAAG;gBAAA6C,QAAA,gBACpEzD,OAAA;kBAAK2D,SAAS,EAAC,aAAa;kBAAAF,QAAA,gBACxBzD,OAAA;oBAAK2D,SAAS,EAAE,eAAe/C,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,EAAG;oBAAA6C,QAAA,EAC/D7C,WAAW,GAAG,CAAC,gBAAGZ,OAAA,CAACH,OAAO,CAACqE,OAAO;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,GAAG;kBAAG;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACNhE,OAAA;oBAAAyD,QAAA,EAAI;kBAAgB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACNhE,OAAA;kBAAK2D,SAAS,EAAC,cAAc;kBAAAF,QAAA,gBACzBzD,OAAA;oBAAAyD,QAAA,EAAG;kBAAkH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzHhE,OAAA;oBAAK2D,SAAS,EAAC,cAAc;oBAAAF,QAAA,eACzBzD,OAAA;sBACIoE,IAAI,EAAC,QAAQ;sBACbD,OAAO,EAAEb,iBAAkB;sBAC3BK,SAAS,EAAC,aAAa;sBACvBa,QAAQ,EAAE5D,WAAW,GAAG,CAAE;sBAAA6C,QAAA,gBAE1BzD,OAAA,CAACJ,OAAO,CAACgE,OAAO;wBAACD,SAAS,EAAC;sBAAa;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,qBAE/C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNhE,OAAA;oBAAK2D,SAAS,EAAC,WAAW;oBAAAF,QAAA,eACtBzD,OAAA;sBAAAyD,QAAA,gBACIzD,OAAA,CAACN,OAAO,CAAC4E,oBAAoB;wBAACX,SAAS,EAAC;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,0DAE3D;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAELpD,WAAW,IAAI,CAAC,iBACbZ,OAAA;gBAAK2D,SAAS,EAAC,cAAc;gBAAAF,QAAA,eACzBzD,OAAA;kBAAK2D,SAAS,EAAC,iBAAiB;kBAAAF,QAAA,gBAC5BzD,OAAA,CAACH,OAAO,CAAC4E,aAAa;oBAACd,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClDhE,OAAA;oBAAAyD,QAAA,EAAI;kBAAuB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChChE,OAAA;oBAAAyD,QAAA,EAAG;kBAAkJ;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEI;EAAC,gBACf,CAAC;AAEX,CAAC;AAAAxD,EAAA,CAtMKD,aAAa;EAAA,QAKEnB,WAAW;AAAA;AAAAsF,GAAA,GAL1BnE,aAAa;AAwMnB,eAAeA,aAAa;AAAC,IAAAD,EAAA,EAAAoE,GAAA;AAAAC,YAAA,CAAArE,EAAA;AAAAqE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}