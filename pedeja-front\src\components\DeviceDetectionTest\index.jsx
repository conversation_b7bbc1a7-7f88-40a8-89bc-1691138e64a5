import React from 'react';
import { useDeviceDetection, useNotificationSupport } from '../../hooks/useDeviceDetection';

/**
 * Componente de teste para verificar a detecção de dispositivos
 * Este componente pode ser usado temporariamente para testar se a detecção está funcionando
 * 
 * Para usar, importe e adicione em qualquer página:
 * import DeviceDetectionTest from '../components/DeviceDetectionTest';
 * <DeviceDetectionTest />
 */
const DeviceDetectionTest = () => {
  const deviceInfo = useDeviceDetection();
  const notificationInfo = useNotificationSupport();

  const handleTestNotification = async () => {
    if (notificationInfo.canShow) {
      new Notification('Teste de Notificação', {
        body: 'Esta é uma notificação de teste do Pede Já',
        icon: '/favicon.ico'
      });
    } else if (notificationInfo.canRequest) {
      const permission = await notificationInfo.requestPermission();
      if (permission === 'granted') {
        new Notification('Teste de Notificação', {
          body: 'Permissão concedida! Esta é uma notificação de teste.',
          icon: '/favicon.ico'
        });
      }
    } else {
      alert(notificationInfo.getStatusMessage());
    }
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      border: '2px solid #ccc', 
      padding: '15px', 
      borderRadius: '8px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      maxWidth: '300px',
      fontSize: '12px',
      zIndex: 9999
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: '#333' }}>🔍 Detecção de Dispositivo</h4>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>Dispositivo:</strong>
        <ul style={{ margin: '5px 0', paddingLeft: '15px' }}>
          <li>iOS: {deviceInfo.isIOS ? '✅' : '❌'}</li>
          <li>Safari: {deviceInfo.isSafari ? '✅' : '❌'}</li>
          <li>Móvel: {deviceInfo.isMobileDevice ? '✅' : '❌'}</li>
          <li>Tela Móvel: {deviceInfo.isMobileScreen ? '✅' : '❌'}</li>
          <li>Tipo: {deviceInfo.getDeviceType()}</li>
        </ul>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>Tela:</strong>
        <ul style={{ margin: '5px 0', paddingLeft: '15px' }}>
          <li>Largura: {deviceInfo.screenWidth}px</li>
          <li>Altura: {deviceInfo.screenHeight}px</li>
        </ul>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>Notificações:</strong>
        <ul style={{ margin: '5px 0', paddingLeft: '15px' }}>
          <li>Suporte: {notificationInfo.isSupported ? '✅' : '❌'}</li>
          <li>Permissão: {notificationInfo.permission}</li>
          <li>Pode Solicitar: {notificationInfo.canRequest ? '✅' : '❌'}</li>
          <li>Pode Mostrar: {notificationInfo.canShow ? '✅' : '❌'}</li>
        </ul>
        <p style={{ margin: '5px 0', fontSize: '11px', color: '#666' }}>
          {notificationInfo.getStatusMessage()}
        </p>
      </div>

      <button 
        onClick={handleTestNotification}
        style={{
          width: '100%',
          padding: '8px',
          backgroundColor: notificationInfo.canShow || notificationInfo.canRequest ? '#007bff' : '#6c757d',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: notificationInfo.canShow || notificationInfo.canRequest ? 'pointer' : 'not-allowed',
          fontSize: '12px'
        }}
        disabled={!notificationInfo.canShow && !notificationInfo.canRequest}
      >
        {notificationInfo.canShow ? 'Testar Notificação' : 
         notificationInfo.canRequest ? 'Solicitar Permissão' : 
         'Notificações Indisponíveis'}
      </button>

      <div style={{ marginTop: '10px', fontSize: '10px', color: '#999' }}>
        <strong>User Agent:</strong>
        <p style={{ margin: '2px 0', wordBreak: 'break-all' }}>
          {deviceInfo.userAgent.substring(0, 100)}...
        </p>
      </div>
    </div>
  );
};

export default DeviceDetectionTest;
