const mongoose = require('mongoose')

const ClienteSchema = new mongoose.Schema({
    /*empresaObjId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Empresa',
        required: true,
    },*/
    createdBy: String,
    id_cliente: Number,
    id_empresa: Number,
    vinculo_empresa: String,
    nome: String,
    telefone: String,
    endereco: [
        {
            rua: String,
            numero: String,
            bairro: String,
            cidade: String,
            estado: String,
            cep: String,
            complemento: String,
            referencia: String,
            latitude: String,
            longitude: String
        },
    ],
    counter_qtd_pedido: Number,
    cpf_cnpj: String,
    type: String,
    inativo: Boolean,
    bloqueado: Boolean,
    createdAt: Date,
    updatedAt: Date,
    deletedAt: Date

})

const Cliente = mongoose.model('Cliente', ClienteSchema)

module.exports = Cliente