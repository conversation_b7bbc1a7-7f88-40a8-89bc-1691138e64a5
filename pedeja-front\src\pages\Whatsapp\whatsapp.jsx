import React, { useState, useEffect, useContext, useCallback } from "react";
import { useRef } from "react";
import { useLocation } from "react-router-dom";
import LeftMenu from "../../components/LeftMenu";
//import { MenuProfile } from "../../contexts/MenuProfileContext";
import './style.css';
import styled from "styled-components";
import Profile from "../../components/Profile";
import Chat from "../../components/Chat";
import io from 'socket.io-client';
import mockMessages from "./mock.json";
import mockOptions from "./mockOptions.json";
import contactUserPhoto from "../../assets/img/contact-user-photo.png";
import backgroundChat from "../../assets/img/chat-background.png";
import SearchBar from "../../components/SearchBar";
import * as S from "./stylesProfile";
import UserProfile from "./UserProfile"; // Importa o modal
import { SidebarContext } from "../../AppRoutes";
import { MenuProfileContext } from "../../contexts/MenuProfileContext";
import { getQrCodeWhatsapp, renewQrCodeWhatsapp, getWhatsappChats, getWhatsappChatLead, sendMessage, sendTextMessageWhatsapp, removeWhatsappSession, toggleBotStatus, getBotStatusLead, getWhatsappProfilePicture, getMyWhatsappProfilePicture, getWhatsappUnreadCount, markWhatsappMessagesAsRead, getWhatsappConnectionStatus, refreshContactInfoWhatsapp, getWhatsappChatById, createWhatsappChat } from "../../services/api";
import Loading from "react-loading";
import CryptoJS from 'crypto-js';
import moment from 'moment-timezone';
import 'moment/locale/pt-br';  // Adiciona a localidade em português
import useImagePreloader from '../../hooks/useImagePreloader';
//import ChatMenu from "../Menu";
import userIcon from "../../assets/img/user-icon.png";
import { ReactComponent as CheckIcon } from "../../assets/svg/check-icon.svg";
import { ReactComponent as StatusIcon } from "../../assets/svg/status-icon.svg";
import { ReactComponent as ChatIcon } from "../../assets/svg/chat-icon.svg";
import { ReactComponent as MenuIcon } from "../../assets/svg/menu-icon.svg";
import { ReactComponent as BackIcon } from "../../assets/svg/arrow-back-icon.svg";
import { FaWhatsapp } from "react-icons/fa";
import { FiSend, FiPauseCircle, FiPlayCircle } from "react-icons/fi"; // Importando o ícone de envio
import { toast } from 'react-toastify';

import Input from "../../components/Input/index";
import UnreadBadge from "../../components/UnreadBadge";

import GlobalStyles, { Container } from "../../styles/global";

// Hook para detectar mobile
function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);
  React.useEffect(() => {
    const onResize = () => setIsMobile(window.innerWidth <= 768);
    window.addEventListener('resize', onResize);
    return () => window.removeEventListener('resize', onResize);
  }, []);
  return isMobile;
}

export const WrapperContainerAll = styled.div`
  display: flex;
  height: 100%;
  
  @media (max-width: 768px) {
    flex-direction: column;
    height: 100vh;
  }
`;

export const WrapperContainerConversa = styled.div`
  width: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-attachment: fixed;
  background-color: #e5ddd5; /* Cor de fallback com padrão do WhatsApp */
  background-image: ${props => props.backgroundLoaded ? 
    `url(${backgroundChat})` : 
    `linear-gradient(45deg, 
      #e5ddd5 0%, 
      #f0f0f0 25%, 
      #e5ddd5 50%, 
      #f0f0f0 75%, 
      #e5ddd5 100%)`}; /* Padrão sutil enquanto carrega */
  transition: background-image 0.3s ease-in-out; /* Transição suave quando a imagem carrega */
  position: relative;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
  
  @media (max-width: 768px) {
    height: 100%;
    background-attachment: scroll; /* Melhor performance no mobile */
    position: relative;
    top: auto;
    left: ${props => props.isMobile && props.showMobileChat ? '0' : 'auto'};
    z-index: auto;
    display: ${props => props.isMobile && !props.showMobileChat ? 'none' : 'flex'};
  }
  
  /* Indicador sutil de carregamento se a imagem não carregou */
  ${props => !props.backgroundLoaded && `
    &::after {
      content: '📸 Carregando background...';
      position: absolute;
      bottom: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.9);
      padding: 8px 12px;
      border-radius: 20px;
      font-size: 12px;
      color: #666;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 1; }
    }
  `}
`;

const WrapperContent = styled.div`
  padding: 1rem 4rem;
  height: 100%;
  overflow-y: scroll;
  margin-bottom: 60px;
  background-color: transparent; /* Remove background duplicado */
  
  @media (max-width: 768px) {
    padding: 1rem 1rem;
    margin-bottom: 70px;
  }
`;

const ChatDate = styled.time`
  display: block;
  background: #e1f3fb;
  padding: 10px;
  border-radius: 7.5px;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
  font-size: 1.42rem;
  margin: 10px auto;
  width: fit-content;
  text-transform: uppercase;
  
  @media (max-width: 768px) {
    font-size: 1.2rem;
    padding: 8px;
  }
`;

const Message = styled.div`
  background: ${(props) => (props.fromMe ? '#dcf8c6' : '#ffffff')};
  padding: 10px;
  border-radius: 7.5px;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
  font-size: 1.42rem;
  margin: 1px 0px 1px ${(props) => (props.fromMe ? 'auto' : '0px')};
  width: fit-content;
  max-width: 60%;
  margin-bottom: ${(props) => (props.fromMe ? '3px' : '10px')};
  white-space: pre-wrap;
  word-break: break-word; /* Adicionado para quebra de palavras longas */
  span {
    font-size: 1.1rem;
    color: rgba(0, 0, 0, 0.45);
    display: block;
    text-align: right;
  }
  
  @media (max-width: 768px) {
    font-size: 1.3rem;
    max-width: 80%;
    padding: 8px;
    
    span {
      font-size: 1rem;
    }
  }
`;

export const Warn = styled.div`
  background: #fdf4c4;
  width: fit-content;
  margin: 30px auto;
  padding: 5px 10px;
  font-size: 1.6rem;
  border-radius: 5px;
  
  @media (max-width: 768px) {
    font-size: 1.4rem;
    margin: 20px auto;
    padding: 8px 12px;
  }
`;

export const WrapperHMC = styled.aside`
  position: relative;
  width: 55rem;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  @media (max-width: 1200px) {
    width: 45rem;
  }
  
  @media (max-width: 768px) {
    width: 100%;
    height: 100%;
    position: relative;
    left: ${props => props.isMobile && props.showMobileChat ? '-100%' : '0'};
    transition: left 0.3s ease;
    z-index: auto;
    display: ${props => props.isMobile && props.showMobileChat ? 'none' : 'flex'};
  }
`;

export const WrapperMenu = styled.div`
  position: absolute;
  z-index: 10;
  top: 60px;
  right: 20px;
  background: #fff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
  border-radius: 2px;

  transition: transform 0.1s ease;
  transform-origin: right top;
  transform: scale(0);

  &.active {
    transform: scale(1);
  }
`;

export const ActionWrapperMenu = styled.ul`
  padding: 10px 0px;
`;

export const ActionMenu = styled.li`
  padding: 10px 20px;
  cursor: pointer;
  font-size: 1.4rem;
  color: #333;
  transition: background 0.2s ease-in-out;

  &:hover {
    background: #f5f5f5;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.3;

    &:hover {
      background: none;
    }
  }
`;

export const Header = styled.header`
  display: flex;
  align-items: center;

  padding: 13px;
  background: #ededed;
  
  @media (max-width: 768px) {
    padding: 15px;
    position: relative;
  }
`;

export const ContactPhoto = styled.div`
  margin-right: 15px;

  img {
    border-radius: 100%;
  }
  
  @media (max-width: 768px) {
    margin-right: 12px;
  }
`;

export const ContactName = styled.span`
  font-size: 1.7rem;
  
  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

export const MobileBackButton = styled.button`
  display: none;
  
  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    padding: 8px;
    margin-right: 10px;
    cursor: pointer;
    color: #333;
    font-size: 1.8rem;
  }
`;

export const WrapperChat = styled.div`
  padding: 10px;
  margin-top: 15px;
  background: #f0f0f0;
  position: absolute;
  bottom: 0;
  width: 100%;
  
  @media (max-width: 768px) {
    padding: 15px;
    margin-top: 0;
    
    form {
      gap: 8px;
    }
  }
`;

export const WrapperListContacts = styled.div`
  display: flex;
  align-items: center;
  background: ${(props) => (props.isSelected ? '#eaeaea' : 'white')};
  cursor: pointer;
  padding: 10px;  /* Adicionado padding para espaçamento */
  border-bottom: 1px solid #eee;
  &:hover {
    background: #f4f4f4;
  }
  
  @media (max-width: 768px) {
    padding: 15px 10px;
    
    &:active {
      background: #e0e0e0;
    }
  }
`;

export const ContactPhotoListContacts = styled.div`
  flex: 0 0 60px;  /* Define a largura fixa da área da foto */
  padding: 10px;

  img {
    border-radius: 100%;
    width: 100%;  /* Garante que a imagem ocupe a largura total do contêiner */
  }
  
  @media (max-width: 768px) {
    flex: 0 0 50px;
    padding: 8px;
  }
`;

export const ContactNameAndTime = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  span {
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70%;
  }

  p {
    font-size: 1.2rem;
    color: rgba(0, 0, 0, 0.45);
    margin-left: 10px;  /* Adiciona espaço entre o nome e a data */
    flex-shrink: 0;  /* Garante que a data não encolha */
  }
  
  @media (max-width: 768px) {
    span {
      font-size: 16px;
      max-width: 65%;
    }
    
    p {
      font-size: 1.1rem;
    }
  }
`;

export const ContactMessage = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  svg {
    margin-right: 5px;
  }

  P {
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;  /* Ajusta a largura máxima para evitar overflow */
  }
  
  @media (max-width: 768px) {
    P {
      font-size: 14px;
    }
  }
`;

export const MessageDataWrapper = styled.div`
  flex: 1;
  padding-right: 15px;
  overflow: hidden;  /* Garante que o conteúdo não extrapole os limites */
  display: flex;
  flex-direction: column;
  
  @media (max-width: 768px) {
    padding-right: 10px;
  }
`;

export const WrapperListHeader = styled.div`
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  
  @media (max-width: 768px) {
    border-right: none;
  }
`;

export const HeaderListHeader = styled.header`
  background-color: #ededed;
  width: 100%;
  padding: 15px;

  display: flex;
  justify-content: space-between;
  align-items: center;
  
  @media (max-width: 768px) {
    padding: 12px 15px;
  }
`;

export const UserImage = styled.div`
  margin-right: auto;
  cursor: pointer;

  img {
    border-radius: 100%;
  }
  
  @media (max-width: 768px) {
    img {
      width: 35px;
      height: 35px;
    }
  }
`;

export const Actions = styled.div`
  display: flex;
  align-items: center;

  svg {
    width: 50px;
    cursor: pointer;

    &:not(:last-child) {
      cursor: not-allowed;
      opacity: 0.3;
    }
  }
  
  @media (max-width: 768px) {
    svg {
      width: 40px;
    }
  }
`;

const Teste = styled.div`
  display: flex;
  margin-left:  ${({ sidebar }) => (sidebar ? '250px' : '100px')}; 
  height:auto;
  width:auto;
  transition: 150ms;
  background-color:rgb(247,247,247)!important;
  --background-color:white!important;
  overflow: initial;
  z-Index: 9;
  
  @media (max-width: 768px) {
    margin-left: 0;
    height: calc(100vh - 60px); /* Deixa espaço para o navbar */
    width: 100vw;
    position: relative; /* Muda de fixed para relative */
    top: auto;
    left: 0;
    z-index: 1;
    margin-top: 0; /* Remove margem superior */
  }
`;

// Helper Function to Group Messages by Date
const groupMessagesByDate = (messages) => {
  return messages.reduce((acc, message) => {
    const date = moment(message.createdAt).format('YYYY-MM-DD');
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(message);
    return acc;
  }, {});
};

// Função para gerar ObjectId aleatório
const generateObjectId = () => {
  return Math.floor(Date.now() / 1000).toString(16) + 'xxxxxxxxxxxxxxxx'.replace(/[x]/g, function () {
    return Math.floor(Math.random() * 16).toString(16);
  }).toLowerCase();
};

const WhatsAppWeb = () => {
  moment.locale('pt-br');  // Define o locale para português
  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';
  const userEncrypted = localStorage.getItem('user');
  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);
  const userParse = JSON.parse(user);
  const userID = userParse._id;
  const empresa = localStorage.getItem('empresa');
  const empresaParse = JSON.parse(empresa);
  const empresaID = empresaParse._id;
  const token = localStorage.getItem('token');

  const location = useLocation(); // Captura o state passado pelo navigate
  const leadIdParaAbrir = location.state?.lead_id || null; // Verifica se recebeu um lead_id

  const isMobile = useIsMobile();
  const [showMobileChat, setShowMobileChat] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  //const { openMenu } = useContext(MenuProfileContext);
  const { isOpen, openMenu, closeMenu } = useContext(MenuProfileContext);
  //const openMenu = () => setIsMenuOpen(true);
  const menuRef = useRef(null);
  const [message, setMessage] = useState("");
  const { sidebar, setSidebar } = useContext(SidebarContext);; //TESTANDO CONST NO AppROUTES
  const [isSubmitting, setIsSubmitting] = useState(false);
  // 📱 Estados do QR Code - MELHORADO
  const [qrCodeImg, setQrCodeImg] = useState('');
  const [generatingQrCode, setGeneratingQrCode] = useState(false);
  const [isLoged, setIsLoged] = useState(true); // Inicializar como true para melhor UX (evita flash do QR Code)
  const [qrCodeExpired, setQrCodeExpired] = useState(false);
  const [qrCodeTimeLeft, setQrCodeTimeLeft] = useState(0);
  const [userRequestedQrCode, setUserRequestedQrCode] = useState(false); // 🔧 NOVO: Controla se usuário solicitou QR Code
  const [showRegenerateButton, setShowRegenerateButton] = useState(false);
  const [expectingQrCode, setExpectingQrCode] = useState(false); // Flag para controlar quando esperamos QR Code
  //const [socket, setSocket] = useState(null);
  const [chats, setChats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedChat, setSelectedChat] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [messagesLeadChannel, setMessagesLeadChannel] = useState([]);
  const timeoutRef = useRef(null); // UseRef to store the timeout ID
  const isMountedRef = useRef(true); // Para saber se o componente está montado
  const cancelQrCodeFetchRef = useRef(false); // Flag para cancelar a execução do fetch
  // 📱 Refs para QR Code melhorado
  const qrCodeTimerRef = useRef(null);
  const countdownTimerRef = useRef(null);
  const [botPausado, setBotPausado] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [messagesCache, setMessagesCache] = useState(new Map());
  const [loadingMessages, setLoadingMessages] = useState(false);

  // **📌 ESTADOS PARA PAGINAÇÃO DE MENSAGENS**
  const [messagesPage, setMessagesPage] = useState(0);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [loadingMoreMessages, setLoadingMoreMessages] = useState(false);
  const messagesContainerRef = useRef(null);
  const [preloadingChats, setPreloadingChats] = useState(false);
  const [preloadProgress, setPreloadProgress] = useState({ current: 0, total: 0 });
  const [hasPreloaded, setHasPreloaded] = useState(false);
  const [raceConditionsFixed, setRaceConditionsFixed] = useState(0);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const fetchingChatsRef = useRef(false);

  // **📌 ESTADO PARA IMAGEM DE PERFIL DO PRÓPRIO USUÁRIO**
  const [myProfilePicture, setMyProfilePicture] = useState(null);

  // **📌 ESTADO PARA CONTROLAR SE DEVE ROLAR PARA O BOTTOM**
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);
  
  // **📌 ESTADO PARA CONTROLAR SE ACABOU DE ABRIR UMA CONVERSA**
  const [justOpenedChat, setJustOpenedChat] = useState(false);
  const justOpenedTimerRef = useRef(null);

  // **📌 ESTADO PARA CONTAGEM DE MENSAGENS NÃO LIDAS**
  const [unreadCounts, setUnreadCounts] = useState({});

  // **📌 PRELOAD DA IMAGEM DE BACKGROUND USANDO HOOK CUSTOMIZADO**
  const { loaded: backgroundLoaded, error: backgroundError, progress: backgroundProgress } = useImagePreloader(backgroundChat, 3000);

  // **📌 DEBUG: LOG DE ERROS DO BACKGROUND**
  useEffect(() => {
    if (backgroundError) {
      console.error('🚨 [BACKGROUND ERROR]:', backgroundError);
      console.log('📊 [BACKGROUND DEBUG]:', {
        backgroundLoaded,
        backgroundProgress: `${backgroundProgress}%`,
        backgroundChat: backgroundChat?.substring(0, 50) + '...',
        isDevelopment: process.env.NODE_ENV === 'development',
        baseURL: window.location.origin
      });
      
      // Tentar carregar a imagem diretamente no console para debug
      console.log('🔍 [DEBUG] Testando carregamento direto da imagem:');
      const testImg = new Image();
      testImg.onload = () => console.log('✅ [DEBUG] Imagem carrega OK diretamente');
      testImg.onerror = (e) => console.error('❌ [DEBUG] Imagem falha ao carregar diretamente:', e);
      testImg.src = backgroundChat;
    } else if (backgroundLoaded && backgroundProgress === 100) {
      console.log('🎉 [BACKGROUND] Carregamento concluído com sucesso!');
      // Toast sutil apenas no desenvolvimento para debug
      /*if (process.env.NODE_ENV === 'development') {
        setTimeout(() => {
          toast.success('🎨 Background do chat carregado', {
            position: "bottom-right",
            autoClose: 2000,
            hideProgressBar: true,
            closeOnClick: true,
            pauseOnHover: false,
            draggable: false,
          });
        }, 500);
      }*/
    }
  }, [backgroundError, backgroundLoaded, backgroundProgress]);

  // Função para navegar para o chat no mobile
  const handleSelectChatMobile = (chat) => {
    setSelectedChat(chat);
    if (isMobile) {
      setShowMobileChat(true);
    }

    // **📌 MARCAR MENSAGENS COMO LIDAS AO SELECIONAR CHAT**
    if (unreadCounts[chat._id] && unreadCounts[chat._id] > 0) {
      markChatAsRead(chat._id);
    }
  };

  // Função para voltar para a lista no mobile
  const handleBackToList = () => {
    if (isMobile) {
      setShowMobileChat(false);
      setSelectedChat(null);
    }
  };

  // Função para abrir perfil apenas quando clicar na foto
  const handleOpenProfile = (e) => {
    e.stopPropagation(); // Previne propagação do evento
    setIsProfileOpen(true);
  };

  // Resetar estado mobile quando a tela mudar de tamanho
  useEffect(() => {
    if (!isMobile) {
      setShowMobileChat(false);
    }
  }, [isMobile]);

  // Sort messages by date
  const sortedMessages = [...messagesLeadChannel].sort((a, b) =>
    new Date(a.createdAt) - new Date(b.createdAt)
  );

  const groupedMessages = groupMessagesByDate(sortedMessages);
  const messageEndRef = useRef(null);

  useEffect(() => {
    // **📌 SÓ ROLAR PARA O BOTTOM SE NÃO ESTIVER CARREGANDO MENSAGENS ANTIGAS**
    if (messageEndRef.current && !loadingMoreMessages && shouldScrollToBottom) {
      // **📌 GARANTIR SCROLL INSTANTÂNEO PARA O BOTTOM, ESPECIALMENTE QUANDO ABRE CONVERSA**
      const container = messagesContainerRef.current;
      if (container && messagesLeadChannel.length > 0) {
        // Forçar scroll para o bottom imediatamente
        container.scrollTop = container.scrollHeight;
        
        // Backup com scrollIntoView para garantir
        setTimeout(() => {
          if (messageEndRef.current && shouldScrollToBottom) {
            messageEndRef.current.scrollIntoView({ behavior: "instant" });
          }
        }, 50);
      }
    }
  }, [messagesLeadChannel, loadingMoreMessages, shouldScrollToBottom]);

  const handleClickOutside = event => {
    if (menuRef.current && !menuRef.current.contains(event.target))
      setIsMenuOpen(false);
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    if (!message.trim() || isSubmitting) return;

    if (!selectedChat || !selectedChat.mobile_number) {
      console.error('Nenhum chat selecionado ou número inválido');
      return;
    }



    const messageToSend = message.trim();

    // **📌 ENVIO OTIMISTA: Adicionar mensagem imediatamente**
    const optimisticMessage = {
      _id: `temp_${Date.now()}`,
      text: messageToSend,
      fromMe: true,
      createdAt: new Date().toISOString(),
      messageDate: new Date().toISOString(),
      leadChannel: selectedChat._id,
      isOptimistic: true, // Flag para identificar mensagens otimistas
      status: 'sending'
    };

    // Adicionar mensagem otimista na conversa
    setMessagesLeadChannel(oldMessages => [optimisticMessage, ...oldMessages]);

    // **📌 PARAR LOADING JÁ QUE TEMOS MENSAGEM OTIMISTA**
    setLoadingMessages(false);
    
    // **📌 GARANTIR QUE O SCROLL PERMANEÇA NO BOTTOM APÓS MENSAGEM OTIMISTA**
    setTimeout(() => {
      const container = messagesContainerRef.current;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }, 50);

    // Atualizar cache das mensagens
    setMessagesCache(prevCache => {
      const newCache = new Map(prevCache);
      const cachedMessages = newCache.get(selectedChat._id) || [];
      newCache.set(selectedChat._id, [optimisticMessage, ...cachedMessages]);
      return newCache;
    });

    // Atualizar a lista de chats com a mensagem otimista
    setChats(oldChats => {
      const updatedChats = oldChats.map(chat => {
        if (chat._id === selectedChat._id) {
          return {
            ...chat,
            message: {
              text: messageToSend,
              message: messageToSend,
              messageDate: new Date().toISOString(),
              createdAt: new Date().toISOString(),
              fromMe: true
            },
            updatedAt: new Date().toISOString()
          };
        }
        return chat;
      });

      // Mover o chat atualizado para o topo da lista
      const chatIndex = updatedChats.findIndex(chat => chat._id === selectedChat._id);
      if (chatIndex > 0) {
        const [chatToMove] = updatedChats.splice(chatIndex, 1);
        updatedChats.unshift(chatToMove);
      }

      return updatedChats;
    });

    try {
      setIsSubmitting(true);
      setMessage('');  // Limpa o campo de mensagem antes do envio

      const response = await sendTextMessageWhatsapp(empresaID, selectedChat.mobile_number, messageToSend);

      // Atualizar status da mensagem otimista para 'sent'
      setMessagesLeadChannel(oldMessages =>
        oldMessages.map(msg =>
          msg._id === optimisticMessage._id
            ? { ...msg, status: 'sent' }
            : msg
        )
      );

      // A mensagem real será adicionada automaticamente via webhook quando for processada

    } catch (error) {
      console.error('❌ Erro ao enviar a mensagem:', error);
      console.error('❌ Detalhes do erro:', error.response?.data);
      console.error('❌ Status:', error.response?.status);

      // Remover mensagem otimista em caso de erro
      setMessagesLeadChannel(oldMessages =>
        oldMessages.filter(msg => msg._id !== optimisticMessage._id)
      );

      // Remover do cache também
      setMessagesCache(prevCache => {
        const newCache = new Map(prevCache);
        const cachedMessages = newCache.get(selectedChat._id) || [];
        newCache.set(selectedChat._id, cachedMessages.filter(msg => msg._id !== optimisticMessage._id));
        return newCache;
      });

      // Restaurar a mensagem em caso de erro
      setMessage(messageToSend);

      // Mostrar toast de erro com detalhes
      const errorMessage = error.response?.data?.error || 'Erro ao enviar mensagem. Tente novamente.';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isDevelopment = window.location.hostname === 'localhost';
  const apiUrl = isDevelopment
    ? process.env.REACT_APP_SERVER_URL_DEV
    : process.env.REACT_APP_SERVER_URL_PROD;

  // **📌 SOCKET PRINCIPAL PARA WHATSAPP**
  const [mainSocket, setMainSocket] = useState(null);

  // **📌 FUNÇÃO PARA MARCAR MENSAGENS COMO LIDAS**
  const markChatAsRead = useCallback(async (chatId) => {
    try {
      console.log(`📖 Marcando mensagens como lidas para chat: ${chatId}`);
      const response = await markWhatsappMessagesAsRead(empresaID, chatId, token);
      if (response.data && response.data.status === 200) {
        console.log(`✅ Mensagens marcadas como lidas com sucesso para chat: ${chatId}`);

        // Atualizar contagem local
        setUnreadCounts(prevCounts => {
          const newCounts = { ...prevCounts };
          delete newCounts[chatId]; // Remove a contagem para este chat
          return newCounts;
        });

        // Atualizar contagem geral
        try {
          const response = await getWhatsappUnreadCount(empresaID, token);
          if (response.data && response.data.status === 200) {
            setUnreadCounts(response.data.unreadCounts);
          }
        } catch (error) {
          console.error('❌ Erro ao buscar contagem de mensagens não lidas:', error);
        }
      } else {
        console.log(`⚠️ Resposta inesperada ao marcar como lidas:`, response.data);
      }
    } catch (error) {
      console.error(`❌ Erro ao marcar mensagens como lidas para chat ${chatId}:`, error);
    }
  }, [empresaID, token]);

  // 📱 FUNÇÃO MELHORADA PARA GERENCIAR QR CODE COM TIMER DE 45 SEGUNDOS (ALINHADO COM BACKEND)
  const startQrCodeTimer = (initialTtl = 45) => {
    // Limpar timers existentes
    if (qrCodeTimerRef.current) {
      clearTimeout(qrCodeTimerRef.current);
    }
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
    }

    // Resetar estados
    setQrCodeExpired(false);
    setShowRegenerateButton(false);
    setQrCodeTimeLeft(initialTtl); // TTL dinâmico (padrão 45 segundos)

    console.log(`⏱️ Iniciando timer de QR Code: ${initialTtl} segundos`);

    // Timer de countdown (atualiza a cada segundo)
    countdownTimerRef.current = setInterval(() => {
      setQrCodeTimeLeft(prev => {
        if (prev <= 1) {
          // QR Code expirou
          setQrCodeExpired(true);
          setShowRegenerateButton(true);
          clearInterval(countdownTimerRef.current);
          
          console.log(`⏰ QR Code expirou após ${initialTtl} segundos`);
          
          // 🔧 REGENERAÇÃO AUTOMÁTICA DESABILITADA - Usuário deve clicar manualmente
          // setTimeout(() => {
          //   if (!isLoged && isMountedRef.current) {
          //     console.log('🔄 Regenerando QR Code automaticamente via nova rota...');
          //     regenerateQrCodeViaAPI('automatic_expiry');
          //   }
          // }, 2000); // Delay de 2 segundos antes de regenerar
          
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Timer principal baseado no TTL
    qrCodeTimerRef.current = setTimeout(() => {
      setQrCodeExpired(true);
      setShowRegenerateButton(true);
      clearInterval(countdownTimerRef.current);
    }, initialTtl * 1000);
  };

  // 📱 NOVA FUNÇÃO PARA GERAR QR CODE MANUALMENTE (PRIMEIRA VEZ)
  const generateQrCodeManually = async () => {
    console.log('🔄 Usuário solicitou geração manual do QR Code...');
    setUserRequestedQrCode(true);
    await fetchQrCode('manual_user_request');
  };

  // 📱 NOVA FUNÇÃO PARA REGENERAR QR CODE VIA API (BACKEND MELHORADO)
  const regenerateQrCodeViaAPI = async (source = 'manual') => {
    console.log(`🔄 Regenerando QR Code via nova API (${source})...`);

    // Verificar se componente ainda está montado
    if (!isMountedRef.current) {
      console.log('🛑 Componente desmontado, cancelando regeneração');
      return;
    }

    // Limpar timers existentes
    if (qrCodeTimerRef.current) {
      clearTimeout(qrCodeTimerRef.current);
    }
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
    }

    setQrCodeExpired(false);
    setShowRegenerateButton(false);
    setGeneratingQrCode(true);

    try {
      // 🚀 USAR NOVA FUNÇÃO DE API PARA RENOVAÇÃO
      const response = await renewQrCodeWhatsapp(empresaID, token);
      const data = response.data;

      if (!isMountedRef.current) return;

      if (data.status === 200) {
        if (data.isConnected) {
          // Já conectado
          console.log('✅ WhatsApp já está conectado!');
          setIsLoged(true);
          setGeneratingQrCode(false);
          setQrCodeImg('');
          clearQrCodeTimers();
          return;
        }

        if (data.qrcode && data.qrcode.qr) {
          // QR Code renovado com sucesso
          console.log('✅ QR Code renovado via API:', data.qrcode);
          setQrCodeImg(data.qrcode.qr);
          setGeneratingQrCode(false);
          
          // Usar TTL do backend se disponível
          const ttl = data.qrcode.ttl ? 
            Math.max(1, data.qrcode.ttl - Math.floor(Date.now() / 1000)) : 45;
          
          startQrCodeTimer(ttl);
          return;
        }
      }

      // Se chegou aqui, algo deu errado
      console.warn('⚠️ Resposta inesperada da API de renovação:', data);
      throw new Error(data.error || 'Erro desconhecido na renovação');

    } catch (error) {
      console.error(`❌ Erro ao regenerar QR Code via API (${source}):`, error);
      
      if (!isMountedRef.current) return;
      
      setGeneratingQrCode(false);
      setShowRegenerateButton(true);
      
      // Fallback: tentar método antigo
      console.log('🔄 Tentando método de fallback...');
      setTimeout(() => {
        if (!isLoged && isMountedRef.current) {
          regenerateQrCodeFallback(source);
        }
      }, 2000);
    }
  };

  // 📱 FUNÇÃO DE FALLBACK PARA REGENERAR QR CODE (MÉTODO ANTIGO)
  const regenerateQrCodeFallback = async (source = 'manual') => {
    console.log(`🔄 Regenerando QR Code via fallback (${source})...`);

    // Verificar se componente ainda está montado
    if (!isMountedRef.current) {
      console.log('🛑 Componente desmontado, cancelando regeneração');
      return;
    }

    setQrCodeExpired(false);
    setShowRegenerateButton(false);
    setGeneratingQrCode(true);

    try {
      await fetchQrCode(`fallback_${source}`);
    } catch (error) {
      console.error(`❌ Erro ao regenerar QR Code via fallback (${source}):`, error);
      setGeneratingQrCode(false);
      setShowRegenerateButton(true);
    }
  };

  // 📱 FUNÇÃO PARA LIMPAR TIMERS DO QR CODE
  const clearQrCodeTimers = () => {
    if (qrCodeTimerRef.current) {
      clearTimeout(qrCodeTimerRef.current);
      qrCodeTimerRef.current = null;
    }
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }
  };

  // 🔌 FUNÇÃO PARA LIMPAR SOCKET GLOBAL (usar apenas em logout/erro crítico)
  const cleanupGlobalSocket = () => {
    if (window.whatsappSocket) {
      console.log("🛑 Limpando socket WhatsApp global...");
      window.whatsappSocket.removeAllListeners();
      window.whatsappSocket.disconnect();
      window.whatsappSocket = null;
    }
  };

  // **📌 CRIAR SOCKET GLOBAL QUE PERSISTE ENTRE NAVEGAÇÕES**
  useEffect(() => {
    const socketNamespace = "/whatsapp";
    const wsUrl = apiUrl.replace(/\/$/, '') + socketNamespace;

    // Verificar se já existe um socket global conectado
    if (window.whatsappSocket && window.whatsappSocket.connected) {
      console.log("✅ Reutilizando socket WhatsApp existente");
      setMainSocket(window.whatsappSocket);
      return;
    }

    console.log("🔌 Criando novo socket WhatsApp global");
    const socket = io(wsUrl, {
      withCredentials: true,
      transports: ['websocket'],
      auth: { token: localStorage.getItem('token') },
      reconnection: true,
      reconnectionAttempts: 10,
      reconnectionDelay: 5000
    });

    // Armazenar socket globalmente para reutilização
    window.whatsappSocket = socket;
    setMainSocket(socket);

    return () => {
      console.log("🛑 Desmontando componente WhatsApp - mantendo socket conectado...");
      // ❌ NÃO DESCONECTAR O SOCKET - apenas limpar listeners específicos
      // O socket deve permanecer conectado para outras telas
      setMainSocket(null);
    };
  }, []);

  // **📌 CONFIGURAR EVENTOS DE CHATS NO SOCKET PRINCIPAL**
  useEffect(() => {
    if (!mainSocket) return;

    // Conectar ao namespace WhatsApp
    mainSocket.on('connect', () => {
      console.log("✅ Socket WhatsApp conectado");
      // **📌 IMPORTANTE: O join na sala é automático no backend quando o socket conecta**
      const roomName = `chats:${empresaID}`;
      console.log(`🏠 Socket automaticamente na sala: ${roomName}`);
    });

    mainSocket.on('disconnect', (reason) => {
      console.log("❌ Socket WhatsApp desconectado:", reason);
    });

    mainSocket.on('connect_error', (error) => {
      console.error("🚨 Erro de conexão socket WhatsApp:", error);
    });

    mainSocket.on('chats', (newChat) => {
      console.log("📨 Novo chat recebido:", newChat.name);
      setChats(oldChats => {
        // Remove o chat existente se já estiver na lista e adiciona o novo no início
        const filteredChats = oldChats.filter(chat => chat._id !== newChat._id);
        return [newChat, ...filteredChats];
      });

      // **📌 ATUALIZAR CACHE SE A MENSAGEM FOR DE UMA CONVERSA ATIVA**
      if (newChat.message && !newChat.message.fromMe) {
        setMessagesCache(prevCache => {
          const newCache = new Map(prevCache);
          const cachedMessages = newCache.get(newChat._id) || [];

          // Criar objeto de mensagem para o cache
          const messageForCache = {
            _id: newChat.message.id || `msg_${Date.now()}`,
            text: newChat.message.text || newChat.message.message,
            fromMe: newChat.message.fromMe,
            createdAt: newChat.message.createdAt || newChat.message.messageDate,
            messageDate: newChat.message.messageDate || newChat.message.createdAt,
            leadChannel: newChat._id
          };

          // Verificar se a mensagem já existe no cache
          const messageExists = cachedMessages.some(msg =>
            msg.text === messageForCache.text &&
            msg.fromMe === messageForCache.fromMe &&
            Math.abs(new Date(msg.createdAt) - new Date(messageForCache.createdAt)) < 5000 // 5 segundos de tolerância
          );

          if (!messageExists) {
            newCache.set(newChat._id, [messageForCache, ...cachedMessages]);
          }

          return newCache;
        });

        // **📌 ATUALIZAR CONTAGEM DE MENSAGENS NÃO LIDAS EM TEMPO REAL**
        if (!selectedChat || selectedChat._id !== newChat._id) {
          // Se o chat não está selecionado, incrementar contador
          setUnreadCounts(prevCounts => ({
            ...prevCounts,
            [newChat._id]: (prevCounts[newChat._id] || 0) + 1
          }));
        } else {
          // **📌 SE O CHAT ESTÁ ABERTO, MARCAR MENSAGEM COMO LIDA AUTOMATICAMENTE**
          console.log(`📖 Chat ${newChat.name} está aberto, marcando nova mensagem como lida automaticamente`);
          setTimeout(() => {
            markChatAsRead(newChat._id);
          }, 1000); // Delay de 1 segundo para garantir que a mensagem foi salva no banco
        }
      }

      console.log("✅ Lista de chats atualizada com nova mensagem de:", newChat.name);
    });

    // 📱 LISTENER MELHORADO PARA QR CODE UPDATES VIA SOCKET - COM FILTRO POR EMPRESA
    mainSocket.on('whatsapp_qrcode_update', (data) => {
      console.log('📱 [SOCKET] QR Code atualizado via socket:', {
        instanceId: data.instanceId,
        empresaID: data.empresaID,
        empresaName: data.empresaName,
        hasQrCode: !!data.qrcode,
        renewed: data.renewed,
        timestamp: data.timestamp
      });

      console.log('📊 [SOCKET] Estado atual:', {
        qrCodeImg: !!qrCodeImg,
        qrCodeExpired,
        generatingQrCode,
        isLoged,
        empresaAtual: empresaID
      });

      // **🎯 FILTRO CRÍTICO: IGNORAR QR CODES DE OUTRAS EMPRESAS**
      if (data.empresaID && data.empresaID !== empresaID) {
        console.log(`🚫 [SOCKET] QR Code ignorado - empresa diferente! Recebido: ${data.empresaName} (${data.empresaID}), Atual: ${empresaID}`);
        return;
      }

      // 🚫 IGNORAR QR CODES AUTOMÁTICOS SE JÁ ESTAMOS CONECTADOS
      if (isLoged) {
        console.log('🚫 [SOCKET] Ignorando QR Code - já conectado (isLoged=true)');
        return;
      }

      // 🔧 NOVO: IGNORAR QR CODES SE USUÁRIO NÃO SOLICITOU
      if (!userRequestedQrCode) {
        console.log('🚫 [SOCKET] Ignorando QR Code - usuário não solicitou geração');
        return;
      }

      if (data.qrcode && data.instanceId) {
        // 🔧 MELHOR VALIDAÇÃO DO QR CODE RECEBIDO VIA SOCKET
        const qrData = data.qrcode;
        let validQrCode = null;

        if (typeof qrData === 'string' && qrData.startsWith('data:image/')) {
          validQrCode = qrData;
        } else if (qrData.qr && typeof qrData.qr === 'string' && qrData.qr.startsWith('data:image/')) {
          validQrCode = qrData.qr;
        } else if (qrData.base64 && typeof qrData.base64 === 'string' && qrData.base64.startsWith('data:image/')) {
          validQrCode = qrData.base64;
        }

        if (validQrCode) {
          console.log('✅ [SOCKET] QR Code válido recebido via socket');
          setQrCodeImg(validQrCode);
          setGeneratingQrCode(false);
          
          // Usar TTL do backend se disponível, senão usar 45 segundos
          let ttl = 45;
          if (qrData.ttl) {
            ttl = Math.max(1, qrData.ttl - Math.floor(Date.now() / 1000));
          }
          
          // Reiniciar timer com TTL correto
          startQrCodeTimer(ttl);
          
          // Mostrar toast se foi renovado
          if (data.renewed) {
            console.log('🔄 QR Code renovado automaticamente via socket');
          }
        } else {
          console.error('❌ [SOCKET] QR Code inválido recebido via socket:', JSON.stringify(qrData, null, 2));
        }
      }
    });

    // 📱 LISTENER MELHORADO PARA CONNECTION UPDATES VIA SOCKET - COM FILTRO POR EMPRESA
    mainSocket.on('whatsapp_connection_update', (data) => {
      console.log('🔗 [SOCKET] Status de conexão atualizado:', {
        instanceId: data.instanceId,
        empresaID: data.empresaID,
        state: data.state,
        isConnected: data.isConnected,
        statusCode: data.statusCode,
        timestamp: data.timestamp
      });

      // **🎯 FILTRO CRÍTICO: IGNORAR CONNECTION UPDATES DE OUTRAS EMPRESAS**
      if (data.empresaID && data.empresaID !== empresaID) {
        console.log(`🚫 [SOCKET] Connection update ignorado - empresa diferente! Recebido: ${data.empresaID}, Atual: ${empresaID}`);
        return;
      }

      if (data.isConnected || data.state === 'open') {
        console.log('✅ [SOCKET] WhatsApp conectado via connection update!');
        setIsLoged(true);
        setGeneratingQrCode(false);
        setQrCodeImg('');
        clearQrCodeTimers();
      }
    });

    // 📱 NOVO LISTENER PARA EVENTO DE CONEXÃO ESTABELECIDA - COM FILTRO POR EMPRESA
    mainSocket.on('whatsapp_connected', (data) => {
      console.log('🎉 [SOCKET] WhatsApp conectado com sucesso!', {
        instanceId: data.instanceId,
        empresaID: data.empresaID,
        message: data.message,
        timestamp: data.timestamp
      });

      // **🎯 FILTRO CRÍTICO: IGNORAR CONEXÕES DE OUTRAS EMPRESAS**
      if (data.empresaID && data.empresaID !== empresaID) {
        console.log(`🚫 [SOCKET] Conexão ignorada - empresa diferente! Recebido: ${data.empresaID}, Atual: ${empresaID}`);
        return;
      }

      // Atualizar interface imediatamente
      setIsLoged(true);
      setGeneratingQrCode(false);
      setQrCodeImg('');
      clearQrCodeTimers();

      // Mostrar toast de sucesso
      /*toast.success('🎉 WhatsApp conectado com sucesso!', {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });*/

      // Recarregar chats apenas se não foi o carregamento inicial
      setTimeout(() => {
        console.log('🔄 Verificando se precisa recarregar chats após conexão...');
        if (!isInitialLoad && chats.length > 0) {
          console.log('♻️ Recarregando chats após reconexão...');
          setChats([]);
          setPage(0);
          setHasMoreChats(true);
          fetchChats(0, false, 'socket_reconnection');
        } else {
          console.log('✅ Carregamento inicial ainda em andamento, não duplicando...');
        }
      }, 1000);
    });

    return () => {
      console.log("🛑 Limpando eventos específicos do componente WhatsApp...");
      // ✅ LIMPEZA ESPECÍFICA - não afetar outros componentes que usam o socket
      if (mainSocket) {
        mainSocket.off('chats');
        mainSocket.off('connect');
        mainSocket.off('disconnect');
        mainSocket.off('connect_error');
        mainSocket.off('reconnect');
        mainSocket.off('whatsapp_qrcode_update');
        mainSocket.off('whatsapp_connection_update');
        mainSocket.off('whatsapp_connected'); // 📱 NOVO EVENTO

        console.log("✅ Eventos específicos do WhatsApp removidos - socket mantido para outras telas");
      }
    };
  }, [mainSocket]); // Remover dependências desnecessárias que causam re-execução

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  });

  useEffect(() => {
    // Defina isMounted como true ao montar o componente
    isMountedRef.current = true;
    cancelQrCodeFetchRef.current = false; // Redefine o cancelamento ao montar o componente

    // **📌 VERIFICAR STATUS REAL DA EVOLUTION API PRIMEIRO**
    console.log('🚀 Iniciando verificação de status REAL da Evolution API...');

    // Função assíncrona para verificar status e decidir o que fazer
    const initializeWhatsApp = async () => {
      try {
        // Primeiro verificar se realmente está conectado
        const isConnected = await checkConnectionStatus('component_mount');

        if (!isConnected) {
          // Se não estiver conectado, NÃO gerar QR code automaticamente
          console.log('📱 Não conectado, aguardando comando manual do usuário para gerar QR Code...');
          // 🔧 REMOVIDO: await fetchQrCode('component_mount_disconnected');
        } else {
          console.log('✅ Já conectado, mantendo interface de chat');
        }
      } catch (error) {
        console.error('❌ Erro na inicialização do WhatsApp:', error);
        // Em caso de erro, NÃO gerar QR code automaticamente
        console.log('⚠️ Erro na verificação, aguardando comando manual do usuário...');
        // 🔧 REMOVIDO: await fetchQrCode('component_mount_error');
      }
    };

    initializeWhatsApp();

    // Função de limpeza ao desmontar o componente
    return () => {
      isMountedRef.current = false; // Define como false quando o componente desmonta
      cancelQrCodeFetchRef.current = true; // Cancela qualquer execução futura de fetchQrCode
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        console.log('🛑 Timeout cleared on unmount');
      }
      // **📌 LIMPAR TIMER DE CONVERSA RECÉM ABERTA**
      if (justOpenedTimerRef.current) {
        clearTimeout(justOpenedTimerRef.current);
        console.log('🛑 justOpenedTimer cleared on unmount');
      }
      // 📱 Limpar timers do QR Code
      clearQrCodeTimers();
      console.log('🛑 QR Code timers cleared on unmount');
    };
  }, []);

  // **📌 FUNÇÃO PARA VERIFICAR STATUS REAL DA EVOLUTION API**
  const checkConnectionStatus = async (source = 'unknown') => {
    if (!isMountedRef.current) return false;

    try {
      console.log(`🔍 [CHECK STATUS] Verificando status REAL da Evolution API... (fonte: ${source})`);
      const response = await getWhatsappConnectionStatus(empresaID);

      console.log('📡 [CHECK STATUS] Resposta da Evolution API:', response.data);

      const isConnected = response.data.isConnected;
      const connectionState = response.data.connectionState;

      // ✅ SEMPRE USAR STATUS REAL DA EVOLUTION API (NUNCA DO BANCO)
      if (isConnected && connectionState === 'open') {
        console.log('✅ [CHECK STATUS] WhatsApp REALMENTE conectado na Evolution API!');
        setIsLoged(true);
        setGeneratingQrCode(false);
        setQrCodeImg('');
        clearQrCodeTimers(); // Limpar timers se conectado
        return true; // Conectado
      } else {
        console.log(`📱 [CHECK STATUS] WhatsApp REALMENTE desconectado na Evolution API (${connectionState})`);
        setIsLoged(false);
        return false; // Desconectado
      }
    } catch (error) {
      console.error('❌ [CHECK STATUS] Erro ao verificar status na Evolution API:', error);
      console.log('📱 [CHECK STATUS] Assumindo desconectado devido ao erro');
      setIsLoged(false);
      return false; // Em caso de erro, assumir desconectado
    }
  };

  const fetchQrCode = async (source = 'unknown') => {
    console.log(`🔍 [FETCH QR CODE] Iniciado por: ${source}`);

    if (!isMountedRef.current) {
      console.log('🚫 [FETCH QR CODE] Componente não montado, cancelando');
      return;
    }

    // **📌 PRIMEIRO VERIFICAR STATUS DE CONEXÃO**
    console.log('🔍 [FETCH QR CODE] Verificando status de conexão...');
    const isConnected = await checkConnectionStatus(`fetchQrCode_${source}`);
    if (isConnected) {
      console.log('✅ [FETCH QR CODE] Já conectado, cancelando geração de QR Code');
      clearQrCodeTimers(); // Limpar timers se conectado
      return; // Se conectado, não precisa gerar QR Code
    }

    // **📌 SE DESCONECTADO, GERAR QR CODE**
    console.log(`🔄 [FETCH QR CODE] Desconectado, gerando QR Code... (fonte: ${source})`);
    setGeneratingQrCode(true);

    getQrCodeWhatsapp(empresaID).then((response) => {
      console.log('📡 QR Code Response completa:', response.data);

      if (!isMountedRef.current) return;

      // 🔧 TRATAMENTO MELHORADO DA RESPOSTA DO QR CODE (ALINHADO COM BACKEND)
      console.log('📊 Resposta completa da API:', response.data);

      // Verificar se já está conectado
      if (response.data.isConnected) {
        console.log('✅ WhatsApp já está conectado segundo a API');
        setIsLoged(true);
        setGeneratingQrCode(false);
        setQrCodeImg('');
        clearQrCodeTimers();
        return;
      }

      let qrCodeData = null;
      let ttl = 45; // TTL padrão

      // Verificar diferentes formatos de resposta da Evolution API
      if (response.data.qrcode) {
        const qrData = response.data.qrcode;
        
        if (qrData.qr) {
          // Formato: { qrcode: { qr: "data:image/png;base64,..." } }
          qrCodeData = qrData.qr;
          console.log('📱 QR Code encontrado em qrcode.qr');
        } else if (qrData.base64) {
          // Formato: { qrcode: { base64: "data:image/png;base64,..." } }
          qrCodeData = qrData.base64;
          console.log('📱 QR Code encontrado em qrcode.base64');
        } else if (typeof qrData === 'string') {
          // Formato: { qrcode: "data:image/png;base64,..." }
          qrCodeData = qrData;
          console.log('📱 QR Code encontrado como string direta');
        }

        // Extrair TTL se disponível
        if (qrData.ttl) {
          ttl = Math.max(1, qrData.ttl - Math.floor(Date.now() / 1000));
          console.log(`⏱️ TTL extraído do backend: ${ttl} segundos`);
        }
      }

      if (qrCodeData) {
        // Validar se é uma imagem base64 válida
        if (qrCodeData.startsWith('data:image/')) {
          setQrCodeImg(qrCodeData);
          console.log('✅ QR Code definido com sucesso - Tamanho:', qrCodeData.length);
          setGeneratingQrCode(false);

          // 📱 INICIAR TIMER COM TTL DO BACKEND
          startQrCodeTimer(ttl);
        } else {
          console.error('❌ QR Code não é uma imagem base64 válida:', qrCodeData.substring(0, 100));
          setGeneratingQrCode(false);
        }
      } else {
        console.error('❌ QR Code não encontrado na resposta:', response.data);
        setGeneratingQrCode(false);
      }

      // ❌ REMOVIDO: Lógica antiga de regeneração automática
      // Agora usamos timer fixo de 20 segundos com botão manual de regeneração

      // ❌ REMOVIDO: Verificação periódica antiga - agora usamos socket listeners e timer fixo de 20s

    }).catch((error) => {
      console.error('Erro ao buscar QR Code:', error);
      setGeneratingQrCode(false);

      // Em caso de erro, tenta novamente após 10 segundos
      if (isMountedRef.current && !cancelQrCodeFetchRef.current) {
        console.log('🔄 [FETCH QR CODE] Erro - tentando novamente em 10 segundos...');
        timeoutRef.current = setTimeout(() => {
          if (isMountedRef.current && !cancelQrCodeFetchRef.current) {
            console.log('🔄 [FETCH QR CODE] Retry após erro');
            fetchQrCode('error_retry');
          }
        }, 10000);
      }
    }).finally(() => {
      if (isMountedRef.current) {
        setGeneratingQrCode(false);
      }
    });
  };

  useEffect(() => {
    if (selectedChat?._id) {
      getBotStatusLead(selectedChat._id)
        .then(response => {
          console.log("Status desse bot:", response.data.bot_pausado)
          setBotPausado(response.data.bot_pausado);
        })
        .catch(error => {
          console.error("Erro ao obter status do bot:", error);
        });

      // Buscar imagem de perfil quando um chat é selecionado
      fetchProfilePicture(selectedChat);
    }
  }, [selectedChat]);

  // **📌 FUNÇÃO PARA BUSCAR IMAGEM DE PERFIL (OTIMIZADA)**
  const fetchProfilePicture = async (chat, forceUpdate = false, priority = 'normal') => {
    // **📌 CACHE MAIS INTELIGENTE - 7 DIAS AO INVÉS DE 24H**
    const cacheValidityDays = 7;
    const hasRecentProfilePicture = chat.channel_data?.whatsapp?.profile_picture_updateAt &&
      new Date() - new Date(chat.channel_data.whatsapp.profile_picture_updateAt) < cacheValidityDays * 24 * 60 * 60 * 1000;

    // **📌 VERIFICAÇÕES PARA EVITAR REQUISIÇÕES DESNECESSÁRIAS**
    if (!forceUpdate && chat.channel_data?.whatsapp?.profile_picture && hasRecentProfilePicture) {

      return;
    }

    // **📌 EVITAR MÚLTIPLAS REQUISIÇÕES SIMULTÂNEAS PARA O MESMO CHAT**
    const requestKey = `profile_picture_${chat._id}`;
    if (window.profilePictureRequests && window.profilePictureRequests[requestKey]) {

      return;
    }

    // Marcar requisição como em andamento
    if (!window.profilePictureRequests) window.profilePictureRequests = {};
    window.profilePictureRequests[requestKey] = true;

    try {
      const response = await getWhatsappProfilePicture(chat._id, token);

      if (response.data && response.data.status === 200 && response.data.profilePictureUrl) {

        // Atualizar o chat na lista local
        setChats(prevChats =>
          prevChats.map(c =>
            c._id === chat._id
              ? {
                ...c,
                channel_data: {
                  ...c.channel_data,
                  whatsapp: {
                    ...c.channel_data?.whatsapp,
                    profile_picture: response.data.profilePictureUrl,
                    profile_picture_updateAt: new Date().toISOString()
                  }
                }
              }
              : c
          )
        );

        // Se é o chat selecionado, atualizar também
        if (selectedChat && selectedChat._id === chat._id) {
          setSelectedChat(prev => ({
            ...prev,
            channel_data: {
              ...prev.channel_data,
              whatsapp: {
                ...prev.channel_data?.whatsapp,
                profile_picture: response.data.profilePictureUrl,
                profile_picture_updateAt: new Date().toISOString()
              }
            }
          }));
        }
      }
    } catch (error) {
      console.error(`❌ Erro ao buscar imagem de perfil para ${chat.name}:`, error);
    } finally {
      // Remover marcação de requisição em andamento
      if (window.profilePictureRequests) {
        delete window.profilePictureRequests[requestKey];
      }
    }
  };

  // **📌 FUNÇÃO PARA BUSCAR IMAGENS EM LOTE DE FORMA INTELIGENTE**
  const fetchProfilePicturesBatch = (chats, batchName = 'batch') => {
    const chatsNeedingImages = chats.filter(chat => {
      const hasImage = chat.channel_data?.whatsapp?.profile_picture;
      const cacheValidityDays = 7;
      const hasRecentUpdate = chat.channel_data?.whatsapp?.profile_picture_updateAt &&
        new Date() - new Date(chat.channel_data.whatsapp.profile_picture_updateAt) < cacheValidityDays * 24 * 60 * 60 * 1000;

      return !hasImage || !hasRecentUpdate;
    });

    if (chatsNeedingImages.length === 0) {
      return;
    }

    // **📌 PRIORIZAR PRIMEIROS 5 CHATS (MAIS VISÍVEIS)**
    const priorityChats = chatsNeedingImages.slice(0, 5);
    const regularChats = chatsNeedingImages.slice(5);

    // Buscar imagens prioritárias primeiro (delay menor)
    priorityChats.forEach((chat, index) => {
      setTimeout(() => {
        fetchProfilePicture(chat, false, 'high');
      }, index * 300); // 300ms entre cada (mais rápido)
    });

    // Buscar imagens regulares depois (delay maior)
    regularChats.forEach((chat, index) => {
      setTimeout(() => {
        fetchProfilePicture(chat, false, 'normal');
      }, (priorityChats.length * 300) + (index * 800)); // 800ms entre cada (mais devagar)
    });
  };

  // **📌 FUNÇÃO PARA BUSCAR CONTAGEM DE MENSAGENS NÃO LIDAS**
  const fetchUnreadCounts = async () => {
    try {
      const response = await getWhatsappUnreadCount(empresaID, token);
      if (response.data && response.data.status === 200) {
        setUnreadCounts(response.data.unreadCounts);
        //console.log(`📊 Contagens de mensagens não lidas atualizadas:`, response.data.unreadCounts);
      }
    } catch (error) {
      console.error('❌ Erro ao buscar contagem de mensagens não lidas:', error);
    }
  };



  // **📌 FUNÇÃO PARA DETECTAR CONTATOS COM NOMES GENÉRICOS**
  const hasGenericName = (contactName, mobileNumber) => {
    if (!contactName || !mobileNumber) return false;
    
    // Verificar se o nome é genérico
    const cleanName = contactName.trim();
    const cleanNumber = mobileNumber.toString();
    
    const genericPatterns = [
      cleanName === cleanNumber, // Nome igual ao número
      cleanName.startsWith('Contato '), // Nome começa com "Contato "
      /^\d{10,}$/.test(cleanName), // Nome é só números (10+ dígitos)
      cleanName === `Contato ${cleanNumber}`, // Formato exato "Contato 556299999"
      cleanName.length < 3, // Nomes muito curtos provavelmente são genéricos
      /^[+\-\(\)\s\d]+$/.test(cleanName) // Nome contém apenas números e símbolos telefônicos
    ];
    
    return genericPatterns.some(pattern => pattern);
  };



  // **📌 FUNÇÃO PARA ATUALIZAR INFORMAÇÕES DE UM CONTATO ESPECÍFICO**
  const refreshContactInfo = async (chatId, chatName) => {
    try {
      console.log(`🔄 Atualizando informações do contato ${chatName}...`);
      const response = await refreshContactInfoWhatsapp(chatId, token);
      
      if (response.data && response.data.status === 200) {
        console.log(`✅ Informações atualizadas:`, response.data);

        // Atualizar o chat na lista local se houve mudança
        if (response.data.contactName !== response.data.oldName || response.data.profilePictureUpdated) {
          setChats(prevChats =>
            prevChats.map(c =>
              c._id === chatId
                ? {
                  ...c,
                  name: response.data.contactName,
                  channel_data: {
                    ...c.channel_data,
                    whatsapp: {
                      ...c.channel_data?.whatsapp,
                      profile_picture: response.data.profilePictureUrl || c.channel_data?.whatsapp?.profile_picture,
                      profile_picture_updateAt: response.data.profilePictureUpdated ? new Date().toISOString() : c.channel_data?.whatsapp?.profile_picture_updateAt,
                      pushName: response.data.contactName
                    }
                  }
                }
                : c
            )
          );

          // Se é o chat selecionado, atualizar também
          if (selectedChat && selectedChat._id === chatId) {
            setSelectedChat(prev => ({
              ...prev,
              name: response.data.contactName,
              channel_data: {
                ...prev.channel_data,
                whatsapp: {
                  ...prev.channel_data?.whatsapp,
                  profile_picture: response.data.profilePictureUrl || prev.channel_data?.whatsapp?.profile_picture,
                  profile_picture_updateAt: response.data.profilePictureUpdated ? new Date().toISOString() : prev.channel_data?.whatsapp?.profile_picture_updateAt,
                  pushName: response.data.contactName
                }
              }
            }));
          }
        }

        // Mostrar toast de sucesso
        toast.success(response.data.message, {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
    } catch (error) {
      console.error(`❌ Erro ao atualizar informações do contato ${chatName}:`, error);
      toast.error('Erro ao atualizar informações do contato', {
        position: "top-right",
        autoClose: 3000,
      });
    }
  };

  // **📌 FUNÇÃO PARA BUSCAR MINHA IMAGEM DE PERFIL (PRÓPRIO USUÁRIO)**
  const fetchMyProfilePicture = async (forceUpdate = false) => {
    // **📌 VERIFICAR CACHE LOCAL PRIMEIRO**
    const cachedImage = localStorage.getItem(`my_profile_picture_${empresaID}`);
    const cachedTimestamp = localStorage.getItem(`my_profile_picture_timestamp_${empresaID}`);

    if (!forceUpdate && cachedImage && cachedTimestamp) {
      const cacheAge = new Date() - new Date(cachedTimestamp);
      const cacheValidityDays = 7;

      if (cacheAge < cacheValidityDays * 24 * 60 * 60 * 1000) {
        console.log(`✅ Minha imagem de perfil já existe no cache local (válida por ${cacheValidityDays} dias)`);
        setMyProfilePicture(cachedImage);
        return;
      }
    }

    try {
      const response = await getMyWhatsappProfilePicture(empresaID, token);

      if (response.data && response.data.status === 200 && response.data.profilePictureUrl) {

        // Salvar no estado e no cache local
        setMyProfilePicture(response.data.profilePictureUrl);
        localStorage.setItem(`my_profile_picture_${empresaID}`, response.data.profilePictureUrl);
        localStorage.setItem(`my_profile_picture_timestamp_${empresaID}`, new Date().toISOString());

      } else {
        setMyProfilePicture(null);
        // Limpar cache se não há imagem
        localStorage.removeItem(`my_profile_picture_${empresaID}`);
        localStorage.removeItem(`my_profile_picture_timestamp_${empresaID}`);
      }
    } catch (error) {
      console.error(`❌ Erro ao buscar minha imagem de perfil:`, error);
      
      // **🔧 RECUPERAÇÃO AUTOMÁTICA: Se erro for por falta de JID, tentar buscar JID automaticamente**
      if (error.response?.data?.error?.includes?.('JID do WhatsApp não encontrado')) {
        console.log(`🔄 JID não encontrado. Tentando buscar e salvar JID automaticamente...`);
        
        try {
          const { fetchWhatsappJID } = await import('../../services/api');
          const jidResponse = await fetchWhatsappJID(empresaID, token);
          
          if (jidResponse.data?.status === 200) {
            console.log(`✅ JID recuperado com sucesso: ${jidResponse.data.jid}`);
            console.log(`🔄 Tentando buscar foto de perfil novamente...`);
            
            // Tentar buscar a foto de perfil novamente agora que o JID foi salvo
            const retryResponse = await getMyWhatsappProfilePicture(empresaID, token);
            
            if (retryResponse.data?.status === 200 && retryResponse.data.profilePictureUrl) {
              setMyProfilePicture(retryResponse.data.profilePictureUrl);
              localStorage.setItem(`my_profile_picture_${empresaID}`, retryResponse.data.profilePictureUrl);
              localStorage.setItem(`my_profile_picture_timestamp_${empresaID}`, new Date().toISOString());
              console.log(`🎉 Foto de perfil carregada com sucesso após recuperar JID!`);
              return;
            }
          } else {
            console.log(`⚠️ Não foi possível recuperar o JID:`, jidResponse.data);
          }
        } catch (jidError) {
          console.error(`❌ Erro ao tentar recuperar JID:`, jidError);
        }
      }
      
      setMyProfilePicture(null);
    }
  };

  // **📌 FUNÇÃO PARA CARREGAR MAIS MENSAGENS (PAGINAÇÃO)**
  const loadMoreMessages = async () => {
    if (!selectedChat || !hasMoreMessages || loadingMoreMessages) {
      return;
    }

    setLoadingMoreMessages(true);
    setShouldScrollToBottom(false); // **📌 NÃO ROLAR PARA O BOTTOM DURANTE PAGINAÇÃO**

    try {
      const nextPage = messagesPage + 1;
      const pageSize = 30;

      const response = await getWhatsappChatLead(empresaID, selectedChat._id, nextPage, pageSize, token);

      if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {
        const newMessages = response.data.messages[0].data;
        const totalCount = response.data.messages[0].metadata[0]?.totalCount || 0;

        if (newMessages.length === 0) {
          setHasMoreMessages(false);
        } else {
          // Adicionar novas mensagens ao início da lista (mensagens mais antigas)
          setMessagesLeadChannel(prevMessages => {
            // Evitar duplicatas
            const existingIds = new Set(prevMessages.map(msg => msg._id));
            const uniqueNewMessages = newMessages.filter(msg => !existingIds.has(msg._id));

            // Adicionar mensagens mais antigas no início
            return [...uniqueNewMessages, ...prevMessages];
          });

          // Atualizar cache
          const cacheKey = selectedChat._id;
          setMessagesCache(prevCache => {
            const newCache = new Map(prevCache);
            const existingMessages = newCache.get(cacheKey) || [];

            const existingIds = new Set(existingMessages.map(msg => msg._id));
            const uniqueNewMessages = newMessages.filter(msg => !existingIds.has(msg._id));

            const finalCacheMessages = [...uniqueNewMessages, ...existingMessages];

            newCache.set(cacheKey, finalCacheMessages);
            return newCache;
          });

          setMessagesPage(nextPage);

          // **📌 VERIFICAR SE AINDA HÁ MAIS MENSAGENS USANDO CALLBACK**
          setMessagesLeadChannel(currentMessages => {
            const currentTotal = currentMessages.length;

            if (currentTotal >= totalCount) {
              setHasMoreMessages(false);
            }

            return currentMessages; // Não modificar, apenas verificar
          });
        }
      } else {
        setHasMoreMessages(false);
        console.log(`⚠️ Resposta inválida ao carregar mais mensagens para ${selectedChat.name}`);
      }
    } catch (error) {
      console.error(`❌ Erro ao carregar mais mensagens para ${selectedChat.name}:`, error);
    } finally {
      setLoadingMoreMessages(false);
      setShouldScrollToBottom(true); // **📌 REATIVAR SCROLL PARA O BOTTOM APÓS PAGINAÇÃO**
    }
  };

  // **📌 HANDLER PARA SCROLL DAS MENSAGENS (CARREGAR MAIS AO ROLAR PARA CIMA)**
  const handleMessagesScroll = (e) => {
    const container = e.target;
    const scrollTop = container.scrollTop;
    const scrollThreshold = 100; // Pixels do topo para disparar carregamento

    // **📌 NÃO CARREGAR MENSAGENS SE ACABOU DE ABRIR A CONVERSA**
    if (justOpenedChat) {
      console.log(`🚫 Conversa acabou de abrir, ignorando scroll no topo para ${selectedChat?.name}`);
      return;
    }

    // **📌 IGNORAR SCROLL AUTOMÁTICO DURANTE CARREGAMENTO**
    if (loadingMessages) {
      return;
    }

    // Se rolou próximo ao topo e há mais mensagens para carregar
    if (scrollTop <= scrollThreshold && hasMoreMessages && !loadingMoreMessages) {
      console.log(`📜 Scroll detectado no topo - carregando mais mensagens para ${selectedChat?.name}`);

      // **📌 SALVAR POSIÇÃO ATUAL DO SCROLL PARA RESTAURAR APÓS CARREGAR**
      const currentScrollHeight = container.scrollHeight;
      const currentScrollTop = scrollTop;

      loadMoreMessages().then(() => {
        // **📌 RESTAURAR POSIÇÃO DO SCROLL DE FORMA MAIS ROBUSTA**
        const restoreScroll = () => {
          const newScrollHeight = container.scrollHeight;
          const scrollDifference = newScrollHeight - currentScrollHeight;
          const newScrollTop = currentScrollTop + scrollDifference;

          // **📌 APLICAR NOVA POSIÇÃO IMEDIATAMENTE**
          container.scrollTop = newScrollTop;

          // **📌 VERIFICAR SE A POSIÇÃO FOI APLICADA CORRETAMENTE**
          requestAnimationFrame(() => {
            if (Math.abs(container.scrollTop - newScrollTop) > 5) {
              container.scrollTop = newScrollTop;
            }
          });
        };

        // **📌 TENTAR RESTAURAR IMEDIATAMENTE E DEPOIS COM DELAY**
        restoreScroll();
        setTimeout(restoreScroll, 50); // Backup caso o primeiro não funcione
      });
    }
  };

  /*useEffect(() => {
    const fetchChats = async () => {
      try {
        const page = 0;
        const pageSize = 30;
        const query = searchQuery; // Adicione seu termo de busca aqui, se houver

        //console.log("O que está sendo enviado:",{empresaID, page, pageSize, query, token})
        const response = await getWhatsappChats(empresaID, page, pageSize, query, token);
        //console.log("O que está sendo Recebido!:",response)
        if (query === '' || query.length >= 3) {
          setChats(response.data.messages[0].data);
        }
        if (response.data.messages[0].data.length > 0 && query !== '') {
          const data = response.data.messages[0].data;
          if (data.length > 0 && query.length >= 3) {
            //console.log("Oq tem aqui?", data);
            setChats([data[0]]); // Armazena apenas o primeiro elemento como array
          }
        }
        //console.log(response.data.messages[0].data);
        setLoading(false);
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    };

    // Chame fetchChats inicialmente
    fetchChats();

  }, [searchQuery]);*/
  const [page, setPage] = useState(0); // Página inicial
  const [hasMoreChats, setHasMoreChats] = useState(true); // Se ainda há chats para carregar
  const chatsContainerRef = useRef(null); // Referência ao container de chats
  const [loadingMore, setLoadingMore] = useState(false); // Para evitar requisições duplicadas

  const fetchChats = async (newPage, append = false, source = 'unknown') => {
    console.log("🎯 fetchChats INICIADO - newPage:", newPage, "append:", append, "source:", source);
    console.log("📋 Parâmetros - empresaID:", empresaID, "token:", token ? "presente" : "ausente");

    // 🛡️ EVITAR EXECUÇÕES DUPLICADAS
    if (fetchingChatsRef.current && !append) {
      console.log("🚫 fetchChats já em execução, ignorando chamada duplicada de:", source);
      return;
    }

    if (!append) {
      fetchingChatsRef.current = true;
    }

    try {
      const pageSize = 30;
      const query = searchQuery;

      console.log("🔄 Carregando página:", newPage);
      console.log("🔍 Query de busca:", query);

      const response = await getWhatsappChats(empresaID, newPage, pageSize, query, token);

      const newChats = response.data.messages[0].data;

      if (newChats.length === 0) {
        setHasMoreChats(false); // Se não há mais chats, parar carregamento
      }

      setChats((prevChats) => {
        let finalChats;
        if (append) {
          // Remove duplicatas ao adicionar novos chats
          const existingIds = new Set(prevChats.map(chat => chat._id));
          const uniqueNewChats = newChats.filter(chat => !existingIds.has(chat._id));
          finalChats = [...prevChats, ...uniqueNewChats];
        } else {
          // Remove duplicatas nos chats iniciais
          const uniqueChats = newChats.filter((chat, index, self) =>
            index === self.findIndex(c => c._id === chat._id)
          );
          finalChats = uniqueChats;

          // **📌 INICIAR PRÉ-CARREGAMENTO APENAS NO CARREGAMENTO INICIAL**
          if (uniqueChats.length > 0 && !preloadingChats && !hasPreloaded && searchQuery === '' && isInitialLoad) {
            console.log("🚀 Iniciando pré-carregamento único...");
            setHasPreloaded(true);
            setIsInitialLoad(false);
            setTimeout(() => {
              preloadAllChats(uniqueChats);
            }, 1000); // Delay de 1 segundo para não interferir com a UI
          }
        }
        // **📌 BUSCAR IMAGENS DE PERFIL SIMULTANEAMENTE PARA TODOS OS CHATS CARREGADOS**
        if (!append) {
          // Para carregamento inicial, usar função de lote inteligente
          setTimeout(() => {
            fetchProfilePicturesBatch(finalChats, 'carregamento inicial');
          }, 800); // Delay menor para começar mais cedo
        }

        // **📌 BUSCAR IMAGENS TAMBÉM PARA CHATS CARREGADOS VIA PAGINAÇÃO**
        if (append && finalChats.length > 0) {
          setTimeout(() => {
            fetchProfilePicturesBatch(finalChats, 'paginação');
          }, 300); // Delay menor para paginação
        }

        // **📌 BUSCAR CONTAGEM DE MENSAGENS NÃO LIDAS APÓS CARREGAR CHATS**
        if (!append && finalChats.length > 0) {
          setTimeout(() => {
            fetchUnreadCounts();
          }, 500); // Delay para não sobrecarregar
        }

        return finalChats;
      });
      setLoading(false);
      setLoadingMore(false);
    } catch (err) {
      console.error("❌ Erro ao carregar chats:", err);
      console.error("📊 Detalhes do erro:", {
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
        config: err.config
      });
      setLoading(false);
      setLoadingMore(false);
    } finally {
      // 🛡️ SEMPRE RESETAR FLAG DE FETCHING
      if (!append) {
        fetchingChatsRef.current = false;
      }
    }
  };



  // **📌 Detectar scroll e carregar mais chats**
  const handleScroll = () => {
    if (!hasMoreChats || loadingMore) return;

    const container = chatsContainerRef.current;
    if (!container) return;

    const bottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 10;
    if (bottom) {
      console.log("📢 Rolou até o fim, carregando mais chats...");
      setLoadingMore(true);
      setPage((prevPage) => {
        const nextPage = prevPage + 1;
        fetchChats(nextPage, true, 'pagination');
        return nextPage;
      });
    }
  };






  const formatDate = (date) => {
    const now = moment().tz("America/Sao_Paulo");
    const inputDate = moment(date).tz("America/Sao_Paulo");

    if (now.isSame(inputDate, 'day')) {
      return inputDate.format('HH:mm');
    } else if (now.subtract(1, 'days').isSame(inputDate, 'day')) {
      return 'Ontem';
    } else if (now.subtract(6, 'days').isBefore(inputDate)) {
      return inputDate.format('dddd');
    } else {
      return inputDate.format('DD/MM/YYYY');
    }
  };


  const handleDisconnect = async () => {
    try {
      const res = await removeWhatsappSession(empresaID); // Aguarda a resposta da API

      // Exibe um toast de sucesso com a mensagem da resposta da API
      toast(res.data.msg, {
        autoClose: 3000,
        type: "success"
      });

      // 🔧 REMOVIDO: geração automática de QR Code após desconexão
      // Agora o usuário deve clicar manualmente para gerar
      setUserRequestedQrCode(false); // Resetar flag para exibir botão de geração
      setQrCodeImg(''); // Limpar QR Code atual
      setIsLoged(false); // Marcar como desconectado
      // fetchQrCode(); // REMOVIDO
    } catch (error) {
      console.error("Erro ao encerrar sessão:", error.response?.data || error.message);

      // Exibe um toast de erro com a mensagem da API ou um fallback padrão
      toast(error.response?.data?.msg || "Erro ao encerrar sessão!", {
        autoClose: 3000,
        type: "error"
      });
    }
  };

  const handleToggleBot = async (lead_id) => {
    try {
      const response = await toggleBotStatus(lead_id);
      setBotPausado(response.data.bot_pausado);
      toast(response.data.msg, {
        autoClose: 3000,
        type: "success"
      });
    } catch (error) {
      console.error("Erro ao alternar o status do bot:", error);
    }
  };

  // **📌 FUNÇÃO PARA PRÉ-CARREGAR TODAS AS CONVERSAS**
  const preloadAllChats = async (chatList) => {
    if (!chatList || chatList.length === 0) return;


    setPreloadingChats(true);

    // Priorizar conversas com mensagens mais recentes
    const sortedChats = [...chatList].sort((a, b) => {
      const dateA = new Date(a.message?.messageDate || a.message?.createdAt || 0);
      const dateB = new Date(b.message?.messageDate || b.message?.createdAt || 0);
      return dateB - dateA;
    });

    // Limitar a 15 conversas mais recentes para não sobrecarregar
    const chatsToPreload = sortedChats.slice(0, 15);
    setPreloadProgress({ current: 0, total: chatsToPreload.length });

    const batchSize = 3; // Carregar 3 conversas por vez

    try {
      for (let i = 0; i < chatsToPreload.length; i += batchSize) {
        const batch = chatsToPreload.slice(i, i + batchSize);

        // Carregar batch em paralelo
        const batchPromises = batch.map(async (chat) => {
          try {
            // Verificar se já não está no cache
            if (messagesCache.has(chat._id)) {
              return;
            }
            const response = await getWhatsappChatLead(empresaID, chat._id, 0, 30, token);

            if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {
              const messages = response.data.messages[0].data;

              // Armazenar no cache
              setMessagesCache(prevCache => {
                const newCache = new Map(prevCache);
                newCache.set(chat._id, messages);
                return newCache;
              });

              //console.log(`✅ ${messages.length} mensagens pré-carregadas para ${chat.name}`);
            } else {
              // Armazenar array vazio para conversas sem mensagens
              setMessagesCache(prevCache => {
                const newCache = new Map(prevCache);
                newCache.set(chat._id, []);
                return newCache;
              });
              //console.log(`📭 Conversa vazia pré-carregada: ${chat.name}`);
            }
          } catch (error) {
            console.error(`❌ Erro ao pré-carregar ${chat.name}:`, error);
            // Armazenar array vazio em caso de erro
            setMessagesCache(prevCache => {
              const newCache = new Map(prevCache);
              newCache.set(chat._id, []);
              return newCache;
            });
          }
        });

        // Aguardar batch atual
        await Promise.all(batchPromises);

        // Atualizar progresso
        const completed = Math.min(i + batchSize, chatsToPreload.length);
        setPreloadProgress({ current: completed, total: chatsToPreload.length });

        // Delay dinâmico baseado na quantidade de conversas
        if (i + batchSize < chatsToPreload.length) {
          const delay = chatsToPreload.length > 10 ? 400 : 200; // Delay maior para listas maiores
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      console.log(`🎉 Pré-carregamento concluído! ${chatsToPreload.length} conversas armazenadas no cache.`);
      console.log(`💾 Cache atual contém ${messagesCache.size} conversas.`);

      // Feedback visual de sucesso
      /*setTimeout(() => {
        toast.success(`✅ ${chatsToPreload.length} conversas pré-carregadas com sucesso!`, {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }, 500);*/

    } catch (error) {
      console.error('❌ Erro no pré-carregamento:', error);
      toast.error('⚠️ Erro ao pré-carregar conversas', {
        position: "top-right",
        autoClose: 3000,
      });
    } finally {
      setPreloadingChats(false);
      setPreloadProgress({ current: 0, total: 0 });
    }
  };

  // **📌 Carregar os chats ao iniciar e ao buscar**
  useEffect(() => {
    console.log("🚀 useEffect disparado - searchQuery:", searchQuery);
    console.log("📊 Estado atual - empresaID:", empresaID, "token:", token ? "presente" : "ausente");

    setChats([]); // Resetar chats ao buscar
    setPage(0);
    setHasMoreChats(true);
    fetchingChatsRef.current = false; // Reset da flag de controle

    const source = searchQuery ? 'search' : 'initial_load';
    console.log("📞 Chamando fetchChats(0, false) -", source);
    fetchChats(0, false, source);
  }, [searchQuery]);

  // **📌 Resetar pré-carregamento quando empresa ou token mudar**
  useEffect(() => {
    setHasPreloaded(false);
    setMessagesCache(new Map());
    setPreloadingChats(false);
    setPreloadProgress({ current: 0, total: 0 });
    setRaceConditionsFixed(0);
    setIsInitialLoad(true); // Reset para nova empresa
    fetchingChatsRef.current = false; // Reset da flag de controle

    // **📌 BUSCAR MINHA IMAGEM DE PERFIL QUANDO EMPRESA/TOKEN MUDAR**
    if (empresaID && token) {
      fetchMyProfilePicture();
    }
  }, [empresaID, token]);

  // **📌 ATUALIZAÇÃO PERIÓDICA DAS CONTAGENS DE MENSAGENS NÃO LIDAS**
  useEffect(() => {
    if (!empresaID || !token) return;

    // Atualizar contagens a cada 30 segundos
    const interval = setInterval(() => {
      fetchUnreadCounts();
    }, 30000);

    return () => clearInterval(interval);
  }, [empresaID, token]);



  // **📌 MONITORAMENTO INTELIGENTE DE STATUS E AUTO-RENOVAÇÃO**
  useEffect(() => {
    if (!empresaID || !token || isLoged) return;

    let monitoringInterval = null;

    const startMonitoring = () => {
      // Verificar status a cada 10 segundos quando desconectado
      monitoringInterval = setInterval(async () => {
        if (isLoged || !isMountedRef.current) {
          clearInterval(monitoringInterval);
          return;
        }

        try {
          // Verificação silenciosa de status
          const isConnected = await checkConnectionStatus('monitoring');
          
          if (isConnected) {
            // Se conectou, limpar monitoramento
            clearInterval(monitoringInterval);
            console.log('📊 Monitoramento detectou conexão estabelecida');
          }
        } catch (error) {
          // Ignorar erros silenciosamente no monitoramento
          console.debug('Erro silencioso no monitoramento:', error.message);
        }
      }, 10000); // 10 segundos
    };

    // Iniciar monitoramento após 30 segundos (dar tempo para conexão inicial)
    const startTimer = setTimeout(() => {
      if (!isLoged && isMountedRef.current) {
        console.log('📊 Iniciando monitoramento de status inteligente');
        startMonitoring();
      }
    }, 30000);

    return () => {
      clearTimeout(startTimer);
      if (monitoringInterval) {
        clearInterval(monitoringInterval);
      }
    };
  }, [empresaID, token, isLoged]);

  // **📌 Mostrar estatísticas de race conditions evitadas**
  useEffect(() => {
    if (raceConditionsFixed > 0 && raceConditionsFixed % 10 === 0) {
      console.log(`🛡️ Race Conditions evitadas: ${raceConditionsFixed}`);
      toast.success(`🛡️ Sistema anti-bug funcionando! ${raceConditionsFixed} conflitos evitados`, {
        position: "top-right",
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    }
  }, [raceConditionsFixed]);

  // **📌 Adicionar listener de scroll**
  useEffect(() => {
    const container = chatsContainerRef.current;
    if (container) {
      container.addEventListener("scroll", handleScroll);
    }
    return () => {
      if (container) {
        container.removeEventListener("scroll", handleScroll);
      }
    };
  }, [hasMoreChats, loadingMore]);

  // **📌 GERENCIAR MENSAGENS DO CHAT SELECIONADO (USANDO SOCKET PRINCIPAL)**

  // **📌 GERENCIAR MENSAGENS DO CHAT SELECIONADO**
  useEffect(() => {
    if (!mainSocket || !selectedChat) {
      return;
    }

    if (selectedChat) {
      // **📌 CONTROLE DE REQUISIÇÕES ATIVAS PARA EVITAR RACE CONDITIONS**
      let isCurrentConversation = true;
      const currentChatId = selectedChat._id;
      const currentChatName = selectedChat.name;

      // **📌 LIMPAR MENSAGENS E INICIAR LOADING IMEDIATAMENTE AO TROCAR CONVERSA**
      setMessagesLeadChannel([]);
      setLoadingMessages(true);
      setShouldScrollToBottom(true); // **📌 REATIVAR SCROLL PARA O BOTTOM EM NOVA CONVERSA**
      
      // **📌 MARCAR QUE ACABOU DE ABRIR UMA CONVERSA**
      setJustOpenedChat(true);
      if (justOpenedTimerRef.current) {
        clearTimeout(justOpenedTimerRef.current);
      }
      
      // **📌 LIMPAR FLAG APÓS 3 SEGUNDOS PARA PERMITIR CARREGAMENTO DE MENSAGENS ANTIGAS**
      justOpenedTimerRef.current = setTimeout(() => {
        setJustOpenedChat(false);
        console.log(`✅ Conversa ${currentChatName} liberada para carregamento de mensagens antigas após 3s`);
      }, 3000);

      mainSocket.emit('join-lead-messages', { leadID: selectedChat._id });

      const fetchChatLead = async () => {
        // **📌 PEQUENO DELAY PARA EVITAR RACE CONDITIONS EM TROCAS MUITO RÁPIDAS**
        await new Promise(resolve => setTimeout(resolve, 50));

        // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA APÓS DELAY**
        if (!isCurrentConversation) {
          //console.log(`🚫 Fetchamento cancelado após delay - conversa ${currentChatName} não é mais ativa`);
          setRaceConditionsFixed(prev => prev + 1);
          return;
        }

        try {

          // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA**
          if (!isCurrentConversation) {
            //console.log(`🚫 Requisição cancelada - conversa ${currentChatName} não é mais ativa`);
            return;
          }

          // **📌 VERIFICAR CACHE PRIMEIRO**
          const cachedMessages = messagesCache.get(currentChatId);
          if (cachedMessages && cachedMessages.length > 0) {

            // **📌 VERIFICAR NOVAMENTE SE AINDA É A CONVERSA ATIVA ANTES DE APLICAR**
            if (isCurrentConversation && selectedChat?._id === currentChatId) {
              setMessagesLeadChannel(cachedMessages);

              // **📌 RESETAR ESTADOS DE PAGINAÇÃO PARA CACHE**
              // Assumir que cache tem apenas primeira página, pode haver mais mensagens
              setMessagesPage(0);
              setHasMoreMessages(cachedMessages.length >= 30); // Se tem 30 ou mais, provavelmente há mais

              setLoadingMessages(false);
              
              // **📌 FORÇAR SCROLL PARA BOTTOM IMEDIATAMENTE APÓS CARREGAR CACHE**
              setTimeout(() => {
                const container = messagesContainerRef.current;
                if (container && cachedMessages.length > 0) {
                  container.scrollTop = container.scrollHeight;
                }
              }, 100);
            } else {
              //console.log(`🚫 Cache descartado - conversa ${currentChatName} não é mais ativa`);
              setRaceConditionsFixed(prev => prev + 1);
            }

            // **📌 OPCIONAL: Fazer requisição em background para atualizar cache**
            // Isso garante que sempre temos as mensagens mais recentes
            setTimeout(async () => {
              try {
                if (!isCurrentConversation) return; // Cancelar se não é mais ativa

                const page = 0;
                const pageSize = 30;
                const response = await getWhatsappChatLead(empresaID, currentChatId, page, pageSize, token);

                if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {
                  const freshMessages = response.data.messages[0].data;

                  // Atualizar cache com mensagens frescas
                  setMessagesCache(prevCache => {
                    const newCache = new Map(prevCache);
                    newCache.set(currentChatId, freshMessages);
                    return newCache;
                  });

                  // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA ANTES DE ATUALIZAR TELA**
                  if (isCurrentConversation && selectedChat?._id === currentChatId) {
                    setMessagesLeadChannel(freshMessages);
                  } else {
                    //console.log(`🚫 Mensagens frescas descartadas - conversa ${currentChatName} não é mais ativa`);
                  }
                }
              } catch (error) {
                if (!isCurrentConversation) {
                  //console.log(`⏹️ Requisição de background cancelada para ${currentChatName}`);
                } else {
                  //console.log('Erro ao atualizar cache em background:', error);
                }
              }
            }, 1000); // 1 segundo de delay

            return; // Usar cache, não faz requisição imediata
          }

          //console.log(`❌ Cache não encontrado para ${currentChatName} - fazendo requisição à API`);

          // **📌 INICIAR LOADING SE NÃO TIVER CACHE**
          if (isCurrentConversation) {
            //console.log(`⏳ Iniciando loading para conversa sem cache: ${currentChatName}`);
            setLoadingMessages(true);
          }

          const page = 0;
          const pageSize = 30;

          const response = await getWhatsappChatLead(empresaID, currentChatId, page, pageSize, token);

          // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA ANTES DE APLICAR RESULTADOS**
          if (!isCurrentConversation) {
            setRaceConditionsFixed(prev => prev + 1);
            return;
          }

          if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {
            const messages = response.data.messages[0].data;

            // **📌 VERIFICAR NOVAMENTE ANTES DE APLICAR**
            if (isCurrentConversation && selectedChat?._id === currentChatId) {
              setMessagesLeadChannel(messages);

              // **📌 RESETAR ESTADOS DE PAGINAÇÃO PARA NOVA CONVERSA**
              const totalCount = response.data.messages[0].metadata[0]?.totalCount || messages.length;
              setMessagesPage(0); // Primeira página carregada
              setHasMoreMessages(messages.length < totalCount); // Há mais mensagens se carregamos menos que o total

              // **📌 ARMAZENAR NO CACHE**
              setMessagesCache(prevCache => {
                const newCache = new Map(prevCache);
                newCache.set(currentChatId, messages);
                return newCache;
              });
              
              // **📌 FORÇAR SCROLL PARA BOTTOM IMEDIATAMENTE APÓS CARREGAR DA API**
              setTimeout(() => {
                const container = messagesContainerRef.current;
                if (container && messages.length > 0) {
                  container.scrollTop = container.scrollHeight;
                }
              }, 100);
            } else {
              //console.log(`🚫 Mensagens da API descartadas - conversa ${currentChatName} não é mais ativa`);
            }
          } else {

            // **📌 VERIFICAR ANTES DE APLICAR ARRAY VAZIO**
            if (isCurrentConversation && selectedChat?._id === currentChatId) {
              console.log(`📭 Aplicando array vazio para conversa ativa: ${currentChatName}`);
              setMessagesLeadChannel([]);

              // **📌 RESETAR ESTADOS DE PAGINAÇÃO PARA CONVERSA VAZIA**
              setMessagesPage(0);
              setHasMoreMessages(false);

              // **📌 ARMAZENAR ARRAY VAZIO NO CACHE**
              setMessagesCache(prevCache => {
                const newCache = new Map(prevCache);
                newCache.set(currentChatId, []);
                return newCache;
              });
            } else {
              console.log(`🚫 Array vazio descartado - conversa ${currentChatName} não é mais ativa`);
            }
          }
        } catch (err) {
          if (!isCurrentConversation) {
            console.log(`⏹️ Requisição cancelada para ${currentChatName}`);
          } else {
            console.error(`❌ Erro ao carregar mensagens para ${currentChatName}:`, err);

            // **📌 VERIFICAR ANTES DE APLICAR ARRAY VAZIO EM ERRO**
            if (isCurrentConversation && selectedChat?._id === currentChatId) {
              console.log(`💥 Aplicando array vazio por erro para conversa ativa: ${currentChatName}`);
              setMessagesLeadChannel([]);
            } else {
              console.log(`🚫 Array vazio por erro descartado - conversa ${currentChatName} não é mais ativa`);
            }
          }
        } finally {
          // **📌 FINALIZAR LOADING APENAS SE AINDA FOR A CONVERSA ATIVA**
          if (isCurrentConversation && selectedChat?._id === currentChatId) {
            setLoadingMessages(false);
          }
        }
      };

      // **📌 ENTRAR NA SALA DE MENSAGENS ESPECÍFICA DO LEAD**
      mainSocket.emit('join-lead-messages', { leadID: currentChatId });

      // **📌 ESCUTAR MENSAGENS DESTA CONVERSA ESPECÍFICA**
      mainSocket.on('messages', (newMessage) => {
        // **📌 VERIFICAR SE A MENSAGEM É DA CONVERSA ATIVA**
        if (!isCurrentConversation || selectedChat?._id !== currentChatId) {
          setRaceConditionsFixed(prev => prev + 1);
          return;
        }

        // **📌 PARAR LOADING SE ESTIVER ATIVO**
        setLoadingMessages(false);

        // Remover mensagem otimista se for a mesma
        setMessagesLeadChannel(oldMessages => {
          // Filtrar mensagens duplicadas e otimistas do mesmo texto
          const filteredMessages = oldMessages.filter(message => {
            // Remover duplicatas por ID
            if (message._id === newMessage._id) return false;
            // Remover mensagens otimistas se a mensagem real chegou
            if (message.isOptimistic && message.text === newMessage.text && message.fromMe === newMessage.fromMe) {
              return false;
            }
            return true;
          });
          return [newMessage, ...filteredMessages];
        });

        // **📌 ATUALIZAR CACHE COM NOVA MENSAGEM**
        setMessagesCache(prevCache => {
          const newCache = new Map(prevCache);
          const cachedMessages = newCache.get(currentChatId) || [];

          // Filtrar mensagens duplicadas e otimistas do cache
          const filteredCachedMessages = cachedMessages.filter(message => {
            if (message._id === newMessage._id) return false;
            if (message.isOptimistic && message.text === newMessage.text && message.fromMe === newMessage.fromMe) {
              return false;
            }
            return true;
          });

          newCache.set(currentChatId, [newMessage, ...filteredCachedMessages]);
          return newCache;
        });

        // **📌 MARCAR MENSAGEM COMO LIDA SE NÃO FOR MINHA E O CHAT ESTIVER ABERTO**
        if (!newMessage.fromMe) {
          console.log(`📖 Nova mensagem recebida no chat aberto, marcando como lida automaticamente`);
          setTimeout(() => {
            markChatAsRead(currentChatId);
          }, 1500); // Delay maior para garantir que a mensagem foi processada no backend
        }
        
        // **📌 GARANTIR QUE O SCROLL PERMANEÇA NO BOTTOM APÓS NOVA MENSAGEM**
        setTimeout(() => {
          const container = messagesContainerRef.current;
          if (container && shouldScrollToBottom) {
            container.scrollTop = container.scrollHeight;
          }
        }, 100);
      });

      fetchChatLead();

      // **📌 TIMEOUT PARA FORÇAR PARADA DO LOADING EM CASO DE DEMORA**
      const loadingTimeout = setTimeout(() => {
        if (isCurrentConversation && selectedChat?._id === currentChatId) {
          setLoadingMessages(false);
        }
      }, 10000); // 10 segundos

      return () => {
        // **📌 MARCAR CONVERSA COMO INATIVA PARA CANCELAR REQUISIÇÕES**
        isCurrentConversation = false;
        clearTimeout(loadingTimeout);
        
        // **📌 LIMPAR TIMER DE CONVERSA RECÉM ABERTA**
        if (justOpenedTimerRef.current) {
          clearTimeout(justOpenedTimerRef.current);
        }
        setJustOpenedChat(false);
        
        mainSocket.emit('leave-lead-messages', { leadID: currentChatId });
        mainSocket.off('messages');
        // **📌 NÃO DESCONECTAR O SOCKET PRINCIPAL, APENAS LIMPAR EVENTOS**
      };
    } else {
      // **📌 LIMPAR LOADING QUANDO NÃO HÁ CHAT SELECIONADO**
      setLoadingMessages(false);
      // **📌 NÃO DESCONECTAR O SOCKET PRINCIPAL**
    }
  }, [selectedChat, mainSocket]);

  // **📌 FUNÇÃO ROBUSTA PARA ABRIR CONVERSA POR LEAD_ID**
  const abrirConversaPorLeadId = useCallback(async (leadId) => {
    if (!leadId) return;

    console.log(`🎯 [ABRIR CONVERSA] Tentando abrir conversa para lead_id: ${leadId}`);
    console.log(`📊 [ABRIR CONVERSA] Contexto atual:`, {
      chatsCarregados: chats.length,
      empresaID,
      token: token ? 'presente' : 'ausente',
      isMobile,
      showMobileChat
    });

    try {
      // **ESTRATÉGIA 1: Procurar na lista atual**
      const chatNaLista = chats.find(chat => chat._id === leadId);
      
      if (chatNaLista) {
        console.log(`✅ Chat encontrado na lista atual: ${chatNaLista.name}`);
        setSelectedChat(chatNaLista);
        if (isMobile) {
          setShowMobileChat(true);
        }
        // Marcar como lido se necessário
        if (unreadCounts[leadId] && unreadCounts[leadId] > 0) {
          markChatAsRead(leadId);
        }
        return;
      }

      // **ESTRATÉGIA 2: Buscar especificamente na API**
      console.log(`🔍 Chat não encontrado na lista, buscando na API...`);
      
      try {
        const response = await getWhatsappChatById(empresaID, leadId, token);
        
        if (response.data && response.data.status === 200 && response.data.chat) {
          const chatEncontrado = response.data.chat;
          console.log(`✅ Chat encontrado na API: ${chatEncontrado.name}`);
          
          // Adicionar o chat à lista no início para futuras consultas
          setChats(prevChats => {
            const chatJaExiste = prevChats.some(chat => chat._id === leadId);
            if (!chatJaExiste) {
              return [chatEncontrado, ...prevChats];
            }
            return prevChats;
          });
          
          // Selecionar o chat
          setSelectedChat(chatEncontrado);
          if (isMobile) {
            setShowMobileChat(true);
          }
          
          // Mostrar toast de sucesso
          toast.success(`💬 Conversa encontrada: ${chatEncontrado.name}`, {
            position: "top-right",
            autoClose: 3000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
          });
          
          return;
        }
      } catch (apiError) {
        console.warn(`⚠️ Erro ao buscar chat na API:`, apiError);
        
        // Se o erro for 404, significa que o chat não existe mais
        if (apiError.response?.status === 404) {
          console.log(`📭 Chat não existe mais no sistema`);
        }
      }

      // **ESTRATÉGIA 3: Buscar por texto em todas as conversas**
      console.log(`🔍 Tentando busca textual em todas as conversas...`);
      
      try {
        // Fazer uma busca mais ampla sem paginação para encontrar a conversa
        const searchResponse = await getWhatsappChats(empresaID, 0, 100, '', token);
        
        if (searchResponse.data?.messages?.[0]?.data) {
          const todasConversas = searchResponse.data.messages[0].data;
          const chatEncontradoPorBusca = todasConversas.find(chat => chat._id === leadId);
          
          if (chatEncontradoPorBusca) {
            console.log(`✅ Chat encontrado por busca ampla: ${chatEncontradoPorBusca.name}`);
            
            // Adicionar à lista atual
            setChats(prevChats => {
              const chatJaExiste = prevChats.some(chat => chat._id === leadId);
              if (!chatJaExiste) {
                return [chatEncontradoPorBusca, ...prevChats];
              }
              return prevChats;
            });
            
            // Selecionar o chat
            setSelectedChat(chatEncontradoPorBusca);
            if (isMobile) {
              setShowMobileChat(true);
            }
            
            toast.success(`💬 Conversa localizada: ${chatEncontradoPorBusca.name}`, {
              position: "top-right",
              autoClose: 3000,
            });
            
            return;
          }
        }
      } catch (searchError) {
        console.warn(`⚠️ Erro na busca ampla:`, searchError);
      }

      // **ESTRATÉGIA 4: Última tentativa - criar nova conversa**
      console.log(`❌ Chat não encontrado em lugar nenhum para lead_id: ${leadId}`);
      
      // Mostrar modal de confirmação para criar nova conversa
      const confirmarNovaConversa = window.confirm(
        '❓ Conversa não encontrada.\n\n' +
        'Isso pode acontecer quando:\n' +
        '• A conversa é muito antiga\n' +
        '• O contato foi removido do WhatsApp\n' +
        '• Houve algum problema na sincronização\n\n' +
        'Deseja tentar localizar o contato e iniciar uma nova conversa?'
      );
      
      if (confirmarNovaConversa) {
        // Tentar extrair informações do location.state se disponível
        const stateData = location.state;
        if (stateData && (stateData.nome || stateData.celular)) {
          console.log(`🆕 Tentando criar nova conversa com dados do atendimento:`, stateData);
          
          // Formatar número corretamente (adicionar código do país se necessário)
          let numeroFormatado = stateData.celular;
          if (numeroFormatado && !numeroFormatado.startsWith('55')) {
            numeroFormatado = '55' + numeroFormatado;
          }
          
          try {
            const createResponse = await createWhatsappChat(
              empresaID, 
              numeroFormatado, 
              stateData.nome, 
              token
            );
            
            if (createResponse.data?.status === 200 && createResponse.data.chat) {
              const novaConversa = createResponse.data.chat;
              console.log(`✅ Nova conversa criada: ${novaConversa.name}`);
              
              // Adicionar à lista
              setChats(prevChats => [novaConversa, ...prevChats]);
              
              // Selecionar
              setSelectedChat(novaConversa);
              if (isMobile) {
                setShowMobileChat(true);
              }
              
              toast.success(`🎉 Nova conversa iniciada com ${novaConversa.name}!`, {
                position: "top-right",
                autoClose: 4000,
              });
              
              return;
            }
          } catch (createError) {
            console.error(`❌ Erro ao criar nova conversa:`, createError);
            toast.error(`❌ Não foi possível criar nova conversa. Verifique se o número ainda existe.`, {
              position: "top-right",
              autoClose: 5000,
            });
          }
        } else {
          toast.info(`ℹ️ Não há dados suficientes para criar uma nova conversa. Tente localizar o contato manualmente.`, {
            position: "top-right",
            autoClose: 5000,
          });
        }
      }
      
      // Toast final se nada funcionou
      if (!confirmarNovaConversa) {
        toast.warning(`⚠️ Conversa não localizada. Pode ser uma conversa muito antiga ou o contato não existe mais.`, {
          position: "top-right",
          autoClose: 6000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
      
    } catch (error) {
      console.error(`❌ Erro geral ao tentar abrir conversa:`, error);
      toast.error(`❌ Erro ao abrir conversa. Tente novamente.`, {
        position: "top-right",
        autoClose: 3000,
      });
    }
  }, [chats, empresaID, token, isMobile, unreadCounts, markChatAsRead, location.state]);

  // **📌 Verificar se há um chat correspondente ao lead_id**
  useEffect(() => {
    if (leadIdParaAbrir && chats.length > 0) {
      // Usar a nova função robusta
      abrirConversaPorLeadId(leadIdParaAbrir);
    }
  }, [leadIdParaAbrir, chats.length, abrirConversaPorLeadId]);

  return (
    <>
      {/*<LeftMenu setSidebar={setSidebar} sidebar={sidebar} />*/}
      <Teste sidebar={sidebar}>
        {!isLoged ?
          <div className="contentItemComplete" style={{ width: '100%' }}>
            <div className="input-group inputGroup-adicinaItem container-whatsapp-custom">
              <div className="formGroupRow">
                <div style={{
                  backgroundColor: 'white',
                  padding: isMobile ? '15px' : '20px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: isMobile ? '15px' : '20px',
                  margin: 'auto',
                  maxWidth: isMobile ? '100%' : 'none'
                }}>
                  <h2 style={{
                    fontSize: isMobile ? '1.3rem' : '1.5rem',
                    textAlign: isMobile ? 'center' : 'left',
                    marginBottom: isMobile ? '10px' : '15px'
                  }}>Para sincronizar o WhatsApp faça os seguintes passos</h2>
                  <ol style={{
                    paddingLeft: isMobile ? '15px' : '20px',
                    fontSize: isMobile ? '14px' : '16px',
                    lineHeight: '1.5'
                  }}>
                    <li style={{ marginBottom: isMobile ? '8px' : '5px' }}>Abra o WhatsApp no seu celular.</li>
                    <li style={{ marginBottom: isMobile ? '8px' : '5px' }}>Toque em Mais opções no Android ou em Configurações no iPhone</li>
                    <li style={{ marginBottom: isMobile ? '8px' : '5px' }}>Toque em Dispositivos conectados e, em seguida, em Conectar dispositivo.</li>
                    <li style={{ marginBottom: isMobile ? '8px' : '5px' }}>Aponte seu celular para esta tela para escanear o QR code.</li>
                  </ol>
                  <div style={{ display: 'flex', justifyContent: 'center', margin: '20px 0' }}>
                    <div style={{
                      padding: isMobile ? '20px' : '30px',
                      backgroundColor: 'white',
                      border: '1px solid #E0E0E0',
                      borderRadius: '12px',
                      maxWidth: '100%',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    }}>
                      <div style={{
                        width: isMobile ? '260px' : '320px',
                        height: isMobile ? '260px' : '320px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '8px',
                        maxWidth: '100%',
                        backgroundColor: '#fafafa',
                        border: '1px solid #f0f0f0'
                      }}>
                        {
                          generatingQrCode ? <Loading type={"spinningBubbles"} className="generatingQrCodeLoading" />
                            :
                            qrCodeImg ? (
                              <div style={{ textAlign: 'center' }}>
                                <img
                                  src={`${qrCodeImg}`}
                                  alt="QR Code"
                                  style={{
                                    maxWidth: '100%',
                                    maxHeight: '100%',
                                    width: 'auto',
                                    height: 'auto',
                                    opacity: qrCodeExpired ? 0.5 : 1,
                                    transition: 'opacity 0.3s ease'
                                  }}
                                  onLoad={() => console.log('QR Code carregado com sucesso')}
                                  onError={(e) => {
                                    console.error('Erro ao carregar QR Code:', e);
                                    console.log('URL da imagem:', qrCodeImg);
                                  }}
                                />

                                {/* 📱 TIMER E CONTROLES MELHORADOS */}
                                <div style={{ marginTop: '15px' }}>
                                  {!qrCodeExpired && qrCodeTimeLeft > 0 && (
                                    <div style={{
                                      color: qrCodeTimeLeft <= 10 ? '#e74c3c' : '#666',
                                      fontSize: '13px',
                                      marginBottom: '10px',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      gap: '5px',
                                      fontWeight: qrCodeTimeLeft <= 10 ? '600' : 'normal'
                                    }}>
                                      <span>⏱️</span>
                                      <span>
                                        Expira em: {Math.floor(qrCodeTimeLeft / 60)}:{(qrCodeTimeLeft % 60).toString().padStart(2, '0')}
                                        {qrCodeTimeLeft <= 10 && ' ⚠️'}
                                      </span>
                                    </div>
                                  )}

                                  {qrCodeExpired && !generatingQrCode && (
                                    <div style={{
                                      color: '#e74c3c',
                                      fontSize: '14px',
                                      marginBottom: '10px',
                                      fontWeight: '500',
                                      textAlign: 'center'
                                    }}>
                                      ⚠️ QR Code expirado
                                      <div style={{
                                        fontSize: '12px',
                                        color: '#666',
                                        marginTop: '5px',
                                        fontWeight: 'normal'
                                      }}>
                                        Clique para gerar um novo
                                      </div>
                                    </div>
                                  )}

                                  {generatingQrCode && (
                                    <div style={{
                                      color: '#25D366',
                                      fontSize: '14px',
                                      marginBottom: '10px',
                                      fontWeight: '500',
                                      textAlign: 'center',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      gap: '8px'
                                    }}>
                                      <Loading type="spin" color="#25D366" height={16} width={16} />
                                      <span>Gerando novo QR Code...</span>
                                    </div>
                                  )}

                                  {(qrCodeExpired || showRegenerateButton) && (
                                    <button
                                      onClick={() => regenerateQrCodeViaAPI('manual')}
                                      disabled={generatingQrCode}
                                      style={{
                                        backgroundColor: generatingQrCode ? '#95a5a6' : '#25D366',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '8px',
                                        padding: '12px 24px',
                                        cursor: generatingQrCode ? 'not-allowed' : 'pointer',
                                        fontSize: '14px',
                                        fontWeight: '500',
                                        transition: 'all 0.3s ease',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        gap: '8px',
                                        margin: '0 auto'
                                      }}
                                    >
                                      {generatingQrCode ? (
                                        <>
                                          <span>⏳</span>
                                          <span>Gerando...</span>
                                        </>
                                      ) : (
                                        <>
                                          <span>🔄</span>
                                          <span>Gerar novo QR Code</span>
                                        </>
                                      )}
                                    </button>
                                  )}
                                </div>
                              </div>
                            ) : userRequestedQrCode ? (
                              // 🔧 NOVO: Estado quando usuário solicitou QR Code mas ainda está carregando
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexDirection: 'column',
                                gap: '15px',
                                color: '#666',
                                textAlign: 'center',
                                padding: '20px'
                              }}>
                                <Loading type="spin" color="#25D366" height={40} width={40} />
                                <div style={{ fontSize: isMobile ? '14px' : '16px', fontWeight: '500' }}>
                                  Gerando QR Code...
                                </div>
                                <div style={{ fontSize: isMobile ? '12px' : '14px', color: '#999' }}>
                                  Aguarde enquanto preparamos seu código
                                </div>
                              </div>
                            ) : (
                              // 🔧 NOVO: Estado inicial - Botão para gerar QR Code manualmente
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexDirection: 'column',
                                gap: '20px',
                                color: '#666',
                                textAlign: 'center',
                                padding: '20px'
                              }}>
                                <div style={{
                                  fontSize: '48px',
                                  color: '#25D366',
                                  marginBottom: '10px'
                                }}>
                                  📱
                                </div>
                                <div style={{ fontSize: isMobile ? '16px' : '18px', fontWeight: '500', color: '#333' }}>
                                  Pronto para conectar?
                                </div>
                                <div style={{ 
                                  fontSize: isMobile ? '13px' : '14px', 
                                  color: '#666',
                                  lineHeight: '1.5',
                                  marginBottom: '10px'
                                }}>
                                  Clique no botão abaixo para gerar seu QR Code e conectar o WhatsApp
                                </div>
                                <button
                                  onClick={generateQrCodeManually}
                                  style={{
                                    backgroundColor: '#25D366',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '12px',
                                    padding: '16px 32px',
                                    cursor: 'pointer',
                                    fontSize: '16px',
                                    fontWeight: '600',
                                    transition: 'all 0.3s ease',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    gap: '10px',
                                    boxShadow: '0 4px 12px rgba(37, 211, 102, 0.3)',
                                    minWidth: '200px'
                                  }}
                                  onMouseOver={(e) => {
                                    e.target.style.backgroundColor = '#128C7E';
                                    e.target.style.transform = 'translateY(-2px)';
                                  }}
                                  onMouseOut={(e) => {
                                    e.target.style.backgroundColor = '#25D366';
                                    e.target.style.transform = 'translateY(0)';
                                  }}
                                >
                                  <span style={{ fontSize: '20px' }}>📱</span>
                                  <span>Gerar QR Code</span>
                                </button>
                              </div>
                            )
                        }
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          :
          <Container>
            <WrapperContainerAll style={{ marginTop: window.innerWidth < 1024 ? '25px' : '0px' }}>

              <WrapperHMC isMobile={isMobile} showMobileChat={showMobileChat}>

                <WrapperListHeader>
                  <HeaderListHeader>
                    <UserImage onClick={openMenu}>
                      <img
                        src={myProfilePicture || userParse?.user_img[0] || userIcon}
                        width="35"
                        height="35"
                        alt="Foto de perfil do usuário"
                        onError={(e) => {
                          // Se a imagem falhar ao carregar, tentar buscar uma nova
                          if (e.target.src !== userIcon) {
                            console.log(`❌ Erro ao carregar minha imagem de perfil, buscando nova...`);
                            e.target.src = userIcon;
                            fetchMyProfilePicture(true); // Forçar atualização
                          }
                        }}
                        onDoubleClick={() => {
                          // Duplo clique para forçar atualização da imagem
                          console.log(`🔄 Forçando atualização da minha imagem de perfil`);
                          fetchMyProfilePicture(true);
                        }}
                      />
                    </UserImage>
                    <Actions ref={menuRef}>
                      <StatusIcon role="img" aria-label="Ícone de abrir status" />
                      <ChatIcon role="img" aria-label="Ícone de iniciar nova conversa" />
                      <MenuIcon
                        role="img"
                        aria-label="Ícone de expandir menu"
                        onClick={() => setIsMenuOpen(true)}
                      />
                    </Actions>
                  </HeaderListHeader>
                </WrapperListHeader>

                <WrapperMenu className={isMenuOpen ? "active" : ""}>
                  <ActionWrapperMenu>
                    {mockOptions.map(({ id, action }) => (
                      <ActionMenu
                        key={id}
                        onClick={action === "Desconectar" ? handleDisconnect : openMenu}
                        className={action === "Desconectar" ? "" : ""} // Apenas desativa se necessário
                      >
                        {action}
                      </ActionMenu>
                    ))}
                  </ActionWrapperMenu>
                </WrapperMenu>

                <SearchBar searchQuery={searchQuery} setSearchQuery={setSearchQuery} />

                {/* 📌 INDICADOR DE PRÉ-CARREGAMENTO */}
                {preloadingChats && (
                  <div style={{
                    padding: '8px 16px',
                    backgroundColor: '#f0f8f0',
                    borderLeft: '3px solid #25D366',
                    fontSize: '12px',
                    color: '#666',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}>
                    <Loading type="spin" color="#25D366" height={12} width={12} />
                    <span>
                      Pré-carregando conversas... ({preloadProgress.current}/{preloadProgress.total})
                    </span>
                  </div>
                )}

                {/* 📌 Adicionamos `ref={chatsContainerRef}` para detectar o scroll */}
                <div className="containerChatsWhatsapp" ref={chatsContainerRef}>
                  {/* Loading inicial dos chats */}
                  {loading && chats.length === 0 && (
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: '40px 20px',
                      color: '#666'
                    }}>
                      <Loading type="spin" color="#25D366" height={40} width={40} />
                      <p style={{ marginTop: '15px', fontSize: '14px' }}>Carregando conversas...</p>
                    </div>
                  )}

                  {chats.map((chat) => (
                    <WrapperListContacts key={chat._id}
                      isSelected={chat._id === selectedChat?._id}
                      onClick={() => {
                        if (isMobile) {
                          handleSelectChatMobile(chat);
                        } else {
                          setSelectedChat(chat);
                          // **📌 MARCAR MENSAGENS COMO LIDAS AO SELECIONAR CHAT (DESKTOP)**
                          if (unreadCounts[chat._id] && unreadCounts[chat._id] > 0) {
                            markChatAsRead(chat._id);
                          }
                        }
                      }}>
                      <ContactPhotoListContacts>
                        <UnreadBadge count={unreadCounts[chat._id] || 0}>
                          <img
                            src={chat.channel_data?.whatsapp?.profile_picture || userIcon}
                            width="60"
                            alt="Foto de perfil do usuário"
                            onError={(e) => {
                              // Se a imagem falhar ao carregar, tentar buscar uma nova
                              if (e.target.src !== userIcon) {
                                console.log(`❌ Erro ao carregar imagem de perfil para ${chat.name}, buscando nova...`);
                                e.target.src = userIcon;
                                fetchProfilePicture(chat);
                              }
                            }}
                            onDoubleClick={() => {
                              // Duplo clique para forçar atualização da imagem
                              console.log(`🔄 Forçando atualização da imagem de perfil para ${chat.name}`);
                              fetchProfilePicture(chat, true); // forceUpdate = true
                            }}
                            title="Duplo clique para atualizar foto de perfil"
                          />
                        </UnreadBadge>
                      </ContactPhotoListContacts>
                      <MessageDataWrapper>
                        <ContactNameAndTime>
                          <span 
                            onDoubleClick={() => {
                              console.log(`🔄 Atualizando informações do contato ${chat.name} via duplo clique`);
                              refreshContactInfo(chat._id, chat.name);
                            }}
                            title="Duplo clique para atualizar informações do contato"
                            style={{ cursor: 'pointer', display: 'flex', alignItems: 'center', gap: '5px' }}
                          >
                            {chat.name}
                            {hasGenericName(chat.name, chat.mobile_number) && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  refreshContactInfo(chat._id, chat.name);
                                }}
                                style={{
                                  background: 'transparent',
                                  border: 'none',
                                  cursor: 'pointer',
                                  color: '#FFA500',
                                  fontSize: '0.8rem',
                                  padding: '0',
                                  margin: '0',
                                  opacity: 0.7,
                                  transition: 'opacity 0.2s ease'
                                }}
                                onMouseOver={(e) => e.target.style.opacity = 1}
                                onMouseOut={(e) => e.target.style.opacity = 0.7}
                                title="Este contato tem um nome genérico. Clique para buscar o nome real."
                              >
                                🔍
                              </button>
                            )}
                          </span>
                          <p>{chat.message ? formatDate(chat.message.messageDate || chat.message.createdAt) : ''}</p>
                        </ContactNameAndTime>
                        <ContactMessage>
                          {chat.message && chat.message.fromMe && <CheckIcon />}
                          <p>{chat.message ? (chat.message.message || chat.message.text || 'Nova conversa') : 'Nova conversa'}</p>
                        </ContactMessage>
                      </MessageDataWrapper>
                    </WrapperListContacts>
                  ))}

                  {/* **📌 Indicador de carregamento enquanto busca mais chats** */}
                  {loadingMore && <p className="loadingChats-indicator">🔄 Carregando mais conversas...</p>}
                </div>

              </WrapperHMC>

              {(selectedChat && (!isMobile || showMobileChat)) ?
                <WrapperContainerConversa 
                  isMobile={isMobile} 
                  showMobileChat={showMobileChat}
                  backgroundLoaded={backgroundLoaded}
                >

                  <Header>
                    <MobileBackButton onClick={handleBackToList}>
                      <BackIcon />
                    </MobileBackButton>
                    <ContactPhoto onClick={handleOpenProfile} style={{ cursor: "pointer" }}>
                      <img
                        src={selectedChat.channel_data?.whatsapp?.profile_picture || userIcon}
                        width="40"
                        alt="Foto de perfil do usuário"
                        onError={(e) => {
                          // Se a imagem falhar ao carregar, tentar buscar uma nova
                          if (e.target.src !== userIcon) {
                            console.log(`❌ Erro ao carregar imagem de perfil para ${selectedChat.name}, buscando nova...`);
                            e.target.src = userIcon;
                            fetchProfilePicture(selectedChat);
                          }
                        }}
                        onDoubleClick={(e) => {
                          // Duplo clique para forçar atualização da imagem (evitar abrir perfil)
                          e.stopPropagation();
                          console.log(`🔄 Forçando atualização da imagem de perfil para ${selectedChat.name}`);
                          fetchProfilePicture(selectedChat, true); // forceUpdate = true
                        }}
                        title="Duplo clique para atualizar foto de perfil"
                      />
                    </ContactPhoto>
                    <ContactName>
                      {selectedChat.name}
                      <button
                        onClick={() => refreshContactInfo(selectedChat._id, selectedChat.name)}
                        style={{
                          background: 'transparent',
                          border: 'none',
                          marginLeft: '10px',
                          cursor: 'pointer',
                          color: '#666',
                          fontSize: '1.2rem',
                          padding: '2px',
                          borderRadius: '3px',
                          transition: 'color 0.2s ease'
                        }}
                        onMouseOver={(e) => e.target.style.color = '#25D366'}
                        onMouseOut={(e) => e.target.style.color = '#666'}
                        title="Atualizar informações do contato"
                      >
                        🔄 Atualizar informações do contato
                      </button>
                    </ContactName>
                  </Header>

                  <WrapperContent
                    ref={messagesContainerRef}
                    onScroll={handleMessagesScroll}
                  >
                    {loadingMessages ? (
                      <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                        color: '#666',
                        gap: '15px'
                      }}>
                        <Loading type="spin" color="#25D366" height={40} width={40} />
                        <p style={{ fontSize: '14px', textAlign: 'center' }}>
                          Carregando mensagens de {selectedChat.name}...
                        </p>
                      </div>
                    ) : Object.keys(groupedMessages).length > 0 ? (
                      <>
                        {/* **📌 INDICADOR DE CARREGAMENTO DE MAIS MENSAGENS** */}
                        {loadingMoreMessages && (
                          <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            padding: '15px',
                            color: '#666',
                            fontSize: '14px',
                            gap: '10px'
                          }}>
                            <Loading type="spin" color="#25D366" height={20} width={20} />
                            <span>Carregando mensagens anteriores...</span>
                          </div>
                        )}

                        {/* **📌 INDICADOR QUANDO NÃO HÁ MAIS MENSAGENS** */}
                        {!hasMoreMessages && messagesLeadChannel.length > 30 && (
                          <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            padding: '15px',
                            color: '#999',
                            fontSize: '12px',
                            fontStyle: 'italic'
                          }}>
                            📜 Início da conversa
                          </div>
                        )}

                        {Object.keys(groupedMessages).map((date) => (
                          <React.Fragment key={date}>
                            <ChatDate>{moment(date).calendar(null, {
                              sameDay: '[Hoje]',
                              lastDay: '[Ontem]',
                              lastWeek: 'dddd',
                              sameElse: 'DD/MM/YYYY'
                            })}</ChatDate>
                            {groupedMessages[date].map((message) => {
                              const { _id, text, createdAt, fromMe, isOptimistic, status } = message;
                              return (
                                <Message key={_id} fromMe={fromMe}>
                                  <p>{text}</p>
                                  <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                                    {moment(createdAt).format('HH:mm')}
                                    {fromMe && isOptimistic && status === 'sending' && (
                                      <span title="Enviando..." style={{ color: '#999', fontSize: '10px' }}>⏳</span>
                                    )}
                                    {fromMe && isOptimistic && status === 'sent' && (
                                      <span title="Enviado" style={{ color: '#25D366', fontSize: '10px' }}>✓</span>
                                    )}
                                    {fromMe && !isOptimistic && (
                                      <span title="Entregue" style={{ color: '#25D366', fontSize: '10px' }}>✓✓</span>
                                    )}
                                  </span>
                                </Message>
                              );
                            })}
                          </React.Fragment>
                        ))}
                        <div ref={messageEndRef} />
                      </>
                    ) : (
                      <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                        color: '#999',
                        textAlign: 'center',
                        padding: '40px 20px'
                      }}>
                        <div style={{
                          fontSize: '48px',
                          marginBottom: '20px',
                          opacity: 0.5
                        }}>
                          💬
                        </div>
                        <p style={{
                          fontSize: '18px',
                          marginBottom: '8px',
                          fontWeight: '500',
                          color: '#666'
                        }}>
                          Nenhuma mensagem ainda
                        </p>
                        <p style={{
                          fontSize: '14px',
                          lineHeight: '1.4',
                          maxWidth: '280px'
                        }}>
                          Esta é uma nova conversa com <strong>{selectedChat.name}</strong>.
                          Envie uma mensagem para começar!
                        </p>
                      </div>
                    )}
                  </WrapperContent>

                  <WrapperChat>
                    <form onSubmit={handleSubmit} style={{ display: "flex", alignItems: "center", gap: 10 }}>
                      <Input
                        type="text"
                        placeholder="Digite uma mensagem"
                        onChange={({ target }) => setMessage(target.value)}
                        value={message}
                        style={{ fontSize: isMobile ? '16px' : '14px' }}
                      />

                      <button
                        type="submit"
                        disabled={isSubmitting || !message.trim()}
                        style={{
                          background: "transparent",
                          border: "none",
                          cursor: isSubmitting || !message.trim() ? "not-allowed" : "pointer",
                          color: isSubmitting || !message.trim() ? "#ccc" : "#25D366", // Cor do ícone (verde do WhatsApp)
                          fontSize: isMobile ? "1.8rem" : "2rem",
                          opacity: isSubmitting || !message.trim() ? 0.6 : 1,
                        }}
                      >
                        {isSubmitting ? (
                          <Loading type="spin" color="#25D366" height={20} width={20} />
                        ) : (
                          <FiSend />
                        )}
                      </button>

                      {/* Botão de Pausar/Reativar o Bot */}
                      <button
                        type="button"
                        onClick={() => handleToggleBot(selectedChat._id)}
                        style={{
                          background: "transparent",
                          border: botPausado ? "1px solid #FF4D4D" : "1px solid #25D366",
                          cursor: "pointer",
                          display: "flex",
                          alignItems: "center",
                          gap: 5,
                          color: botPausado ? "#FF4D4D" : "#25D366", // Vermelho se pausado, branco se ativo
                          fontSize: isMobile ? "12px" : "14px",
                          fontWeight: '600',
                          minWidth: isMobile ? 120 : 140,
                          justifyContent: 'center',
                          borderRadius: 8,
                          padding: isMobile ? '8px' : '10px'
                        }}
                      >
                        {botPausado ? <FiPauseCircle style={{ fontSize: 17 }} /> : <FiPlayCircle style={{ fontSize: 17 }} />}
                        <span>{botPausado ? "Robô Pausado" : "Robô Ativado"}</span>
                      </button>
                    </form>
                  </WrapperChat>
                  {/* Modal do Perfil */}
                  <UserProfile isOpen={isProfileOpen} onClose={() => setIsProfileOpen(false)} user={selectedChat} />
                </WrapperContainerConversa>
                :
                (!isMobile || !showMobileChat) && <WrapperContainerConversa 
                  backgroundLoaded={backgroundLoaded}
                  style={{
                    background: 'white', 
                    justifyContent: 'center',
                    display: isMobile ? 'none' : 'flex',
                    flexDirection: 'column',
                    alignItems: 'center'
                  }}
                >
                  <FaWhatsapp style={{ fontSize: '100px', color: 'rgba(0, 0, 0, 0.45)' }} />
                  <Warn style={{ background: 'white' }}>Selecione uma conversa para começar a conversar</Warn>
                </WrapperContainerConversa>
              }

            </WrapperContainerAll>

            {isOpen && (
              <S.Wrapper>
                <S.Header>
                  <S.Back>
                    <BackIcon onClick={closeMenu} />
                    <h3>Perfil</h3>
                  </S.Back>
                </S.Header>

                <S.UserImage>
                  <img
                    src={myProfilePicture || userParse?.user_img[0] || userIcon}
                    width="200"
                    height="200"
                    alt="Foto de perfil do usuário"
                    onError={(e) => {
                      // Se a imagem falhar ao carregar, tentar buscar uma nova
                      if (e.target.src !== userIcon) {
                        console.log(`❌ Erro ao carregar minha imagem de perfil no modal, buscando nova...`);
                        e.target.src = userIcon;
                        fetchMyProfilePicture(true); // Forçar atualização
                      }
                    }}
                    onDoubleClick={() => {
                      // Duplo clique para forçar atualização da imagem
                      console.log(`🔄 Forçando atualização da minha imagem de perfil no modal`);
                      fetchMyProfilePicture(true);
                    }}
                    style={{ cursor: 'pointer' }}
                  />
                </S.UserImage>

                <S.UserData>
                  <span>Nome</span>
                  <p>{empresaParse.name}</p>
                </S.UserData>

                <S.UserData>
                  <span>Recado</span>
                  <p>Olá! Eu estou usando o WhatsApp.</p>
                </S.UserData>
              </S.Wrapper>
            )}
          </Container>
        }

        <GlobalStyles />

      </Teste>
    </>
  );
}

export default WhatsAppWeb;