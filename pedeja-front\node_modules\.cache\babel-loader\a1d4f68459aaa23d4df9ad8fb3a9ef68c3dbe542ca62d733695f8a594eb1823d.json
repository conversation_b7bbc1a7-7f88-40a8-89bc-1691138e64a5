{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\roboConfig\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport './style.css';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport LeftMenu from \"../../components/LeftMenu\";\nimport { Modal } from \"../../components/Modal\";\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\nimport styled from 'styled-components';\nimport { FaEdit, FaTrash, FaPlus, FaSearch } from \"react-icons/fa\";\nimport { IoMdClose } from \"react-icons/io\";\nimport CryptoJS from 'crypto-js';\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RoboConfigContainer = styled.div`\n    display: flex;\n    margin-left: ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'};\n    height: auto;\n    width: auto;\n    transition: 150ms;\n    background-color: rgb(247,247,247) !important;\n    overflow: initial;\n    z-index: 9;\n\n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\nconst RoboCfg = () => {\n  _s();\n  var _selectedResponse2, _selectedResponse3, _selectedResponse4, _selectedResponse5;\n  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\n  const userEncrypted = localStorage.getItem('user');\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  const userParse = JSON.parse(user);\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const empresaObjId = empresaParse._id;\n  const {\n    sidebar\n  } = useContext(SidebarContext);\n  const navigate = useNavigate();\n  const [customResponses, setCustomResponses] = useState([]);\n  const [filteredResponses, setFilteredResponses] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isAddModalOpen, setIsAddModalOpen] = useState(false);\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false);\n  const [editingIndex, setEditingIndex] = useState(null);\n  const [editingData, setEditingData] = useState({\n    question: '',\n    response: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(5);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [deleteIndex, setDeleteIndex] = useState(null);\n  useEffect(() => {\n    // Função para buscar os dados do banco de dados\n    const fetchData = async () => {\n      try {\n        // Supondo que getEmpresaWithObjId seja uma função que retorna uma Promise\n        const empresaTemp = await getEmpresaWithObjId(empresaObjId);\n        const companyResponsesTemp = await getCompanyResponses(empresaObjId);\n        console.log(\"companyResponses:\", companyResponsesTemp.data.responses);\n        setCompanyResponses(companyResponsesTemp.data.responses);\n        setSelectedResponse(companyResponsesTemp.data.responses[0]);\n      } catch (error) {\n        console.error(\"Erro ao buscar dados da empresa:\", error);\n        // Lidar com o erro, talvez definindo algum estado de erro na UI\n      }\n    };\n    fetchData();\n  }, [refresh]); // O array vazio [] indica que o useEffect será executado uma vez após a montagem do componente\n\n  const debouncedUpdateQuestionActive = debounce(async (empresaObjId, questionIdentifier, isActive, setCompanyResponses, companyResponses) => {\n    try {\n      // Atualiza no backend\n      await updateQuestionActive(empresaObjId, questionIdentifier, isActive);\n\n      // Atualiza localmente após confirmação do backend\n      const updatedResponses = companyResponses.map(response => response.questionIdentifier === questionIdentifier ? {\n        ...response,\n        active: isActive\n      } : response);\n      setCompanyResponses(updatedResponses);\n      console.log(\"Disponibilidade atualizada com sucesso.\");\n    } catch (error) {\n      console.error(\"Erro ao atualizar disponibilidade:\", error);\n    }\n  }, 300); // Aguarda 300ms após o último evento\n\n  const handleBack = () => {\n    //setSidebar(!sidebar)\n    navigate(\"/\");\n  };\n  const handleSelectQuestionAndAnswer = message => {\n    setSelectedResponse(message);\n  };\n\n  // Função para atualizar o response do selectedResponse diretamente\n  const handleResponseChange = e => {\n    setSelectedResponse(prevSelectedResponse => ({\n      ...prevSelectedResponse,\n      response: e.target.value\n    }));\n  };\n\n  // Função para salvar as alterações no companyResponses\n  const handleSaveResponse = async () => {\n    if (selectedResponse) {\n      // Atualiza a resposta localmente\n      const updatedResponses = companyResponses.map(response => response.questionIdentifier === selectedResponse.questionIdentifier ? {\n        ...response,\n        response: selectedResponse.response\n      } // Atualiza apenas a mensagem de resposta\n      : response);\n      setCompanyResponses(updatedResponses);\n      try {\n        // Chama a API para atualizar o backend\n        await updateQuestionResponses(empresaObjId, updatedResponses);\n        toast(\"Respostas atualizadas com sucesso!\", {\n          autoClose: 2000,\n          type: \"success\"\n        });\n      } catch (error) {\n        console.error(\"Erro ao atualizar respostas:\", error);\n        toast(\"Ocorreu um erro ao salvar as respostas.\", {\n          autoClose: 2000,\n          type: \"error\"\n        });\n      }\n    }\n  };\n\n  // Função genérica para inserir texto no cursor\n  const insertAtCursor = (textarea, text) => {\n    if (textarea) {\n      const startPos = textarea.selectionStart;\n      const endPos = textarea.selectionEnd;\n      const value = textarea.value;\n\n      // Atualiza o texto no campo com o novo valor\n      textarea.value = value.substring(0, startPos) + text + value.substring(endPos);\n\n      // Mantém o cursor após o texto inserido\n      textarea.selectionStart = textarea.selectionEnd = startPos + text.length;\n    }\n  };\n\n  // Funções específicas para cada botão\n  const handleInsertNomeCliente = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"{nome_cliente}\");\n  };\n  const handleInsertLinkCardapio = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"{link}\");\n  };\n  const handleInsertSaudacao = () => {\n    const textarea = document.querySelector(\".roboCfg-textarea\");\n    insertAtCursor(textarea, \"Agradecemos a preferência\");\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: /*#__PURE__*/_jsxDEV(Teste, {\n        sidebar: sidebar,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-100 p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-header\",\n              style: {\n                marginBottom: \"0px\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"title\",\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  children: \"Configura\\xE7\\xF5es do Rob\\xF4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                justifyContent: \"right\" /*, height:\"80px\"*/\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contentItemComplete flex-column flex-md-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group inputGroup-etapasItem\",\n                style: {\n                  height: 50\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: tela === \"tela1\" ? \"etapasAddItem etapaAtiva\" : \"etapasAddItem\",\n                  style: {\n                    borderBottom: '1px solid lightgray'\n                  },\n                  onClick: () => setTela(\"tela1\"),\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"1. Personalizar Mensagens\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group mh-100\",\n                style: {\n                  maxWidth: \"80%\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"formGroupRow\",\n                  children: tela === \"tela1\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"roboCfg-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"roboCfg-sidebar\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        children: \"Mensagens\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"roboCfg-message-list\",\n                        children: companyResponses.map((message, index) => {\n                          var _selectedResponse;\n                          return /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: `roboCfg-message-item ${((_selectedResponse = selectedResponse) === null || _selectedResponse === void 0 ? void 0 : _selectedResponse.questionIdentifier) === message.questionIdentifier ? 'roboCfg-selected' : ''}`,\n                            onClick: () => handleSelectQuestionAndAnswer(message),\n                            children: [message.questionType, /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"checkBoxContentMsg\",\n                              children: [message.active ? /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"roboCfg-edit-button-ativo\",\n                                children: \"Ativo\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 231,\n                                columnNumber: 73\n                              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"roboCfg-edit-button-inativo\",\n                                children: \"Inativo\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 233,\n                                columnNumber: 73\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"wrapper\",\n                                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"switch_box box_1\",\n                                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                                    type: \"checkbox\",\n                                    className: \"switch_1\",\n                                    checked: message.active || false\n                                    // Dentro do checkbox\n                                    ,\n                                    onChange: e => {\n                                      const isActive = e.target.checked;\n\n                                      // Chamar a função debounce para evitar múltiplas chamadas ao backend\n                                      debouncedUpdateQuestionActive(empresaObjId, message.questionIdentifier, isActive, setCompanyResponses, companyResponses);\n                                    }\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 237,\n                                    columnNumber: 77\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 236,\n                                  columnNumber: 73\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 235,\n                                columnNumber: 69\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 229,\n                              columnNumber: 65\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 223,\n                            columnNumber: 61\n                          }, this);\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 221,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"roboCfg-main\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"roboCfg-preview\",\n                        style: {\n                          backgroundImage: `url(${backgroundWhatsApp})`\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-chat\",\n                          children: [((_selectedResponse2 = selectedResponse) === null || _selectedResponse2 === void 0 ? void 0 : _selectedResponse2.question) && /*#__PURE__*/_jsxDEV(Message, {\n                            fromMe: false,\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              children: (_selectedResponse3 = selectedResponse) === null || _selectedResponse3 === void 0 ? void 0 : _selectedResponse3.question\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 262,\n                              columnNumber: 69\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"12:00\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 263,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 261,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(Message, {\n                            fromMe: true,\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              children: (_selectedResponse4 = selectedResponse) === null || _selectedResponse4 === void 0 ? void 0 : _selectedResponse4.response\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 269,\n                              columnNumber: 65\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"12:01\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 270,\n                              columnNumber: 65\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 268,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 258,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 257,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"roboCfg-editor\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          children: \"Personalize a mensagem\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 275,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                          className: \"roboCfg-textarea\",\n                          value: ((_selectedResponse5 = selectedResponse) === null || _selectedResponse5 === void 0 ? void 0 : _selectedResponse5.response) || \"\",\n                          onChange: handleResponseChange // Altera diretamente o response do selectedResponse\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 276,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-buttons\",\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-button\",\n                            type: \"button\",\n                            onClick: handleInsertNomeCliente // Chama a função para Nome do cliente\n                            ,\n                            children: \"Nome do cliente\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 282,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-button\",\n                            type: \"button\",\n                            onClick: handleInsertLinkCardapio // Chama a função para Link do cardápio\n                            ,\n                            children: \"Link do card\\xE1pio\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 289,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 281,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"roboCfg-save-cancel\",\n                          children: /*#__PURE__*/_jsxDEV(\"button\", {\n                            className: \"roboCfg-save-button\",\n                            type: \"button\",\n                            onClick: handleSaveResponse,\n                            children: \"Salvar\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 306,\n                            columnNumber: 61\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 305,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 274,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(RoboCfg, \"EcshtfA4WzkkDiNbDK26zM9D2qI=\", false, function () {\n  return [useNavigate];\n});\n_c = RoboCfg;\nexport default RoboCfg;\nvar _c;\n$RefreshReg$(_c, \"RoboCfg\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "SidebarContext", "PermissionGate", "LeftMenu", "Modal", "ModalAddCustomResponse", "ModalEditCustomResponse", "styled", "FaEdit", "FaTrash", "FaPlus", "FaSearch", "IoMdClose", "CryptoJS", "getCustomResponses", "addCustomResponse", "updateCustomResponse", "deleteCustomResponse", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RoboConfigContainer", "div", "sidebar", "RoboCfg", "_s", "_selectedResponse2", "_selectedResponse3", "_selectedResponse4", "_selectedResponse5", "secret<PERSON>ey", "userEncrypted", "localStorage", "getItem", "user", "AES", "decrypt", "toString", "enc", "Utf8", "userParse", "JSON", "parse", "empresa", "empresaParse", "empresaObjId", "_id", "navigate", "customResponses", "setCustomResponses", "filteredResponses", "setFilteredResponses", "searchTerm", "setSearchTerm", "isAddModalOpen", "setIsAddModalOpen", "isEditModalOpen", "setIsEditModalOpen", "editingIndex", "setEditingIndex", "editingData", "setEditingData", "question", "response", "loading", "setLoading", "currentPage", "setCurrentPage", "itemsPerPage", "showDeleteConfirm", "setShowDeleteConfirm", "deleteIndex", "setDeleteIndex", "fetchData", "empresaTemp", "getEmpresaWithObjId", "companyResponsesTemp", "getCompanyResponses", "console", "log", "data", "responses", "setCompanyResponses", "setSelectedResponse", "error", "refresh", "debouncedUpdateQuestionActive", "debounce", "questionIdentifier", "isActive", "companyResponses", "updateQuestionActive", "updatedResponses", "map", "active", "handleBack", "handleSelectQuestionAndAnswer", "message", "handleResponseChange", "e", "prevSelectedResponse", "target", "value", "handleSaveResponse", "selectedResponse", "updateQuestionResponses", "autoClose", "type", "insertAtCursor", "textarea", "text", "startPos", "selectionStart", "endPos", "selectionEnd", "substring", "length", "handleInsertNomeCliente", "document", "querySelector", "handleInsertLinkCardapio", "handleInsertSaudacao", "children", "permissions", "<PERSON>e", "className", "style", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "height", "tela", "borderBottom", "onClick", "<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "index", "_selectedResponse", "questionType", "checked", "onChange", "backgroundImage", "backgroundWhatsApp", "Message", "fromMe", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/roboConfig/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport './style.css';\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\nimport { Modal } from \"../../components/Modal\";\r\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\r\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\r\nimport styled from 'styled-components';\r\nimport { FaEdit, FaTrash, FaPlus, FaSearch } from \"react-icons/fa\";\r\nimport { IoMdClose } from \"react-icons/io\";\r\nimport CryptoJS from 'crypto-js';\r\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst RoboConfigContainer = styled.div`\r\n    display: flex;\r\n    margin-left: ${({ sidebar }) => (sidebar ? '250px' : '100px')};\r\n    height: auto;\r\n    width: auto;\r\n    transition: 150ms;\r\n    background-color: rgb(247,247,247) !important;\r\n    overflow: initial;\r\n    z-index: 9;\r\n\r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\nconst RoboCfg = () => {\r\n    const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\r\n    const userEncrypted = localStorage.getItem('user');\r\n    const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\r\n    const userParse = JSON.parse(user);\r\n    const empresa = localStorage.getItem('empresa');\r\n    const empresaParse = JSON.parse(empresa);\r\n    const empresaObjId = empresaParse._id;\r\n\r\n    const { sidebar } = useContext(SidebarContext);\r\n    const navigate = useNavigate();\r\n\r\n    const [customResponses, setCustomResponses] = useState([]);\r\n    const [filteredResponses, setFilteredResponses] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [isAddModalOpen, setIsAddModalOpen] = useState(false);\r\n    const [isEditModalOpen, setIsEditModalOpen] = useState(false);\r\n    const [editingIndex, setEditingIndex] = useState(null);\r\n    const [editingData, setEditingData] = useState({ question: '', response: '' });\r\n    const [loading, setLoading] = useState(false);\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [itemsPerPage] = useState(5);\r\n    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\r\n    const [deleteIndex, setDeleteIndex] = useState(null);\r\n\r\n    useEffect(() => {\r\n        // Função para buscar os dados do banco de dados\r\n        const fetchData = async () => {\r\n            try {\r\n                // Supondo que getEmpresaWithObjId seja uma função que retorna uma Promise\r\n                const empresaTemp = await getEmpresaWithObjId(empresaObjId);\r\n                const companyResponsesTemp = await getCompanyResponses(empresaObjId);\r\n                console.log(\"companyResponses:\", companyResponsesTemp.data.responses);\r\n                setCompanyResponses(companyResponsesTemp.data.responses);\r\n                setSelectedResponse(companyResponsesTemp.data.responses[0]);             \r\n            } catch (error) {\r\n                console.error(\"Erro ao buscar dados da empresa:\", error);\r\n                // Lidar com o erro, talvez definindo algum estado de erro na UI\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, [refresh]); // O array vazio [] indica que o useEffect será executado uma vez após a montagem do componente\r\n\r\n    const debouncedUpdateQuestionActive = debounce(async (empresaObjId, questionIdentifier, isActive, setCompanyResponses, companyResponses) => {\r\n        try {\r\n            // Atualiza no backend\r\n            await updateQuestionActive(empresaObjId, questionIdentifier, isActive);\r\n    \r\n            // Atualiza localmente após confirmação do backend\r\n            const updatedResponses = companyResponses.map((response) =>\r\n                response.questionIdentifier === questionIdentifier\r\n                    ? { ...response, active: isActive }\r\n                    : response\r\n            );\r\n            setCompanyResponses(updatedResponses);\r\n            console.log(\"Disponibilidade atualizada com sucesso.\");\r\n        } catch (error) {\r\n            console.error(\"Erro ao atualizar disponibilidade:\", error);\r\n        }\r\n    }, 300); // Aguarda 300ms após o último evento\r\n\r\n\r\n    const handleBack = () => {\r\n        //setSidebar(!sidebar)\r\n        navigate(\"/\");\r\n    }\r\n\r\n    const handleSelectQuestionAndAnswer = (message) => {\r\n        setSelectedResponse(message);\r\n    };\r\n\r\n    // Função para atualizar o response do selectedResponse diretamente\r\n    const handleResponseChange = (e) => {\r\n        setSelectedResponse((prevSelectedResponse) => ({\r\n            ...prevSelectedResponse,\r\n            response: e.target.value,\r\n        }));\r\n    };\r\n\r\n    // Função para salvar as alterações no companyResponses\r\n    const handleSaveResponse = async () => {\r\n        if (selectedResponse) {\r\n            // Atualiza a resposta localmente\r\n            const updatedResponses = companyResponses.map((response) =>\r\n                response.questionIdentifier === selectedResponse.questionIdentifier\r\n                    ? { ...response, response: selectedResponse.response } // Atualiza apenas a mensagem de resposta\r\n                    : response\r\n            );\r\n            setCompanyResponses(updatedResponses);\r\n\r\n            try {\r\n                // Chama a API para atualizar o backend\r\n                await updateQuestionResponses(empresaObjId, updatedResponses);\r\n                toast(\"Respostas atualizadas com sucesso!\", { autoClose: 2000, type: \"success\" });\r\n            } catch (error) {\r\n                console.error(\"Erro ao atualizar respostas:\", error);\r\n                toast(\"Ocorreu um erro ao salvar as respostas.\", { autoClose: 2000, type: \"error\" });\r\n            }\r\n        }\r\n    };\r\n\r\n\r\n    // Função genérica para inserir texto no cursor\r\n    const insertAtCursor = (textarea, text) => {\r\n        if (textarea) {\r\n            const startPos = textarea.selectionStart;\r\n            const endPos = textarea.selectionEnd;\r\n            const value = textarea.value;\r\n\r\n            // Atualiza o texto no campo com o novo valor\r\n            textarea.value = value.substring(0, startPos) + text + value.substring(endPos);\r\n\r\n            // Mantém o cursor após o texto inserido\r\n            textarea.selectionStart = textarea.selectionEnd = startPos + text.length;\r\n        }\r\n    };\r\n\r\n    // Funções específicas para cada botão\r\n    const handleInsertNomeCliente = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"{nome_cliente}\");\r\n    };\r\n\r\n    const handleInsertLinkCardapio = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"{link}\");\r\n    };\r\n\r\n    const handleInsertSaudacao = () => {\r\n        const textarea = document.querySelector(\".roboCfg-textarea\");\r\n        insertAtCursor(textarea, \"Agradecemos a preferência\");\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <PermissionGate permissions={['default']}>\r\n\r\n                <Teste sidebar={sidebar}>\r\n                    <div className=\"w-100 p-4\">\r\n                        {/*<form onSubmit={handleSubmitButton}>*/}\r\n                        <form /*onSubmit={formik.handleSubmit}*/ >\r\n\r\n                            <div className=\"form-header\" style={{ marginBottom: \"0px\" }}>\r\n                                <div className=\"title\">\r\n                                    <h1>Configurações do Robô</h1>\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div style={{ display: \"flex\", justifyContent: \"right\"/*, height:\"80px\"*/ }}>\r\n\r\n                                {/*<div className=\"div-buttons\">\r\n                                    <div className=\"continue-button\">\r\n                                        {tela === \"tela1\" ?\r\n                                            <button type=\"button\" onClick={saveChanges} disabled={isSubmitting}>\r\n                                                <AiIcons.AiOutlineSave style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Salvar</a>\r\n                                            </button>\r\n                                            :\r\n                                            <button type=\"button\" disabled={isSubmitting}>\r\n                                                <AiIcons.AiOutlineSave style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Salvar</a>\r\n                                            </button>\r\n                                        }\r\n                                    </div>\r\n\r\n                                    <div className=\"back-button\">\r\n                                        <button onClick={handleBack}>\r\n                                            <SlIcons.SlActionUndo style={{ color: \"#ff4c4c\", marginRight: \"5px\", fontSize: \"18px\", marginBottom: \"2px\" }} /><a >Voltar</a>\r\n                                        </button>\r\n                                    </div>\r\n                                </div>*/}\r\n\r\n                            </div>\r\n\r\n                            <div className=\"contentItemComplete flex-column flex-md-row\">\r\n                                <div className=\"input-group inputGroup-etapasItem\" style={{ height: 50 }}>\r\n                                    <div className={tela === \"tela1\" ? \"etapasAddItem etapaAtiva\" : \"etapasAddItem\"}\r\n                                        style={{ borderBottom: '1px solid lightgray' }} onClick={() => setTela(\"tela1\")}\r\n                                    >\r\n                                        <label>1. Personalizar Mensagens</label>\r\n                                    </div>                                   \r\n                                </div>\r\n\r\n                                <div className=\"input-group mh-100\" style={{ maxWidth: \"80%\" }}>\r\n\r\n                                    <div className=\"formGroupRow\">\r\n                                        {tela === \"tela1\" &&\r\n                                            <div className=\"roboCfg-container\">\r\n                                                <div className=\"roboCfg-sidebar\">\r\n                                                    <h3>Mensagens</h3>\r\n                                                    <ul className=\"roboCfg-message-list\">\r\n                                                        {companyResponses.map((message, index) => (\r\n                                                            <li\r\n                                                                key={index}\r\n                                                                className={`roboCfg-message-item ${selectedResponse?.questionIdentifier === message.questionIdentifier ? 'roboCfg-selected' : ''}`}\r\n                                                                onClick={() => handleSelectQuestionAndAnswer(message)}\r\n                                                            >\r\n                                                                {message.questionType}\r\n                                                                <div className=\"checkBoxContentMsg\">\r\n                                                                    {message.active ? \r\n                                                                        <div className=\"roboCfg-edit-button-ativo\">Ativo</div> \r\n                                                                        : \r\n                                                                        <div className=\"roboCfg-edit-button-inativo\">Inativo</div>\r\n                                                                    }\r\n                                                                    <div className=\"wrapper\">\r\n                                                                        <div className=\"switch_box box_1\">\r\n                                                                            <input\r\n                                                                                type=\"checkbox\"\r\n                                                                                className=\"switch_1\"\r\n                                                                                checked={message.active || false}\r\n                                                                                // Dentro do checkbox\r\n                                                                                onChange={(e) => {\r\n                                                                                    const isActive = e.target.checked;\r\n                                                                                \r\n                                                                                    // Chamar a função debounce para evitar múltiplas chamadas ao backend\r\n                                                                                    debouncedUpdateQuestionActive(empresaObjId, message.questionIdentifier, isActive, setCompanyResponses, companyResponses);\r\n                                                                                }}\r\n                                                                            />\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </li>\r\n                                                        ))}\r\n                                                    </ul>\r\n                                                </div>\r\n                                                <div className=\"roboCfg-main\">\r\n                                                    <div className=\"roboCfg-preview\" style={{ backgroundImage: `url(${backgroundWhatsApp})` }}>\r\n                                                        <div className=\"roboCfg-chat\">\r\n\r\n                                                            {selectedResponse?.question &&\r\n                                                                <Message fromMe={false}>\r\n                                                                    <p>{selectedResponse?.question}</p>\r\n                                                                    <span>12:00</span>\r\n                                                                </Message>\r\n                                                            }\r\n\r\n\r\n                                                            <Message fromMe={true}>\r\n                                                                <p>{selectedResponse?.response}</p>\r\n                                                                <span>12:01</span>\r\n                                                            </Message>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div className=\"roboCfg-editor\">\r\n                                                        <h4>Personalize a mensagem</h4>\r\n                                                        <textarea\r\n                                                            className=\"roboCfg-textarea\"\r\n                                                            value={selectedResponse?.response || \"\"}\r\n                                                            onChange={handleResponseChange} // Altera diretamente o response do selectedResponse\r\n                                                        />\r\n                                                        <div className=\"roboCfg-buttons\">\r\n                                                            <button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertNomeCliente} // Chama a função para Nome do cliente\r\n                                                            >\r\n                                                                Nome do cliente\r\n                                                            </button>\r\n                                                            <button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertLinkCardapio} // Chama a função para Link do cardápio\r\n                                                            >\r\n                                                                Link do cardápio\r\n                                                            </button>\r\n                                                            {/*<button\r\n                                                                className=\"roboCfg-button\"\r\n                                                                type=\"button\"\r\n                                                                onClick={handleInsertSaudacao} // Chama a função para Saudação\r\n                                                            >\r\n                                                                Saudação\r\n                                                            </button>*/}\r\n                                                        </div>\r\n\r\n                                                        <div className=\"roboCfg-save-cancel\">\r\n                                                            <button className=\"roboCfg-save-button\" type=\"button\" onClick={handleSaveResponse}>Salvar</button>\r\n                                                            {/*<button className=\"roboCfg-cancel-button\">Cancelar</button>*/}\r\n                                                        </div>\r\n\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        }\r\n                                        {/*tela === \"tela2\" && (\r\n                                            <div>NADA AQUI POR ENQUANTO</div>\r\n                                        )*/}\r\n                                    </div>\r\n\r\n                                </div>\r\n\r\n\r\n\r\n                            </div>\r\n\r\n                        </form>\r\n                    </div>\r\n                </Teste>\r\n\r\n            </PermissionGate>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default RoboCfg;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,aAAa;AACpB,SAASC,cAAc,QAAQ,iBAAiB;AAChD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,OAAOC,sBAAsB,MAAM,yCAAyC;AAC5E,OAAOC,uBAAuB,MAAM,0CAA0C;AAC9E,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAClE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,QAAQ,MAAM,WAAW;AAChC,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,oBAAoB;AACtH,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,mBAAmB,GAAGhB,MAAM,CAACiB,GAAG;AACtC;AACA,mBAAmB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;EAClB,MAAMC,SAAS,GAAG,uFAAuF;EACzG,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMC,IAAI,GAAGvB,QAAQ,CAACwB,GAAG,CAACC,OAAO,CAACL,aAAa,EAAED,SAAS,CAAC,CAACO,QAAQ,CAAC1B,QAAQ,CAAC2B,GAAG,CAACC,IAAI,CAAC;EACvF,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACR,IAAI,CAAC;EAClC,MAAMS,OAAO,GAAGX,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMW,YAAY,GAAGH,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC;EACxC,MAAME,YAAY,GAAGD,YAAY,CAACE,GAAG;EAErC,MAAM;IAAEvB;EAAQ,CAAC,GAAG3B,UAAU,CAACG,cAAc,CAAC;EAC9C,MAAMgD,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC;IAAEmE,QAAQ,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyE,YAAY,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAClC,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EAEpDE,SAAS,CAAC,MAAM;IACZ;IACA,MAAM4E,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAI;QACA;QACA,MAAMC,WAAW,GAAG,MAAMC,mBAAmB,CAAC9B,YAAY,CAAC;QAC3D,MAAM+B,oBAAoB,GAAG,MAAMC,mBAAmB,CAAChC,YAAY,CAAC;QACpEiC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEH,oBAAoB,CAACI,IAAI,CAACC,SAAS,CAAC;QACrEC,mBAAmB,CAACN,oBAAoB,CAACI,IAAI,CAACC,SAAS,CAAC;QACxDE,mBAAmB,CAACP,oBAAoB,CAACI,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;MAC/D,CAAC,CAAC,OAAOG,KAAK,EAAE;QACZN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD;MACJ;IACJ,CAAC;IAEDX,SAAS,CAAC,CAAC;EACf,CAAC,EAAE,CAACY,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf,MAAMC,6BAA6B,GAAGC,QAAQ,CAAC,OAAO1C,YAAY,EAAE2C,kBAAkB,EAAEC,QAAQ,EAAEP,mBAAmB,EAAEQ,gBAAgB,KAAK;IACxI,IAAI;MACA;MACA,MAAMC,oBAAoB,CAAC9C,YAAY,EAAE2C,kBAAkB,EAAEC,QAAQ,CAAC;;MAEtE;MACA,MAAMG,gBAAgB,GAAGF,gBAAgB,CAACG,GAAG,CAAE9B,QAAQ,IACnDA,QAAQ,CAACyB,kBAAkB,KAAKA,kBAAkB,GAC5C;QAAE,GAAGzB,QAAQ;QAAE+B,MAAM,EAAEL;MAAS,CAAC,GACjC1B,QACV,CAAC;MACDmB,mBAAmB,CAACU,gBAAgB,CAAC;MACrCd,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAC1D,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZN,OAAO,CAACM,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC9D;EACJ,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;EAGT,MAAMW,UAAU,GAAGA,CAAA,KAAM;IACrB;IACAhD,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,MAAMiD,6BAA6B,GAAIC,OAAO,IAAK;IAC/Cd,mBAAmB,CAACc,OAAO,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIC,CAAC,IAAK;IAChChB,mBAAmB,CAAEiB,oBAAoB,KAAM;MAC3C,GAAGA,oBAAoB;MACvBrC,QAAQ,EAAEoC,CAAC,CAACE,MAAM,CAACC;IACvB,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIC,gBAAgB,EAAE;MAClB;MACA,MAAMZ,gBAAgB,GAAGF,gBAAgB,CAACG,GAAG,CAAE9B,QAAQ,IACnDA,QAAQ,CAACyB,kBAAkB,KAAKgB,gBAAgB,CAAChB,kBAAkB,GAC7D;QAAE,GAAGzB,QAAQ;QAAEA,QAAQ,EAAEyC,gBAAgB,CAACzC;MAAS,CAAC,CAAC;MAAA,EACrDA,QACV,CAAC;MACDmB,mBAAmB,CAACU,gBAAgB,CAAC;MAErC,IAAI;QACA;QACA,MAAMa,uBAAuB,CAAC5D,YAAY,EAAE+C,gBAAgB,CAAC;QAC7D5E,KAAK,CAAC,oCAAoC,EAAE;UAAE0F,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC;MACrF,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACZN,OAAO,CAACM,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDpE,KAAK,CAAC,yCAAyC,EAAE;UAAE0F,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAQ,CAAC,CAAC;MACxF;IACJ;EACJ,CAAC;;EAGD;EACA,MAAMC,cAAc,GAAGA,CAACC,QAAQ,EAAEC,IAAI,KAAK;IACvC,IAAID,QAAQ,EAAE;MACV,MAAME,QAAQ,GAAGF,QAAQ,CAACG,cAAc;MACxC,MAAMC,MAAM,GAAGJ,QAAQ,CAACK,YAAY;MACpC,MAAMZ,KAAK,GAAGO,QAAQ,CAACP,KAAK;;MAE5B;MACAO,QAAQ,CAACP,KAAK,GAAGA,KAAK,CAACa,SAAS,CAAC,CAAC,EAAEJ,QAAQ,CAAC,GAAGD,IAAI,GAAGR,KAAK,CAACa,SAAS,CAACF,MAAM,CAAC;;MAE9E;MACAJ,QAAQ,CAACG,cAAc,GAAGH,QAAQ,CAACK,YAAY,GAAGH,QAAQ,GAAGD,IAAI,CAACM,MAAM;IAC5E;EACJ,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMR,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,gBAAgB,CAAC;EAC9C,CAAC;EAED,MAAMW,wBAAwB,GAAGA,CAAA,KAAM;IACnC,MAAMX,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,QAAQ,CAAC;EACtC,CAAC;EAED,MAAMY,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,MAAMZ,QAAQ,GAAGS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;IAC5DX,cAAc,CAACC,QAAQ,EAAE,2BAA2B,CAAC;EACzD,CAAC;EAED,oBACI3F,OAAA,CAAAE,SAAA;IAAAsG,QAAA,eACIxG,OAAA,CAAClB,cAAc;MAAC2H,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAD,QAAA,eAErCxG,OAAA,CAAC0G,KAAK;QAACrG,OAAO,EAAEA,OAAQ;QAAAmG,QAAA,eACpBxG,OAAA;UAAK2G,SAAS,EAAC,WAAW;UAAAH,QAAA,eAEtBxG,OAAA;YAAAwG,QAAA,gBAEIxG,OAAA;cAAK2G,SAAS,EAAC,aAAa;cAACC,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAL,QAAA,eACxDxG,OAAA;gBAAK2G,SAAS,EAAC,OAAO;gBAAAH,QAAA,eAClBxG,OAAA;kBAAAwG,QAAA,EAAI;gBAAqB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENjH,OAAA;cAAK4G,KAAK,EAAE;gBAAEM,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,OAAO;cAAoB;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBvE,CAAC,eAENjH,OAAA;cAAK2G,SAAS,EAAC,6CAA6C;cAAAH,QAAA,gBACxDxG,OAAA;gBAAK2G,SAAS,EAAC,mCAAmC;gBAACC,KAAK,EAAE;kBAAEQ,MAAM,EAAE;gBAAG,CAAE;gBAAAZ,QAAA,eACrExG,OAAA;kBAAK2G,SAAS,EAAEU,IAAI,KAAK,OAAO,GAAG,0BAA0B,GAAG,eAAgB;kBAC5ET,KAAK,EAAE;oBAAEU,YAAY,EAAE;kBAAsB,CAAE;kBAACC,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAAC,OAAO,CAAE;kBAAAhB,QAAA,eAEhFxG,OAAA;oBAAAwG,QAAA,EAAO;kBAAyB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENjH,OAAA;gBAAK2G,SAAS,EAAC,oBAAoB;gBAACC,KAAK,EAAE;kBAAEa,QAAQ,EAAE;gBAAM,CAAE;gBAAAjB,QAAA,eAE3DxG,OAAA;kBAAK2G,SAAS,EAAC,cAAc;kBAAAH,QAAA,EACxBa,IAAI,KAAK,OAAO,iBACbrH,OAAA;oBAAK2G,SAAS,EAAC,mBAAmB;oBAAAH,QAAA,gBAC9BxG,OAAA;sBAAK2G,SAAS,EAAC,iBAAiB;sBAAAH,QAAA,gBAC5BxG,OAAA;wBAAAwG,QAAA,EAAI;sBAAS;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClBjH,OAAA;wBAAI2G,SAAS,EAAC,sBAAsB;wBAAAH,QAAA,EAC/BhC,gBAAgB,CAACG,GAAG,CAAC,CAACI,OAAO,EAAE2C,KAAK;0BAAA,IAAAC,iBAAA;0BAAA,oBACjC3H,OAAA;4BAEI2G,SAAS,EAAE,wBAAwB,EAAAgB,iBAAA,GAAArC,gBAAgB,cAAAqC,iBAAA,uBAAhBA,iBAAA,CAAkBrD,kBAAkB,MAAKS,OAAO,CAACT,kBAAkB,GAAG,kBAAkB,GAAG,EAAE,EAAG;4BACnIiD,OAAO,EAAEA,CAAA,KAAMzC,6BAA6B,CAACC,OAAO,CAAE;4BAAAyB,QAAA,GAErDzB,OAAO,CAAC6C,YAAY,eACrB5H,OAAA;8BAAK2G,SAAS,EAAC,oBAAoB;8BAAAH,QAAA,GAC9BzB,OAAO,CAACH,MAAM,gBACX5E,OAAA;gCAAK2G,SAAS,EAAC,2BAA2B;gCAAAH,QAAA,EAAC;8BAAK;gCAAAM,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,gBAEtDjH,OAAA;gCAAK2G,SAAS,EAAC,6BAA6B;gCAAAH,QAAA,EAAC;8BAAO;gCAAAM,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,eAE9DjH,OAAA;gCAAK2G,SAAS,EAAC,SAAS;gCAAAH,QAAA,eACpBxG,OAAA;kCAAK2G,SAAS,EAAC,kBAAkB;kCAAAH,QAAA,eAC7BxG,OAAA;oCACIyF,IAAI,EAAC,UAAU;oCACfkB,SAAS,EAAC,UAAU;oCACpBkB,OAAO,EAAE9C,OAAO,CAACH,MAAM,IAAI;oCAC3B;oCAAA;oCACAkD,QAAQ,EAAG7C,CAAC,IAAK;sCACb,MAAMV,QAAQ,GAAGU,CAAC,CAACE,MAAM,CAAC0C,OAAO;;sCAEjC;sCACAzD,6BAA6B,CAACzC,YAAY,EAAEoD,OAAO,CAACT,kBAAkB,EAAEC,QAAQ,EAAEP,mBAAmB,EAAEQ,gBAAgB,CAAC;oCAC5H;kCAAE;oCAAAsC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACL;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACD;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACL,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC;0BAAA,GA3BDS,KAAK;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OA4BV,CAAC;wBAAA,CACR;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNjH,OAAA;sBAAK2G,SAAS,EAAC,cAAc;sBAAAH,QAAA,gBACzBxG,OAAA;wBAAK2G,SAAS,EAAC,iBAAiB;wBAACC,KAAK,EAAE;0BAAEmB,eAAe,EAAE,OAAOC,kBAAkB;wBAAI,CAAE;wBAAAxB,QAAA,eACtFxG,OAAA;0BAAK2G,SAAS,EAAC,cAAc;0BAAAH,QAAA,GAExB,EAAAhG,kBAAA,GAAA8E,gBAAgB,cAAA9E,kBAAA,uBAAhBA,kBAAA,CAAkBoC,QAAQ,kBACvB5C,OAAA,CAACiI,OAAO;4BAACC,MAAM,EAAE,KAAM;4BAAA1B,QAAA,gBACnBxG,OAAA;8BAAAwG,QAAA,GAAA/F,kBAAA,GAAI6E,gBAAgB,cAAA7E,kBAAA,uBAAhBA,kBAAA,CAAkBmC;4BAAQ;8BAAAkE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnCjH,OAAA;8BAAAwG,QAAA,EAAM;4BAAK;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC,eAIdjH,OAAA,CAACiI,OAAO;4BAACC,MAAM,EAAE,IAAK;4BAAA1B,QAAA,gBAClBxG,OAAA;8BAAAwG,QAAA,GAAA9F,kBAAA,GAAI4E,gBAAgB,cAAA5E,kBAAA,uBAAhBA,kBAAA,CAAkBmC;4BAAQ;8BAAAiE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnCjH,OAAA;8BAAAwG,QAAA,EAAM;4BAAK;8BAAAM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNjH,OAAA;wBAAK2G,SAAS,EAAC,gBAAgB;wBAAAH,QAAA,gBAC3BxG,OAAA;0BAAAwG,QAAA,EAAI;wBAAsB;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC/BjH,OAAA;0BACI2G,SAAS,EAAC,kBAAkB;0BAC5BvB,KAAK,EAAE,EAAAzE,kBAAA,GAAA2E,gBAAgB,cAAA3E,kBAAA,uBAAhBA,kBAAA,CAAkBkC,QAAQ,KAAI,EAAG;0BACxCiF,QAAQ,EAAE9C,oBAAqB,CAAC;wBAAA;0BAAA8B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC,CAAC,eACFjH,OAAA;0BAAK2G,SAAS,EAAC,iBAAiB;0BAAAH,QAAA,gBAC5BxG,OAAA;4BACI2G,SAAS,EAAC,gBAAgB;4BAC1BlB,IAAI,EAAC,QAAQ;4BACb8B,OAAO,EAAEpB,uBAAwB,CAAC;4BAAA;4BAAAK,QAAA,EACrC;0BAED;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACTjH,OAAA;4BACI2G,SAAS,EAAC,gBAAgB;4BAC1BlB,IAAI,EAAC,QAAQ;4BACb8B,OAAO,EAAEjB,wBAAyB,CAAC;4BAAA;4BAAAE,QAAA,EACtC;0BAED;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAQR,CAAC,eAENjH,OAAA;0BAAK2G,SAAS,EAAC,qBAAqB;0BAAAH,QAAA,eAChCxG,OAAA;4BAAQ2G,SAAS,EAAC,qBAAqB;4BAAClB,IAAI,EAAC,QAAQ;4BAAC8B,OAAO,EAAElC,kBAAmB;4BAAAmB,QAAA,EAAC;0BAAM;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEjG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEI;EAAC,gBACnB,CAAC;AAEX,CAAC;AAAA1G,EAAA,CA5SKD,OAAO;EAAA,QAUQ1B,WAAW;AAAA;AAAAuJ,EAAA,GAV1B7H,OAAO;AA8Sb,eAAeA,OAAO;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}