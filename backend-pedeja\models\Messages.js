const mongoose = require('mongoose')


const Messages = mongoose.model('Messages',{
    id_message: String,
    id_empresa: Number,
    empresaObjId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Empresa',
        required: true,
    },
    leadChannel: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'LeadChannel',
        required: true,
    },
    whatsapp_number: String,
    lead_number: String,
    text: String,
    message: String, // Campo unificado para o conteúdo da mensagem
    channel: String,
    channel_id: String,
    type: String,
    messageType: String, // Tipo da mensagem (text, media, etc.)
    fromMe: Boolean,
    messageDate: Date,
    statusMessage: Number,
    whatsapp_message_id: String, // Para Evolution API - ID único da mensagem
    created_at: Date,
    updated_at: Date,
    createdAt: Date,
    deletedAt: Date
})

module.exports = Messages