.printer-config-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

.form-header {
    text-align: center;
    margin-bottom: 40px;
}

.form-header .title h1 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.header-icon {
    color: #3498db;
    font-size: 2.2rem;
}

.subtitle {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin: 0;
    max-width: 600px;
    margin: 0 auto;
}

.info-banner {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8fd 100%);
    border-left: 4px solid #2196f3;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.info-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.info-icon {
    color: #2196f3;
    font-size: 1.5rem;
    margin-top: 2px;
    flex-shrink: 0;
}

.info-content h3 {
    color: #1976d2;
    font-size: 1.1rem;
    margin: 0 0 8px 0;
}

.info-content p {
    color: #424242;
    margin: 0;
    line-height: 1.5;
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 40px;
}

.step-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.step-card.active {
    border-color: #3498db;
    box-shadow: 0 4px 20px rgba(52, 152, 219, 0.15);
}

.step-card.disabled {
    opacity: 0.6;
    background: #f8f9fa;
}

.step-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ecf0f1;
    color: #7f8c8d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.step-card.active .step-number {
    background: #3498db;
    color: white;
}

.step-number.completed {
    background: #27ae60;
    color: white;
}

.step-header h2 {
    color: #2c3e50;
    font-size: 1.3rem;
    margin: 0;
}

.step-content p {
    color: #5d6d7e;
    line-height: 1.6;
    margin-bottom: 15px;
}

.step-actions {
    margin: 20px 0;
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.download-button, .test-button, .already-downloaded-button {
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.download-button, .test-button {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.already-downloaded-button {
    background: linear-gradient(135deg, #8e44ad 0%, #732d91 100%);
}

.download-button:hover, .test-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.already-downloaded-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(142, 68, 173, 0.3);
}

.test-button {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.test-button:hover {
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.test-button:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.button-icon {
    font-size: 1.1rem;
}

.step-info {
    margin-top: 15px;
}

.step-info small {
    color: #7f8c8d;
    display: flex;
    align-items: center;
    gap: 6px;
}

.os-icon, .check-icon {
    color: #3498db;
}

.installation-tips {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.installation-tips h4 {
    color: #2c3e50;
    font-size: 1rem;
    margin: 0 0 10px 0;
}

.installation-tips ul {
    margin: 0;
    padding-left: 20px;
}

.installation-tips li {
    color: #5d6d7e;
    margin-bottom: 5px;
    line-height: 1.4;
}

.success-card {
    background: linear-gradient(135deg, #d5f4e6 0%, #e8f8f0 100%);
    border: 2px solid #27ae60;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.success-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.success-icon {
    font-size: 3rem;
    color: #27ae60;
}

.success-content h2 {
    color: #27ae60;
    font-size: 1.5rem;
    margin: 0;
}

.success-content p {
    color: #2c3e50;
    max-width: 500px;
    margin: 0;
    line-height: 1.6;
}

.support-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    text-align: center;
}

.support-section h3 {
    color: #2c3e50;
    font-size: 1.2rem;
    margin: 0 0 10px 0;
}

.support-section p {
    color: #7f8c8d;
    margin: 0 0 20px 0;
}

.support-button {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.support-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

/* Responsividade */
@media (max-width: 768px) {
    .printer-config-container {
        padding: 15px;
    }
    
    .form-header .title h1 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 8px;
    }
    
    .info-content {
        flex-direction: column;
        gap: 10px;
    }
    
    .step-card {
        padding: 20px;
    }
    
    .step-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .step-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .success-content {
        gap: 10px;
    }
    
    .success-icon {
        font-size: 2.5rem;
    }
}