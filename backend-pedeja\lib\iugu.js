const fetch = require('node-fetch');
require('dotenv').config();
const Planos = require('../models/Planos')
const Assinatura = require('../models/Assinatura')

const apiToken = process.env.TOKEN_DEV_IUGU;

async function createIuguPlan(title, plan_identifier, tempo_duracao, plan_cycle, valor_plano, max_cycle) {
  const url = `https://api.iugu.com/v1/plans?api_token=${apiToken}`;
  const options = {
    method: 'POST',
    headers: {
      'accept': 'application/json',
      'content-type': 'application/json'
    },
    body: JSON.stringify({
      name: title,
      identifier: plan_identifier,
      interval: tempo_duracao,
      interval_type: plan_cycle,
      value_cents: valor_plano,
      max_cycles: max_cycle
    })
  };

  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Erro:', error);
    throw error;
  }
}

async function getIuguPlans() {
    try {
        const planos = await Planos.find(); // Obtém todos os planos
    
        if (planos && planos.length > 0) {
          return { planos };
        } else {
          return { msg: 'Nenhum plano encontrado!' };
        }
    } catch (error) {
        console.error('Erro ao obter os planos:', error);
        return { msg: 'Aconteceu um erro no servidor, tente novamente mais tarde!' };
    }
}

async function getFilteredIuguPlans() {
    try {
      const planos = await Planos.find({ access_type: { $ne: "free" } }); // Filtra os planos onde access_type é diferente de "free"
      
      if (planos && planos.length > 0) {
        return { planos };
      } else {
        return { msg: 'Nenhum plano encontrado!' };
      }
    } catch (error) {
      console.error('Erro ao obter os planos:', error);
      return { msg: 'Aconteceu um erro no servidor, tente novamente mais tarde!' };
    }
}

async function deleteIuguPlan(planId) {
    const url = `https://api.iugu.com/v1/plans/${planId}?api_token=${apiToken}`;
    const options = {
      method: 'DELETE',
      headers: {
        'accept': 'application/json'
      }
    };
  
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      // Excluir o plano do MongoDB usando o campo id_iugu
      const planoDeletado = await Planos.findOneAndDelete({ id_iugu: planId });
                
      if (!planoDeletado) {
          return { msg: 'Plano não encontrado no MongoDB' };
      }
      return { msg: 'Plano removido com sucesso' };
    } catch (error) {
      console.error('Erro ao remover o plano:', error);
      throw error;
    }
}

async function createIuguCustomer(email, name, cpf_cnpj, zip_code, number, rua, bairro) {
    // Função para remover máscara dos dados
    //const removeMask = (value) => value.replace(/[^\d]/g, '');

    // Remove máscaras do CPF/CNPJ e do ZIP code, se necessário
    //const cleanCpfCnpj = removeMask(cpf_cnpj);
    //const cleanZipCode = removeMask(zip_code);

    console.log("Dados do createIuguCustomer>>>",{email, name, cpf_cnpj, zip_code, number, rua, bairro});

    const url = `https://api.iugu.com/v1/customers?api_token=${apiToken}`;
    const options = {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'content-type': 'application/json'
      },
      body: JSON.stringify({
        email: email,
        name: name,
        cpf_cnpj: cpf_cnpj,
        zip_code: zip_code,
        number: number,
        street: rua,
        district: bairro
      })
    };
  
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`Erro HTTP! Status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Erro ao criar o cliente:', error);
      throw error;
    }
}

async function createIuguSubscription(plan_identifier, customer_id) {
    console.log("Dados utilizados na criação da assinatura:", {plan_identifier, customer_id})
    const url = `https://api.iugu.com/v1/subscriptions?api_token=${apiToken}`;
    const options = {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'content-type': 'application/json'
      },
      body: JSON.stringify({
        plan_identifier: plan_identifier,
        customer_id: customer_id
      })
    };
  
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`Erro HTTP! Status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Erro ao criar a assinatura:', error);
      throw error;
    }
}

async function deleteIuguSubscription(subscriptionId) {
    const url = `https://api.iugu.com/v1/subscriptions/${subscriptionId}?api_token=${apiToken}`;
    
    const options = {
      method: 'DELETE',
      headers: {
        'accept': 'application/json'
      }
    };
  
    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`Erro HTTP! Status: ${response.status}`);
      }
  
      const result = await response.json();
      return { msg: 'Assinatura removida com sucesso', result };
  
    } catch (error) {
      console.error('Erro ao remover a assinatura:', error);
      throw new Error('Erro ao remover a assinatura na Iugu');
    }
}

async function cancelIuguInvoice(invoiceId) {
    const url = `https://api.iugu.com/v1/invoices/${invoiceId}/cancel?api_token=${apiToken}`;
  
    const options = {
      method: 'PUT',
      headers: {
        'accept': 'application/json'
      }
    };
  
    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`Erro HTTP! Status: ${response.status}`);
      }
  
      const result = await response.json();
      return { msg: 'Fatura cancelada com sucesso', result };
  
    } catch (error) {
      console.error('Erro ao cancelar a fatura:', error);
      throw new Error('Erro ao cancelar a fatura na Iugu');
    }
}

async function checkUserInvoices(customerId) {
    try {
        // Busca a assinatura correspondente ao customerId no banco de dados
        const assinatura = await Assinatura.findOne({ "assinatura_obj.customer_id": customerId });
    
        if (!assinatura) {
          throw new Error('Assinatura não encontrada para este customerId');
        }
    
        // Verifica se há assinatura ativa
        const { active } = assinatura.assinatura_obj;
        const hasActiveSubscription = active ? true : false;
    
        //if (!recent_invoices || recent_invoices.length === 0) {
        //  throw new Error('Nenhuma fatura encontrada para este cliente');
        //}
    
        // Verifica se há alguma fatura com status 'pending' ou 'paid'
        //const hasActiveInvoice = recent_invoices.some(invoice =>
        //  invoice.status === 'pending' || invoice.status === 'paid'
        //);
    
        return hasActiveSubscription;
    } catch (error) {
        console.error('Erro ao verificar as faturas no banco de dados:', error);
        throw new Error('Erro ao verificar as faturas no MongoDB');
    }
}

async function getCustomerData(customerId) {
  const url = `https://api.iugu.com/v1/customers/${customerId}?api_token=${apiToken}`;

  const options = {
    method: 'GET',
    headers: {
      'accept': 'application/json'
    }
  };

  try {
    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`Erro HTTP! Status: ${response.status}`);
    }

    const result = await response.json();
    return { msg: 'Dados do cliente obtidos com sucesso', result };

  } catch (error) {
    console.error('Erro ao obter os dados do cliente:', error);
    throw new Error('Erro ao obter os dados do cliente na Iugu');
  }
}

async function getInvoiceByID(invoiceId) {
  const url = `https://api.iugu.com/v1/invoices/${invoiceId}?api_token=${apiToken}`;

  const options = {
    method: 'GET',
    headers: {
      'accept': 'application/json'
    }
  };

  try {
    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`Erro HTTP! Status: ${response.status}`);
    }

    const result = await response.json();
    return { msg: 'Dados da fatura obtidos com sucesso', result };

  } catch (error) {
    console.error('Erro ao obter os dados da fatura:', error);
    throw new Error('Erro ao obter os dados da fatura na Iugu');
  }
}

async function getSubscriptionByID(subscriptionId) {
  const url = `https://api.iugu.com/v1/subscriptions/${subscriptionId}?api_token=${apiToken}`;

  const options = {
    method: 'GET',
    headers: {
      'accept': 'application/json',
    },
  };

  try {
    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`Erro HTTP! Status: ${response.status}`);
    }

    const result = await response.json();
    return { msg: 'Dados da assinatura obtidos com sucesso', result };

  } catch (error) {
    console.error('Erro ao obter os dados da assinatura:', error);
    throw new Error('Erro ao obter os dados da assinatura na Iugu');
  }
}
  

module.exports = { 
    createIuguPlan,
    getIuguPlans,
    getFilteredIuguPlans,
    deleteIuguPlan,
    createIuguCustomer,
    createIuguSubscription,
    deleteIuguSubscription,
    cancelIuguInvoice,
    checkUserInvoices,
    getCustomerData,
    getInvoiceByID,
    getSubscriptionByID
};