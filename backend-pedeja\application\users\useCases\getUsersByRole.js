class GetUserByRoleUseCase {
  constructor(userDAO) {
    this.userDAO = userDAO;
  }

  async execute(empresaVinculo, role) {
    try {
      const users = await this.userDAO.getUsersByRole(empresaVinculo, role);

      return {
        status: true,
        data: users,
      };
    } catch (error) {
      return {
        status: false,
        message: "It was not possible to get users by role.",
      };
    }
  }
}

module.exports = GetUserByRoleUseCase;
