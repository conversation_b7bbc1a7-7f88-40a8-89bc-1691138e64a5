{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useRef, useCallback } from 'react';\nimport { toast } from 'react-toastify';\nimport { buscarFilaAtendimento as apiBuscarFila, iniciarAtendimentoFila, finalizarAtendimentoFila, cancelarAtendimentoFila, limparAtendimentosFinalizados as apiLimparFinalizados } from '../../services/api';\n\n/**\r\n * Hook customizado para gerenciar fila de atendimento humano\r\n * Combina WebSocket (tempo real) + polling inteligente (confiabilidade)\r\n * \r\n * 🔌 Quando socket conectado: Usa principalmente WebSocket + polling raro (10min)\r\n * 📡 Quando socket desconectado: Polling frequente de backup (2min)\r\n * \r\n * 🚀 ESTRATÉGIA DE ATUALIZAÇÃO INSTANTÂNEA:\r\n * - Socket recebe atendimento → Adiciona temporariamente (feedback visual)\r\n * - Após 300ms → Busca fila completa (IDs reais do banco)\r\n * - Usuário clica resolver → Já tem dados atualizados, sem espera frustrante!\r\n * \r\n * ⚠️ FALLBACK PARA IDs TEMPORÁRIOS:\r\n * - Se ainda existir ID temporário, busca automaticamente o real\r\n * - Operações sempre tentam resolver automaticamente\r\n */\nconst useFilaAtendimento = (empresaId, socket) => {\n  _s();\n  const [atendimentosPendentes, setAtendimentosPendentes] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Ref para evitar chamadas desnecessárias\n  const lastFetchRef = useRef(Date.now());\n  const intervalRef = useRef(null);\n\n  /**\r\n   * 🔍 VERIFICAR SE É ID TEMPORÁRIO\r\n   */\n  const isTemporaryId = useCallback(id => {\n    return typeof id === 'string' && id.startsWith('temp-');\n  }, []);\n\n  /**\r\n   * 🔍 BUSCAR FILA DE ATENDIMENTO DO BANCO DE DADOS\r\n   */\n  const buscarFilaAtendimento = useCallback(async () => {\n    if (!empresaId) return;\n    try {\n      setIsLoading(true);\n      setError(null);\n      console.log('🔍 Buscando fila de atendimento do banco para empresa:', empresaId);\n      const response = await apiBuscarFila(empresaId);\n      if (response.data.status === 200) {\n        const {\n          atendimentos,\n          total_pendentes\n        } = response.data.data;\n        console.log('✅ Fila de atendimento obtida do banco:', {\n          total_pendentes,\n          atendimentos: atendimentos.length\n        });\n\n        // Converter para formato compatível com o frontend existente\n        const atendimentosFormatados = atendimentos.map(item => ({\n          ...item,\n          atendimento_id: item._id || item.atendimento_id,\n          company_id: empresaId\n        }));\n        setAtendimentosPendentes(atendimentosFormatados);\n        lastFetchRef.current = Date.now();\n        return atendimentosFormatados;\n      }\n    } catch (err) {\n      console.error('❌ Erro ao buscar fila de atendimento:', err);\n      setError(err.message || 'Erro ao carregar fila de atendimento');\n\n      // Toast apenas em casos críticos, não em atualizações periódicas\n      if (atendimentosPendentes.length === 0) {\n        toast('Erro ao carregar fila de atendimento', {\n          type: 'error'\n        });\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  }, [empresaId]);\n\n  /**\r\n   * 🔄 INICIAR ATENDIMENTO\r\n   */\n  const iniciarAtendimento = useCallback(async (atendimentoId, userData) => {\n    try {\n      console.log('🔄 Iniciando atendimento:', atendimentoId);\n\n      // ⚠️ VALIDAÇÃO: Se ainda for ID temporário, buscar fila uma última vez\n      if (isTemporaryId(atendimentoId)) {\n        console.log('⚠️ ID temporário detectado, buscando fila atualizada...');\n        await buscarFilaAtendimento();\n\n        // Encontrar o atendimento real na lista atualizada\n        const atendimentoReal = atendimentosPendentes.find(item => !isTemporaryId(item.atendimento_id) && (item.lead_id === (userData === null || userData === void 0 ? void 0 : userData.lead_id) || item.celular === (userData === null || userData === void 0 ? void 0 : userData.celular)));\n        if (atendimentoReal) {\n          console.log('✅ Encontrado atendimento real:', atendimentoReal.atendimento_id);\n          // Recursivamente chamar com o ID real\n          return iniciarAtendimento(atendimentoReal.atendimento_id, userData);\n        } else {\n          toast('Atendimento não encontrado, tente novamente', {\n            type: 'error'\n          });\n          return false;\n        }\n      }\n      const response = await iniciarAtendimentoFila(empresaId, atendimentoId, userData);\n      if (response.data.status === 200) {\n        // Remover da lista de pendentes\n        setAtendimentosPendentes(prev => prev.filter(item => item.atendimento_id !== atendimentoId));\n        toast('Atendimento iniciado!', {\n          type: 'success'\n        });\n        return true;\n      }\n    } catch (err) {\n      console.error('❌ Erro ao iniciar atendimento:', err);\n      toast('Erro ao iniciar atendimento', {\n        type: 'error'\n      });\n      return false;\n    }\n  }, [empresaId, buscarFilaAtendimento, atendimentosPendentes, isTemporaryId]);\n\n  /**\r\n   * ✅ FINALIZAR ATENDIMENTO\r\n   */\n  const finalizarAtendimento = useCallback(async (atendimentoId, observacoes = '') => {\n    try {\n      console.log('✅ Finalizando atendimento:', atendimentoId);\n\n      // ⚠️ VALIDAÇÃO: Se ainda for ID temporário, buscar fila uma última vez\n      if (isTemporaryId(atendimentoId)) {\n        console.log('⚠️ ID temporário detectado, buscando fila atualizada...');\n        await buscarFilaAtendimento();\n\n        // Para finalizar, tentar encontrar pelo ID temporário ou buscar o primeiro disponível\n        const atendimentoReal = atendimentosPendentes.find(item => !isTemporaryId(item.atendimento_id));\n        if (atendimentoReal) {\n          console.log('✅ Usando primeiro atendimento real encontrado:', atendimentoReal.atendimento_id);\n          // Recursivamente chamar com o ID real\n          return finalizarAtendimento(atendimentoReal.atendimento_id, observacoes);\n        } else {\n          toast('Atendimento não encontrado, tente novamente', {\n            type: 'error'\n          });\n          return false;\n        }\n      }\n      const response = await finalizarAtendimentoFila(empresaId, atendimentoId, observacoes);\n      if (response.data.status === 200) {\n        // Remover da lista de pendentes\n        setAtendimentosPendentes(prev => prev.filter(item => item.atendimento_id !== atendimentoId));\n        toast('Atendimento finalizado!', {\n          type: 'success'\n        });\n        return true;\n      }\n    } catch (err) {\n      console.error('❌ Erro ao finalizar atendimento:', err);\n      toast('Erro ao finalizar atendimento', {\n        type: 'error'\n      });\n      return false;\n    }\n  }, [empresaId, buscarFilaAtendimento, atendimentosPendentes, isTemporaryId]);\n\n  /**\r\n   * ❌ CANCELAR ATENDIMENTO\r\n   */\n  const cancelarAtendimento = useCallback(async (atendimentoId, motivo = '', showToast = true) => {\n    try {\n      console.log('❌ Cancelando atendimento:', atendimentoId);\n\n      // ⚠️ VALIDAÇÃO: Verificar se é ID temporário\n      if (isTemporaryId(atendimentoId)) {\n        console.log('⚠️ ID temporário detectado, removendo da lista local apenas');\n\n        // Para IDs temporários, apenas remove da lista local\n        setAtendimentosPendentes(prev => prev.filter(item => item.atendimento_id !== atendimentoId));\n        if (showToast) {\n          toast('Atendimento removido', {\n            type: 'info'\n          });\n        }\n        return true;\n      }\n      const response = await cancelarAtendimentoFila(empresaId, atendimentoId, motivo);\n      if (response.data.status === 200) {\n        // Remover da lista de pendentes\n        setAtendimentosPendentes(prev => prev.filter(item => item.atendimento_id !== atendimentoId));\n\n        // Só mostra toast se solicitado (padrão é mostrar)\n        if (showToast) {\n          toast('Atendimento cancelado', {\n            type: 'info'\n          });\n        }\n        return true;\n      }\n    } catch (err) {\n      console.error('❌ Erro ao cancelar atendimento:', err);\n      // Toast de erro sempre mostra, independente do parâmetro\n      if (showToast) {\n        toast('Erro ao cancelar atendimento', {\n          type: 'error'\n        });\n      }\n      return false;\n    }\n  }, [empresaId]);\n\n  /**\r\n   * 🧹 LIMPAR ATENDIMENTOS FINALIZADOS (limpeza periódica)\r\n   */\n  const limparAtendimentosFinalizados = useCallback(async () => {\n    try {\n      console.log('🧹 Limpando atendimentos finalizados...');\n      await apiLimparFinalizados(empresaId);\n      console.log('✅ Limpeza realizada com sucesso');\n    } catch (err) {\n      console.error('⚠️ Erro na limpeza (não crítico):', err);\n    }\n  }, [empresaId]);\n\n  /**\r\n   * 🚀 CANCELAR TODOS OS ATENDIMENTOS (operação em lote)\r\n   */\n  const cancelarTodosAtendimentos = useCallback(async () => {\n    const totalAtendimentos = atendimentosPendentes.length;\n    if (totalAtendimentos === 0) {\n      return {\n        sucesso: true,\n        total: 0,\n        cancelados: 0\n      };\n    }\n    try {\n      console.log(`🚀 Cancelando ${totalAtendimentos} atendimentos em lote...`);\n\n      // Cancelar todos sem mostrar toast individual\n      const promessas = atendimentosPendentes.map(atendimento => cancelarAtendimento(atendimento.atendimento_id, 'Resolvido em lote pelo usuário', false));\n      const resultados = await Promise.allSettled(promessas);\n\n      // Contar sucessos e falhas\n      const cancelados = resultados.filter(r => r.status === 'fulfilled' && r.value === true).length;\n      const falharam = totalAtendimentos - cancelados;\n      console.log(`✅ Resultado do lote: ${cancelados} cancelados, ${falharam} falharam`);\n      return {\n        sucesso: falharam === 0,\n        total: totalAtendimentos,\n        cancelados,\n        falharam\n      };\n    } catch (err) {\n      console.error('❌ Erro na operação em lote:', err);\n      return {\n        sucesso: false,\n        total: totalAtendimentos,\n        cancelados: 0,\n        falharam: totalAtendimentos,\n        erro: err.message\n      };\n    }\n  }, [atendimentosPendentes, cancelarAtendimento]);\n\n  /**\r\n   * 🔄 ATUALIZAR FILA (manual)\r\n   */\n  const atualizarFila = useCallback(() => {\n    buscarFilaAtendimento();\n  }, [buscarFilaAtendimento]);\n\n  /**\r\n   * ➕ ADICIONAR ATENDIMENTO VIA SOCKET (TEMPORÁRIO)\r\n   * Adiciona imediatamente para feedback visual, mas será substituído pela busca completa\r\n   */\n  const adicionarAtendimentoSocket = useCallback(novoAtendimento => {\n    console.log('📨 Adicionando atendimento temporário via socket:', novoAtendimento);\n    setAtendimentosPendentes(prev => {\n      // Verificar se já existe para evitar duplicados\n      const jaExiste = prev.some(item => item.lead_id === novoAtendimento.lead_id || item.atendimento_id === novoAtendimento.atendimento_id || item._id === novoAtendimento._id || item.celular && novoAtendimento.celular && item.celular === novoAtendimento.celular);\n      if (jaExiste) {\n        console.log('⚠️ Atendimento já existe, ignorando socket');\n        return prev;\n      }\n\n      // Priorizar _id do MongoDB, depois atendimento_id, só criar temp como último recurso\n      const atendimentoId = novoAtendimento._id || novoAtendimento.atendimento_id || `temp-${Date.now()}`;\n      console.log('📨 ID do atendimento socket (temporário):', {\n        _id: novoAtendimento._id,\n        atendimento_id: novoAtendimento.atendimento_id,\n        final: atendimentoId,\n        nota: 'Será substituído pela busca completa em 300ms'\n      });\n      return [...prev, {\n        ...novoAtendimento,\n        atendimento_id: atendimentoId,\n        _isTemporary: !novoAtendimento._id // Marcar como temporário se não tem _id real\n      }];\n    });\n  }, []);\n\n  /**\r\n   * 🚀 INICIALIZAÇÃO E CONFIGURAÇÃO DOS LISTENERS\r\n   */\n  useEffect(() => {\n    if (!empresaId) return;\n\n    // Buscar fila inicial do banco\n    buscarFilaAtendimento();\n\n    // Configurar polling inteligente baseado no status do socket\n    intervalRef.current = setInterval(() => {\n      const isSocketConnected = socket && socket.connected;\n      if (!isSocketConnected) {\n        // Socket desconectado: polling mais frequente (2 minutos)\n        console.log('🔄 Polling de backup (socket desconectado)...');\n        buscarFilaAtendimento();\n      } else {\n        // Socket conectado: polling muito raro apenas como failsafe (10 minutos)\n        if (Date.now() - lastFetchRef.current > 600000) {\n          // 10 minutos\n          console.log('🔄 Polling de failsafe (socket conectado mas sem atualizações há muito tempo)...');\n          buscarFilaAtendimento();\n        }\n      }\n    }, 120000); // Verificar a cada 2 minutos\n\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [empresaId, socket, buscarFilaAtendimento]);\n\n  /**\r\n   * 📡 CONFIGURAR LISTENERS DO SOCKET\r\n   */\n  useEffect(() => {\n    if (!socket) return;\n    console.log('📡 Configurando listeners de atendimento no socket...');\n\n    // Listener para novos atendimentos\n    const handleAtendimentoPendente = data => {\n      console.log('🚨 Novo atendimento via socket:', data);\n\n      // Adicionar temporariamente para feedback visual imediato\n      adicionarAtendimentoSocket(data);\n\n      // 🚀 IMEDIATAMENTE buscar fila atualizada para obter dados reais\n      console.log('🔄 Atualizando fila automaticamente após socket...');\n      setTimeout(() => {\n        buscarFilaAtendimento();\n      }, 300); // Delay pequeno para garantir que o backend processou\n    };\n\n    // Listener para atendimento iniciado\n    const handleAtendimentoIniciado = data => {\n      console.log('🔄 Atendimento iniciado via socket:', data);\n      setAtendimentosPendentes(prev => prev.filter(item => item.atendimento_id !== data.atendimento_id));\n    };\n\n    // Listener para atendimento finalizado\n    const handleAtendimentoFinalizado = data => {\n      console.log('✅ Atendimento finalizado via socket:', data);\n      setAtendimentosPendentes(prev => prev.filter(item => item.atendimento_id !== data.atendimento_id));\n    };\n\n    // Listener para atendimento cancelado\n    const handleAtendimentoCancelado = data => {\n      console.log('❌ Atendimento cancelado via socket:', data);\n      setAtendimentosPendentes(prev => prev.filter(item => item.atendimento_id !== data.atendimento_id));\n    };\n\n    // Registrar listeners\n    socket.on('atendimento_pendente', handleAtendimentoPendente);\n    socket.on('atendimento_iniciado', handleAtendimentoIniciado);\n    socket.on('atendimento_finalizado', handleAtendimentoFinalizado);\n    socket.on('atendimento_cancelado', handleAtendimentoCancelado);\n\n    // Cleanup\n    return () => {\n      socket.off('atendimento_pendente', handleAtendimentoPendente);\n      socket.off('atendimento_iniciado', handleAtendimentoIniciado);\n      socket.off('atendimento_finalizado', handleAtendimentoFinalizado);\n      socket.off('atendimento_cancelado', handleAtendimentoCancelado);\n    };\n  }, [socket, adicionarAtendimentoSocket, buscarFilaAtendimento]);\n\n  /**\r\n   * 🧹 LIMPEZA AUTOMÁTICA REMOVIDA (evitar sobrecarga no servidor)\r\n   * Limpeza será manual via botão ou endpoint específico\r\n   */\n  // useEffect(() => {\n  //   const limpezaInterval = setInterval(() => {\n  //     limparAtendimentosFinalizados();\n  //   }, 60 * 60 * 1000); // 1 hora\n  //   return () => clearInterval(limpezaInterval);\n  // }, [limparAtendimentosFinalizados]);\n\n  return {\n    // Estados\n    atendimentosPendentes,\n    isLoading,\n    error,\n    totalPendentes: atendimentosPendentes.length,\n    // Ações\n    iniciarAtendimento,\n    finalizarAtendimento,\n    cancelarAtendimento,\n    cancelarTodosAtendimentos,\n    atualizarFila,\n    limparAtendimentosFinalizados,\n    // Para compatibilidade com o código existente\n    removerAtendimento: cancelarAtendimento\n  };\n};\n_s(useFilaAtendimento, \"GaBdH/g9t1G90cxO7wD4BAp5zDI=\");\nexport default useFilaAtendimento;", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "useCallback", "toast", "buscarFilaAtendimento", "apiBuscarFila", "iniciarAtendimentoFila", "finalizarAtendimentoFila", "cancelarAtendimentoFila", "limparAtendimentosFinalizados", "apiLimparFinalizados", "useFilaAtendimento", "empresaId", "socket", "_s", "atendimentosPendentes", "setAtendimentosPendentes", "isLoading", "setIsLoading", "error", "setError", "lastFetchRef", "Date", "now", "intervalRef", "isTemporaryId", "id", "startsWith", "console", "log", "response", "data", "status", "atendimentos", "total_pendentes", "length", "atendimentosFormatados", "map", "item", "atendimento_id", "_id", "company_id", "current", "err", "message", "type", "iniciarAtendimento", "atendimentoId", "userData", "atendimentoReal", "find", "lead_id", "celular", "prev", "filter", "finalizarA<PERSON>imento", "observacoes", "cancelarAtendimento", "motivo", "showToast", "cancelarTodosAtendimentos", "totalAtendimentos", "sucesso", "total", "cancelados", "promessas", "atendimento", "resultados", "Promise", "allSettled", "r", "value", "falharam", "erro", "atualizarFila", "adicionarAtendimentoSocket", "novoAtendimento", "jaExiste", "some", "final", "nota", "_isTemporary", "setInterval", "isSocketConnected", "connected", "clearInterval", "handleAtendimentoPendente", "setTimeout", "handleAtendimentoIniciado", "handleAtendimentoFinalizado", "handleAtendimentoCancelado", "on", "off", "totalPendentes", "remover<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/hooks/useFilaAtendimento/index.jsx"], "sourcesContent": ["import { useState, useEffect, useRef, useCallback } from 'react';\r\nimport { toast } from 'react-toastify';\r\nimport { \r\n  buscarFilaAtendimento as apiBuscarFila,\r\n  iniciarAtendimentoFila,\r\n  finalizarAtendimentoFila,\r\n  cancelarAtendimentoFila,\r\n  limparAtendimentosFinalizados as apiLimparFinalizados\r\n} from '../../services/api';\r\n\r\n/**\r\n * Hook customizado para gerenciar fila de atendimento humano\r\n * Combina WebSocket (tempo real) + polling inteligente (confiabilidade)\r\n * \r\n * 🔌 Quando socket conectado: Usa principalmente WebSocket + polling raro (10min)\r\n * 📡 Quando socket desconectado: Polling frequente de backup (2min)\r\n * \r\n * 🚀 ESTRATÉGIA DE ATUALIZAÇÃO INSTANTÂNEA:\r\n * - Socket recebe atendimento → Adiciona temporariamente (feedback visual)\r\n * - Após 300ms → Busca fila completa (IDs reais do banco)\r\n * - Usuário clica resolver → Já tem dados atualizados, sem espera frustrante!\r\n * \r\n * ⚠️ FALLBACK PARA IDs TEMPORÁRIOS:\r\n * - Se ainda existir ID temporário, busca automaticamente o real\r\n * - Operações sempre tentam resolver automaticamente\r\n */\r\nconst useFilaAtendimento = (empresaId, socket) => {\r\n  const [atendimentosPendentes, setAtendimentosPendentes] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  \r\n  // Ref para evitar chamadas desnecessárias\r\n  const lastFetchRef = useRef(Date.now());\r\n  const intervalRef = useRef(null);\r\n\r\n  /**\r\n   * 🔍 VERIFICAR SE É ID TEMPORÁRIO\r\n   */\r\n  const isTemporaryId = useCallback((id) => {\r\n    return typeof id === 'string' && id.startsWith('temp-');\r\n  }, []);\r\n\r\n  /**\r\n   * 🔍 BUSCAR FILA DE ATENDIMENTO DO BANCO DE DADOS\r\n   */\r\n  const buscarFilaAtendimento = useCallback(async () => {\r\n    if (!empresaId) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      console.log('🔍 Buscando fila de atendimento do banco para empresa:', empresaId);\r\n      \r\n      const response = await apiBuscarFila(empresaId);\r\n      \r\n      if (response.data.status === 200) {\r\n        const { atendimentos, total_pendentes } = response.data.data;\r\n        \r\n        console.log('✅ Fila de atendimento obtida do banco:', {\r\n          total_pendentes,\r\n          atendimentos: atendimentos.length\r\n        });\r\n\r\n        // Converter para formato compatível com o frontend existente\r\n        const atendimentosFormatados = atendimentos.map(item => ({\r\n          ...item,\r\n          atendimento_id: item._id || item.atendimento_id,\r\n          company_id: empresaId\r\n        }));\r\n\r\n        setAtendimentosPendentes(atendimentosFormatados);\r\n        lastFetchRef.current = Date.now();\r\n        \r\n        return atendimentosFormatados;\r\n      }\r\n    } catch (err) {\r\n      console.error('❌ Erro ao buscar fila de atendimento:', err);\r\n      setError(err.message || 'Erro ao carregar fila de atendimento');\r\n      \r\n      // Toast apenas em casos críticos, não em atualizações periódicas\r\n      if (atendimentosPendentes.length === 0) {\r\n        toast('Erro ao carregar fila de atendimento', { type: 'error' });\r\n      }\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [empresaId]);\r\n\r\n  /**\r\n   * 🔄 INICIAR ATENDIMENTO\r\n   */\r\n  const iniciarAtendimento = useCallback(async (atendimentoId, userData) => {\r\n    try {\r\n      console.log('🔄 Iniciando atendimento:', atendimentoId);\r\n      \r\n      // ⚠️ VALIDAÇÃO: Se ainda for ID temporário, buscar fila uma última vez\r\n      if (isTemporaryId(atendimentoId)) {\r\n        console.log('⚠️ ID temporário detectado, buscando fila atualizada...');\r\n        await buscarFilaAtendimento();\r\n        \r\n        // Encontrar o atendimento real na lista atualizada\r\n        const atendimentoReal = atendimentosPendentes.find(item => \r\n          !isTemporaryId(item.atendimento_id) && \r\n          (item.lead_id === userData?.lead_id || item.celular === userData?.celular)\r\n        );\r\n        \r\n        if (atendimentoReal) {\r\n          console.log('✅ Encontrado atendimento real:', atendimentoReal.atendimento_id);\r\n          // Recursivamente chamar com o ID real\r\n          return iniciarAtendimento(atendimentoReal.atendimento_id, userData);\r\n        } else {\r\n          toast('Atendimento não encontrado, tente novamente', { type: 'error' });\r\n          return false;\r\n        }\r\n      }\r\n      \r\n      const response = await iniciarAtendimentoFila(empresaId, atendimentoId, userData);\r\n\r\n      if (response.data.status === 200) {\r\n        // Remover da lista de pendentes\r\n        setAtendimentosPendentes(prev => \r\n          prev.filter(item => item.atendimento_id !== atendimentoId)\r\n        );\r\n        \r\n        toast('Atendimento iniciado!', { type: 'success' });\r\n        return true;\r\n      }\r\n    } catch (err) {\r\n      console.error('❌ Erro ao iniciar atendimento:', err);\r\n      toast('Erro ao iniciar atendimento', { type: 'error' });\r\n      return false;\r\n    }\r\n  }, [empresaId, buscarFilaAtendimento, atendimentosPendentes, isTemporaryId]);\r\n\r\n  /**\r\n   * ✅ FINALIZAR ATENDIMENTO\r\n   */\r\n  const finalizarAtendimento = useCallback(async (atendimentoId, observacoes = '') => {\r\n    try {\r\n      console.log('✅ Finalizando atendimento:', atendimentoId);\r\n      \r\n      // ⚠️ VALIDAÇÃO: Se ainda for ID temporário, buscar fila uma última vez\r\n      if (isTemporaryId(atendimentoId)) {\r\n        console.log('⚠️ ID temporário detectado, buscando fila atualizada...');\r\n        await buscarFilaAtendimento();\r\n        \r\n        // Para finalizar, tentar encontrar pelo ID temporário ou buscar o primeiro disponível\r\n        const atendimentoReal = atendimentosPendentes.find(item => \r\n          !isTemporaryId(item.atendimento_id)\r\n        );\r\n        \r\n        if (atendimentoReal) {\r\n          console.log('✅ Usando primeiro atendimento real encontrado:', atendimentoReal.atendimento_id);\r\n          // Recursivamente chamar com o ID real\r\n          return finalizarAtendimento(atendimentoReal.atendimento_id, observacoes);\r\n        } else {\r\n          toast('Atendimento não encontrado, tente novamente', { type: 'error' });\r\n          return false;\r\n        }\r\n      }\r\n      \r\n      const response = await finalizarAtendimentoFila(empresaId, atendimentoId, observacoes);\r\n\r\n      if (response.data.status === 200) {\r\n        // Remover da lista de pendentes\r\n        setAtendimentosPendentes(prev => \r\n          prev.filter(item => item.atendimento_id !== atendimentoId)\r\n        );\r\n        \r\n        toast('Atendimento finalizado!', { type: 'success' });\r\n        return true;\r\n      }\r\n    } catch (err) {\r\n      console.error('❌ Erro ao finalizar atendimento:', err);\r\n      toast('Erro ao finalizar atendimento', { type: 'error' });\r\n      return false;\r\n    }\r\n  }, [empresaId, buscarFilaAtendimento, atendimentosPendentes, isTemporaryId]);\r\n\r\n  /**\r\n   * ❌ CANCELAR ATENDIMENTO\r\n   */\r\n  const cancelarAtendimento = useCallback(async (atendimentoId, motivo = '', showToast = true) => {\r\n    try {\r\n      console.log('❌ Cancelando atendimento:', atendimentoId);\r\n      \r\n      // ⚠️ VALIDAÇÃO: Verificar se é ID temporário\r\n      if (isTemporaryId(atendimentoId)) {\r\n        console.log('⚠️ ID temporário detectado, removendo da lista local apenas');\r\n        \r\n        // Para IDs temporários, apenas remove da lista local\r\n        setAtendimentosPendentes(prev => \r\n          prev.filter(item => item.atendimento_id !== atendimentoId)\r\n        );\r\n        \r\n        if (showToast) {\r\n          toast('Atendimento removido', { type: 'info' });\r\n        }\r\n        return true;\r\n      }\r\n      \r\n      const response = await cancelarAtendimentoFila(empresaId, atendimentoId, motivo);\r\n\r\n      if (response.data.status === 200) {\r\n        // Remover da lista de pendentes\r\n        setAtendimentosPendentes(prev => \r\n          prev.filter(item => item.atendimento_id !== atendimentoId)\r\n        );\r\n        \r\n        // Só mostra toast se solicitado (padrão é mostrar)\r\n        if (showToast) {\r\n          toast('Atendimento cancelado', { type: 'info' });\r\n        }\r\n        return true;\r\n      }\r\n    } catch (err) {\r\n      console.error('❌ Erro ao cancelar atendimento:', err);\r\n      // Toast de erro sempre mostra, independente do parâmetro\r\n      if (showToast) {\r\n        toast('Erro ao cancelar atendimento', { type: 'error' });\r\n      }\r\n      return false;\r\n    }\r\n  }, [empresaId]);\r\n\r\n  /**\r\n   * 🧹 LIMPAR ATENDIMENTOS FINALIZADOS (limpeza periódica)\r\n   */\r\n  const limparAtendimentosFinalizados = useCallback(async () => {\r\n    try {\r\n      console.log('🧹 Limpando atendimentos finalizados...');\r\n      \r\n      await apiLimparFinalizados(empresaId);\r\n      console.log('✅ Limpeza realizada com sucesso');\r\n    } catch (err) {\r\n      console.error('⚠️ Erro na limpeza (não crítico):', err);\r\n    }\r\n  }, [empresaId]);\r\n\r\n  /**\r\n   * 🚀 CANCELAR TODOS OS ATENDIMENTOS (operação em lote)\r\n   */\r\n  const cancelarTodosAtendimentos = useCallback(async () => {\r\n    const totalAtendimentos = atendimentosPendentes.length;\r\n    \r\n    if (totalAtendimentos === 0) {\r\n      return { sucesso: true, total: 0, cancelados: 0 };\r\n    }\r\n\r\n    try {\r\n      console.log(`🚀 Cancelando ${totalAtendimentos} atendimentos em lote...`);\r\n      \r\n      // Cancelar todos sem mostrar toast individual\r\n      const promessas = atendimentosPendentes.map(atendimento => \r\n        cancelarAtendimento(atendimento.atendimento_id, 'Resolvido em lote pelo usuário', false)\r\n      );\r\n      \r\n      const resultados = await Promise.allSettled(promessas);\r\n      \r\n      // Contar sucessos e falhas\r\n      const cancelados = resultados.filter(r => r.status === 'fulfilled' && r.value === true).length;\r\n      const falharam = totalAtendimentos - cancelados;\r\n      \r\n      console.log(`✅ Resultado do lote: ${cancelados} cancelados, ${falharam} falharam`);\r\n      \r\n      return { \r\n        sucesso: falharam === 0, \r\n        total: totalAtendimentos, \r\n        cancelados, \r\n        falharam \r\n      };\r\n      \r\n    } catch (err) {\r\n      console.error('❌ Erro na operação em lote:', err);\r\n      return { \r\n        sucesso: false, \r\n        total: totalAtendimentos, \r\n        cancelados: 0, \r\n        falharam: totalAtendimentos,\r\n        erro: err.message \r\n      };\r\n    }\r\n  }, [atendimentosPendentes, cancelarAtendimento]);\r\n\r\n  /**\r\n   * 🔄 ATUALIZAR FILA (manual)\r\n   */\r\n  const atualizarFila = useCallback(() => {\r\n    buscarFilaAtendimento();\r\n  }, [buscarFilaAtendimento]);\r\n\r\n  /**\r\n   * ➕ ADICIONAR ATENDIMENTO VIA SOCKET (TEMPORÁRIO)\r\n   * Adiciona imediatamente para feedback visual, mas será substituído pela busca completa\r\n   */\r\n  const adicionarAtendimentoSocket = useCallback((novoAtendimento) => {\r\n    console.log('📨 Adicionando atendimento temporário via socket:', novoAtendimento);\r\n    \r\n    setAtendimentosPendentes(prev => {\r\n      // Verificar se já existe para evitar duplicados\r\n      const jaExiste = prev.some(item => \r\n        item.lead_id === novoAtendimento.lead_id ||\r\n        item.atendimento_id === novoAtendimento.atendimento_id ||\r\n        item._id === novoAtendimento._id ||\r\n        (item.celular && novoAtendimento.celular && item.celular === novoAtendimento.celular)\r\n      );\r\n      \r\n      if (jaExiste) {\r\n        console.log('⚠️ Atendimento já existe, ignorando socket');\r\n        return prev;\r\n      }\r\n      \r\n      // Priorizar _id do MongoDB, depois atendimento_id, só criar temp como último recurso\r\n      const atendimentoId = novoAtendimento._id || \r\n                           novoAtendimento.atendimento_id || \r\n                           `temp-${Date.now()}`;\r\n      \r\n              console.log('📨 ID do atendimento socket (temporário):', { \r\n          _id: novoAtendimento._id, \r\n          atendimento_id: novoAtendimento.atendimento_id,\r\n          final: atendimentoId,\r\n          nota: 'Será substituído pela busca completa em 300ms'\r\n        });\r\n      \r\n      return [...prev, {\r\n        ...novoAtendimento,\r\n        atendimento_id: atendimentoId,\r\n        _isTemporary: !novoAtendimento._id // Marcar como temporário se não tem _id real\r\n      }];\r\n    });\r\n  }, []);\r\n\r\n  /**\r\n   * 🚀 INICIALIZAÇÃO E CONFIGURAÇÃO DOS LISTENERS\r\n   */\r\n  useEffect(() => {\r\n    if (!empresaId) return;\r\n\r\n    // Buscar fila inicial do banco\r\n    buscarFilaAtendimento();\r\n\r\n    // Configurar polling inteligente baseado no status do socket\r\n    intervalRef.current = setInterval(() => {\r\n      const isSocketConnected = socket && socket.connected;\r\n      \r\n      if (!isSocketConnected) {\r\n        // Socket desconectado: polling mais frequente (2 minutos)\r\n        console.log('🔄 Polling de backup (socket desconectado)...');\r\n        buscarFilaAtendimento();\r\n      } else {\r\n        // Socket conectado: polling muito raro apenas como failsafe (10 minutos)\r\n        if (Date.now() - lastFetchRef.current > 600000) { // 10 minutos\r\n          console.log('🔄 Polling de failsafe (socket conectado mas sem atualizações há muito tempo)...');\r\n          buscarFilaAtendimento();\r\n        }\r\n      }\r\n    }, 120000); // Verificar a cada 2 minutos\r\n\r\n    return () => {\r\n      if (intervalRef.current) {\r\n        clearInterval(intervalRef.current);\r\n      }\r\n    };\r\n  }, [empresaId, socket, buscarFilaAtendimento]);\r\n\r\n  /**\r\n   * 📡 CONFIGURAR LISTENERS DO SOCKET\r\n   */\r\n  useEffect(() => {\r\n    if (!socket) return;\r\n\r\n    console.log('📡 Configurando listeners de atendimento no socket...');\r\n\r\n    // Listener para novos atendimentos\r\n    const handleAtendimentoPendente = (data) => {\r\n      console.log('🚨 Novo atendimento via socket:', data);\r\n      \r\n      // Adicionar temporariamente para feedback visual imediato\r\n      adicionarAtendimentoSocket(data);\r\n      \r\n      // 🚀 IMEDIATAMENTE buscar fila atualizada para obter dados reais\r\n      console.log('🔄 Atualizando fila automaticamente após socket...');\r\n      setTimeout(() => {\r\n        buscarFilaAtendimento();\r\n      }, 300); // Delay pequeno para garantir que o backend processou\r\n    };\r\n\r\n    // Listener para atendimento iniciado\r\n    const handleAtendimentoIniciado = (data) => {\r\n      console.log('🔄 Atendimento iniciado via socket:', data);\r\n      setAtendimentosPendentes(prev =>\r\n        prev.filter(item => item.atendimento_id !== data.atendimento_id)\r\n      );\r\n    };\r\n\r\n    // Listener para atendimento finalizado\r\n    const handleAtendimentoFinalizado = (data) => {\r\n      console.log('✅ Atendimento finalizado via socket:', data);\r\n      setAtendimentosPendentes(prev =>\r\n        prev.filter(item => item.atendimento_id !== data.atendimento_id)\r\n      );\r\n    };\r\n\r\n    // Listener para atendimento cancelado\r\n    const handleAtendimentoCancelado = (data) => {\r\n      console.log('❌ Atendimento cancelado via socket:', data);\r\n      setAtendimentosPendentes(prev =>\r\n        prev.filter(item => item.atendimento_id !== data.atendimento_id)\r\n      );\r\n    };\r\n\r\n    // Registrar listeners\r\n    socket.on('atendimento_pendente', handleAtendimentoPendente);\r\n    socket.on('atendimento_iniciado', handleAtendimentoIniciado);\r\n    socket.on('atendimento_finalizado', handleAtendimentoFinalizado);\r\n    socket.on('atendimento_cancelado', handleAtendimentoCancelado);\r\n\r\n    // Cleanup\r\n    return () => {\r\n      socket.off('atendimento_pendente', handleAtendimentoPendente);\r\n      socket.off('atendimento_iniciado', handleAtendimentoIniciado);\r\n      socket.off('atendimento_finalizado', handleAtendimentoFinalizado);\r\n      socket.off('atendimento_cancelado', handleAtendimentoCancelado);\r\n    };\r\n  }, [socket, adicionarAtendimentoSocket, buscarFilaAtendimento]);\r\n\r\n  /**\r\n   * 🧹 LIMPEZA AUTOMÁTICA REMOVIDA (evitar sobrecarga no servidor)\r\n   * Limpeza será manual via botão ou endpoint específico\r\n   */\r\n  // useEffect(() => {\r\n  //   const limpezaInterval = setInterval(() => {\r\n  //     limparAtendimentosFinalizados();\r\n  //   }, 60 * 60 * 1000); // 1 hora\r\n  //   return () => clearInterval(limpezaInterval);\r\n  // }, [limparAtendimentosFinalizados]);\r\n\r\n  return {\r\n    // Estados\r\n    atendimentosPendentes,\r\n    isLoading,\r\n    error,\r\n    totalPendentes: atendimentosPendentes.length,\r\n    \r\n    // Ações\r\n    iniciarAtendimento,\r\n    finalizarAtendimento,\r\n    cancelarAtendimento,\r\n    cancelarTodosAtendimentos,\r\n    atualizarFila,\r\n    limparAtendimentosFinalizados,\r\n    \r\n    // Para compatibilidade com o código existente\r\n    removerAtendimento: cancelarAtendimento,\r\n  };\r\n};\r\n\r\nexport default useFilaAtendimento; "], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,qBAAqB,IAAIC,aAAa,EACtCC,sBAAsB,EACtBC,wBAAwB,EACxBC,uBAAuB,EACvBC,6BAA6B,IAAIC,oBAAoB,QAChD,oBAAoB;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,MAAM,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMsB,YAAY,GAAGpB,MAAM,CAACqB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EACvC,MAAMC,WAAW,GAAGvB,MAAM,CAAC,IAAI,CAAC;;EAEhC;AACF;AACA;EACE,MAAMwB,aAAa,GAAGvB,WAAW,CAAEwB,EAAE,IAAK;IACxC,OAAO,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAACC,UAAU,CAAC,OAAO,CAAC;EACzD,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMvB,qBAAqB,GAAGF,WAAW,CAAC,YAAY;IACpD,IAAI,CAACU,SAAS,EAAE;IAEhB,IAAI;MACFM,YAAY,CAAC,IAAI,CAAC;MAClBE,QAAQ,CAAC,IAAI,CAAC;MAEdQ,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEjB,SAAS,CAAC;MAEhF,MAAMkB,QAAQ,GAAG,MAAMzB,aAAa,CAACO,SAAS,CAAC;MAE/C,IAAIkB,QAAQ,CAACC,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;QAChC,MAAM;UAAEC,YAAY;UAAEC;QAAgB,CAAC,GAAGJ,QAAQ,CAACC,IAAI,CAACA,IAAI;QAE5DH,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UACpDK,eAAe;UACfD,YAAY,EAAEA,YAAY,CAACE;QAC7B,CAAC,CAAC;;QAEF;QACA,MAAMC,sBAAsB,GAAGH,YAAY,CAACI,GAAG,CAACC,IAAI,KAAK;UACvD,GAAGA,IAAI;UACPC,cAAc,EAAED,IAAI,CAACE,GAAG,IAAIF,IAAI,CAACC,cAAc;UAC/CE,UAAU,EAAE7B;QACd,CAAC,CAAC,CAAC;QAEHI,wBAAwB,CAACoB,sBAAsB,CAAC;QAChDf,YAAY,CAACqB,OAAO,GAAGpB,IAAI,CAACC,GAAG,CAAC,CAAC;QAEjC,OAAOa,sBAAsB;MAC/B;IACF,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZf,OAAO,CAACT,KAAK,CAAC,uCAAuC,EAAEwB,GAAG,CAAC;MAC3DvB,QAAQ,CAACuB,GAAG,CAACC,OAAO,IAAI,sCAAsC,CAAC;;MAE/D;MACA,IAAI7B,qBAAqB,CAACoB,MAAM,KAAK,CAAC,EAAE;QACtChC,KAAK,CAAC,sCAAsC,EAAE;UAAE0C,IAAI,EAAE;QAAQ,CAAC,CAAC;MAClE;IACF,CAAC,SAAS;MACR3B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACN,SAAS,CAAC,CAAC;;EAEf;AACF;AACA;EACE,MAAMkC,kBAAkB,GAAG5C,WAAW,CAAC,OAAO6C,aAAa,EAAEC,QAAQ,KAAK;IACxE,IAAI;MACFpB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEkB,aAAa,CAAC;;MAEvD;MACA,IAAItB,aAAa,CAACsB,aAAa,CAAC,EAAE;QAChCnB,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,MAAMzB,qBAAqB,CAAC,CAAC;;QAE7B;QACA,MAAM6C,eAAe,GAAGlC,qBAAqB,CAACmC,IAAI,CAACZ,IAAI,IACrD,CAACb,aAAa,CAACa,IAAI,CAACC,cAAc,CAAC,KAClCD,IAAI,CAACa,OAAO,MAAKH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,OAAO,KAAIb,IAAI,CAACc,OAAO,MAAKJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,OAAO,EAC3E,CAAC;QAED,IAAIH,eAAe,EAAE;UACnBrB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoB,eAAe,CAACV,cAAc,CAAC;UAC7E;UACA,OAAOO,kBAAkB,CAACG,eAAe,CAACV,cAAc,EAAES,QAAQ,CAAC;QACrE,CAAC,MAAM;UACL7C,KAAK,CAAC,6CAA6C,EAAE;YAAE0C,IAAI,EAAE;UAAQ,CAAC,CAAC;UACvE,OAAO,KAAK;QACd;MACF;MAEA,MAAMf,QAAQ,GAAG,MAAMxB,sBAAsB,CAACM,SAAS,EAAEmC,aAAa,EAAEC,QAAQ,CAAC;MAEjF,IAAIlB,QAAQ,CAACC,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;QAChC;QACAhB,wBAAwB,CAACqC,IAAI,IAC3BA,IAAI,CAACC,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACC,cAAc,KAAKQ,aAAa,CAC3D,CAAC;QAED5C,KAAK,CAAC,uBAAuB,EAAE;UAAE0C,IAAI,EAAE;QAAU,CAAC,CAAC;QACnD,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZf,OAAO,CAACT,KAAK,CAAC,gCAAgC,EAAEwB,GAAG,CAAC;MACpDxC,KAAK,CAAC,6BAA6B,EAAE;QAAE0C,IAAI,EAAE;MAAQ,CAAC,CAAC;MACvD,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAACjC,SAAS,EAAER,qBAAqB,EAAEW,qBAAqB,EAAEU,aAAa,CAAC,CAAC;;EAE5E;AACF;AACA;EACE,MAAM8B,oBAAoB,GAAGrD,WAAW,CAAC,OAAO6C,aAAa,EAAES,WAAW,GAAG,EAAE,KAAK;IAClF,IAAI;MACF5B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkB,aAAa,CAAC;;MAExD;MACA,IAAItB,aAAa,CAACsB,aAAa,CAAC,EAAE;QAChCnB,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,MAAMzB,qBAAqB,CAAC,CAAC;;QAE7B;QACA,MAAM6C,eAAe,GAAGlC,qBAAqB,CAACmC,IAAI,CAACZ,IAAI,IACrD,CAACb,aAAa,CAACa,IAAI,CAACC,cAAc,CACpC,CAAC;QAED,IAAIU,eAAe,EAAE;UACnBrB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEoB,eAAe,CAACV,cAAc,CAAC;UAC7F;UACA,OAAOgB,oBAAoB,CAACN,eAAe,CAACV,cAAc,EAAEiB,WAAW,CAAC;QAC1E,CAAC,MAAM;UACLrD,KAAK,CAAC,6CAA6C,EAAE;YAAE0C,IAAI,EAAE;UAAQ,CAAC,CAAC;UACvE,OAAO,KAAK;QACd;MACF;MAEA,MAAMf,QAAQ,GAAG,MAAMvB,wBAAwB,CAACK,SAAS,EAAEmC,aAAa,EAAES,WAAW,CAAC;MAEtF,IAAI1B,QAAQ,CAACC,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;QAChC;QACAhB,wBAAwB,CAACqC,IAAI,IAC3BA,IAAI,CAACC,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACC,cAAc,KAAKQ,aAAa,CAC3D,CAAC;QAED5C,KAAK,CAAC,yBAAyB,EAAE;UAAE0C,IAAI,EAAE;QAAU,CAAC,CAAC;QACrD,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZf,OAAO,CAACT,KAAK,CAAC,kCAAkC,EAAEwB,GAAG,CAAC;MACtDxC,KAAK,CAAC,+BAA+B,EAAE;QAAE0C,IAAI,EAAE;MAAQ,CAAC,CAAC;MACzD,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAACjC,SAAS,EAAER,qBAAqB,EAAEW,qBAAqB,EAAEU,aAAa,CAAC,CAAC;;EAE5E;AACF;AACA;EACE,MAAMgC,mBAAmB,GAAGvD,WAAW,CAAC,OAAO6C,aAAa,EAAEW,MAAM,GAAG,EAAE,EAAEC,SAAS,GAAG,IAAI,KAAK;IAC9F,IAAI;MACF/B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEkB,aAAa,CAAC;;MAEvD;MACA,IAAItB,aAAa,CAACsB,aAAa,CAAC,EAAE;QAChCnB,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;;QAE1E;QACAb,wBAAwB,CAACqC,IAAI,IAC3BA,IAAI,CAACC,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACC,cAAc,KAAKQ,aAAa,CAC3D,CAAC;QAED,IAAIY,SAAS,EAAE;UACbxD,KAAK,CAAC,sBAAsB,EAAE;YAAE0C,IAAI,EAAE;UAAO,CAAC,CAAC;QACjD;QACA,OAAO,IAAI;MACb;MAEA,MAAMf,QAAQ,GAAG,MAAMtB,uBAAuB,CAACI,SAAS,EAAEmC,aAAa,EAAEW,MAAM,CAAC;MAEhF,IAAI5B,QAAQ,CAACC,IAAI,CAACC,MAAM,KAAK,GAAG,EAAE;QAChC;QACAhB,wBAAwB,CAACqC,IAAI,IAC3BA,IAAI,CAACC,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACC,cAAc,KAAKQ,aAAa,CAC3D,CAAC;;QAED;QACA,IAAIY,SAAS,EAAE;UACbxD,KAAK,CAAC,uBAAuB,EAAE;YAAE0C,IAAI,EAAE;UAAO,CAAC,CAAC;QAClD;QACA,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZf,OAAO,CAACT,KAAK,CAAC,iCAAiC,EAAEwB,GAAG,CAAC;MACrD;MACA,IAAIgB,SAAS,EAAE;QACbxD,KAAK,CAAC,8BAA8B,EAAE;UAAE0C,IAAI,EAAE;QAAQ,CAAC,CAAC;MAC1D;MACA,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAACjC,SAAS,CAAC,CAAC;;EAEf;AACF;AACA;EACE,MAAMH,6BAA6B,GAAGP,WAAW,CAAC,YAAY;IAC5D,IAAI;MACF0B,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAEtD,MAAMnB,oBAAoB,CAACE,SAAS,CAAC;MACrCgB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAChD,CAAC,CAAC,OAAOc,GAAG,EAAE;MACZf,OAAO,CAACT,KAAK,CAAC,mCAAmC,EAAEwB,GAAG,CAAC;IACzD;EACF,CAAC,EAAE,CAAC/B,SAAS,CAAC,CAAC;;EAEf;AACF;AACA;EACE,MAAMgD,yBAAyB,GAAG1D,WAAW,CAAC,YAAY;IACxD,MAAM2D,iBAAiB,GAAG9C,qBAAqB,CAACoB,MAAM;IAEtD,IAAI0B,iBAAiB,KAAK,CAAC,EAAE;MAC3B,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEC,KAAK,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC;IACnD;IAEA,IAAI;MACFpC,OAAO,CAACC,GAAG,CAAC,iBAAiBgC,iBAAiB,0BAA0B,CAAC;;MAEzE;MACA,MAAMI,SAAS,GAAGlD,qBAAqB,CAACsB,GAAG,CAAC6B,WAAW,IACrDT,mBAAmB,CAACS,WAAW,CAAC3B,cAAc,EAAE,gCAAgC,EAAE,KAAK,CACzF,CAAC;MAED,MAAM4B,UAAU,GAAG,MAAMC,OAAO,CAACC,UAAU,CAACJ,SAAS,CAAC;;MAEtD;MACA,MAAMD,UAAU,GAAGG,UAAU,CAACb,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACtC,MAAM,KAAK,WAAW,IAAIsC,CAAC,CAACC,KAAK,KAAK,IAAI,CAAC,CAACpC,MAAM;MAC9F,MAAMqC,QAAQ,GAAGX,iBAAiB,GAAGG,UAAU;MAE/CpC,OAAO,CAACC,GAAG,CAAC,wBAAwBmC,UAAU,gBAAgBQ,QAAQ,WAAW,CAAC;MAElF,OAAO;QACLV,OAAO,EAAEU,QAAQ,KAAK,CAAC;QACvBT,KAAK,EAAEF,iBAAiB;QACxBG,UAAU;QACVQ;MACF,CAAC;IAEH,CAAC,CAAC,OAAO7B,GAAG,EAAE;MACZf,OAAO,CAACT,KAAK,CAAC,6BAA6B,EAAEwB,GAAG,CAAC;MACjD,OAAO;QACLmB,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEF,iBAAiB;QACxBG,UAAU,EAAE,CAAC;QACbQ,QAAQ,EAAEX,iBAAiB;QAC3BY,IAAI,EAAE9B,GAAG,CAACC;MACZ,CAAC;IACH;EACF,CAAC,EAAE,CAAC7B,qBAAqB,EAAE0C,mBAAmB,CAAC,CAAC;;EAEhD;AACF;AACA;EACE,MAAMiB,aAAa,GAAGxE,WAAW,CAAC,MAAM;IACtCE,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAACA,qBAAqB,CAAC,CAAC;;EAE3B;AACF;AACA;AACA;EACE,MAAMuE,0BAA0B,GAAGzE,WAAW,CAAE0E,eAAe,IAAK;IAClEhD,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE+C,eAAe,CAAC;IAEjF5D,wBAAwB,CAACqC,IAAI,IAAI;MAC/B;MACA,MAAMwB,QAAQ,GAAGxB,IAAI,CAACyB,IAAI,CAACxC,IAAI,IAC7BA,IAAI,CAACa,OAAO,KAAKyB,eAAe,CAACzB,OAAO,IACxCb,IAAI,CAACC,cAAc,KAAKqC,eAAe,CAACrC,cAAc,IACtDD,IAAI,CAACE,GAAG,KAAKoC,eAAe,CAACpC,GAAG,IAC/BF,IAAI,CAACc,OAAO,IAAIwB,eAAe,CAACxB,OAAO,IAAId,IAAI,CAACc,OAAO,KAAKwB,eAAe,CAACxB,OAC/E,CAAC;MAED,IAAIyB,QAAQ,EAAE;QACZjD,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzD,OAAOwB,IAAI;MACb;;MAEA;MACA,MAAMN,aAAa,GAAG6B,eAAe,CAACpC,GAAG,IACpBoC,eAAe,CAACrC,cAAc,IAC9B,QAAQjB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAEjCK,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;QAC7DW,GAAG,EAAEoC,eAAe,CAACpC,GAAG;QACxBD,cAAc,EAAEqC,eAAe,CAACrC,cAAc;QAC9CwC,KAAK,EAAEhC,aAAa;QACpBiC,IAAI,EAAE;MACR,CAAC,CAAC;MAEJ,OAAO,CAAC,GAAG3B,IAAI,EAAE;QACf,GAAGuB,eAAe;QAClBrC,cAAc,EAAEQ,aAAa;QAC7BkC,YAAY,EAAE,CAACL,eAAe,CAACpC,GAAG,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACExC,SAAS,CAAC,MAAM;IACd,IAAI,CAACY,SAAS,EAAE;;IAEhB;IACAR,qBAAqB,CAAC,CAAC;;IAEvB;IACAoB,WAAW,CAACkB,OAAO,GAAGwC,WAAW,CAAC,MAAM;MACtC,MAAMC,iBAAiB,GAAGtE,MAAM,IAAIA,MAAM,CAACuE,SAAS;MAEpD,IAAI,CAACD,iBAAiB,EAAE;QACtB;QACAvD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5DzB,qBAAqB,CAAC,CAAC;MACzB,CAAC,MAAM;QACL;QACA,IAAIkB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,YAAY,CAACqB,OAAO,GAAG,MAAM,EAAE;UAAE;UAChDd,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC;UAC/FzB,qBAAqB,CAAC,CAAC;QACzB;MACF;IACF,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAM;MACX,IAAIoB,WAAW,CAACkB,OAAO,EAAE;QACvB2C,aAAa,CAAC7D,WAAW,CAACkB,OAAO,CAAC;MACpC;IACF,CAAC;EACH,CAAC,EAAE,CAAC9B,SAAS,EAAEC,MAAM,EAAET,qBAAqB,CAAC,CAAC;;EAE9C;AACF;AACA;EACEJ,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,MAAM,EAAE;IAEbe,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;;IAEpE;IACA,MAAMyD,yBAAyB,GAAIvD,IAAI,IAAK;MAC1CH,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEE,IAAI,CAAC;;MAEpD;MACA4C,0BAA0B,CAAC5C,IAAI,CAAC;;MAEhC;MACAH,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE0D,UAAU,CAAC,MAAM;QACfnF,qBAAqB,CAAC,CAAC;MACzB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC;;IAED;IACA,MAAMoF,yBAAyB,GAAIzD,IAAI,IAAK;MAC1CH,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEE,IAAI,CAAC;MACxDf,wBAAwB,CAACqC,IAAI,IAC3BA,IAAI,CAACC,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACC,cAAc,KAAKR,IAAI,CAACQ,cAAc,CACjE,CAAC;IACH,CAAC;;IAED;IACA,MAAMkD,2BAA2B,GAAI1D,IAAI,IAAK;MAC5CH,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEE,IAAI,CAAC;MACzDf,wBAAwB,CAACqC,IAAI,IAC3BA,IAAI,CAACC,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACC,cAAc,KAAKR,IAAI,CAACQ,cAAc,CACjE,CAAC;IACH,CAAC;;IAED;IACA,MAAMmD,0BAA0B,GAAI3D,IAAI,IAAK;MAC3CH,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEE,IAAI,CAAC;MACxDf,wBAAwB,CAACqC,IAAI,IAC3BA,IAAI,CAACC,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACC,cAAc,KAAKR,IAAI,CAACQ,cAAc,CACjE,CAAC;IACH,CAAC;;IAED;IACA1B,MAAM,CAAC8E,EAAE,CAAC,sBAAsB,EAAEL,yBAAyB,CAAC;IAC5DzE,MAAM,CAAC8E,EAAE,CAAC,sBAAsB,EAAEH,yBAAyB,CAAC;IAC5D3E,MAAM,CAAC8E,EAAE,CAAC,wBAAwB,EAAEF,2BAA2B,CAAC;IAChE5E,MAAM,CAAC8E,EAAE,CAAC,uBAAuB,EAAED,0BAA0B,CAAC;;IAE9D;IACA,OAAO,MAAM;MACX7E,MAAM,CAAC+E,GAAG,CAAC,sBAAsB,EAAEN,yBAAyB,CAAC;MAC7DzE,MAAM,CAAC+E,GAAG,CAAC,sBAAsB,EAAEJ,yBAAyB,CAAC;MAC7D3E,MAAM,CAAC+E,GAAG,CAAC,wBAAwB,EAAEH,2BAA2B,CAAC;MACjE5E,MAAM,CAAC+E,GAAG,CAAC,uBAAuB,EAAEF,0BAA0B,CAAC;IACjE,CAAC;EACH,CAAC,EAAE,CAAC7E,MAAM,EAAE8D,0BAA0B,EAAEvE,qBAAqB,CAAC,CAAC;;EAE/D;AACF;AACA;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;EAEA,OAAO;IACL;IACAW,qBAAqB;IACrBE,SAAS;IACTE,KAAK;IACL0E,cAAc,EAAE9E,qBAAqB,CAACoB,MAAM;IAE5C;IACAW,kBAAkB;IAClBS,oBAAoB;IACpBE,mBAAmB;IACnBG,yBAAyB;IACzBc,aAAa;IACbjE,6BAA6B;IAE7B;IACAqF,kBAAkB,EAAErC;EACtB,CAAC;AACH,CAAC;AAAC3C,EAAA,CA9aIH,kBAAkB;AAgbxB,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}