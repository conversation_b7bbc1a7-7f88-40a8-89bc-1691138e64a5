const mongoose = require('mongoose');

const responseSchema = new mongoose.Schema({
  questionIdentifier: {
    type: String,
    required: true
  },
  questionType: {
    type: String,
    required: true
  },
  question: {
    type: String,
    default: '' // Padrão vazio para personalização futura
  },
  response: {
    type: String,
    default: '' // Padrão vazio para personalização futura
  },
  active: Boolean
});

const companyResponsesSchema = new mongoose.Schema({
  companyId: {
    type: String,
    required: true,
    unique: true
  },
  responses: [responseSchema]
});

const CompanyResponses = mongoose.model('CompanyResponses', companyResponsesSchema)

module.exports = CompanyResponses