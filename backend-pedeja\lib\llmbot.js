const axios = require('axios');

class LLMBot {
    endpoint = '';
    token = '';

	constructor(endpoint, token) {

		if (!endpoint) {
			throw "ERRO_SET_ENDPOINT"
		}

		this.endpoint = endpoint;
		this.token = token;
	}

    async axioGETRequest (path = '/') {
		const options = {
			method: "GET",
			url: this.endpoint + path,
			headers: {accept: 'application/json', Authorization: this.token}
		};

		return axios.request(options);

    }

	async axioPOSTRequest (path = '/' , data) {
		const options = {
			method: "POST",
			url: this.endpoint + path,
			headers: {accept: 'application/json', Authorization: this.token, 'Content-Type': 'application/json'},
			data: data
		};

		return axios.request(options);

    }

	async Call(message = "", user_id = "", company_id = "") {
		const data = {
			message: message, 
			user_id: user_id,
            company_id: company_id
		};

		return this.axioPOSTRequest("/", data)
	}
    
}

module.exports = LLMBot