{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\roboConfig\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport './style.css';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport LeftMenu from \"../../components/LeftMenu\";\nimport { Modal } from \"../../components/Modal\";\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\nimport styled from 'styled-components';\nimport { FaEdit, FaTrash, FaPlus, FaSearch } from \"react-icons/fa\";\nimport { IoMdClose } from \"react-icons/io\";\nimport CryptoJS from 'crypto-js';\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RoboConfigContainer = styled.div`\n    display: flex;\n    margin-left: ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'};\n    height: auto;\n    width: auto;\n    transition: 150ms;\n    background-color: rgb(247,247,247) !important;\n    overflow: initial;\n    z-index: 9;\n\n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\n_c = RoboConfigContainer;\nconst RoboCfg = () => {\n  _s();\n  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\n  const userEncrypted = localStorage.getItem('user');\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  const userParse = JSON.parse(user);\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const empresaObjId = empresaParse._id;\n  const {\n    sidebar\n  } = useContext(SidebarContext);\n  const navigate = useNavigate();\n  const [customResponses, setCustomResponses] = useState([]);\n  const [filteredResponses, setFilteredResponses] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isAddModalOpen, setIsAddModalOpen] = useState(false);\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false);\n  const [editingIndex, setEditingIndex] = useState(null);\n  const [editingData, setEditingData] = useState({\n    question: '',\n    response: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(5);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [deleteIndex, setDeleteIndex] = useState(null);\n\n  // Carregar respostas personalizadas\n  useEffect(() => {\n    fetchCustomResponses();\n  }, []);\n\n  // Filtrar respostas baseado na busca\n  useEffect(() => {\n    if (searchTerm) {\n      const filtered = customResponses.filter(response => response.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredResponses(filtered);\n    } else {\n      setFilteredResponses(customResponses);\n    }\n  }, [searchTerm, customResponses]);\n  const fetchCustomResponses = async () => {\n    try {\n      setLoading(true);\n      const response = await getCustomResponses(empresaObjId);\n      setCustomResponses(response.data.customResponses || []);\n    } catch (error) {\n      console.error(\"Erro ao buscar respostas personalizadas:\", error);\n      toast.error(\"Erro ao carregar respostas personalizadas\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Função para extrair pergunta e resposta da string\n  const parseCustomResponse = responseString => {\n    const match = responseString.match(/quando perguntarem sobre (.+?): (.+)/);\n    if (match) {\n      return {\n        question: match[1],\n        response: match[2]\n      };\n    }\n    return {\n      question: '',\n      response: responseString\n    };\n  };\n  const handleAddResponse = async (values, {\n    setSubmitting,\n    resetForm\n  }) => {\n    try {\n      await addCustomResponse(empresaObjId, values.question, values.response);\n      toast.success(\"Resposta personalizada adicionada com sucesso!\");\n      setIsAddModalOpen(false);\n      resetForm();\n      fetchCustomResponses();\n    } catch (error) {\n      console.error(\"Erro ao adicionar resposta:\", error);\n      toast.error(\"Erro ao adicionar resposta personalizada\");\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEditResponse = async (values, {\n    setSubmitting\n  }) => {\n    try {\n      await updateCustomResponse(empresaObjId, editingIndex, values.question, values.response);\n      toast.success(\"Resposta personalizada atualizada com sucesso!\");\n      setIsEditModalOpen(false);\n      setEditingIndex(null);\n      setEditingData({\n        question: '',\n        response: ''\n      });\n      fetchCustomResponses();\n    } catch (error) {\n      console.error(\"Erro ao atualizar resposta:\", error);\n      toast.error(\"Erro ao atualizar resposta personalizada\");\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleDeleteResponse = async () => {\n    if (deleteIndex !== null) {\n      try {\n        await deleteCustomResponse(empresaObjId, deleteIndex);\n        toast.success(\"Resposta personalizada removida com sucesso!\");\n        setShowDeleteConfirm(false);\n        setDeleteIndex(null);\n        fetchCustomResponses();\n        // Ajustar página se necessário\n        const totalPages = Math.ceil((customResponses.length - 1) / itemsPerPage);\n        if (currentPage > totalPages && totalPages > 0) {\n          setCurrentPage(totalPages);\n        }\n      } catch (error) {\n        console.error(\"Erro ao remover resposta:\", error);\n        toast.error(\"Erro ao remover resposta personalizada\");\n      }\n    }\n  };\n  const confirmDelete = index => {\n    setDeleteIndex(index);\n    setShowDeleteConfirm(true);\n  };\n  const openEditModal = index => {\n    const responseString = customResponses[index];\n    const parsed = parseCustomResponse(responseString);\n    setEditingIndex(index);\n    setEditingData(parsed);\n    setIsEditModalOpen(true);\n  };\n\n  // Paginação\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentItems = filteredResponses.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(filteredResponses.length / itemsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: [/*#__PURE__*/_jsxDEV(LeftMenu, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(RoboConfigContainer, {\n        sidebar: sidebar,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"robo-respostas-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"robo-respostas-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"robo-header-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                children: \"Respostas personalizadas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Configure respostas personalizadas para que o bot responda de acordo com seu restaurante.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"robo-btn-nova-resposta\",\n              onClick: () => setIsAddModalOpen(true),\n              children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 33\n              }, this), \" Nova resposta\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"robo-respostas-search\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"robo-search-input-container\",\n              children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n                className: \"robo-search-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Buscar por pergunta ou resposta...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"robo-search-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"robo-respostas-content\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"robo-loading-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"robo-loading-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Carregando respostas...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 33\n            }, this) : filteredResponses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"robo-empty-state\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"robo-empty-icon\",\n                children: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Nenhuma resposta personalizada encontrada\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: searchTerm ? \"Nenhuma resposta corresponde à sua busca.\" : \"Comece adicionando sua primeira resposta personalizada.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 37\n              }, this), !searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"robo-btn-add-first\",\n                onClick: () => setIsAddModalOpen(true),\n                children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 45\n                }, this), \" Adicionar primeira resposta\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"robo-respostas-list\",\n                children: currentItems.map((responseString, index) => {\n                  const originalIndex = customResponses.indexOf(responseString);\n                  const parsed = parseCustomResponse(responseString);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"robo-resposta-card\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"robo-resposta-content\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"robo-resposta-question\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"robo-question-label\",\n                          children: \"Quando usar essa resposta\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 238,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: parsed.question\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 239,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 237,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"robo-resposta-response\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"robo-response-label\",\n                          children: \"Instru\\xE7\\xF5es para a Resposta\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 242,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          children: parsed.response\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 243,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 241,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"robo-resposta-actions\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"robo-btn-edit\",\n                        onClick: () => openEditModal(originalIndex),\n                        title: \"Editar\",\n                        children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 252,\n                          columnNumber: 61\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"robo-btn-delete\",\n                        onClick: () => confirmDelete(originalIndex),\n                        title: \"Excluir\",\n                        children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 259,\n                          columnNumber: 61\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 254,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"robo-resposta-index\",\n                      children: [originalIndex + 1, \" de \", customResponses.length]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 53\n                    }, this)]\n                  }, originalIndex, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 49\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 37\n              }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"robo-pagination\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"robo-pagination-btn\",\n                  onClick: () => paginate(currentPage - 1),\n                  disabled: currentPage === 1,\n                  children: \"Anterior\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"robo-pagination-numbers\",\n                  children: Array.from({\n                    length: totalPages\n                  }, (_, i) => i + 1).map(number => /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `robo-pagination-number ${currentPage === number ? 'active' : ''}`,\n                    onClick: () => paginate(number),\n                    children: number\n                  }, number, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 53\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"robo-pagination-btn\",\n                  onClick: () => paginate(currentPage + 1),\n                  disabled: currentPage === totalPages,\n                  children: \"Pr\\xF3xima\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(ModalAddCustomResponse, {\n        isOpen: isAddModalOpen,\n        onClose: () => setIsAddModalOpen(false),\n        onSubmit: handleAddResponse\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(ModalEditCustomResponse, {\n        isOpen: isEditModalOpen,\n        onClose: () => {\n          setIsEditModalOpen(false);\n          setEditingIndex(null);\n          setEditingData({\n            question: '',\n            response: ''\n          });\n        },\n        onSubmit: handleEditResponse,\n        initialData: editingData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        isOpen: showDeleteConfirm,\n        onClose: () => setShowDeleteConfirm(false),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"robo-delete-confirm-modal\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"robo-delete-confirm-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"robo-delete-icon\",\n              children: \"\\uD83D\\uDDD1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Confirmar exclus\\xE3o\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Tem certeza que deseja excluir esta resposta personalizada? Esta a\\xE7\\xE3o n\\xE3o pode ser desfeita.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"robo-delete-confirm-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"robo-btn-cancel-delete\",\n              onClick: () => {\n                setShowDeleteConfirm(false);\n                setDeleteIndex(null);\n              },\n              children: \"Cancelar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"robo-btn-confirm-delete\",\n              onClick: handleDeleteResponse,\n              children: \"Excluir\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(RoboCfg, \"waM1wUoBa4U7D0dOyCO753JRUrA=\", false, function () {\n  return [useNavigate];\n});\n_c2 = RoboCfg;\nexport default RoboCfg;\nvar _c, _c2;\n$RefreshReg$(_c, \"RoboConfigContainer\");\n$RefreshReg$(_c2, \"RoboCfg\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "SidebarContext", "PermissionGate", "LeftMenu", "Modal", "ModalAddCustomResponse", "ModalEditCustomResponse", "styled", "FaEdit", "FaTrash", "FaPlus", "FaSearch", "IoMdClose", "CryptoJS", "getCustomResponses", "addCustomResponse", "updateCustomResponse", "deleteCustomResponse", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RoboConfigContainer", "div", "sidebar", "_c", "RoboCfg", "_s", "secret<PERSON>ey", "userEncrypted", "localStorage", "getItem", "user", "AES", "decrypt", "toString", "enc", "Utf8", "userParse", "JSON", "parse", "empresa", "empresaParse", "empresaObjId", "_id", "navigate", "customResponses", "setCustomResponses", "filteredResponses", "setFilteredResponses", "searchTerm", "setSearchTerm", "isAddModalOpen", "setIsAddModalOpen", "isEditModalOpen", "setIsEditModalOpen", "editingIndex", "setEditingIndex", "editingData", "setEditingData", "question", "response", "loading", "setLoading", "currentPage", "setCurrentPage", "itemsPerPage", "showDeleteConfirm", "setShowDeleteConfirm", "deleteIndex", "setDeleteIndex", "fetchCustomResponses", "filtered", "filter", "toLowerCase", "includes", "data", "error", "console", "parseCustomResponse", "responseString", "match", "handleAddResponse", "values", "setSubmitting", "resetForm", "success", "handleEditResponse", "handleDeleteResponse", "totalPages", "Math", "ceil", "length", "confirmDelete", "index", "openEditModal", "parsed", "indexOfLastItem", "indexOfFirstItem", "currentItems", "slice", "paginate", "pageNumber", "children", "permissions", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "map", "originalIndex", "indexOf", "title", "disabled", "Array", "from", "_", "i", "number", "isOpen", "onClose", "onSubmit", "initialData", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/roboConfig/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport './style.css';\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\nimport { Modal } from \"../../components/Modal\";\r\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\r\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\r\nimport styled from 'styled-components';\r\nimport { FaEdit, FaTrash, FaPlus, FaSearch } from \"react-icons/fa\";\r\nimport { IoMdClose } from \"react-icons/io\";\r\nimport CryptoJS from 'crypto-js';\r\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst RoboConfigContainer = styled.div`\r\n    display: flex;\r\n    margin-left: ${({ sidebar }) => (sidebar ? '250px' : '100px')};\r\n    height: auto;\r\n    width: auto;\r\n    transition: 150ms;\r\n    background-color: rgb(247,247,247) !important;\r\n    overflow: initial;\r\n    z-index: 9;\r\n\r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\nconst RoboCfg = () => {\r\n    const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\r\n    const userEncrypted = localStorage.getItem('user');\r\n    const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\r\n    const userParse = JSON.parse(user);\r\n    const empresa = localStorage.getItem('empresa');\r\n    const empresaParse = JSON.parse(empresa);\r\n    const empresaObjId = empresaParse._id;\r\n\r\n    const { sidebar } = useContext(SidebarContext);\r\n    const navigate = useNavigate();\r\n\r\n    const [customResponses, setCustomResponses] = useState([]);\r\n    const [filteredResponses, setFilteredResponses] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [isAddModalOpen, setIsAddModalOpen] = useState(false);\r\n    const [isEditModalOpen, setIsEditModalOpen] = useState(false);\r\n    const [editingIndex, setEditingIndex] = useState(null);\r\n    const [editingData, setEditingData] = useState({ question: '', response: '' });\r\n    const [loading, setLoading] = useState(false);\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [itemsPerPage] = useState(5);\r\n    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\r\n    const [deleteIndex, setDeleteIndex] = useState(null);\r\n\r\n    // Carregar respostas personalizadas\r\n    useEffect(() => {\r\n        fetchCustomResponses();\r\n    }, []);\r\n\r\n    // Filtrar respostas baseado na busca\r\n    useEffect(() => {\r\n        if (searchTerm) {\r\n            const filtered = customResponses.filter(response =>\r\n                response.toLowerCase().includes(searchTerm.toLowerCase())\r\n            );\r\n            setFilteredResponses(filtered);\r\n        } else {\r\n            setFilteredResponses(customResponses);\r\n        }\r\n    }, [searchTerm, customResponses]);\r\n\r\n    const fetchCustomResponses = async () => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await getCustomResponses(empresaObjId);\r\n            setCustomResponses(response.data.customResponses || []);\r\n        } catch (error) {\r\n            console.error(\"Erro ao buscar respostas personalizadas:\", error);\r\n            toast.error(\"Erro ao carregar respostas personalizadas\");\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // Função para extrair pergunta e resposta da string\r\n    const parseCustomResponse = (responseString) => {\r\n        const match = responseString.match(/quando perguntarem sobre (.+?): (.+)/);\r\n        if (match) {\r\n            return {\r\n                question: match[1],\r\n                response: match[2]\r\n            };\r\n        }\r\n        return { question: '', response: responseString };\r\n    };\r\n\r\n    const handleAddResponse = async (values, { setSubmitting, resetForm }) => {\r\n        try {\r\n            await addCustomResponse(empresaObjId, values.question, values.response);\r\n            toast.success(\"Resposta personalizada adicionada com sucesso!\");\r\n            setIsAddModalOpen(false);\r\n            resetForm();\r\n            fetchCustomResponses();\r\n        } catch (error) {\r\n            console.error(\"Erro ao adicionar resposta:\", error);\r\n            toast.error(\"Erro ao adicionar resposta personalizada\");\r\n        } finally {\r\n            setSubmitting(false);\r\n        }\r\n    };\r\n\r\n    const handleEditResponse = async (values, { setSubmitting }) => {\r\n        try {\r\n            await updateCustomResponse(empresaObjId, editingIndex, values.question, values.response);\r\n            toast.success(\"Resposta personalizada atualizada com sucesso!\");\r\n            setIsEditModalOpen(false);\r\n            setEditingIndex(null);\r\n            setEditingData({ question: '', response: '' });\r\n            fetchCustomResponses();\r\n        } catch (error) {\r\n            console.error(\"Erro ao atualizar resposta:\", error);\r\n            toast.error(\"Erro ao atualizar resposta personalizada\");\r\n        } finally {\r\n            setSubmitting(false);\r\n        }\r\n    };\r\n\r\n    const handleDeleteResponse = async () => {\r\n        if (deleteIndex !== null) {\r\n            try {\r\n                await deleteCustomResponse(empresaObjId, deleteIndex);\r\n                toast.success(\"Resposta personalizada removida com sucesso!\");\r\n                setShowDeleteConfirm(false);\r\n                setDeleteIndex(null);\r\n                fetchCustomResponses();\r\n                // Ajustar página se necessário\r\n                const totalPages = Math.ceil((customResponses.length - 1) / itemsPerPage);\r\n                if (currentPage > totalPages && totalPages > 0) {\r\n                    setCurrentPage(totalPages);\r\n                }\r\n            } catch (error) {\r\n                console.error(\"Erro ao remover resposta:\", error);\r\n                toast.error(\"Erro ao remover resposta personalizada\");\r\n            }\r\n        }\r\n    };\r\n\r\n    const confirmDelete = (index) => {\r\n        setDeleteIndex(index);\r\n        setShowDeleteConfirm(true);\r\n    };\r\n\r\n    const openEditModal = (index) => {\r\n        const responseString = customResponses[index];\r\n        const parsed = parseCustomResponse(responseString);\r\n        setEditingIndex(index);\r\n        setEditingData(parsed);\r\n        setIsEditModalOpen(true);\r\n    };\r\n\r\n    // Paginação\r\n    const indexOfLastItem = currentPage * itemsPerPage;\r\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\r\n    const currentItems = filteredResponses.slice(indexOfFirstItem, indexOfLastItem);\r\n    const totalPages = Math.ceil(filteredResponses.length / itemsPerPage);\r\n\r\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n    return (\r\n        <>\r\n            <PermissionGate permissions={['default']}>\r\n                <LeftMenu />\r\n                <RoboConfigContainer sidebar={sidebar}>\r\n                    <div className=\"robo-respostas-container\">\r\n                        <div className=\"robo-respostas-header\">\r\n                            <div className=\"robo-header-content\">\r\n                                <h1>Respostas personalizadas</h1>\r\n                                <p>Configure respostas personalizadas para que o bot responda de acordo com seu restaurante.</p>\r\n                            </div>\r\n                            <button\r\n                                className=\"robo-btn-nova-resposta\"\r\n                                onClick={() => setIsAddModalOpen(true)}\r\n                            >\r\n                                <FaPlus /> Nova resposta\r\n                            </button>\r\n                        </div>\r\n\r\n                        <div className=\"robo-respostas-search\">\r\n                            <div className=\"robo-search-input-container\">\r\n                                <FaSearch className=\"robo-search-icon\" />\r\n                                <input\r\n                                    type=\"text\"\r\n                                    placeholder=\"Buscar por pergunta ou resposta...\"\r\n                                    value={searchTerm}\r\n                                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                                    className=\"robo-search-input\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"robo-respostas-content\">\r\n                            {loading ? (\r\n                                <div className=\"robo-loading-container\">\r\n                                    <div className=\"robo-loading-spinner\"></div>\r\n                                    <p>Carregando respostas...</p>\r\n                                </div>\r\n                            ) : filteredResponses.length === 0 ? (\r\n                                <div className=\"robo-empty-state\">\r\n                                    <div className=\"robo-empty-icon\">💬</div>\r\n                                    <h3>Nenhuma resposta personalizada encontrada</h3>\r\n                                    <p>\r\n                                        {searchTerm\r\n                                            ? \"Nenhuma resposta corresponde à sua busca.\"\r\n                                            : \"Comece adicionando sua primeira resposta personalizada.\"\r\n                                        }\r\n                                    </p>\r\n                                    {!searchTerm && (\r\n                                        <button\r\n                                            className=\"robo-btn-add-first\"\r\n                                            onClick={() => setIsAddModalOpen(true)}\r\n                                        >\r\n                                            <FaPlus /> Adicionar primeira resposta\r\n                                        </button>\r\n                                    )}\r\n                                </div>\r\n                            ) : (\r\n                                <>\r\n                                    <div className=\"robo-respostas-list\">\r\n                                        {currentItems.map((responseString, index) => {\r\n                                            const originalIndex = customResponses.indexOf(responseString);\r\n                                            const parsed = parseCustomResponse(responseString);\r\n                                            return (\r\n                                                <div key={originalIndex} className=\"robo-resposta-card\">\r\n                                                    <div className=\"robo-resposta-content\">\r\n                                                        <div className=\"robo-resposta-question\">\r\n                                                            <span className=\"robo-question-label\">Quando usar essa resposta</span>\r\n                                                            <p>{parsed.question}</p>\r\n                                                        </div>\r\n                                                        <div className=\"robo-resposta-response\">\r\n                                                            <span className=\"robo-response-label\">Instruções para a Resposta</span>\r\n                                                            <p>{parsed.response}</p>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div className=\"robo-resposta-actions\">\r\n                                                        <button\r\n                                                            className=\"robo-btn-edit\"\r\n                                                            onClick={() => openEditModal(originalIndex)}\r\n                                                            title=\"Editar\"\r\n                                                        >\r\n                                                            <FaEdit />\r\n                                                        </button>\r\n                                                        <button\r\n                                                            className=\"robo-btn-delete\"\r\n                                                            onClick={() => confirmDelete(originalIndex)}\r\n                                                            title=\"Excluir\"\r\n                                                        >\r\n                                                            <FaTrash />\r\n                                                        </button>\r\n                                                    </div>\r\n                                                    <div className=\"robo-resposta-index\">\r\n                                                        {originalIndex + 1} de {customResponses.length}\r\n                                                    </div>\r\n                                                </div>\r\n                                            );\r\n                                        })}\r\n                                    </div>\r\n\r\n                                    {/* Paginação */}\r\n                                    {totalPages > 1 && (\r\n                                        <div className=\"robo-pagination\">\r\n                                            <button\r\n                                                className=\"robo-pagination-btn\"\r\n                                                onClick={() => paginate(currentPage - 1)}\r\n                                                disabled={currentPage === 1}\r\n                                            >\r\n                                                Anterior\r\n                                            </button>\r\n\r\n                                            <div className=\"robo-pagination-numbers\">\r\n                                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (\r\n                                                    <button\r\n                                                        key={number}\r\n                                                        className={`robo-pagination-number ${currentPage === number ? 'active' : ''}`}\r\n                                                        onClick={() => paginate(number)}\r\n                                                    >\r\n                                                        {number}\r\n                                                    </button>\r\n                                                ))}\r\n                                            </div>\r\n\r\n                                            <button\r\n                                                className=\"robo-pagination-btn\"\r\n                                                onClick={() => paginate(currentPage + 1)}\r\n                                                disabled={currentPage === totalPages}\r\n                                            >\r\n                                                Próxima\r\n                                            </button>\r\n                                        </div>\r\n                                    )}\r\n                                </>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                </RoboConfigContainer>\r\n\r\n                {/* Modais */}\r\n                <ModalAddCustomResponse\r\n                    isOpen={isAddModalOpen}\r\n                    onClose={() => setIsAddModalOpen(false)}\r\n                    onSubmit={handleAddResponse}\r\n                />\r\n\r\n                <ModalEditCustomResponse\r\n                    isOpen={isEditModalOpen}\r\n                    onClose={() => {\r\n                        setIsEditModalOpen(false);\r\n                        setEditingIndex(null);\r\n                        setEditingData({ question: '', response: '' });\r\n                    }}\r\n                    onSubmit={handleEditResponse}\r\n                    initialData={editingData}\r\n                />\r\n\r\n                {/* Modal de confirmação de exclusão */}\r\n                <Modal isOpen={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>\r\n                    <div className=\"robo-delete-confirm-modal\">\r\n                        <div className=\"robo-delete-confirm-header\">\r\n                            <div className=\"robo-delete-icon\">🗑️</div>\r\n                            <h3>Confirmar exclusão</h3>\r\n                        </div>\r\n                        <p>Tem certeza que deseja excluir esta resposta personalizada? Esta ação não pode ser desfeita.</p>\r\n                        <div className=\"robo-delete-confirm-actions\">\r\n                            <button\r\n                                className=\"robo-btn-cancel-delete\"\r\n                                onClick={() => {\r\n                                    setShowDeleteConfirm(false);\r\n                                    setDeleteIndex(null);\r\n                                }}\r\n                            >\r\n                                Cancelar\r\n                            </button>\r\n                            <button\r\n                                className=\"robo-btn-confirm-delete\"\r\n                                onClick={handleDeleteResponse}\r\n                            >\r\n                                Excluir\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </Modal>\r\n            </PermissionGate>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default RoboCfg;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,aAAa;AACpB,SAASC,cAAc,QAAQ,iBAAiB;AAChD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,OAAOC,sBAAsB,MAAM,yCAAyC;AAC5E,OAAOC,uBAAuB,MAAM,0CAA0C;AAC9E,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAClE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,QAAQ,MAAM,WAAW;AAChC,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,oBAAoB;AACtH,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,mBAAmB,GAAGhB,MAAM,CAACiB,GAAG;AACtC;AACA,mBAAmB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAbIH,mBAAmB;AAezB,MAAMI,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,SAAS,GAAG,uFAAuF;EACzG,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMC,IAAI,GAAGpB,QAAQ,CAACqB,GAAG,CAACC,OAAO,CAACL,aAAa,EAAED,SAAS,CAAC,CAACO,QAAQ,CAACvB,QAAQ,CAACwB,GAAG,CAACC,IAAI,CAAC;EACvF,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACR,IAAI,CAAC;EAClC,MAAMS,OAAO,GAAGX,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMW,YAAY,GAAGH,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC;EACxC,MAAME,YAAY,GAAGD,YAAY,CAACE,GAAG;EAErC,MAAM;IAAEpB;EAAQ,CAAC,GAAG3B,UAAU,CAACG,cAAc,CAAC;EAC9C,MAAM6C,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAE9B,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC;IAAEgE,QAAQ,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsE,YAAY,CAAC,GAAGtE,QAAQ,CAAC,CAAC,CAAC;EAClC,MAAM,CAACuE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACAE,SAAS,CAAC,MAAM;IACZyE,oBAAoB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzE,SAAS,CAAC,MAAM;IACZ,IAAIoD,UAAU,EAAE;MACZ,MAAMsB,QAAQ,GAAG1B,eAAe,CAAC2B,MAAM,CAACZ,QAAQ,IAC5CA,QAAQ,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,CAAC,CAAC,CAC5D,CAAC;MACDzB,oBAAoB,CAACuB,QAAQ,CAAC;IAClC,CAAC,MAAM;MACHvB,oBAAoB,CAACH,eAAe,CAAC;IACzC;EACJ,CAAC,EAAE,CAACI,UAAU,EAAEJ,eAAe,CAAC,CAAC;EAEjC,MAAMyB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACAR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMF,QAAQ,GAAG,MAAMhD,kBAAkB,CAAC8B,YAAY,CAAC;MACvDI,kBAAkB,CAACc,QAAQ,CAACe,IAAI,CAAC9B,eAAe,IAAI,EAAE,CAAC;IAC3D,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE5D,KAAK,CAAC4D,KAAK,CAAC,2CAA2C,CAAC;IAC5D,CAAC,SAAS;MACNd,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMgB,mBAAmB,GAAIC,cAAc,IAAK;IAC5C,MAAMC,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,sCAAsC,CAAC;IAC1E,IAAIA,KAAK,EAAE;MACP,OAAO;QACHrB,QAAQ,EAAEqB,KAAK,CAAC,CAAC,CAAC;QAClBpB,QAAQ,EAAEoB,KAAK,CAAC,CAAC;MACrB,CAAC;IACL;IACA,OAAO;MAAErB,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAEmB;IAAe,CAAC;EACrD,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,EAAE;IAAEC,aAAa;IAAEC;EAAU,CAAC,KAAK;IACtE,IAAI;MACA,MAAMvE,iBAAiB,CAAC6B,YAAY,EAAEwC,MAAM,CAACvB,QAAQ,EAAEuB,MAAM,CAACtB,QAAQ,CAAC;MACvE5C,KAAK,CAACqE,OAAO,CAAC,gDAAgD,CAAC;MAC/DjC,iBAAiB,CAAC,KAAK,CAAC;MACxBgC,SAAS,CAAC,CAAC;MACXd,oBAAoB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOM,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD5D,KAAK,CAAC4D,KAAK,CAAC,0CAA0C,CAAC;IAC3D,CAAC,SAAS;MACNO,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;EAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAOJ,MAAM,EAAE;IAAEC;EAAc,CAAC,KAAK;IAC5D,IAAI;MACA,MAAMrE,oBAAoB,CAAC4B,YAAY,EAAEa,YAAY,EAAE2B,MAAM,CAACvB,QAAQ,EAAEuB,MAAM,CAACtB,QAAQ,CAAC;MACxF5C,KAAK,CAACqE,OAAO,CAAC,gDAAgD,CAAC;MAC/D/B,kBAAkB,CAAC,KAAK,CAAC;MACzBE,eAAe,CAAC,IAAI,CAAC;MACrBE,cAAc,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC,CAAC;MAC9CU,oBAAoB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOM,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD5D,KAAK,CAAC4D,KAAK,CAAC,0CAA0C,CAAC;IAC3D,CAAC,SAAS;MACNO,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;EAED,MAAMI,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAInB,WAAW,KAAK,IAAI,EAAE;MACtB,IAAI;QACA,MAAMrD,oBAAoB,CAAC2B,YAAY,EAAE0B,WAAW,CAAC;QACrDpD,KAAK,CAACqE,OAAO,CAAC,8CAA8C,CAAC;QAC7DlB,oBAAoB,CAAC,KAAK,CAAC;QAC3BE,cAAc,CAAC,IAAI,CAAC;QACpBC,oBAAoB,CAAC,CAAC;QACtB;QACA,MAAMkB,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC,CAAC7C,eAAe,CAAC8C,MAAM,GAAG,CAAC,IAAI1B,YAAY,CAAC;QACzE,IAAIF,WAAW,GAAGyB,UAAU,IAAIA,UAAU,GAAG,CAAC,EAAE;UAC5CxB,cAAc,CAACwB,UAAU,CAAC;QAC9B;MACJ,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD5D,KAAK,CAAC4D,KAAK,CAAC,wCAAwC,CAAC;MACzD;IACJ;EACJ,CAAC;EAED,MAAMgB,aAAa,GAAIC,KAAK,IAAK;IAC7BxB,cAAc,CAACwB,KAAK,CAAC;IACrB1B,oBAAoB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM2B,aAAa,GAAID,KAAK,IAAK;IAC7B,MAAMd,cAAc,GAAGlC,eAAe,CAACgD,KAAK,CAAC;IAC7C,MAAME,MAAM,GAAGjB,mBAAmB,CAACC,cAAc,CAAC;IAClDvB,eAAe,CAACqC,KAAK,CAAC;IACtBnC,cAAc,CAACqC,MAAM,CAAC;IACtBzC,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM0C,eAAe,GAAGjC,WAAW,GAAGE,YAAY;EAClD,MAAMgC,gBAAgB,GAAGD,eAAe,GAAG/B,YAAY;EACvD,MAAMiC,YAAY,GAAGnD,iBAAiB,CAACoD,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EAC/E,MAAMR,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC3C,iBAAiB,CAAC4C,MAAM,GAAG1B,YAAY,CAAC;EAErE,MAAMmC,QAAQ,GAAIC,UAAU,IAAKrC,cAAc,CAACqC,UAAU,CAAC;EAE3D,oBACInF,OAAA,CAAAE,SAAA;IAAAkF,QAAA,eACIpF,OAAA,CAAClB,cAAc;MAACuG,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAD,QAAA,gBACrCpF,OAAA,CAACjB,QAAQ;QAAAuG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACZzF,OAAA,CAACG,mBAAmB;QAACE,OAAO,EAAEA,OAAQ;QAAA+E,QAAA,eAClCpF,OAAA;UAAK0F,SAAS,EAAC,0BAA0B;UAAAN,QAAA,gBACrCpF,OAAA;YAAK0F,SAAS,EAAC,uBAAuB;YAAAN,QAAA,gBAClCpF,OAAA;cAAK0F,SAAS,EAAC,qBAAqB;cAAAN,QAAA,gBAChCpF,OAAA;gBAAAoF,QAAA,EAAI;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCzF,OAAA;gBAAAoF,QAAA,EAAG;cAAyF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eACNzF,OAAA;cACI0F,SAAS,EAAC,wBAAwB;cAClCC,OAAO,EAAEA,CAAA,KAAMzD,iBAAiB,CAAC,IAAI,CAAE;cAAAkD,QAAA,gBAEvCpF,OAAA,CAACV,MAAM;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBACd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENzF,OAAA;YAAK0F,SAAS,EAAC,uBAAuB;YAAAN,QAAA,eAClCpF,OAAA;cAAK0F,SAAS,EAAC,6BAA6B;cAAAN,QAAA,gBACxCpF,OAAA,CAACT,QAAQ;gBAACmG,SAAS,EAAC;cAAkB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCzF,OAAA;gBACI4F,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oCAAoC;gBAChDC,KAAK,EAAE/D,UAAW;gBAClBgE,QAAQ,EAAGC,CAAC,IAAKhE,aAAa,CAACgE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CJ,SAAS,EAAC;cAAmB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENzF,OAAA;YAAK0F,SAAS,EAAC,wBAAwB;YAAAN,QAAA,EAClCzC,OAAO,gBACJ3C,OAAA;cAAK0F,SAAS,EAAC,wBAAwB;cAAAN,QAAA,gBACnCpF,OAAA;gBAAK0F,SAAS,EAAC;cAAsB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CzF,OAAA;gBAAAoF,QAAA,EAAG;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,GACN5D,iBAAiB,CAAC4C,MAAM,KAAK,CAAC,gBAC9BzE,OAAA;cAAK0F,SAAS,EAAC,kBAAkB;cAAAN,QAAA,gBAC7BpF,OAAA;gBAAK0F,SAAS,EAAC,iBAAiB;gBAAAN,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzCzF,OAAA;gBAAAoF,QAAA,EAAI;cAAyC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDzF,OAAA;gBAAAoF,QAAA,EACKrD,UAAU,GACL,2CAA2C,GAC3C;cAAyD;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhE,CAAC,EACH,CAAC1D,UAAU,iBACR/B,OAAA;gBACI0F,SAAS,EAAC,oBAAoB;gBAC9BC,OAAO,EAAEA,CAAA,KAAMzD,iBAAiB,CAAC,IAAI,CAAE;gBAAAkD,QAAA,gBAEvCpF,OAAA,CAACV,MAAM;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gCACd;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAENzF,OAAA,CAAAE,SAAA;cAAAkF,QAAA,gBACIpF,OAAA;gBAAK0F,SAAS,EAAC,qBAAqB;gBAAAN,QAAA,EAC/BJ,YAAY,CAACkB,GAAG,CAAC,CAACrC,cAAc,EAAEc,KAAK,KAAK;kBACzC,MAAMwB,aAAa,GAAGxE,eAAe,CAACyE,OAAO,CAACvC,cAAc,CAAC;kBAC7D,MAAMgB,MAAM,GAAGjB,mBAAmB,CAACC,cAAc,CAAC;kBAClD,oBACI7D,OAAA;oBAAyB0F,SAAS,EAAC,oBAAoB;oBAAAN,QAAA,gBACnDpF,OAAA;sBAAK0F,SAAS,EAAC,uBAAuB;sBAAAN,QAAA,gBAClCpF,OAAA;wBAAK0F,SAAS,EAAC,wBAAwB;wBAAAN,QAAA,gBACnCpF,OAAA;0BAAM0F,SAAS,EAAC,qBAAqB;0BAAAN,QAAA,EAAC;wBAAyB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACtEzF,OAAA;0BAAAoF,QAAA,EAAIP,MAAM,CAACpC;wBAAQ;0BAAA6C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC,eACNzF,OAAA;wBAAK0F,SAAS,EAAC,wBAAwB;wBAAAN,QAAA,gBACnCpF,OAAA;0BAAM0F,SAAS,EAAC,qBAAqB;0BAAAN,QAAA,EAAC;wBAA0B;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACvEzF,OAAA;0BAAAoF,QAAA,EAAIP,MAAM,CAACnC;wBAAQ;0BAAA4C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNzF,OAAA;sBAAK0F,SAAS,EAAC,uBAAuB;sBAAAN,QAAA,gBAClCpF,OAAA;wBACI0F,SAAS,EAAC,eAAe;wBACzBC,OAAO,EAAEA,CAAA,KAAMf,aAAa,CAACuB,aAAa,CAAE;wBAC5CE,KAAK,EAAC,QAAQ;wBAAAjB,QAAA,eAEdpF,OAAA,CAACZ,MAAM;0BAAAkG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACTzF,OAAA;wBACI0F,SAAS,EAAC,iBAAiB;wBAC3BC,OAAO,EAAEA,CAAA,KAAMjB,aAAa,CAACyB,aAAa,CAAE;wBAC5CE,KAAK,EAAC,SAAS;wBAAAjB,QAAA,eAEfpF,OAAA,CAACX,OAAO;0BAAAiG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACNzF,OAAA;sBAAK0F,SAAS,EAAC,qBAAqB;sBAAAN,QAAA,GAC/Be,aAAa,GAAG,CAAC,EAAC,MAAI,EAACxE,eAAe,CAAC8C,MAAM;oBAAA;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC;kBAAA,GA7BAU,aAAa;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA8BlB,CAAC;gBAEd,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EAGLnB,UAAU,GAAG,CAAC,iBACXtE,OAAA;gBAAK0F,SAAS,EAAC,iBAAiB;gBAAAN,QAAA,gBAC5BpF,OAAA;kBACI0F,SAAS,EAAC,qBAAqB;kBAC/BC,OAAO,EAAEA,CAAA,KAAMT,QAAQ,CAACrC,WAAW,GAAG,CAAC,CAAE;kBACzCyD,QAAQ,EAAEzD,WAAW,KAAK,CAAE;kBAAAuC,QAAA,EAC/B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAETzF,OAAA;kBAAK0F,SAAS,EAAC,yBAAyB;kBAAAN,QAAA,EACnCmB,KAAK,CAACC,IAAI,CAAC;oBAAE/B,MAAM,EAAEH;kBAAW,CAAC,EAAE,CAACmC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAACR,GAAG,CAACS,MAAM,iBAC3D3G,OAAA;oBAEI0F,SAAS,EAAE,0BAA0B7C,WAAW,KAAK8D,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;oBAC9EhB,OAAO,EAAEA,CAAA,KAAMT,QAAQ,CAACyB,MAAM,CAAE;oBAAAvB,QAAA,EAE/BuB;kBAAM,GAJFA,MAAM;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKP,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAENzF,OAAA;kBACI0F,SAAS,EAAC,qBAAqB;kBAC/BC,OAAO,EAAEA,CAAA,KAAMT,QAAQ,CAACrC,WAAW,GAAG,CAAC,CAAE;kBACzCyD,QAAQ,EAAEzD,WAAW,KAAKyB,UAAW;kBAAAc,QAAA,EACxC;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACR;YAAA,eACH;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAGtBzF,OAAA,CAACf,sBAAsB;QACnB2H,MAAM,EAAE3E,cAAe;QACvB4E,OAAO,EAAEA,CAAA,KAAM3E,iBAAiB,CAAC,KAAK,CAAE;QACxC4E,QAAQ,EAAE/C;MAAkB;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAEFzF,OAAA,CAACd,uBAAuB;QACpB0H,MAAM,EAAEzE,eAAgB;QACxB0E,OAAO,EAAEA,CAAA,KAAM;UACXzE,kBAAkB,CAAC,KAAK,CAAC;UACzBE,eAAe,CAAC,IAAI,CAAC;UACrBE,cAAc,CAAC;YAAEC,QAAQ,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAG,CAAC,CAAC;QAClD,CAAE;QACFoE,QAAQ,EAAE1C,kBAAmB;QAC7B2C,WAAW,EAAExE;MAAY;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAGFzF,OAAA,CAAChB,KAAK;QAAC4H,MAAM,EAAE5D,iBAAkB;QAAC6D,OAAO,EAAEA,CAAA,KAAM5D,oBAAoB,CAAC,KAAK,CAAE;QAAAmC,QAAA,eACzEpF,OAAA;UAAK0F,SAAS,EAAC,2BAA2B;UAAAN,QAAA,gBACtCpF,OAAA;YAAK0F,SAAS,EAAC,4BAA4B;YAAAN,QAAA,gBACvCpF,OAAA;cAAK0F,SAAS,EAAC,kBAAkB;cAAAN,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3CzF,OAAA;cAAAoF,QAAA,EAAI;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACNzF,OAAA;YAAAoF,QAAA,EAAG;UAA4F;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnGzF,OAAA;YAAK0F,SAAS,EAAC,6BAA6B;YAAAN,QAAA,gBACxCpF,OAAA;cACI0F,SAAS,EAAC,wBAAwB;cAClCC,OAAO,EAAEA,CAAA,KAAM;gBACX1C,oBAAoB,CAAC,KAAK,CAAC;gBAC3BE,cAAc,CAAC,IAAI,CAAC;cACxB,CAAE;cAAAiC,QAAA,EACL;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzF,OAAA;cACI0F,SAAS,EAAC,yBAAyB;cACnCC,OAAO,EAAEtB,oBAAqB;cAAAe,QAAA,EACjC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC,gBACnB,CAAC;AAEX,CAAC;AAAAjF,EAAA,CApUKD,OAAO;EAAA,QAUQ3B,WAAW;AAAA;AAAAoI,GAAA,GAV1BzG,OAAO;AAsUb,eAAeA,OAAO;AAAC,IAAAD,EAAA,EAAA0G,GAAA;AAAAC,YAAA,CAAA3G,EAAA;AAAA2G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}