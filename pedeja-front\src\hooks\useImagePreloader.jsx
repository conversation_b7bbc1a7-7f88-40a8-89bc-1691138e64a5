import { useState, useEffect } from 'react';

/**
 * Hook customizado para preload de imagens
 * @param {string|string[]} imageSources - URL da imagem ou array de URLs
 * @param {number} timeout - Timeout em ms (padrão: 5000)
 * @returns {object} { loaded, error, progress }
 */
const useImagePreloader = (imageSources, timeout = 5000) => {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!imageSources) {
      setLoaded(true);
      return;
    }

    const sources = Array.isArray(imageSources) ? imageSources : [imageSources];
    let loadedCount = 0;
    let hasError = false;

    console.log(`📸 Iniciando preload de ${sources.length} imagem(ns)...`);

    const images = sources.map((src) => {
      const img = new Image();
      
      img.onload = () => {
        loadedCount++;
        const progressPercent = (loadedCount / sources.length) * 100;
        setProgress(progressPercent);
        
        console.log(`✅ Imagem carregada: ${src} (${loadedCount}/${sources.length})`);
        
        if (loadedCount === sources.length && !hasError) {
          console.log(`🎉 Todas as ${sources.length} imagens carregadas com sucesso!`);
          setLoaded(true);
        }
      };

      img.onerror = (err) => {
        hasError = true;
        console.error(`❌ Erro ao carregar imagem: ${src}`, err);
        setError(`Erro ao carregar imagem: ${src}`);
        
        // Marcar como carregado mesmo com erro para não travar a interface
        loadedCount++;
        const progressPercent = (loadedCount / sources.length) * 100;
        setProgress(progressPercent);
        
        if (loadedCount === sources.length) {
          console.log(`⚠️ Preload finalizado com alguns erros`);
          setLoaded(true);
        }
      };

      img.src = src;
      return img;
    });

    // Fallback de segurança com timeout
    const fallbackTimer = setTimeout(() => {
      if (!loaded) {
        console.log(`⏰ Timeout de ${timeout}ms atingido - marcando imagens como carregadas`);
        setLoaded(true);
        setError('Timeout no carregamento das imagens');
      }
    }, timeout);

    return () => {
      clearTimeout(fallbackTimer);
      images.forEach(img => {
        img.onload = null;
        img.onerror = null;
      });
    };
  }, [imageSources, timeout]);

  return { loaded, error, progress };
};

export default useImagePreloader; 