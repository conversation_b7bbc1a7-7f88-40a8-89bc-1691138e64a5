{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\AppRoutes.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useContext, useState, useEffect, createContext, Suspense } from \"react\";\nimport { loadUsetifulScript, setUsetifulTags } from \"usetiful-sdk\"; // 🔹 Importa Usetiful\nimport { BrowserRouter as Router, Route, Routes, Navigate } from \"react-router-dom\";\nimport * as AiIcons from \"react-icons/ai\";\nimport Loading from \"react-loading\";\nimport RouteTracker from \"./utils/RouteTracker\"; // Importar o RouteTracker\n\nimport useCheckLicense from \"./hooks/useCheckLicense\"; // Importar o hook\n\nimport LeftMenu from \"./components/LeftMenu\";\nimport LoginPage from \"./pages/Login\";\nimport HomePage from \"./pages/Home\";\nimport Pdv from \"./pages/Pdv\";\nimport AddUser from \"./pages/Users/<USER>\";\nimport Empresa from \"./pages/Users/<USER>\";\nimport AddCategoria from \"./pages/ListCategoria/addCategoria\";\nimport AddAdicional from \"./pages/ListAdicional/addAdicional\";\nimport AddItemAdicional from \"./pages/ListAdicional/addItemAdicional\";\nimport EditItemAdicional from \"./pages/ListAdicional/editItemAdicional\";\nimport AddItem from \"./pages/ListItem/addItem\";\nimport EditItem from \"./pages/ListItem/editItem\";\nimport Revenda from \"./pages/Users/<USER>\";\nimport Cliente from \"./pages/Cliente\";\nimport Orcamento from \"./pages/Orcamento\";\nimport ListUsers from \"./pages/ListUsers\";\nimport ListEntregadores from \"./pages/ListEntregadores\";\nimport ListEmpresa from \"./pages/ListEmpresa\";\nimport ListCategoria from \"./pages/ListCategoria\";\nimport ListItem from \"./pages/ListItem\";\nimport ListAdicional from \"./pages/ListAdicional\";\nimport ListCliente from \"./pages/ListCliente\";\nimport ListVendedor from \"./pages/ListVendedor\";\nimport ListOrcamento from \"./pages/ListOrcamento\";\nimport RecuperarSenha from \"./pages/RecuperarSenha\";\nimport RedefinirSenha from \"./pages/RedefinirSenha\";\nimport Cardapio from \"./pages/Cardapio\";\nimport CardapioSalao from \"./pages/CardapioSalao\";\nimport DetalheProduto from \"./pages/Cardapio/detalheProduto\";\nimport DetalheProdutoSalao from \"./pages/CardapioSalao/detalheProduto\";\nimport Carrinho from \"./pages/Cardapio/carrinho\";\n//import Mapa from \"./pages/Mapa\";\nimport AddRaioEntrega from \"./pages/Mapa/addRaioEntrega\";\nimport EnderecoEstabelecimento from \"./pages/Endereco/enderecoEstabelecimento\";\nimport WhatsSync from \"./pages/Whatsapp/sync\";\nimport WhatsApp from \"./pages/Whatsapp\";\nimport Planos from \"./pages/MinhaConta/planos\";\nimport IntegracaoCardapio from \"./pages/AddEmpresaCliente/integracao\";\nimport Endereco from \"./pages/AddEmpresaCliente/endereco\";\nimport PlanosAdmin from \"./pages/PlanosAdmin/addPlanos\";\nimport ListPlanosAdmin from \"./pages/PlanosAdmin/listPlansAdmin\";\nimport FinalizarPedido from \"./pages/Cardapio/finalizarPedido\";\nimport AddEndereco from \"./pages/Cardapio/addEndereco\";\nimport ConfigPrinter from \"./pages/Impressora\";\nimport HorarioFuncionamento from \"./pages/HorarioFuncionamento\";\nimport MeusPedidosCfg from \"./pages/MeusPedidosConfig\";\nimport RoboCfg from \"./pages/roboConfig\";\nimport RespostasPersonalizadas from \"./pages/RespostasPersonalizadas\";\nimport InfoLoja from \"./pages/Cardapio/infoLoja\";\nimport InfoLojaSalao from \"./pages/CardapioSalao/infoLoja\";\nimport RelatorioGeral from \"./pages/Relatorios/geral\";\nimport Desempenho from \"./pages/Relatorios/desempenho\";\n\n//import TesteSocketIO from \"./pages/Whatsapp/socketIOcomponent\"; // Teste Socket IO\n\nimport { AuthProvider, AuthContext } from \"./contexts/auth\";\nimport ImportaCardapio from \"./pages/ImportaCardapio\";\nimport ListGarcons from \"./pages/ListGarcons\";\nimport AddGarcom from \"./pages/Users/<USER>\";\nimport FormasPagamento from \"./pages/FormasPagamento\";\nimport FormasTiposEntrega from \"./pages/FormasTiposEntrega\";\nimport ListEmpresas from \"./pages/ListEmpresas\";\nimport AdminSolicitacoesCardapio from \"./pages/AdminSolicitacoesCardapio\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Vendedor = /*#__PURE__*/React.lazy(_c = () => import(\"./pages/Vendedor\"));\n_c2 = Vendedor;\nconst AddEmpresa = /*#__PURE__*/React.lazy(_c3 = () => import(\"./pages/AddEmpresaCliente\"));\n_c4 = AddEmpresa;\nconst AddEntregador = /*#__PURE__*/React.lazy(_c5 = () => import(\"./pages/ListEntregadores/addEntregador\"));\n_c6 = AddEntregador;\nconst Caixa = /*#__PURE__*/React.lazy(_c7 = () => import(\"./pages/Caixa\"));\n_c8 = Caixa;\nconst CaixaHistory = /*#__PURE__*/React.lazy(_c9 = () => import(\"./pages/Caixa/CaixaHistory\"));\n_c0 = CaixaHistory;\nconst CaixaById = /*#__PURE__*/React.lazy(_c1 = () => import(\"./pages/Caixa/CaixaById\"));\n_c10 = CaixaById;\nconst Mesas = /*#__PURE__*/React.lazy(_c11 = () => import(\"./pages/Mesas\"));\n_c12 = Mesas;\nconst LandPage = /*#__PURE__*/React.lazy(_c13 = () => import(\"./pages/LandPage\"));\n_c14 = LandPage;\nconst AddCredencial = /*#__PURE__*/React.lazy(_c15 = () => import(\"./pages/AddEmpresaCliente/credenciais\"));\n_c16 = AddCredencial;\nconst ConfiguracaoInicial = /*#__PURE__*/React.lazy(_c17 = () => import(\"./pages/ConfiguracaoInicial\"));\n\n// Criando o contexto Sidebar\n_c18 = ConfiguracaoInicial;\nexport const SidebarContext = /*#__PURE__*/createContext({\n  sidebar: true,\n  setSidebar: () => {} // Valor inicial como uma função vazia\n});\nexport const SidebarProvider = ({\n  children\n}) => {\n  _s();\n  const [sidebar, setSidebar] = useState(window.innerWidth >= 768); // 🔹 Fecha apenas em mobile (<768px)\n\n  return /*#__PURE__*/_jsxDEV(SidebarContext.Provider, {\n    value: {\n      sidebar,\n      setSidebar\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n};\n\n// Componente de Layout que inclui o LeftMenu\n_s(SidebarProvider, \"EJEcCQ0WiMAQEVwzIr2v2snm6hQ=\");\n_c19 = SidebarProvider;\nconst Layout = ({\n  children\n}) => {\n  _s2();\n  const {\n    sidebar,\n    setSidebar\n  } = useContext(SidebarContext);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(LeftMenu, {\n      setSidebar: setSidebar,\n      sidebar: sidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s2(Layout, \"EI3IcB8ZmOnQ8oqy6R8njqhcf9I=\");\n_c20 = Layout;\nconst AppRoutes = () => {\n  var _s3 = $RefreshSig$();\n  const PrivateRoute = ({\n    children\n  }) => {\n    _s3();\n    useCheckLicense();\n    const {\n      authenticated,\n      loading,\n      hasLicense,\n      redirectToPlanos,\n      user\n    } = useContext(AuthContext);\n\n    //const { sidebar, setSidebar } = useContext(SidebarContext); //TESTANDO CONST NO AppROUTES\n    // 🔹 Carregar Usetiful apenas quando o usuário estiver autenticado\n    useEffect(() => {\n      if (authenticated && user) {\n        console.log(\"🔹 Carregando Usetiful...\");\n        loadUsetifulScript(\"fd74333f30ec05ce31b5da50cc1be18f\"); // 🔥 Chama o Usetiful\n\n        try {\n          // Define os dados do usuário para o Usetiful\n          setUsetifulTags({\n            userId: user.id,\n            firstName: user.name,\n            lastName: user.role\n          });\n          console.log(\"✅ Usetiful configurado com sucesso!\");\n        } catch (error) {\n          console.error(\"❌ Erro ao definir os dados do Usetiful:\", error);\n        }\n      }\n    }, [authenticated, user]); // 🔹 Executa apenas quando `authenticated` ou `user` mudar\n\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: /*#__PURE__*/_jsxDEV(AiIcons.AiOutlineLoading3Quarters, {\n          style: {\n            fontSize: \"100px\",\n            color: \"rgb(180,180,180)\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this);\n    }\n    if (!authenticated) {\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 14\n      }, this);\n    }\n    if (!hasLicense && hasLicense !== null && window.location.pathname !== \"/planos\" && redirectToPlanos) {\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/planos\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 14\n      }, this);\n    }\n\n    /*return (\r\n      <>\r\n        {children}\r\n      </>\r\n    );*/\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 12\n    }, this); // Envolve as páginas no Layout para incluir o LeftMenu\n  };\n  _s3(PrivateRoute, \"9JO3BO5TUy0kf07b5TecAhTQBM8=\", false, function () {\n    return [useCheckLicense];\n  });\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(SidebarProvider, {\n        children: [/*#__PURE__*/_jsxDEV(RouteTracker, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/landpage\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(LandPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/informacoes\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(AddEmpresa, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/credenciais\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(AddCredencial, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/configuracao-inicial\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ConfiguracaoInicial, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/integracao\",\n            element: /*#__PURE__*/_jsxDEV(IntegracaoCardapio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 54\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/endereco\",\n            element: /*#__PURE__*/_jsxDEV(Endereco, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/pdv\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Pdv, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cadastro-user\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(AddUser, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cadastro-garcom\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(AddGarcom, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/empresa\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Empresa, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cadastro-categoria\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(AddCategoria, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cadastro-item\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(AddItem, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cadastro-adicional\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(AddAdicional, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cadastro-item-adicional\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(AddItemAdicional, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/edit-item-adicional\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(EditItemAdicional, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/edit-item\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(EditItem, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/revenda\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Revenda, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cadastro-cliente\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Cliente, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cadastro-entregador\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 39\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(AddEntregador, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cadastro-vendedor\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 39\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Vendedor, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/orcamento/:type\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Orcamento, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-entregadores\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListEntregadores, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-empresas\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListEmpresas, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/admin-solicitacoes-cardapio\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminSolicitacoesCardapio, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-users\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListUsers, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/mesas-garcons\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListGarcons, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-empresa\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListEmpresa, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-categoria\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListCategoria, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-item\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListItem, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-adicional\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListAdicional, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-cliente\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListCliente, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-vendedor\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListVendedor, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-orcamento\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListOrcamento, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/endereco-estabelecimento\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(EnderecoEstabelecimento, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/raio-entrega\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(AddRaioEntrega, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/whatsapp-sync\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(WhatsSync, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/whatsapp\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(WhatsApp, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/impressora\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ConfigPrinter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/horario-funcionamento\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(HorarioFuncionamento, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/formas-pagamento\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(FormasPagamento, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/entrega-retirada\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(FormasTiposEntrega, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/meus-pedidos-configuracao\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(MeusPedidosCfg, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/robo-configuracao\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(RoboCfg, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/planos\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Planos, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/planos-admin\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(PlanosAdmin, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/list-plans-admin\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ListPlanosAdmin, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/relatorio-geral\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(RelatorioGeral, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/desempenho\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Desempenho, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"pedidos-mesas\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 39\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Mesas, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/caixa\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 39\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Caixa, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/caixa/historico\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 39\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(CaixaHistory, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/caixa/historico/:caixaId\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 39\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(CaixaById, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/importacao-cardapio\",\n            element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n              children: /*#__PURE__*/_jsxDEV(ImportaCardapio, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cardapio/:nomeEmpresaForUrl/:idEmpresa\",\n            element: /*#__PURE__*/_jsxDEV(Cardapio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/cardapio-salao/:nomeEmpresaForUrl/:idEmpresa\",\n            element: /*#__PURE__*/_jsxDEV(CardapioSalao, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/:nomeEmpresaForUrl/:idEmpresa/detalhes/:objIdProduto\",\n            element: /*#__PURE__*/_jsxDEV(DetalheProduto, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/:nomeEmpresaForUrl/:idEmpresa/detalhes-salao/:objIdProduto\",\n            element: /*#__PURE__*/_jsxDEV(DetalheProdutoSalao, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/:nomeEmpresaForUrl/:idEmpresa/carrinho\",\n            element: /*#__PURE__*/_jsxDEV(Carrinho, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 704,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/:nomeEmpresaForUrl/:idEmpresa/cadastrar-endereco\",\n            element: /*#__PURE__*/_jsxDEV(AddEndereco, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/:nomeEmpresaForUrl/:idEmpresa/finalizar-pedido\",\n            element: /*#__PURE__*/_jsxDEV(FinalizarPedido, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/:nomeEmpresaForUrl/:idEmpresa/info-loja\",\n            element: /*#__PURE__*/_jsxDEV(InfoLoja, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/:nomeEmpresaForUrl/:idEmpresa/info-loja-salao\",\n            element: /*#__PURE__*/_jsxDEV(InfoLojaSalao, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 724,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/recuperar-senha\",\n            element: /*#__PURE__*/_jsxDEV(RecuperarSenha, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            exact: true,\n            path: \"/redefinir-senha/:token\",\n            element: /*#__PURE__*/_jsxDEV(RedefinirSenha, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 13\n          }, this), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this);\n};\n_c21 = AppRoutes;\nexport default AppRoutes;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21;\n$RefreshReg$(_c, \"Vendedor$React.lazy\");\n$RefreshReg$(_c2, \"Vendedor\");\n$RefreshReg$(_c3, \"AddEmpresa$React.lazy\");\n$RefreshReg$(_c4, \"AddEmpresa\");\n$RefreshReg$(_c5, \"AddEntregador$React.lazy\");\n$RefreshReg$(_c6, \"AddEntregador\");\n$RefreshReg$(_c7, \"Caixa$React.lazy\");\n$RefreshReg$(_c8, \"Caixa\");\n$RefreshReg$(_c9, \"CaixaHistory$React.lazy\");\n$RefreshReg$(_c0, \"CaixaHistory\");\n$RefreshReg$(_c1, \"CaixaById$React.lazy\");\n$RefreshReg$(_c10, \"CaixaById\");\n$RefreshReg$(_c11, \"Mesas$React.lazy\");\n$RefreshReg$(_c12, \"Mesas\");\n$RefreshReg$(_c13, \"LandPage$React.lazy\");\n$RefreshReg$(_c14, \"LandPage\");\n$RefreshReg$(_c15, \"AddCredencial$React.lazy\");\n$RefreshReg$(_c16, \"AddCredencial\");\n$RefreshReg$(_c17, \"ConfiguracaoInicial$React.lazy\");\n$RefreshReg$(_c18, \"ConfiguracaoInicial\");\n$RefreshReg$(_c19, \"SidebarProvider\");\n$RefreshReg$(_c20, \"Layout\");\n$RefreshReg$(_c21, \"AppRoutes\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "useEffect", "createContext", "Suspense", "loadUsetifulScript", "setUsetifulTags", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "Navigate", "AiIcons", "Loading", "RouteTracker", "useCheckLicense", "LeftMenu", "LoginPage", "HomePage", "Pdv", "AddUser", "Empresa", "AddCategoria", "AddAdicional", "AddItemAdicional", "EditItemAdicional", "AddItem", "EditItem", "Revenda", "Cliente", "Orcamento", "ListUsers", "ListEntregadores", "ListEmpresa", "ListCategoria", "ListItem", "ListAdicional", "ListCliente", "ListVendedor", "ListOrcamento", "RecuperarSenha", "RedefinirSenha", "Cardapio", "CardapioSalao", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DetalheProdutoSalao", "<PERSON><PERSON><PERSON>", "AddRaioEntrega", "EnderecoEstabelecimento", "WhatsSync", "WhatsApp", "Planos", "IntegracaoCardapio", "Endereco", "PlanosAdmin", "ListPlanosAdmin", "FinalizarPedido", "AddEndereco", "ConfigPrinter", "HorarioFuncionamento", "MeusPedidosCfg", "RoboCfg", "RespostasPersonalizadas", "InfoLoja", "InfoLojaSalao", "RelatorioGeral", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>th<PERSON><PERSON><PERSON>", "AuthContext", "ImportaCardapio", "ListGarcons", "AddGarcom", "FormasPagamento", "FormasTiposEntrega", "ListEmpresas", "AdminSolicitacoesCardapio", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "lazy", "_c", "_c2", "AddEmpresa", "_c3", "_c4", "AddEntregador", "_c5", "_c6", "Caixa", "_c7", "_c8", "CaixaHistory", "_c9", "_c0", "CaixaById", "_c1", "_c10", "Mesas", "_c11", "_c12", "LandPage", "_c13", "_c14", "AddCredencial", "_c15", "_c16", "ConfiguracaoInicial", "_c17", "_c18", "SidebarContext", "sidebar", "setSidebar", "SidebarProvider", "children", "_s", "window", "innerWidth", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c19", "Layout", "_s2", "_c20", "AppRoutes", "_s3", "$RefreshSig$", "PrivateRoute", "authenticated", "loading", "hasLicense", "redirectToPlanos", "user", "console", "log", "userId", "id", "firstName", "name", "lastName", "role", "error", "className", "AiOutlineLoading3Quarters", "style", "fontSize", "color", "to", "location", "pathname", "exact", "path", "element", "fallback", "_c21", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/AppRoutes.jsx"], "sourcesContent": ["import React, { useContext, useState, useEffect, createContext, Suspense } from \"react\";\r\nimport { loadUsetifulScript, setUsetifulTags } from \"usetiful-sdk\"; // 🔹 Importa Usetiful\r\nimport {\r\n  BrowserRouter as Router,\r\n  Route,\r\n  Routes,\r\n  Navigate,\r\n} from \"react-router-dom\";\r\nimport * as AiIcons from \"react-icons/ai\";\r\nimport Loading from \"react-loading\";\r\nimport RouteTracker from \"./utils/RouteTracker\"; // Importar o RouteTracker\r\n\r\nimport useCheckLicense from \"./hooks/useCheckLicense\"; // Importar o hook\r\n\r\nimport LeftMenu from \"./components/LeftMenu\"\r\nimport LoginPage from \"./pages/Login\";\r\nimport HomePage from \"./pages/Home\";\r\nimport Pdv from \"./pages/Pdv\";\r\nimport AddUser from \"./pages/Users/<USER>\";\r\nimport Empresa from \"./pages/Users/<USER>\";\r\nimport AddCategoria from \"./pages/ListCategoria/addCategoria\";\r\nimport AddAdicional from \"./pages/ListAdicional/addAdicional\";\r\nimport AddItemAdicional from \"./pages/ListAdicional/addItemAdicional\";\r\nimport EditItemAdicional from \"./pages/ListAdicional/editItemAdicional\";\r\nimport AddItem from \"./pages/ListItem/addItem\";\r\nimport EditItem from \"./pages/ListItem/editItem\";\r\nimport Revenda from \"./pages/Users/<USER>\";\r\nimport Cliente from \"./pages/Cliente\";\r\n\r\nimport Orcamento from \"./pages/Orcamento\";\r\nimport ListUsers from \"./pages/ListUsers\";\r\nimport ListEntregadores from \"./pages/ListEntregadores\";\r\nimport ListEmpresa from \"./pages/ListEmpresa\";\r\nimport ListCategoria from \"./pages/ListCategoria\";\r\nimport ListItem from \"./pages/ListItem\";\r\nimport ListAdicional from \"./pages/ListAdicional\";\r\nimport ListCliente from \"./pages/ListCliente\";\r\nimport ListVendedor from \"./pages/ListVendedor\";\r\nimport ListOrcamento from \"./pages/ListOrcamento\";\r\nimport RecuperarSenha from \"./pages/RecuperarSenha\";\r\nimport RedefinirSenha from \"./pages/RedefinirSenha\";\r\nimport Cardapio from \"./pages/Cardapio\";\r\nimport CardapioSalao from \"./pages/CardapioSalao\";\r\nimport DetalheProduto from \"./pages/Cardapio/detalheProduto\";\r\nimport DetalheProdutoSalao from \"./pages/CardapioSalao/detalheProduto\";\r\nimport Carrinho from \"./pages/Cardapio/carrinho\";\r\n//import Mapa from \"./pages/Mapa\";\r\nimport AddRaioEntrega from \"./pages/Mapa/addRaioEntrega\";\r\nimport EnderecoEstabelecimento from \"./pages/Endereco/enderecoEstabelecimento\";\r\nimport WhatsSync from \"./pages/Whatsapp/sync\";\r\nimport WhatsApp from \"./pages/Whatsapp\";\r\nimport Planos from \"./pages/MinhaConta/planos\";\r\n\r\nimport IntegracaoCardapio from \"./pages/AddEmpresaCliente/integracao\";\r\nimport Endereco from \"./pages/AddEmpresaCliente/endereco\";\r\nimport PlanosAdmin from \"./pages/PlanosAdmin/addPlanos\";\r\nimport ListPlanosAdmin from \"./pages/PlanosAdmin/listPlansAdmin\";\r\nimport FinalizarPedido from \"./pages/Cardapio/finalizarPedido\";\r\nimport AddEndereco from \"./pages/Cardapio/addEndereco\";\r\nimport ConfigPrinter from \"./pages/Impressora\";\r\nimport HorarioFuncionamento from \"./pages/HorarioFuncionamento\";\r\nimport MeusPedidosCfg from \"./pages/MeusPedidosConfig\";\r\nimport RoboCfg from \"./pages/roboConfig\";\r\nimport RespostasPersonalizadas from \"./pages/RespostasPersonalizadas\";\r\nimport InfoLoja from \"./pages/Cardapio/infoLoja\";\r\nimport InfoLojaSalao from \"./pages/CardapioSalao/infoLoja\";\r\nimport RelatorioGeral from \"./pages/Relatorios/geral\";\r\nimport Desempenho from \"./pages/Relatorios/desempenho\";\r\n\r\n//import TesteSocketIO from \"./pages/Whatsapp/socketIOcomponent\"; // Teste Socket IO\r\n\r\nimport { AuthProvider, AuthContext } from \"./contexts/auth\";\r\n\r\nimport ImportaCardapio from \"./pages/ImportaCardapio\";\r\nimport ListGarcons from \"./pages/ListGarcons\";\r\nimport AddGarcom from \"./pages/Users/<USER>\";\r\nimport FormasPagamento from \"./pages/FormasPagamento\";\r\nimport FormasTiposEntrega from \"./pages/FormasTiposEntrega\";\r\nimport ListEmpresas from \"./pages/ListEmpresas\";\r\nimport AdminSolicitacoesCardapio from \"./pages/AdminSolicitacoesCardapio\";\r\n\r\nconst Vendedor = React.lazy(() => import(\"./pages/Vendedor\"));\r\n\r\nconst AddEmpresa = React.lazy(() => import(\"./pages/AddEmpresaCliente\"));\r\nconst AddEntregador = React.lazy(() =>\r\n  import(\"./pages/ListEntregadores/addEntregador\")\r\n);\r\nconst Caixa = React.lazy(() => import(\"./pages/Caixa\"));\r\nconst CaixaHistory = React.lazy(() => import(\"./pages/Caixa/CaixaHistory\"));\r\nconst CaixaById = React.lazy(() => import(\"./pages/Caixa/CaixaById\"));\r\nconst Mesas = React.lazy(() => import(\"./pages/Mesas\"));\r\n\r\nconst LandPage = React.lazy(() => import(\"./pages/LandPage\"));\r\n\r\nconst AddCredencial = React.lazy(() =>\r\n  import(\"./pages/AddEmpresaCliente/credenciais\")\r\n);\r\n\r\nconst ConfiguracaoInicial = React.lazy(() =>\r\n  import(\"./pages/ConfiguracaoInicial\")\r\n);\r\n\r\n// Criando o contexto Sidebar\r\nexport const SidebarContext = createContext({\r\n  sidebar: true,\r\n  setSidebar: () => { }, // Valor inicial como uma função vazia\r\n});\r\nexport const SidebarProvider = ({ children }) => {\r\n  const [sidebar, setSidebar] = useState(window.innerWidth >= 768); // 🔹 Fecha apenas em mobile (<768px)\r\n\r\n  return (\r\n    <SidebarContext.Provider value={{ sidebar, setSidebar }}>\r\n      {children}\r\n    </SidebarContext.Provider>\r\n  );\r\n};\r\n\r\n// Componente de Layout que inclui o LeftMenu\r\nconst Layout = ({ children }) => {\r\n  const { sidebar, setSidebar } = useContext(SidebarContext);\r\n\r\n  return (\r\n    <>\r\n      <LeftMenu setSidebar={setSidebar} sidebar={sidebar} />\r\n      <div>\r\n        {children}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nconst AppRoutes = () => {\r\n\r\n  const PrivateRoute = ({ children }) => {\r\n    useCheckLicense();\r\n    const { authenticated, loading, hasLicense, redirectToPlanos, user } = useContext(AuthContext);\r\n\r\n    //const { sidebar, setSidebar } = useContext(SidebarContext); //TESTANDO CONST NO AppROUTES\r\n    // 🔹 Carregar Usetiful apenas quando o usuário estiver autenticado\r\n    useEffect(() => {\r\n      if (authenticated && user) {\r\n        console.log(\"🔹 Carregando Usetiful...\");\r\n        loadUsetifulScript(\"fd74333f30ec05ce31b5da50cc1be18f\"); // 🔥 Chama o Usetiful\r\n\r\n        try {\r\n\r\n          // Define os dados do usuário para o Usetiful\r\n          setUsetifulTags({\r\n            userId: user.id,\r\n            firstName: user.name,\r\n            lastName: user.role,\r\n          });\r\n\r\n          console.log(\"✅ Usetiful configurado com sucesso!\");\r\n\r\n        } catch (error) {\r\n          console.error(\"❌ Erro ao definir os dados do Usetiful:\", error);\r\n        }\r\n      }\r\n    }, [authenticated, user]); // 🔹 Executa apenas quando `authenticated` ou `user` mudar\r\n\r\n    if (loading) {\r\n      return (\r\n        <div className=\"loading\">\r\n          <AiIcons.AiOutlineLoading3Quarters\r\n            style={{ fontSize: \"100px\", color: \"rgb(180,180,180)\" }}\r\n          />\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (!authenticated) {\r\n      return <Navigate to=\"/login\" />;\r\n    }\r\n\r\n    if (\r\n      !hasLicense &&\r\n      hasLicense !== null &&\r\n      window.location.pathname !== \"/planos\" &&\r\n      redirectToPlanos\r\n    ) {\r\n      return <Navigate to=\"/planos\" />;\r\n    }\r\n\r\n    /*return (\r\n      <>\r\n        {children}\r\n      </>\r\n    );*/\r\n    return <Layout>{children}</Layout>; // Envolve as páginas no Layout para incluir o LeftMenu\r\n  };\r\n\r\n  return (\r\n    <Router>\r\n      <AuthProvider>\r\n        <SidebarProvider>\r\n          <RouteTracker />\r\n          <Routes>\r\n            <Route\r\n              exact\r\n              path=\"/landpage\"\r\n              element={\r\n                <Suspense fallback={<Loading />}>\r\n                  <LandPage />\r\n                </Suspense>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/informacoes\"\r\n              element={\r\n                <Suspense fallback={<Loading />}>\r\n                  <AddEmpresa />\r\n                </Suspense>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/credenciais\"\r\n              element={\r\n                <Suspense fallback={<Loading />}>\r\n                  <AddCredencial />\r\n                </Suspense>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/configuracao-inicial\"\r\n              element={\r\n                <Suspense fallback={<Loading />}>\r\n                  <ConfiguracaoInicial />\r\n                </Suspense>\r\n              }\r\n            />\r\n            <Route exact path=\"/integracao\" element={<IntegracaoCardapio />} />\r\n            <Route exact path=\"/endereco\" element={<Endereco />} />\r\n            <Route exact path=\"/login\" element={<LoginPage />} />\r\n            <Route\r\n              exact\r\n              path=\"/\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <HomePage />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/pdv\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Pdv />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cadastro-user\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <AddUser />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cadastro-garcom\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <AddGarcom />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/empresa\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Empresa />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cadastro-categoria\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <AddCategoria />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cadastro-item\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <AddItem />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cadastro-adicional\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <AddAdicional />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cadastro-item-adicional\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <AddItemAdicional />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/edit-item-adicional\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <EditItemAdicional />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/edit-item\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <EditItem />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/revenda\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Revenda />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cadastro-cliente\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Cliente />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cadastro-entregador\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Suspense fallback={<Loading />}>\r\n                    <AddEntregador />\r\n                  </Suspense>\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cadastro-vendedor\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Suspense fallback={<Loading />}>\r\n                    <Vendedor />\r\n                  </Suspense>\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              path=\"/orcamento/:type\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Orcamento />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-entregadores\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListEntregadores />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-empresas\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListEmpresas />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/admin-solicitacoes-cardapio\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <AdminSolicitacoesCardapio />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-users\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListUsers />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/mesas-garcons\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListGarcons />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-empresa\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListEmpresa />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-categoria\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListCategoria />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-item\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListItem />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-adicional\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListAdicional />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-cliente\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListCliente />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-vendedor\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListVendedor />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-orcamento\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListOrcamento />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            {/*<Route exact path=\"/mapa\" element={<PrivateRoute><Mapa/></PrivateRoute>} />*/}\r\n            <Route\r\n              exact\r\n              path=\"/endereco-estabelecimento\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <EnderecoEstabelecimento />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/raio-entrega\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <AddRaioEntrega />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/whatsapp-sync\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <WhatsSync />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/whatsapp\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <WhatsApp />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/impressora\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ConfigPrinter />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/horario-funcionamento\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <HorarioFuncionamento />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/formas-pagamento\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <FormasPagamento />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/entrega-retirada\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <FormasTiposEntrega />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/meus-pedidos-configuracao\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <MeusPedidosCfg />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/robo-configuracao\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <RoboCfg />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n\r\n            <Route\r\n              exact\r\n              path=\"/planos\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Planos />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/planos-admin\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <PlanosAdmin />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/list-plans-admin\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ListPlanosAdmin />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/relatorio-geral\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <RelatorioGeral />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/desempenho\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Desempenho />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"pedidos-mesas\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Suspense fallback={<Loading />}>\r\n                    <Mesas />\r\n                  </Suspense>\r\n\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/caixa\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Suspense fallback={<Loading />}>\r\n                    <Caixa />\r\n                  </Suspense>\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/caixa/historico\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Suspense fallback={<Loading />}>\r\n                    <CaixaHistory />\r\n                  </Suspense>\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/caixa/historico/:caixaId\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <Suspense fallback={<Loading />}>\r\n                    <CaixaById />\r\n                  </Suspense>\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/importacao-cardapio\"\r\n              element={\r\n                <PrivateRoute>\r\n                  <ImportaCardapio />\r\n                </PrivateRoute>\r\n              }\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cardapio/:nomeEmpresaForUrl/:idEmpresa\"\r\n              element={<Cardapio />}\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/cardapio-salao/:nomeEmpresaForUrl/:idEmpresa\"\r\n              element={<CardapioSalao />}\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/:nomeEmpresaForUrl/:idEmpresa/detalhes/:objIdProduto\"\r\n              element={<DetalheProduto />}\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/:nomeEmpresaForUrl/:idEmpresa/detalhes-salao/:objIdProduto\"\r\n              element={<DetalheProdutoSalao />}\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/:nomeEmpresaForUrl/:idEmpresa/carrinho\"\r\n              element={<Carrinho />}\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/:nomeEmpresaForUrl/:idEmpresa/cadastrar-endereco\"\r\n              element={<AddEndereco />}\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/:nomeEmpresaForUrl/:idEmpresa/finalizar-pedido\"\r\n              element={<FinalizarPedido />}\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/:nomeEmpresaForUrl/:idEmpresa/info-loja\"\r\n              element={<InfoLoja />}\r\n            />\r\n            <Route\r\n              exact\r\n              path=\"/:nomeEmpresaForUrl/:idEmpresa/info-loja-salao\"\r\n              element={<InfoLojaSalao />}\r\n            />\r\n            <Route exact path=\"/recuperar-senha\" element={<RecuperarSenha />} />\r\n            <Route\r\n              exact\r\n              path=\"/redefinir-senha/:token\"\r\n              element={<RedefinirSenha />}\r\n            />{\" \"}\r\n            {/* Após configurar o serviço de e-mail definir rota Privada */}\r\n          </Routes>\r\n        </SidebarProvider>\r\n      </AuthProvider>\r\n    </Router>\r\n  );\r\n};\r\n\r\nexport default AppRoutes;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AACvF,SAASC,kBAAkB,EAAEC,eAAe,QAAQ,cAAc,CAAC,CAAC;AACpE,SACEC,aAAa,IAAIC,MAAM,EACvBC,KAAK,EACLC,MAAM,EACNC,QAAQ,QACH,kBAAkB;AACzB,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,YAAY,MAAM,sBAAsB,CAAC,CAAC;;AAEjD,OAAOC,eAAe,MAAM,yBAAyB,CAAC,CAAC;;AAEvD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,eAAe;AACrC,OAAOC,QAAQ,MAAM,cAAc;AACnC,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,iBAAiB,MAAM,yCAAyC;AACvE,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,OAAO,MAAM,iBAAiB;AAErC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD;AACA,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,uBAAuB,MAAM,0CAA0C;AAC9E,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,MAAM,MAAM,2BAA2B;AAE9C,OAAOC,kBAAkB,MAAM,sCAAsC;AACrE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,oBAAoB,MAAM,8BAA8B;AAC/D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,UAAU,MAAM,+BAA+B;;AAEtD;;AAEA,SAASC,YAAY,EAAEC,WAAW,QAAQ,iBAAiB;AAE3D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,yBAAyB,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1E,MAAMC,QAAQ,gBAAGjF,KAAK,CAACkF,IAAI,CAAAC,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,GAAA,GAAxDH,QAAQ;AAEd,MAAMI,UAAU,gBAAGrF,KAAK,CAACkF,IAAI,CAAAI,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,GAAA,GAAnEF,UAAU;AAChB,MAAMG,aAAa,gBAAGxF,KAAK,CAACkF,IAAI,CAAAO,GAAA,GAACA,CAAA,KAC/B,MAAM,CAAC,wCAAwC,CACjD,CAAC;AAACC,GAAA,GAFIF,aAAa;AAGnB,MAAMG,KAAK,gBAAG3F,KAAK,CAACkF,IAAI,CAAAU,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,eAAe,CAAC,CAAC;AAACC,GAAA,GAAlDF,KAAK;AACX,MAAMG,YAAY,gBAAG9F,KAAK,CAACkF,IAAI,CAAAa,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,GAAA,GAAtEF,YAAY;AAClB,MAAMG,SAAS,gBAAGjG,KAAK,CAACkF,IAAI,CAAAgB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAAhEF,SAAS;AACf,MAAMG,KAAK,gBAAGpG,KAAK,CAACkF,IAAI,CAAAmB,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,eAAe,CAAC,CAAC;AAACC,IAAA,GAAlDF,KAAK;AAEX,MAAMG,QAAQ,gBAAGvG,KAAK,CAACkF,IAAI,CAAAsB,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,IAAA,GAAxDF,QAAQ;AAEd,MAAMG,aAAa,gBAAG1G,KAAK,CAACkF,IAAI,CAAAyB,IAAA,GAACA,CAAA,KAC/B,MAAM,CAAC,uCAAuC,CAChD,CAAC;AAACC,IAAA,GAFIF,aAAa;AAInB,MAAMG,mBAAmB,gBAAG7G,KAAK,CAACkF,IAAI,CAAA4B,IAAA,GAACA,CAAA,KACrC,MAAM,CAAC,6BAA6B,CACtC,CAAC;;AAED;AAAAC,IAAA,GAJMF,mBAAmB;AAKzB,OAAO,MAAMG,cAAc,gBAAG5G,aAAa,CAAC;EAC1C6G,OAAO,EAAE,IAAI;EACbC,UAAU,EAAEA,CAAA,KAAM,CAAE,CAAC,CAAE;AACzB,CAAC,CAAC;AACF,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAM,CAACJ,OAAO,EAAEC,UAAU,CAAC,GAAGhH,QAAQ,CAACoH,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC;;EAElE,oBACEzC,OAAA,CAACkC,cAAc,CAACQ,QAAQ;IAACC,KAAK,EAAE;MAAER,OAAO;MAAEC;IAAW,CAAE;IAAAE,QAAA,EACrDA;EAAQ;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAE9B,CAAC;;AAED;AAAAR,EAAA,CAVaF,eAAe;AAAAW,IAAA,GAAfX,eAAe;AAW5B,MAAMY,MAAM,GAAGA,CAAC;EAAEX;AAAS,CAAC,KAAK;EAAAY,GAAA;EAC/B,MAAM;IAAEf,OAAO;IAAEC;EAAW,CAAC,GAAGjH,UAAU,CAAC+G,cAAc,CAAC;EAE1D,oBACElC,OAAA,CAAAE,SAAA;IAAAoC,QAAA,gBACEtC,OAAA,CAAC7D,QAAQ;MAACiG,UAAU,EAAEA,UAAW;MAACD,OAAO,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtD/C,OAAA;MAAAsC,QAAA,EACGA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACG,GAAA,CAXID,MAAM;AAAAE,IAAA,GAANF,MAAM;AAaZ,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAAA,IAAAC,GAAA,GAAAC,YAAA;EAEtB,MAAMC,YAAY,GAAGA,CAAC;IAAEjB;EAAS,CAAC,KAAK;IAAAe,GAAA;IACrCnH,eAAe,CAAC,CAAC;IACjB,MAAM;MAAEsH,aAAa;MAAEC,OAAO;MAAEC,UAAU;MAAEC,gBAAgB;MAAEC;IAAK,CAAC,GAAGzI,UAAU,CAACoE,WAAW,CAAC;;IAE9F;IACA;IACAlE,SAAS,CAAC,MAAM;MACd,IAAImI,aAAa,IAAII,IAAI,EAAE;QACzBC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCtI,kBAAkB,CAAC,kCAAkC,CAAC,CAAC,CAAC;;QAExD,IAAI;UAEF;UACAC,eAAe,CAAC;YACdsI,MAAM,EAAEH,IAAI,CAACI,EAAE;YACfC,SAAS,EAAEL,IAAI,CAACM,IAAI;YACpBC,QAAQ,EAAEP,IAAI,CAACQ;UACjB,CAAC,CAAC;UAEFP,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAEpD,CAAC,CAAC,OAAOO,KAAK,EAAE;UACdR,OAAO,CAACQ,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QACjE;MACF;IACF,CAAC,EAAE,CAACb,aAAa,EAAEI,IAAI,CAAC,CAAC,CAAC,CAAC;;IAE3B,IAAIH,OAAO,EAAE;MACX,oBACEzD,OAAA;QAAKsE,SAAS,EAAC,SAAS;QAAAhC,QAAA,eACtBtC,OAAA,CAACjE,OAAO,CAACwI,yBAAyB;UAChCC,KAAK,EAAE;YAAEC,QAAQ,EAAE,OAAO;YAAEC,KAAK,EAAE;UAAmB;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEV;IAEA,IAAI,CAACS,aAAa,EAAE;MAClB,oBAAOxD,OAAA,CAAClE,QAAQ;QAAC6I,EAAE,EAAC;MAAQ;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACjC;IAEA,IACE,CAACW,UAAU,IACXA,UAAU,KAAK,IAAI,IACnBlB,MAAM,CAACoC,QAAQ,CAACC,QAAQ,KAAK,SAAS,IACtClB,gBAAgB,EAChB;MACA,oBAAO3D,OAAA,CAAClE,QAAQ;QAAC6I,EAAE,EAAC;MAAS;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAClC;;IAEA;AACJ;AACA;AACA;AACA;IACI,oBAAO/C,OAAA,CAACiD,MAAM;MAAAX,QAAA,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAS,CAAC,CAAC,CAAC;EACtC,CAAC;EAACM,GAAA,CAzDIE,YAAY;IAAA,QAChBrH,eAAe;EAAA;EA0DjB,oBACE8D,OAAA,CAACrE,MAAM;IAAA2G,QAAA,eACLtC,OAAA,CAACV,YAAY;MAAAgD,QAAA,eACXtC,OAAA,CAACqC,eAAe;QAAAC,QAAA,gBACdtC,OAAA,CAAC/D,YAAY;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChB/C,OAAA,CAACnE,MAAM;UAAAyG,QAAA,gBACLtC,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLhF,OAAA,CAACzE,QAAQ;cAAC0J,QAAQ,eAAEjF,OAAA,CAAChE,OAAO;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAT,QAAA,eAC9BtC,OAAA,CAACyB,QAAQ;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLhF,OAAA,CAACzE,QAAQ;cAAC0J,QAAQ,eAAEjF,OAAA,CAAChE,OAAO;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAT,QAAA,eAC9BtC,OAAA,CAACO,UAAU;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLhF,OAAA,CAACzE,QAAQ;cAAC0J,QAAQ,eAAEjF,OAAA,CAAChE,OAAO;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAT,QAAA,eAC9BtC,OAAA,CAAC4B,aAAa;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,uBAAuB;YAC5BC,OAAO,eACLhF,OAAA,CAACzE,QAAQ;cAAC0J,QAAQ,eAAEjF,OAAA,CAAChE,OAAO;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAT,QAAA,eAC9BtC,OAAA,CAAC+B,mBAAmB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YAACkJ,KAAK;YAACC,IAAI,EAAC,aAAa;YAACC,OAAO,eAAEhF,OAAA,CAACzB,kBAAkB;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnE/C,OAAA,CAACpE,KAAK;YAACkJ,KAAK;YAACC,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEhF,OAAA,CAACxB,QAAQ;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvD/C,OAAA,CAACpE,KAAK;YAACkJ,KAAK;YAACC,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEhF,OAAA,CAAC5D,SAAS;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrD/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,GAAG;YACRC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC3D,QAAQ;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,MAAM;YACXC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC1D,GAAG;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACzD,OAAO;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACN,SAAS;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,UAAU;YACfC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACxD,OAAO;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,qBAAqB;YAC1BC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACvD,YAAY;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACnD,OAAO;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,qBAAqB;YAC1BC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACtD,YAAY;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,0BAA0B;YAC/BC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACrD,gBAAgB;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACpD,iBAAiB;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAClD,QAAQ;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,UAAU;YACfC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACjD,OAAO;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,mBAAmB;YACxBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAChD,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACzE,QAAQ;gBAAC0J,QAAQ,eAAEjF,OAAA,CAAChE,OAAO;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAT,QAAA,eAC9BtC,OAAA,CAACU,aAAa;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,oBAAoB;YACzBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACzE,QAAQ;gBAAC0J,QAAQ,eAAEjF,OAAA,CAAChE,OAAO;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAT,QAAA,eAC9BtC,OAAA,CAACG,QAAQ;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJmJ,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC/C,SAAS;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,oBAAoB;YACzBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC7C,gBAAgB;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACH,YAAY;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,8BAA8B;YACnCC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACF,yBAAyB;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,aAAa;YAClBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC9C,SAAS;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACP,WAAW;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC5C,WAAW;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,iBAAiB;YACtBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC3C,aAAa;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC1C,QAAQ;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,iBAAiB;YACtBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACzC,aAAa;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACxC,WAAW;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACvC,YAAY;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,iBAAiB;YACtBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACtC,aAAa;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,2BAA2B;YAChCC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC7B,uBAAuB;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC9B,cAAc;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC5B,SAAS;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC3B,QAAQ;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,aAAa;YAClBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACnB,aAAa;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,wBAAwB;YAC7BC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAClB,oBAAoB;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,mBAAmB;YACxBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACL,eAAe;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,mBAAmB;YACxBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACJ,kBAAkB;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,4BAA4B;YACjCC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACjB,cAAc;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,oBAAoB;YACzBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAChB,OAAO;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,SAAS;YACdC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAAC1B,MAAM;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACvB,WAAW;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,mBAAmB;YACxBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACtB,eAAe;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACZ,cAAc;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,aAAa;YAClBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACX,UAAU;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACzE,QAAQ;gBAAC0J,QAAQ,eAAEjF,OAAA,CAAChE,OAAO;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAT,QAAA,eAC9BtC,OAAA,CAACsB,KAAK;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,QAAQ;YACbC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACzE,QAAQ;gBAAC0J,QAAQ,eAAEjF,OAAA,CAAChE,OAAO;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAT,QAAA,eAC9BtC,OAAA,CAACa,KAAK;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACzE,QAAQ;gBAAC0J,QAAQ,eAAEjF,OAAA,CAAChE,OAAO;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAT,QAAA,eAC9BtC,OAAA,CAACgB,YAAY;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,2BAA2B;YAChCC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACzE,QAAQ;gBAAC0J,QAAQ,eAAEjF,OAAA,CAAChE,OAAO;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAT,QAAA,eAC9BtC,OAAA,CAACmB,SAAS;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLhF,OAAA,CAACuD,YAAY;cAAAjB,QAAA,eACXtC,OAAA,CAACR,eAAe;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UACf;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,yCAAyC;YAC9CC,OAAO,eAAEhF,OAAA,CAACnC,QAAQ;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,+CAA+C;YACpDC,OAAO,eAAEhF,OAAA,CAAClC,aAAa;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,uDAAuD;YAC5DC,OAAO,eAAEhF,OAAA,CAACjC,cAAc;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,6DAA6D;YAClEC,OAAO,eAAEhF,OAAA,CAAChC,mBAAmB;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,yCAAyC;YAC9CC,OAAO,eAAEhF,OAAA,CAAC/B,QAAQ;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,mDAAmD;YACxDC,OAAO,eAAEhF,OAAA,CAACpB,WAAW;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,iDAAiD;YACtDC,OAAO,eAAEhF,OAAA,CAACrB,eAAe;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,0CAA0C;YAC/CC,OAAO,eAAEhF,OAAA,CAACd,QAAQ;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACF/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,gDAAgD;YACrDC,OAAO,eAAEhF,OAAA,CAACb,aAAa;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACF/C,OAAA,CAACpE,KAAK;YAACkJ,KAAK;YAACC,IAAI,EAAC,kBAAkB;YAACC,OAAO,eAAEhF,OAAA,CAACrC,cAAc;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpE/C,OAAA,CAACpE,KAAK;YACJkJ,KAAK;YACLC,IAAI,EAAC,yBAAyB;YAC9BC,OAAO,eAAEhF,OAAA,CAACpC,cAAc;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,EAAC,GAAG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEb,CAAC;AAACmC,IAAA,GAjmBI9B,SAAS;AAmmBf,eAAeA,SAAS;AAAC,IAAA/C,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAe,IAAA,EAAAG,IAAA,EAAA+B,IAAA;AAAAC,YAAA,CAAA9E,EAAA;AAAA8E,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAApE,GAAA;AAAAoE,YAAA,CAAAlE,GAAA;AAAAkE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAnC,IAAA;AAAAmC,YAAA,CAAAhC,IAAA;AAAAgC,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}