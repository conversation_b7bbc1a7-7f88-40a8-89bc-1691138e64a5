{"name": "portal-venda", "version": "0.1.1", "private": true, "dependencies": {"@coreui/react": "^4.11.1", "@emotion/react": "^11.10.5", "@hello-pangea/dnd": "^16.5.0", "@hookform/error-message": "^2.0.1", "@material-ui/core": "^4.12.4", "@mui/material": "^5.11.2", "@react-google-maps/api": "^2.19.3", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "antd": "^5.14.1", "axios": "^1.1.3", "bootstrap": "^5.2.3", "buffer": "^6.0.3", "card-validator": "^9.1.0", "cep-promise": "^4.3.0", "chart.js": "^4.4.2", "cpf-cnpj-validator": "^1.0.3", "credit-card-type": "^10.0.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dayjs": "^1.11.6", "formik": "^2.2.9", "framer-motion": "^12.23.0", "google-maps-react": "^2.0.6", "html-to-pdfmake": "^2.5.2", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.1", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "lodash.debounce": "^4.0.8", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "pdfmake": "^0.2.9", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-bootstrap": "^2.10.2", "react-calendar": "^4.0.0", "react-chartjs-2": "^5.2.0", "react-currency-input-field": "^3.7.0", "react-datepicker": "^6.9.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-geocode": "^1.0.0-alpha.1", "react-hook-form": "^7.51.1", "react-icons": "^5.2.1", "react-image": "^4.1.0", "react-image-gallery": "^1.3.0", "react-images-uploading": "^3.1.7", "react-input-mask": "^2.0.4", "react-input-number": "^5.0.19", "react-intersection-observer": "^9.8.1", "react-loading": "^2.0.3", "react-modal": "^3.16.3", "react-numeric-input": "^2.2.3", "react-pro-sidebar": "^1.0.0-alpha.7", "react-qr-code": "^2.0.15", "react-qr-reader": "^3.0.0-beta-1", "react-query": "^3.39.3", "react-router-dom": "^6.22.1", "react-scripts": "^5.0.1", "react-scroll": "^1.9.0", "react-select": "^5.8.0", "react-thermal-printer": "^0.18.1", "react-to-pdf": "^2.0.0", "react-toastify": "^9.1.1", "react-window": "^1.8.10", "reactstrap": "^9.1.5", "socket.io-client": "^4.7.5", "styled-components": "^5.3.6", "usetiful-sdk": "^0.2.0", "uuid": "^9.0.1", "web-vitals": "^2.1.0", "xlsx": "^0.18.5", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts --max_old_space_size=4096 start", "build": "react-scripts --max_old_space_size=4096 build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"babel-plugin-transform-remove-console": "^6.9.4"}}