class UserDAO {
  constructor(userDAO) {
    this.model = userDAO;
  }

  async getUserById(userId) {
    try {
      const user = await this.model.findOne({ id: userId });
      return user;
    } catch (error) {
      console.log(error);
    }
  }

  async getUsersByRole(empresaVinculo, role) {
    try {
      const users = await this.model
        .find(
          { role, vinculo_empresa: empresaVinculo, inativo: false },
          "-password"
        )
        .populate("userEmpresa");
      return users;
    } catch (error) {
      console.log(error);
    }
  }

  async getUserByEmail(email) {
    try {
      const user = await this.model.findOne({ email, inativo: false });
      return user;
    } catch (error) {
      console.log(error);
      throw new Error("Não foi possível obter usuário por email.");
    }
  }

  async createUser(user) {
    try {
      const newUser = new this.model(user);
      await newUser.save();
      return newUser;
    } catch (error) {
      console.log(error);
      throw new Error("It was not possible to create user.");
    }
  }
}

module.exports = UserDAO;
