@import url('https://fonts.googleapis.com/css2?family=Courier+Prime:ital,wght@0,400;0,700;1,400;1,700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&display=swap');

.m-5 {
    background: rgb(247,247,247)!important;

    overflow-x: scroll;
    padding-bottom: 12px;
}

.m5-home{
    overflow: hidden!important;
}

*{
    margin:0;
    padding:0;
    box-sizing: border-box;
}

.title{
    h1{
        font-size: 2rem!important;
    }
}

.container{
    width: 100%;
    margin: 0 auto;
    
    display: flex;
    flex-flow: row wrap;
}

.container-menu{
    width: 50%;
    min-height: 100vh;

    display: flex;
    flex-wrap: wrap;
    align-items:center;
    justify-content: center;

    padding: 15px;

    background-color:#F8FAF9;
}


.container-direita{
    width: 50%;
    min-height: 100vh;

    display: flex;
    flex-wrap: wrap;
    align-items:center;
    justify-content: center;

    padding: 15px;

    background-color:#1E32FF;
}

.buttons-menu{
    width: 100px;
}

.input-box-list.home{
    margin-bottom: 0px!important;
}

.input-fieldClienteOuPedido{
    box-shadow: 0px 0px 3px 2px lightgray!important;
}

.continue-button.cfgHome button{
    background: white!important;
    width:50px!important;
    --border: 1px solid black!important;
    box-shadow: 0px 0px 3px 2px lightgray;
}

.continue-button.cfgHome{
    width:50px!important;
}

.divSpanCancelado{
    font-size: 10px;
    font-weight: 500;
    color: white;
    background-color: red;
    border: 1px solid red;
    padding: 2px;
    border-radius: 5px;
}

.blocoAnalise{
    display: table;
    padding: 10px;
    background: #D0D1D1;
    width: 100%;
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
    height: 640px;
    scrollbar-width: none;
}

.blocoProducao{
    display: table;
    padding: 10px;
    background: #FFA800;
    width: 100%;
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
    height: 640px;
    scrollbar-width: none;
}

.blocoPronto{
    display: table;
    padding: 10px;
    background: #07C670;
    width: 100%;
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
    height: 640px;
    scrollbar-width: none;
}

.blocoFinalizado{
    display: table;
    padding: 10px;
    background:#C7FCFF;
    width: 100%;
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
    height: 640px;
    scrollbar-width: none;
}

.containerAnalise{
    height: 100%;
}

.containerProducao{
    height: 100%;
}

.containerPronto{
    height: 100%;
}

.containerFinalizado{
    height: 100%;
}

.header-column-analise{
    display: inline-flex;
    justify-content: space-between;
    width: 100%;
    background: #7C7C7C;
    font-weight: bold;
    color: white;
    padding: 6px;
    h4{
        margin-bottom: 0px!important;
        font-weight: 600!important;
        font-size: 18px;
    }
}

.header-column-producao{
    display: inline-flex;
    justify-content: space-between;
    width: 100%;
    background: #8F5F00;
    color: white;
    padding: 6px;
    h4{
        margin-bottom: 0px!important;
        font-weight: 600!important;
        font-size: 18px;
    }
}

.header-column-pronto{
    display: inline-flex;
    justify-content: space-between;
    width: 100%;
    background: #046338;
    color: white;
    padding: 6px;
    h4{
        margin-bottom: 0px!important;
        font-weight: 600!important;
        font-size: 18px;
    }
}


.header-column-finalizado{
    display: inline-flex;
    justify-content: space-between;
    width: 100%;
    background: #00A2AB;
    color: white;
    padding: 6px;
    h4{
        margin-bottom: 0px!important;
        font-weight: 600!important;
        font-size: 18px;
    }
}


.header-analise{
    display: inline-flex;
    padding: 10px;
}

.time{
    background: white;
    border-radius: 8px;
    padding: 10px;
    display: inline-flex;
    width: 100%;
    justify-content: space-between;
    font-size: 11.5px;
    height: 100px;
}

.titleOptEmp{
    margin-bottom: 0.5rem!important;
}

.text-edit{
    display: flex;
    align-items: center;
    font-weight: bold;
    text-decoration: underline;    
    transition: .2s;
}

.text-edit:hover{
    color: rgb(49, 140, 213);
}

.text{
    padding:40px; 
    font-size: 14px;    
    font-weight: 500;
    p{
        margin-bottom:0px; 
        justify-content:center; 
        text-align:center; 
        display:flex;
    }                             
}

.header-column-analise{
    padding-left: 5px;
    h3{
        padding-right: 5px;
    }
}

.header-column-producao{
    padding-left: 5px;
    h3{
        padding-right: 5px;
    }
}

.header-column-pronto{
    padding-left: 5px;
    h3{
        padding-right: 5px;
    }
}


.buttonCancelPadrao{
    width: 100%;
    margin-top: 12px;
    min-width: 40px;
    padding: 0 10px;
    min-height: 40px;
    font-size: 14px;
    border: none;
    outline: none;
    border-radius: 4px;
    font-weight: 700;
    user-select: none;
    cursor: pointer;
    transition: all .2s;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    background-color: #e3e3e3;
    color: #777 !important;
}

.buttonCancelPadrao:hover{
    background-color: #d3d3d3;
}

@-webkit-keyframes shaking {
    from {-webkit-transform: rotate(15deg)}
    to   {-webkit-transform: rotate(-15deg)}
}

@keyframes shaking {
    from {transform: rotate(15deg)}
    to   {transform: rotate(-15deg)}
}

#shaking {
    -webkit-animation: shaking 0.3s alternate infinite ease-in-out;
            animation: shaking 0.3s alternate infinite ease-in-out;
}

.input-box.number{
    display: inline-block!important;
    --width: 5%!important;
    input{
        padding: 0.8rem 0px 10px 0px!important;
        text-align: center;
        min-width: 60%;
    }
}

.decrement{    
    border:none;
    width: 36px;
    height: 36px;
    border-radius: 10px;
    background: white;
    box-shadow: 0px 0px 1px 1px lightgray;
    margin-right: 5px;
    svg{
        color:#4a4a4a;
    }
}

.decrement.disable{    
    border:none;
    width: 36px;
    height: 36px;
    border-radius: 10px;
    box-shadow: 0px 0px 1px 1px lightgray;
    margin-right: 5px;
    pointer-events: none;
    background-color: #ccc;
    cursor: not-allowed;
    opacity: 0.5;
    svg{
        color:#4a4a4a;
    }
}


.increment{    
    border:none;
    width: 36px;
    height: 36px;
    border-radius: 10px;
    background: white;
    box-shadow: 0px 0px 1px 1px lightgray;
    margin-left: 5px;
    svg{
        color:#4a4a4a;
    }
}

.line{
    height: 2px;
    width: 100%;
    background: lightgray;
    margin-left: 5px;
  }

.subtitle-form-tempo-entrega{
    color:gray;
    font-weight:bold;
    display: flex;
    align-items: center;
    --width: 280px;
}

.configure-tempo-entrega{
    display: inline-grid;
    width: 50%;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.bloco-pedidos{
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 7px;
    margin-top: 5px;
    padding: 5px 5px 5px 5px;
    border: 1px solid #ffffff;
    cursor: pointer;
    transition: .1s;
}

.bloco-pedidos-printing{
    display: flex;
    flex-direction: column;
    background-color: #fff;
    padding: 10px 10px;
    border: 1px solid #ffffff;
    cursor: pointer;
    transition: .1s;
}

.roboto-font *{
    font-family: "Roboto Condensed", sans-serif!important;
    font-weight: 700;
    font-style: normal;
}

.bloco-pedidos:hover{
    box-shadow: 5px 5px 9px -2px #6a6a6a;
}

.pedido-time{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.tag-pedido{
    display: flex;
    align-items: center;
    gap: 5px;
}

.icon-pedido{
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 5px;
    height: 30px;
    width: 30px;
    flex-shrink: 0;
}

.time-container{
    display: flex;
    align-items: center;
    gap: 5px;
}

.time-div{
    display: flex;
    align-items: center;
    background-color: #d3eeff;
    color: #0067ac;
    border-radius: 3px;
    padding: 3px 5px;
    gap: 3px;
    height: 22px;
    box-sizing: border-box;
    font-weight: 500;
    font-size: 12px;
    line-height: normal;
}

.cliente-total{
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.bloco{
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.linha{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.texto{
    color: #5a5a5a;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
}

.label-recorrencia-pedido{
    background-color: #5aaf51;
    border-color: #5aaf51;
    border-radius: 4px;
    width: 15px;
    height: 15px;
}

.label-recorrencia-pedidoMoreThanOne{
    background-color: #aaa;
    border-color: #aaa;
    border-radius: 4px;
    width: 15px;
    height: 15px;
}

.loadingSpinHomeFilters{
    position: absolute;
    top: 50%;
    left: 50%;
}

.caixaNumero{
    color:white;
    text-align: center;
    align-items: center;
    display: flex;
    justify-content: center;
}

.bold{
    color: #2b2b2b;
    text-align: right;
    font-size: 13px;
    font-weight: 700;
    line-height: 19px;
}

.bloco-entrega{
    margin-top: 5px;
    border-radius: 4px;
    background-color: #f5f5f5;
}

.flex-entrega{
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 5px;
    padding: 5px;
}

.bloco-footer{
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 5px;
    min-width: 70%;
}

.pin{
    width: 12px;
    height: 12px;
    stroke: rgb(157, 157, 157);
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.underline{
    font-style: italic;
    font-weight: 500;
    text-decoration-line: underline;
}


.button-avancar-pedido button {
    border: none;
    box-shadow: 1px 1px 6px lightgray;
    height:40px;
    background:white;
    border:solid 2px var(--main-color-pedeja);
    width:100%;
    background: -webkit-linear-gradient(to left, #2D74FF, #07BFFF);
    background: -o-linear-gradient(to left, #2D74FF, #07BFFF);
    background: -moz-linear-gradient(to left, #2D74FF, #07BFFF);
    background: linear-gradient(to left, #2D74FF, #07BFFF);
    background: white!important;
    padding: 0.62rem;
    border-radius: 5px;
    align-items: center;
    display: flex;
    justify-content: center;
    cursor: pointer;
}

.button-avancar-pedido button:hover {
    background: -webkit-linear-gradient(to left, #4281FF, #51D2FF);
    background: -o-linear-gradient(to left, #4281FF, #51D2FF);
    background: -moz-linear-gradient(to left, #4281FF, #51D2FF);
    background: linear-gradient(to left, #4281FF, #51D2FF)!important;
    border:none;
    a{
        color:white!important;
    }
    svg{
        color: white!important;
    }
}

.button-avancar-pedido button a {
    text-decoration: none;
    font-size: 12px;
    font-weight: 700;
    color: var(--main-color-pedeja)!important;
}

.button-avancar-pedido button svg {
    color: var(--main-color-pedeja)!important;
}

.justifycontent-center{
    display: flex;
    justify-content: center;
    font-weight: bold;
    font-size: 22px!important;
}

.justifycontent-center.numero-pedido{
    display: flex;
    justify-content: center;
    font-weight: bold;
    font-size: 46px!important;
}


.itens-justify{
    display: flex;
    justify-content: space-between;
    padding: 5px;
    font-weight: 600;
}

.itens-justify-valores{    
    display: flex;
    justify-content: space-between;
    padding: 0px 5px 0px 5px;
    font-weight: 600;
}

.itens-row-cliente{
    padding: 5px;
    font-weight: 600;
    padding-bottom: 20px;
}

@media (min-width: 780px) and (max-width: 1080px){
    .Ordercolumn {
        min-width: 1200px;
    }
}

/* Para telas maiores que 780px, definir width: 100% */
@media (min-width: 1080px) {
    .Ordercolumn {
        width: 100%;
    }
}


@media (max-width: 1080px){
    .header-mobile-kanban {
        flex-direction: column;
        margin-bottom: 15px;
    }
}

/* ======================== BOTÕES DE FILTRO MODERNOS ======================== */
/* Estilos elegantes e modernos para os botões de filtragem */

/* Container dos botões - design mais sofisticado */
.filter-buttons-container {
    display: flex;
    align-items: center;
    height: max-content;
    width: max-content;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid rgba(49, 140, 213, 0.1);
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.08),
        0 3px 10px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border-radius: 16px !important;
    overflow: hidden;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-buttons-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(49, 140, 213, 0.02), transparent, rgba(118, 75, 162, 0.02));
    pointer-events: none;
}

.filter-buttons-container:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 35px rgba(0, 0, 0, 0.12),
        0 5px 15px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 1);
}

/* Botões individuais - design elegante */
.filter-button {
    cursor: pointer;
    height: 52px;
    padding: 14px 18px;
    background: transparent;
    justify-content: center;
    align-items: center;
    gap: 10px;
    display: inline-flex;
    white-space: nowrap;
    flex-shrink: 0;
    border: none;
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.filter-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(49, 140, 213, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.filter-button:hover::before {
    opacity: 1;
}

.filter-button:first-child {
    border-radius: 16px 0px 0px 16px !important;
}

.filter-button:last-child {
    border-right: none;
    border-radius: 0px 16px 16px 0px !important;
}

/* Hover state elegante */
.filter-button:hover {
    transform: translateY(-1px) scale(1.02);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
    box-shadow: 
        0 4px 15px rgba(49, 140, 213, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Estado ativo - muito mais bonito */
.filter-button.active {
    background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%) !important;
    transform: translateY(-2px);
    box-shadow: 
        0 8px 25px rgba(49, 140, 213, 0.3),
        0 4px 12px rgba(49, 140, 213, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: transparent;
}

.filter-button.active::before {
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
    opacity: 1;
}

.filter-button.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Texto dos botões - tipografia refinada */
.filter-button-text {
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.3px;
    word-wrap: break-word;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.filter-button:hover .filter-button-text {
    color: #1a252f;
    transform: translateY(-0.5px);
}

.filter-button.active .filter-button-text {
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    font-weight: 700;
}

/* Ícones com animações sutis */
.filter-icon {
    font-size: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.filter-button:hover .filter-icon {
    transform: scale(1.1) rotate(5deg);
}

.filter-button.active .filter-icon {
    transform: scale(1.05);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Efeitos específicos por tipo de botão */
.filter-button-todos:hover {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.1) 0%, rgba(248, 249, 250, 0.9) 100%);
}

.filter-button-delivery:hover {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(248, 249, 250, 0.9) 100%);
}

.filter-button-balcao:hover {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(248, 249, 250, 0.9) 100%);
}

.filter-button-mesas:hover {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(248, 249, 250, 0.9) 100%);
}

/* Estados de focus para acessibilidade */
.filter-button:focus {
    outline: none;
    box-shadow: 
        0 0 0 3px rgba(49, 140, 213, 0.2),
        0 4px 15px rgba(49, 140, 213, 0.1);
}

.filter-button.active:focus {
    box-shadow: 
        0 0 0 3px rgba(255, 255, 255, 0.3),
        0 8px 25px rgba(49, 140, 213, 0.3),
        0 4px 12px rgba(49, 140, 213, 0.2);
}

/* Responsividade aprimorada */
.filter-buttons-container.mobile {
    width: 100%;
    max-width: 100%;
    border-radius: 12px !important;
}

.filter-buttons-container.mobile .filter-button {
    flex: 1;
    min-width: 0;
    height: 48px;
    padding: 12px 8px;
}

.filter-buttons-container.mobile .filter-button:first-child {
    border-radius: 12px 0px 0px 12px !important;
}

.filter-buttons-container.mobile .filter-button:last-child {
    border-radius: 0px 12px 12px 0px !important;
}

@media (max-width: 768px) {
    .filter-button.mobile {
        min-width: 70px;
        width: auto;
        padding: 10px 6px;
        height: 44px;
    }
    
    .filter-button.mobile .filter-button-text {
        font-size: 12px;
        font-weight: 600;
    }
    
    .filter-button.mobile .filter-icon {
        font-size: 16px;
    }
    
    .filter-buttons-container {
        border-radius: 10px !important;
    }
}

@media (max-width: 480px) {
    .filter-button.mobile {
        padding: 8px 4px;
        height: 40px;
    }
    
    .filter-button.mobile .filter-button-text {
        font-size: 11px;
        gap: 6px;
    }
    
    .filter-button.mobile .filter-icon {
        font-size: 14px;
    }
}

@media (max-width: 360px) {
    .filter-button.mobile {
        padding: 6px 3px;
        height: 38px;
    }
    
    .filter-button.mobile .filter-button-text {
        font-size: 10px;
        gap: 4px;
    }
    
    .filter-button.mobile .filter-icon {
        font-size: 12px;
    }
}

/* Animação de entrada suave */
@keyframes filterButtonFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-buttons-container {
    animation: filterButtonFadeIn 0.6s ease-out;
}

/* Efeito de pulso sutil para o botão ativo */
@keyframes activePulse {
    0%, 100% {
        box-shadow: 
            0 8px 25px rgba(49, 140, 213, 0.3),
            0 4px 12px rgba(49, 140, 213, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    50% {
        box-shadow: 
            0 10px 30px rgba(49, 140, 213, 0.35),
            0 5px 15px rgba(49, 140, 213, 0.25),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
}

.filter-button.active {
    animation: activePulse 3s infinite ease-in-out;
}

/* Melhorias na scrollbar para mobile */
.filter-buttons-container::-webkit-scrollbar {
    display: none;
}

.filter-buttons-container {
    scrollbar-width: none;
    -webkit-overflow-scrolling: touch;
}

/* ======================== BOTÃO NOVO PEDIDO MODERNO ======================== */
/* Usando especificidade alta para sobrescrever outras regras CSS */

.header-mobile-kanban .div-buttons .continue-button button {
    background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    /*padding: 14px 20px !important;*/
    box-shadow: 
        0 6px 20px rgba(49, 140, 213, 0.25),
        0 3px 10px rgba(49, 140, 213, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    cursor: pointer !important;
    min-height: 52px !important;
    height: 52px !important;
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-top: 0 !important;
}

.header-mobile-kanban .div-buttons .continue-button button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 
        0 8px 28px rgba(49, 140, 213, 0.3),
        0 4px 15px rgba(49, 140, 213, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.header-mobile-kanban .div-buttons .continue-button button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.header-mobile-kanban .div-buttons .continue-button button:hover::before {
    left: 100%;
}

.header-mobile-kanban .div-buttons .continue-button button a {
    color: white !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    text-decoration: none !important;
    position: relative !important;
    z-index: 1 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.header-mobile-kanban .div-buttons .continue-button button svg {
    color: white !important;
    position: relative !important;
    z-index: 1 !important;
    transition: transform 0.3s ease !important;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
}

.header-mobile-kanban .div-buttons .continue-button button:hover svg {
    transform: scale(1.1) !important;
}

.header-mobile-kanban .div-buttons .continue-button button:focus {
    outline: none !important;
    box-shadow: 
        0 0 0 3px rgba(49, 140, 213, 0.2),
        0 6px 20px rgba(49, 140, 213, 0.25),
        0 3px 10px rgba(49, 140, 213, 0.15) !important;
}

.header-mobile-kanban .div-buttons .continue-button button:active {
    transform: translateY(-1px) !important;
    box-shadow: 
        0 4px 15px rgba(49, 140, 213, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}
