const router = require('express').Router(); 
const Empresa = require('../models/Empresa');
const User = require('../models/User');
const bcrypt = require('bcrypt')
const jwt = require('jsonwebtoken')
var mongoose = require('mongoose');

router.post("/login", async (req, res) => {

	/* #swagger.security = [{}] */
	/*
		#swagger.path = '/api/v1/auth/login'
		#swagger.tags = ['Auth']
		#swagger.summary = 'Login de usuário'
		#swagger.description = 'Login de usuário'
	*/
	const { email, password } = req.body

	//validations
	if (!email) {
		return res.status(401).json({ msg: 'O email é obrigatório!' })
	}

	if (!password) {
		return res.status(401).json({ msg: 'A senha é obrigatória!' })
	}

	//Checar se usuario existe
	const user = await User.findOne({ email: email })

	if (!user) {
		return res.status(401).json({ msg: 'Usuário não encontrado!' })
	}

	//Check if password match
	const checkPassword = await bcrypt.compare(password, user.password)

	if (!checkPassword) {
		return res.status(401).json({ msg: 'Senha inválida!' })
	}

	//TESTE
	const userSS = await User.findOne({ email: email }, 'name email vinculo_empresa role user_img bloqueado inativo')
	//const userSS = await User.findOne({ email: email},'name email vinculo_empresa user_img')

	const empresa = await Empresa.findOne({ "cnpj": userSS.vinculo_empresa })

	console.log('user', userSS.role === 'garcom');

	if (userSS.bloqueado || userSS.inativo || userSS.role === 'garcom') {
		return res.status(401).json({ msg: 'Usuário bloqueado ou inativo!' })
	}

	//const userSS = await User.findById(user.id, '-password')
	//FIM TESTE

	try {

		const secret = process.env.SECRET

		const token = jwt.sign({
			id: user.id,
			email: user.email,
			vinculo_empresa: user.vinculo_empresa,
			empresa_id: empresa.id
		},
			secret,
		)

		return res.status(200).json({ msg: 'Autenticação realizada com sucesso!', user: userSS, empresa, token })

	} catch (error) {

		console.log(error)

		res
			.status(500)
			.json({
				msg: 'Aconteceu um erro no servidor, tente novamente mais tarde!',
			})
	}
})

router.post('/garcom/login', async (req, res) => {

	/* #swagger.security = [{}] */
	/*
		#swagger.path = '/api/v1/auth/garcom/login'
		#swagger.tags = ['Auth']
		#swagger.summary = 'Login de garçom'
		#swagger.description = 'Login de garçom'
	*/
	const { email, password } = req.body

	//validations
	if (!email) {
		return res.status(401).json({ msg: 'O email é obrigatório!' })
	}

	if (!password) {
		return res.status(401).json({ msg: 'A senha é obrigatória!' })
	}

	//Checar se usuario existe
	const user = await User.findOne({ email: email })

	if (!user) {
		return res.status(401).json({ msg: 'Usuário não encontrado!' })
	}

	//Check if password match
	const checkPassword = await bcrypt.compare(password, user.password)

	if (!checkPassword) {
		return res.status(401).json({ msg: 'Não foi possível autenticar.' })
	}

	

})

module.exports = router;