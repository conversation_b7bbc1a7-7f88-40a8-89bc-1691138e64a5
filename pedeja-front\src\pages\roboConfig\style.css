/* Estilos únicos para a página de respostas personalizadas do robô */
.robo-respostas-container {
    width: 100%;
    padding: 2rem;
    background-color: #f8f9fa;
    min-height: 100vh;
}

.robo-respostas-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%);
    padding: 2rem;
    border-radius: 12px;
    color: white;
    box-shadow: 0 4px 20px rgba(49, 140, 213, 0.3);
}

.robo-header-content h1 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 600;
}

.robo-header-content p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

.robo-btn-nova-resposta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 0.2rem;
    min-width: 160px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.robo-btn-nova-resposta:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.robo-respostas-search {
    margin-bottom: 2rem;
}

.robo-search-input-container {
    position: relative;
    max-width: 500px;
}

.robo-search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1rem;
}

.robo-search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    transition: all 0.3s ease;
}

.robo-search-input:focus {
    outline: none;
    border-color: #318CD5;
    box-shadow: 0 0 0 3px rgba(49, 140, 213, 0.1);
}

.robo-respostas-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.robo-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.robo-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #318CD5;
    border-radius: 50%;
    animation: robo-spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes robo-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.robo-empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.robo-empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.robo-empty-state h3 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1.5rem;
}

.robo-empty-state p {
    margin: 0 0 2rem 0;
    font-size: 1.1rem;
}

.robo-btn-add-first {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.robo-btn-add-first:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(49, 140, 213, 0.3);
}

.robo-respostas-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}
.robo-resposta-card {
    position: relative;
    background: white;
    border-radius: 16px;
    padding: 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(49, 140, 213, 0.08);
    border: 1px solid rgba(49, 140, 213, 0.1);
    overflow: hidden;
}

.robo-resposta-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%);
    opacity: 1;
    transition: opacity 0.3s ease;
}

.robo-resposta-card.inactive::before {
    background: linear-gradient(135deg, #95a5a6 0%, #bdc3c7 100%);
}

.robo-resposta-card.inactive {
    opacity: 0.7;
    background: #f8f9fa;
}

.robo-resposta-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(49, 140, 213, 0.15);
}

.robo-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.robo-card-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.robo-status-indicator {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s ease;
}

.robo-status-indicator.active {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.robo-status-indicator.inactive {
    background: linear-gradient(135deg, #95a5a6 0%, #bdc3c7 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(149, 165, 166, 0.3);
}

.robo-status-text {
    font-weight: 600;
    font-size: 14px;
    color: #2c3e50;
}

.robo-card-number {
    background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(49, 140, 213, 0.3);
}

.robo-resposta-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 24px;
}

.robo-section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.robo-section-icon {
    color: #318CD5;
    font-size: 16px;
}

.robo-resposta-question,
.robo-resposta-response {
    display: flex;
    flex-direction: column;
}

.robo-question-label,
.robo-response-label {
    font-weight: 600;
    color: #318CD5;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.robo-question-text,
.robo-response-text {
    margin: 0;
    color: #2c3e50;
    line-height: 1.6;
    font-size: 14px;
    white-space: pre-wrap;
    word-wrap: break-word;
    background: #f8f9fa;
    padding: 16px;
    border-radius: 12px;
    border-left: 4px solid #318CD5;
}

.robo-resposta-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

.robo-btn-toggle,
.robo-btn-edit,
.robo-btn-delete {
    padding: 10px 16px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    min-width: 44px;
    height: 44px;
}

.robo-btn-toggle.active {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.robo-btn-toggle.inactive {
    background: linear-gradient(135deg, #95a5a6 0%, #bdc3c7 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
}

.robo-btn-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(39, 174, 96, 0.4);
}

.robo-btn-edit {
    background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(49, 140, 213, 0.3);
}

.robo-btn-edit:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(49, 140, 213, 0.4);
}

.robo-btn-delete {
    background: linear-gradient(135deg, #e74c3c 0%, #ec7063 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.robo-btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(231, 76, 60, 0.4);
}

.robo-resposta-index {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    font-size: 0.75rem;
    color: #6c757d;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.robo-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.robo-pagination-btn {
    padding: 0.5rem 1rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
    transition: all 0.3s ease;
}

.robo-pagination-btn:hover:not(:disabled) {
    border-color: #318CD5;
    color: #318CD5;
}

.robo-pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.robo-pagination-numbers {
    display: flex;
    gap: 0.5rem;
}

.robo-pagination-number {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
    transition: all 0.3s ease;
}

.robo-pagination-number:hover {
    border-color: #318CD5;
    color: #318CD5;
}

.robo-pagination-number.active {
    background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%);
    border-color: #318CD5;
    color: white;
}

.robo-delete-confirm-modal {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    max-width: 400px;
    width: 100%;
    text-align: center;
}

.robo-delete-confirm-header {
    margin-bottom: 1.5rem;
}

.robo-delete-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.robo-delete-confirm-header h3 {
    margin: 0;
    color: #495057;
    font-size: 1.25rem;
}

.robo-delete-confirm-modal p {
    margin: 0 0 2rem 0;
    color: #6c757d;
    line-height: 1.5;
}

.robo-delete-confirm-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.robo-btn-cancel-delete,
.robo-btn-confirm-delete {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.robo-btn-cancel-delete {
    background: #6c757d;
    color: white;
}

.robo-btn-cancel-delete:hover {
    background: #5a6268;
}

.robo-btn-confirm-delete {
    background: #dc3545;
    color: white;
}

.robo-btn-confirm-delete:hover {
    background: #c82333;
}

/* Responsividade */
@media (max-width: 768px) {
    .robo-respostas-container {
        padding: 1rem;
    }

    .robo-respostas-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .robo-header-content h1 {
        font-size: 1.5rem;
    }

    .robo-resposta-content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .robo-resposta-actions {
        position: static;
        justify-content: center;
        margin-top: 1rem;
    }

    .robo-resposta-index {
        position: static;
        text-align: center;
        margin-top: 1rem;
    }
}
