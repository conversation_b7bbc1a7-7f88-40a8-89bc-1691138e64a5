import React, { useState, useContext, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import './style.css';
import { SidebarContext } from "../../AppRoutes";
import PermissionGate from "../../services/PermissionGate";
import LeftMenu from "../../components/LeftMenu";
import { Modal } from "../../components/Modal";
import ModalAddCustomResponse from "../../components/ModalAddCustomResponse";
import ModalEditCustomResponse from "../../components/ModalEditCustomResponse";
import styled from 'styled-components';
import { FaEdit, FaTrash, FaPlus, FaSearch, FaToggleOn, FaToggleOff, FaRobot, FaComments } from "react-icons/fa";
import { IoMdClose } from "react-icons/io";
import { MdQuestionAnswer } from "react-icons/md";
import { BiMessageDetail } from "react-icons/bi";
import CryptoJS from 'crypto-js';
import { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from "../../services/api";
import { toast } from "react-toastify";

const RoboConfigContainer = styled.div`
    display: flex;
    margin-left: ${({ sidebar }) => (sidebar ? '250px' : '100px')};
    height: auto;
    width: auto;
    transition: 150ms;
    background-color: rgb(247,247,247) !important;
    overflow: initial;
    z-index: 9;

    @media (max-width: 880px) {
        margin-left: 0;
    }
`;

const RoboCfg = () => {
    const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';
    const userEncrypted = localStorage.getItem('user');
    const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);
    const userParse = JSON.parse(user);
    const empresa = localStorage.getItem('empresa');
    const empresaParse = JSON.parse(empresa);
    const empresaObjId = empresaParse._id;

    const { sidebar } = useContext(SidebarContext);
    const navigate = useNavigate();

    const [customResponses, setCustomResponses] = useState([]);
    const [filteredResponses, setFilteredResponses] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [editingData, setEditingData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(5);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [deleteIndex, setDeleteIndex] = useState(null);

    // Carregar respostas personalizadas
    useEffect(() => {
        fetchCustomResponses();
    }, []);

    // Filtrar respostas baseado na busca
    useEffect(() => {
        if (searchTerm) {
            const filtered = customResponses.filter(response =>
                response.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                response.response.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilteredResponses(filtered);
        } else {
            setFilteredResponses(customResponses);
        }
    }, [searchTerm, customResponses]);

    const fetchCustomResponses = async () => {
        try {
            setLoading(true);
            const response = await getCustomResponses(empresaObjId);
            setCustomResponses(response.data.customResponses || []);
        } catch (error) {
            console.error("Erro ao buscar respostas personalizadas:", error);
            toast.error("Erro ao carregar respostas personalizadas");
        } finally {
            setLoading(false);
        }
    };

    // A nova estrutura já vem como objeto, não precisa mais fazer parsing
    // Mantendo a função para compatibilidade, mas agora apenas retorna o objeto
    const parseCustomResponse = (responseObj) => {
        if (typeof responseObj === 'object' && responseObj.question && responseObj.response) {
            return {
                question: responseObj.question,
                response: responseObj.response
            };
        }
        return {
            question: 'Pergunta não encontrada',
            response: 'Resposta não encontrada'
        };
    };

    const handleAddResponse = async (values, { setSubmitting, resetForm }) => {
        try {
            await addCustomResponse(empresaObjId, values.question, values.response);
            toast.success("Resposta personalizada adicionada com sucesso!");
            setIsAddModalOpen(false);
            resetForm();
            fetchCustomResponses();
        } catch (error) {
            console.error("Erro ao adicionar resposta:", error);
            toast.error("Erro ao adicionar resposta personalizada");
        } finally {
            setSubmitting(false);
        }
    };

    const handleEditResponse = async (values, { setSubmitting }) => {
        try {
            await updateCustomResponse(empresaObjId, editingData.id, values.question, values.response);
            toast.success("Resposta personalizada atualizada com sucesso!");
            setIsEditModalOpen(false);
            setEditingData(null);
            fetchCustomResponses();
        } catch (error) {
            console.error("Erro ao atualizar resposta:", error);
            toast.error("Erro ao atualizar resposta personalizada");
        } finally {
            setSubmitting(false);
        }
    };

    const handleToggleActive = async (responseObj) => {
        try {
            await updateCustomResponse(empresaObjId, responseObj.id, responseObj.question, responseObj.response, !responseObj.active);
            toast.success(`Resposta ${!responseObj.active ? 'ativada' : 'desativada'} com sucesso!`);
            fetchCustomResponses();
        } catch (error) {
            console.error("Erro ao alterar status da resposta:", error);
            toast.error("Erro ao alterar status da resposta!");
        }
    };

    const handleDeleteResponse = async () => {
        if (deleteIndex !== null) {
            try {
                await deleteCustomResponse(empresaObjId, deleteIndex);
                toast.success("Resposta personalizada removida com sucesso!");
                setShowDeleteConfirm(false);
                setDeleteIndex(null);
                fetchCustomResponses();
                // Ajustar página se necessário
                const totalPages = Math.ceil((customResponses.length - 1) / itemsPerPage);
                if (currentPage > totalPages && totalPages > 0) {
                    setCurrentPage(totalPages);
                }
            } catch (error) {
                console.error("Erro ao remover resposta:", error);
                toast.error("Erro ao remover resposta personalizada");
            }
        }
    };

    const confirmDelete = (responseObj) => {
        setDeleteIndex(responseObj.id);
        setShowDeleteConfirm(true);
    };

    const openEditModal = (responseObj) => {
        const parsed = parseCustomResponse(responseObj);
        setEditingData({ ...parsed, id: responseObj.id });
        setIsEditModalOpen(true);
    };

    // Paginação
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredResponses.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredResponses.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    return (
        <>
            <PermissionGate permissions={['default']}>

                <RoboConfigContainer sidebar={sidebar}>
                    <div className="robo-respostas-container">
                        <div className="robo-respostas-header">
                            <div className="robo-header-content">
                                <h4>Respostas personalizadas</h4>
                                <p>Configure respostas personalizadas para que o bot responda de acordo com seu restaurante.</p>
                            </div>
                            <button
                                className="robo-btn-nova-resposta"
                                onClick={() => setIsAddModalOpen(true)}
                            >
                                <FaPlus /> Nova resposta
                            </button>
                        </div>

                        <div className="robo-respostas-search">
                            <div className="robo-search-input-container">
                                <FaSearch className="robo-search-icon" />
                                <input
                                    type="text"
                                    placeholder="Buscar por pergunta ou resposta..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="robo-search-input"
                                />
                            </div>
                        </div>

                        <div className="robo-respostas-content">
                            {loading ? (
                                <div className="robo-loading-container">
                                    <div className="robo-loading-spinner"></div>
                                    <p>Carregando respostas...</p>
                                </div>
                            ) : filteredResponses.length === 0 ? (
                                <div className="robo-empty-state">
                                    <div className="robo-empty-icon">💬</div>
                                    <h3>Nenhuma resposta personalizada encontrada</h3>
                                    <p>
                                        {searchTerm
                                            ? "Nenhuma resposta corresponde à sua busca."
                                            : "Comece adicionando sua primeira resposta personalizada."
                                        }
                                    </p>
                                    {!searchTerm && (
                                        <button
                                            className="robo-btn-add-first"
                                            onClick={() => setIsAddModalOpen(true)}
                                        >
                                            <FaPlus /> Adicionar primeira resposta
                                        </button>
                                    )}
                                </div>
                            ) : (
                                <>
                                    <div className="robo-respostas-list">
                                        {currentItems.map((responseObj, index) => {
                                            const parsed = parseCustomResponse(responseObj);
                                            return (
                                                <div key={responseObj.id} className={`robo-resposta-card ${!responseObj.active ? 'inactive' : ''}`}>
                                                    <div className="robo-card-header">
                                                        <div className="robo-card-status">
                                                            <div className={`robo-status-indicator ${responseObj.active ? 'active' : 'inactive'}`}>
                                                                {responseObj.active ? <FaRobot /> : <FaRobot />}
                                                            </div>
                                                            <span className="robo-status-text">
                                                                {responseObj.active ? 'Ativa' : 'Inativa'}
                                                            </span>
                                                        </div>
                                                        <div className="robo-card-number">
                                                            #{(currentPage - 1) * itemsPerPage + index + 1}
                                                        </div>
                                                    </div>

                                                    <div className="robo-resposta-content">
                                                        <div className="robo-resposta-question">
                                                            <div className="robo-section-header">
                                                                <MdQuestionAnswer className="robo-section-icon" />
                                                                <span className="robo-question-label">Quando usar essa resposta</span>
                                                            </div>
                                                            <p className="robo-question-text">{parsed.question}</p>
                                                        </div>

                                                        <div className="robo-resposta-response">
                                                            <div className="robo-section-header">
                                                                <BiMessageDetail className="robo-section-icon" />
                                                                <span className="robo-response-label">Instruções para a Resposta</span>
                                                            </div>
                                                            <p className="robo-response-text">{parsed.response}</p>
                                                        </div>
                                                    </div>

                                                    <div className="robo-resposta-actions">
                                                        <button
                                                            className={`robo-btn-toggle ${responseObj.active ? 'active' : 'inactive'}`}
                                                            onClick={() => handleToggleActive(responseObj)}
                                                            title={responseObj.active ? 'Desativar' : 'Ativar'}
                                                        >
                                                            {responseObj.active ? <FaToggleOn /> : <FaToggleOff />}
                                                        </button>
                                                        <button
                                                            className="robo-btn-edit"
                                                            onClick={() => openEditModal(responseObj)}
                                                            title="Editar"
                                                        >
                                                            <FaEdit />
                                                        </button>
                                                        <button
                                                            className="robo-btn-delete"
                                                            onClick={() => confirmDelete(responseObj)}
                                                            title="Excluir"
                                                        >
                                                            <FaTrash />
                                                        </button>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>

                                    {/* Paginação */}
                                    {totalPages > 1 && (
                                        <div className="robo-pagination">
                                            <button
                                                className="robo-pagination-btn"
                                                onClick={() => paginate(currentPage - 1)}
                                                disabled={currentPage === 1}
                                            >
                                                Anterior
                                            </button>

                                            <div className="robo-pagination-numbers">
                                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                                                    <button
                                                        key={number}
                                                        className={`robo-pagination-number ${currentPage === number ? 'active' : ''}`}
                                                        onClick={() => paginate(number)}
                                                    >
                                                        {number}
                                                    </button>
                                                ))}
                                            </div>

                                            <button
                                                className="robo-pagination-btn"
                                                onClick={() => paginate(currentPage + 1)}
                                                disabled={currentPage === totalPages}
                                            >
                                                Próxima
                                            </button>
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    </div>
                </RoboConfigContainer>

                {/* Modais */}
                <ModalAddCustomResponse
                    isOpen={isAddModalOpen}
                    onClose={() => setIsAddModalOpen(false)}
                    onSubmit={handleAddResponse}
                />

                <ModalEditCustomResponse
                    isOpen={isEditModalOpen}
                    onClose={() => {
                        setIsEditModalOpen(false);
                        setEditingData(null);
                    }}
                    onSubmit={handleEditResponse}
                    initialData={editingData}
                />

                {/* Modal de confirmação de exclusão */}
                <Modal isOpen={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>
                    <div className="robo-delete-confirm-modal">
                        <div className="robo-delete-confirm-header">
                            <div className="robo-delete-icon">🗑️</div>
                            <h3>Confirmar exclusão</h3>
                        </div>
                        <p>Tem certeza que deseja excluir esta resposta personalizada? Esta ação não pode ser desfeita.</p>
                        <div className="robo-delete-confirm-actions">
                            <button
                                className="robo-btn-cancel-delete"
                                onClick={() => {
                                    setShowDeleteConfirm(false);
                                    setDeleteIndex(null);
                                }}
                            >
                                Cancelar
                            </button>
                            <button
                                className="robo-btn-confirm-delete"
                                onClick={handleDeleteResponse}
                            >
                                Excluir
                            </button>
                        </div>
                    </div>
                </Modal>
            </PermissionGate>
        </>
    )
}

export default RoboCfg;