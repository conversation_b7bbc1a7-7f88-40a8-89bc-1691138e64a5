{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\components\\\\Modal\\\\index.jsx\";\nimport { IoClose } from \"react-icons/io5\";\nimport './styles.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function Modal({\n  isOpen,\n  onClose,\n  children\n}) {\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `modal-overlay ${isOpen ? 'fadeIn' : 'fadeOut'}`,\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-container\",\n      onClick: event => event.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"close-button\",\n        onClick: onClose,\n        children: /*#__PURE__*/_jsxDEV(IoClose, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 68\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 9\n  }, this);\n}\n_c = Modal;\nvar _c;\n$RefreshReg$(_c, \"Modal\");", "map": {"version": 3, "names": ["IoClose", "jsxDEV", "_jsxDEV", "Modal", "isOpen", "onClose", "children", "className", "onClick", "event", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/components/Modal/index.jsx"], "sourcesContent": ["import { IoClose } from \"react-icons/io5\";\r\nimport './styles.css'\r\n\r\nexport function Modal({ isOpen, onClose, children}){\r\n    if (!isOpen) return null;\r\n\r\n    return (\r\n        <div className={`modal-overlay ${isOpen ? 'fadeIn' : 'fadeOut'}`} onClick={onClose}>\r\n            <div className=\"modal-container\" onClick={(event) => event.stopPropagation()} >\r\n                <button className=\"close-button\" onClick={onClose}><IoClose /></button>\r\n                {children}\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,OAAO,cAAc;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAErB,OAAO,SAASC,KAAKA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAQ,CAAC,EAAC;EAC/C,IAAI,CAACF,MAAM,EAAE,OAAO,IAAI;EAExB,oBACIF,OAAA;IAAKK,SAAS,EAAE,iBAAiBH,MAAM,GAAG,QAAQ,GAAG,SAAS,EAAG;IAACI,OAAO,EAAEH,OAAQ;IAAAC,QAAA,eAC/EJ,OAAA;MAAKK,SAAS,EAAC,iBAAiB;MAACC,OAAO,EAAGC,KAAK,IAAKA,KAAK,CAACC,eAAe,CAAC,CAAE;MAAAJ,QAAA,gBACzEJ,OAAA;QAAQK,SAAS,EAAC,cAAc;QAACC,OAAO,EAAEH,OAAQ;QAAAC,QAAA,eAACJ,OAAA,CAACF,OAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACtER,QAAQ;IAAA;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACC,EAAA,GAXeZ,KAAK;AAAA,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}