import React, { useState, useContext, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import './style.css'
import PermissionGate from "../../services/PermissionGate";
import LeftMenu from "../../components/LeftMenu";
import styled from 'styled-components';
import QRCode from 'qrcode.react';
import { SidebarContext } from "../../AppRoutes";
import * as AiIcons from 'react-icons/ai'
import * as SlIcons from 'react-icons/sl'
import * as MdIcons from 'react-icons/md'
import * as FaIcons from 'react-icons/fa'
import { testeImpressao } from "../../services/api";

const Teste = styled.div`

    display: flex;
    margin-left:  ${({ sidebar }) => (sidebar ? '250px' : '100px')}; 
    height:auto;
    width:auto;
    transition: 150ms;
    background-color:rgb(247,247,247)!important;
    overflow: initial;
    z-Index: 9;

    @media (max-width: 880px) {
        margin-left: 0;
    }
`;

const ConfigPrinter = () => {

    const { sidebar, setSidebar } = useContext(SidebarContext);; //TESTANDO CONST NO AppROUTES
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [currentStep, setCurrentStep] = useState(1);
    const navigate = useNavigate();

    const empresa = localStorage.getItem('empresa')
    const empresaParse = JSON.parse(empresa)
    const idEmpresa = empresaParse.id_empresa;
    const objIdEmpresa = empresaParse._id;

    const qrCodeData = 'https://www.example.com';

    const handleBack = () => {
        //setSidebar(!sidebar)
        navigate("/list-item");
    }

    const downloadInstallationPackage = async () => {
        try {
            // Endpoint da GitHub API para obter as releases
            const releasesUrl = 'https://api.github.com/repos/PedeJaSoftware/PedeJaPrint-Release/releases/latest';
            
            // Obtém a última release
            const response = await fetch(releasesUrl);
            if (!response.ok) {
                throw new Error('Não foi possível obter as releases do GitHub');
            }
            const data = await response.json();
    
            // Encontra o link para o arquivo PedeJaPrintSetup.exe na última release
            const setupAsset = data.assets.find(asset => asset.name === 'PedeJaPrintSetup.exe');
            if (!setupAsset) {
                throw new Error('Arquivo PedeJaPrintSetup.exe não encontrado na última release');
            }
    
            // URL para download do arquivo
            const downloadUrl = setupAsset.browser_download_url;
    
            // Inicia o download
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = 'PedeJaPrintSetup.exe'; // Nome do arquivo ao salvar
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            console.log('Download iniciado para:', downloadUrl);
            setCurrentStep(2); // Avança para o próximo passo
        } catch (error) {
            console.error('Erro ao baixar o pacote de instalação:', error);
        }
    };

    const handlePrinterTest = async () => {
        const response = await testeImpressao(objIdEmpresa)
        console.log("Resposta do print test:",response)
        if (response && response.success) {
            setCurrentStep(3); // Avança para finalização
        }
    }

    const handleAlreadyDownloaded = () => {
        setCurrentStep(2); // Avança para a etapa de teste
    }

    return (
        <>
        <PermissionGate permissions={['default']}>
    

            {/*<LeftMenu setSidebar={setSidebar} sidebar={sidebar} />*/}

            <Teste sidebar={sidebar}>
                <div className="w-100 p-4">
                    <div className="printer-config-container">
                        <div className="form-header">
                            <div className="title">
                                <h2><MdIcons.MdPrint className="header-icon" />Configurar Impressora de Comandas</h2>
                                <p className="subtitle">Configure sua impressora para receber automaticamente os pedidos dos seus clientes</p>
                            </div>
                        </div>

                        <div className="info-banner">
                            <div className="info-content">
                                <AiIcons.AiOutlineInfoCircle className="info-icon" />
                                <div>
                                    <h3>Por que preciso do software cliente?</h3>
                                    <p>Nosso software cliente conecta sua impressora de comandas diretamente ao sistema PedeJá. Isso permite que os pedidos sejam impressos automaticamente, sem necessidade de intervenção manual.</p>
                                </div>
                            </div>
                        </div>

                        <div className="steps-container">
                            <div className={`step-card ${currentStep >= 1 ? 'active' : ''}`}>
                                <div className="step-header">
                                    <div className={`step-number ${currentStep > 1 ? 'completed' : ''}`}>
                                        {currentStep > 1 ? <FaIcons.FaCheck /> : '1'}
                                    </div>
                                    <h2>Baixar o Software Cliente</h2>
                                </div>
                                <div className="step-content">
                                    <p>Primeiro, você precisa baixar e instalar nosso software cliente que fará a conexão entre o sistema PedeJá e sua impressora.</p>
                                    <div className="step-actions">
                                        <button 
                                            onClick={downloadInstallationPackage} 
                                            className="download-button"
                                            type="button"
                                        >
                                            <AiIcons.AiOutlineDownload className="button-icon" />
                                            Baixar PedeJá Print
                                        </button>
                                        <button 
                                            onClick={handleAlreadyDownloaded} 
                                            className="already-downloaded-button"
                                            type="button"
                                        >
                                            <AiIcons.AiOutlineCheckCircle className="button-icon" />
                                            Já Baixei o Software
                                        </button>
                                    </div>
                                    <div className="step-info">
                                        <small>
                                            <AiIcons.AiOutlineWindows className="os-icon" />
                                            Compatível com Windows 10 ou superior
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div className={`step-card ${currentStep >= 2 ? 'active' : 'disabled'}`}>
                                <div className="step-header">
                                    <div className={`step-number ${currentStep > 2 ? 'completed' : ''}`}>
                                        {currentStep > 2 ? <FaIcons.FaCheck /> : '2'}
                                    </div>
                                    <h2>Instalar, Autenticar e Configurar</h2>
                                </div>
                                <div className="step-content">
                                    <p>Execute o arquivo baixado e realize o login com o seu email e senha do PedeJá. O software irá listar suas impressoras e você deve selecionar a impressora que deseja usar.</p>
                                    <div className="installation-tips">
                                        <h4>Dicas de instalação:</h4>
                                        <ul>
                                            <li>Certifique-se de que sua impressora está conectada</li>
                                            <li>Execute como administrador se necessário</li>
                                            <li>Mantenha o software sempre aberto para receber pedidos</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div className={`step-card ${currentStep >= 2 ? 'active' : 'disabled'}`}>
                                <div className="step-header">
                                    <div className={`step-number ${currentStep > 2 ? 'completed' : ''}`}>
                                        {currentStep > 2 ? <FaIcons.FaCheck /> : '3'}
                                    </div>
                                    <h2>Testar Impressão</h2>
                                </div>
                                <div className="step-content">
                                    <p>Após instalar o software, teste a conexão com sua impressora para garantir que tudo está funcionando corretamente.</p>
                                    <div className="step-actions">
                                        <button 
                                            type="button" 
                                            onClick={handlePrinterTest}
                                            className="test-button"
                                            disabled={currentStep < 2}
                                        >
                                            <MdIcons.MdPrint className="button-icon" />
                                            Testar Impressora
                                        </button>
                                    </div>
                                    <div className="step-info">
                                        <small>
                                            <AiIcons.AiOutlineCheckCircle className="check-icon" />
                                            Um comando de teste será impresso em sua impressora
                                        </small>
                                    </div>
                                </div>
                            </div>

                            {currentStep >= 3 && (
                                <div className="success-card">
                                    <div className="success-content">
                                        <FaIcons.FaCheckCircle className="success-icon" />
                                        <h2>Configuração Concluída!</h2>
                                        <p>Sua impressora está configurada e pronta para receber pedidos automaticamente. Os comandos serão impressos assim que os pedidos forem confirmados.</p>
                                    </div>
                                </div>
                            )}
                        </div>

                    </div>
                </div>
            </Teste>
                
        </PermissionGate>       
        </>
    )
}

export default ConfigPrinter;