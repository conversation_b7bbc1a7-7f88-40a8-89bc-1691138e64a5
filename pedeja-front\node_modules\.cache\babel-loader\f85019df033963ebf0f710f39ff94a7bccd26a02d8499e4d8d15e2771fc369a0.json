{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\n\n/**\r\n * Hook customizado para preload de imagens\r\n * @param {string|string[]} imageSources - URL da imagem ou array de URLs\r\n * @param {number} timeout - Timeout em ms (padrão: 5000)\r\n * @returns {object} { loaded, error, progress }\r\n */\nconst useImagePreloader = (imageSources, timeout = 5000) => {\n  _s();\n  const [loaded, setLoaded] = useState(false);\n  const [error, setError] = useState(null);\n  const [progress, setProgress] = useState(0);\n  useEffect(() => {\n    if (!imageSources) {\n      setLoaded(true);\n      return;\n    }\n    const sources = Array.isArray(imageSources) ? imageSources : [imageSources];\n    let loadedCount = 0;\n    let hasError = false;\n    console.log(`📸 Iniciando preload de ${sources.length} imagem(ns)...`);\n    const images = sources.map(src => {\n      const img = new Image();\n      img.onload = () => {\n        loadedCount++;\n        const progressPercent = loadedCount / sources.length * 100;\n        setProgress(progressPercent);\n        console.log(`✅ Imagem carregada: ${src} (${loadedCount}/${sources.length})`);\n        if (loadedCount === sources.length && !hasError) {\n          console.log(`🎉 Todas as ${sources.length} imagens carregadas com sucesso!`);\n          setLoaded(true);\n        }\n      };\n      img.onerror = err => {\n        hasError = true;\n        console.error(`❌ Erro ao carregar imagem: ${src}`, err);\n        setError(`Erro ao carregar imagem: ${src}`);\n\n        // Marcar como carregado mesmo com erro para não travar a interface\n        loadedCount++;\n        const progressPercent = loadedCount / sources.length * 100;\n        setProgress(progressPercent);\n        if (loadedCount === sources.length) {\n          console.log(`⚠️ Preload finalizado com alguns erros`);\n          setLoaded(true);\n        }\n      };\n      img.src = src;\n      return img;\n    });\n\n    // Fallback de segurança com timeout\n    const fallbackTimer = setTimeout(() => {\n      if (!loaded) {\n        console.log(`⏰ Timeout de ${timeout}ms atingido - marcando imagens como carregadas`);\n        setLoaded(true);\n        setError('Timeout no carregamento das imagens');\n      }\n    }, timeout);\n    return () => {\n      clearTimeout(fallbackTimer);\n      images.forEach(img => {\n        img.onload = null;\n        img.onerror = null;\n      });\n    };\n  }, [imageSources, timeout]);\n  return {\n    loaded,\n    error,\n    progress\n  };\n};\n_s(useImagePreloader, \"LRyuMD799SO8UIz2yyS1w4v4DJo=\");\nexport default useImagePreloader;", "map": {"version": 3, "names": ["useState", "useEffect", "useImagePreloader", "imageSources", "timeout", "_s", "loaded", "setLoaded", "error", "setError", "progress", "setProgress", "sources", "Array", "isArray", "loadedCount", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "length", "images", "map", "src", "img", "Image", "onload", "progressPercent", "onerror", "err", "fallbackTimer", "setTimeout", "clearTimeout", "for<PERSON>ach"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/hooks/useImagePreloader.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\n\r\n/**\r\n * Hook customizado para preload de imagens\r\n * @param {string|string[]} imageSources - URL da imagem ou array de URLs\r\n * @param {number} timeout - Timeout em ms (padrão: 5000)\r\n * @returns {object} { loaded, error, progress }\r\n */\r\nconst useImagePreloader = (imageSources, timeout = 5000) => {\r\n  const [loaded, setLoaded] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [progress, setProgress] = useState(0);\r\n\r\n  useEffect(() => {\r\n    if (!imageSources) {\r\n      setLoaded(true);\r\n      return;\r\n    }\r\n\r\n    const sources = Array.isArray(imageSources) ? imageSources : [imageSources];\r\n    let loadedCount = 0;\r\n    let hasError = false;\r\n\r\n    console.log(`📸 Iniciando preload de ${sources.length} imagem(ns)...`);\r\n\r\n    const images = sources.map((src) => {\r\n      const img = new Image();\r\n      \r\n      img.onload = () => {\r\n        loadedCount++;\r\n        const progressPercent = (loadedCount / sources.length) * 100;\r\n        setProgress(progressPercent);\r\n        \r\n        console.log(`✅ Imagem carregada: ${src} (${loadedCount}/${sources.length})`);\r\n        \r\n        if (loadedCount === sources.length && !hasError) {\r\n          console.log(`🎉 Todas as ${sources.length} imagens carregadas com sucesso!`);\r\n          setLoaded(true);\r\n        }\r\n      };\r\n\r\n      img.onerror = (err) => {\r\n        hasError = true;\r\n        console.error(`❌ Erro ao carregar imagem: ${src}`, err);\r\n        setError(`Erro ao carregar imagem: ${src}`);\r\n        \r\n        // Marcar como carregado mesmo com erro para não travar a interface\r\n        loadedCount++;\r\n        const progressPercent = (loadedCount / sources.length) * 100;\r\n        setProgress(progressPercent);\r\n        \r\n        if (loadedCount === sources.length) {\r\n          console.log(`⚠️ Preload finalizado com alguns erros`);\r\n          setLoaded(true);\r\n        }\r\n      };\r\n\r\n      img.src = src;\r\n      return img;\r\n    });\r\n\r\n    // Fallback de segurança com timeout\r\n    const fallbackTimer = setTimeout(() => {\r\n      if (!loaded) {\r\n        console.log(`⏰ Timeout de ${timeout}ms atingido - marcando imagens como carregadas`);\r\n        setLoaded(true);\r\n        setError('Timeout no carregamento das imagens');\r\n      }\r\n    }, timeout);\r\n\r\n    return () => {\r\n      clearTimeout(fallbackTimer);\r\n      images.forEach(img => {\r\n        img.onload = null;\r\n        img.onerror = null;\r\n      });\r\n    };\r\n  }, [imageSources, timeout]);\r\n\r\n  return { loaded, error, progress };\r\n};\r\n\r\nexport default useImagePreloader; "], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGA,CAACC,YAAY,EAAEC,OAAO,GAAG,IAAI,KAAK;EAAAC,EAAA;EAC1D,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACd,IAAI,CAACE,YAAY,EAAE;MACjBI,SAAS,CAAC,IAAI,CAAC;MACf;IACF;IAEA,MAAMK,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACX,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;IAC3E,IAAIY,WAAW,GAAG,CAAC;IACnB,IAAIC,QAAQ,GAAG,KAAK;IAEpBC,OAAO,CAACC,GAAG,CAAC,2BAA2BN,OAAO,CAACO,MAAM,gBAAgB,CAAC;IAEtE,MAAMC,MAAM,GAAGR,OAAO,CAACS,GAAG,CAAEC,GAAG,IAAK;MAClC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MAEvBD,GAAG,CAACE,MAAM,GAAG,MAAM;QACjBV,WAAW,EAAE;QACb,MAAMW,eAAe,GAAIX,WAAW,GAAGH,OAAO,CAACO,MAAM,GAAI,GAAG;QAC5DR,WAAW,CAACe,eAAe,CAAC;QAE5BT,OAAO,CAACC,GAAG,CAAC,uBAAuBI,GAAG,KAAKP,WAAW,IAAIH,OAAO,CAACO,MAAM,GAAG,CAAC;QAE5E,IAAIJ,WAAW,KAAKH,OAAO,CAACO,MAAM,IAAI,CAACH,QAAQ,EAAE;UAC/CC,OAAO,CAACC,GAAG,CAAC,eAAeN,OAAO,CAACO,MAAM,kCAAkC,CAAC;UAC5EZ,SAAS,CAAC,IAAI,CAAC;QACjB;MACF,CAAC;MAEDgB,GAAG,CAACI,OAAO,GAAIC,GAAG,IAAK;QACrBZ,QAAQ,GAAG,IAAI;QACfC,OAAO,CAACT,KAAK,CAAC,8BAA8Bc,GAAG,EAAE,EAAEM,GAAG,CAAC;QACvDnB,QAAQ,CAAC,4BAA4Ba,GAAG,EAAE,CAAC;;QAE3C;QACAP,WAAW,EAAE;QACb,MAAMW,eAAe,GAAIX,WAAW,GAAGH,OAAO,CAACO,MAAM,GAAI,GAAG;QAC5DR,WAAW,CAACe,eAAe,CAAC;QAE5B,IAAIX,WAAW,KAAKH,OAAO,CAACO,MAAM,EAAE;UAClCF,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrDX,SAAS,CAAC,IAAI,CAAC;QACjB;MACF,CAAC;MAEDgB,GAAG,CAACD,GAAG,GAAGA,GAAG;MACb,OAAOC,GAAG;IACZ,CAAC,CAAC;;IAEF;IACA,MAAMM,aAAa,GAAGC,UAAU,CAAC,MAAM;MACrC,IAAI,CAACxB,MAAM,EAAE;QACXW,OAAO,CAACC,GAAG,CAAC,gBAAgBd,OAAO,gDAAgD,CAAC;QACpFG,SAAS,CAAC,IAAI,CAAC;QACfE,QAAQ,CAAC,qCAAqC,CAAC;MACjD;IACF,CAAC,EAAEL,OAAO,CAAC;IAEX,OAAO,MAAM;MACX2B,YAAY,CAACF,aAAa,CAAC;MAC3BT,MAAM,CAACY,OAAO,CAACT,GAAG,IAAI;QACpBA,GAAG,CAACE,MAAM,GAAG,IAAI;QACjBF,GAAG,CAACI,OAAO,GAAG,IAAI;MACpB,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACxB,YAAY,EAAEC,OAAO,CAAC,CAAC;EAE3B,OAAO;IAAEE,MAAM;IAAEE,KAAK;IAAEE;EAAS,CAAC;AACpC,CAAC;AAACL,EAAA,CAxEIH,iBAAiB;AA0EvB,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}