const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AssinaturaSchema = new Schema({
  empresa: {
    type: Schema.Types.ObjectId,
    ref: 'Empresa',
    required: true,
    unique: true // Garante que uma empresa tenha apenas uma assinatura
  },
  assinatura_obj: {
    type: Object,
    default: {}
  }
});

const Assinatura = mongoose.model('Assinatura', AssinaturaSchema)

module.exports = Assinatura