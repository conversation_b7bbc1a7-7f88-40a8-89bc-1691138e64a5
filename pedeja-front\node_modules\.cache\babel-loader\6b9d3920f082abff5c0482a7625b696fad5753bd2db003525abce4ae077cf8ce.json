{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\ListEmpresas\\\\index.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport './style.css';\nimport styled from 'styled-components';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport { toast } from \"react-toastify\";\nimport 'react-toastify/dist/ReactToastify.css';\nimport Modal from \"react-modal\";\nimport { FaMoneyBillWave } from \"react-icons/fa\";\nimport moment from 'moment';\nimport { AuthContext } from \"../../contexts/auth\";\nimport { deleteInstanceAdmin, getEmpresasAdmin, createInstanceAdmin, getInstanceStatus, deleteEmpresaCompleta } from \"../../services/api\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport { useNavigate } from \"react-router-dom\";\nimport ModalEditEntregador from \"./modalEditEntregador\";\nimport ConfirmDialog from \"../../components/ConfirmDialog\";\nimport { BarContext } from \"../../components/LeftMenu\";\nimport { useFormik } from \"formik\";\nimport * as Yup from \"yup\";\nimport * as FiIcons from 'react-icons/fi';\nimport * as AiIcons from 'react-icons/ai';\nimport * as SlIcons from 'react-icons/sl';\nimport * as FaIcons from 'react-icons/fa';\nimport { date } from \"yup/lib/locale\";\nimport { useMemo } from \"react\";\nimport { Table } from \"antd\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InvoicesModal = ({\n  invoices,\n  isOpen,\n  onClose\n}) => {\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: isOpen,\n    onRequestClose: onClose,\n    contentLabel: \"Faturas Dispon\\xEDveis\",\n    ariaHideApp: false,\n    className: \"custom-invoices-modal\",\n    overlayClassName: \"custom-invoices-overlay\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Faturas Dispon\\xEDveis\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"custom-invoices-table\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Data de Vencimento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"A\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: invoices === null || invoices === void 0 ? void 0 : invoices.map((invoice, index) => {\n          var _invoice$discount;\n          return /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: moment(invoice === null || invoice === void 0 ? void 0 : invoice.dueDate).format(\"DD/MM/YYYY\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: statusMap[invoice === null || invoice === void 0 ? void 0 : invoice.status] || \"Status desconhecido\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 29\n            }, this), (invoice === null || invoice === void 0 ? void 0 : invoice.status) === \"OVERDUE\" || (invoice === null || invoice === void 0 ? void 0 : invoice.status) === \"PENDING\" ? /*#__PURE__*/_jsxDEV(\"td\", {\n              children: (((invoice === null || invoice === void 0 ? void 0 : invoice.value) || 0) - ((invoice === null || invoice === void 0 ? void 0 : (_invoice$discount = invoice.discount) === null || _invoice$discount === void 0 ? void 0 : _invoice$discount.value) || 0)).toLocaleString(\"pt-BR\", {\n                style: \"currency\",\n                currency: \"BRL\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"td\", {\n              children: ((invoice === null || invoice === void 0 ? void 0 : invoice.value) || 0).toLocaleString(\"pt-BR\", {\n                style: \"currency\",\n                currency: \"BRL\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: (invoice === null || invoice === void 0 ? void 0 : invoice.status) === \"OVERDUE\" || (invoice === null || invoice === void 0 ? void 0 : invoice.status) === \"PENDING\" ? /*#__PURE__*/_jsxDEV(\"a\", {\n                href: invoice === null || invoice === void 0 ? void 0 : invoice.invoiceUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"custom-invoices-pay-button\",\n                children: [/*#__PURE__*/_jsxDEV(FaMoneyBillWave, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 41\n                }, this), \" Pagar\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 37\n              }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"custom-invoices-pay-button custom-invoices-disabled-button\",\n                disabled: true,\n                children: [/*#__PURE__*/_jsxDEV(FaMoneyBillWave, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 41\n                }, this), \" Indispon\\xEDvel\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 29\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 25\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClose,\n      className: \"custom-invoices-close-button\",\n      children: \"Fechar\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 9\n  }, this);\n};\n_c = InvoicesModal;\nconst DeleteMasterModal = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  empresaData,\n  isLoading\n}) => {\n  _s();\n  const [usuario, setUsuario] = useState('');\n  const [senha, setSenha] = useState('');\n  const [confirmarNome, setConfirmarNome] = useState('');\n  const handleSubmit = e => {\n    var _empresaData$name;\n    e.preventDefault();\n    if (!usuario.trim() || !senha.trim()) {\n      toast.error(\"Usuário e senha são obrigatórios!\");\n      return;\n    }\n    if (confirmarNome.toLowerCase().trim() !== (empresaData === null || empresaData === void 0 ? void 0 : (_empresaData$name = empresaData.name) === null || _empresaData$name === void 0 ? void 0 : _empresaData$name.toLowerCase().trim())) {\n      toast.error(\"Nome da empresa não confere! Digite exatamente como está listado.\");\n      return;\n    }\n    onConfirm(usuario, senha);\n  };\n  const handleClose = () => {\n    setUsuario('');\n    setSenha('');\n    setConfirmarNome('');\n    onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: isOpen,\n    onRequestClose: handleClose,\n    contentLabel: \"Deletar Empresa Completamente\",\n    ariaHideApp: false,\n    className: \"delete-master-modal\",\n    overlayClassName: \"delete-master-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"delete-master-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"delete-master-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\u26A0\\uFE0F DELE\\xC7\\xC3O COMPLETA DA EMPRESA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"delete-master-warning\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"ATEN\\xC7\\xC3O:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 24\n          }, this), \" Esta a\\xE7\\xE3o ir\\xE1 deletar permanentemente:\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"\\u2717 A empresa \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: empresaData === null || empresaData === void 0 ? void 0 : empresaData.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2717 Todos os clientes vinculados\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2717 Todos os pedidos e hist\\xF3rico\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2717 Card\\xE1pio completo (categorias e itens)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2717 Usu\\xE1rios da empresa\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2717 Dados de caixa e movimenta\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2717 Mensagens e configura\\xE7\\xF5es WhatsApp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"irreversible-warning\",\n          children: [/*#__PURE__*/_jsxDEV(FiIcons.FiAlertCircle, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 25\n          }, this), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Esta a\\xE7\\xE3o \\xE9 IRREVERS\\xCDVEL!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 51\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"delete-master-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Usu\\xE1rio Master:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: usuario,\n            onChange: e => setUsuario(e.target.value),\n            className: \"form-control\",\n            placeholder: \"Digite o usu\\xE1rio master\",\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Senha Master:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            value: senha,\n            onChange: e => setSenha(e.target.value),\n            className: \"form-control\",\n            placeholder: \"Digite a senha master\",\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Para confirmar, digite o nome da empresa:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: confirmarNome,\n            onChange: e => setConfirmarNome(e.target.value),\n            className: \"form-control\",\n            placeholder: `Digite: ${empresaData === null || empresaData === void 0 ? void 0 : empresaData.name}`,\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [\"Digite exatamente: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: empresaData === null || empresaData === void 0 ? void 0 : empresaData.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 74\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-master-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleClose,\n            className: \"btn-cancel\",\n            disabled: isLoading,\n            children: [/*#__PURE__*/_jsxDEV(FiIcons.FiX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 29\n            }, this), \" Cancelar\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn-delete-master\",\n            disabled: isLoading || !usuario || !senha || !confirmarNome,\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(AiIcons.AiOutlineLoading3Quarters, {\n                className: \"spinning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 37\n              }, this), \"Deletando...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(FiIcons.FiTrash2, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 37\n              }, this), \" Deletar Permanentemente\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 9\n  }, this);\n};\n_s(DeleteMasterModal, \"XrAw2IC68bBtMQ1nfFZHBbZD/i8=\");\n_c2 = DeleteMasterModal;\nconst Teste = styled.div`\n    display: flex;\n    margin-left:  ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'}; \n    height:auto;\n    width:auto;\n    transition: 150ms;\n    background-color:rgb(247,247,247)!important;\n    overflow: initial;\n    z-Index: 9;\n\n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\n\n// Tooltip (Mini Modal)\n_c3 = Teste;\nconst Tooltip = styled.div`\n    position: absolute;\n    background-color: black;\n    color: white;\n    padding: 5px 8px;\n    border-radius: 5px;\n    font-size: 12px;\n    white-space: nowrap;\n    opacity: 0;\n    transition: opacity 0.2s ease-in-out;\n    pointer-events: none;\n    transform: translate(-50%, -5px);\n    z-index:12;\n`;\n\n// Wrapper para os botões com tooltip\n_c4 = Tooltip;\nconst ButtonWrapper = styled.div`\n    position: relative;\n    display: inline-block;\n    \n    &:hover ${Tooltip} {\n        opacity: 1;\n    }\n`;\n_c5 = ButtonWrapper;\nconst columns = [{\n  title: 'Nome',\n  dataIndex: 'name',\n  key: 'name'\n}, {\n  title: 'E-mail',\n  dataIndex: 'email',\n  key: 'email'\n}, {\n  title: 'CPF/CNPJ',\n  dataIndex: 'cnpj',\n  key: 'cnpj'\n}, {\n  title: 'ID da Instância',\n  dataIndex: 'instanceId',\n  key: 'instanceId'\n}, {\n  title: 'Status WhatsApp',\n  dataIndex: 'instanceStatus',\n  key: 'instanceStatus'\n}, {\n  title: 'Data Criação',\n  dataIndex: 'createdAt',\n  key: 'createdAt'\n}, {\n  title: 'Ver faturas',\n  dataIndex: 'verFaturas',\n  key: 'verFaturas'\n}, {\n  title: 'Ações',\n  dataIndex: 'actions',\n  key: 'actions'\n}];\nconst statusMap = {\n  PENDING: \"Pendente\",\n  PAID: \"Paga\",\n  CANCELED: \"Cancelada\",\n  IN_ANALYSIS: \"Em Análise\",\n  DRAFT: \"Rascunho\",\n  PARTIALLY_PAID: \"Parcialmente Paga\",\n  REFUNDED: \"Reembolsada\",\n  EXPIRED: \"Expirada\",\n  IN_PROTEST: \"Em Protesto\",\n  CHARGEBACK: \"Contestada\",\n  EXTERNALLY_PAID: \"Paga Externamente\",\n  RECEIVED: \"Paga\",\n  CONFIRMED: \"Confirmada\",\n  OVERDUE: \"Atrasada\",\n  RECEIVED_IN_CASH: \"Recebida em Dinheiro\",\n  REFUND_REQUESTED: \"Reembolso Solicitado\",\n  REFUND_IN_PROGRESS: \"Reembolso em Andamento\",\n  CHARGEBACK_REQUESTED: \"Contestação Solicitada\",\n  CHARGEBACK_DISPUTE: \"Disputa de Contestação\",\n  AWAITING_CHARGEBACK_REVERSAL: \"Aguardando Reversão de Contestação\",\n  DUNNING_REQUESTED: \"Cobrança Judicial Solicitada\",\n  DUNNING_RECEIVED: \"Cobrança Judicial Recebida\",\n  AWAITING_RISK_ANALYSIS: \"Aguardando Análise de Risco\"\n};\nconst ListEmpresas = ({\n  list = []\n}) => {\n  _s2();\n  const {\n    user\n  } = useContext(AuthContext);\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const objIdEmpresa = empresaParse._id;\n  const vinculo_empresa = empresaParse.cnpj;\n  const navigate = useNavigate();\n  const {\n    sidebar,\n    setSidebar\n  } = useContext(SidebarContext);\n  ;\n  //console.log(\"LISTUSERS\",sidebar);\n\n  const [order, setOrder] = useState(1);\n  const [columnOrder, setColumnOrder] = useState('title');\n  const [allEmpresas, setAllEmpresas] = useState([]); // Armazena todas as empresas\n  const [empresas, setEmpresas] = useState([]);\n  const [filter, setFilter] = useState('');\n  const [_idEntregadorEdit, set_idEntregadorEdit] = useState('');\n  const [nomeEntregadorEdit, setNomeEntregadorEdit] = useState('');\n  const [veiculoEdit, setVeiculoEdit] = useState('');\n  const [telefoneEdit, setTelefoneEdit] = useState('');\n  const [placaEdit, setPlacaEdit] = useState('');\n  const [result, setResult] = useState(false);\n  const [showEditEntregador, setEditEntregador] = useState(true);\n  const [loading, setLoading] = useState(true);\n  const [refresh, setRefresh] = useState(false);\n  const [page, setPage] = useState(1);\n  const [limit] = useState(10);\n  const [total, setTotal] = useState(0); // Armazena o total de empresas disponíveis\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [invoices, setInvoices] = useState([]);\n  const [deleteMasterModalOpen, setDeleteMasterModalOpen] = useState(false);\n  const [empresaToDelete, setEmpresaToDelete] = useState(null);\n  const [deleteMasterLoading, setDeleteMasterLoading] = useState(false);\n  const INITIAL_DATA = {\n    value: \"\",\n    label: 'Selecione uma empresa'\n  };\n  const [selectData, setselectData] = useState(INITIAL_DATA);\n\n  /*useEffect(() => {\r\n      (async () => {\r\n            const response = await getEmpresasAdmin(user._id);\r\n          //console.log(\"TESTEEE:::\",user._id)\r\n          //const teste = [response.data]\r\n          setEmpresas(response.data.empresas);\r\n          console.log(response.data)\r\n          setLoading(false);\r\n          setRefresh(false);\r\n        })();\r\n  }, [refresh]);*/\n  /*useEffect(() => {\r\n      (async () => {\r\n          setLoading(true);\r\n          const response = await getEmpresasAdmin(user._id, page, limit);\r\n          console.log(response.data)\r\n          setEmpresas(response.data.empresas);\r\n          setTotal(response.data.total); // Atualiza o total com base no backend\r\n          setLoading(false);\r\n          setRefresh(false);\r\n      })();\r\n  }, [refresh, page]); // Dispara o efeito ao mudar de página*/\n  useEffect(() => {\n    (async () => {\n      setLoading(true);\n\n      // Buscar todas as empresas apenas uma vez (sem paginação)\n      if (allEmpresas.length === 0 || refresh) {\n        const response = await getEmpresasAdmin(user._id, 1, 1000000); // Pegamos todas as empresas\n        setAllEmpresas(response.data.empresas);\n      }\n\n      // Agora buscamos apenas as empresas da página atual\n      const response = await getEmpresasAdmin(user._id, page, limit);\n      setEmpresas(response.data.empresas);\n      setTotal(response.data.total);\n      setLoading(false);\n      setRefresh(false);\n    })();\n  }, [refresh, page]);\n  const handleEdit = async idToEdit => {\n    //setEditEntregador(!showEditEntregador);\n    console.log(JSON.stringify(empresas));\n    const entregadorToEdit = empresas.find(entregador => entregador._id === idToEdit);\n    if (entregadorToEdit) {\n      set_idEntregadorEdit(entregadorToEdit._id);\n      setNomeEntregadorEdit(entregadorToEdit.name);\n      setVeiculoEdit(entregadorToEdit.veiculo);\n      console.log(\"entregadorToEdit.telefone>\", entregadorToEdit.telefone);\n      setTelefoneEdit(entregadorToEdit.telefone);\n      setPlacaEdit(entregadorToEdit.placa);\n      setEditEntregador(!showEditEntregador); // Supondo que isso abra o modal\n    }\n    /*const response = await getEntregadores(idToEdit);\r\n    //console.log(\"Infos do Edit:\",response.data.user)\r\n    //console.log(idToEdit,\"----\");\r\n    if(showEditEntregador){\r\n        set_idEntregadorEdit(idToEdit)            \r\n        setNomeEntregadorEdit(response.data.user.name)\r\n        setVeiculoEdit(response.data.user.email)            \r\n        setRoleEdit(response.data.user.role)           \r\n      }*/\n  };\n  const [confirmOpen, setConfirmOpen] = useState(false);\n  const [shouldDelete, setShouldDelete] = useState(false);\n  const [idToDelete, setIdToDelete] = useState('');\n  const handleDelete = idRecebido => {\n    // Abre Componente de Confirmação do delete\n    setConfirmOpen(true);\n    setIdToDelete(idRecebido);\n    if (idRecebido) {\n      //console.log(\"ID RECEBIDO:\",idRecebido)\n      setShouldDelete(true);\n    }\n  };\n  const deleteReg = async () => {\n    //console.log(\"O ID CLICADO FOI :::\",idToDelete);\n    //console.log(\"ID do Usuário que irá efetuar a exclusão\", user._id);    \n    if (shouldDelete) {\n      setLoading(true);\n      deleteInstanceAdmin(user._id, idToDelete).then(cast => {\n        //console.log(\"RESULT DELETE:\",cast);\n        if (cast.status === 201) {\n          toast(cast.data.msg, {\n            autoClose: 5000,\n            type: \"success\"\n          });\n          setRefresh(true);\n        } else {\n          toast(cast.data.msg, {\n            autoClose: 5000,\n            type: \"error\"\n          });\n        }\n      }).catch(err => {\n        //console.log(\"ERROR:\",err);\n        toast(err.response.data.msg, {\n          autoClose: 5000,\n          type: \"error\"\n        });\n      });\n    }\n  };\n  const handleCreateInstance = async empresaId => {\n    try {\n      setLoading(true);\n      const response = await createInstanceAdmin(user._id, empresaId);\n      if (response.status === 201) {\n        toast.success(response.data.msg); // Exibe toast apenas se sucesso\n        setRefresh(true); // Atualiza a lista\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Erro ao criar instância:\", error);\n      const msg = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.msg) || \"Erro desconhecido\";\n      toast.error(msg);\n    } finally {\n      setLoading(false); // Garante que o estado de carregamento seja atualizado no final\n    }\n  };\n  const handleOrder = fieldName => {\n    setOrder(-order);\n    setColumnOrder(fieldName);\n    //console.log(\"order:\",order);\n    //console.log(\"fieldName\",fieldName)\n  };\n  const handleRefresh = () => {\n    //window.location.reload(false);\n    setLoading(true);\n    setRefresh(true);\n  };\n  const handleCadastro = () => {\n    //setSidebar(!sidebarTeste);\n    navigate(\"/cadastro-entregador\");\n  };\n  const handleFilter = e => {\n    setFilter(e.target.value);\n    setResult(!result);\n  };\n  const handleOpenFaturas = faturas => {\n    setIsModalOpen(true);\n    setInvoices(faturas);\n  };\n  const handleDeleteMaster = empresa => {\n    setEmpresaToDelete(empresa);\n    setDeleteMasterModalOpen(true);\n  };\n  const handleDeleteMasterConfirm = async (usuario, senha) => {\n    try {\n      setDeleteMasterLoading(true);\n      const response = await deleteEmpresaCompleta(empresaToDelete._id, usuario, senha);\n      if (response.status === 200) {\n        toast.success(`✅ ${response.data.msg}`);\n        setDeleteMasterModalOpen(false);\n        setEmpresaToDelete(null);\n        setRefresh(true); // Atualiza a lista\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error(\"Erro ao deletar empresa completa:\", error);\n      const errorMsg = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.msg) || \"Erro desconhecido ao deletar empresa\";\n      toast.error(`❌ ${errorMsg}`);\n    } finally {\n      setDeleteMasterLoading(false);\n    }\n  };\n  const handleDeleteMasterClose = () => {\n    setDeleteMasterModalOpen(false);\n    setEmpresaToDelete(null);\n  };\n\n  /*\r\n  var arrayEmpresas = [];\r\n  arrayEmpresas = empresas.sort((a, b) => {\r\n      return a[columnOrder] < b[columnOrder] ? -order : order;\r\n  })\r\n    if (filter) {\r\n      const exp = eval(`/${filter.replace(/[^\\d\\w]+/, '.*')}/i`)\r\n        list = empresas.filter(item => exp.test(item.name))\r\n  }\r\n    const tableData = useMemo(() => {\r\n      if (filter) return list.map(({ _id, id_empresa, name, cnpj, whatsapp, instanceStatus, faturas }) => {\r\n          return {\r\n              key: _id,\r\n              code: id_empresa,\r\n              name: name,\r\n              cnpj: cnpj,\r\n              instanceId: whatsapp.id,\r\n              instanceStatus: (\r\n                  <span style={{\r\n                      color: instanceStatus.code === 400 ? \"red\"\r\n                          : instanceStatus.connected ? \"green\"\r\n                              : \"gray\",\r\n                      fontWeight: \"bold\"\r\n                  }}>\r\n                      {instanceStatus.code === 400 ? \"Desconectado\"\r\n                          : instanceStatus.connected ? \"Conectado\"\r\n                              : \"Desconhecido\"}\r\n                  </span>\r\n              ),\r\n              verFaturas: (\r\n                  <>\r\n                  {faturas.length > 0 ?\r\n                      <a className=\"openFaturasText\" onClick={() => handleOpenFaturas(faturas)}>Ver faturas</a>\r\n                      :\r\n                      <span>Não gerada</span>\r\n                  }\r\n                  </>\r\n              ),\r\n              actions: (\r\n                  <>\r\n                      <span className=\"btn btn-sm btn-danger\" style={{ marginLeft: \"5px\" }}><FiIcons.FiTrash2 style={{ color: \"white\" }} /></span>\r\n                  </>\r\n              )\r\n          }\r\n      });\r\n        return arrayEmpresas.map(({ _id, id_empresa, name, cnpj, whatsapp, instanceStatus, faturas }) => {\r\n          return {\r\n              key: _id,\r\n              code: id_empresa,\r\n              name: name,\r\n              cnpj: cnpj,\r\n              instanceId: whatsapp.id,\r\n              instanceStatus: (\r\n                  <span style={{\r\n                      color: instanceStatus.code === 400 ? \"red\"\r\n                          : instanceStatus.connected ? \"green\"\r\n                              : \"gray\",\r\n                      fontWeight: \"bold\"\r\n                  }}>\r\n                      {instanceStatus.code === 400 ? \"Desconectado\"\r\n                          : instanceStatus.connected ? \"Conectado\"\r\n                              : \"Desconhecido\"}\r\n                  </span>\r\n              ),\r\n              verFaturas: (\r\n                  <>\r\n                  {faturas.length > 0 ?\r\n                      <a className=\"openFaturasText\" onClick={() => handleOpenFaturas(faturas)}>Ver faturas</a>\r\n                      :\r\n                      <span>Não gerada</span>\r\n                  }\r\n                  </>\r\n              ),\r\n              actions: (\r\n                  <>\r\n                      <span className=\"btn btn-sm btn-danger\" style={{ marginLeft: \"5px\" }}><FiIcons.FiTrash2 style={{ color: \"white\" }} /></span>\r\n                  </>\r\n              )\r\n          }\r\n      })\r\n  }, [filter, arrayEmpresas, list]);\r\n  */\n\n  // Função para remover caracteres não numéricos\n  const removeMask = str => str.replace(/\\D/g, '');\n  // Função para filtrar empresas\n  const filteredEmpresas = useMemo(() => {\n    if (!filter) return allEmpresas; // Se não houver filtro, retorna todas\n\n    const cleanFilter = removeMask(filter); // Remove a máscara do filtro digitado\n    const exp = new RegExp(filter.replace(/[^\\d\\w]+/, '.*'), 'i');\n    return allEmpresas.filter(item => {\n      var _item$whatsapp, _item$whatsapp2;\n      return exp.test(item.name) ||\n      // Filtra pelo nome\n      exp.test(item.email) ||\n      // Filtra pelo email\n      exp.test(item.cnpj) ||\n      // Filtra pelo CNPJ com máscara\n      exp.test(removeMask(item.cnpj)) ||\n      // Filtra pelo CNPJ sem máscara\n      ((_item$whatsapp = item.whatsapp) === null || _item$whatsapp === void 0 ? void 0 : _item$whatsapp.id) && exp.test((_item$whatsapp2 = item.whatsapp) === null || _item$whatsapp2 === void 0 ? void 0 : _item$whatsapp2.id);\n    } // Filtra pelo ID do WhatsApp, se existir\n    );\n  }, [filter, allEmpresas]);\n\n  // Aplicar a paginação na lista filtrada\n  const paginatedEmpresas = useMemo(() => {\n    return filteredEmpresas.slice((page - 1) * limit, page * limit);\n  }, [filteredEmpresas, page, limit]);\n  const tableData = useMemo(() => {\n    return paginatedEmpresas.map(({\n      _id,\n      id_empresa,\n      name,\n      email,\n      cnpj,\n      whatsapp,\n      instanceStatus,\n      createdAt,\n      faturas\n    }) => ({\n      key: _id,\n      code: id_empresa,\n      name: name,\n      email: email,\n      cnpj: cnpj,\n      instanceId: (whatsapp === null || whatsapp === void 0 ? void 0 : whatsapp.id) || \"N/A\",\n      // 🔹 Evita erro quando whatsapp é undefined\n      instanceStatus: /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: (instanceStatus === null || instanceStatus === void 0 ? void 0 : instanceStatus.code) === 400 ? \"red\" : instanceStatus !== null && instanceStatus !== void 0 && instanceStatus.connected ? \"green\" : \"gray\",\n          fontWeight: \"bold\"\n        },\n        children: (instanceStatus === null || instanceStatus === void 0 ? void 0 : instanceStatus.code) === 400 ? \"Desconectado\" : instanceStatus !== null && instanceStatus !== void 0 && instanceStatus.connected ? \"Conectado\" : \"Desconhecido\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 17\n      }, this),\n      createdAt: moment(createdAt).format(\"DD/MM/YYYY\"),\n      verFaturas: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: faturas.length > 0 ? /*#__PURE__*/_jsxDEV(\"a\", {\n          className: \"openFaturasText\",\n          onClick: () => handleOpenFaturas(faturas),\n          children: \"Ver faturas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"N\\xE3o gerada\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 25\n        }, this)\n      }, void 0, false),\n      actions: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(ButtonWrapper, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"btn btn-sm\",\n            onClick: whatsapp !== null && whatsapp !== void 0 && whatsapp.id ? () => handleDelete(_id) : undefined,\n            style: {\n              marginLeft: \"5px\",\n              backgroundColor: whatsapp !== null && whatsapp !== void 0 && whatsapp.id ? \"red\" : \"gray\",\n              cursor: whatsapp !== null && whatsapp !== void 0 && whatsapp.id ? \"pointer\" : \"not-allowed\",\n              position: \"relative\"\n            },\n            children: /*#__PURE__*/_jsxDEV(FiIcons.FiTrash2, {\n              style: {\n                color: \"white\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            children: \"Deletar Inst\\xE2ncia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ButtonWrapper, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"btn btn-sm\",\n            onClick: !(whatsapp !== null && whatsapp !== void 0 && whatsapp.id) ? () => handleCreateInstance(_id) : undefined,\n            style: {\n              marginLeft: \"5px\",\n              backgroundColor: !(whatsapp !== null && whatsapp !== void 0 && whatsapp.id) ? \"rgb(66, 129, 255)\" : \"gray\",\n              cursor: !(whatsapp !== null && whatsapp !== void 0 && whatsapp.id) ? \"pointer\" : \"not-allowed\",\n              position: \"relative\"\n            },\n            children: /*#__PURE__*/_jsxDEV(FaIcons.FaPlus, {\n              style: {\n                color: \"white\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            children: \"Criar Inst\\xE2ncia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ButtonWrapper, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"btn btn-sm btn-delete-master\",\n            onClick: () => handleDeleteMaster({\n              _id,\n              name,\n              email,\n              cnpj\n            }),\n            style: {\n              marginLeft: \"5px\",\n              position: \"relative\"\n            },\n            children: /*#__PURE__*/_jsxDEV(FaIcons.FaBomb, {\n              style: {\n                color: \"white\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            children: \"Delete Master Completo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true)\n    }));\n  }, [paginatedEmpresas]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: [/*#__PURE__*/_jsxDEV(ConfirmDialog, {\n        title: \"Deletar Inst\\xE2ncia?\",\n        open: confirmOpen,\n        setOpen: setConfirmOpen,\n        onConfirm: deleteReg,\n        children: \"Tem certeza que deseja deletar a inst\\xE2ncia/whatsapp da empresa?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(ModalEditEntregador, {\n        setEditEntregador: setEditEntregador,\n        showEditEntregador: showEditEntregador,\n        setRefresh: setRefresh,\n        selectData: selectData,\n        setselectData: setselectData,\n        _idEntregadorEdit: _idEntregadorEdit,\n        nomeEntregadorEdit: nomeEntregadorEdit,\n        veiculoEdit: veiculoEdit,\n        telefoneEdit: telefoneEdit,\n        placaEdit: placaEdit,\n        userID: user._id,\n        id_empresa: objIdEmpresa\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(InvoicesModal, {\n        invoices: invoices,\n        isOpen: isModalOpen,\n        onClose: () => setIsModalOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DeleteMasterModal, {\n        isOpen: deleteMasterModalOpen,\n        onClose: handleDeleteMasterClose,\n        onConfirm: handleDeleteMasterConfirm,\n        empresaData: empresaToDelete,\n        isLoading: deleteMasterLoading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 792,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Teste, {\n        sidebar: sidebar,\n        children: [loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: /*#__PURE__*/_jsxDEV(AiIcons.AiOutlineLoading3Quarters, {\n            style: {\n              fontSize: \"100px\",\n              color: \"rgb(180,180,180)\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 32\n        }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-100 p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"list-header-empresas\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"title\",\n              children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                children: \"Empresas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-column flex-md-row\",\n            style: {\n              display: \"flex\",\n              justifyContent: \"space-between\" /*, height:\"80px\"*/\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-box-list\",\n              style: {\n                width: \"100%\",\n                maxWidth: \"400px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"input-field\",\n                placeholder: \"Pesquisar\",\n                onChange: handleFilter\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"icon\",\n                children: /*#__PURE__*/_jsxDEV(FiIcons.FiSearch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 53\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"div-buttons flex-column flex-md-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"refresh-button\",\n                style: {\n                  textAlign: \"start\",\n                  marginBottom: \"10px\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"refresh-button\",\n                  onClick: handleRefresh,\n                  children: [/*#__PURE__*/_jsxDEV(SlIcons.SlRefresh, {\n                    style: {\n                      color: \"#4281FF\",\n                      marginRight: \"5px\",\n                      fontSize: \"18px\",\n                      marginBottom: \"2px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                    children: \"Atualizar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 150\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 818,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            columns: columns,\n            dataSource: tableData,\n            pagination: {\n              current: page,\n              pageSize: limit,\n              total: filteredEmpresas.length,\n              // Total agora é baseado na lista filtrada\n              onChange: newPage => setPage(newPage),\n              showSizeChanger: false\n            },\n            rowClassName: \"row-table\",\n            rowHoverable: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"Total de empresas ativas: \", total]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 800,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 762,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s2(ListEmpresas, \"ntPuQVclMkrwq3MHI7ujaa5tpQo=\", false, function () {\n  return [useNavigate];\n});\n_c6 = ListEmpresas;\nexport default ListEmpresas;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"InvoicesModal\");\n$RefreshReg$(_c2, \"DeleteMasterModal\");\n$RefreshReg$(_c3, \"Teste\");\n$RefreshReg$(_c4, \"Tooltip\");\n$RefreshReg$(_c5, \"ButtonWrapper\");\n$RefreshReg$(_c6, \"ListEmpresas\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "styled", "SidebarContext", "toast", "Modal", "FaMoneyBillWave", "moment", "AuthContext", "deleteInstanceAdmin", "getEmpresasAdmin", "createInstanceAdmin", "getInstanceStatus", "deleteEmpresaCompleta", "PermissionGate", "useNavigate", "ModalEditEntregador", "ConfirmDialog", "BarContext", "useFormik", "<PERSON><PERSON>", "FiIcons", "AiIcons", "SlIcons", "FaIcons", "date", "useMemo", "Table", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InvoicesModal", "invoices", "isOpen", "onClose", "onRequestClose", "contentLabel", "ariaHideApp", "className", "overlayClassName", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "invoice", "index", "_invoice$discount", "dueDate", "format", "statusMap", "status", "value", "discount", "toLocaleString", "style", "currency", "href", "invoiceUrl", "target", "rel", "disabled", "onClick", "_c", "DeleteMasterModal", "onConfirm", "empresaData", "isLoading", "_s", "usuario", "setUsuario", "<PERSON><PERSON>a", "setSenha", "confirmarNome", "setConfirmarNome", "handleSubmit", "e", "_empresaData$name", "preventDefault", "trim", "error", "toLowerCase", "name", "handleClose", "FiAlertCircle", "onSubmit", "type", "onChange", "placeholder", "FiX", "AiOutlineLoading3Quarters", "FiTrash2", "_c2", "<PERSON>e", "div", "sidebar", "_c3", "<PERSON><PERSON><PERSON>", "_c4", "ButtonWrapper", "_c5", "columns", "title", "dataIndex", "key", "PENDING", "PAID", "CANCELED", "IN_ANALYSIS", "DRAFT", "PARTIALLY_PAID", "REFUNDED", "EXPIRED", "IN_PROTEST", "CHARGEBACK", "EXTERNALLY_PAID", "RECEIVED", "CONFIRMED", "OVERDUE", "RECEIVED_IN_CASH", "REFUND_REQUESTED", "REFUND_IN_PROGRESS", "CHARGEBACK_REQUESTED", "CHARGEBACK_DISPUTE", "AWAITING_CHARGEBACK_REVERSAL", "DUNNING_REQUESTED", "DUNNING_RECEIVED", "AWAITING_RISK_ANALYSIS", "ListEmpresas", "list", "_s2", "user", "empresa", "localStorage", "getItem", "empresaParse", "JSON", "parse", "objIdEmpresa", "_id", "vinculo_empresa", "cnpj", "navigate", "setSidebar", "order", "setOrder", "columnOrder", "setColumnOrder", "allEmpresas", "setAllEmpresas", "empresas", "setEmpresas", "filter", "setFilter", "_idEntregadorEdit", "set_idEntregadorEdit", "nomeEntregadorEdit", "setNomeEntregadorEdit", "veiculoEdit", "setVeiculoEdit", "telefoneEdit", "setTelefoneEdit", "placaEdit", "setPlacaEdit", "result", "setResult", "showEditEntregador", "setEditEntregador", "loading", "setLoading", "refresh", "setRefresh", "page", "setPage", "limit", "total", "setTotal", "isModalOpen", "setIsModalOpen", "setInvoices", "deleteMasterModalOpen", "setDeleteMasterModalOpen", "empresaToDelete", "setEmpresaToDelete", "deleteMasterLoading", "setDeleteMasterLoading", "INITIAL_DATA", "label", "selectData", "setselectData", "length", "response", "data", "handleEdit", "idToEdit", "console", "log", "stringify", "entregadorToEdit", "find", "entregador", "veiculo", "telefone", "placa", "confirmOpen", "setConfirmOpen", "shouldDelete", "setShouldDelete", "idToDelete", "setIdToDelete", "handleDelete", "idRecebido", "deleteReg", "then", "cast", "msg", "autoClose", "catch", "err", "handleCreateInstance", "empresaId", "success", "_error$response", "_error$response$data", "handleOrder", "fieldName", "handleRefresh", "handleCadastro", "handleFilter", "handleOpenFaturas", "faturas", "handleDeleteMaster", "handleDeleteMasterConfirm", "_error$response2", "_error$response2$data", "errorMsg", "handleDeleteMasterClose", "removeMask", "str", "replace", "filteredEmpresas", "cleanFilter", "exp", "RegExp", "item", "_item$whatsapp", "_item$whatsapp2", "test", "email", "whatsapp", "id", "paginatedEmpresas", "slice", "tableData", "id_empresa", "instanceStatus", "createdAt", "code", "instanceId", "color", "connected", "fontWeight", "verFaturas", "actions", "undefined", "marginLeft", "backgroundColor", "cursor", "position", "FaPlus", "FaBomb", "permissions", "open", "<PERSON><PERSON><PERSON>", "userID", "fontSize", "display", "justifyContent", "width", "max<PERSON><PERSON><PERSON>", "FiSearch", "textAlign", "marginBottom", "SlRefresh", "marginRight", "dataSource", "pagination", "current", "pageSize", "newPage", "showSizeChanger", "rowClassName", "rowHoverable", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/ListEmpresas/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport './style.css'\r\nimport styled from 'styled-components';\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport { toast } from \"react-toastify\";\r\nimport 'react-toastify/dist/ReactToastify.css';\r\nimport Modal from \"react-modal\";\r\nimport { FaMoneyBillWave } from \"react-icons/fa\";\r\n\r\nimport moment from 'moment'\r\n\r\nimport { AuthContext } from \"../../contexts/auth\";\r\n\r\nimport { deleteInstanceAdmin, getEmpresasAdmin, createInstanceAdmin, getInstanceStatus,  deleteEmpresaCompleta } from \"../../services/api\";\r\n\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\n\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport ModalEditEntregador from \"./modalEditEntregador\";\r\nimport ConfirmDialog from \"../../components/ConfirmDialog\";\r\n\r\nimport { BarContext } from \"../../components/LeftMenu\";\r\n\r\nimport { useFormik } from \"formik\";\r\nimport * as Yup from \"yup\";\r\n\r\nimport * as FiIcons from 'react-icons/fi'\r\nimport * as AiIcons from 'react-icons/ai'\r\nimport * as SlIcons from 'react-icons/sl'\r\nimport * as FaIcons from 'react-icons/fa'\r\nimport { date } from \"yup/lib/locale\";\r\nimport { useMemo } from \"react\";\r\nimport { Table } from \"antd\";\r\n\r\nconst InvoicesModal = ({ invoices, isOpen, onClose }) => {\r\n    return (\r\n        <Modal\r\n            isOpen={isOpen}\r\n            onRequestClose={onClose}\r\n            contentLabel=\"Faturas Disponíveis\"\r\n            ariaHideApp={false}\r\n            className=\"custom-invoices-modal\"\r\n            overlayClassName=\"custom-invoices-overlay\"\r\n        >\r\n            <h2>Faturas Disponíveis</h2>\r\n            <table className=\"custom-invoices-table\">\r\n                <thead>\r\n                    <tr>\r\n                        <th>Data de Vencimento</th>\r\n                        <th>Status</th>\r\n                        <th>Total</th>\r\n                        <th>Ação</th>\r\n                    </tr>\r\n                </thead>\r\n                <tbody>\r\n                    {invoices?.map((invoice, index) => (\r\n                        <tr key={index}>\r\n                            <td>{moment(invoice?.dueDate).format(\"DD/MM/YYYY\")}</td>\r\n                            <td>{statusMap[invoice?.status] || \"Status desconhecido\"}</td>\r\n                            {(invoice?.status === \"OVERDUE\" || invoice?.status === \"PENDING\") ?\r\n                                <td>\r\n                                    {((invoice?.value || 0) - (invoice?.discount?.value || 0))\r\n                                        .toLocaleString(\"pt-BR\", { style: \"currency\", currency: \"BRL\" })}\r\n                                </td>\r\n                                :\r\n                                <td>\r\n                                    {((invoice?.value || 0))\r\n                                        .toLocaleString(\"pt-BR\", { style: \"currency\", currency: \"BRL\" })}\r\n                                </td>\r\n                            }\r\n                            <td>\r\n                                {(invoice?.status === \"OVERDUE\" || invoice?.status === \"PENDING\") ? (\r\n                                    <a\r\n                                        href={invoice?.invoiceUrl}\r\n                                        target=\"_blank\"\r\n                                        rel=\"noopener noreferrer\"\r\n                                        className=\"custom-invoices-pay-button\"\r\n                                    >\r\n                                        <FaMoneyBillWave /> Pagar\r\n                                    </a>\r\n                                ) : (\r\n                                    <button className=\"custom-invoices-pay-button custom-invoices-disabled-button\" disabled>\r\n                                        <FaMoneyBillWave /> Indisponível\r\n                                    </button>\r\n                                )}\r\n                            </td>\r\n                        </tr>\r\n                    ))}\r\n                </tbody>\r\n            </table>\r\n            <button onClick={onClose} className=\"custom-invoices-close-button\">Fechar</button>\r\n        </Modal>\r\n    );\r\n};\r\n\r\nconst DeleteMasterModal = ({ isOpen, onClose, onConfirm, empresaData, isLoading }) => {\r\n    const [usuario, setUsuario] = useState('');\r\n    const [senha, setSenha] = useState('');\r\n    const [confirmarNome, setConfirmarNome] = useState('');\r\n\r\n    const handleSubmit = (e) => {\r\n        e.preventDefault();\r\n        \r\n        if (!usuario.trim() || !senha.trim()) {\r\n            toast.error(\"Usuário e senha são obrigatórios!\");\r\n            return;\r\n        }\r\n\r\n        if (confirmarNome.toLowerCase().trim() !== empresaData?.name?.toLowerCase().trim()) {\r\n            toast.error(\"Nome da empresa não confere! Digite exatamente como está listado.\");\r\n            return;\r\n        }\r\n\r\n        onConfirm(usuario, senha);\r\n    };\r\n\r\n    const handleClose = () => {\r\n        setUsuario('');\r\n        setSenha('');\r\n        setConfirmarNome('');\r\n        onClose();\r\n    };\r\n\r\n    return (\r\n        <Modal\r\n            isOpen={isOpen}\r\n            onRequestClose={handleClose}\r\n            contentLabel=\"Deletar Empresa Completamente\"\r\n            ariaHideApp={false}\r\n            className=\"delete-master-modal\"\r\n            overlayClassName=\"delete-master-overlay\"\r\n        >\r\n            <div className=\"delete-master-content\">\r\n                <div className=\"delete-master-header\">\r\n                    <h2>⚠️ DELEÇÃO COMPLETA DA EMPRESA</h2>\r\n                </div>\r\n\r\n                <div className=\"delete-master-warning\">\r\n                    <p><strong>ATENÇÃO:</strong> Esta ação irá deletar permanentemente:</p>\r\n                    <ul>\r\n                        <li>✗ A empresa <strong>{empresaData?.name}</strong></li>\r\n                        <li>✗ Todos os clientes vinculados</li>\r\n                        <li>✗ Todos os pedidos e histórico</li>\r\n                        <li>✗ Cardápio completo (categorias e itens)</li>\r\n                        <li>✗ Usuários da empresa</li>\r\n                        <li>✗ Dados de caixa e movimentação</li>\r\n                        <li>✗ Mensagens e configurações WhatsApp</li>\r\n                    </ul>\r\n                    <p className=\"irreversible-warning\">\r\n                        <FiIcons.FiAlertCircle /> <strong>Esta ação é IRREVERSÍVEL!</strong>\r\n                    </p>\r\n                </div>\r\n\r\n                <form onSubmit={handleSubmit} className=\"delete-master-form\">\r\n                    <div className=\"form-group\">\r\n                        <label>Usuário Master:</label>\r\n                        <input\r\n                            type=\"text\"\r\n                            value={usuario}\r\n                            onChange={(e) => setUsuario(e.target.value)}\r\n                            className=\"form-control\"\r\n                            placeholder=\"Digite o usuário master\"\r\n                            disabled={isLoading}\r\n                        />\r\n                    </div>\r\n\r\n                    <div className=\"form-group\">\r\n                        <label>Senha Master:</label>\r\n                        <input\r\n                            type=\"password\"\r\n                            value={senha}\r\n                            onChange={(e) => setSenha(e.target.value)}\r\n                            className=\"form-control\"\r\n                            placeholder=\"Digite a senha master\"\r\n                            disabled={isLoading}\r\n                        />\r\n                    </div>\r\n\r\n                    <div className=\"form-group\">\r\n                        <label>Para confirmar, digite o nome da empresa:</label>\r\n                        <input\r\n                            type=\"text\"\r\n                            value={confirmarNome}\r\n                            onChange={(e) => setConfirmarNome(e.target.value)}\r\n                            className=\"form-control\"\r\n                            placeholder={`Digite: ${empresaData?.name}`}\r\n                            disabled={isLoading}\r\n                        />\r\n                        <small className=\"text-muted\">Digite exatamente: <strong>{empresaData?.name}</strong></small>\r\n                    </div>\r\n\r\n                    <div className=\"delete-master-buttons\">\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={handleClose}\r\n                            className=\"btn-cancel\"\r\n                            disabled={isLoading}\r\n                        >\r\n                            <FiIcons.FiX /> Cancelar\r\n                        </button>\r\n                        <button\r\n                            type=\"submit\"\r\n                            className=\"btn-delete-master\"\r\n                            disabled={isLoading || !usuario || !senha || !confirmarNome}\r\n                        >\r\n                            {isLoading ? (\r\n                                <>\r\n                                    <AiIcons.AiOutlineLoading3Quarters className=\"spinning\" />\r\n                                    Deletando...\r\n                                </>\r\n                            ) : (\r\n                                <>\r\n                                    <FiIcons.FiTrash2 /> Deletar Permanentemente\r\n                                </>\r\n                            )}\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </Modal>\r\n    );\r\n};\r\n\r\nconst Teste = styled.div`\r\n    display: flex;\r\n    margin-left:  ${({ sidebar }) => (sidebar ? '250px' : '100px')}; \r\n    height:auto;\r\n    width:auto;\r\n    transition: 150ms;\r\n    background-color:rgb(247,247,247)!important;\r\n    overflow: initial;\r\n    z-Index: 9;\r\n\r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\n// Tooltip (Mini Modal)\r\nconst Tooltip = styled.div`\r\n    position: absolute;\r\n    background-color: black;\r\n    color: white;\r\n    padding: 5px 8px;\r\n    border-radius: 5px;\r\n    font-size: 12px;\r\n    white-space: nowrap;\r\n    opacity: 0;\r\n    transition: opacity 0.2s ease-in-out;\r\n    pointer-events: none;\r\n    transform: translate(-50%, -5px);\r\n    z-index:12;\r\n`;\r\n\r\n// Wrapper para os botões com tooltip\r\nconst ButtonWrapper = styled.div`\r\n    position: relative;\r\n    display: inline-block;\r\n    \r\n    &:hover ${Tooltip} {\r\n        opacity: 1;\r\n    }\r\n`;\r\n\r\n\r\nconst columns = [\r\n    {\r\n        title: 'Nome',\r\n        dataIndex: 'name',\r\n        key: 'name',\r\n    },\r\n    {\r\n        title: 'E-mail',\r\n        dataIndex: 'email',\r\n        key: 'email',\r\n    },\r\n    {\r\n        title: 'CPF/CNPJ',\r\n        dataIndex: 'cnpj',\r\n        key: 'cnpj',\r\n    },\r\n    {\r\n        title: 'ID da Instância',\r\n        dataIndex: 'instanceId',\r\n        key: 'instanceId',\r\n    },\r\n    {\r\n        title: 'Status WhatsApp',\r\n        dataIndex: 'instanceStatus',\r\n        key: 'instanceStatus',\r\n    },\r\n    {\r\n        title: 'Data Criação',\r\n        dataIndex: 'createdAt',\r\n        key: 'createdAt',\r\n    },\r\n    {\r\n        title: 'Ver faturas',\r\n        dataIndex: 'verFaturas',\r\n        key: 'verFaturas',\r\n    },\r\n    {\r\n        title: 'Ações',\r\n        dataIndex: 'actions',\r\n        key: 'actions',\r\n    }\r\n];\r\n\r\nconst statusMap = {\r\n    PENDING: \"Pendente\",\r\n    PAID: \"Paga\",\r\n    CANCELED: \"Cancelada\",\r\n    IN_ANALYSIS: \"Em Análise\",\r\n    DRAFT: \"Rascunho\",\r\n    PARTIALLY_PAID: \"Parcialmente Paga\",\r\n    REFUNDED: \"Reembolsada\",\r\n    EXPIRED: \"Expirada\",\r\n    IN_PROTEST: \"Em Protesto\",\r\n    CHARGEBACK: \"Contestada\",\r\n    EXTERNALLY_PAID: \"Paga Externamente\",\r\n    RECEIVED: \"Paga\",\r\n    CONFIRMED: \"Confirmada\",\r\n    OVERDUE: \"Atrasada\",\r\n    RECEIVED_IN_CASH: \"Recebida em Dinheiro\",\r\n    REFUND_REQUESTED: \"Reembolso Solicitado\",\r\n    REFUND_IN_PROGRESS: \"Reembolso em Andamento\",\r\n    CHARGEBACK_REQUESTED: \"Contestação Solicitada\",\r\n    CHARGEBACK_DISPUTE: \"Disputa de Contestação\",\r\n    AWAITING_CHARGEBACK_REVERSAL: \"Aguardando Reversão de Contestação\",\r\n    DUNNING_REQUESTED: \"Cobrança Judicial Solicitada\",\r\n    DUNNING_RECEIVED: \"Cobrança Judicial Recebida\",\r\n    AWAITING_RISK_ANALYSIS: \"Aguardando Análise de Risco\",\r\n};\r\n\r\nconst ListEmpresas = ({ list = [] }) => {\r\n    const { user } = useContext(AuthContext);\r\n    const empresa = localStorage.getItem('empresa')\r\n    const empresaParse = JSON.parse(empresa)\r\n    const objIdEmpresa = empresaParse._id;\r\n    const vinculo_empresa = empresaParse.cnpj;\r\n    const navigate = useNavigate();\r\n\r\n    const { sidebar, setSidebar } = useContext(SidebarContext);;\r\n    //console.log(\"LISTUSERS\",sidebar);\r\n\r\n    const [order, setOrder] = useState(1)\r\n    const [columnOrder, setColumnOrder] = useState('title')\r\n    const [allEmpresas, setAllEmpresas] = useState([]); // Armazena todas as empresas\r\n    const [empresas, setEmpresas] = useState([]);\r\n    const [filter, setFilter] = useState('');\r\n\r\n    const [_idEntregadorEdit, set_idEntregadorEdit] = useState('');\r\n    const [nomeEntregadorEdit, setNomeEntregadorEdit] = useState('');\r\n    const [veiculoEdit, setVeiculoEdit] = useState('');\r\n    const [telefoneEdit, setTelefoneEdit] = useState('');\r\n    const [placaEdit, setPlacaEdit] = useState('');\r\n\r\n    const [result, setResult] = useState(false);\r\n\r\n    const [showEditEntregador, setEditEntregador] = useState(true);\r\n\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    const [refresh, setRefresh] = useState(false);\r\n    const [page, setPage] = useState(1);\r\n    const [limit] = useState(10);\r\n    const [total, setTotal] = useState(0); // Armazena o total de empresas disponíveis\r\n    const [isModalOpen, setIsModalOpen] = useState(false);\r\n    const [invoices, setInvoices] = useState([]);\r\n    const [deleteMasterModalOpen, setDeleteMasterModalOpen] = useState(false);\r\n    const [empresaToDelete, setEmpresaToDelete] = useState(null);\r\n    const [deleteMasterLoading, setDeleteMasterLoading] = useState(false);\r\n\r\n    const INITIAL_DATA = {\r\n        value: \"\",\r\n        label: 'Selecione uma empresa',\r\n    };\r\n\r\n    const [selectData, setselectData] = useState(INITIAL_DATA);\r\n\r\n    /*useEffect(() => {\r\n        (async () => {\r\n\r\n            const response = await getEmpresasAdmin(user._id);\r\n            //console.log(\"TESTEEE:::\",user._id)\r\n            //const teste = [response.data]\r\n            setEmpresas(response.data.empresas);\r\n            console.log(response.data)\r\n            setLoading(false);\r\n            setRefresh(false);\r\n\r\n        })();\r\n    }, [refresh]);*/\r\n    /*useEffect(() => {\r\n        (async () => {\r\n            setLoading(true);\r\n            const response = await getEmpresasAdmin(user._id, page, limit);\r\n            console.log(response.data)\r\n            setEmpresas(response.data.empresas);\r\n            setTotal(response.data.total); // Atualiza o total com base no backend\r\n            setLoading(false);\r\n            setRefresh(false);\r\n        })();\r\n    }, [refresh, page]); // Dispara o efeito ao mudar de página*/\r\n    useEffect(() => {\r\n        (async () => {\r\n            setLoading(true);\r\n\r\n            // Buscar todas as empresas apenas uma vez (sem paginação)\r\n            if (allEmpresas.length === 0 || refresh) {\r\n                const response = await getEmpresasAdmin(user._id, 1, 1000000); // Pegamos todas as empresas\r\n                setAllEmpresas(response.data.empresas);\r\n            }\r\n\r\n            // Agora buscamos apenas as empresas da página atual\r\n            const response = await getEmpresasAdmin(user._id, page, limit);\r\n            setEmpresas(response.data.empresas);\r\n            setTotal(response.data.total);\r\n            setLoading(false);\r\n            setRefresh(false);\r\n        })();\r\n    }, [refresh, page]);\r\n\r\n    const handleEdit = async idToEdit => {\r\n        //setEditEntregador(!showEditEntregador);\r\n        console.log(JSON.stringify(empresas))\r\n        const entregadorToEdit = empresas.find(entregador => entregador._id === idToEdit);\r\n        if (entregadorToEdit) {\r\n            set_idEntregadorEdit(entregadorToEdit._id);\r\n            setNomeEntregadorEdit(entregadorToEdit.name);\r\n            setVeiculoEdit(entregadorToEdit.veiculo);\r\n            console.log(\"entregadorToEdit.telefone>\", entregadorToEdit.telefone)\r\n            setTelefoneEdit(entregadorToEdit.telefone);\r\n            setPlacaEdit(entregadorToEdit.placa);\r\n            setEditEntregador(!showEditEntregador);  // Supondo que isso abra o modal\r\n        }\r\n        /*const response = await getEntregadores(idToEdit);\r\n        //console.log(\"Infos do Edit:\",response.data.user)\r\n        //console.log(idToEdit,\"----\");\r\n        if(showEditEntregador){\r\n            set_idEntregadorEdit(idToEdit)            \r\n            setNomeEntregadorEdit(response.data.user.name)\r\n            setVeiculoEdit(response.data.user.email)            \r\n            setRoleEdit(response.data.user.role)           \r\n\r\n        }*/\r\n    }\r\n\r\n    const [confirmOpen, setConfirmOpen] = useState(false);\r\n    const [shouldDelete, setShouldDelete] = useState(false);\r\n    const [idToDelete, setIdToDelete] = useState('');\r\n    const handleDelete = (idRecebido) => {\r\n        // Abre Componente de Confirmação do delete\r\n        setConfirmOpen(true)\r\n\r\n        setIdToDelete(idRecebido)\r\n        if (idRecebido) {\r\n            //console.log(\"ID RECEBIDO:\",idRecebido)\r\n            setShouldDelete(true);\r\n        }\r\n    }\r\n\r\n    const deleteReg = async () => {\r\n        //console.log(\"O ID CLICADO FOI :::\",idToDelete);\r\n        //console.log(\"ID do Usuário que irá efetuar a exclusão\", user._id);    \r\n        if (shouldDelete) {\r\n            setLoading(true);\r\n            deleteInstanceAdmin(user._id, idToDelete).then(cast => {\r\n                //console.log(\"RESULT DELETE:\",cast);\r\n                if (cast.status === 201) {\r\n                    toast(cast.data.msg, { autoClose: 5000, type: \"success\" });\r\n                    setRefresh(true);\r\n                } else {\r\n                    toast(cast.data.msg, { autoClose: 5000, type: \"error\" });\r\n                }\r\n            }).catch(err => {\r\n                //console.log(\"ERROR:\",err);\r\n                toast(err.response.data.msg, { autoClose: 5000, type: \"error\" });\r\n            });\r\n        }\r\n\r\n    }\r\n\r\n    const handleCreateInstance = async (empresaId) => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await createInstanceAdmin(user._id, empresaId);\r\n\r\n            if (response.status === 201) {\r\n                toast.success(response.data.msg); // Exibe toast apenas se sucesso\r\n                setRefresh(true); // Atualiza a lista\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Erro ao criar instância:\", error);\r\n            const msg = error.response?.data?.msg || \"Erro desconhecido\";\r\n            toast.error(msg);\r\n        } finally {\r\n            setLoading(false); // Garante que o estado de carregamento seja atualizado no final\r\n        }\r\n    };\r\n\r\n    const handleOrder = fieldName => {\r\n        setOrder(-order)\r\n        setColumnOrder(fieldName)\r\n        //console.log(\"order:\",order);\r\n        //console.log(\"fieldName\",fieldName)\r\n    }\r\n\r\n    const handleRefresh = () => {\r\n        //window.location.reload(false);\r\n        setLoading(true);\r\n        setRefresh(true);\r\n    }\r\n\r\n    const handleCadastro = () => {\r\n        //setSidebar(!sidebarTeste);\r\n        navigate(\"/cadastro-entregador\");\r\n\r\n    }\r\n\r\n    const handleFilter = e => {\r\n        setFilter(e.target.value);\r\n        setResult(!result);\r\n    }\r\n\r\n    const handleOpenFaturas = (faturas) => {\r\n        setIsModalOpen(true)\r\n        setInvoices(faturas)\r\n    }\r\n\r\n    const handleDeleteMaster = (empresa) => {\r\n        setEmpresaToDelete(empresa);\r\n        setDeleteMasterModalOpen(true);\r\n    }\r\n\r\n    const handleDeleteMasterConfirm = async (usuario, senha) => {\r\n        try {\r\n            setDeleteMasterLoading(true);\r\n            \r\n            const response = await deleteEmpresaCompleta(empresaToDelete._id, usuario, senha);\r\n            \r\n            if (response.status === 200) {\r\n                toast.success(`✅ ${response.data.msg}`);\r\n                setDeleteMasterModalOpen(false);\r\n                setEmpresaToDelete(null);\r\n                setRefresh(true); // Atualiza a lista\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Erro ao deletar empresa completa:\", error);\r\n            const errorMsg = error.response?.data?.msg || \"Erro desconhecido ao deletar empresa\";\r\n            toast.error(`❌ ${errorMsg}`);\r\n        } finally {\r\n            setDeleteMasterLoading(false);\r\n        }\r\n    }\r\n\r\n    const handleDeleteMasterClose = () => {\r\n        setDeleteMasterModalOpen(false);\r\n        setEmpresaToDelete(null);\r\n    }\r\n\r\n    /*\r\n    var arrayEmpresas = [];\r\n    arrayEmpresas = empresas.sort((a, b) => {\r\n        return a[columnOrder] < b[columnOrder] ? -order : order;\r\n    })\r\n\r\n    if (filter) {\r\n        const exp = eval(`/${filter.replace(/[^\\d\\w]+/, '.*')}/i`)\r\n\r\n        list = empresas.filter(item => exp.test(item.name))\r\n    }\r\n\r\n    const tableData = useMemo(() => {\r\n        if (filter) return list.map(({ _id, id_empresa, name, cnpj, whatsapp, instanceStatus, faturas }) => {\r\n            return {\r\n                key: _id,\r\n                code: id_empresa,\r\n                name: name,\r\n                cnpj: cnpj,\r\n                instanceId: whatsapp.id,\r\n                instanceStatus: (\r\n                    <span style={{\r\n                        color: instanceStatus.code === 400 ? \"red\"\r\n                            : instanceStatus.connected ? \"green\"\r\n                                : \"gray\",\r\n                        fontWeight: \"bold\"\r\n                    }}>\r\n                        {instanceStatus.code === 400 ? \"Desconectado\"\r\n                            : instanceStatus.connected ? \"Conectado\"\r\n                                : \"Desconhecido\"}\r\n                    </span>\r\n                ),\r\n                verFaturas: (\r\n                    <>\r\n                    {faturas.length > 0 ?\r\n                        <a className=\"openFaturasText\" onClick={() => handleOpenFaturas(faturas)}>Ver faturas</a>\r\n                        :\r\n                        <span>Não gerada</span>\r\n                    }\r\n                    </>\r\n                ),\r\n                actions: (\r\n                    <>\r\n                        <span className=\"btn btn-sm btn-danger\" style={{ marginLeft: \"5px\" }}><FiIcons.FiTrash2 style={{ color: \"white\" }} /></span>\r\n                    </>\r\n                )\r\n            }\r\n        });\r\n\r\n        return arrayEmpresas.map(({ _id, id_empresa, name, cnpj, whatsapp, instanceStatus, faturas }) => {\r\n            return {\r\n                key: _id,\r\n                code: id_empresa,\r\n                name: name,\r\n                cnpj: cnpj,\r\n                instanceId: whatsapp.id,\r\n                instanceStatus: (\r\n                    <span style={{\r\n                        color: instanceStatus.code === 400 ? \"red\"\r\n                            : instanceStatus.connected ? \"green\"\r\n                                : \"gray\",\r\n                        fontWeight: \"bold\"\r\n                    }}>\r\n                        {instanceStatus.code === 400 ? \"Desconectado\"\r\n                            : instanceStatus.connected ? \"Conectado\"\r\n                                : \"Desconhecido\"}\r\n                    </span>\r\n                ),\r\n                verFaturas: (\r\n                    <>\r\n                    {faturas.length > 0 ?\r\n                        <a className=\"openFaturasText\" onClick={() => handleOpenFaturas(faturas)}>Ver faturas</a>\r\n                        :\r\n                        <span>Não gerada</span>\r\n                    }\r\n                    </>\r\n                ),\r\n                actions: (\r\n                    <>\r\n                        <span className=\"btn btn-sm btn-danger\" style={{ marginLeft: \"5px\" }}><FiIcons.FiTrash2 style={{ color: \"white\" }} /></span>\r\n                    </>\r\n                )\r\n            }\r\n        })\r\n    }, [filter, arrayEmpresas, list]);\r\n    */\r\n\r\n    \r\n    // Função para remover caracteres não numéricos\r\n    const removeMask = (str) => str.replace(/\\D/g, '');\r\n    // Função para filtrar empresas\r\n    const filteredEmpresas = useMemo(() => {\r\n        if (!filter) return allEmpresas; // Se não houver filtro, retorna todas\r\n\r\n        const cleanFilter = removeMask(filter); // Remove a máscara do filtro digitado\r\n        const exp = new RegExp(filter.replace(/[^\\d\\w]+/, '.*'), 'i');\r\n\r\n        return allEmpresas.filter(item =>\r\n            exp.test(item.name) ||               // Filtra pelo nome\r\n            exp.test(item.email) ||              // Filtra pelo email\r\n            exp.test(item.cnpj) ||               // Filtra pelo CNPJ com máscara\r\n            exp.test(removeMask(item.cnpj)) ||   // Filtra pelo CNPJ sem máscara\r\n            (item.whatsapp?.id && exp.test(item.whatsapp?.id)) // Filtra pelo ID do WhatsApp, se existir\r\n        );\r\n    }, [filter, allEmpresas]);\r\n\r\n    // Aplicar a paginação na lista filtrada\r\n    const paginatedEmpresas = useMemo(() => {\r\n        return filteredEmpresas.slice((page - 1) * limit, page * limit);\r\n    }, [filteredEmpresas, page, limit]);\r\n\r\n    const tableData = useMemo(() => {\r\n        return paginatedEmpresas.map(({ _id, id_empresa, name, email, cnpj, whatsapp, instanceStatus, createdAt, faturas }) => ({\r\n            key: _id,\r\n            code: id_empresa,\r\n            name: name,\r\n            email: email,\r\n            cnpj: cnpj,\r\n            instanceId: whatsapp?.id || \"N/A\", // 🔹 Evita erro quando whatsapp é undefined\r\n            instanceStatus: (\r\n                <span style={{\r\n                    color: instanceStatus?.code === 400 ? \"red\"\r\n                        : instanceStatus?.connected ? \"green\"\r\n                            : \"gray\",\r\n                    fontWeight: \"bold\"\r\n                }}>\r\n                    {instanceStatus?.code === 400 ? \"Desconectado\"\r\n                        : instanceStatus?.connected ? \"Conectado\"\r\n                            : \"Desconhecido\"}\r\n                </span>\r\n            ),\r\n            createdAt: moment(createdAt).format(\"DD/MM/YYYY\"),\r\n            verFaturas: (\r\n                <>\r\n                    {faturas.length > 0 ?\r\n                        <a className=\"openFaturasText\" onClick={() => handleOpenFaturas(faturas)}>Ver faturas</a>\r\n                        :\r\n                        <span>Não gerada</span>\r\n                    }\r\n                </>\r\n            ),\r\n            actions: (\r\n                <>\r\n                    {/* Botão de deletar instância */}\r\n                    <ButtonWrapper>\r\n                        <span\r\n                            className=\"btn btn-sm\"\r\n                            onClick={whatsapp?.id ? () => handleDelete(_id) : undefined}\r\n                            style={{\r\n                                marginLeft: \"5px\",\r\n                                backgroundColor: whatsapp?.id ? \"red\" : \"gray\",\r\n                                cursor: whatsapp?.id ? \"pointer\" : \"not-allowed\",\r\n                                position: \"relative\"\r\n                            }}\r\n                        >\r\n                            <FiIcons.FiTrash2 style={{ color: \"white\" }} />\r\n                        </span>\r\n                        <Tooltip>Deletar Instância</Tooltip>\r\n                    </ButtonWrapper>\r\n\r\n                    {/* Botão de criar instância */}\r\n                    <ButtonWrapper>\r\n                        <span\r\n                            className=\"btn btn-sm\"\r\n                            onClick={!whatsapp?.id ? () => handleCreateInstance(_id) : undefined}\r\n                            style={{\r\n                                marginLeft: \"5px\",\r\n                                backgroundColor: !whatsapp?.id ? \"rgb(66, 129, 255)\" : \"gray\",\r\n                                cursor: !whatsapp?.id ? \"pointer\" : \"not-allowed\",\r\n                                position: \"relative\"\r\n                            }}\r\n                        >\r\n                            <FaIcons.FaPlus style={{ color: \"white\" }} />\r\n                        </span>\r\n                        <Tooltip>Criar Instância</Tooltip>\r\n                    </ButtonWrapper>\r\n\r\n                    {/* Botão de Delete Master Completo */}\r\n                    <ButtonWrapper>\r\n                        <span\r\n                            className=\"btn btn-sm btn-delete-master\"\r\n                            onClick={() => handleDeleteMaster({ _id, name, email, cnpj })}\r\n                            style={{\r\n                                marginLeft: \"5px\",\r\n                                position: \"relative\"\r\n                            }}\r\n                        >\r\n                            <FaIcons.FaBomb style={{ color: \"white\" }} />\r\n                        </span>\r\n                        <Tooltip>Delete Master Completo</Tooltip>\r\n                    </ButtonWrapper>\r\n                </>\r\n            )\r\n        }));\r\n    }, [paginatedEmpresas]);\r\n\r\n    return (\r\n\r\n        <>\r\n            <PermissionGate permissions={['default']}>\r\n\r\n                <ConfirmDialog\r\n                    title=\"Deletar Instância?\"\r\n                    open={confirmOpen}\r\n                    setOpen={setConfirmOpen}\r\n                    onConfirm={deleteReg}\r\n                >\r\n                    Tem certeza que deseja deletar a instância/whatsapp da empresa?\r\n                </ConfirmDialog>\r\n\r\n                <ModalEditEntregador setEditEntregador={setEditEntregador} showEditEntregador={showEditEntregador}\r\n                    setRefresh={setRefresh}\r\n                    selectData={selectData}\r\n                    setselectData={setselectData}\r\n                    _idEntregadorEdit={_idEntregadorEdit}\r\n                    nomeEntregadorEdit={nomeEntregadorEdit}\r\n                    veiculoEdit={veiculoEdit}\r\n                    telefoneEdit={telefoneEdit}\r\n                    placaEdit={placaEdit}\r\n                    userID={user._id}\r\n                    id_empresa={objIdEmpresa}\r\n                />\r\n\r\n                <InvoicesModal\r\n                    invoices={invoices}\r\n                    isOpen={isModalOpen}\r\n                    onClose={() => setIsModalOpen(false)}\r\n                />\r\n\r\n                <DeleteMasterModal\r\n                    isOpen={deleteMasterModalOpen}\r\n                    onClose={handleDeleteMasterClose}\r\n                    onConfirm={handleDeleteMasterConfirm}\r\n                    empresaData={empresaToDelete}\r\n                    isLoading={deleteMasterLoading}\r\n                />\r\n\r\n                <Teste sidebar={sidebar}>\r\n                    {loading ? <div className=\"loading\"><AiIcons.AiOutlineLoading3Quarters style={{ fontSize: \"100px\", color: \"rgb(180,180,180)\" }} /></div> : null}\r\n                    <div className=\"w-100 p-4\">\r\n                        <div className=\"list-header-empresas\">\r\n                            <div className=\"title\">\r\n                                <h1>Empresas</h1>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex-column flex-md-row\" style={{ display: \"flex\", justifyContent: \"space-between\"/*, height:\"80px\"*/ }}>\r\n\r\n                            <div className=\"input-box-list\" style={{ width: \"100%\", maxWidth: \"400px\" }}>\r\n                                <input className=\"input-field\" placeholder=\"Pesquisar\" onChange={handleFilter} />\r\n                                <i className=\"icon\"><FiIcons.FiSearch /></i>\r\n                            </div>\r\n\r\n                            <div className=\"div-buttons flex-column flex-md-row\">\r\n\r\n                                <div className=\"refresh-button\" style={{ textAlign: \"start\", marginBottom: \"10px\" }}>\r\n                                    <button className=\"refresh-button\" onClick={handleRefresh}>\r\n                                        <SlIcons.SlRefresh style={{ color: \"#4281FF\", marginRight: \"5px\", fontSize: \"18px\", marginBottom: \"2px\" }} /><a >Atualizar</a>\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n\r\n\r\n\r\n                        </div>\r\n\r\n                        <Table\r\n                            columns={columns}\r\n                            dataSource={tableData}\r\n                            pagination={{\r\n                                current: page,\r\n                                pageSize: limit,\r\n                                total: filteredEmpresas.length, // Total agora é baseado na lista filtrada\r\n                                onChange: (newPage) => setPage(newPage),\r\n                                showSizeChanger: false,\r\n                            }}\r\n                            rowClassName=\"row-table\"\r\n                            rowHoverable={true}\r\n                        />\r\n                        <div>Total de empresas ativas: {total}</div>\r\n                    </div>\r\n                </Teste>\r\n\r\n            </PermissionGate>\r\n        </>\r\n\r\n\r\n    );\r\n};\r\n\r\nexport default ListEmpresas;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,OAAO,aAAa;AACpB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAO,sCAAsC;AAC7C,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,uCAAuC;AAC9C,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,eAAe,QAAQ,gBAAgB;AAEhD,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAASC,WAAW,QAAQ,qBAAqB;AAEjD,SAASC,mBAAmB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAGC,qBAAqB,QAAQ,oBAAoB;AAE1I,OAAOC,cAAc,MAAM,+BAA+B;AAE1D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,aAAa,MAAM,gCAAgC;AAE1D,SAASC,UAAU,QAAQ,2BAA2B;AAEtD,SAASC,SAAS,QAAQ,QAAQ;AAClC,OAAO,KAAKC,GAAG,MAAM,KAAK;AAE1B,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EACrD,oBACIN,OAAA,CAACxB,KAAK;IACF6B,MAAM,EAAEA,MAAO;IACfE,cAAc,EAAED,OAAQ;IACxBE,YAAY,EAAC,wBAAqB;IAClCC,WAAW,EAAE,KAAM;IACnBC,SAAS,EAAC,uBAAuB;IACjCC,gBAAgB,EAAC,yBAAyB;IAAAC,QAAA,gBAE1CZ,OAAA;MAAAY,QAAA,EAAI;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5BhB,OAAA;MAAOU,SAAS,EAAC,uBAAuB;MAAAE,QAAA,gBACpCZ,OAAA;QAAAY,QAAA,eACIZ,OAAA;UAAAY,QAAA,gBACIZ,OAAA;YAAAY,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BhB,OAAA;YAAAY,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfhB,OAAA;YAAAY,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdhB,OAAA;YAAAY,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACRhB,OAAA;QAAAY,QAAA,EACKR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEa,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK;UAAA,IAAAC,iBAAA;UAAA,oBAC1BpB,OAAA;YAAAY,QAAA,gBACIZ,OAAA;cAAAY,QAAA,EAAKlC,MAAM,CAACwC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,OAAO,CAAC,CAACC,MAAM,CAAC,YAAY;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxDhB,OAAA;cAAAY,QAAA,EAAKW,SAAS,CAACL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,MAAM,CAAC,IAAI;YAAqB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAC5D,CAAAE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,MAAM,MAAK,SAAS,IAAI,CAAAN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,MAAM,MAAK,SAAS,gBAC5DxB,OAAA;cAAAY,QAAA,EACK,CAAC,CAAC,CAAAM,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,KAAK,KAAI,CAAC,KAAK,CAAAP,OAAO,aAAPA,OAAO,wBAAAE,iBAAA,GAAPF,OAAO,CAAEQ,QAAQ,cAAAN,iBAAA,uBAAjBA,iBAAA,CAAmBK,KAAK,KAAI,CAAC,CAAC,EACpDE,cAAc,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,gBAELhB,OAAA;cAAAY,QAAA,EACK,CAAE,CAAAM,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,KAAK,KAAI,CAAC,EACjBE,cAAc,CAAC,OAAO,EAAE;gBAAEC,KAAK,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAM,CAAC;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eAEThB,OAAA;cAAAY,QAAA,EACM,CAAAM,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,MAAM,MAAK,SAAS,IAAI,CAAAN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,MAAM,MAAK,SAAS,gBAC5DxB,OAAA;gBACI8B,IAAI,EAAEZ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,UAAW;gBAC1BC,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBACzBvB,SAAS,EAAC,4BAA4B;gBAAAE,QAAA,gBAEtCZ,OAAA,CAACvB,eAAe;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UACvB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,gBAEJhB,OAAA;gBAAQU,SAAS,EAAC,4DAA4D;gBAACwB,QAAQ;gBAAAtB,QAAA,gBACnFZ,OAAA,CAACvB,eAAe;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oBACvB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GA7BAG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8BV,CAAC;QAAA,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACRhB,OAAA;MAAQmC,OAAO,EAAE7B,OAAQ;MAACI,SAAS,EAAC,8BAA8B;MAAAE,QAAA,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/E,CAAC;AAEhB,CAAC;AAACoB,EAAA,GA3DIjC,aAAa;AA6DnB,MAAMkC,iBAAiB,GAAGA,CAAC;EAAEhC,MAAM;EAAEC,OAAO;EAAEgC,SAAS;EAAEC,WAAW;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAClF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0E,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM8E,YAAY,GAAIC,CAAC,IAAK;IAAA,IAAAC,iBAAA;IACxBD,CAAC,CAACE,cAAc,CAAC,CAAC;IAElB,IAAI,CAACT,OAAO,CAACU,IAAI,CAAC,CAAC,IAAI,CAACR,KAAK,CAACQ,IAAI,CAAC,CAAC,EAAE;MAClC7E,KAAK,CAAC8E,KAAK,CAAC,mCAAmC,CAAC;MAChD;IACJ;IAEA,IAAIP,aAAa,CAACQ,WAAW,CAAC,CAAC,CAACF,IAAI,CAAC,CAAC,MAAKb,WAAW,aAAXA,WAAW,wBAAAW,iBAAA,GAAXX,WAAW,CAAEgB,IAAI,cAAAL,iBAAA,uBAAjBA,iBAAA,CAAmBI,WAAW,CAAC,CAAC,CAACF,IAAI,CAAC,CAAC,GAAE;MAChF7E,KAAK,CAAC8E,KAAK,CAAC,mEAAmE,CAAC;MAChF;IACJ;IAEAf,SAAS,CAACI,OAAO,EAAEE,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMY,WAAW,GAAGA,CAAA,KAAM;IACtBb,UAAU,CAAC,EAAE,CAAC;IACdE,QAAQ,CAAC,EAAE,CAAC;IACZE,gBAAgB,CAAC,EAAE,CAAC;IACpBzC,OAAO,CAAC,CAAC;EACb,CAAC;EAED,oBACIN,OAAA,CAACxB,KAAK;IACF6B,MAAM,EAAEA,MAAO;IACfE,cAAc,EAAEiD,WAAY;IAC5BhD,YAAY,EAAC,+BAA+B;IAC5CC,WAAW,EAAE,KAAM;IACnBC,SAAS,EAAC,qBAAqB;IAC/BC,gBAAgB,EAAC,uBAAuB;IAAAC,QAAA,eAExCZ,OAAA;MAAKU,SAAS,EAAC,uBAAuB;MAAAE,QAAA,gBAClCZ,OAAA;QAAKU,SAAS,EAAC,sBAAsB;QAAAE,QAAA,eACjCZ,OAAA;UAAAY,QAAA,EAAI;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAENhB,OAAA;QAAKU,SAAS,EAAC,uBAAuB;QAAAE,QAAA,gBAClCZ,OAAA;UAAAY,QAAA,gBAAGZ,OAAA;YAAAY,QAAA,EAAQ;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,oDAAuC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvEhB,OAAA;UAAAY,QAAA,gBACIZ,OAAA;YAAAY,QAAA,GAAI,mBAAY,eAAAZ,OAAA;cAAAY,QAAA,EAAS2B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB;YAAI;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDhB,OAAA;YAAAY,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvChB,OAAA;YAAAY,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvChB,OAAA;YAAAY,QAAA,EAAI;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDhB,OAAA;YAAAY,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BhB,OAAA;YAAAY,QAAA,EAAI;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxChB,OAAA;YAAAY,QAAA,EAAI;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACLhB,OAAA;UAAGU,SAAS,EAAC,sBAAsB;UAAAE,QAAA,gBAC/BZ,OAAA,CAACR,OAAO,CAACiE,aAAa;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC,eAAAhB,OAAA;YAAAY,QAAA,EAAQ;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhB,OAAA;QAAM0D,QAAQ,EAAEV,YAAa;QAACtC,SAAS,EAAC,oBAAoB;QAAAE,QAAA,gBACxDZ,OAAA;UAAKU,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACvBZ,OAAA;YAAAY,QAAA,EAAO;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9BhB,OAAA;YACI2D,IAAI,EAAC,MAAM;YACXlC,KAAK,EAAEiB,OAAQ;YACfkB,QAAQ,EAAGX,CAAC,IAAKN,UAAU,CAACM,CAAC,CAACjB,MAAM,CAACP,KAAK,CAAE;YAC5Cf,SAAS,EAAC,cAAc;YACxBmD,WAAW,EAAC,4BAAyB;YACrC3B,QAAQ,EAAEM;UAAU;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENhB,OAAA;UAAKU,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACvBZ,OAAA;YAAAY,QAAA,EAAO;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BhB,OAAA;YACI2D,IAAI,EAAC,UAAU;YACflC,KAAK,EAAEmB,KAAM;YACbgB,QAAQ,EAAGX,CAAC,IAAKJ,QAAQ,CAACI,CAAC,CAACjB,MAAM,CAACP,KAAK,CAAE;YAC1Cf,SAAS,EAAC,cAAc;YACxBmD,WAAW,EAAC,uBAAuB;YACnC3B,QAAQ,EAAEM;UAAU;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENhB,OAAA;UAAKU,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACvBZ,OAAA;YAAAY,QAAA,EAAO;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxDhB,OAAA;YACI2D,IAAI,EAAC,MAAM;YACXlC,KAAK,EAAEqB,aAAc;YACrBc,QAAQ,EAAGX,CAAC,IAAKF,gBAAgB,CAACE,CAAC,CAACjB,MAAM,CAACP,KAAK,CAAE;YAClDf,SAAS,EAAC,cAAc;YACxBmD,WAAW,EAAE,WAAWtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,IAAI,EAAG;YAC5CrB,QAAQ,EAAEM;UAAU;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACFhB,OAAA;YAAOU,SAAS,EAAC,YAAY;YAAAE,QAAA,GAAC,qBAAmB,eAAAZ,OAAA;cAAAY,QAAA,EAAS2B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB;YAAI;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,eAENhB,OAAA;UAAKU,SAAS,EAAC,uBAAuB;UAAAE,QAAA,gBAClCZ,OAAA;YACI2D,IAAI,EAAC,QAAQ;YACbxB,OAAO,EAAEqB,WAAY;YACrB9C,SAAS,EAAC,YAAY;YACtBwB,QAAQ,EAAEM,SAAU;YAAA5B,QAAA,gBAEpBZ,OAAA,CAACR,OAAO,CAACsE,GAAG;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aACnB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThB,OAAA;YACI2D,IAAI,EAAC,QAAQ;YACbjD,SAAS,EAAC,mBAAmB;YAC7BwB,QAAQ,EAAEM,SAAS,IAAI,CAACE,OAAO,IAAI,CAACE,KAAK,IAAI,CAACE,aAAc;YAAAlC,QAAA,EAE3D4B,SAAS,gBACNxC,OAAA,CAAAE,SAAA;cAAAU,QAAA,gBACIZ,OAAA,CAACP,OAAO,CAACsE,yBAAyB;gBAACrD,SAAS,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9D;YAAA,eAAE,CAAC,gBAEHhB,OAAA,CAAAE,SAAA;cAAAU,QAAA,gBACIZ,OAAA,CAACR,OAAO,CAACwE,QAAQ;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BACxB;YAAA,eAAE;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACyB,EAAA,CA9HIJ,iBAAiB;AAAA4B,GAAA,GAAjB5B,iBAAiB;AAgIvB,MAAM6B,KAAK,GAAG7F,MAAM,CAAC8F,GAAG;AACxB;AACA,oBAAoB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMH,KAAK;AAgBX,MAAMI,OAAO,GAAGjG,MAAM,CAAC8F,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAI,GAAA,GAfMD,OAAO;AAgBb,MAAME,aAAa,GAAGnG,MAAM,CAAC8F,GAAG;AAChC;AACA;AACA;AACA,cAAcG,OAAO;AACrB;AACA;AACA,CAAC;AAACG,GAAA,GAPID,aAAa;AAUnB,MAAME,OAAO,GAAG,CACZ;EACIC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE,MAAM;EACjBC,GAAG,EAAE;AACT,CAAC,EACD;EACIF,KAAK,EAAE,QAAQ;EACfC,SAAS,EAAE,OAAO;EAClBC,GAAG,EAAE;AACT,CAAC,EACD;EACIF,KAAK,EAAE,UAAU;EACjBC,SAAS,EAAE,MAAM;EACjBC,GAAG,EAAE;AACT,CAAC,EACD;EACIF,KAAK,EAAE,iBAAiB;EACxBC,SAAS,EAAE,YAAY;EACvBC,GAAG,EAAE;AACT,CAAC,EACD;EACIF,KAAK,EAAE,iBAAiB;EACxBC,SAAS,EAAE,gBAAgB;EAC3BC,GAAG,EAAE;AACT,CAAC,EACD;EACIF,KAAK,EAAE,cAAc;EACrBC,SAAS,EAAE,WAAW;EACtBC,GAAG,EAAE;AACT,CAAC,EACD;EACIF,KAAK,EAAE,aAAa;EACpBC,SAAS,EAAE,YAAY;EACvBC,GAAG,EAAE;AACT,CAAC,EACD;EACIF,KAAK,EAAE,OAAO;EACdC,SAAS,EAAE,SAAS;EACpBC,GAAG,EAAE;AACT,CAAC,CACJ;AAED,MAAMtD,SAAS,GAAG;EACduD,OAAO,EAAE,UAAU;EACnBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,YAAY;EACzBC,KAAK,EAAE,UAAU;EACjBC,cAAc,EAAE,mBAAmB;EACnCC,QAAQ,EAAE,aAAa;EACvBC,OAAO,EAAE,UAAU;EACnBC,UAAU,EAAE,aAAa;EACzBC,UAAU,EAAE,YAAY;EACxBC,eAAe,EAAE,mBAAmB;EACpCC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE,YAAY;EACvBC,OAAO,EAAE,UAAU;EACnBC,gBAAgB,EAAE,sBAAsB;EACxCC,gBAAgB,EAAE,sBAAsB;EACxCC,kBAAkB,EAAE,wBAAwB;EAC5CC,oBAAoB,EAAE,wBAAwB;EAC9CC,kBAAkB,EAAE,wBAAwB;EAC5CC,4BAA4B,EAAE,oCAAoC;EAClEC,iBAAiB,EAAE,8BAA8B;EACjDC,gBAAgB,EAAE,4BAA4B;EAC9CC,sBAAsB,EAAE;AAC5B,CAAC;AAED,MAAMC,YAAY,GAAGA,CAAC;EAAEC,IAAI,GAAG;AAAG,CAAC,KAAK;EAAAC,GAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGrI,UAAU,CAACQ,WAAW,CAAC;EACxC,MAAM8H,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,CAAC;EACxC,MAAMM,YAAY,GAAGH,YAAY,CAACI,GAAG;EACrC,MAAMC,eAAe,GAAGL,YAAY,CAACM,IAAI;EACzC,MAAMC,QAAQ,GAAGjI,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAEkF,OAAO;IAAEgD;EAAW,CAAC,GAAGjJ,UAAU,CAACG,cAAc,CAAC;EAAC;EAC3D;;EAEA,MAAM,CAAC+I,KAAK,EAAEC,QAAQ,CAAC,GAAGpJ,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACqJ,WAAW,EAAEC,cAAc,CAAC,GAAGtJ,QAAQ,CAAC,OAAO,CAAC;EACvD,MAAM,CAACuJ,WAAW,EAAEC,cAAc,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,MAAM,CAACyJ,QAAQ,EAAEC,WAAW,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2J,MAAM,EAAEC,SAAS,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAM,CAAC6J,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9J,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC+J,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhK,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACiK,WAAW,EAAEC,cAAc,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmK,YAAY,EAAEC,eAAe,CAAC,GAAGpK,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqK,SAAS,EAAEC,YAAY,CAAC,GAAGtK,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAACuK,MAAM,EAAEC,SAAS,CAAC,GAAGxK,QAAQ,CAAC,KAAK,CAAC;EAE3C,MAAM,CAACyK,kBAAkB,EAAEC,iBAAiB,CAAC,GAAG1K,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAM,CAAC2K,OAAO,EAAEC,UAAU,CAAC,GAAG5K,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM,CAAC6K,OAAO,EAAEC,UAAU,CAAC,GAAG9K,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+K,IAAI,EAAEC,OAAO,CAAC,GAAGhL,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACiL,KAAK,CAAC,GAAGjL,QAAQ,CAAC,EAAE,CAAC;EAC5B,MAAM,CAACkL,KAAK,EAAEC,QAAQ,CAAC,GAAGnL,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,MAAM,CAACoL,WAAW,EAAEC,cAAc,CAAC,GAAGrL,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkC,QAAQ,EAAEoJ,WAAW,CAAC,GAAGtL,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuL,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGxL,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACyL,eAAe,EAAEC,kBAAkB,CAAC,GAAG1L,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2L,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5L,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAM6L,YAAY,GAAG;IACjBtI,KAAK,EAAE,EAAE;IACTuI,KAAK,EAAE;EACX,CAAC;EAED,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhM,QAAQ,CAAC6L,YAAY,CAAC;;EAE1D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAGI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3L,SAAS,CAAC,MAAM;IACZ,CAAC,YAAY;MACT0K,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAIrB,WAAW,CAAC0C,MAAM,KAAK,CAAC,IAAIpB,OAAO,EAAE;QACrC,MAAMqB,QAAQ,GAAG,MAAMvL,gBAAgB,CAAC2H,IAAI,CAACQ,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;QAC/DU,cAAc,CAAC0C,QAAQ,CAACC,IAAI,CAAC1C,QAAQ,CAAC;MAC1C;;MAEA;MACA,MAAMyC,QAAQ,GAAG,MAAMvL,gBAAgB,CAAC2H,IAAI,CAACQ,GAAG,EAAEiC,IAAI,EAAEE,KAAK,CAAC;MAC9DvB,WAAW,CAACwC,QAAQ,CAACC,IAAI,CAAC1C,QAAQ,CAAC;MACnC0B,QAAQ,CAACe,QAAQ,CAACC,IAAI,CAACjB,KAAK,CAAC;MAC7BN,UAAU,CAAC,KAAK,CAAC;MACjBE,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,CAAC;EACR,CAAC,EAAE,CAACD,OAAO,EAAEE,IAAI,CAAC,CAAC;EAEnB,MAAMqB,UAAU,GAAG,MAAMC,QAAQ,IAAI;IACjC;IACAC,OAAO,CAACC,GAAG,CAAC5D,IAAI,CAAC6D,SAAS,CAAC/C,QAAQ,CAAC,CAAC;IACrC,MAAMgD,gBAAgB,GAAGhD,QAAQ,CAACiD,IAAI,CAACC,UAAU,IAAIA,UAAU,CAAC7D,GAAG,KAAKuD,QAAQ,CAAC;IACjF,IAAII,gBAAgB,EAAE;MAClB3C,oBAAoB,CAAC2C,gBAAgB,CAAC3D,GAAG,CAAC;MAC1CkB,qBAAqB,CAACyC,gBAAgB,CAACpH,IAAI,CAAC;MAC5C6E,cAAc,CAACuC,gBAAgB,CAACG,OAAO,CAAC;MACxCN,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEE,gBAAgB,CAACI,QAAQ,CAAC;MACpEzC,eAAe,CAACqC,gBAAgB,CAACI,QAAQ,CAAC;MAC1CvC,YAAY,CAACmC,gBAAgB,CAACK,KAAK,CAAC;MACpCpC,iBAAiB,CAAC,CAACD,kBAAkB,CAAC,CAAC,CAAE;IAC7C;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEI,CAAC;EAED,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGhN,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiN,YAAY,EAAEC,eAAe,CAAC,GAAGlN,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmN,UAAU,EAAEC,aAAa,CAAC,GAAGpN,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMqN,YAAY,GAAIC,UAAU,IAAK;IACjC;IACAN,cAAc,CAAC,IAAI,CAAC;IAEpBI,aAAa,CAACE,UAAU,CAAC;IACzB,IAAIA,UAAU,EAAE;MACZ;MACAJ,eAAe,CAAC,IAAI,CAAC;IACzB;EACJ,CAAC;EAED,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC1B;IACA;IACA,IAAIN,YAAY,EAAE;MACdrC,UAAU,CAAC,IAAI,CAAC;MAChBlK,mBAAmB,CAAC4H,IAAI,CAACQ,GAAG,EAAEqE,UAAU,CAAC,CAACK,IAAI,CAACC,IAAI,IAAI;QACnD;QACA,IAAIA,IAAI,CAACnK,MAAM,KAAK,GAAG,EAAE;UACrBjD,KAAK,CAACoN,IAAI,CAACtB,IAAI,CAACuB,GAAG,EAAE;YAAEC,SAAS,EAAE,IAAI;YAAElI,IAAI,EAAE;UAAU,CAAC,CAAC;UAC1DqF,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHzK,KAAK,CAACoN,IAAI,CAACtB,IAAI,CAACuB,GAAG,EAAE;YAAEC,SAAS,EAAE,IAAI;YAAElI,IAAI,EAAE;UAAQ,CAAC,CAAC;QAC5D;MACJ,CAAC,CAAC,CAACmI,KAAK,CAACC,GAAG,IAAI;QACZ;QACAxN,KAAK,CAACwN,GAAG,CAAC3B,QAAQ,CAACC,IAAI,CAACuB,GAAG,EAAE;UAAEC,SAAS,EAAE,IAAI;UAAElI,IAAI,EAAE;QAAQ,CAAC,CAAC;MACpE,CAAC,CAAC;IACN;EAEJ,CAAC;EAED,MAAMqI,oBAAoB,GAAG,MAAOC,SAAS,IAAK;IAC9C,IAAI;MACAnD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsB,QAAQ,GAAG,MAAMtL,mBAAmB,CAAC0H,IAAI,CAACQ,GAAG,EAAEiF,SAAS,CAAC;MAE/D,IAAI7B,QAAQ,CAAC5I,MAAM,KAAK,GAAG,EAAE;QACzBjD,KAAK,CAAC2N,OAAO,CAAC9B,QAAQ,CAACC,IAAI,CAACuB,GAAG,CAAC,CAAC,CAAC;QAClC5C,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC,OAAO3F,KAAK,EAAE;MAAA,IAAA8I,eAAA,EAAAC,oBAAA;MACZ5B,OAAO,CAACnH,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMuI,GAAG,GAAG,EAAAO,eAAA,GAAA9I,KAAK,CAAC+G,QAAQ,cAAA+B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB9B,IAAI,cAAA+B,oBAAA,uBAApBA,oBAAA,CAAsBR,GAAG,KAAI,mBAAmB;MAC5DrN,KAAK,CAAC8E,KAAK,CAACuI,GAAG,CAAC;IACpB,CAAC,SAAS;MACN9C,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IACvB;EACJ,CAAC;EAED,MAAMuD,WAAW,GAAGC,SAAS,IAAI;IAC7BhF,QAAQ,CAAC,CAACD,KAAK,CAAC;IAChBG,cAAc,CAAC8E,SAAS,CAAC;IACzB;IACA;EACJ,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxB;IACAzD,UAAU,CAAC,IAAI,CAAC;IAChBE,UAAU,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMwD,cAAc,GAAGA,CAAA,KAAM;IACzB;IACArF,QAAQ,CAAC,sBAAsB,CAAC;EAEpC,CAAC;EAED,MAAMsF,YAAY,GAAGxJ,CAAC,IAAI;IACtB6E,SAAS,CAAC7E,CAAC,CAACjB,MAAM,CAACP,KAAK,CAAC;IACzBiH,SAAS,CAAC,CAACD,MAAM,CAAC;EACtB,CAAC;EAED,MAAMiE,iBAAiB,GAAIC,OAAO,IAAK;IACnCpD,cAAc,CAAC,IAAI,CAAC;IACpBC,WAAW,CAACmD,OAAO,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAInG,OAAO,IAAK;IACpCmD,kBAAkB,CAACnD,OAAO,CAAC;IAC3BiD,wBAAwB,CAAC,IAAI,CAAC;EAClC,CAAC;EAED,MAAMmD,yBAAyB,GAAG,MAAAA,CAAOnK,OAAO,EAAEE,KAAK,KAAK;IACxD,IAAI;MACAkH,sBAAsB,CAAC,IAAI,CAAC;MAE5B,MAAMM,QAAQ,GAAG,MAAMpL,qBAAqB,CAAC2K,eAAe,CAAC3C,GAAG,EAAEtE,OAAO,EAAEE,KAAK,CAAC;MAEjF,IAAIwH,QAAQ,CAAC5I,MAAM,KAAK,GAAG,EAAE;QACzBjD,KAAK,CAAC2N,OAAO,CAAC,KAAK9B,QAAQ,CAACC,IAAI,CAACuB,GAAG,EAAE,CAAC;QACvClC,wBAAwB,CAAC,KAAK,CAAC;QAC/BE,kBAAkB,CAAC,IAAI,CAAC;QACxBZ,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC,OAAO3F,KAAK,EAAE;MAAA,IAAAyJ,gBAAA,EAAAC,qBAAA;MACZvC,OAAO,CAACnH,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAM2J,QAAQ,GAAG,EAAAF,gBAAA,GAAAzJ,KAAK,CAAC+G,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzC,IAAI,cAAA0C,qBAAA,uBAApBA,qBAAA,CAAsBnB,GAAG,KAAI,sCAAsC;MACpFrN,KAAK,CAAC8E,KAAK,CAAC,KAAK2J,QAAQ,EAAE,CAAC;IAChC,CAAC,SAAS;MACNlD,sBAAsB,CAAC,KAAK,CAAC;IACjC;EACJ,CAAC;EAED,MAAMmD,uBAAuB,GAAGA,CAAA,KAAM;IAClCvD,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAOI;EACA,MAAMsD,UAAU,GAAIC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAClD;EACA,MAAMC,gBAAgB,GAAGxN,OAAO,CAAC,MAAM;IACnC,IAAI,CAACgI,MAAM,EAAE,OAAOJ,WAAW,CAAC,CAAC;;IAEjC,MAAM6F,WAAW,GAAGJ,UAAU,CAACrF,MAAM,CAAC,CAAC,CAAC;IACxC,MAAM0F,GAAG,GAAG,IAAIC,MAAM,CAAC3F,MAAM,CAACuF,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;IAE7D,OAAO3F,WAAW,CAACI,MAAM,CAAC4F,IAAI;MAAA,IAAAC,cAAA,EAAAC,eAAA;MAAA,OAC1BJ,GAAG,CAACK,IAAI,CAACH,IAAI,CAAClK,IAAI,CAAC;MAAkB;MACrCgK,GAAG,CAACK,IAAI,CAACH,IAAI,CAACI,KAAK,CAAC;MAAiB;MACrCN,GAAG,CAACK,IAAI,CAACH,IAAI,CAACvG,IAAI,CAAC;MAAkB;MACrCqG,GAAG,CAACK,IAAI,CAACV,UAAU,CAACO,IAAI,CAACvG,IAAI,CAAC,CAAC;MAAM;MACpC,EAAAwG,cAAA,GAAAD,IAAI,CAACK,QAAQ,cAAAJ,cAAA,uBAAbA,cAAA,CAAeK,EAAE,KAAIR,GAAG,CAACK,IAAI,EAAAD,eAAA,GAACF,IAAI,CAACK,QAAQ,cAAAH,eAAA,uBAAbA,eAAA,CAAeI,EAAE,CAAE;IAAA,EAAC;IACvD,CAAC;EACL,CAAC,EAAE,CAAClG,MAAM,EAAEJ,WAAW,CAAC,CAAC;;EAEzB;EACA,MAAMuG,iBAAiB,GAAGnO,OAAO,CAAC,MAAM;IACpC,OAAOwN,gBAAgB,CAACY,KAAK,CAAC,CAAChF,IAAI,GAAG,CAAC,IAAIE,KAAK,EAAEF,IAAI,GAAGE,KAAK,CAAC;EACnE,CAAC,EAAE,CAACkE,gBAAgB,EAAEpE,IAAI,EAAEE,KAAK,CAAC,CAAC;EAEnC,MAAM+E,SAAS,GAAGrO,OAAO,CAAC,MAAM;IAC5B,OAAOmO,iBAAiB,CAAC/M,GAAG,CAAC,CAAC;MAAE+F,GAAG;MAAEmH,UAAU;MAAE5K,IAAI;MAAEsK,KAAK;MAAE3G,IAAI;MAAE4G,QAAQ;MAAEM,cAAc;MAAEC,SAAS;MAAE1B;IAAQ,CAAC,MAAM;MACpH9H,GAAG,EAAEmC,GAAG;MACRsH,IAAI,EAAEH,UAAU;MAChB5K,IAAI,EAAEA,IAAI;MACVsK,KAAK,EAAEA,KAAK;MACZ3G,IAAI,EAAEA,IAAI;MACVqH,UAAU,EAAE,CAAAT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,EAAE,KAAI,KAAK;MAAE;MACnCK,cAAc,eACVpO,OAAA;QAAM4B,KAAK,EAAE;UACT4M,KAAK,EAAE,CAAAJ,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEE,IAAI,MAAK,GAAG,GAAG,KAAK,GACrCF,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEK,SAAS,GAAG,OAAO,GAC/B,MAAM;UAChBC,UAAU,EAAE;QAChB,CAAE;QAAA9N,QAAA,EACG,CAAAwN,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEE,IAAI,MAAK,GAAG,GAAG,cAAc,GACxCF,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEK,SAAS,GAAG,WAAW,GACnC;MAAc;QAAA5N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CACT;MACDqN,SAAS,EAAE3P,MAAM,CAAC2P,SAAS,CAAC,CAAC/M,MAAM,CAAC,YAAY,CAAC;MACjDqN,UAAU,eACN3O,OAAA,CAAAE,SAAA;QAAAU,QAAA,EACK+L,OAAO,CAACxC,MAAM,GAAG,CAAC,gBACfnK,OAAA;UAAGU,SAAS,EAAC,iBAAiB;UAACyB,OAAO,EAAEA,CAAA,KAAMuK,iBAAiB,CAACC,OAAO,CAAE;UAAA/L,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEzFhB,OAAA;UAAAY,QAAA,EAAM;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC,gBAE7B,CACL;MACD4N,OAAO,eACH5O,OAAA,CAAAE,SAAA;QAAAU,QAAA,gBAEIZ,OAAA,CAACwE,aAAa;UAAA5D,QAAA,gBACVZ,OAAA;YACIU,SAAS,EAAC,YAAY;YACtByB,OAAO,EAAE2L,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,EAAE,GAAG,MAAMxC,YAAY,CAACvE,GAAG,CAAC,GAAG6H,SAAU;YAC5DjN,KAAK,EAAE;cACHkN,UAAU,EAAE,KAAK;cACjBC,eAAe,EAAEjB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,EAAE,GAAG,KAAK,GAAG,MAAM;cAC9CiB,MAAM,EAAElB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,EAAE,GAAG,SAAS,GAAG,aAAa;cAChDkB,QAAQ,EAAE;YACd,CAAE;YAAArO,QAAA,eAEFZ,OAAA,CAACR,OAAO,CAACwE,QAAQ;cAACpC,KAAK,EAAE;gBAAE4M,KAAK,EAAE;cAAQ;YAAE;cAAA3N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACPhB,OAAA,CAACsE,OAAO;YAAA1D,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAGhBhB,OAAA,CAACwE,aAAa;UAAA5D,QAAA,gBACVZ,OAAA;YACIU,SAAS,EAAC,YAAY;YACtByB,OAAO,EAAE,EAAC2L,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,EAAE,IAAG,MAAM/B,oBAAoB,CAAChF,GAAG,CAAC,GAAG6H,SAAU;YACrEjN,KAAK,EAAE;cACHkN,UAAU,EAAE,KAAK;cACjBC,eAAe,EAAE,EAACjB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,EAAE,IAAG,mBAAmB,GAAG,MAAM;cAC7DiB,MAAM,EAAE,EAAClB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,EAAE,IAAG,SAAS,GAAG,aAAa;cACjDkB,QAAQ,EAAE;YACd,CAAE;YAAArO,QAAA,eAEFZ,OAAA,CAACL,OAAO,CAACuP,MAAM;cAACtN,KAAK,EAAE;gBAAE4M,KAAK,EAAE;cAAQ;YAAE;cAAA3N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACPhB,OAAA,CAACsE,OAAO;YAAA1D,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAGhBhB,OAAA,CAACwE,aAAa;UAAA5D,QAAA,gBACVZ,OAAA;YACIU,SAAS,EAAC,8BAA8B;YACxCyB,OAAO,EAAEA,CAAA,KAAMyK,kBAAkB,CAAC;cAAE5F,GAAG;cAAEzD,IAAI;cAAEsK,KAAK;cAAE3G;YAAK,CAAC,CAAE;YAC9DtF,KAAK,EAAE;cACHkN,UAAU,EAAE,KAAK;cACjBG,QAAQ,EAAE;YACd,CAAE;YAAArO,QAAA,eAEFZ,OAAA,CAACL,OAAO,CAACwP,MAAM;cAACvN,KAAK,EAAE;gBAAE4M,KAAK,EAAE;cAAQ;YAAE;cAAA3N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACPhB,OAAA,CAACsE,OAAO;YAAA1D,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA,eAClB;IAEV,CAAC,CAAC,CAAC;EACP,CAAC,EAAE,CAACgN,iBAAiB,CAAC,CAAC;EAEvB,oBAEIhO,OAAA,CAAAE,SAAA;IAAAU,QAAA,eACIZ,OAAA,CAACf,cAAc;MAACmQ,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAxO,QAAA,gBAErCZ,OAAA,CAACZ,aAAa;QACVuF,KAAK,EAAC,uBAAoB;QAC1B0K,IAAI,EAAEpE,WAAY;QAClBqE,OAAO,EAAEpE,cAAe;QACxB5I,SAAS,EAAEmJ,SAAU;QAAA7K,QAAA,EACxB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhBhB,OAAA,CAACb,mBAAmB;QAACyJ,iBAAiB,EAAEA,iBAAkB;QAACD,kBAAkB,EAAEA,kBAAmB;QAC9FK,UAAU,EAAEA,UAAW;QACvBiB,UAAU,EAAEA,UAAW;QACvBC,aAAa,EAAEA,aAAc;QAC7BnC,iBAAiB,EAAEA,iBAAkB;QACrCE,kBAAkB,EAAEA,kBAAmB;QACvCE,WAAW,EAAEA,WAAY;QACzBE,YAAY,EAAEA,YAAa;QAC3BE,SAAS,EAAEA,SAAU;QACrBgH,MAAM,EAAE/I,IAAI,CAACQ,GAAI;QACjBmH,UAAU,EAAEpH;MAAa;QAAAlG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAEFhB,OAAA,CAACG,aAAa;QACVC,QAAQ,EAAEA,QAAS;QACnBC,MAAM,EAAEiJ,WAAY;QACpBhJ,OAAO,EAAEA,CAAA,KAAMiJ,cAAc,CAAC,KAAK;MAAE;QAAA1I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAEFhB,OAAA,CAACqC,iBAAiB;QACdhC,MAAM,EAAEoJ,qBAAsB;QAC9BnJ,OAAO,EAAE2M,uBAAwB;QACjC3K,SAAS,EAAEuK,yBAA0B;QACrCtK,WAAW,EAAEoH,eAAgB;QAC7BnH,SAAS,EAAEqH;MAAoB;QAAAhJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAEFhB,OAAA,CAACkE,KAAK;QAACE,OAAO,EAAEA,OAAQ;QAAAxD,QAAA,GACnBiI,OAAO,gBAAG7I,OAAA;UAAKU,SAAS,EAAC,SAAS;UAAAE,QAAA,eAACZ,OAAA,CAACP,OAAO,CAACsE,yBAAyB;YAACnC,KAAK,EAAE;cAAE4N,QAAQ,EAAE,OAAO;cAAEhB,KAAK,EAAE;YAAmB;UAAE;YAAA3N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GAAG,IAAI,eAC/IhB,OAAA;UAAKU,SAAS,EAAC,WAAW;UAAAE,QAAA,gBACtBZ,OAAA;YAAKU,SAAS,EAAC,sBAAsB;YAAAE,QAAA,eACjCZ,OAAA;cAAKU,SAAS,EAAC,OAAO;cAAAE,QAAA,eAClBZ,OAAA;gBAAAY,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhB,OAAA;YAAKU,SAAS,EAAC,yBAAyB;YAACkB,KAAK,EAAE;cAAE6N,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;YAAoB,CAAE;YAAA9O,QAAA,gBAEpHZ,OAAA;cAAKU,SAAS,EAAC,gBAAgB;cAACkB,KAAK,EAAE;gBAAE+N,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAAhP,QAAA,gBACxEZ,OAAA;gBAAOU,SAAS,EAAC,aAAa;gBAACmD,WAAW,EAAC,WAAW;gBAACD,QAAQ,EAAE6I;cAAa;gBAAA5L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjFhB,OAAA;gBAAGU,SAAS,EAAC,MAAM;gBAAAE,QAAA,eAACZ,OAAA,CAACR,OAAO,CAACqQ,QAAQ;kBAAAhP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eAENhB,OAAA;cAAKU,SAAS,EAAC,qCAAqC;cAAAE,QAAA,eAEhDZ,OAAA;gBAAKU,SAAS,EAAC,gBAAgB;gBAACkB,KAAK,EAAE;kBAAEkO,SAAS,EAAE,OAAO;kBAAEC,YAAY,EAAE;gBAAO,CAAE;gBAAAnP,QAAA,eAChFZ,OAAA;kBAAQU,SAAS,EAAC,gBAAgB;kBAACyB,OAAO,EAAEoK,aAAc;kBAAA3L,QAAA,gBACtDZ,OAAA,CAACN,OAAO,CAACsQ,SAAS;oBAACpO,KAAK,EAAE;sBAAE4M,KAAK,EAAE,SAAS;sBAAEyB,WAAW,EAAE,KAAK;sBAAET,QAAQ,EAAE,MAAM;sBAAEO,YAAY,EAAE;oBAAM;kBAAE;oBAAAlP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAAAhB,OAAA;oBAAAY,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIL,CAAC,eAENhB,OAAA,CAACF,KAAK;YACF4E,OAAO,EAAEA,OAAQ;YACjBwL,UAAU,EAAEhC,SAAU;YACtBiC,UAAU,EAAE;cACRC,OAAO,EAAEnH,IAAI;cACboH,QAAQ,EAAElH,KAAK;cACfC,KAAK,EAAEiE,gBAAgB,CAAClD,MAAM;cAAE;cAChCvG,QAAQ,EAAG0M,OAAO,IAAKpH,OAAO,CAACoH,OAAO,CAAC;cACvCC,eAAe,EAAE;YACrB,CAAE;YACFC,YAAY,EAAC,WAAW;YACxBC,YAAY,EAAE;UAAK;YAAA5P,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACFhB,OAAA;YAAAY,QAAA,GAAK,4BAA0B,EAACwI,KAAK;UAAA;YAAAvI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEI;EAAC,gBACnB,CAAC;AAIX,CAAC;AAACuF,GAAA,CAngBIF,YAAY;EAAA,QAMGnH,WAAW;AAAA;AAAAwR,GAAA,GAN1BrK,YAAY;AAqgBlB,eAAeA,YAAY;AAAC,IAAAjE,EAAA,EAAA6B,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAiM,GAAA;AAAAC,YAAA,CAAAvO,EAAA;AAAAuO,YAAA,CAAA1M,GAAA;AAAA0M,YAAA,CAAAtM,GAAA;AAAAsM,YAAA,CAAApM,GAAA;AAAAoM,YAAA,CAAAlM,GAAA;AAAAkM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}