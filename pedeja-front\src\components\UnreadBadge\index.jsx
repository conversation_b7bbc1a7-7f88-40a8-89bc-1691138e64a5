import React from 'react';
import styled from 'styled-components';

const BadgeContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const Badge = styled.div`
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #25d366;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  
  /* Para números maiores que 99, mostrar 99+ */
  ${props => props.count > 99 && `
    font-size: 10px;
    min-width: 24px;
    height: 20px;
  `}
  
  @media (max-width: 768px) {
    top: -6px;
    right: -6px;
    min-width: 18px;
    height: 18px;
    font-size: 11px;
    
    ${props => props.count > 99 && `
      font-size: 9px;
      min-width: 22px;
      height: 18px;
    `}
  }
`;

const UnreadBadge = ({ count, children }) => {
  if (!count || count === 0) {
    return children;
  }

  const displayCount = count > 99 ? '99+' : count.toString();

  return (
    <BadgeContainer>
      {children}
      <Badge count={count}>
        {displayCount}
      </Badge>
    </BadgeContainer>
  );
};

export default UnreadBadge;
