{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\components\\\\ModalAddCustomResponse\\\\index.jsx\";\nimport React from 'react';\nimport { Modal } from '../Modal';\nimport { Formik, Form, Field, ErrorMessage } from 'formik';\nimport * as Yup from 'yup';\nimport './styles.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModalAddCustomResponse = ({\n  isOpen,\n  onClose,\n  onSubmit\n}) => {\n  const validationSchema = Yup.object({\n    question: Yup.string().required('Pergunta é obrigatória').min(3, 'Pergunta deve ter pelo menos 3 caracteres').max(100, 'Pergunta deve ter no máximo 100 caracteres'),\n    response: Yup.string().required('Resposta é obrigatória').min(10, 'Resposta deve ter pelo menos 10 caracteres').max(300, 'Resposta deve ter no máximo 300 caracteres')\n  });\n  const PREFIX_TEXT = \"Quando perguntarem sobre: \";\n  const initialValues = {\n    question: '',\n    response: ''\n  };\n  const handleSubmit = async (values, {\n    setSubmitting,\n    resetForm\n  }) => {\n    try {\n      // Adicionar o prefixo antes de enviar\n      const finalValues = {\n        ...values,\n        question: PREFIX_TEXT + values.question\n      };\n      await onSubmit(finalValues, {\n        setSubmitting,\n        resetForm\n      });\n    } catch (error) {\n      setSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: isOpen,\n    onClose: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-custom-response\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Nova resposta personalizada\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Personalize as respostas do seu agente virtual para que ele possa responder perguntas das categorias abaixo baseado no exemplo fornecido.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Formik, {\n        initialValues: initialValues,\n        validationSchema: validationSchema,\n        onSubmit: handleSubmit,\n        enableReinitialize: true,\n        children: ({\n          isSubmitting,\n          values\n        }) => /*#__PURE__*/_jsxDEV(Form, {\n          className: \"modal-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"question\",\n              children: \"Quando usar essa resposta\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Field, {\n              as: \"textarea\",\n              id: \"question\",\n              name: \"question\",\n              placeholder: \"Quando o cliente perguntar sobre promo\\xE7\\xF5es\",\n              className: \"form-textarea question-field\",\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              name: \"question\",\n              component: \"div\",\n              className: \"error-message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"response\",\n              children: \"Instru\\xE7\\xF5es para a Resposta\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Field, {\n              as: \"textarea\",\n              id: \"response\",\n              name: \"response\",\n              placeholder: \"As promo\\xE7\\xF5es s\\xE3o:\\nCombinado para 1 pessoa por 59,90\\nCombinado para 2 pessoas por 109,90\",\n              className: \"form-textarea response-field\",\n              rows: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(ErrorMessage, {\n              name: \"response\",\n              component: \"div\",\n              className: \"error-message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"char-counter\",\n              children: [values.response.length, \"/300\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn-cancel\",\n              onClick: onClose,\n              disabled: isSubmitting,\n              children: \"Cancelar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn-submit\",\n              disabled: isSubmitting,\n              children: isSubmitting ? 'Adicionando...' : 'Adicionar'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 9\n  }, this);\n};\n_c = ModalAddCustomResponse;\nexport default ModalAddCustomResponse;\nvar _c;\n$RefreshReg$(_c, \"ModalAddCustomResponse\");", "map": {"version": 3, "names": ["React", "Modal", "<PERSON><PERSON>", "Form", "Field", "ErrorMessage", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ModalAddCustomResponse", "isOpen", "onClose", "onSubmit", "validationSchema", "object", "question", "string", "required", "min", "max", "response", "PREFIX_TEXT", "initialValues", "handleSubmit", "values", "setSubmitting", "resetForm", "finalValues", "error", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "enableReinitialize", "isSubmitting", "htmlFor", "as", "id", "name", "placeholder", "rows", "component", "length", "type", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/components/ModalAddCustomResponse/index.jsx"], "sourcesContent": ["import React from 'react';\nimport { Modal } from '../Modal';\nimport { Formik, Form, Field, ErrorMessage } from 'formik';\nimport * as Yup from 'yup';\nimport './styles.css';\n\nconst ModalAddCustomResponse = ({ isOpen, onClose, onSubmit }) => {\n    const validationSchema = Yup.object({\n        question: Yup.string()\n            .required('Pergunta é obrigatória')\n            .min(3, 'Pergunta deve ter pelo menos 3 caracteres')\n            .max(100, 'Pergunta deve ter no máximo 100 caracteres'),\n        response: Yup.string()\n            .required('Resposta é obrigatória')\n            .min(10, 'Resposta deve ter pelo menos 10 caracteres')\n            .max(300, 'Resposta deve ter no máximo 300 caracteres')\n    });\n\n    const PREFIX_TEXT = \"Quando perguntarem sobre: \";\n\n    const initialValues = {\n        question: '',\n        response: ''\n    };\n\n    const handleSubmit = async (values, { setSubmitting, resetForm }) => {\n        try {\n            // Adicionar o prefixo antes de enviar\n            const finalValues = {\n                ...values,\n                question: PREFIX_TEXT + values.question\n            };\n            await onSubmit(finalValues, { setSubmitting, resetForm });\n        } catch (error) {\n            setSubmitting(false);\n        }\n    };\n\n    return (\n        <Modal isOpen={isOpen} onClose={onClose}>\n            <div className=\"modal-custom-response\">\n                <div className=\"modal-header\">\n                    <h2>Nova resposta personalizada</h2>\n                    <p>Personalize as respostas do seu agente virtual para que ele possa responder perguntas das categorias abaixo baseado no exemplo fornecido.</p>\n                </div>\n\n                <Formik\n                    initialValues={initialValues}\n                    validationSchema={validationSchema}\n                    onSubmit={handleSubmit}\n                    enableReinitialize\n                >\n                    {({ isSubmitting, values }) => (\n                        <Form className=\"modal-form\">\n                            <div className=\"form-group\">\n                                <label htmlFor=\"question\">Quando usar essa resposta</label>\n                                <Field\n                                    as=\"textarea\"\n                                    id=\"question\"\n                                    name=\"question\"\n                                    placeholder=\"Quando o cliente perguntar sobre promoções\"\n                                    className=\"form-textarea question-field\"\n                                    rows=\"3\"\n                                />\n                                <ErrorMessage name=\"question\" component=\"div\" className=\"error-message\" />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label htmlFor=\"response\">Instruções para a Resposta</label>\n                                <Field\n                                    as=\"textarea\"\n                                    id=\"response\"\n                                    name=\"response\"\n                                    placeholder=\"As promoções são:&#10;Combinado para 1 pessoa por 59,90&#10;Combinado para 2 pessoas por 109,90\"\n                                    className=\"form-textarea response-field\"\n                                    rows=\"6\"\n                                />\n                                <ErrorMessage name=\"response\" component=\"div\" className=\"error-message\" />\n                                <div className=\"char-counter\">\n                                    {values.response.length}/300\n                                </div>\n                            </div>\n\n                            <div className=\"form-actions\">\n                                <button\n                                    type=\"button\"\n                                    className=\"btn-cancel\"\n                                    onClick={onClose}\n                                    disabled={isSubmitting}\n                                >\n                                    Cancelar\n                                </button>\n                                <button\n                                    type=\"submit\"\n                                    className=\"btn-submit\"\n                                    disabled={isSubmitting}\n                                >\n                                    {isSubmitting ? 'Adicionando...' : 'Adicionar'}\n                                </button>\n                            </div>\n                        </Form>\n                    )}\n                </Formik>\n            </div>\n        </Modal>\n    );\n};\n\nexport default ModalAddCustomResponse;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,YAAY,QAAQ,QAAQ;AAC1D,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAC9D,MAAMC,gBAAgB,GAAGP,GAAG,CAACQ,MAAM,CAAC;IAChCC,QAAQ,EAAET,GAAG,CAACU,MAAM,CAAC,CAAC,CACjBC,QAAQ,CAAC,wBAAwB,CAAC,CAClCC,GAAG,CAAC,CAAC,EAAE,2CAA2C,CAAC,CACnDC,GAAG,CAAC,GAAG,EAAE,4CAA4C,CAAC;IAC3DC,QAAQ,EAAEd,GAAG,CAACU,MAAM,CAAC,CAAC,CACjBC,QAAQ,CAAC,wBAAwB,CAAC,CAClCC,GAAG,CAAC,EAAE,EAAE,4CAA4C,CAAC,CACrDC,GAAG,CAAC,GAAG,EAAE,4CAA4C;EAC9D,CAAC,CAAC;EAEF,MAAME,WAAW,GAAG,4BAA4B;EAEhD,MAAMC,aAAa,GAAG;IAClBP,QAAQ,EAAE,EAAE;IACZK,QAAQ,EAAE;EACd,CAAC;EAED,MAAMG,YAAY,GAAG,MAAAA,CAAOC,MAAM,EAAE;IAAEC,aAAa;IAAEC;EAAU,CAAC,KAAK;IACjE,IAAI;MACA;MACA,MAAMC,WAAW,GAAG;QAChB,GAAGH,MAAM;QACTT,QAAQ,EAAEM,WAAW,GAAGG,MAAM,CAACT;MACnC,CAAC;MACD,MAAMH,QAAQ,CAACe,WAAW,EAAE;QAAEF,aAAa;QAAEC;MAAU,CAAC,CAAC;IAC7D,CAAC,CAAC,OAAOE,KAAK,EAAE;MACZH,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;EAED,oBACIjB,OAAA,CAACP,KAAK;IAACS,MAAM,EAAEA,MAAO;IAACC,OAAO,EAAEA,OAAQ;IAAAkB,QAAA,eACpCrB,OAAA;MAAKsB,SAAS,EAAC,uBAAuB;MAAAD,QAAA,gBAClCrB,OAAA;QAAKsB,SAAS,EAAC,cAAc;QAAAD,QAAA,gBACzBrB,OAAA;UAAAqB,QAAA,EAAI;QAA2B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpC1B,OAAA;UAAAqB,QAAA,EAAG;QAAyI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/I,CAAC,eAEN1B,OAAA,CAACN,MAAM;QACHoB,aAAa,EAAEA,aAAc;QAC7BT,gBAAgB,EAAEA,gBAAiB;QACnCD,QAAQ,EAAEW,YAAa;QACvBY,kBAAkB;QAAAN,QAAA,EAEjBA,CAAC;UAAEO,YAAY;UAAEZ;QAAO,CAAC,kBACtBhB,OAAA,CAACL,IAAI;UAAC2B,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACxBrB,OAAA;YAAKsB,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACvBrB,OAAA;cAAO6B,OAAO,EAAC,UAAU;cAAAR,QAAA,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3D1B,OAAA,CAACJ,KAAK;cACFkC,EAAE,EAAC,UAAU;cACbC,EAAE,EAAC,UAAU;cACbC,IAAI,EAAC,UAAU;cACfC,WAAW,EAAC,kDAA4C;cACxDX,SAAS,EAAC,8BAA8B;cACxCY,IAAI,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACF1B,OAAA,CAACH,YAAY;cAACmC,IAAI,EAAC,UAAU;cAACG,SAAS,EAAC,KAAK;cAACb,SAAS,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAEN1B,OAAA;YAAKsB,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACvBrB,OAAA;cAAO6B,OAAO,EAAC,UAAU;cAAAR,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5D1B,OAAA,CAACJ,KAAK;cACFkC,EAAE,EAAC,UAAU;cACbC,EAAE,EAAC,UAAU;cACbC,IAAI,EAAC,UAAU;cACfC,WAAW,EAAC,oGAAiG;cAC7GX,SAAS,EAAC,8BAA8B;cACxCY,IAAI,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACF1B,OAAA,CAACH,YAAY;cAACmC,IAAI,EAAC,UAAU;cAACG,SAAS,EAAC,KAAK;cAACb,SAAS,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1E1B,OAAA;cAAKsB,SAAS,EAAC,cAAc;cAAAD,QAAA,GACxBL,MAAM,CAACJ,QAAQ,CAACwB,MAAM,EAAC,MAC5B;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN1B,OAAA;YAAKsB,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACzBrB,OAAA;cACIqC,IAAI,EAAC,QAAQ;cACbf,SAAS,EAAC,YAAY;cACtBgB,OAAO,EAAEnC,OAAQ;cACjBoC,QAAQ,EAAEX,YAAa;cAAAP,QAAA,EAC1B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1B,OAAA;cACIqC,IAAI,EAAC,QAAQ;cACbf,SAAS,EAAC,YAAY;cACtBiB,QAAQ,EAAEX,YAAa;cAAAP,QAAA,EAEtBO,YAAY,GAAG,gBAAgB,GAAG;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACc,EAAA,GApGIvC,sBAAsB;AAsG5B,eAAeA,sBAAsB;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}