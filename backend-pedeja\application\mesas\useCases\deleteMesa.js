class DeleteMesaUseCase {
    constructor(mesasDAO){
        this.mesasDAO = mesasDAO
    }

    async execute({ mesa_id }) {
        try {
            
            const mesa = await this.mesasDAO.findByIdSimple({ mesa_id });

            if(!mesa){
                return {
                    status: false,
                    message: "Mesa não encontrada"
                }
            }

            console.log('mesa', mesa);

            if(mesa.status === "occupied"){
                return {
                    status: false,
                    message: "Mesa atualmente ocupada, desocupe ela para prossegui com a exclusão."
                }
            }

            await this.mesasDAO.deleteMesa({ mesa_id });

            return {
                status: true,
                message: "Mesa deletada com sucesso."
            }
        } catch(error){
            console.error(error);
            return {
                status: false,
                message: "Error on get mesas"
            }
        }
    }
}

module.exports = DeleteMesaUseCase;