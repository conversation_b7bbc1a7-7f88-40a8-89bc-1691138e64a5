{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\roboConfig\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport './style.css';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport LeftMenu from \"../../components/LeftMenu\";\nimport { Modal } from \"../../components/Modal\";\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\nimport styled from 'styled-components';\nimport { FaEdit, FaTrash, FaPlus, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aR<PERSON>ot, FaCom<PERSON> } from \"react-icons/fa\";\nimport { IoMdClose } from \"react-icons/io\";\nimport { MdQuestionAnswer } from \"react-icons/md\";\nimport { BiMessageDetail } from \"react-icons/bi\";\nimport CryptoJS from 'crypto-js';\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RoboConfigContainer = styled.div`\n    display: flex;\n    margin-left: ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'};\n    height: auto;\n    width: auto;\n    transition: 150ms;\n    background-color: rgb(247,247,247) !important;\n    overflow: initial;\n    z-index: 9;\n\n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\n_c = RoboConfigContainer;\nconst RoboCfg = () => {\n  _s();\n  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\n  const userEncrypted = localStorage.getItem('user');\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  const userParse = JSON.parse(user);\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const empresaObjId = empresaParse._id;\n  const {\n    sidebar\n  } = useContext(SidebarContext);\n  const navigate = useNavigate();\n  const [customResponses, setCustomResponses] = useState([]);\n  const [filteredResponses, setFilteredResponses] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isAddModalOpen, setIsAddModalOpen] = useState(false);\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false);\n  const [editingData, setEditingData] = useState(null);\n\n  // Debug: verificar estado do modal\n  console.log('Modal state:', {\n    isEditModalOpen,\n    editingData\n  });\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(5);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [deleteIndex, setDeleteIndex] = useState(null);\n\n  // Carregar respostas personalizadas\n  useEffect(() => {\n    fetchCustomResponses();\n  }, []);\n\n  // Filtrar respostas baseado na busca\n  useEffect(() => {\n    if (searchTerm) {\n      const filtered = customResponses.filter(response => response.question.toLowerCase().includes(searchTerm.toLowerCase()) || response.response.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredResponses(filtered);\n    } else {\n      setFilteredResponses(customResponses);\n    }\n  }, [searchTerm, customResponses]);\n  const fetchCustomResponses = async () => {\n    try {\n      setLoading(true);\n      const response = await getCustomResponses(empresaObjId);\n      setCustomResponses(response.data.customResponses || []);\n    } catch (error) {\n      console.error(\"Erro ao buscar respostas personalizadas:\", error);\n      toast.error(\"Erro ao carregar respostas personalizadas\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // A nova estrutura já vem como objeto, não precisa mais fazer parsing\n  // Mantendo a função para compatibilidade, mas agora apenas retorna o objeto\n  const parseCustomResponse = responseObj => {\n    if (typeof responseObj === 'object' && responseObj.question && responseObj.response) {\n      return {\n        question: responseObj.question,\n        response: responseObj.response\n      };\n    }\n    return {\n      question: 'Pergunta não encontrada',\n      response: 'Resposta não encontrada'\n    };\n  };\n  const handleAddResponse = async (values, {\n    setSubmitting,\n    resetForm\n  }) => {\n    try {\n      await addCustomResponse(empresaObjId, values.question, values.response);\n      toast.success(\"Resposta personalizada adicionada com sucesso!\");\n      setIsAddModalOpen(false);\n      resetForm();\n      fetchCustomResponses();\n    } catch (error) {\n      console.error(\"Erro ao adicionar resposta:\", error);\n      toast.error(\"Erro ao adicionar resposta personalizada\");\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEditResponse = async (values, {\n    setSubmitting\n  }) => {\n    try {\n      await updateCustomResponse(empresaObjId, editingData.id, values.question, values.response);\n      toast.success(\"Resposta personalizada atualizada com sucesso!\");\n      setIsEditModalOpen(false);\n      setEditingData(null);\n      fetchCustomResponses();\n    } catch (error) {\n      console.error(\"Erro ao atualizar resposta:\", error);\n      toast.error(\"Erro ao atualizar resposta personalizada\");\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleToggleActive = async responseObj => {\n    try {\n      await updateCustomResponse(empresaObjId, responseObj.id, responseObj.question, responseObj.response, !responseObj.active);\n      toast.success(`Resposta ${!responseObj.active ? 'ativada' : 'desativada'} com sucesso!`);\n      fetchCustomResponses();\n    } catch (error) {\n      console.error(\"Erro ao alterar status da resposta:\", error);\n      toast.error(\"Erro ao alterar status da resposta!\");\n    }\n  };\n  const handleDeleteResponse = async () => {\n    if (deleteIndex !== null) {\n      try {\n        await deleteCustomResponse(empresaObjId, deleteIndex);\n        toast.success(\"Resposta personalizada removida com sucesso!\");\n        setShowDeleteConfirm(false);\n        setDeleteIndex(null);\n        fetchCustomResponses();\n        // Ajustar página se necessário\n        const totalPages = Math.ceil((customResponses.length - 1) / itemsPerPage);\n        if (currentPage > totalPages && totalPages > 0) {\n          setCurrentPage(totalPages);\n        }\n      } catch (error) {\n        console.error(\"Erro ao remover resposta:\", error);\n        toast.error(\"Erro ao remover resposta personalizada\");\n      }\n    }\n  };\n  const confirmDelete = responseObj => {\n    setDeleteIndex(responseObj.id);\n    setShowDeleteConfirm(true);\n  };\n  const openEditModal = responseObj => {\n    const parsed = parseCustomResponse(responseObj);\n    setEditingData({\n      ...parsed,\n      id: responseObj.id\n    });\n    setIsEditModalOpen(true);\n  };\n\n  // Paginação\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentItems = filteredResponses.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(filteredResponses.length / itemsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: [/*#__PURE__*/_jsxDEV(RoboConfigContainer, {\n        sidebar: sidebar,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"robo-respostas-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"robo-respostas-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"robo-header-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                children: \"Respostas personalizadas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Configure respostas personalizadas para que o bot responda de acordo com seu restaurante.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"robo-btn-nova-resposta\",\n              onClick: () => setIsAddModalOpen(true),\n              children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 33\n              }, this), \" Nova resposta\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"robo-respostas-search\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"robo-search-input-container\",\n              children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n                className: \"robo-search-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Buscar por pergunta ou resposta...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"robo-search-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"robo-respostas-content\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"robo-loading-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"robo-loading-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Carregando respostas...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 33\n            }, this) : filteredResponses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"robo-empty-state\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"robo-empty-icon\",\n                children: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Nenhuma resposta personalizada encontrada\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: searchTerm ? \"Nenhuma resposta corresponde à sua busca.\" : \"Comece adicionando sua primeira resposta personalizada.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 37\n              }, this), !searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"robo-btn-add-first\",\n                onClick: () => setIsAddModalOpen(true),\n                children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 45\n                }, this), \" Adicionar primeira resposta\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"robo-respostas-list\",\n                children: currentItems.map((responseObj, index) => {\n                  const parsed = parseCustomResponse(responseObj);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `robo-resposta-card ${!responseObj.active ? 'inactive' : ''}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"robo-card-header\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"robo-card-status\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `robo-status-indicator ${responseObj.active ? 'active' : 'inactive'}`,\n                          children: responseObj.active ? /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 254,\n                            columnNumber: 87\n                          }, this) : /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 254,\n                            columnNumber: 101\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 253,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"robo-status-text\",\n                          children: responseObj.active ? 'Ativa' : 'Inativa'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 256,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"robo-card-number\",\n                        children: [\"#\", (currentPage - 1) * itemsPerPage + index + 1]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 260,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"robo-resposta-content\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"robo-resposta-question\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"robo-section-header\",\n                          children: [/*#__PURE__*/_jsxDEV(MdQuestionAnswer, {\n                            className: \"robo-section-icon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 268,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"robo-question-label\",\n                            children: \"Quando usar essa resposta\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 269,\n                            columnNumber: 65\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 267,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"robo-question-text\",\n                          children: parsed.question\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 271,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 266,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"robo-resposta-response\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"robo-section-header\",\n                          children: [/*#__PURE__*/_jsxDEV(BiMessageDetail, {\n                            className: \"robo-section-icon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 276,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"robo-response-label\",\n                            children: \"Instru\\xE7\\xF5es para a Resposta\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 277,\n                            columnNumber: 65\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 275,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"robo-response-text\",\n                          children: parsed.response\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 279,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 274,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"robo-resposta-actions\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: `robo-btn-toggle ${responseObj.active ? 'active' : 'inactive'}`,\n                        onClick: () => handleToggleActive(responseObj),\n                        title: responseObj.active ? 'Desativar' : 'Ativar',\n                        children: responseObj.active ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 289,\n                          columnNumber: 83\n                        }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 289,\n                          columnNumber: 100\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 284,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"robo-btn-edit\",\n                        onClick: () => openEditModal(responseObj),\n                        title: \"Editar\",\n                        children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 296,\n                          columnNumber: 61\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"robo-btn-delete\",\n                        onClick: () => confirmDelete(responseObj),\n                        title: \"Excluir\",\n                        children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 303,\n                          columnNumber: 61\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 298,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 53\n                    }, this)]\n                  }, responseObj.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 49\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 37\n              }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"robo-pagination\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"robo-pagination-btn\",\n                  onClick: () => paginate(currentPage - 1),\n                  disabled: currentPage === 1,\n                  children: \"Anterior\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"robo-pagination-numbers\",\n                  children: Array.from({\n                    length: totalPages\n                  }, (_, i) => i + 1).map(number => /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `robo-pagination-number ${currentPage === number ? 'active' : ''}`,\n                    onClick: () => paginate(number),\n                    children: number\n                  }, number, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 53\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"robo-pagination-btn\",\n                  onClick: () => paginate(currentPage + 1),\n                  disabled: currentPage === totalPages,\n                  children: \"Pr\\xF3xima\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(ModalAddCustomResponse, {\n        isOpen: isAddModalOpen,\n        onClose: () => setIsAddModalOpen(false),\n        onSubmit: handleAddResponse\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(ModalEditCustomResponse, {\n        isOpen: isEditModalOpen,\n        onClose: () => {\n          setIsEditModalOpen(false);\n          setEditingData(null);\n        },\n        onSubmit: handleEditResponse,\n        initialData: editingData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        isOpen: showDeleteConfirm,\n        onClose: () => setShowDeleteConfirm(false),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"robo-delete-confirm-modal\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"robo-delete-confirm-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"robo-delete-icon\",\n              children: \"\\uD83D\\uDDD1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Confirmar exclus\\xE3o\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Tem certeza que deseja excluir esta resposta personalizada? Esta a\\xE7\\xE3o n\\xE3o pode ser desfeita.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"robo-delete-confirm-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"robo-btn-cancel-delete\",\n              onClick: () => {\n                setShowDeleteConfirm(false);\n                setDeleteIndex(null);\n              },\n              children: \"Cancelar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"robo-btn-confirm-delete\",\n              onClick: handleDeleteResponse,\n              children: \"Excluir\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(RoboCfg, \"yCK3dG8YetpMxqe7lH6R3d8CHq0=\", false, function () {\n  return [useNavigate];\n});\n_c2 = RoboCfg;\nexport default RoboCfg;\nvar _c, _c2;\n$RefreshReg$(_c, \"RoboConfigContainer\");\n$RefreshReg$(_c2, \"RoboCfg\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "SidebarContext", "PermissionGate", "LeftMenu", "Modal", "ModalAddCustomResponse", "ModalEditCustomResponse", "styled", "FaEdit", "FaTrash", "FaPlus", "FaSearch", "FaToggleOn", "FaToggleOff", "FaRobot", "FaComments", "IoMdClose", "MdQuestionAnswer", "BiMessageDetail", "CryptoJS", "getCustomResponses", "addCustomResponse", "updateCustomResponse", "deleteCustomResponse", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RoboConfigContainer", "div", "sidebar", "_c", "RoboCfg", "_s", "secret<PERSON>ey", "userEncrypted", "localStorage", "getItem", "user", "AES", "decrypt", "toString", "enc", "Utf8", "userParse", "JSON", "parse", "empresa", "empresaParse", "empresaObjId", "_id", "navigate", "customResponses", "setCustomResponses", "filteredResponses", "setFilteredResponses", "searchTerm", "setSearchTerm", "isAddModalOpen", "setIsAddModalOpen", "isEditModalOpen", "setIsEditModalOpen", "editingData", "setEditingData", "console", "log", "loading", "setLoading", "currentPage", "setCurrentPage", "itemsPerPage", "showDeleteConfirm", "setShowDeleteConfirm", "deleteIndex", "setDeleteIndex", "fetchCustomResponses", "filtered", "filter", "response", "question", "toLowerCase", "includes", "data", "error", "parseCustomResponse", "responseObj", "handleAddResponse", "values", "setSubmitting", "resetForm", "success", "handleEditResponse", "id", "handleToggleActive", "active", "handleDeleteResponse", "totalPages", "Math", "ceil", "length", "confirmDelete", "openEditModal", "parsed", "indexOfLastItem", "indexOfFirstItem", "currentItems", "slice", "paginate", "pageNumber", "children", "permissions", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "map", "index", "title", "disabled", "Array", "from", "_", "i", "number", "isOpen", "onClose", "onSubmit", "initialData", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/roboConfig/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport './style.css';\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\nimport { Modal } from \"../../components/Modal\";\r\nimport ModalAddCustomResponse from \"../../components/ModalAddCustomResponse\";\r\nimport ModalEditCustomResponse from \"../../components/ModalEditCustomResponse\";\r\nimport styled from 'styled-components';\r\nimport { FaEdit, FaTrash, FaPlus, FaSearch, FaToggleOn, FaToggleOff, FaRobot, FaComments } from \"react-icons/fa\";\r\nimport { IoMdClose } from \"react-icons/io\";\r\nimport { MdQuestionAnswer } from \"react-icons/md\";\r\nimport { BiMessageDetail } from \"react-icons/bi\";\r\nimport CryptoJS from 'crypto-js';\r\nimport { getCustomResponses, addCustomResponse, updateCustomResponse, deleteCustomResponse } from \"../../services/api\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst RoboConfigContainer = styled.div`\r\n    display: flex;\r\n    margin-left: ${({ sidebar }) => (sidebar ? '250px' : '100px')};\r\n    height: auto;\r\n    width: auto;\r\n    transition: 150ms;\r\n    background-color: rgb(247,247,247) !important;\r\n    overflow: initial;\r\n    z-index: 9;\r\n\r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\nconst RoboCfg = () => {\r\n    const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\r\n    const userEncrypted = localStorage.getItem('user');\r\n    const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\r\n    const userParse = JSON.parse(user);\r\n    const empresa = localStorage.getItem('empresa');\r\n    const empresaParse = JSON.parse(empresa);\r\n    const empresaObjId = empresaParse._id;\r\n\r\n    const { sidebar } = useContext(SidebarContext);\r\n    const navigate = useNavigate();\r\n\r\n    const [customResponses, setCustomResponses] = useState([]);\r\n    const [filteredResponses, setFilteredResponses] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [isAddModalOpen, setIsAddModalOpen] = useState(false);\r\n    const [isEditModalOpen, setIsEditModalOpen] = useState(false);\r\n    const [editingData, setEditingData] = useState(null);\r\n\r\n    // Debug: verificar estado do modal\r\n    console.log('Modal state:', { isEditModalOpen, editingData });\r\n    const [loading, setLoading] = useState(false);\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [itemsPerPage] = useState(5);\r\n    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\r\n    const [deleteIndex, setDeleteIndex] = useState(null);\r\n\r\n    // Carregar respostas personalizadas\r\n    useEffect(() => {\r\n        fetchCustomResponses();\r\n    }, []);\r\n\r\n    // Filtrar respostas baseado na busca\r\n    useEffect(() => {\r\n        if (searchTerm) {\r\n            const filtered = customResponses.filter(response =>\r\n                response.question.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                response.response.toLowerCase().includes(searchTerm.toLowerCase())\r\n            );\r\n            setFilteredResponses(filtered);\r\n        } else {\r\n            setFilteredResponses(customResponses);\r\n        }\r\n    }, [searchTerm, customResponses]);\r\n\r\n    const fetchCustomResponses = async () => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await getCustomResponses(empresaObjId);\r\n            setCustomResponses(response.data.customResponses || []);\r\n        } catch (error) {\r\n            console.error(\"Erro ao buscar respostas personalizadas:\", error);\r\n            toast.error(\"Erro ao carregar respostas personalizadas\");\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // A nova estrutura já vem como objeto, não precisa mais fazer parsing\r\n    // Mantendo a função para compatibilidade, mas agora apenas retorna o objeto\r\n    const parseCustomResponse = (responseObj) => {\r\n        if (typeof responseObj === 'object' && responseObj.question && responseObj.response) {\r\n            return {\r\n                question: responseObj.question,\r\n                response: responseObj.response\r\n            };\r\n        }\r\n        return {\r\n            question: 'Pergunta não encontrada',\r\n            response: 'Resposta não encontrada'\r\n        };\r\n    };\r\n\r\n    const handleAddResponse = async (values, { setSubmitting, resetForm }) => {\r\n        try {\r\n            await addCustomResponse(empresaObjId, values.question, values.response);\r\n            toast.success(\"Resposta personalizada adicionada com sucesso!\");\r\n            setIsAddModalOpen(false);\r\n            resetForm();\r\n            fetchCustomResponses();\r\n        } catch (error) {\r\n            console.error(\"Erro ao adicionar resposta:\", error);\r\n            toast.error(\"Erro ao adicionar resposta personalizada\");\r\n        } finally {\r\n            setSubmitting(false);\r\n        }\r\n    };\r\n\r\n    const handleEditResponse = async (values, { setSubmitting }) => {\r\n        try {\r\n            await updateCustomResponse(empresaObjId, editingData.id, values.question, values.response);\r\n            toast.success(\"Resposta personalizada atualizada com sucesso!\");\r\n            setIsEditModalOpen(false);\r\n            setEditingData(null);\r\n            fetchCustomResponses();\r\n        } catch (error) {\r\n            console.error(\"Erro ao atualizar resposta:\", error);\r\n            toast.error(\"Erro ao atualizar resposta personalizada\");\r\n        } finally {\r\n            setSubmitting(false);\r\n        }\r\n    };\r\n\r\n    const handleToggleActive = async (responseObj) => {\r\n        try {\r\n            await updateCustomResponse(empresaObjId, responseObj.id, responseObj.question, responseObj.response, !responseObj.active);\r\n            toast.success(`Resposta ${!responseObj.active ? 'ativada' : 'desativada'} com sucesso!`);\r\n            fetchCustomResponses();\r\n        } catch (error) {\r\n            console.error(\"Erro ao alterar status da resposta:\", error);\r\n            toast.error(\"Erro ao alterar status da resposta!\");\r\n        }\r\n    };\r\n\r\n    const handleDeleteResponse = async () => {\r\n        if (deleteIndex !== null) {\r\n            try {\r\n                await deleteCustomResponse(empresaObjId, deleteIndex);\r\n                toast.success(\"Resposta personalizada removida com sucesso!\");\r\n                setShowDeleteConfirm(false);\r\n                setDeleteIndex(null);\r\n                fetchCustomResponses();\r\n                // Ajustar página se necessário\r\n                const totalPages = Math.ceil((customResponses.length - 1) / itemsPerPage);\r\n                if (currentPage > totalPages && totalPages > 0) {\r\n                    setCurrentPage(totalPages);\r\n                }\r\n            } catch (error) {\r\n                console.error(\"Erro ao remover resposta:\", error);\r\n                toast.error(\"Erro ao remover resposta personalizada\");\r\n            }\r\n        }\r\n    };\r\n\r\n    const confirmDelete = (responseObj) => {\r\n        setDeleteIndex(responseObj.id);\r\n        setShowDeleteConfirm(true);\r\n    };\r\n\r\n    const openEditModal = (responseObj) => {\r\n        const parsed = parseCustomResponse(responseObj);\r\n        setEditingData({ ...parsed, id: responseObj.id });\r\n        setIsEditModalOpen(true);\r\n    };\r\n\r\n    // Paginação\r\n    const indexOfLastItem = currentPage * itemsPerPage;\r\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\r\n    const currentItems = filteredResponses.slice(indexOfFirstItem, indexOfLastItem);\r\n    const totalPages = Math.ceil(filteredResponses.length / itemsPerPage);\r\n\r\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n    return (\r\n        <>\r\n            <PermissionGate permissions={['default']}>\r\n\r\n                <RoboConfigContainer sidebar={sidebar}>\r\n                    <div className=\"robo-respostas-container\">\r\n                        <div className=\"robo-respostas-header\">\r\n                            <div className=\"robo-header-content\">\r\n                                <h1>Respostas personalizadas</h1>\r\n                                <p>Configure respostas personalizadas para que o bot responda de acordo com seu restaurante.</p>\r\n                            </div>\r\n                            <button\r\n                                className=\"robo-btn-nova-resposta\"\r\n                                onClick={() => setIsAddModalOpen(true)}\r\n                            >\r\n                                <FaPlus /> Nova resposta\r\n                            </button>\r\n                        </div>\r\n\r\n                        <div className=\"robo-respostas-search\">\r\n                            <div className=\"robo-search-input-container\">\r\n                                <FaSearch className=\"robo-search-icon\" />\r\n                                <input\r\n                                    type=\"text\"\r\n                                    placeholder=\"Buscar por pergunta ou resposta...\"\r\n                                    value={searchTerm}\r\n                                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                                    className=\"robo-search-input\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"robo-respostas-content\">\r\n                            {loading ? (\r\n                                <div className=\"robo-loading-container\">\r\n                                    <div className=\"robo-loading-spinner\"></div>\r\n                                    <p>Carregando respostas...</p>\r\n                                </div>\r\n                            ) : filteredResponses.length === 0 ? (\r\n                                <div className=\"robo-empty-state\">\r\n                                    <div className=\"robo-empty-icon\">💬</div>\r\n                                    <h3>Nenhuma resposta personalizada encontrada</h3>\r\n                                    <p>\r\n                                        {searchTerm\r\n                                            ? \"Nenhuma resposta corresponde à sua busca.\"\r\n                                            : \"Comece adicionando sua primeira resposta personalizada.\"\r\n                                        }\r\n                                    </p>\r\n                                    {!searchTerm && (\r\n                                        <button\r\n                                            className=\"robo-btn-add-first\"\r\n                                            onClick={() => setIsAddModalOpen(true)}\r\n                                        >\r\n                                            <FaPlus /> Adicionar primeira resposta\r\n                                        </button>\r\n                                    )}\r\n                                </div>\r\n                            ) : (\r\n                                <>\r\n                                    <div className=\"robo-respostas-list\">\r\n                                        {currentItems.map((responseObj, index) => {\r\n                                            const parsed = parseCustomResponse(responseObj);\r\n                                            return (\r\n                                                <div key={responseObj.id} className={`robo-resposta-card ${!responseObj.active ? 'inactive' : ''}`}>\r\n                                                    <div className=\"robo-card-header\">\r\n                                                        <div className=\"robo-card-status\">\r\n                                                            <div className={`robo-status-indicator ${responseObj.active ? 'active' : 'inactive'}`}>\r\n                                                                {responseObj.active ? <FaRobot /> : <FaRobot />}\r\n                                                            </div>\r\n                                                            <span className=\"robo-status-text\">\r\n                                                                {responseObj.active ? 'Ativa' : 'Inativa'}\r\n                                                            </span>\r\n                                                        </div>\r\n                                                        <div className=\"robo-card-number\">\r\n                                                            #{(currentPage - 1) * itemsPerPage + index + 1}\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    <div className=\"robo-resposta-content\">\r\n                                                        <div className=\"robo-resposta-question\">\r\n                                                            <div className=\"robo-section-header\">\r\n                                                                <MdQuestionAnswer className=\"robo-section-icon\" />\r\n                                                                <span className=\"robo-question-label\">Quando usar essa resposta</span>\r\n                                                            </div>\r\n                                                            <p className=\"robo-question-text\">{parsed.question}</p>\r\n                                                        </div>\r\n\r\n                                                        <div className=\"robo-resposta-response\">\r\n                                                            <div className=\"robo-section-header\">\r\n                                                                <BiMessageDetail className=\"robo-section-icon\" />\r\n                                                                <span className=\"robo-response-label\">Instruções para a Resposta</span>\r\n                                                            </div>\r\n                                                            <p className=\"robo-response-text\">{parsed.response}</p>\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    <div className=\"robo-resposta-actions\">\r\n                                                        <button\r\n                                                            className={`robo-btn-toggle ${responseObj.active ? 'active' : 'inactive'}`}\r\n                                                            onClick={() => handleToggleActive(responseObj)}\r\n                                                            title={responseObj.active ? 'Desativar' : 'Ativar'}\r\n                                                        >\r\n                                                            {responseObj.active ? <FaToggleOn /> : <FaToggleOff />}\r\n                                                        </button>\r\n                                                        <button\r\n                                                            className=\"robo-btn-edit\"\r\n                                                            onClick={() => openEditModal(responseObj)}\r\n                                                            title=\"Editar\"\r\n                                                        >\r\n                                                            <FaEdit />\r\n                                                        </button>\r\n                                                        <button\r\n                                                            className=\"robo-btn-delete\"\r\n                                                            onClick={() => confirmDelete(responseObj)}\r\n                                                            title=\"Excluir\"\r\n                                                        >\r\n                                                            <FaTrash />\r\n                                                        </button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            );\r\n                                        })}\r\n                                    </div>\r\n\r\n                                    {/* Paginação */}\r\n                                    {totalPages > 1 && (\r\n                                        <div className=\"robo-pagination\">\r\n                                            <button\r\n                                                className=\"robo-pagination-btn\"\r\n                                                onClick={() => paginate(currentPage - 1)}\r\n                                                disabled={currentPage === 1}\r\n                                            >\r\n                                                Anterior\r\n                                            </button>\r\n\r\n                                            <div className=\"robo-pagination-numbers\">\r\n                                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (\r\n                                                    <button\r\n                                                        key={number}\r\n                                                        className={`robo-pagination-number ${currentPage === number ? 'active' : ''}`}\r\n                                                        onClick={() => paginate(number)}\r\n                                                    >\r\n                                                        {number}\r\n                                                    </button>\r\n                                                ))}\r\n                                            </div>\r\n\r\n                                            <button\r\n                                                className=\"robo-pagination-btn\"\r\n                                                onClick={() => paginate(currentPage + 1)}\r\n                                                disabled={currentPage === totalPages}\r\n                                            >\r\n                                                Próxima\r\n                                            </button>\r\n                                        </div>\r\n                                    )}\r\n                                </>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                </RoboConfigContainer>\r\n\r\n                {/* Modais */}\r\n                <ModalAddCustomResponse\r\n                    isOpen={isAddModalOpen}\r\n                    onClose={() => setIsAddModalOpen(false)}\r\n                    onSubmit={handleAddResponse}\r\n                />\r\n\r\n                <ModalEditCustomResponse\r\n                    isOpen={isEditModalOpen}\r\n                    onClose={() => {\r\n                        setIsEditModalOpen(false);\r\n                        setEditingData(null);\r\n                    }}\r\n                    onSubmit={handleEditResponse}\r\n                    initialData={editingData}\r\n                />\r\n\r\n                {/* Modal de confirmação de exclusão */}\r\n                <Modal isOpen={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>\r\n                    <div className=\"robo-delete-confirm-modal\">\r\n                        <div className=\"robo-delete-confirm-header\">\r\n                            <div className=\"robo-delete-icon\">🗑️</div>\r\n                            <h3>Confirmar exclusão</h3>\r\n                        </div>\r\n                        <p>Tem certeza que deseja excluir esta resposta personalizada? Esta ação não pode ser desfeita.</p>\r\n                        <div className=\"robo-delete-confirm-actions\">\r\n                            <button\r\n                                className=\"robo-btn-cancel-delete\"\r\n                                onClick={() => {\r\n                                    setShowDeleteConfirm(false);\r\n                                    setDeleteIndex(null);\r\n                                }}\r\n                            >\r\n                                Cancelar\r\n                            </button>\r\n                            <button\r\n                                className=\"robo-btn-confirm-delete\"\r\n                                onClick={handleDeleteResponse}\r\n                            >\r\n                                Excluir\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </Modal>\r\n            </PermissionGate>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default RoboCfg;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,aAAa;AACpB,SAASC,cAAc,QAAQ,iBAAiB;AAChD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,OAAOC,sBAAsB,MAAM,yCAAyC;AAC5E,OAAOC,uBAAuB,MAAM,0CAA0C;AAC9E,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AAChH,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,QAAQ,MAAM,WAAW;AAChC,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,oBAAoB;AACtH,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,mBAAmB,GAAGtB,MAAM,CAACuB,GAAG;AACtC;AACA,mBAAmB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAbIH,mBAAmB;AAezB,MAAMI,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,SAAS,GAAG,uFAAuF;EACzG,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMC,IAAI,GAAGpB,QAAQ,CAACqB,GAAG,CAACC,OAAO,CAACL,aAAa,EAAED,SAAS,CAAC,CAACO,QAAQ,CAACvB,QAAQ,CAACwB,GAAG,CAACC,IAAI,CAAC;EACvF,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACR,IAAI,CAAC;EAClC,MAAMS,OAAO,GAAGX,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMW,YAAY,GAAGH,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC;EACxC,MAAME,YAAY,GAAGD,YAAY,CAACE,GAAG;EAErC,MAAM;IAAEpB;EAAQ,CAAC,GAAGjC,UAAU,CAACG,cAAc,CAAC;EAC9C,MAAMmD,QAAQ,GAAGpD,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACAoE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;IAAEL,eAAe;IAAEE;EAAY,CAAC,CAAC;EAC7D,MAAM,CAACI,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC0E,YAAY,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC;EAClC,MAAM,CAAC2E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6E,WAAW,EAAEC,cAAc,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACAE,SAAS,CAAC,MAAM;IACZ6E,oBAAoB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7E,SAAS,CAAC,MAAM;IACZ,IAAI0D,UAAU,EAAE;MACZ,MAAMoB,QAAQ,GAAGxB,eAAe,CAACyB,MAAM,CAACC,QAAQ,IAC5CA,QAAQ,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,CAAC,CAAC,CAAC,IAClEF,QAAQ,CAACA,QAAQ,CAACE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,CAAC,CAAC,CACrE,CAAC;MACDzB,oBAAoB,CAACqB,QAAQ,CAAC;IAClC,CAAC,MAAM;MACHrB,oBAAoB,CAACH,eAAe,CAAC;IACzC;EACJ,CAAC,EAAE,CAACI,UAAU,EAAEJ,eAAe,CAAC,CAAC;EAEjC,MAAMuB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACAR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMW,QAAQ,GAAG,MAAM3D,kBAAkB,CAAC8B,YAAY,CAAC;MACvDI,kBAAkB,CAACyB,QAAQ,CAACI,IAAI,CAAC9B,eAAe,IAAI,EAAE,CAAC;IAC3D,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACZnB,OAAO,CAACmB,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE5D,KAAK,CAAC4D,KAAK,CAAC,2CAA2C,CAAC;IAC5D,CAAC,SAAS;MACNhB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA;EACA,MAAMiB,mBAAmB,GAAIC,WAAW,IAAK;IACzC,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACN,QAAQ,IAAIM,WAAW,CAACP,QAAQ,EAAE;MACjF,OAAO;QACHC,QAAQ,EAAEM,WAAW,CAACN,QAAQ;QAC9BD,QAAQ,EAAEO,WAAW,CAACP;MAC1B,CAAC;IACL;IACA,OAAO;MACHC,QAAQ,EAAE,yBAAyB;MACnCD,QAAQ,EAAE;IACd,CAAC;EACL,CAAC;EAED,MAAMQ,iBAAiB,GAAG,MAAAA,CAAOC,MAAM,EAAE;IAAEC,aAAa;IAAEC;EAAU,CAAC,KAAK;IACtE,IAAI;MACA,MAAMrE,iBAAiB,CAAC6B,YAAY,EAAEsC,MAAM,CAACR,QAAQ,EAAEQ,MAAM,CAACT,QAAQ,CAAC;MACvEvD,KAAK,CAACmE,OAAO,CAAC,gDAAgD,CAAC;MAC/D/B,iBAAiB,CAAC,KAAK,CAAC;MACxB8B,SAAS,CAAC,CAAC;MACXd,oBAAoB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACZnB,OAAO,CAACmB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD5D,KAAK,CAAC4D,KAAK,CAAC,0CAA0C,CAAC;IAC3D,CAAC,SAAS;MACNK,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;EAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAOJ,MAAM,EAAE;IAAEC;EAAc,CAAC,KAAK;IAC5D,IAAI;MACA,MAAMnE,oBAAoB,CAAC4B,YAAY,EAAEa,WAAW,CAAC8B,EAAE,EAAEL,MAAM,CAACR,QAAQ,EAAEQ,MAAM,CAACT,QAAQ,CAAC;MAC1FvD,KAAK,CAACmE,OAAO,CAAC,gDAAgD,CAAC;MAC/D7B,kBAAkB,CAAC,KAAK,CAAC;MACzBE,cAAc,CAAC,IAAI,CAAC;MACpBY,oBAAoB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACZnB,OAAO,CAACmB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD5D,KAAK,CAAC4D,KAAK,CAAC,0CAA0C,CAAC;IAC3D,CAAC,SAAS;MACNK,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAOR,WAAW,IAAK;IAC9C,IAAI;MACA,MAAMhE,oBAAoB,CAAC4B,YAAY,EAAEoC,WAAW,CAACO,EAAE,EAAEP,WAAW,CAACN,QAAQ,EAAEM,WAAW,CAACP,QAAQ,EAAE,CAACO,WAAW,CAACS,MAAM,CAAC;MACzHvE,KAAK,CAACmE,OAAO,CAAC,YAAY,CAACL,WAAW,CAACS,MAAM,GAAG,SAAS,GAAG,YAAY,eAAe,CAAC;MACxFnB,oBAAoB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACZnB,OAAO,CAACmB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D5D,KAAK,CAAC4D,KAAK,CAAC,qCAAqC,CAAC;IACtD;EACJ,CAAC;EAED,MAAMY,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAItB,WAAW,KAAK,IAAI,EAAE;MACtB,IAAI;QACA,MAAMnD,oBAAoB,CAAC2B,YAAY,EAAEwB,WAAW,CAAC;QACrDlD,KAAK,CAACmE,OAAO,CAAC,8CAA8C,CAAC;QAC7DlB,oBAAoB,CAAC,KAAK,CAAC;QAC3BE,cAAc,CAAC,IAAI,CAAC;QACpBC,oBAAoB,CAAC,CAAC;QACtB;QACA,MAAMqB,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC,CAAC9C,eAAe,CAAC+C,MAAM,GAAG,CAAC,IAAI7B,YAAY,CAAC;QACzE,IAAIF,WAAW,GAAG4B,UAAU,IAAIA,UAAU,GAAG,CAAC,EAAE;UAC5C3B,cAAc,CAAC2B,UAAU,CAAC;QAC9B;MACJ,CAAC,CAAC,OAAOb,KAAK,EAAE;QACZnB,OAAO,CAACmB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD5D,KAAK,CAAC4D,KAAK,CAAC,wCAAwC,CAAC;MACzD;IACJ;EACJ,CAAC;EAED,MAAMiB,aAAa,GAAIf,WAAW,IAAK;IACnCX,cAAc,CAACW,WAAW,CAACO,EAAE,CAAC;IAC9BpB,oBAAoB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM6B,aAAa,GAAIhB,WAAW,IAAK;IACnC,MAAMiB,MAAM,GAAGlB,mBAAmB,CAACC,WAAW,CAAC;IAC/CtB,cAAc,CAAC;MAAE,GAAGuC,MAAM;MAAEV,EAAE,EAAEP,WAAW,CAACO;IAAG,CAAC,CAAC;IACjD/B,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM0C,eAAe,GAAGnC,WAAW,GAAGE,YAAY;EAClD,MAAMkC,gBAAgB,GAAGD,eAAe,GAAGjC,YAAY;EACvD,MAAMmC,YAAY,GAAGnD,iBAAiB,CAACoD,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EAC/E,MAAMP,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC5C,iBAAiB,CAAC6C,MAAM,GAAG7B,YAAY,CAAC;EAErE,MAAMqC,QAAQ,GAAIC,UAAU,IAAKvC,cAAc,CAACuC,UAAU,CAAC;EAE3D,oBACInF,OAAA,CAAAE,SAAA;IAAAkF,QAAA,eACIpF,OAAA,CAACxB,cAAc;MAAC6G,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAD,QAAA,gBAErCpF,OAAA,CAACG,mBAAmB;QAACE,OAAO,EAAEA,OAAQ;QAAA+E,QAAA,eAClCpF,OAAA;UAAKsF,SAAS,EAAC,0BAA0B;UAAAF,QAAA,gBACrCpF,OAAA;YAAKsF,SAAS,EAAC,uBAAuB;YAAAF,QAAA,gBAClCpF,OAAA;cAAKsF,SAAS,EAAC,qBAAqB;cAAAF,QAAA,gBAChCpF,OAAA;gBAAAoF,QAAA,EAAI;cAAwB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjC1F,OAAA;gBAAAoF,QAAA,EAAG;cAAyF;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eACN1F,OAAA;cACIsF,SAAS,EAAC,wBAAwB;cAClCK,OAAO,EAAEA,CAAA,KAAMzD,iBAAiB,CAAC,IAAI,CAAE;cAAAkD,QAAA,gBAEvCpF,OAAA,CAAChB,MAAM;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBACd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEN1F,OAAA;YAAKsF,SAAS,EAAC,uBAAuB;YAAAF,QAAA,eAClCpF,OAAA;cAAKsF,SAAS,EAAC,6BAA6B;cAAAF,QAAA,gBACxCpF,OAAA,CAACf,QAAQ;gBAACqG,SAAS,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC1F,OAAA;gBACI4F,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oCAAoC;gBAChDC,KAAK,EAAE/D,UAAW;gBAClBgE,QAAQ,EAAGC,CAAC,IAAKhE,aAAa,CAACgE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CR,SAAS,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN1F,OAAA;YAAKsF,SAAS,EAAC,wBAAwB;YAAAF,QAAA,EAClC3C,OAAO,gBACJzC,OAAA;cAAKsF,SAAS,EAAC,wBAAwB;cAAAF,QAAA,gBACnCpF,OAAA;gBAAKsF,SAAS,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5C1F,OAAA;gBAAAoF,QAAA,EAAG;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,GACN7D,iBAAiB,CAAC6C,MAAM,KAAK,CAAC,gBAC9B1E,OAAA;cAAKsF,SAAS,EAAC,kBAAkB;cAAAF,QAAA,gBAC7BpF,OAAA;gBAAKsF,SAAS,EAAC,iBAAiB;gBAAAF,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzC1F,OAAA;gBAAAoF,QAAA,EAAI;cAAyC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClD1F,OAAA;gBAAAoF,QAAA,EACKrD,UAAU,GACL,2CAA2C,GAC3C;cAAyD;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhE,CAAC,EACH,CAAC3D,UAAU,iBACR/B,OAAA;gBACIsF,SAAS,EAAC,oBAAoB;gBAC9BK,OAAO,EAAEA,CAAA,KAAMzD,iBAAiB,CAAC,IAAI,CAAE;gBAAAkD,QAAA,gBAEvCpF,OAAA,CAAChB,MAAM;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gCACd;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAEN1F,OAAA,CAAAE,SAAA;cAAAkF,QAAA,gBACIpF,OAAA;gBAAKsF,SAAS,EAAC,qBAAqB;gBAAAF,QAAA,EAC/BJ,YAAY,CAACkB,GAAG,CAAC,CAACtC,WAAW,EAAEuC,KAAK,KAAK;kBACtC,MAAMtB,MAAM,GAAGlB,mBAAmB,CAACC,WAAW,CAAC;kBAC/C,oBACI5D,OAAA;oBAA0BsF,SAAS,EAAE,sBAAsB,CAAC1B,WAAW,CAACS,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;oBAAAe,QAAA,gBAC/FpF,OAAA;sBAAKsF,SAAS,EAAC,kBAAkB;sBAAAF,QAAA,gBAC7BpF,OAAA;wBAAKsF,SAAS,EAAC,kBAAkB;wBAAAF,QAAA,gBAC7BpF,OAAA;0BAAKsF,SAAS,EAAE,yBAAyB1B,WAAW,CAACS,MAAM,GAAG,QAAQ,GAAG,UAAU,EAAG;0BAAAe,QAAA,EACjFxB,WAAW,CAACS,MAAM,gBAAGrE,OAAA,CAACZ,OAAO;4BAAAmG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAAG1F,OAAA,CAACZ,OAAO;4BAAAmG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C,CAAC,eACN1F,OAAA;0BAAMsF,SAAS,EAAC,kBAAkB;0BAAAF,QAAA,EAC7BxB,WAAW,CAACS,MAAM,GAAG,OAAO,GAAG;wBAAS;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACN1F,OAAA;wBAAKsF,SAAS,EAAC,kBAAkB;wBAAAF,QAAA,GAAC,GAC7B,EAAC,CAACzC,WAAW,GAAG,CAAC,IAAIE,YAAY,GAAGsD,KAAK,GAAG,CAAC;sBAAA;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eAEN1F,OAAA;sBAAKsF,SAAS,EAAC,uBAAuB;sBAAAF,QAAA,gBAClCpF,OAAA;wBAAKsF,SAAS,EAAC,wBAAwB;wBAAAF,QAAA,gBACnCpF,OAAA;0BAAKsF,SAAS,EAAC,qBAAqB;0BAAAF,QAAA,gBAChCpF,OAAA,CAACT,gBAAgB;4BAAC+F,SAAS,EAAC;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAClD1F,OAAA;4BAAMsF,SAAS,EAAC,qBAAqB;4BAAAF,QAAA,EAAC;0BAAyB;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrE,CAAC,eACN1F,OAAA;0BAAGsF,SAAS,EAAC,oBAAoB;0BAAAF,QAAA,EAAEP,MAAM,CAACvB;wBAAQ;0BAAAiC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,eAEN1F,OAAA;wBAAKsF,SAAS,EAAC,wBAAwB;wBAAAF,QAAA,gBACnCpF,OAAA;0BAAKsF,SAAS,EAAC,qBAAqB;0BAAAF,QAAA,gBAChCpF,OAAA,CAACR,eAAe;4BAAC8F,SAAS,EAAC;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACjD1F,OAAA;4BAAMsF,SAAS,EAAC,qBAAqB;4BAAAF,QAAA,EAAC;0BAA0B;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtE,CAAC,eACN1F,OAAA;0BAAGsF,SAAS,EAAC,oBAAoB;0BAAAF,QAAA,EAAEP,MAAM,CAACxB;wBAAQ;0BAAAkC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eAEN1F,OAAA;sBAAKsF,SAAS,EAAC,uBAAuB;sBAAAF,QAAA,gBAClCpF,OAAA;wBACIsF,SAAS,EAAE,mBAAmB1B,WAAW,CAACS,MAAM,GAAG,QAAQ,GAAG,UAAU,EAAG;wBAC3EsB,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAACR,WAAW,CAAE;wBAC/CwC,KAAK,EAAExC,WAAW,CAACS,MAAM,GAAG,WAAW,GAAG,QAAS;wBAAAe,QAAA,EAElDxB,WAAW,CAACS,MAAM,gBAAGrE,OAAA,CAACd,UAAU;0BAAAqG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAG1F,OAAA,CAACb,WAAW;0BAAAoG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACT1F,OAAA;wBACIsF,SAAS,EAAC,eAAe;wBACzBK,OAAO,EAAEA,CAAA,KAAMf,aAAa,CAAChB,WAAW,CAAE;wBAC1CwC,KAAK,EAAC,QAAQ;wBAAAhB,QAAA,eAEdpF,OAAA,CAAClB,MAAM;0BAAAyG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACT1F,OAAA;wBACIsF,SAAS,EAAC,iBAAiB;wBAC3BK,OAAO,EAAEA,CAAA,KAAMhB,aAAa,CAACf,WAAW,CAAE;wBAC1CwC,KAAK,EAAC,SAAS;wBAAAhB,QAAA,eAEfpF,OAAA,CAACjB,OAAO;0BAAAwG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA,GAvDA9B,WAAW,CAACO,EAAE;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAwDnB,CAAC;gBAEd,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EAGLnB,UAAU,GAAG,CAAC,iBACXvE,OAAA;gBAAKsF,SAAS,EAAC,iBAAiB;gBAAAF,QAAA,gBAC5BpF,OAAA;kBACIsF,SAAS,EAAC,qBAAqB;kBAC/BK,OAAO,EAAEA,CAAA,KAAMT,QAAQ,CAACvC,WAAW,GAAG,CAAC,CAAE;kBACzC0D,QAAQ,EAAE1D,WAAW,KAAK,CAAE;kBAAAyC,QAAA,EAC/B;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAET1F,OAAA;kBAAKsF,SAAS,EAAC,yBAAyB;kBAAAF,QAAA,EACnCkB,KAAK,CAACC,IAAI,CAAC;oBAAE7B,MAAM,EAAEH;kBAAW,CAAC,EAAE,CAACiC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAACP,GAAG,CAACQ,MAAM,iBAC3D1G,OAAA;oBAEIsF,SAAS,EAAE,0BAA0B3C,WAAW,KAAK+D,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;oBAC9Ef,OAAO,EAAEA,CAAA,KAAMT,QAAQ,CAACwB,MAAM,CAAE;oBAAAtB,QAAA,EAE/BsB;kBAAM,GAJFA,MAAM;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKP,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEN1F,OAAA;kBACIsF,SAAS,EAAC,qBAAqB;kBAC/BK,OAAO,EAAEA,CAAA,KAAMT,QAAQ,CAACvC,WAAW,GAAG,CAAC,CAAE;kBACzC0D,QAAQ,EAAE1D,WAAW,KAAK4B,UAAW;kBAAAa,QAAA,EACxC;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACR;YAAA,eACH;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAGtB1F,OAAA,CAACrB,sBAAsB;QACnBgI,MAAM,EAAE1E,cAAe;QACvB2E,OAAO,EAAEA,CAAA,KAAM1E,iBAAiB,CAAC,KAAK,CAAE;QACxC2E,QAAQ,EAAEhD;MAAkB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAEF1F,OAAA,CAACpB,uBAAuB;QACpB+H,MAAM,EAAExE,eAAgB;QACxByE,OAAO,EAAEA,CAAA,KAAM;UACXxE,kBAAkB,CAAC,KAAK,CAAC;UACzBE,cAAc,CAAC,IAAI,CAAC;QACxB,CAAE;QACFuE,QAAQ,EAAE3C,kBAAmB;QAC7B4C,WAAW,EAAEzE;MAAY;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAGF1F,OAAA,CAACtB,KAAK;QAACiI,MAAM,EAAE7D,iBAAkB;QAAC8D,OAAO,EAAEA,CAAA,KAAM7D,oBAAoB,CAAC,KAAK,CAAE;QAAAqC,QAAA,eACzEpF,OAAA;UAAKsF,SAAS,EAAC,2BAA2B;UAAAF,QAAA,gBACtCpF,OAAA;YAAKsF,SAAS,EAAC,4BAA4B;YAAAF,QAAA,gBACvCpF,OAAA;cAAKsF,SAAS,EAAC,kBAAkB;cAAAF,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3C1F,OAAA;cAAAoF,QAAA,EAAI;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACN1F,OAAA;YAAAoF,QAAA,EAAG;UAA4F;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnG1F,OAAA;YAAKsF,SAAS,EAAC,6BAA6B;YAAAF,QAAA,gBACxCpF,OAAA;cACIsF,SAAS,EAAC,wBAAwB;cAClCK,OAAO,EAAEA,CAAA,KAAM;gBACX5C,oBAAoB,CAAC,KAAK,CAAC;gBAC3BE,cAAc,CAAC,IAAI,CAAC;cACxB,CAAE;cAAAmC,QAAA,EACL;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1F,OAAA;cACIsF,SAAS,EAAC,yBAAyB;cACnCK,OAAO,EAAErB,oBAAqB;cAAAc,QAAA,EACjC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC,gBACnB,CAAC;AAEX,CAAC;AAAAlF,EAAA,CA1WKD,OAAO;EAAA,QAUQjC,WAAW;AAAA;AAAAyI,GAAA,GAV1BxG,OAAO;AA4Wb,eAAeA,OAAO;AAAC,IAAAD,EAAA,EAAAyG,GAAA;AAAAC,YAAA,CAAA1G,EAAA;AAAA0G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}