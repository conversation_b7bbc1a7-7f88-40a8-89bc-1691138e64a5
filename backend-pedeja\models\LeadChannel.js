const mongoose = require('mongoose')

const LeadChannelSchema = new mongoose.Schema({
    empresaObjId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Empresa',
        required: true,
    },
    name: String,
    channel: String,
    channel_id: String,
    mobile_number: String,
    whatsapp_remote_jid: String, // Para Evolution API - identificador único do chat
    id_cliente: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Cliente'
    },
    channel_data: {
        whatsapp: {
            bio: String,
            pushName: String,
            contacName: String,
            profile_picture: String,
            profile_picture_updateAt: Date,
            jid: String
        }
    },
    bot_pausado: { type: Boolean, default: false },
    createdAt: Date,
    updatedAt: Date,
    deletedAt: Date
});

// Especificar explicitamente o nome da collection para manter compatibilidade
const LeadChannel = mongoose.model('LeadChannel', LeadChannelSchema, 'lead_channels');

module.exports = LeadChannel