const mongoose = require('mongoose')

const CategoriaSchema = new mongoose.Schema({
empresaObjId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Empresa',
    required: true,
},
id_categoria: Number,
id_empresa: Number,
createdBy: String,

title: String,
order: Number,
disponibilidade: String,
dia_horario_disponibilidade: Array,
only_pdv: Boolean,
only_qrcode: Boolean,
only_delivery_take_local: Boolean, 
import_id: Number,
modelo: {
    type: Object,
    required: false
},
inativo:Boolean,
bloqueado:Boolean,

createdAt: Date,
updatedAt: Date,
deletedAt: Date

})

const Categorias = mongoose.model('Categorias', CategoriaSchema)

module.exports = Categorias