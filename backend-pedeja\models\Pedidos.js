const mongoose = require('mongoose')


const Pedidos = mongoose.model('Pedidos',{
    id_pedido: Number,
    id_pedido_counter: Number,
    id_empresa: Number,
    createdBy: String,
    garcom_name: String,
    status_pedido: String,
    itens:{},
    celular_cliente: String,
    nome_cliente: String,
    counter_qtd_pedido: Number,
    tipo_pagamento: String,
    entrega:{},
    entregador: Object,
    valor_total: Number,
    valor_troco: Number,
    descricao: String,
    desconto: Number,
    cpf_cnpj: String,
    status_print: Boolean,
    cancelado: Boolean,
    lead_id: { type: mongoose.Schema.Types.ObjectId, ref: 'LeadChannel', default: null },
    objIdCliente: { type: mongoose.Schema.Types.ObjectId, ref: 'Cliente', default: null },
    createdAt: Date,
    updatedAt: Date,
    finalizadoAt: Date,
    deletedAt: Date
})

module.exports = Pedidos