const jwt = require('jsonwebtoken')
require('dotenv').config()

const middlewareAuth = async (req, res, next) => {

    const authHeader = req.headers['authorization']
    const token = authHeader && authHeader.split(" ")[1]

    if (!token) {
        return res.status(401).json({ msg: `Acesso negado! ${req.path}` })
    }

    try {
        const secret = process.env.SECRET
        jwt.verify(token, secret)
        const currentUser = jwt.decode(token)
        //console.log('currentUser', currentUser);
        req.context = {
            currentUser,
        };
        next()
    } catch (error) {
        return res.status(400).json({ msg: "Token inválido!" })
    }
}

module.exports = middlewareAuth;