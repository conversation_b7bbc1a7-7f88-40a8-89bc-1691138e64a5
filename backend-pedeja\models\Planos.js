const mongoose = require('mongoose')

const PlanoSchema = new mongoose.Schema({
/*empresaObjId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Empresa',
    required: true,
},*/
createdBy: String,
id_plano: Number,

name:String,
plan_identifier:String,
order:Number,
//interval:Number,
plan_cycle:String,
max_cycles:Number,
valor_plano:Number,
//interval_type:String,
access_type:String,

inativo:Boolean,
bloqueado:Boolean,

createdAt: Date,
updatedAt: Date,
deletedAt: Date

})

const Planos = mongoose.model('Planos', PlanoSchema)

module.exports = Planos