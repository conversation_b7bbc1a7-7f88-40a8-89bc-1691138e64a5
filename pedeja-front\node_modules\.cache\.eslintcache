[{"C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\AppRoutes.jsx": "3", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\facebookPixel.js": "4", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\RouteTracker.jsx": "5", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\contexts\\auth.jsx": "6", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Users\\empresa.jsx": "7", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Users\\addGarcom.jsx": "8", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Users\\adduser.jsx": "9", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Users\\revenda.jsx": "10", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListAdicional\\addItemAdicional.jsx": "11", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListAdicional\\addAdicional.jsx": "12", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListAdicional\\editItemAdicional.jsx": "13", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\carrinho.jsx": "14", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\addCategoria.jsx": "15", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\addItem.jsx": "16", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\editItem.jsx": "17", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\detalheProduto.jsx": "18", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\infoLoja.jsx": "19", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\addEndereco.jsx": "20", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\finalizarPedido.jsx": "21", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\CardapioSalao\\infoLoja.jsx": "22", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\CardapioSalao\\detalheProduto.jsx": "23", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\sync.jsx": "24", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mapa\\addRaioEntrega.jsx": "25", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Endereco\\enderecoEstabelecimento.jsx": "26", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\AddEmpresaCliente\\integracao.jsx": "27", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\AddEmpresaCliente\\endereco.jsx": "28", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\AddEmpresaCliente\\credenciais.jsx": "29", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\MinhaConta\\planos.jsx": "30", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\PlanosAdmin\\addPlanos.jsx": "31", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\PlanosAdmin\\listPlansAdmin.jsx": "32", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\index.jsx": "33", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Relatorios\\geral.jsx": "34", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Relatorios\\desempenho.jsx": "35", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\CardapioSalao\\index.jsx": "36", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\index.jsx": "37", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\index.jsx": "38", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListAdicional\\index.jsx": "39", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\index.jsx": "40", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEntregadores\\addEntregador.jsx": "41", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEntregadores\\index.jsx": "42", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\AddEmpresaCliente\\index.jsx": "43", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\index.jsx": "44", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Login\\index.jsx": "45", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Orcamento\\index.jsx": "46", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\index.jsx": "47", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cliente\\index.jsx": "48", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEmpresa\\index.jsx": "49", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListUsers\\index.jsx": "50", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCliente\\index.jsx": "51", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Home\\index.jsx": "52", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListVendedor\\index.jsx": "53", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\RecuperarSenha\\index.jsx": "54", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListOrcamento\\index.jsx": "55", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\RedefinirSenha\\index.jsx": "56", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\HorarioFuncionamento\\index.jsx": "57", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Impressora\\index.jsx": "58", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\MeusPedidosConfig\\index.jsx": "59", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\roboConfig\\index.jsx": "60", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ImportaCardapio\\index.jsx": "61", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\FormasPagamento\\index.jsx": "62", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListGarcons\\index.jsx": "63", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\LandPage\\index.jsx": "64", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mesas\\index.jsx": "65", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEmpresas\\index.jsx": "66", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Vendedor\\index.jsx": "67", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\CaixaById\\index.jsx": "68", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\CaixaHistory\\index.jsx": "69", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useCheckLicense\\index.jsx": "70", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\LeftMenu\\index.jsx": "71", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\InputMoney.jsx": "72", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\Input.jsx": "73", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\services\\PermissionGate.js": "74", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\services\\api.js": "75", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\ModalAddGroupSabores.jsx": "76", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\ModalAddGroupAdicionais.jsx": "77", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\ToastWithLink.jsx": "78", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\CardapioSalao\\ToastWithLink.jsx": "79", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Endereco\\index.jsx": "80", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mapa\\InputMoney.jsx": "81", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\px2vw.js": "82", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mapa\\index.jsx": "83", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\whatsapp.jsx": "84", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\contexts\\MenuProfileContext.js": "85", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\Categoria.jsx": "86", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\CardapioSalao\\Categoria.jsx": "87", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListAdicional\\ModalEditGrupoAdd.jsx": "88", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\ModalEditGrupoAdd.jsx": "89", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\InputMoney.jsx": "90", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\ModalEditItemAdicional.jsx": "91", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEntregadores\\modalEditEntregador.jsx": "92", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\ModalRegisterItemAdicional.jsx": "93", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalAddObsItemCarrinho\\index.jsx": "94", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalItemAdicionado\\index.jsx": "95", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalConfirmInfoCliente\\index.jsx": "96", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalPaymentCash\\index.jsx": "97", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ConfirmDialog\\index.jsx": "98", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\DatePicker\\index.js": "99", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditUser\\index.jsx": "100", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditCategoria\\index.jsx": "101", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\contexts\\caixaContext.jsx": "102", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\localStorage.js": "103", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalAddObsItem.jsx": "104", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalAdicionais.jsx": "105", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalPagamentos.jsx": "106", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalAddObsPedido.jsx": "107", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalCpfCnpj.jsx": "108", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalEntregaPdv.jsx": "109", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Home\\ModalAddEntregador.jsx": "110", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\ClosedCaixa\\index.jsx": "111", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\CaixaComponent\\index.jsx": "112", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\OpenedCaixa\\index.jsx": "113", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalAddItem\\index.jsx": "114", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditEmpresa\\index.jsx": "115", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditCliente\\index.jsx": "116", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditTempoEntrega\\index.jsx": "117", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalPedido\\index.jsx": "118", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Home\\MobileKanban\\index.jsx": "119", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditVendedor\\index.jsx": "120", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\formatPrice.js": "121", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEmpresas\\modalEditEntregador.jsx": "122", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\convertPrice.js": "123", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\LeftMenu\\SidebarData.jsx": "124", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\PedidosComponent\\index.jsx": "125", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\LeftMenu\\SubMenu.jsx": "126", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\LeftMenu\\AtendimentoModal.jsx": "127", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Buttons\\MesasCardButton\\index.jsx": "128", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\Mesas\\useGetMesas\\index.jsx": "129", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useGetCaixaById\\index.jsx": "130", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Pagination\\index.jsx": "131", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useGetCaixa\\index.jsx": "132", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalAddUserImg\\index.jsx": "133", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalLinkCardapioSalao\\index.jsx": "134", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalLinkCardapio\\index.jsx": "135", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\stylesProfile.js": "136", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\input.styles.jsx": "137", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\ModalAddItemAdicionais.jsx": "138", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mapa\\Input.jsx": "139", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\UserProfile.jsx": "140", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\Input.jsx": "141", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\DatePicker\\CustomInput.jsx": "142", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\styles\\global.js": "143", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\SearchBar\\index.js": "144", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Input\\index.js": "145", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\index.js": "146", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Profile\\index.js": "147", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useGetEmpresa\\index.jsx": "148", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalOpenCaixa\\index.jsx": "149", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Modal\\index.jsx": "150", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalConfirmacaoSenha\\index.jsx": "151", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalPdv\\index.jsx": "152", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Modals\\SuprimentoModal\\index.jsx": "153", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Modals\\CloseCaixaModal\\index.jsx": "154", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Modals\\SangriaModal\\index.jsx": "155", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Pagination\\PaginationItem\\index.jsx": "156", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\stylesUserProfile.js": "157", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mapa\\input.styles.jsx": "158", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\input.styles.jsx": "159", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\SearchBar\\styles.js": "160", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Input\\styles.js": "161", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\styles.js": "162", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Profile\\styles.js": "163", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\index.js": "164", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\index.js": "165", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Form\\CurrencyInput\\index.jsx": "166", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Forms\\SuprimentoForm\\index.jsx": "167", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Forms\\SangriaForm\\index.jsx": "168", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Forms\\CloseCaixaForm\\index.jsx": "169", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\styles.js": "170", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\styles.js": "171", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\Header\\index.js": "172", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\Content\\index.js": "173", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\Menu\\index.js": "174", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\Contacts\\index.js": "175", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\Header\\index.js": "176", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\MessageField\\index.jsx": "177", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\Header\\styles.js": "178", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\Menu\\styles.js": "179", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\Content\\styles.js": "180", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\MessageField\\styles.js": "181", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\Contacts\\styles.js": "182", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\FormasTiposEntrega\\index.jsx": "183", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ConfiguracaoInicial\\index.jsx": "184", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ui\\RegistrationStepper.jsx": "185", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\AdminSolicitacoesCardapio\\index.jsx": "186", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useFilaAtendimento\\index.jsx": "187", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useImagePreloader.jsx": "188", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\UnreadBadge\\index.jsx": "189", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalAddCustomResponse\\index.jsx": "190", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditCustomResponse\\index.jsx": "191", "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\deviceDetection.js": "192"}, {"size": 376, "mtime": 1748131000257, "results": "193", "hashOfConfig": "194"}, {"size": 821, "mtime": 1748131000191, "results": "195", "hashOfConfig": "194"}, {"size": 22391, "mtime": 1753556394521, "results": "196", "hashOfConfig": "194"}, {"size": 3069, "mtime": 1748131000317, "results": "197", "hashOfConfig": "194"}, {"size": 1015, "mtime": 1748131000316, "results": "198", "hashOfConfig": "194"}, {"size": 4473, "mtime": 1748131000234, "results": "199", "hashOfConfig": "194"}, {"size": 17049, "mtime": 1748131000309, "results": "200", "hashOfConfig": "194"}, {"size": 11875, "mtime": 1748131000309, "results": "201", "hashOfConfig": "194"}, {"size": 18576, "mtime": 1748131000309, "results": "202", "hashOfConfig": "194"}, {"size": 18604, "mtime": 1748131000310, "results": "203", "hashOfConfig": "194"}, {"size": 25732, "mtime": 1748131000279, "results": "204", "hashOfConfig": "194"}, {"size": 24914, "mtime": 1748986805291, "results": "205", "hashOfConfig": "194"}, {"size": 28036, "mtime": 1748131000279, "results": "206", "hashOfConfig": "194"}, {"size": 21936, "mtime": 1748131000264, "results": "207", "hashOfConfig": "194"}, {"size": 20198, "mtime": 1748131000282, "results": "208", "hashOfConfig": "194"}, {"size": 39504, "mtime": 1748131000290, "results": "209", "hashOfConfig": "194"}, {"size": 94453, "mtime": 1748131000291, "results": "210", "hashOfConfig": "194"}, {"size": 37919, "mtime": 1748131000264, "results": "211", "hashOfConfig": "194"}, {"size": 34461, "mtime": 1748131000266, "results": "212", "hashOfConfig": "194"}, {"size": 33358, "mtime": 1748131000264, "results": "213", "hashOfConfig": "194"}, {"size": 100787, "mtime": 1748219599769, "results": "214", "hashOfConfig": "194"}, {"size": 34473, "mtime": 1748131000269, "results": "215", "hashOfConfig": "194"}, {"size": 34174, "mtime": 1748131000268, "results": "216", "hashOfConfig": "194"}, {"size": 9234, "mtime": 1748187523231, "results": "217", "hashOfConfig": "194"}, {"size": 38444, "mtime": 1751513309346, "results": "218", "hashOfConfig": "194"}, {"size": 18240, "mtime": 1751676099486, "results": "219", "hashOfConfig": "194"}, {"size": 20510, "mtime": 1748137769486, "results": "220", "hashOfConfig": "194"}, {"size": 29956, "mtime": 1748180488410, "results": "221", "hashOfConfig": "194"}, {"size": 10959, "mtime": 1751492723473, "results": "222", "hashOfConfig": "194"}, {"size": 56110, "mtime": 1750120608994, "results": "223", "hashOfConfig": "194"}, {"size": 20937, "mtime": 1748131000305, "results": "224", "hashOfConfig": "194"}, {"size": 12194, "mtime": 1748131000306, "results": "225", "hashOfConfig": "194"}, {"size": 313, "mtime": 1748189370566, "results": "226", "hashOfConfig": "194"}, {"size": 28189, "mtime": 1748131000308, "results": "227", "hashOfConfig": "194"}, {"size": 21265, "mtime": 1748131000308, "results": "228", "hashOfConfig": "194"}, {"size": 44540, "mtime": 1748217325660, "results": "229", "hashOfConfig": "194"}, {"size": 43655, "mtime": 1748131000292, "results": "230", "hashOfConfig": "194"}, {"size": 55358, "mtime": 1748217325661, "results": "231", "hashOfConfig": "194"}, {"size": 44455, "mtime": 1748131000279, "results": "232", "hashOfConfig": "194"}, {"size": 285523, "mtime": 1748131000283, "results": "233", "hashOfConfig": "194"}, {"size": 11137, "mtime": 1748131000287, "results": "234", "hashOfConfig": "194"}, {"size": 15181, "mtime": 1748131000287, "results": "235", "hashOfConfig": "194"}, {"size": 15073, "mtime": 1751494834683, "results": "236", "hashOfConfig": "194"}, {"size": 797, "mtime": 1748131000263, "results": "237", "hashOfConfig": "194"}, {"size": 9710, "mtime": 1748183664185, "results": "238", "hashOfConfig": "194"}, {"size": 52789, "mtime": 1748131000301, "results": "239", "hashOfConfig": "194"}, {"size": 70453, "mtime": 1748131000304, "results": "240", "hashOfConfig": "194"}, {"size": 22360, "mtime": 1748131000270, "results": "241", "hashOfConfig": "194"}, {"size": 21139, "mtime": 1748131000285, "results": "242", "hashOfConfig": "194"}, {"size": 15814, "mtime": 1748131000294, "results": "243", "hashOfConfig": "194"}, {"size": 20707, "mtime": 1748131000284, "results": "244", "hashOfConfig": "194"}, {"size": 93068, "mtime": 1753554998767, "results": "245", "hashOfConfig": "194"}, {"size": 19596, "mtime": 1748131000295, "results": "246", "hashOfConfig": "194"}, {"size": 5826, "mtime": 1748131000306, "results": "247", "hashOfConfig": "194"}, {"size": 29034, "mtime": 1748131000293, "results": "248", "hashOfConfig": "194"}, {"size": 7303, "mtime": 1748131000307, "results": "249", "hashOfConfig": "194"}, {"size": 22264, "mtime": 1748131000275, "results": "250", "hashOfConfig": "194"}, {"size": 11714, "mtime": 1753554998769, "results": "251", "hashOfConfig": "194"}, {"size": 36697, "mtime": 1748131000299, "results": "252", "hashOfConfig": "194"}, {"size": 20569, "mtime": 1753563211461, "results": "253", "hashOfConfig": "194"}, {"size": 27100, "mtime": 1752865018874, "results": "254", "hashOfConfig": "194"}, {"size": 38500, "mtime": 1748131000272, "results": "255", "hashOfConfig": "194"}, {"size": 15595, "mtime": 1748131000288, "results": "256", "hashOfConfig": "194"}, {"size": 8740, "mtime": 1748131000277, "results": "257", "hashOfConfig": "194"}, {"size": 33072, "mtime": 1748131000298, "results": "258", "hashOfConfig": "194"}, {"size": 33368, "mtime": 1753554998769, "results": "259", "hashOfConfig": "194"}, {"size": 18822, "mtime": 1748131000310, "results": "260", "hashOfConfig": "194"}, {"size": 13269, "mtime": 1748131000260, "results": "261", "hashOfConfig": "194"}, {"size": 9570, "mtime": 1748131000261, "results": "262", "hashOfConfig": "194"}, {"size": 2702, "mtime": 1748131000236, "results": "263", "hashOfConfig": "194"}, {"size": 82395, "mtime": 1753564754621, "results": "264", "hashOfConfig": "194"}, {"size": 1440, "mtime": 1748131000289, "results": "265", "hashOfConfig": "194"}, {"size": 434, "mtime": 1748131000289, "results": "266", "hashOfConfig": "194"}, {"size": 1615, "mtime": 1748131000315, "results": "267", "hashOfConfig": "194"}, {"size": 45590, "mtime": 1753559609222, "results": "268", "hashOfConfig": "194"}, {"size": 31202, "mtime": 1748131000290, "results": "269", "hashOfConfig": "194"}, {"size": 30218, "mtime": 1748131000290, "results": "270", "hashOfConfig": "194"}, {"size": 720, "mtime": 1748131000264, "results": "271", "hashOfConfig": "194"}, {"size": 720, "mtime": 1748131000267, "results": "272", "hashOfConfig": "194"}, {"size": 5285, "mtime": 1751502015215, "results": "273", "hashOfConfig": "194"}, {"size": 1261, "mtime": 1751511260628, "results": "274", "hashOfConfig": "194"}, {"size": 91, "mtime": 1748131000318, "results": "275", "hashOfConfig": "194"}, {"size": 3338, "mtime": 1748131000297, "results": "276", "hashOfConfig": "194"}, {"size": 144779, "mtime": 1753554998773, "results": "277", "hashOfConfig": "194"}, {"size": 445, "mtime": 1748131000234, "results": "278", "hashOfConfig": "194"}, {"size": 3231, "mtime": 1748131000263, "results": "279", "hashOfConfig": "194"}, {"size": 3393, "mtime": 1748131000266, "results": "280", "hashOfConfig": "194"}, {"size": 13722, "mtime": 1748131000277, "results": "281", "hashOfConfig": "194"}, {"size": 13863, "mtime": 1748131000281, "results": "282", "hashOfConfig": "194"}, {"size": 1440, "mtime": 1748131000280, "results": "283", "hashOfConfig": "194"}, {"size": 26594, "mtime": 1748131000281, "results": "284", "hashOfConfig": "194"}, {"size": 11510, "mtime": 1748131000288, "results": "285", "hashOfConfig": "194"}, {"size": 24926, "mtime": 1748131000282, "results": "286", "hashOfConfig": "194"}, {"size": 4274, "mtime": 1748131000220, "results": "287", "hashOfConfig": "194"}, {"size": 8739, "mtime": 1748131000226, "results": "288", "hashOfConfig": "194"}, {"size": 4945, "mtime": 1748131000221, "results": "289", "hashOfConfig": "194"}, {"size": 5440, "mtime": 1748131000228, "results": "290", "hashOfConfig": "194"}, {"size": 1408, "mtime": 1748131000214, "results": "291", "hashOfConfig": "194"}, {"size": 880, "mtime": 1748131000215, "results": "292", "hashOfConfig": "194"}, {"size": 26193, "mtime": 1751418059990, "results": "293", "hashOfConfig": "194"}, {"size": 24078, "mtime": 1748131000223, "results": "294", "hashOfConfig": "194"}, {"size": 3836, "mtime": 1748131000235, "results": "295", "hashOfConfig": "194"}, {"size": 372, "mtime": 1748131000317, "results": "296", "hashOfConfig": "194"}, {"size": 5251, "mtime": 1748131000302, "results": "297", "hashOfConfig": "194"}, {"size": 15121, "mtime": 1748131000303, "results": "298", "hashOfConfig": "194"}, {"size": 33779, "mtime": 1748131000304, "results": "299", "hashOfConfig": "194"}, {"size": 5181, "mtime": 1748131000302, "results": "300", "hashOfConfig": "194"}, {"size": 5422, "mtime": 1748131000303, "results": "301", "hashOfConfig": "194"}, {"size": 50526, "mtime": 1748131000303, "results": "302", "hashOfConfig": "194"}, {"size": 9466, "mtime": 1748131000273, "results": "303", "hashOfConfig": "194"}, {"size": 3549, "mtime": 1748131000262, "results": "304", "hashOfConfig": "194"}, {"size": 386, "mtime": 1748131000261, "results": "305", "hashOfConfig": "194"}, {"size": 12815, "mtime": 1748131000262, "results": "306", "hashOfConfig": "194"}, {"size": 25767, "mtime": 1748131000220, "results": "307", "hashOfConfig": "194"}, {"size": 21243, "mtime": 1748131000224, "results": "308", "hashOfConfig": "194"}, {"size": 20526, "mtime": 1748131000223, "results": "309", "hashOfConfig": "194"}, {"size": 34486, "mtime": 1748131000224, "results": "310", "hashOfConfig": "194"}, {"size": 47502, "mtime": 1748131000229, "results": "311", "hashOfConfig": "194"}, {"size": 40439, "mtime": 1748131000273, "results": "312", "hashOfConfig": "194"}, {"size": 16816, "mtime": 1748131000225, "results": "313", "hashOfConfig": "194"}, {"size": 615, "mtime": 1748131000317, "results": "314", "hashOfConfig": "194"}, {"size": 11510, "mtime": 1748131000286, "results": "315", "hashOfConfig": "194"}, {"size": 197, "mtime": 1748131000317, "results": "316", "hashOfConfig": "194"}, {"size": 28613, "mtime": 1753557631299, "results": "317", "hashOfConfig": "194"}, {"size": 63128, "mtime": 1748131000232, "results": "318", "hashOfConfig": "194"}, {"size": 2704, "mtime": 1748131000217, "results": "319", "hashOfConfig": "194"}, {"size": 12648, "mtime": 1753554998761, "results": "320", "hashOfConfig": "194"}, {"size": 205, "mtime": 1748131000203, "results": "321", "hashOfConfig": "194"}, {"size": 239, "mtime": 1748131000236, "results": "322", "hashOfConfig": "194"}, {"size": 315, "mtime": 1748131000238, "results": "323", "hashOfConfig": "194"}, {"size": 2503, "mtime": 1748131000231, "results": "324", "hashOfConfig": "194"}, {"size": 352, "mtime": 1748131000238, "results": "325", "hashOfConfig": "194"}, {"size": 10156, "mtime": 1748131000221, "results": "326", "hashOfConfig": "194"}, {"size": 4612, "mtime": 1748131000227, "results": "327", "hashOfConfig": "194"}, {"size": 4722, "mtime": 1748131000227, "results": "328", "hashOfConfig": "194"}, {"size": 2339, "mtime": 1748187523231, "results": "329", "hashOfConfig": "194"}, {"size": 229, "mtime": 1748131000292, "results": "330", "hashOfConfig": "194"}, {"size": 23634, "mtime": 1748131000290, "results": "331", "hashOfConfig": "194"}, {"size": 733, "mtime": 1751512924424, "results": "332", "hashOfConfig": "194"}, {"size": 2145, "mtime": 1748131000311, "results": "333", "hashOfConfig": "194"}, {"size": 434, "mtime": 1748131000280, "results": "334", "hashOfConfig": "194"}, {"size": 460, "mtime": 1748131000214, "results": "335", "hashOfConfig": "194"}, {"size": 1264, "mtime": 1751497715005, "results": "336", "hashOfConfig": "194"}, {"size": 948, "mtime": 1748131000233, "results": "337", "hashOfConfig": "194"}, {"size": 195, "mtime": 1748131000216, "results": "338", "hashOfConfig": "194"}, {"size": 243, "mtime": 1748131000213, "results": "339", "hashOfConfig": "194"}, {"size": 1098, "mtime": 1748131000233, "results": "340", "hashOfConfig": "194"}, {"size": 305, "mtime": 1748131000238, "results": "341", "hashOfConfig": "194"}, {"size": 3294, "mtime": 1748131000227, "results": "342", "hashOfConfig": "194"}, {"size": 518, "mtime": 1753558022217, "results": "343", "hashOfConfig": "194"}, {"size": 2821, "mtime": 1748131000222, "results": "344", "hashOfConfig": "194"}, {"size": 73491, "mtime": 1748131000229, "results": "345", "hashOfConfig": "194"}, {"size": 398, "mtime": 1748131000206, "results": "346", "hashOfConfig": "194"}, {"size": 394, "mtime": 1748131000205, "results": "347", "hashOfConfig": "194"}, {"size": 381, "mtime": 1748131000206, "results": "348", "hashOfConfig": "194"}, {"size": 437, "mtime": 1748131000230, "results": "349", "hashOfConfig": "194"}, {"size": 2434, "mtime": 1748187523231, "results": "350", "hashOfConfig": "194"}, {"size": 229, "mtime": 1748131000298, "results": "351", "hashOfConfig": "194"}, {"size": 229, "mtime": 1748131000284, "results": "352", "hashOfConfig": "194"}, {"size": 177, "mtime": 1748131000234, "results": "353", "hashOfConfig": "194"}, {"size": 235, "mtime": 1748131000216, "results": "354", "hashOfConfig": "194"}, {"size": 118, "mtime": 1748131000213, "results": "355", "hashOfConfig": "194"}, {"size": 1173, "mtime": 1748131000233, "results": "356", "hashOfConfig": "194"}, {"size": 366, "mtime": 1748131000210, "results": "357", "hashOfConfig": "194"}, {"size": 408, "mtime": 1748131000212, "results": "358", "hashOfConfig": "194"}, {"size": 1169, "mtime": 1748131000215, "results": "359", "hashOfConfig": "194"}, {"size": 3540, "mtime": 1748131000205, "results": "360", "hashOfConfig": "194"}, {"size": 3499, "mtime": 1748131000204, "results": "361", "hashOfConfig": "194"}, {"size": 11881, "mtime": 1748131000204, "results": "362", "hashOfConfig": "194"}, {"size": 257, "mtime": 1748131000210, "results": "363", "hashOfConfig": "194"}, {"size": 221, "mtime": 1748131000213, "results": "364", "hashOfConfig": "194"}, {"size": 465, "mtime": 1748131000209, "results": "365", "hashOfConfig": "194"}, {"size": 501, "mtime": 1748131000208, "results": "366", "hashOfConfig": "194"}, {"size": 638, "mtime": 1748131000212, "results": "367", "hashOfConfig": "194"}, {"size": 815, "mtime": 1748131000210, "results": "368", "hashOfConfig": "194"}, {"size": 1769, "mtime": 1748131000211, "results": "369", "hashOfConfig": "194"}, {"size": 1597, "mtime": 1748131000209, "results": "370", "hashOfConfig": "194"}, {"size": 352, "mtime": 1748131000209, "results": "371", "hashOfConfig": "194"}, {"size": 732, "mtime": 1748131000212, "results": "372", "hashOfConfig": "194"}, {"size": 907, "mtime": 1748131000208, "results": "373", "hashOfConfig": "194"}, {"size": 199, "mtime": 1748131000210, "results": "374", "hashOfConfig": "194"}, {"size": 782, "mtime": 1748131000211, "results": "375", "hashOfConfig": "194"}, {"size": 11572, "mtime": 1751754287831, "results": "376", "hashOfConfig": "194"}, {"size": 211321, "mtime": 1753554998766, "results": "377", "hashOfConfig": "194"}, {"size": 8554, "mtime": 1751497715005, "results": "378", "hashOfConfig": "194"}, {"size": 20345, "mtime": 1751771237751, "results": "379", "hashOfConfig": "194"}, {"size": 16586, "mtime": 1753554998763, "results": "380", "hashOfConfig": "194"}, {"size": 2592, "mtime": 1753554998764, "results": "381", "hashOfConfig": "194"}, {"size": 1303, "mtime": 1753554998763, "results": "382", "hashOfConfig": "194"}, {"size": 5016, "mtime": 1753563960008, "results": "383", "hashOfConfig": "194"}, {"size": 5899, "mtime": 1753563893507, "results": "384", "hashOfConfig": "194"}, {"size": 4497, "mtime": 1753564713777, "results": "385", "hashOfConfig": "194"}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "bf7hta", {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 38, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 52, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 36, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 36, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 34, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 34, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 51, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\AppRoutes.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\facebookPixel.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\RouteTracker.jsx", ["962"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\contexts\\auth.jsx", ["963", "964", "965", "966"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Users\\empresa.jsx", ["967", "968", "969", "970"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Users\\addGarcom.jsx", ["971", "972", "973", "974", "975", "976", "977", "978", "979", "980"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Users\\adduser.jsx", ["981", "982", "983", "984"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Users\\revenda.jsx", ["985", "986", "987", "988"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListAdicional\\addItemAdicional.jsx", ["989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListAdicional\\addAdicional.jsx", ["1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListAdicional\\editItemAdicional.jsx", ["1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\carrinho.jsx", ["1045", "1046", "1047", "1048", "1049", "1050", "1051"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\addCategoria.jsx", ["1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\addItem.jsx", ["1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\editItem.jsx", ["1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\detalheProduto.jsx", ["1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\infoLoja.jsx", ["1148", "1149", "1150", "1151", "1152", "1153", "1154"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\addEndereco.jsx", ["1155", "1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\finalizarPedido.jsx", ["1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\CardapioSalao\\infoLoja.jsx", ["1216", "1217", "1218", "1219", "1220", "1221", "1222"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\CardapioSalao\\detalheProduto.jsx", ["1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\sync.jsx", ["1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mapa\\addRaioEntrega.jsx", ["1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Endereco\\enderecoEstabelecimento.jsx", ["1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297"], ["1298"], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\AddEmpresaCliente\\integracao.jsx", ["1299", "1300", "1301"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\AddEmpresaCliente\\endereco.jsx", ["1302", "1303", "1304", "1305", "1306", "1307"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\AddEmpresaCliente\\credenciais.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\MinhaConta\\planos.jsx", ["1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\PlanosAdmin\\addPlanos.jsx", ["1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354", "1355", "1356", "1357", "1358", "1359"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\PlanosAdmin\\listPlansAdmin.jsx", ["1360", "1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Relatorios\\geral.jsx", ["1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Relatorios\\desempenho.jsx", ["1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\CardapioSalao\\index.jsx", ["1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\index.jsx", ["1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\index.jsx", ["1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListAdicional\\index.jsx", ["1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\index.jsx", ["1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEntregadores\\addEntregador.jsx", ["1555", "1556", "1557", "1558", "1559"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEntregadores\\index.jsx", ["1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\AddEmpresaCliente\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\index.jsx", ["1577", "1578", "1579", "1580", "1581", "1582"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Login\\index.jsx", ["1583", "1584", "1585", "1586"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Orcamento\\index.jsx", ["1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\index.jsx", ["1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cliente\\index.jsx", ["1625", "1626", "1627", "1628"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEmpresa\\index.jsx", ["1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListUsers\\index.jsx", ["1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCliente\\index.jsx", ["1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Home\\index.jsx", ["1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListVendedor\\index.jsx", ["1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\RecuperarSenha\\index.jsx", ["1747"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListOrcamento\\index.jsx", ["1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\RedefinirSenha\\index.jsx", ["1774"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\HorarioFuncionamento\\index.jsx", ["1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Impressora\\index.jsx", ["1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\MeusPedidosConfig\\index.jsx", ["1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\roboConfig\\index.jsx", ["1825", "1826", "1827", "1828", "1829", "1830"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ImportaCardapio\\index.jsx", ["1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\FormasPagamento\\index.jsx", ["1844", "1845", "1846"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListGarcons\\index.jsx", ["1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\LandPage\\index.jsx", ["1861", "1862", "1863", "1864", "1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mesas\\index.jsx", ["1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEmpresas\\index.jsx", ["1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Vendedor\\index.jsx", ["1903", "1904", "1905", "1906"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\CaixaById\\index.jsx", ["1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914", "1915", "1916"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\CaixaHistory\\index.jsx", ["1917", "1918", "1919", "1920", "1921"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useCheckLicense\\index.jsx", ["1922", "1923"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\LeftMenu\\index.jsx", ["1924", "1925", "1926", "1927", "1928", "1929", "1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939", "1940", "1941", "1942", "1943", "1944", "1945", "1946", "1947", "1948", "1949", "1950", "1951", "1952", "1953", "1954", "1955", "1956"], ["1957"], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\InputMoney.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\Input.jsx", ["1958"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\services\\PermissionGate.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\services\\api.js", ["1959", "1960", "1961"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\ModalAddGroupSabores.jsx", ["1962", "1963", "1964", "1965", "1966", "1967", "1968", "1969", "1970", "1971", "1972", "1973", "1974", "1975", "1976", "1977", "1978", "1979", "1980", "1981", "1982", "1983", "1984", "1985", "1986", "1987", "1988", "1989", "1990", "1991", "1992", "1993", "1994", "1995"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\ModalAddGroupAdicionais.jsx", ["1996", "1997", "1998", "1999", "2000", "2001", "2002", "2003", "2004", "2005", "2006", "2007", "2008", "2009", "2010", "2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018", "2019", "2020", "2021", "2022", "2023", "2024", "2025", "2026", "2027", "2028", "2029"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\ToastWithLink.jsx", ["2030"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\CardapioSalao\\ToastWithLink.jsx", ["2031"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Endereco\\index.jsx", ["2032", "2033", "2034"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mapa\\InputMoney.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\px2vw.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mapa\\index.jsx", ["2035", "2036", "2037"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\whatsapp.jsx", ["2038", "2039", "2040", "2041", "2042", "2043", "2044", "2045", "2046", "2047", "2048", "2049", "2050", "2051", "2052", "2053", "2054", "2055", "2056", "2057", "2058", "2059", "2060", "2061", "2062", "2063", "2064", "2065", "2066"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\contexts\\MenuProfileContext.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Cardapio\\Categoria.jsx", ["2067"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\CardapioSalao\\Categoria.jsx", ["2068"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListAdicional\\ModalEditGrupoAdd.jsx", ["2069", "2070", "2071", "2072", "2073", "2074", "2075", "2076", "2077", "2078", "2079", "2080"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\ModalEditGrupoAdd.jsx", ["2081", "2082", "2083", "2084", "2085", "2086", "2087", "2088", "2089", "2090", "2091", "2092"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\InputMoney.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\ModalEditItemAdicional.jsx", ["2093", "2094", "2095", "2096", "2097", "2098", "2099", "2100", "2101", "2102", "2103", "2104", "2105", "2106", "2107", "2108", "2109", "2110"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEntregadores\\modalEditEntregador.jsx", ["2111", "2112", "2113", "2114", "2115", "2116", "2117", "2118", "2119", "2120"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\ModalRegisterItemAdicional.jsx", ["2121", "2122", "2123", "2124", "2125", "2126", "2127", "2128", "2129", "2130", "2131", "2132", "2133", "2134", "2135", "2136", "2137"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalAddObsItemCarrinho\\index.jsx", ["2138", "2139", "2140", "2141", "2142", "2143", "2144", "2145"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalItemAdicionado\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalConfirmInfoCliente\\index.jsx", ["2146", "2147", "2148", "2149", "2150"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalPaymentCash\\index.jsx", ["2151", "2152", "2153", "2154", "2155", "2156", "2157"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ConfirmDialog\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\DatePicker\\index.js", ["2158"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditUser\\index.jsx", ["2159", "2160", "2161", "2162", "2163", "2164", "2165"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditCategoria\\index.jsx", ["2166", "2167", "2168", "2169", "2170", "2171", "2172", "2173", "2174", "2175", "2176"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\contexts\\caixaContext.jsx", ["2177", "2178", "2179", "2180"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\localStorage.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalAddObsItem.jsx", ["2181", "2182", "2183", "2184", "2185", "2186", "2187", "2188", "2189", "2190", "2191", "2192", "2193", "2194", "2195", "2196", "2197", "2198", "2199", "2200", "2201"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalAdicionais.jsx", ["2202", "2203"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalPagamentos.jsx", ["2204", "2205", "2206", "2207", "2208", "2209", "2210", "2211", "2212", "2213", "2214", "2215", "2216", "2217", "2218", "2219", "2220", "2221", "2222", "2223", "2224", "2225", "2226", "2227", "2228", "2229", "2230", "2231", "2232", "2233", "2234", "2235", "2236", "2237", "2238", "2239", "2240", "2241", "2242", "2243", "2244", "2245", "2246", "2247", "2248", "2249", "2250", "2251", "2252", "2253", "2254"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalAddObsPedido.jsx", ["2255", "2256", "2257", "2258", "2259", "2260", "2261", "2262", "2263", "2264", "2265", "2266", "2267", "2268", "2269", "2270", "2271", "2272", "2273", "2274", "2275"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalCpfCnpj.jsx", ["2276"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Pdv\\ModalEntregaPdv.jsx", ["2277", "2278", "2279", "2280", "2281", "2282", "2283", "2284", "2285", "2286", "2287", "2288", "2289", "2290", "2291", "2292", "2293", "2294", "2295", "2296", "2297", "2298", "2299", "2300", "2301", "2302", "2303", "2304", "2305", "2306", "2307", "2308", "2309", "2310", "2311", "2312", "2313", "2314", "2315", "2316", "2317", "2318", "2319", "2320", "2321", "2322", "2323", "2324", "2325", "2326", "2327", "2328", "2329", "2330", "2331", "2332", "2333"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Home\\ModalAddEntregador.jsx", ["2334", "2335", "2336", "2337", "2338", "2339", "2340", "2341", "2342", "2343", "2344", "2345", "2346", "2347"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\ClosedCaixa\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\CaixaComponent\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Caixa\\OpenedCaixa\\index.jsx", ["2348", "2349"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalAddItem\\index.jsx", ["2350", "2351", "2352", "2353", "2354"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditEmpresa\\index.jsx", ["2355", "2356", "2357", "2358", "2359", "2360", "2361"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditCliente\\index.jsx", ["2362", "2363", "2364", "2365", "2366"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditTempoEntrega\\index.jsx", ["2367", "2368", "2369", "2370", "2371", "2372", "2373", "2374", "2375", "2376", "2377", "2378", "2379", "2380", "2381", "2382", "2383", "2384", "2385", "2386", "2387", "2388", "2389", "2390", "2391", "2392", "2393", "2394", "2395", "2396", "2397", "2398", "2399", "2400", "2401", "2402", "2403", "2404", "2405", "2406", "2407", "2408", "2409", "2410", "2411", "2412", "2413", "2414", "2415", "2416", "2417", "2418", "2419", "2420", "2421", "2422", "2423"], ["2424"], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalPedido\\index.jsx", ["2425", "2426", "2427", "2428", "2429", "2430", "2431", "2432", "2433", "2434", "2435", "2436", "2437", "2438", "2439", "2440", "2441", "2442", "2443", "2444", "2445"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Home\\MobileKanban\\index.jsx", ["2446", "2447", "2448", "2449", "2450", "2451", "2452", "2453", "2454", "2455", "2456", "2457", "2458", "2459", "2460", "2461", "2462", "2463", "2464", "2465", "2466"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditVendedor\\index.jsx", ["2467", "2468", "2469", "2470", "2471"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\formatPrice.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListEmpresas\\modalEditEntregador.jsx", ["2472", "2473", "2474", "2475", "2476", "2477", "2478", "2479", "2480", "2481"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\convertPrice.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\LeftMenu\\SidebarData.jsx", ["2482", "2483", "2484", "2485", "2486"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\PedidosComponent\\index.jsx", ["2487", "2488", "2489", "2490", "2491", "2492", "2493", "2494", "2495", "2496", "2497", "2498", "2499", "2500", "2501", "2502", "2503"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\LeftMenu\\SubMenu.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\LeftMenu\\AtendimentoModal.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Buttons\\MesasCardButton\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\Mesas\\useGetMesas\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useGetCaixaById\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Pagination\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useGetCaixa\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalAddUserImg\\index.jsx", ["2504", "2505", "2506", "2507", "2508", "2509", "2510"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalLinkCardapioSalao\\index.jsx", ["2511"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalLinkCardapio\\index.jsx", ["2512"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\stylesProfile.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\input.styles.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListItem\\ModalAddItemAdicionais.jsx", ["2513", "2514", "2515", "2516", "2517", "2518", "2519", "2520", "2521", "2522", "2523", "2524", "2525", "2526", "2527", "2528", "2529", "2530", "2531", "2532", "2533", "2534", "2535", "2536"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mapa\\Input.jsx", ["2537", "2538"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\UserProfile.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\Input.jsx", ["2539"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\DatePicker\\CustomInput.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\styles\\global.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\SearchBar\\index.js", ["2540"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Input\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Profile\\index.js", ["2541", "2542"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useGetEmpresa\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalOpenCaixa\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Modal\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalConfirmacaoSenha\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalPdv\\index.jsx", ["2543", "2544", "2545", "2546", "2547", "2548", "2549", "2550", "2551", "2552", "2553", "2554", "2555", "2556", "2557", "2558", "2559", "2560", "2561", "2562", "2563", "2564", "2565", "2566", "2567", "2568"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Modals\\SuprimentoModal\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Modals\\CloseCaixaModal\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Modals\\SangriaModal\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Pagination\\PaginationItem\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Whatsapp\\stylesUserProfile.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\Mapa\\input.styles.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ListCategoria\\input.styles.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\SearchBar\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Input\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Profile\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Form\\CurrencyInput\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Forms\\SuprimentoForm\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Forms\\SangriaForm\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Caixa\\Forms\\CloseCaixaForm\\index.jsx", ["2569", "2570"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\Header\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\Content\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\Menu\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\Contacts\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\Header\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\MessageField\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\Header\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\Menu\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\Content\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\Full\\MessageField\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\Chat\\List\\Contacts\\styles.js", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\FormasTiposEntrega\\index.jsx", ["2571", "2572", "2573"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\ConfiguracaoInicial\\index.jsx", ["2574", "2575", "2576", "2577", "2578", "2579", "2580", "2581", "2582", "2583", "2584", "2585", "2586", "2587", "2588", "2589", "2590", "2591", "2592", "2593", "2594", "2595", "2596"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ui\\RegistrationStepper.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\pages\\AdminSolicitacoesCardapio\\index.jsx", ["2597"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useFilaAtendimento\\index.jsx", ["2598", "2599"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\hooks\\useImagePreloader.jsx", ["2600"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\UnreadBadge\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalAddCustomResponse\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\components\\ModalEditCustomResponse\\index.jsx", ["2601"], [], "C:\\Users\\<USER>\\Documents\\PEDEJA\\pedeja-front\\src\\utils\\deviceDetection.js", [], [], {"ruleId": "2602", "severity": 1, "message": "2603", "line": 19, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2606", "line": 5, "column": 30, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 42}, {"ruleId": "2602", "severity": 1, "message": "2607", "line": 5, "column": 44, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 54}, {"ruleId": "2602", "severity": 1, "message": "2608", "line": 18, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2609", "line": 18, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 35}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 14, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 44, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 44, "endColumn": 32}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 189, "column": 131, "nodeType": "2614", "endLine": 189, "endColumn": 134}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 195, "column": 146, "nodeType": "2614", "endLine": 195, "endColumn": 150}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 2, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2616", "line": 10, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2617", "line": 10, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 15, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2618", "line": 43, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 43, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 45, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 45, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2619", "line": 53, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 53, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2620", "line": 54, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 54, "endColumn": 39}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 159, "column": 139, "nodeType": "2614", "endLine": 159, "endColumn": 142}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 165, "column": 154, "nodeType": "2614", "endLine": 165, "endColumn": 158}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 15, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 44, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 44, "endColumn": 32}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 208, "column": 135, "nodeType": "2614", "endLine": 208, "endColumn": 138}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 214, "column": 150, "nodeType": "2614", "endLine": 214, "endColumn": 154}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 14, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 44, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 44, "endColumn": 32}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 206, "column": 131, "nodeType": "2614", "endLine": 206, "endColumn": 134}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 212, "column": 146, "nodeType": "2614", "endLine": 212, "endColumn": 150}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 15, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 23, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 53, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 53, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 60, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 60, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2627", "line": 68, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 68, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2628", "line": 79, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 79, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2629", "line": 84, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 84, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2630", "line": 89, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 89, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2631", "line": 93, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2632", "line": 97, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 97, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2633", "line": 101, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 26}, {"ruleId": "2634", "severity": 1, "message": "2635", "line": 112, "column": 8, "nodeType": "2636", "endLine": 112, "endColumn": 19, "suggestions": "2637"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 231, "column": 146, "nodeType": "2614", "endLine": 231, "endColumn": 149}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 238, "column": 157, "nodeType": "2614", "endLine": 238, "endColumn": 161}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 345, "column": 125, "nodeType": "2614", "endLine": 345, "endColumn": 172}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 429, "column": 70, "nodeType": "2614", "endLine": 429, "endColumn": 117}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2638", "line": 8, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 14, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 21, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 21, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 438, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 438, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 445, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 445, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2639", "line": 450, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 450, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2640", "line": 451, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 451, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2641", "line": 451, "column": 32, "nodeType": "2604", "messageId": "2605", "endLine": 451, "endColumn": 53}, {"ruleId": "2602", "severity": 1, "message": "2642", "line": 452, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 452, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2643", "line": 452, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 452, "endColumn": 43}, {"ruleId": "2602", "severity": 1, "message": "2644", "line": 454, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 454, "endColumn": 16}, {"ruleId": "2634", "severity": 1, "message": "2635", "line": 488, "column": 8, "nodeType": "2636", "endLine": 488, "endColumn": 19, "suggestions": "2645"}, {"ruleId": "2602", "severity": 1, "message": "2646", "line": 525, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 525, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2647", "line": 530, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 530, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2648", "line": 531, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 531, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2649", "line": 535, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 535, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 1, "column": 39, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 48}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2651", "line": 10, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 15, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 23, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 54, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 54, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2652", "line": 59, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 59, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 61, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 61, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2627", "line": 74, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 74, "endColumn": 29}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 244, "column": 146, "nodeType": "2614", "endLine": 244, "endColumn": 149}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 251, "column": 157, "nodeType": "2614", "endLine": 251, "endColumn": 161}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 379, "column": 125, "nodeType": "2614", "endLine": 379, "endColumn": 172}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 463, "column": 70, "nodeType": "2614", "endLine": 463, "endColumn": 117}, {"ruleId": "2602", "severity": 1, "message": "2653", "line": 1, "column": 38, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 44}, {"ruleId": "2602", "severity": 1, "message": "2654", "line": 2, "column": 34, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 45}, {"ruleId": "2602", "severity": 1, "message": "2655", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2656", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 16}, {"ruleId": "2634", "severity": 1, "message": "2657", "line": 69, "column": 8, "nodeType": "2636", "endLine": 69, "endColumn": 10, "suggestions": "2658"}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 319, "column": 45, "nodeType": "2614", "endLine": 319, "endColumn": 57}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 330, "column": 65, "nodeType": "2614", "endLine": 330, "endColumn": 94}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2661", "line": 9, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2662", "line": 9, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 35}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 14, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2663", "line": 70, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2664", "line": 71, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 71, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 72, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 72, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 78, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 78, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2665", "line": 81, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 81, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2666", "line": 81, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 81, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2667", "line": 82, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 82, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2668", "line": 82, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 82, "endColumn": 35}, {"ruleId": "2602", "severity": 1, "message": "2669", "line": 83, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 83, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2670", "line": 83, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 83, "endColumn": 31}, {"ruleId": "2634", "severity": 1, "message": "2671", "line": 101, "column": 8, "nodeType": "2636", "endLine": 101, "endColumn": 22, "suggestions": "2672"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 298, "column": 140, "nodeType": "2614", "endLine": 298, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 304, "column": 157, "nodeType": "2614", "endLine": 304, "endColumn": 161}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 12, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 16, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 23, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 24, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 24, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2673", "line": 27, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 27, "endColumn": 13}, {"ruleId": "2602", "severity": 1, "message": "2663", "line": 57, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 57, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2664", "line": 58, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 58, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 59, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 59, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 68, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 68, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2674", "line": 72, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 72, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2665", "line": 73, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 73, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2666", "line": 73, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 73, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2667", "line": 74, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 74, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2668", "line": 74, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 74, "endColumn": 35}, {"ruleId": "2602", "severity": 1, "message": "2669", "line": 75, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 75, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2670", "line": 75, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 75, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2627", "line": 131, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 131, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2675", "line": 246, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 246, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2676", "line": 254, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 254, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2677", "line": 300, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 300, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2678", "line": 302, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 302, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2679", "line": 319, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 319, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2680", "line": 328, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 328, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2681", "line": 336, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 336, "endColumn": 40}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 378, "column": 146, "nodeType": "2614", "endLine": 378, "endColumn": 149}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 385, "column": 157, "nodeType": "2614", "endLine": 385, "endColumn": 161}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 562, "column": 121, "nodeType": "2614", "endLine": 562, "endColumn": 168}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 646, "column": 66, "nodeType": "2614", "endLine": 646, "endColumn": 113}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 13, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 21, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 21, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2682", "line": 27, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 27, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 31, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 31, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 32, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 32, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2683", "line": 33, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 33, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2684", "line": 34, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 34, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2685", "line": 38, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 38, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2686", "line": 39, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 39, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2687", "line": 40, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 40, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2663", "line": 68, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 68, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2664", "line": 69, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 69, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 70, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2688", "line": 79, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 79, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 81, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 81, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2674", "line": 91, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 91, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2665", "line": 92, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 92, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2666", "line": 92, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 92, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2667", "line": 93, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2668", "line": 93, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 35}, {"ruleId": "2602", "severity": 1, "message": "2669", "line": 94, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 94, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2670", "line": 94, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 94, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2627", "line": 211, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 211, "endColumn": 29}, {"ruleId": "2634", "severity": 1, "message": "2689", "line": 251, "column": 8, "nodeType": "2636", "endLine": 251, "endColumn": 10, "suggestions": "2690"}, {"ruleId": "2634", "severity": 1, "message": "2689", "line": 261, "column": 8, "nodeType": "2636", "endLine": 261, "endColumn": 10, "suggestions": "2691"}, {"ruleId": "2634", "severity": 1, "message": "2692", "line": 396, "column": 8, "nodeType": "2636", "endLine": 396, "endColumn": 67, "suggestions": "2693"}, {"ruleId": "2634", "severity": 1, "message": "2694", "line": 414, "column": 8, "nodeType": "2636", "endLine": 414, "endColumn": 17, "suggestions": "2695"}, {"ruleId": "2602", "severity": 1, "message": "2675", "line": 515, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 515, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2676", "line": 523, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 523, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2677", "line": 569, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 569, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2678", "line": 571, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 571, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2679", "line": 588, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 588, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2680", "line": 597, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 597, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2681", "line": 605, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 605, "endColumn": 40}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 661, "column": 140, "nodeType": "2614", "endLine": 661, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 667, "column": 157, "nodeType": "2614", "endLine": 667, "endColumn": 161}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 885, "column": 129, "nodeType": "2614", "endLine": 885, "endColumn": 176}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 969, "column": 74, "nodeType": "2614", "endLine": 969, "endColumn": 121}, {"ruleId": "2602", "severity": 1, "message": "2655", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2656", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 16}, {"ruleId": "2634", "severity": 1, "message": "2696", "line": 70, "column": 8, "nodeType": "2636", "endLine": 70, "endColumn": 10, "suggestions": "2697"}, {"ruleId": "2602", "severity": 1, "message": "2698", "line": 73, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 73, "endColumn": 33}, {"ruleId": "2602", "severity": 1, "message": "2699", "line": 74, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 74, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2700", "line": 75, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 75, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2701", "line": 76, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 76, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2702", "line": 77, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 77, "endColumn": 21}, {"ruleId": "2634", "severity": 1, "message": "2703", "line": 133, "column": 8, "nodeType": "2636", "endLine": 133, "endColumn": 22, "suggestions": "2704"}, {"ruleId": "2634", "severity": 1, "message": "2705", "line": 438, "column": 8, "nodeType": "2636", "endLine": 438, "endColumn": 21, "suggestions": "2706"}, {"ruleId": "2602", "severity": 1, "message": "2707", "line": 501, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 501, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 1, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2653", "line": 1, "column": 38, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 44}, {"ruleId": "2602", "severity": 1, "message": "2656", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2709", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2710", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2711", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2712", "line": 12, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2653", "line": 1, "column": 38, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 44}, {"ruleId": "2602", "severity": 1, "message": "2656", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2713", "line": 6, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2714", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2715", "line": 8, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 11, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2716", "line": 12, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2717", "line": 14, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2718", "line": 19, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2719", "line": 21, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 21, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2720", "line": 22, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2721", "line": 23, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2722", "line": 24, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 24, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2723", "line": 25, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 25, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2724", "line": 26, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 26, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2725", "line": 27, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 27, "endColumn": 12}, {"ruleId": "2602", "severity": 1, "message": "2726", "line": 28, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 28, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2727", "line": 31, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 31, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2728", "line": 32, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 32, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2729", "line": 35, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 35, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2730", "line": 36, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 36, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2731", "line": 37, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 37, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2732", "line": 50, "column": 7, "nodeType": "2604", "messageId": "2605", "endLine": 50, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2733", "line": 57, "column": 7, "nodeType": "2604", "messageId": "2605", "endLine": 57, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2734", "line": 70, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2735", "line": 70, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2736", "line": 70, "column": 32, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 44}, {"ruleId": "2602", "severity": 1, "message": "2737", "line": 70, "column": 46, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 52}, {"ruleId": "2602", "severity": 1, "message": "2738", "line": 99, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 99, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2739", "line": 99, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 99, "endColumn": 43}, {"ruleId": "2602", "severity": 1, "message": "2740", "line": 100, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 100, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2741", "line": 100, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 100, "endColumn": 33}, {"ruleId": "2602", "severity": 1, "message": "2742", "line": 101, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2743", "line": 101, "column": 29, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 47}, {"ruleId": "2602", "severity": 1, "message": "2744", "line": 102, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 102, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "2745", "line": 102, "column": 30, "nodeType": "2604", "messageId": "2605", "endLine": 102, "endColumn": 49}, {"ruleId": "2602", "severity": 1, "message": "2746", "line": 103, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 103, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2747", "line": 103, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 103, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2748", "line": 104, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 104, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2749", "line": 104, "column": 28, "nodeType": "2604", "messageId": "2605", "endLine": 104, "endColumn": 45}, {"ruleId": "2602", "severity": 1, "message": "2750", "line": 117, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 117, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2751", "line": 122, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 122, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2752", "line": 122, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 122, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2753", "line": 125, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 125, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2754", "line": 125, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 125, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2755", "line": 126, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 126, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2756", "line": 126, "column": 29, "nodeType": "2604", "messageId": "2605", "endLine": 126, "endColumn": 47}, {"ruleId": "2602", "severity": 1, "message": "2757", "line": 127, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 127, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2758", "line": 127, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 127, "endColumn": 35}, {"ruleId": "2759", "severity": 1, "message": "2760", "line": 226, "column": 47, "nodeType": "2761", "messageId": "2762", "endLine": 251, "endColumn": 22}, {"ruleId": "2634", "severity": 1, "message": "2763", "line": 461, "column": 8, "nodeType": "2636", "endLine": 461, "endColumn": 10, "suggestions": "2764"}, {"ruleId": "2602", "severity": 1, "message": "2655", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2656", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2713", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2714", "line": 8, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 17}, {"ruleId": "2634", "severity": 1, "message": "2765", "line": 120, "column": 8, "nodeType": "2636", "endLine": 120, "endColumn": 10, "suggestions": "2766"}, {"ruleId": "2634", "severity": 1, "message": "2767", "line": 147, "column": 8, "nodeType": "2636", "endLine": 147, "endColumn": 30, "suggestions": "2768"}, {"ruleId": "2634", "severity": 1, "message": "2769", "line": 169, "column": 8, "nodeType": "2636", "endLine": 169, "endColumn": 10, "suggestions": "2770"}, {"ruleId": "2602", "severity": 1, "message": "2771", "line": 286, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 286, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2772", "line": 501, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 501, "endColumn": 34}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 1, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2653", "line": 1, "column": 38, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 44}, {"ruleId": "2602", "severity": 1, "message": "2656", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2709", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2710", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2711", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2712", "line": 12, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2656", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2713", "line": 12, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2714", "line": 12, "column": 18, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2773", "line": 12, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 34}, {"ruleId": "2602", "severity": 1, "message": "2774", "line": 36, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 36, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "2775", "line": 38, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 38, "endColumn": 25}, {"ruleId": "2634", "severity": 1, "message": "2696", "line": 64, "column": 8, "nodeType": "2636", "endLine": 64, "endColumn": 10, "suggestions": "2776"}, {"ruleId": "2602", "severity": 1, "message": "2698", "line": 67, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 67, "endColumn": 33}, {"ruleId": "2602", "severity": 1, "message": "2699", "line": 68, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 68, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2700", "line": 69, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 69, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2701", "line": 70, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2702", "line": 71, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 71, "endColumn": 21}, {"ruleId": "2634", "severity": 1, "message": "2703", "line": 127, "column": 8, "nodeType": "2636", "endLine": 127, "endColumn": 22, "suggestions": "2777"}, {"ruleId": "2602", "severity": 1, "message": "2778", "line": 243, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 243, "endColumn": 28}, {"ruleId": "2634", "severity": 1, "message": "2705", "line": 376, "column": 8, "nodeType": "2636", "endLine": 376, "endColumn": 21, "suggestions": "2779"}, {"ruleId": "2602", "severity": 1, "message": "2780", "line": 378, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 378, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2781", "line": 412, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 412, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2782", "line": 418, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 418, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2783", "line": 426, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 426, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2707", "line": 439, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 439, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2784", "line": 447, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 447, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "2785", "line": 465, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 465, "endColumn": 33}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 8, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2787", "line": 9, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 46, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 46, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2788", "line": 47, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 47, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2789", "line": 47, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 47, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2790", "line": 51, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 51, "endColumn": 19}, {"ruleId": "2634", "severity": 1, "message": "2791", "line": 96, "column": 8, "nodeType": "2636", "endLine": 96, "endColumn": 10, "suggestions": "2792"}, {"ruleId": "2602", "severity": 1, "message": "2793", "line": 98, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 98, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2653", "line": 1, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 33}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 5, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2794", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2795", "line": 7, "column": 25, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "2796", "line": 7, "column": 117, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 127}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 9, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 10, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 13, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2787", "line": 14, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2682", "line": 15, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2798", "line": 16, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2799", "line": 17, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 17, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2800", "line": 17, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 17, "endColumn": 34}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 18, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2801", "line": 19, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 19, "column": 18, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2802", "line": 19, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2663", "line": 32, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 32, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2664", "line": 33, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 33, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 40, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 40, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2803", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2804", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 12, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 13, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2794", "line": 16, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2795", "line": 16, "column": 25, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "2805", "line": 16, "column": 39, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 63}, {"ruleId": "2602", "severity": 1, "message": "2806", "line": 16, "column": 77, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 94}, {"ruleId": "2602", "severity": 1, "message": "2807", "line": 16, "column": 96, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 112}, {"ruleId": "2602", "severity": 1, "message": "2808", "line": 16, "column": 114, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 140}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 21, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 21, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2799", "line": 28, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 28, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 29, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 29, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2801", "line": 30, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 30, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 30, "column": 18, "nodeType": "2604", "messageId": "2605", "endLine": 30, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2663", "line": 58, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 58, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2664", "line": 59, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 59, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 60, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 60, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2809", "line": 65, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 65, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2775", "line": 67, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 67, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 68, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 68, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2810", "line": 73, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 73, "endColumn": 31}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 230, "column": 140, "nodeType": "2614", "endLine": 230, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 236, "column": 157, "nodeType": "2614", "endLine": 236, "endColumn": 161}, {"ruleId": "2634", "severity": 1, "message": "2811", "line": 96, "column": 8, "nodeType": "2636", "endLine": 96, "endColumn": 17, "suggestions": "2812", "suppressions": "2813"}, {"ruleId": "2602", "severity": 1, "message": "2711", "line": 8, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 15}, {"ruleId": "2814", "severity": 1, "message": "2815", "line": 204, "column": 69, "nodeType": "2614", "endLine": 215, "endColumn": 70}, {"ruleId": "2814", "severity": 1, "message": "2815", "line": 254, "column": 69, "nodeType": "2614", "endLine": 265, "endColumn": 70}, {"ruleId": "2602", "severity": 1, "message": "2816", "line": 12, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2817", "line": 40, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 40, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2818", "line": 44, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 44, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2819", "line": 45, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 45, "endColumn": 22}, {"ruleId": "2634", "severity": 1, "message": "2820", "line": 237, "column": 8, "nodeType": "2636", "endLine": 237, "endColumn": 10, "suggestions": "2821"}, {"ruleId": "2634", "severity": 1, "message": "2822", "line": 261, "column": 8, "nodeType": "2636", "endLine": 261, "endColumn": 28, "suggestions": "2823"}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2824", "line": 11, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 13}, {"ruleId": "2602", "severity": 1, "message": "2825", "line": 18, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2826", "line": 19, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2827", "line": 22, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2828", "line": 30, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 30, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 140, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 140, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 141, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 141, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2830", "line": 148, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 148, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2831", "line": 160, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 160, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2832", "line": 161, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 161, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2833", "line": 162, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 162, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2834", "line": 163, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 163, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2835", "line": 178, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 178, "endColumn": 43}, {"ruleId": "2634", "severity": 1, "message": "2836", "line": 191, "column": 8, "nodeType": "2636", "endLine": 191, "endColumn": 17, "suggestions": "2837"}, {"ruleId": "2602", "severity": 1, "message": "2838", "line": 193, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 193, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2839", "line": 197, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 197, "endColumn": 14}, {"ruleId": "2634", "severity": 1, "message": "2840", "line": 238, "column": 8, "nodeType": "2636", "endLine": 238, "endColumn": 17, "suggestions": "2841"}, {"ruleId": "2602", "severity": 1, "message": "2842", "line": 336, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 336, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2843", "line": 342, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 342, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "2844", "line": 430, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 430, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2845", "line": 431, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 431, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2846", "line": 432, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 432, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2847", "line": 439, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 439, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2848", "line": 486, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 486, "endColumn": 31}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 589, "column": 61, "nodeType": "2614", "endLine": 589, "endColumn": 129}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 1, "column": 39, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 48}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 14, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 21, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 21, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2663", "line": 56, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 56, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2664", "line": 57, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 57, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 58, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 58, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2652", "line": 62, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 62, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 63, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 63, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2665", "line": 66, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 66, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2666", "line": 66, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 66, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2667", "line": 67, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 67, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2668", "line": 67, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 67, "endColumn": 35}, {"ruleId": "2602", "severity": 1, "message": "2669", "line": 68, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 68, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2670", "line": 68, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 68, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2675", "line": 161, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 161, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2677", "line": 238, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 238, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2678", "line": 240, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 240, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2679", "line": 257, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 257, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2680", "line": 266, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 266, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2681", "line": 274, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 274, "endColumn": 40}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 305, "column": 140, "nodeType": "2614", "endLine": 305, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 311, "column": 157, "nodeType": "2614", "endLine": 311, "endColumn": 161}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 18, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2849", "line": 19, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2850", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2851", "line": 24, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 24, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2852", "line": 25, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 25, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2853", "line": 30, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 30, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2854", "line": 31, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 31, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 56, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 56, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2855", "line": 59, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 59, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2856", "line": 59, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 59, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2857", "line": 60, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 60, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2858", "line": 60, "column": 25, "nodeType": "2604", "messageId": "2605", "endLine": 60, "endColumn": 39}, {"ruleId": "2602", "severity": 1, "message": "2859", "line": 62, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 62, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2860", "line": 62, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 62, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2861", "line": 64, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 64, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2862", "line": 64, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 64, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2863", "line": 66, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 66, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2864", "line": 66, "column": 25, "nodeType": "2604", "messageId": "2605", "endLine": 66, "endColumn": 39}, {"ruleId": "2602", "severity": 1, "message": "2865", "line": 67, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 67, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2866", "line": 67, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 67, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2867", "line": 68, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 68, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2868", "line": 68, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 68, "endColumn": 35}, {"ruleId": "2602", "severity": 1, "message": "2869", "line": 69, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 69, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2870", "line": 69, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 69, "endColumn": 33}, {"ruleId": "2602", "severity": 1, "message": "2871", "line": 71, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 71, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2872", "line": 73, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 73, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2873", "line": 73, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 73, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "2619", "line": 84, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 84, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2874", "line": 84, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 84, "endColumn": 37}, {"ruleId": "2634", "severity": 1, "message": "2875", "line": 97, "column": 7, "nodeType": "2636", "endLine": 97, "endColumn": 16, "suggestions": "2876"}, {"ruleId": "2602", "severity": 1, "message": "2877", "line": 99, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 99, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2848", "line": 150, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 150, "endColumn": 31}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 180, "column": 133, "nodeType": "2614", "endLine": 180, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 186, "column": 139, "nodeType": "2614", "endLine": 186, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 254, "column": 133, "nodeType": "2614", "endLine": 254, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 260, "column": 139, "nodeType": "2614", "endLine": 260, "endColumn": 143}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2878", "line": 8, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 13, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2787", "line": 14, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 85, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 85, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2788", "line": 86, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 86, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2789", "line": 86, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 86, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 87, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 87, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2879", "line": 89, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 89, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "2880", "line": 90, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 90, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2881", "line": 91, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 91, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "2882", "line": 92, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 92, "endColumn": 34}, {"ruleId": "2602", "severity": 1, "message": "2883", "line": 93, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2884", "line": 95, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 95, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "2885", "line": 96, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 96, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "2886", "line": 105, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 105, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2887", "line": 106, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 106, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2888", "line": 151, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 151, "endColumn": 30}, {"ruleId": "2634", "severity": 1, "message": "2889", "line": 207, "column": 8, "nodeType": "2636", "endLine": 207, "endColumn": 10, "suggestions": "2890"}, {"ruleId": "2602", "severity": 1, "message": "2891", "line": 358, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 358, "endColumn": 41}, {"ruleId": "2634", "severity": 1, "message": "2892", "line": 396, "column": 8, "nodeType": "2636", "endLine": 396, "endColumn": 28, "suggestions": "2893"}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 441, "column": 41, "nodeType": "2614", "endLine": 441, "endColumn": 83}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 448, "column": 41, "nodeType": "2614", "endLine": 448, "endColumn": 99}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 455, "column": 41, "nodeType": "2614", "endLine": 455, "endColumn": 86}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 462, "column": 41, "nodeType": "2614", "endLine": 462, "endColumn": 91}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2878", "line": 7, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2894", "line": 10, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 13, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2787", "line": 14, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 81, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 81, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2788", "line": 82, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 82, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2789", "line": 82, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 82, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 83, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 83, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2882", "line": 88, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 88, "endColumn": 34}, {"ruleId": "2602", "severity": 1, "message": "2885", "line": 92, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 92, "endColumn": 28}, {"ruleId": "2634", "severity": 1, "message": "2889", "line": 214, "column": 8, "nodeType": "2636", "endLine": 214, "endColumn": 10, "suggestions": "2895"}, {"ruleId": "2602", "severity": 1, "message": "2891", "line": 265, "column": 28, "nodeType": "2604", "messageId": "2605", "endLine": 265, "endColumn": 46}, {"ruleId": "2634", "severity": 1, "message": "2896", "line": 298, "column": 8, "nodeType": "2636", "endLine": 298, "endColumn": 17, "suggestions": "2897"}, {"ruleId": "2602", "severity": 1, "message": "2898", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 13}, {"ruleId": "2602", "severity": 1, "message": "2656", "line": 7, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2899", "line": 8, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2900", "line": 11, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2901", "line": 92, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 92, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2902", "line": 92, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 92, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "2903", "line": 93, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2904", "line": 93, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 27}, {"ruleId": "2634", "severity": 1, "message": "2905", "line": 256, "column": 8, "nodeType": "2636", "endLine": 256, "endColumn": 44, "suggestions": "2906"}, {"ruleId": "2602", "severity": 1, "message": "2907", "line": 280, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 280, "endColumn": 19}, {"ruleId": "2634", "severity": 1, "message": "2703", "line": 396, "column": 7, "nodeType": "2636", "endLine": 396, "endColumn": 21, "suggestions": "2908"}, {"ruleId": "2602", "severity": 1, "message": "2738", "line": 398, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 398, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2909", "line": 412, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 412, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2910", "line": 429, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 429, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2911", "line": 7, "column": 53, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 68}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 12, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2894", "line": 17, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 17, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2912", "line": 18, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2913", "line": 19, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2853", "line": 24, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 24, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2914", "line": 25, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 25, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2915", "line": 55, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 55, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2916", "line": 71, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 71, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2917", "line": 76, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 76, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2918", "line": 76, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 76, "endColumn": 33}, {"ruleId": "2602", "severity": 1, "message": "2919", "line": 81, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 81, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2857", "line": 87, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 87, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 91, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 91, "endColumn": 32}, {"ruleId": "2634", "severity": 1, "message": "2920", "line": 111, "column": 7, "nodeType": "2636", "endLine": 111, "endColumn": 16, "suggestions": "2921"}, {"ruleId": "2634", "severity": 1, "message": "2922", "line": 176, "column": 8, "nodeType": "2636", "endLine": 176, "endColumn": 43, "suggestions": "2923"}, {"ruleId": "2602", "severity": 1, "message": "2877", "line": 302, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 302, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2648", "line": 356, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 356, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2649", "line": 360, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 360, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2646", "line": 364, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 364, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2924", "line": 372, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 372, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2925", "line": 379, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 379, "endColumn": 18}, {"ruleId": "2926", "severity": 1, "message": "2927", "line": 416, "column": 21, "nodeType": "2928", "messageId": "2929", "endLine": 416, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2930", "line": 460, "column": 15, "nodeType": "2604", "messageId": "2605", "endLine": 460, "endColumn": 24}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 554, "column": 133, "nodeType": "2614", "endLine": 554, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 560, "column": 139, "nodeType": "2614", "endLine": 560, "endColumn": 143}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 630, "column": 102, "nodeType": "2933", "messageId": "2929", "endLine": 630, "endColumn": 104}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 631, "column": 96, "nodeType": "2933", "messageId": "2929", "endLine": 631, "endColumn": 98}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 637, "column": 81, "nodeType": "2614", "endLine": 643, "endColumn": 82}, {"ruleId": "2602", "severity": 1, "message": "2655", "line": 2, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2898", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 13}, {"ruleId": "2602", "severity": 1, "message": "2656", "line": 7, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2899", "line": 8, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2934", "line": 10, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2900", "line": 11, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2935", "line": 14, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 13}, {"ruleId": "2602", "severity": 1, "message": "2901", "line": 92, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 92, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2902", "line": 92, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 92, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "2903", "line": 93, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2904", "line": 93, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2700", "line": 106, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 106, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2936", "line": 106, "column": 25, "nodeType": "2604", "messageId": "2605", "endLine": 106, "endColumn": 39}, {"ruleId": "2602", "severity": 1, "message": "2702", "line": 108, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 108, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2937", "line": 108, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 108, "endColumn": 35}, {"ruleId": "2602", "severity": 1, "message": "2701", "line": 109, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 109, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2938", "line": 109, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 109, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2907", "line": 406, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 406, "endColumn": 19}, {"ruleId": "2634", "severity": 1, "message": "2939", "line": 531, "column": 8, "nodeType": "2636", "endLine": 531, "endColumn": 21, "suggestions": "2940"}, {"ruleId": "2634", "severity": 1, "message": "2657", "line": 631, "column": 8, "nodeType": "2636", "endLine": 631, "endColumn": 10, "suggestions": "2941"}, {"ruleId": "2602", "severity": 1, "message": "2910", "line": 658, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 658, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 6, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2915", "line": 45, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 45, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2917", "line": 60, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 60, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2918", "line": 60, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 60, "endColumn": 33}, {"ruleId": "2602", "severity": 1, "message": "2857", "line": 66, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 66, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 68, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 68, "endColumn": 32}, {"ruleId": "2634", "severity": 1, "message": "2920", "line": 87, "column": 8, "nodeType": "2636", "endLine": 87, "endColumn": 17, "suggestions": "2942"}, {"ruleId": "2602", "severity": 1, "message": "2924", "line": 202, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 202, "endColumn": 22}, {"ruleId": "2926", "severity": 1, "message": "2927", "line": 254, "column": 21, "nodeType": "2928", "messageId": "2929", "endLine": 254, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2943", "line": 273, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 273, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2944", "line": 276, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 276, "endColumn": 24}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 356, "column": 142, "nodeType": "2614", "endLine": 356, "endColumn": 145}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 362, "column": 150, "nodeType": "2614", "endLine": 362, "endColumn": 154}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 410, "column": 61, "nodeType": "2614", "endLine": 413, "endColumn": 62}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 416, "column": 61, "nodeType": "2614", "endLine": 420, "endColumn": 62}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 438, "column": 85, "nodeType": "2614", "endLine": 438, "endColumn": 135}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 567, "column": 61, "nodeType": "2614", "endLine": 570, "endColumn": 62}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 573, "column": 61, "nodeType": "2614", "endLine": 577, "endColumn": 62}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 595, "column": 85, "nodeType": "2614", "endLine": 595, "endColumn": 135}, {"ruleId": "2602", "severity": 1, "message": "2945", "line": 3, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2946", "line": 3, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 30, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 30, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2915", "line": 106, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 106, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2857", "line": 128, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 128, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 130, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 130, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2919", "line": 152, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 152, "endColumn": 23}, {"ruleId": "2634", "severity": 1, "message": "2947", "line": 260, "column": 8, "nodeType": "2636", "endLine": 260, "endColumn": 10, "suggestions": "2948"}, {"ruleId": "2634", "severity": 1, "message": "2920", "line": 276, "column": 8, "nodeType": "2636", "endLine": 276, "endColumn": 35, "suggestions": "2949"}, {"ruleId": "2602", "severity": 1, "message": "2925", "line": 466, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 466, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2648", "line": 514, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 514, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2924", "line": 538, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 538, "endColumn": 22}, {"ruleId": "2926", "severity": 1, "message": "2927", "line": 712, "column": 21, "nodeType": "2928", "messageId": "2929", "endLine": 712, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2930", "line": 747, "column": 15, "nodeType": "2604", "messageId": "2605", "endLine": 747, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2930", "line": 788, "column": 15, "nodeType": "2604", "messageId": "2605", "endLine": 788, "endColumn": 24}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 985, "column": 142, "nodeType": "2614", "endLine": 985, "endColumn": 145}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 991, "column": 150, "nodeType": "2614", "endLine": 991, "endColumn": 154}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1120, "column": 146, "nodeType": "2933", "messageId": "2929", "endLine": 1120, "endColumn": 148}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1121, "column": 140, "nodeType": "2933", "messageId": "2929", "endLine": 1121, "endColumn": 142}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 1135, "column": 125, "nodeType": "2614", "endLine": 1135, "endColumn": 174}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1428, "column": 173, "nodeType": "2614", "endLine": 1430, "endColumn": 174}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1433, "column": 173, "nodeType": "2614", "endLine": 1435, "endColumn": 174}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 1453, "column": 197, "nodeType": "2614", "endLine": 1453, "endColumn": 249}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1566, "column": 173, "nodeType": "2614", "endLine": 1568, "endColumn": 174}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1571, "column": 173, "nodeType": "2614", "endLine": 1573, "endColumn": 174}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 1591, "column": 197, "nodeType": "2614", "endLine": 1591, "endColumn": 249}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1839, "column": 146, "nodeType": "2933", "messageId": "2929", "endLine": 1839, "endColumn": 148}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1840, "column": 140, "nodeType": "2933", "messageId": "2929", "endLine": 1840, "endColumn": 142}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 1854, "column": 125, "nodeType": "2614", "endLine": 1854, "endColumn": 174}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 2147, "column": 173, "nodeType": "2614", "endLine": 2149, "endColumn": 174}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 2152, "column": 173, "nodeType": "2614", "endLine": 2154, "endColumn": 174}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 2172, "column": 197, "nodeType": "2614", "endLine": 2172, "endColumn": 249}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 2285, "column": 173, "nodeType": "2614", "endLine": 2287, "endColumn": 174}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 2290, "column": 173, "nodeType": "2614", "endLine": 2292, "endColumn": 174}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 2310, "column": 197, "nodeType": "2614", "endLine": 2310, "endColumn": 249}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 2, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 15, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 46, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 46, "endColumn": 32}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 151, "column": 135, "nodeType": "2614", "endLine": 151, "endColumn": 138}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 157, "column": 150, "nodeType": "2614", "endLine": 157, "endColumn": 154}, {"ruleId": "2602", "severity": 1, "message": "2894", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 18, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2850", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2851", "line": 24, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 24, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2852", "line": 25, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 25, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2853", "line": 30, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 30, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2854", "line": 31, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 31, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2950", "line": 87, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 87, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 90, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 90, "endColumn": 32}, {"ruleId": "2634", "severity": 1, "message": "2951", "line": 132, "column": 7, "nodeType": "2636", "endLine": 132, "endColumn": 16, "suggestions": "2952"}, {"ruleId": "2602", "severity": 1, "message": "2924", "line": 193, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 193, "endColumn": 22}, {"ruleId": "2926", "severity": 1, "message": "2927", "line": 219, "column": 21, "nodeType": "2928", "messageId": "2929", "endLine": 219, "endColumn": 25}, {"ruleId": "2634", "severity": 1, "message": "2953", "line": 263, "column": 10, "nodeType": "2636", "endLine": 263, "endColumn": 43, "suggestions": "2954"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 294, "column": 133, "nodeType": "2614", "endLine": 294, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 300, "column": 139, "nodeType": "2614", "endLine": 300, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 382, "column": 133, "nodeType": "2614", "endLine": 382, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 388, "column": 139, "nodeType": "2614", "endLine": 388, "endColumn": 143}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 1, "column": 29, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2955", "line": 8, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2956", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2957", "line": 14, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 14, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2958", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "2960", "line": 69, "column": 11, "nodeType": "2961", "messageId": "2929", "endLine": 69, "endColumn": 25}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 103, "column": 15, "nodeType": "2614", "endLine": 107, "endColumn": 41}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 196, "column": 19, "nodeType": "2614", "endLine": 196, "endColumn": 84}, {"ruleId": "2602", "severity": 1, "message": "2899", "line": 2, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 19, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2962", "line": 30, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 30, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 105, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 105, "endColumn": 32}, {"ruleId": "2634", "severity": 1, "message": "2963", "line": 202, "column": 7, "nodeType": "2636", "endLine": 202, "endColumn": 9, "suggestions": "2964"}, {"ruleId": "2602", "severity": 1, "message": "2965", "line": 328, "column": 15, "nodeType": "2604", "messageId": "2605", "endLine": 328, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2966", "line": 402, "column": 14, "nodeType": "2604", "messageId": "2605", "endLine": 402, "endColumn": 28}, {"ruleId": "2967", "severity": 1, "message": "2968", "line": 439, "column": 24, "nodeType": "2761", "messageId": "2969", "endLine": 439, "endColumn": 26}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 538, "column": 131, "nodeType": "2614", "endLine": 538, "endColumn": 134}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 544, "column": 146, "nodeType": "2614", "endLine": 544, "endColumn": 150}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 789, "column": 134, "nodeType": "2614", "endLine": 789, "endColumn": 137}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 947, "column": 41, "nodeType": "2614", "endLine": 947, "endColumn": 167}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 953, "column": 119, "nodeType": "2614", "endLine": 953, "endColumn": 122}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 961, "column": 41, "nodeType": "2614", "endLine": 961, "endColumn": 167}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 967, "column": 119, "nodeType": "2614", "endLine": 967, "endColumn": 122}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 968, "column": 117, "nodeType": "2614", "endLine": 968, "endColumn": 120}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 8, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2970", "line": 12, "column": 51, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 73}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2914", "line": 16, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2971", "line": 17, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 17, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2972", "line": 18, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 24, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 24, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2973", "line": 26, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 26, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2974", "line": 29, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 29, "endColumn": 13}, {"ruleId": "2602", "severity": 1, "message": "2975", "line": 58, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 58, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2950", "line": 60, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 60, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2976", "line": 69, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 69, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2977", "line": 69, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 69, "endColumn": 43}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 70, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2978", "line": 86, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 86, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2979", "line": 86, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 86, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "2980", "line": 90, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 90, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2981", "line": 90, "column": 32, "nodeType": "2604", "messageId": "2605", "endLine": 90, "endColumn": 53}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 103, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 103, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2982", "line": 659, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 659, "endColumn": 29}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1122, "column": 100, "nodeType": "2614", "endLine": 1122, "endColumn": 131}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1123, "column": 99, "nodeType": "2614", "endLine": 1123, "endColumn": 130}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 14, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 44, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 44, "endColumn": 32}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 215, "column": 131, "nodeType": "2614", "endLine": 215, "endColumn": 134}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 221, "column": 146, "nodeType": "2614", "endLine": 221, "endColumn": 150}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2853", "line": 23, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 104, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 104, "endColumn": 32}, {"ruleId": "2634", "severity": 1, "message": "2983", "line": 122, "column": 7, "nodeType": "2636", "endLine": 122, "endColumn": 16, "suggestions": "2984"}, {"ruleId": "2602", "severity": 1, "message": "2924", "line": 208, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 208, "endColumn": 22}, {"ruleId": "2926", "severity": 1, "message": "2927", "line": 222, "column": 21, "nodeType": "2928", "messageId": "2929", "endLine": 222, "endColumn": 25}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 229, "column": 23, "nodeType": "2933", "messageId": "2929", "endLine": 229, "endColumn": 25}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 265, "column": 21, "nodeType": "2614", "endLine": 265, "endColumn": 105}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 266, "column": 21, "nodeType": "2614", "endLine": 266, "endColumn": 126}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 284, "column": 21, "nodeType": "2614", "endLine": 284, "endColumn": 97}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 285, "column": 21, "nodeType": "2614", "endLine": 285, "endColumn": 118}, {"ruleId": "2634", "severity": 1, "message": "2985", "line": 290, "column": 10, "nodeType": "2636", "endLine": 290, "endColumn": 39, "suggestions": "2986"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 318, "column": 137, "nodeType": "2614", "endLine": 318, "endColumn": 140}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 324, "column": 143, "nodeType": "2614", "endLine": 324, "endColumn": 147}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 413, "column": 133, "nodeType": "2614", "endLine": 413, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 419, "column": 139, "nodeType": "2614", "endLine": 419, "endColumn": 143}, {"ruleId": "2602", "severity": 1, "message": "2894", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2607", "line": 13, "column": 29, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 39}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 18, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2850", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2851", "line": 24, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 24, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2852", "line": 25, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 25, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2853", "line": 30, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 30, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 102, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 102, "endColumn": 32}, {"ruleId": "2634", "severity": 1, "message": "2875", "line": 145, "column": 7, "nodeType": "2636", "endLine": 145, "endColumn": 16, "suggestions": "2987"}, {"ruleId": "2602", "severity": 1, "message": "2924", "line": 221, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 221, "endColumn": 22}, {"ruleId": "2926", "severity": 1, "message": "2927", "line": 247, "column": 21, "nodeType": "2928", "messageId": "2929", "endLine": 247, "endColumn": 25}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 251, "column": 23, "nodeType": "2933", "messageId": "2929", "endLine": 251, "endColumn": 25}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 255, "column": 23, "nodeType": "2933", "messageId": "2929", "endLine": 255, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2988", "line": 268, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 268, "endColumn": 22}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 285, "column": 21, "nodeType": "2614", "endLine": 285, "endColumn": 95}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 286, "column": 21, "nodeType": "2614", "endLine": 286, "endColumn": 123}, {"ruleId": "2634", "severity": 1, "message": "2985", "line": 310, "column": 10, "nodeType": "2636", "endLine": 310, "endColumn": 36, "suggestions": "2989"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 340, "column": 133, "nodeType": "2614", "endLine": 340, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 346, "column": 139, "nodeType": "2614", "endLine": 346, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 418, "column": 133, "nodeType": "2614", "endLine": 418, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 424, "column": 139, "nodeType": "2614", "endLine": 424, "endColumn": 143}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 57, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 57, "endColumn": 32}, {"ruleId": "2634", "severity": 1, "message": "2875", "line": 75, "column": 7, "nodeType": "2636", "endLine": 75, "endColumn": 16, "suggestions": "2990"}, {"ruleId": "2926", "severity": 1, "message": "2927", "line": 179, "column": 21, "nodeType": "2928", "messageId": "2929", "endLine": 179, "endColumn": 25}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 183, "column": 23, "nodeType": "2933", "messageId": "2929", "endLine": 183, "endColumn": 25}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 187, "column": 23, "nodeType": "2933", "messageId": "2929", "endLine": 187, "endColumn": 25}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 258, "column": 136, "nodeType": "2614", "endLine": 258, "endColumn": 139}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 264, "column": 137, "nodeType": "2614", "endLine": 264, "endColumn": 140}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 270, "column": 143, "nodeType": "2614", "endLine": 270, "endColumn": 147}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 372, "column": 132, "nodeType": "2614", "endLine": 372, "endColumn": 135}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 378, "column": 133, "nodeType": "2614", "endLine": 378, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 384, "column": 139, "nodeType": "2614", "endLine": 384, "endColumn": 142}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 424, "column": 41, "nodeType": "2614", "endLine": 424, "endColumn": 125}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 425, "column": 41, "nodeType": "2614", "endLine": 425, "endColumn": 146}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 444, "column": 41, "nodeType": "2614", "endLine": 444, "endColumn": 117}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 445, "column": 41, "nodeType": "2614", "endLine": 445, "endColumn": 138}, {"ruleId": "2602", "severity": 1, "message": "2991", "line": 3, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 12}, {"ruleId": "2602", "severity": 1, "message": "2992", "line": 3, "column": 14, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2993", "line": 3, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2994", "line": 3, "column": 25, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2995", "line": 3, "column": 34, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 38}, {"ruleId": "2602", "severity": 1, "message": "2996", "line": 3, "column": 40, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 43}, {"ruleId": "2602", "severity": 1, "message": "2997", "line": 3, "column": 45, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 51}, {"ruleId": "2602", "severity": 1, "message": "2935", "line": 7, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 13}, {"ruleId": "2602", "severity": 1, "message": "2998", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2999", "line": 10, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3000", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 16, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3001", "line": 19, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 12}, {"ruleId": "2602", "severity": 1, "message": "3002", "line": 22, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3003", "line": 25, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 25, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 33, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 33, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3004", "line": 71, "column": 7, "nodeType": "2604", "messageId": "2605", "endLine": 71, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 135, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 135, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "3005", "line": 154, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 154, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "3006", "line": 154, "column": 30, "nodeType": "2604", "messageId": "2605", "endLine": 154, "endColumn": 49}, {"ruleId": "2602", "severity": 1, "message": "3007", "line": 155, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 155, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "3008", "line": 155, "column": 34, "nodeType": "2604", "messageId": "2605", "endLine": 155, "endColumn": 57}, {"ruleId": "2602", "severity": 1, "message": "3009", "line": 166, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 166, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3010", "line": 167, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 167, "endColumn": 21}, {"ruleId": "2634", "severity": 1, "message": "3011", "line": 200, "column": 8, "nodeType": "2636", "endLine": 200, "endColumn": 17, "suggestions": "3012"}, {"ruleId": "2634", "severity": 1, "message": "3013", "line": 312, "column": 8, "nodeType": "2636", "endLine": 312, "endColumn": 17, "suggestions": "3014"}, {"ruleId": "2634", "severity": 1, "message": "3015", "line": 470, "column": 8, "nodeType": "2636", "endLine": 470, "endColumn": 19, "suggestions": "3016"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 704, "column": 150, "nodeType": "2614", "endLine": 704, "endColumn": 153}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 757, "column": 82, "nodeType": "2933", "messageId": "2929", "endLine": 757, "endColumn": 84}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 757, "column": 125, "nodeType": "2933", "messageId": "2929", "endLine": 757, "endColumn": 127}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 767, "column": 71, "nodeType": "2933", "messageId": "2929", "endLine": 767, "endColumn": 73}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 779, "column": 77, "nodeType": "2933", "messageId": "2929", "endLine": 779, "endColumn": 79}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 784, "column": 106, "nodeType": "2933", "messageId": "2929", "endLine": 784, "endColumn": 108}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 836, "column": 98, "nodeType": "2933", "messageId": "2929", "endLine": 836, "endColumn": 100}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 871, "column": 81, "nodeType": "2614", "endLine": 871, "endColumn": 84}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 919, "column": 69, "nodeType": "2933", "messageId": "2929", "endLine": 919, "endColumn": 71}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 930, "column": 73, "nodeType": "2933", "messageId": "2929", "endLine": 930, "endColumn": 75}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 935, "column": 102, "nodeType": "2933", "messageId": "2929", "endLine": 935, "endColumn": 104}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 999, "column": 106, "nodeType": "2933", "messageId": "2929", "endLine": 999, "endColumn": 108}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1037, "column": 77, "nodeType": "2614", "endLine": 1037, "endColumn": 80}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1074, "column": 68, "nodeType": "2933", "messageId": "2929", "endLine": 1074, "endColumn": 70}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1085, "column": 73, "nodeType": "2933", "messageId": "2929", "endLine": 1085, "endColumn": 75}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1090, "column": 102, "nodeType": "2933", "messageId": "2929", "endLine": 1090, "endColumn": 104}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1154, "column": 106, "nodeType": "2933", "messageId": "2929", "endLine": 1154, "endColumn": 108}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1192, "column": 77, "nodeType": "2614", "endLine": 1192, "endColumn": 80}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1254, "column": 71, "nodeType": "2933", "messageId": "2929", "endLine": 1254, "endColumn": 73}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1265, "column": 73, "nodeType": "2933", "messageId": "2929", "endLine": 1265, "endColumn": 75}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1270, "column": 102, "nodeType": "2933", "messageId": "2929", "endLine": 1270, "endColumn": 104}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1338, "column": 106, "nodeType": "2933", "messageId": "2929", "endLine": 1338, "endColumn": 108}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 57, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 57, "endColumn": 32}, {"ruleId": "2634", "severity": 1, "message": "2875", "line": 75, "column": 7, "nodeType": "2636", "endLine": 75, "endColumn": 16, "suggestions": "3017"}, {"ruleId": "2926", "severity": 1, "message": "2927", "line": 171, "column": 21, "nodeType": "2928", "messageId": "2929", "endLine": 171, "endColumn": 25}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 175, "column": 23, "nodeType": "2933", "messageId": "2929", "endLine": 175, "endColumn": 25}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 179, "column": 23, "nodeType": "2933", "messageId": "2929", "endLine": 179, "endColumn": 25}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 250, "column": 136, "nodeType": "2614", "endLine": 250, "endColumn": 139}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 256, "column": 137, "nodeType": "2614", "endLine": 256, "endColumn": 140}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 262, "column": 143, "nodeType": "2614", "endLine": 262, "endColumn": 147}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 360, "column": 132, "nodeType": "2614", "endLine": 360, "endColumn": 135}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 366, "column": 133, "nodeType": "2614", "endLine": 366, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 372, "column": 139, "nodeType": "2614", "endLine": 372, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 409, "column": 41, "nodeType": "2614", "endLine": 409, "endColumn": 127}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 410, "column": 41, "nodeType": "2614", "endLine": 410, "endColumn": 147}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 428, "column": 41, "nodeType": "2614", "endLine": 428, "endColumn": 118}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 429, "column": 41, "nodeType": "2614", "endLine": 429, "endColumn": 138}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 79, "column": 13, "nodeType": "2614", "endLine": 79, "endColumn": 82}, {"ruleId": "2602", "severity": 1, "message": "2607", "line": 8, "column": 25, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 35}, {"ruleId": "2602", "severity": 1, "message": "3018", "line": 8, "column": 37, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 50}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3019", "line": 21, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 21, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 94, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 94, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "3020", "line": 101, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "3021", "line": 101, "column": 29, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 43}, {"ruleId": "2634", "severity": 1, "message": "2875", "line": 114, "column": 7, "nodeType": "2636", "endLine": 114, "endColumn": 16, "suggestions": "3022"}, {"ruleId": "2926", "severity": 1, "message": "2927", "line": 226, "column": 21, "nodeType": "2928", "messageId": "2929", "endLine": 226, "endColumn": 25}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 231, "column": 23, "nodeType": "2933", "messageId": "2929", "endLine": 231, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3023", "line": 250, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 250, "endColumn": 25}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 304, "column": 136, "nodeType": "2614", "endLine": 304, "endColumn": 139}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 310, "column": 137, "nodeType": "2614", "endLine": 310, "endColumn": 140}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 316, "column": 143, "nodeType": "2614", "endLine": 316, "endColumn": 147}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 417, "column": 132, "nodeType": "2614", "endLine": 417, "endColumn": 135}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 436, "column": 139, "nodeType": "2614", "endLine": 436, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 487, "column": 41, "nodeType": "2614", "endLine": 487, "endColumn": 168}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 490, "column": 41, "nodeType": "2614", "endLine": 490, "endColumn": 176}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 510, "column": 119, "nodeType": "2614", "endLine": 510, "endColumn": 122}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 511, "column": 139, "nodeType": "2614", "endLine": 511, "endColumn": 142}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 512, "column": 134, "nodeType": "2614", "endLine": 512, "endColumn": 137}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 544, "column": 41, "nodeType": "2614", "endLine": 544, "endColumn": 158}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 547, "column": 41, "nodeType": "2614", "endLine": 547, "endColumn": 166}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 567, "column": 109, "nodeType": "2614", "endLine": 567, "endColumn": 112}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 568, "column": 129, "nodeType": "2614", "endLine": 568, "endColumn": 132}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 569, "column": 124, "nodeType": "2614", "endLine": 569, "endColumn": 127}, {"ruleId": "2959", "severity": 1, "message": "2960", "line": 70, "column": 11, "nodeType": "2961", "messageId": "2929", "endLine": 70, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2878", "line": 8, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 46, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 46, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2789", "line": 47, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 47, "endColumn": 41}, {"ruleId": "2634", "severity": 1, "message": "3024", "line": 105, "column": 8, "nodeType": "2636", "endLine": 105, "endColumn": 10, "suggestions": "3025"}, {"ruleId": "3026", "severity": 1, "message": "3027", "line": 199, "column": 9, "nodeType": "3028", "messageId": "3029", "endLine": 214, "endColumn": 10}, {"ruleId": "3026", "severity": 1, "message": "3027", "line": 217, "column": 9, "nodeType": "3028", "messageId": "3029", "endLine": 232, "endColumn": 10}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 307, "column": 131, "nodeType": "2614", "endLine": 307, "endColumn": 134}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 313, "column": 146, "nodeType": "2614", "endLine": 313, "endColumn": 150}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 1, "column": 39, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 48}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2878", "line": 7, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2787", "line": 10, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 33, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 33, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2788", "line": 34, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 34, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2789", "line": 34, "column": 26, "nodeType": "2604", "messageId": "2605", "endLine": 34, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2652", "line": 40, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 40, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3030", "line": 43, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 43, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2793", "line": 45, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 45, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2878", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "3031", "line": 10, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2686", "line": 13, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2935", "line": 17, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 17, "endColumn": 13}, {"ruleId": "2602", "severity": 1, "message": "3032", "line": 43, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 43, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 61, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 61, "endColumn": 32}, {"ruleId": "2634", "severity": 1, "message": "3033", "line": 98, "column": 8, "nodeType": "2636", "endLine": 98, "endColumn": 17, "suggestions": "3034"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 283, "column": 140, "nodeType": "2614", "endLine": 283, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 287, "column": 140, "nodeType": "2614", "endLine": 287, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 294, "column": 157, "nodeType": "2614", "endLine": 294, "endColumn": 161}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 338, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 338, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 338, "column": 105, "nodeType": "3037", "messageId": "3038", "endLine": 338, "endColumn": 107}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 338, "column": 105, "nodeType": "3037", "messageId": "3038", "endLine": 338, "endColumn": 107}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 339, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 339, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 339, "column": 105, "nodeType": "3037", "messageId": "3038", "endLine": 339, "endColumn": 107}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 340, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 340, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 340, "column": 105, "nodeType": "3037", "messageId": "3038", "endLine": 340, "endColumn": 107}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 341, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 341, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 341, "column": 105, "nodeType": "3037", "messageId": "3038", "endLine": 341, "endColumn": 107}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 342, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 342, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 383, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 383, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 383, "column": 105, "nodeType": "3037", "messageId": "3038", "endLine": 383, "endColumn": 107}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 383, "column": 105, "nodeType": "3037", "messageId": "3038", "endLine": 383, "endColumn": 107}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 384, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 384, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 384, "column": 105, "nodeType": "3037", "messageId": "3038", "endLine": 384, "endColumn": 107}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 385, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 385, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 385, "column": 105, "nodeType": "3037", "messageId": "3038", "endLine": 385, "endColumn": 107}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 386, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 386, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 386, "column": 105, "nodeType": "3037", "messageId": "3038", "endLine": 386, "endColumn": 107}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 387, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 387, "endColumn": 83}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3040", "line": 11, "column": 79, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 89}, {"ruleId": "2602", "severity": 1, "message": "3041", "line": 12, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3042", "line": 38, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 38, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 44, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 44, "endColumn": 19}, {"ruleId": "2634", "severity": 1, "message": "3043", "line": 61, "column": 8, "nodeType": "2636", "endLine": 61, "endColumn": 10, "suggestions": "3044"}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2878", "line": 7, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 12, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2787", "line": 13, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 38, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 38, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "3045", "line": 47, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 47, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "3046", "line": 58, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 58, "endColumn": 18}, {"ruleId": "2634", "severity": 1, "message": "3047", "line": 112, "column": 8, "nodeType": "2636", "endLine": 112, "endColumn": 10, "suggestions": "3048"}, {"ruleId": "2602", "severity": 1, "message": "3049", "line": 152, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 152, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2793", "line": 158, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 158, "endColumn": 21}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 249, "column": 37, "nodeType": "2614", "endLine": 249, "endColumn": 72}, {"ruleId": "2814", "severity": 1, "message": "2815", "line": 348, "column": 77, "nodeType": "2614", "endLine": 348, "endColumn": 267}, {"ruleId": "2814", "severity": 1, "message": "2815", "line": 387, "column": 77, "nodeType": "2614", "endLine": 387, "endColumn": 267}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3042", "line": 529, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 529, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 534, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 534, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 16, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 93, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2877", "line": 133, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 133, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2924", "line": 207, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 207, "endColumn": 22}, {"ruleId": "2634", "severity": 1, "message": "3050", "line": 214, "column": 9, "nodeType": "3051", "endLine": 214, "endColumn": 24}, {"ruleId": "2926", "severity": 1, "message": "2927", "line": 233, "column": 21, "nodeType": "2928", "messageId": "2929", "endLine": 233, "endColumn": 25}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 237, "column": 23, "nodeType": "2933", "messageId": "2929", "endLine": 237, "endColumn": 25}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 241, "column": 23, "nodeType": "2933", "messageId": "2929", "endLine": 241, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2988", "line": 254, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 254, "endColumn": 22}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 272, "column": 21, "nodeType": "2614", "endLine": 272, "endColumn": 123}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 326, "column": 133, "nodeType": "2614", "endLine": 326, "endColumn": 136}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 332, "column": 139, "nodeType": "2614", "endLine": 332, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 401, "column": 137, "nodeType": "2614", "endLine": 401, "endColumn": 140}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 407, "column": 143, "nodeType": "2614", "endLine": 407, "endColumn": 147}, {"ruleId": "2602", "severity": 1, "message": "3052", "line": 4, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 17}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 23, "column": 17, "nodeType": "2614", "endLine": 23, "endColumn": 70}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 44, "column": 17, "nodeType": "2614", "endLine": 44, "endColumn": 63}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 86, "column": 13, "nodeType": "2614", "endLine": 86, "endColumn": 34}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 91, "column": 15, "nodeType": "2614", "endLine": 91, "endColumn": 32}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 96, "column": 15, "nodeType": "2614", "endLine": 96, "endColumn": 34}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 101, "column": 15, "nodeType": "2614", "endLine": 101, "endColumn": 35}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 106, "column": 15, "nodeType": "2614", "endLine": 106, "endColumn": 31}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 111, "column": 15, "nodeType": "2614", "endLine": 111, "endColumn": 33}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 116, "column": 15, "nodeType": "2614", "endLine": 116, "endColumn": 34}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 121, "column": 15, "nodeType": "2614", "endLine": 121, "endColumn": 32}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 126, "column": 15, "nodeType": "2614", "endLine": 126, "endColumn": 32}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 131, "column": 15, "nodeType": "2614", "endLine": 131, "endColumn": 31}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 136, "column": 15, "nodeType": "2614", "endLine": 136, "endColumn": 34}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 141, "column": 15, "nodeType": "2614", "endLine": 141, "endColumn": 36}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 11, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3053", "line": 23, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3054", "line": 38, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 38, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "3055", "line": 39, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 39, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 124, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 124, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 130, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 130, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "3010", "line": 139, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 139, "endColumn": 33}, {"ruleId": "2602", "severity": 1, "message": "2618", "line": 141, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 141, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "3049", "line": 159, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 159, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3049", "line": 200, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 200, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3049", "line": 259, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 259, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3056", "line": 15, "column": 70, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 87}, {"ruleId": "2602", "severity": 1, "message": "2850", "line": 23, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2851", "line": 25, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 25, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2852", "line": 26, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 26, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2854", "line": 32, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 32, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2950", "line": 341, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 341, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 344, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 344, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2857", "line": 348, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 348, "endColumn": 23}, {"ruleId": "2634", "severity": 1, "message": "3057", "line": 423, "column": 8, "nodeType": "2636", "endLine": 423, "endColumn": 23, "suggestions": "3058"}, {"ruleId": "2602", "severity": 1, "message": "2877", "line": 425, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 425, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2924", "line": 503, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 503, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3023", "line": 516, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 516, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3059", "line": 657, "column": 15, "nodeType": "2604", "messageId": "2605", "endLine": 657, "endColumn": 26}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 698, "column": 25, "nodeType": "2614", "endLine": 698, "endColumn": 99}, {"ruleId": "2634", "severity": 1, "message": "3060", "line": 757, "column": 8, "nodeType": "2636", "endLine": 757, "endColumn": 27, "suggestions": "3061"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 820, "column": 150, "nodeType": "2614", "endLine": 820, "endColumn": 154}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 14, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 44, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 44, "endColumn": 32}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 207, "column": 131, "nodeType": "2614", "endLine": 207, "endColumn": 134}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 213, "column": 146, "nodeType": "2614", "endLine": 213, "endColumn": 150}, {"ruleId": "2602", "severity": 1, "message": "3062", "line": 6, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3063", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 21, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 21, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 47, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 47, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "3064", "line": 50, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 50, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3065", "line": 63, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 63, "endColumn": 12}, {"ruleId": "2634", "severity": 1, "message": "3066", "line": 87, "column": 20, "nodeType": "2604", "endLine": 87, "endColumn": 27}, {"ruleId": "2634", "severity": 1, "message": "3066", "line": 98, "column": 23, "nodeType": "2604", "endLine": 98, "endColumn": 30}, {"ruleId": "2634", "severity": 1, "message": "3066", "line": 109, "column": 25, "nodeType": "2604", "endLine": 109, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "3067", "line": 120, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 120, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 10, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3068", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 91, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 91, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 96, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 96, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3065", "line": 107, "column": 33, "nodeType": "2604", "messageId": "2605", "endLine": 107, "endColumn": 40}, {"ruleId": "2602", "severity": 1, "message": "3069", "line": 4, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 31}, {"ruleId": "2634", "severity": 1, "message": "3070", "line": 64, "column": 6, "nodeType": "2636", "endLine": 64, "endColumn": 73, "suggestions": "3071"}, {"ruleId": "2602", "severity": 1, "message": "2773", "line": 17, "column": 19, "nodeType": "2604", "messageId": "2605", "endLine": 17, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "3072", "line": 31, "column": 48, "nodeType": "2604", "messageId": "2605", "endLine": 31, "endColumn": 61}, {"ruleId": "2602", "severity": 1, "message": "3073", "line": 32, "column": 16, "nodeType": "2604", "messageId": "2605", "endLine": 32, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3074", "line": 245, "column": 7, "nodeType": "2604", "messageId": "2605", "endLine": 245, "endColumn": 17}, {"ruleId": "2634", "severity": 1, "message": "3075", "line": 321, "column": 6, "nodeType": "2636", "endLine": 321, "endColumn": 8, "suggestions": "3076"}, {"ruleId": "2602", "severity": 1, "message": "3077", "line": 334, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 334, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2652", "line": 379, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 379, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2832", "line": 386, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 386, "endColumn": 13}, {"ruleId": "2602", "severity": 1, "message": "2833", "line": 387, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 387, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "3009", "line": 403, "column": 7, "nodeType": "2604", "messageId": "2605", "endLine": 403, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3078", "line": 415, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 415, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3079", "line": 416, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 416, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "3080", "line": 418, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 418, "endColumn": 18}, {"ruleId": "2634", "severity": 1, "message": "3081", "line": 431, "column": 6, "nodeType": "2636", "endLine": 431, "endColumn": 64, "suggestions": "3082"}, {"ruleId": "2602", "severity": 1, "message": "3083", "line": 550, "column": 42, "nodeType": "2604", "messageId": "2605", "endLine": 550, "endColumn": 75}, {"ruleId": "2602", "severity": 1, "message": "3084", "line": 566, "column": 15, "nodeType": "2604", "messageId": "2605", "endLine": 566, "endColumn": 24}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 660, "column": 55, "nodeType": "2933", "messageId": "2929", "endLine": 660, "endColumn": 57}, {"ruleId": "2634", "severity": 1, "message": "3085", "line": 912, "column": 6, "nodeType": "2636", "endLine": 912, "endColumn": 20, "suggestions": "3086"}, {"ruleId": "2634", "severity": 1, "message": "3087", "line": 1037, "column": 6, "nodeType": "2636", "endLine": 1037, "endColumn": 8, "suggestions": "3088"}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 1322, "column": 19, "nodeType": "2614", "endLine": 1322, "endColumn": 69}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 1359, "column": 19, "nodeType": "2614", "endLine": 1359, "endColumn": 66}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1451, "column": 19, "nodeType": "2614", "endLine": 1451, "endColumn": 63}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1468, "column": 19, "nodeType": "2614", "endLine": 1468, "endColumn": 63}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1579, "column": 21, "nodeType": "2614", "endLine": 1579, "endColumn": 65}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1596, "column": 21, "nodeType": "2614", "endLine": 1596, "endColumn": 65}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 1608, "column": 56, "nodeType": "2933", "messageId": "2929", "endLine": 1608, "endColumn": 58}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 1909, "column": 19, "nodeType": "2614", "endLine": 1918, "endColumn": 21}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1945, "column": 25, "nodeType": "2614", "endLine": 1945, "endColumn": 28}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1951, "column": 25, "nodeType": "2614", "endLine": 1951, "endColumn": 28}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1954, "column": 25, "nodeType": "2614", "endLine": 1954, "endColumn": 28}, {"ruleId": "2959", "severity": 1, "message": "3089", "line": 1984, "column": 21, "nodeType": "2961", "messageId": "2929", "endLine": 1984, "endColumn": 34}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 1988, "column": 19, "nodeType": "2614", "endLine": 1998, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3090", "line": 2023, "column": 21, "nodeType": "2961", "messageId": "2929", "endLine": 2023, "endColumn": 28}, {"ruleId": "2634", "severity": 1, "message": "2836", "line": 610, "column": 6, "nodeType": "2636", "endLine": 610, "endColumn": 8, "suggestions": "3091", "suppressions": "3092"}, {"ruleId": "2602", "severity": 1, "message": "3093", "line": 2, "column": 44, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 58}, {"ruleId": "2602", "severity": 1, "message": "3094", "line": 1, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2711", "line": 3, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "3095", "line": 14, "column": 7, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2638", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2651", "line": 11, "column": 78, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 96}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 12, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2803", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2804", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 15, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3097", "line": 16, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2687", "line": 23, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2685", "line": 24, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 24, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2686", "line": 25, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 25, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 83, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 83, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2639", "line": 93, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3098", "line": 98, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 98, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "3099", "line": 98, "column": 29, "nodeType": "2604", "messageId": "2605", "endLine": 98, "endColumn": 47}, {"ruleId": "2602", "severity": 1, "message": "3100", "line": 99, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 99, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3101", "line": 101, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2627", "line": 101, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "3102", "line": 102, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 102, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "3103", "line": 103, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 103, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3104", "line": 104, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 104, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "3105", "line": 106, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 106, "endColumn": 22}, {"ruleId": "2634", "severity": 1, "message": "2635", "line": 231, "column": 8, "nodeType": "2636", "endLine": 231, "endColumn": 19, "suggestions": "3106"}, {"ruleId": "2602", "severity": 1, "message": "2646", "line": 265, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 265, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2647", "line": 270, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 270, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2648", "line": 271, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 271, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2649", "line": 275, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 275, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2793", "line": 279, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 279, "endColumn": 21}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 361, "column": 140, "nodeType": "2614", "endLine": 361, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 365, "column": 140, "nodeType": "2614", "endLine": 365, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 373, "column": 153, "nodeType": "2614", "endLine": 373, "endColumn": 157}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 410, "column": 49, "nodeType": "2614", "endLine": 414, "endColumn": 50}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 431, "column": 73, "nodeType": "2614", "endLine": 431, "endColumn": 123}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2638", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2651", "line": 11, "column": 78, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 96}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 12, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2803", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2804", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 15, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3097", "line": 16, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2687", "line": 23, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2685", "line": 24, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 24, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2686", "line": 25, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 25, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 83, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 83, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2639", "line": 93, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3098", "line": 98, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 98, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "3099", "line": 98, "column": 29, "nodeType": "2604", "messageId": "2605", "endLine": 98, "endColumn": 47}, {"ruleId": "2602", "severity": 1, "message": "3100", "line": 99, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 99, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3101", "line": 101, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2627", "line": 101, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "3102", "line": 102, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 102, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "3103", "line": 103, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 103, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3104", "line": 104, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 104, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "3105", "line": 106, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 106, "endColumn": 22}, {"ruleId": "2634", "severity": 1, "message": "2635", "line": 211, "column": 8, "nodeType": "2636", "endLine": 211, "endColumn": 19, "suggestions": "3107"}, {"ruleId": "2602", "severity": 1, "message": "2646", "line": 245, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 245, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2647", "line": 250, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 250, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2648", "line": 251, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 251, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2649", "line": 255, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 255, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2793", "line": 259, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 259, "endColumn": 21}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 341, "column": 140, "nodeType": "2614", "endLine": 341, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 345, "column": 140, "nodeType": "2614", "endLine": 345, "endColumn": 143}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 353, "column": 153, "nodeType": "2614", "endLine": 353, "endColumn": 157}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 390, "column": 49, "nodeType": "2614", "endLine": 394, "endColumn": 50}, {"ruleId": "2659", "severity": 1, "message": "2660", "line": 411, "column": 73, "nodeType": "2614", "endLine": 411, "endColumn": 123}, {"ruleId": "2602", "severity": 1, "message": "2711", "line": 2, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2711", "line": 2, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "3108", "line": 3, "column": 37, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 43}, {"ruleId": "2602", "severity": 1, "message": "2751", "line": 44, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 44, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "3109", "line": 80, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 80, "endColumn": 35}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 1, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 1, "column": 28, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 36}, {"ruleId": "2602", "severity": 1, "message": "2751", "line": 33, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 33, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3110", "line": 8, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "3111", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 12}, {"ruleId": "2602", "severity": 1, "message": "3112", "line": 11, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3113", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "3114", "line": 20, "column": 89, "nodeType": "2604", "messageId": "2605", "endLine": 20, "endColumn": 100}, {"ruleId": "2602", "severity": 1, "message": "3115", "line": 526, "column": 7, "nodeType": "2604", "messageId": "2605", "endLine": 526, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "3032", "line": 538, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 538, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 555, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 555, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "3116", "line": 565, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 565, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3117", "line": 565, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 565, "endColumn": 45}, {"ruleId": "2602", "severity": 1, "message": "3118", "line": 569, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 569, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "3119", "line": 569, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 569, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3049", "line": 794, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 794, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3120", "line": 1046, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 1046, "endColumn": 28}, {"ruleId": "2634", "severity": 1, "message": "3121", "line": 1087, "column": 6, "nodeType": "2636", "endLine": 1087, "endColumn": 8, "suggestions": "3122"}, {"ruleId": "2634", "severity": 1, "message": "3123", "line": 1328, "column": 6, "nodeType": "2636", "endLine": 1328, "endColumn": 18, "suggestions": "3124"}, {"ruleId": "2634", "severity": 1, "message": "3125", "line": 1383, "column": 6, "nodeType": "2636", "endLine": 1383, "endColumn": 8, "suggestions": "3126"}, {"ruleId": "2634", "severity": 1, "message": "3127", "line": 1544, "column": 6, "nodeType": "2636", "endLine": 1544, "endColumn": 20, "suggestions": "3128"}, {"ruleId": "3129", "severity": 1, "message": "3130", "line": 1682, "column": 13, "nodeType": "3131", "messageId": "3132", "endLine": 1682, "endColumn": 14, "suggestions": "3133"}, {"ruleId": "3129", "severity": 1, "message": "3134", "line": 1682, "column": 15, "nodeType": "3131", "messageId": "3132", "endLine": 1682, "endColumn": 16, "suggestions": "3135"}, {"ruleId": "2602", "severity": 1, "message": "3136", "line": 1982, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 1982, "endColumn": 14}, {"ruleId": "2634", "severity": 1, "message": "3137", "line": 2284, "column": 6, "nodeType": "2636", "endLine": 2284, "endColumn": 19, "suggestions": "3138"}, {"ruleId": "2634", "severity": 1, "message": "3139", "line": 2300, "column": 6, "nodeType": "2636", "endLine": 2300, "endColumn": 24, "suggestions": "3140"}, {"ruleId": "2634", "severity": 1, "message": "3141", "line": 2312, "column": 6, "nodeType": "2636", "endLine": 2312, "endColumn": 24, "suggestions": "3142"}, {"ruleId": "2634", "severity": 1, "message": "3125", "line": 2360, "column": 6, "nodeType": "2636", "endLine": 2360, "endColumn": 33, "suggestions": "3143"}, {"ruleId": "2634", "severity": 1, "message": "3144", "line": 2388, "column": 6, "nodeType": "2636", "endLine": 2388, "endColumn": 33, "suggestions": "3145"}, {"ruleId": "2634", "severity": 1, "message": "3146", "line": 2692, "column": 6, "nodeType": "2636", "endLine": 2692, "endColumn": 32, "suggestions": "3147"}, {"ruleId": "2634", "severity": 1, "message": "3148", "line": 2898, "column": 6, "nodeType": "2636", "endLine": 2898, "endColumn": 87, "suggestions": "3149"}, {"ruleId": "2602", "severity": 1, "message": "3150", "line": 14, "column": 16, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3150", "line": 14, "column": 16, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3151", "line": 5, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 15, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3152", "line": 69, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 69, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "3153", "line": 70, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 24}, {"ruleId": "2634", "severity": 1, "message": "2635", "line": 121, "column": 6, "nodeType": "2636", "endLine": 121, "endColumn": 17, "suggestions": "3154"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 188, "column": 13, "nodeType": "2614", "endLine": 188, "endColumn": 16}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 194, "column": 19, "nodeType": "2614", "endLine": 194, "endColumn": 22}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 199, "column": 139, "nodeType": "2614", "endLine": 199, "endColumn": 142}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3151", "line": 5, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 15, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3152", "line": 69, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 69, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "3153", "line": 70, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 24}, {"ruleId": "2634", "severity": 1, "message": "2635", "line": 121, "column": 6, "nodeType": "2636", "endLine": 121, "endColumn": 17, "suggestions": "3155"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 188, "column": 13, "nodeType": "2614", "endLine": 188, "endColumn": 16}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 194, "column": 19, "nodeType": "2614", "endLine": 194, "endColumn": 22}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 199, "column": 139, "nodeType": "2614", "endLine": 199, "endColumn": 142}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3151", "line": 5, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "3156", "line": 8, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 15, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 16, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2618", "line": 72, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 72, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2652", "line": 75, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 75, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3157", "line": 77, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 77, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 85, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 85, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 86, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 86, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2627", "line": 101, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 29}, {"ruleId": "2634", "severity": 1, "message": "3158", "line": 155, "column": 8, "nodeType": "2636", "endLine": 155, "endColumn": 31, "suggestions": "3159"}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 248, "column": 142, "nodeType": "2614", "endLine": 248, "endColumn": 145}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 255, "column": 153, "nodeType": "2614", "endLine": 255, "endColumn": 157}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 383, "column": 117, "nodeType": "2614", "endLine": 383, "endColumn": 164}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 467, "column": 62, "nodeType": "2614", "endLine": 467, "endColumn": 109}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 11, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 13, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3097", "line": 14, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 14, "column": 33, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 20, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 20, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3160", "line": 119, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 119, "endColumn": 13}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 165, "column": 37, "nodeType": "2614", "endLine": 165, "endColumn": 40}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 170, "column": 157, "nodeType": "2614", "endLine": 170, "endColumn": 160}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3151", "line": 5, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "3156", "line": 8, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 11, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 15, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 16, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2618", "line": 73, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 73, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3157", "line": 78, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 78, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 86, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 86, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 87, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 87, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2627", "line": 101, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 101, "endColumn": 29}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 241, "column": 142, "nodeType": "2614", "endLine": 241, "endColumn": 145}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 248, "column": 153, "nodeType": "2614", "endLine": 248, "endColumn": 157}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 355, "column": 117, "nodeType": "2614", "endLine": 355, "endColumn": 164}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 439, "column": 62, "nodeType": "2614", "endLine": 439, "endColumn": 109}, {"ruleId": "2602", "severity": 1, "message": "3161", "line": 3, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2714", "line": 4, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3162", "line": 5, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 5, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2899", "line": 5, "column": 34, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 38}, {"ruleId": "2602", "severity": 1, "message": "2713", "line": 6, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2638", "line": 8, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2711", "line": 9, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "3161", "line": 3, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2714", "line": 4, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2899", "line": 5, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2713", "line": 6, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3163", "line": 81, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 81, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "3161", "line": 3, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2714", "line": 4, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2899", "line": 5, "column": 34, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 38}, {"ruleId": "2602", "severity": 1, "message": "2713", "line": 6, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 78, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 78, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3164", "line": 79, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 79, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2652", "line": 79, "column": 32, "nodeType": "2604", "messageId": "2605", "endLine": 79, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 1, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3165", "line": 10, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 11, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 20, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 20, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3166", "line": 86, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 86, "endColumn": 27}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 222, "column": 150, "nodeType": "2614", "endLine": 222, "endColumn": 153}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 3, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 3, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3151", "line": 6, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 11, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "3167", "line": 102, "column": 28, "nodeType": "2604", "messageId": "2605", "endLine": 102, "endColumn": 45}, {"ruleId": "2634", "severity": 1, "message": "3168", "line": 138, "column": 8, "nodeType": "2636", "endLine": 138, "endColumn": 10, "suggestions": "3169"}, {"ruleId": "2634", "severity": 1, "message": "3170", "line": 196, "column": 8, "nodeType": "2636", "endLine": 196, "endColumn": 27, "suggestions": "3171"}, {"ruleId": "2602", "severity": 1, "message": "2681", "line": 335, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 335, "endColumn": 40}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 351, "column": 25, "nodeType": "2614", "endLine": 351, "endColumn": 28}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 360, "column": 37, "nodeType": "2614", "endLine": 360, "endColumn": 40}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 365, "column": 157, "nodeType": "2614", "endLine": 365, "endColumn": 160}, {"ruleId": "2634", "severity": 1, "message": "3172", "line": 54, "column": 6, "nodeType": "2636", "endLine": 54, "endColumn": 32, "suggestions": "3173"}, {"ruleId": "2634", "severity": 1, "message": "3066", "line": 57, "column": 20, "nodeType": "2604", "endLine": 57, "endColumn": 27}, {"ruleId": "2634", "severity": 1, "message": "3066", "line": 68, "column": 23, "nodeType": "2604", "endLine": 68, "endColumn": 30}, {"ruleId": "2634", "severity": 1, "message": "3066", "line": 79, "column": 25, "nodeType": "2604", "endLine": 79, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2894", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2710", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2851", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2852", "line": 8, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 10, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3165", "line": 11, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2617", "line": 11, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 34}, {"ruleId": "2602", "severity": 1, "message": "3174", "line": 11, "column": 36, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 61}, {"ruleId": "2602", "severity": 1, "message": "3175", "line": 11, "column": 63, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 77}, {"ruleId": "2602", "severity": 1, "message": "3176", "line": 11, "column": 79, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 89}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 12, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 15, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3097", "line": 15, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 15, "column": 33, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2711", "line": 17, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 17, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "3177", "line": 19, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3178", "line": 19, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "3179", "line": 20, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 20, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 1, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 25}, {"ruleId": "2634", "severity": 1, "message": "2705", "line": 153, "column": 8, "nodeType": "2636", "endLine": 153, "endColumn": 21, "suggestions": "3180"}, {"ruleId": "2602", "severity": 1, "message": "2653", "line": 2, "column": 16, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2894", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2710", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2851", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2852", "line": 8, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3181", "line": 9, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 10, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3182", "line": 11, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "3183", "line": 11, "column": 29, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 50}, {"ruleId": "2602", "severity": 1, "message": "3165", "line": 11, "column": 52, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 63}, {"ruleId": "2602", "severity": 1, "message": "2617", "line": 11, "column": 65, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 76}, {"ruleId": "2602", "severity": 1, "message": "3174", "line": 11, "column": 78, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 103}, {"ruleId": "2602", "severity": 1, "message": "3175", "line": 11, "column": 105, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 119}, {"ruleId": "2602", "severity": 1, "message": "3176", "line": 11, "column": 121, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 131}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 12, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 15, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3097", "line": 15, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "3177", "line": 18, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3178", "line": 18, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "3179", "line": 19, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 20, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 20, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3184", "line": 21, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 21, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3185", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "3186", "line": 23, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2652", "line": 97, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 97, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3187", "line": 128, "column": 15, "nodeType": "2604", "messageId": "2605", "endLine": 128, "endColumn": 28}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 221, "column": 43, "nodeType": "3037", "messageId": "3038", "endLine": 221, "endColumn": 45}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 221, "column": 71, "nodeType": "3037", "messageId": "3038", "endLine": 221, "endColumn": 73}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 221, "column": 71, "nodeType": "3037", "messageId": "3038", "endLine": 221, "endColumn": 73}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 221, "column": 104, "nodeType": "3037", "messageId": "3038", "endLine": 221, "endColumn": 106}, {"ruleId": "2602", "severity": 1, "message": "2647", "line": 250, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 250, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2648", "line": 251, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 251, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2649", "line": 255, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 255, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3188", "line": 259, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 259, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3189", "line": 260, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 260, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "3190", "line": 264, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 264, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2646", "line": 268, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 268, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3191", "line": 273, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 273, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "3192", "line": 278, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 278, "endColumn": 29}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 554, "column": 57, "nodeType": "3037", "messageId": "3038", "endLine": 554, "endColumn": 59}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 554, "column": 88, "nodeType": "3037", "messageId": "3038", "endLine": 554, "endColumn": 90}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 555, "column": 53, "nodeType": "3037", "messageId": "3038", "endLine": 555, "endColumn": 55}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 555, "column": 84, "nodeType": "3037", "messageId": "3038", "endLine": 555, "endColumn": 86}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 558, "column": 94, "nodeType": "3037", "messageId": "3038", "endLine": 558, "endColumn": 96}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 558, "column": 125, "nodeType": "3037", "messageId": "3038", "endLine": 558, "endColumn": 127}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 560, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 560, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 560, "column": 92, "nodeType": "3037", "messageId": "3038", "endLine": 560, "endColumn": 94}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 561, "column": 57, "nodeType": "3037", "messageId": "3038", "endLine": 561, "endColumn": 59}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 561, "column": 88, "nodeType": "3037", "messageId": "3038", "endLine": 561, "endColumn": 90}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2894", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2710", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2851", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2852", "line": 8, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 10, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3165", "line": 11, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2617", "line": 11, "column": 23, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 34}, {"ruleId": "2602", "severity": 1, "message": "3174", "line": 11, "column": 36, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 61}, {"ruleId": "2602", "severity": 1, "message": "3175", "line": 11, "column": 63, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 77}, {"ruleId": "2602", "severity": 1, "message": "3176", "line": 11, "column": 79, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 89}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 12, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 15, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3097", "line": 15, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 15, "column": 33, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2711", "line": 17, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 17, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "3177", "line": 19, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3178", "line": 19, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "3179", "line": 20, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 20, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 2, "column": 16, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2653", "line": 2, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2894", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2710", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2851", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2852", "line": 8, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 10, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3165", "line": 11, "column": 52, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 63}, {"ruleId": "2602", "severity": 1, "message": "2617", "line": 11, "column": 65, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 76}, {"ruleId": "2602", "severity": 1, "message": "3174", "line": 11, "column": 78, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 103}, {"ruleId": "2602", "severity": 1, "message": "3175", "line": 11, "column": 105, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 119}, {"ruleId": "2602", "severity": 1, "message": "3176", "line": 11, "column": 121, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 131}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 12, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3097", "line": 15, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "3177", "line": 18, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3178", "line": 18, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "3179", "line": 19, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 20, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 20, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2718", "line": 25, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 25, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2719", "line": 27, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 27, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2720", "line": 28, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 28, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2721", "line": 29, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 29, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2722", "line": 30, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 30, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2723", "line": 31, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 31, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2724", "line": 32, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 32, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2725", "line": 33, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 33, "endColumn": 12}, {"ruleId": "2602", "severity": 1, "message": "2726", "line": 34, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 34, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2727", "line": 37, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 37, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2728", "line": 38, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 38, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2729", "line": 41, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 41, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2730", "line": 42, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 42, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2731", "line": 43, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 43, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2753", "line": 142, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 142, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2750", "line": 144, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 144, "endColumn": 27}, {"ruleId": "2634", "severity": 1, "message": "3193", "line": 152, "column": 8, "nodeType": "2636", "endLine": 152, "endColumn": 25, "suggestions": "3194"}, {"ruleId": "2759", "severity": 1, "message": "2760", "line": 468, "column": 47, "nodeType": "2761", "messageId": "2762", "endLine": 493, "endColumn": 22}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 892, "column": 69, "nodeType": "3037", "messageId": "3038", "endLine": 892, "endColumn": 71}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 892, "column": 89, "nodeType": "3037", "messageId": "3038", "endLine": 892, "endColumn": 91}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 892, "column": 89, "nodeType": "3037", "messageId": "3038", "endLine": 892, "endColumn": 91}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 892, "column": 120, "nodeType": "3037", "messageId": "3038", "endLine": 892, "endColumn": 122}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 893, "column": 65, "nodeType": "3037", "messageId": "3038", "endLine": 893, "endColumn": 67}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 893, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 893, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 893, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 893, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 893, "column": 116, "nodeType": "3037", "messageId": "3038", "endLine": 893, "endColumn": 118}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 896, "column": 111, "nodeType": "3037", "messageId": "3038", "endLine": 896, "endColumn": 113}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 896, "column": 131, "nodeType": "3037", "messageId": "3038", "endLine": 896, "endColumn": 133}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 896, "column": 131, "nodeType": "3037", "messageId": "3038", "endLine": 896, "endColumn": 133}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 896, "column": 162, "nodeType": "3037", "messageId": "3038", "endLine": 896, "endColumn": 164}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 899, "column": 65, "nodeType": "3037", "messageId": "3038", "endLine": 899, "endColumn": 67}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 899, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 899, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 899, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 899, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 899, "column": 116, "nodeType": "3037", "messageId": "3038", "endLine": 899, "endColumn": 118}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 902, "column": 65, "nodeType": "3037", "messageId": "3038", "endLine": 902, "endColumn": 67}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 902, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 902, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 902, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 902, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 902, "column": 116, "nodeType": "3037", "messageId": "3038", "endLine": 902, "endColumn": 118}, {"ruleId": "2602", "severity": 1, "message": "2653", "line": 2, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2894", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2710", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2851", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2852", "line": 8, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 10, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3097", "line": 15, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "3177", "line": 19, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3178", "line": 19, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "3179", "line": 20, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 20, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3049", "line": 139, "column": 17, "nodeType": "2604", "messageId": "2605", "endLine": 139, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3063", "line": 21, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 21, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "3064", "line": 52, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 52, "endColumn": 25}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 229, "column": 29, "nodeType": "2614", "endLine": 229, "endColumn": 76}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 230, "column": 29, "nodeType": "2614", "endLine": 230, "endColumn": 76}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 231, "column": 29, "nodeType": "2614", "endLine": 231, "endColumn": 74}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 240, "column": 37, "nodeType": "2614", "endLine": 240, "endColumn": 40}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 246, "column": 154, "nodeType": "2614", "endLine": 246, "endColumn": 157}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 95, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 95, "endColumn": 19}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 170, "column": 27, "nodeType": "2933", "messageId": "2929", "endLine": 170, "endColumn": 29}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 242, "column": 21, "nodeType": "2614", "endLine": 242, "endColumn": 24}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 251, "column": 33, "nodeType": "2614", "endLine": 251, "endColumn": 36}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 256, "column": 150, "nodeType": "2614", "endLine": 256, "endColumn": 153}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 442, "column": 52, "nodeType": "2933", "messageId": "2929", "endLine": 442, "endColumn": 54}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 83, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 83, "endColumn": 19}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 218, "column": 21, "nodeType": "2614", "endLine": 218, "endColumn": 24}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 227, "column": 33, "nodeType": "2614", "endLine": 227, "endColumn": 36}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 232, "column": 150, "nodeType": "2614", "endLine": 232, "endColumn": 153}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3165", "line": 10, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3176", "line": 10, "column": 63, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 73}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 11, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 19, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 19, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2652", "line": 89, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 89, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3195", "line": 91, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 91, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3196", "line": 91, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 91, "endColumn": 43}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 99, "column": 63, "nodeType": "2933", "messageId": "2929", "endLine": 99, "endColumn": 65}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 110, "column": 41, "nodeType": "2933", "messageId": "2929", "endLine": 110, "endColumn": 43}, {"ruleId": "2602", "severity": 1, "message": "3166", "line": 127, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 127, "endColumn": 27}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 178, "column": 25, "nodeType": "2933", "messageId": "2929", "endLine": 178, "endColumn": 27}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 181, "column": 25, "nodeType": "2933", "messageId": "2929", "endLine": 181, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "3197", "line": 182, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 182, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3160", "line": 197, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 197, "endColumn": 13}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 293, "column": 33, "nodeType": "2614", "endLine": 293, "endColumn": 36}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 298, "column": 150, "nodeType": "2614", "endLine": 298, "endColumn": 153}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 346, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 346, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 346, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 346, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 346, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 346, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 347, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 347, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 347, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 347, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 348, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 348, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 348, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 348, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 349, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 349, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 349, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 349, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 350, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 350, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 413, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 413, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 413, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 413, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 413, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 413, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 414, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 414, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 414, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 414, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 415, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 415, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 415, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 415, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 416, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 416, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 416, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 416, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 417, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 417, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 498, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 498, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 498, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 498, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 498, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 498, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 499, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 499, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 499, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 499, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 500, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 500, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 500, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 500, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 501, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 501, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 501, "column": 85, "nodeType": "3037", "messageId": "3038", "endLine": 501, "endColumn": 87}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 502, "column": 61, "nodeType": "3037", "messageId": "3038", "endLine": 502, "endColumn": 63}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 566, "column": 57, "nodeType": "3037", "messageId": "3038", "endLine": 566, "endColumn": 59}, {"ruleId": "3035", "severity": 1, "message": "3036", "line": 566, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 566, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 566, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 566, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 567, "column": 57, "nodeType": "3037", "messageId": "3038", "endLine": 567, "endColumn": 59}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 567, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 567, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 568, "column": 57, "nodeType": "3037", "messageId": "3038", "endLine": 568, "endColumn": 59}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 568, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 568, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 569, "column": 57, "nodeType": "3037", "messageId": "3038", "endLine": 569, "endColumn": 59}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 569, "column": 81, "nodeType": "3037", "messageId": "3038", "endLine": 569, "endColumn": 83}, {"ruleId": "3035", "severity": 1, "message": "3039", "line": 570, "column": 57, "nodeType": "3037", "messageId": "3038", "endLine": 570, "endColumn": 59}, {"ruleId": "2634", "severity": 1, "message": "3198", "line": 114, "column": 7, "nodeType": "2636", "endLine": 114, "endColumn": 40, "suggestions": "3199", "suppressions": "3200"}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2786", "line": 10, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 10, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 18, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 28, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 28, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2652", "line": 107, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 107, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "3195", "line": 112, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 112, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "3196", "line": 112, "column": 25, "nodeType": "2604", "messageId": "2605", "endLine": 112, "endColumn": 41}, {"ruleId": "3201", "severity": 1, "message": "3202", "line": 122, "column": 3, "nodeType": "3203", "messageId": "3204", "endLine": 124, "endColumn": 4}, {"ruleId": "2602", "severity": 1, "message": "3205", "line": 302, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 302, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "3166", "line": 312, "column": 18, "nodeType": "2604", "messageId": "2605", "endLine": 312, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3206", "line": 349, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 349, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "3160", "line": 385, "column": 3, "nodeType": "2604", "messageId": "2605", "endLine": 385, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "3207", "line": 391, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 391, "endColumn": 24}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 549, "column": 44, "nodeType": "2933", "messageId": "2929", "endLine": 549, "endColumn": 46}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 551, "column": 48, "nodeType": "2933", "messageId": "2929", "endLine": 551, "endColumn": 50}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 553, "column": 50, "nodeType": "2933", "messageId": "2929", "endLine": 553, "endColumn": 52}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 558, "column": 43, "nodeType": "2933", "messageId": "2929", "endLine": 558, "endColumn": 45}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 560, "column": 46, "nodeType": "2933", "messageId": "2929", "endLine": 560, "endColumn": 48}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 562, "column": 48, "nodeType": "2933", "messageId": "2929", "endLine": 562, "endColumn": 50}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 779, "column": 50, "nodeType": "2933", "messageId": "2929", "endLine": 779, "endColumn": 52}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 795, "column": 53, "nodeType": "2933", "messageId": "2929", "endLine": 795, "endColumn": 55}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 67, "column": 58, "nodeType": "2933", "messageId": "2929", "endLine": 67, "endColumn": 60}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 67, "column": 101, "nodeType": "2933", "messageId": "2929", "endLine": 67, "endColumn": 103}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 77, "column": 47, "nodeType": "2933", "messageId": "2929", "endLine": 77, "endColumn": 49}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 89, "column": 53, "nodeType": "2933", "messageId": "2929", "endLine": 89, "endColumn": 55}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 94, "column": 81, "nodeType": "2933", "messageId": "2929", "endLine": 94, "endColumn": 83}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 146, "column": 73, "nodeType": "2933", "messageId": "2929", "endLine": 146, "endColumn": 75}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 181, "column": 57, "nodeType": "2614", "endLine": 181, "endColumn": 60}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 215, "column": 45, "nodeType": "2933", "messageId": "2929", "endLine": 215, "endColumn": 47}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 226, "column": 49, "nodeType": "2933", "messageId": "2929", "endLine": 226, "endColumn": 51}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 231, "column": 77, "nodeType": "2933", "messageId": "2929", "endLine": 231, "endColumn": 79}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 283, "column": 69, "nodeType": "2933", "messageId": "2929", "endLine": 283, "endColumn": 71}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 319, "column": 53, "nodeType": "2614", "endLine": 319, "endColumn": 56}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 356, "column": 44, "nodeType": "2933", "messageId": "2929", "endLine": 356, "endColumn": 46}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 367, "column": 49, "nodeType": "2933", "messageId": "2929", "endLine": 367, "endColumn": 51}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 372, "column": 77, "nodeType": "2933", "messageId": "2929", "endLine": 372, "endColumn": 79}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 424, "column": 69, "nodeType": "2933", "messageId": "2929", "endLine": 424, "endColumn": 71}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 460, "column": 53, "nodeType": "2614", "endLine": 460, "endColumn": 56}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 521, "column": 47, "nodeType": "2933", "messageId": "2929", "endLine": 521, "endColumn": 49}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 532, "column": 49, "nodeType": "2933", "messageId": "2929", "endLine": 532, "endColumn": 51}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 537, "column": 77, "nodeType": "2933", "messageId": "2929", "endLine": 537, "endColumn": 79}, {"ruleId": "2931", "severity": 1, "message": "2932", "line": 593, "column": 69, "nodeType": "2933", "messageId": "2929", "endLine": 593, "endColumn": 71}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 80, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 80, "endColumn": 19}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 199, "column": 21, "nodeType": "2614", "endLine": 199, "endColumn": 24}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 208, "column": 33, "nodeType": "2614", "endLine": 208, "endColumn": 36}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 213, "column": 150, "nodeType": "2614", "endLine": 213, "endColumn": 153}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 11, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 11, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 13, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2650", "line": 14, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3097", "line": 14, "column": 21, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 31}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 14, "column": 33, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 41}, {"ruleId": "2602", "severity": 1, "message": "2797", "line": 20, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 20, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3160", "line": 119, "column": 5, "nodeType": "2604", "messageId": "2605", "endLine": 119, "endColumn": 13}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 165, "column": 37, "nodeType": "2614", "endLine": 165, "endColumn": 40}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 170, "column": 157, "nodeType": "2614", "endLine": 170, "endColumn": 160}, {"ruleId": "2602", "severity": 1, "message": "3208", "line": 6, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3209", "line": 9, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 9, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "3210", "line": 12, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "3211", "line": 13, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "3212", "line": 16, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2975", "line": 35, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 35, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2950", "line": 37, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 37, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "3213", "line": 39, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 39, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2976", "line": 46, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 46, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2977", "line": 46, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 46, "endColumn": 43}, {"ruleId": "2602", "severity": 1, "message": "2957", "line": 47, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 47, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 47, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 47, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2978", "line": 63, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 63, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2979", "line": 63, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 63, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "2980", "line": 67, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 67, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2981", "line": 67, "column": 32, "nodeType": "2604", "messageId": "2605", "endLine": 67, "endColumn": 53}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 80, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 80, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2982", "line": 626, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 626, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "3214", "line": 736, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 736, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "3215", "line": 753, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 753, "endColumn": 20}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1064, "column": 100, "nodeType": "2614", "endLine": 1064, "endColumn": 131}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1065, "column": 99, "nodeType": "2614", "endLine": 1065, "endColumn": 130}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 109, "column": 21, "nodeType": "2614", "endLine": 109, "endColumn": 24}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 115, "column": 25, "nodeType": "2614", "endLine": 115, "endColumn": 28}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 120, "column": 142, "nodeType": "2614", "endLine": 120, "endColumn": 145}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 183, "column": 87, "nodeType": "2614", "endLine": 183, "endColumn": 90}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 188, "column": 41, "nodeType": "2614", "endLine": 188, "endColumn": 135}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 189, "column": 41, "nodeType": "2614", "endLine": 189, "endColumn": 134}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 230, "column": 26, "nodeType": "2614", "endLine": 230, "endColumn": 55}, {"ruleId": "2602", "severity": 1, "message": "2618", "line": 134, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 134, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2618", "line": 134, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 134, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "2621", "line": 4, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "2622", "line": 5, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 5, "endColumn": 11}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 7, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 7, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3096", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 15, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 22, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 22, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2625", "line": 23, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 23, "endColumn": 17}, {"ruleId": "2602", "severity": 1, "message": "3216", "line": 47, "column": 7, "nodeType": "2604", "messageId": "2605", "endLine": 47, "endColumn": 15}, {"ruleId": "2602", "severity": 1, "message": "3157", "line": 70, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 70, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2626", "line": 78, "column": 9, "nodeType": "2604", "messageId": "2605", "endLine": 78, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2957", "line": 85, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 85, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 85, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 85, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2627", "line": 93, "column": 20, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2628", "line": 106, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 106, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2629", "line": 111, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 111, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2630", "line": 116, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 116, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2631", "line": 120, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 120, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2632", "line": 124, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 124, "endColumn": 26}, {"ruleId": "2602", "severity": 1, "message": "2633", "line": 128, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 128, "endColumn": 26}, {"ruleId": "2634", "severity": 1, "message": "2635", "line": 139, "column": 8, "nodeType": "2636", "endLine": 139, "endColumn": 19, "suggestions": "3217"}, {"ruleId": "2602", "severity": 1, "message": "2793", "line": 187, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 187, "endColumn": 21}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 344, "column": 113, "nodeType": "2614", "endLine": 344, "endColumn": 160}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 442, "column": 154, "nodeType": "2614", "endLine": 442, "endColumn": 157}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 449, "column": 165, "nodeType": "2614", "endLine": 449, "endColumn": 169}, {"ruleId": "2602", "severity": 1, "message": "3093", "line": 2, "column": 44, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 58}, {"ruleId": "2602", "severity": 1, "message": "3218", "line": 8, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3093", "line": 2, "column": 44, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 58}, {"ruleId": "2602", "severity": 1, "message": "2708", "line": 1, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "3097", "line": 1, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 1, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "3219", "line": 2, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 2, "endColumn": 28}, {"ruleId": "2602", "severity": 1, "message": "2623", "line": 8, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 8, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3220", "line": 12, "column": 30, "nodeType": "2604", "messageId": "2605", "endLine": 12, "endColumn": 49}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 13, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "2914", "line": 16, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 16, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2971", "line": 17, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 17, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "2972", "line": 18, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 18, "endColumn": 18}, {"ruleId": "2602", "severity": 1, "message": "2615", "line": 24, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 24, "endColumn": 19}, {"ruleId": "2602", "severity": 1, "message": "2973", "line": 26, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 26, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "3221", "line": 27, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 27, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2974", "line": 30, "column": 10, "nodeType": "2604", "messageId": "2605", "endLine": 30, "endColumn": 13}, {"ruleId": "2602", "severity": 1, "message": "3222", "line": 54, "column": 7, "nodeType": "2604", "messageId": "2605", "endLine": 54, "endColumn": 23}, {"ruleId": "2602", "severity": 1, "message": "2975", "line": 152, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 152, "endColumn": 24}, {"ruleId": "2602", "severity": 1, "message": "2950", "line": 154, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 154, "endColumn": 27}, {"ruleId": "2602", "severity": 1, "message": "2976", "line": 163, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 163, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "2977", "line": 163, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 163, "endColumn": 43}, {"ruleId": "2602", "severity": 1, "message": "2957", "line": 164, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 164, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 164, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 164, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2978", "line": 182, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 182, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2979", "line": 182, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 182, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "2980", "line": 186, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 186, "endColumn": 30}, {"ruleId": "2602", "severity": 1, "message": "2981", "line": 186, "column": 32, "nodeType": "2604", "messageId": "2605", "endLine": 186, "endColumn": 53}, {"ruleId": "2602", "severity": 1, "message": "2829", "line": 200, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 200, "endColumn": 19}, {"ruleId": "2634", "severity": 1, "message": "3223", "line": 494, "column": 8, "nodeType": "2636", "endLine": 494, "endColumn": 18, "suggestions": "3224"}, {"ruleId": "2602", "severity": 1, "message": "2982", "line": 764, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 764, "endColumn": 29}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1275, "column": 96, "nodeType": "2614", "endLine": 1275, "endColumn": 127}, {"ruleId": "2612", "severity": 1, "message": "2613", "line": 1276, "column": 95, "nodeType": "2614", "endLine": 1276, "endColumn": 126}, {"ruleId": "2602", "severity": 1, "message": "3225", "line": 4, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 4, "endColumn": 29}, {"ruleId": "2602", "severity": 1, "message": "3049", "line": 61, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 61, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "2610", "line": 6, "column": 8, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3042", "line": 145, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 145, "endColumn": 20}, {"ruleId": "2602", "severity": 1, "message": "2611", "line": 150, "column": 22, "nodeType": "2604", "messageId": "2605", "endLine": 150, "endColumn": 32}, {"ruleId": "2602", "severity": 1, "message": "2796", "line": 6, "column": 269, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 279}, {"ruleId": "2602", "severity": 1, "message": "3226", "line": 6, "column": 379, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 399}, {"ruleId": "2602", "severity": 1, "message": "3227", "line": 6, "column": 517, "nodeType": "2604", "messageId": "2605", "endLine": 6, "endColumn": 538}, {"ruleId": "2602", "severity": 1, "message": "3228", "line": 13, "column": 78, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 91}, {"ruleId": "2602", "severity": 1, "message": "3229", "line": 13, "column": 93, "nodeType": "2604", "messageId": "2605", "endLine": 13, "endColumn": 116}, {"ruleId": "2602", "severity": 1, "message": "2801", "line": 14, "column": 31, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 37}, {"ruleId": "2602", "severity": 1, "message": "3230", "line": 14, "column": 63, "nodeType": "2604", "messageId": "2605", "endLine": 14, "endColumn": 70}, {"ruleId": "2602", "severity": 1, "message": "3231", "line": 15, "column": 55, "nodeType": "2604", "messageId": "2605", "endLine": 15, "endColumn": 61}, {"ruleId": "2602", "severity": 1, "message": "2624", "line": 17, "column": 32, "nodeType": "2604", "messageId": "2605", "endLine": 17, "endColumn": 44}, {"ruleId": "2602", "severity": 1, "message": "3232", "line": 29, "column": 13, "nodeType": "2604", "messageId": "2605", "endLine": 29, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "3233", "line": 29, "column": 24, "nodeType": "2604", "messageId": "2605", "endLine": 29, "endColumn": 33}, {"ruleId": "2602", "severity": 1, "message": "3234", "line": 49, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 49, "endColumn": 25}, {"ruleId": "2602", "severity": 1, "message": "3235", "line": 49, "column": 27, "nodeType": "2604", "messageId": "2605", "endLine": 49, "endColumn": 43}, {"ruleId": "2602", "severity": 1, "message": "3236", "line": 50, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 50, "endColumn": 16}, {"ruleId": "2602", "severity": 1, "message": "3237", "line": 51, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 51, "endColumn": 14}, {"ruleId": "2602", "severity": 1, "message": "2647", "line": 52, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 52, "endColumn": 21}, {"ruleId": "2602", "severity": 1, "message": "3046", "line": 93, "column": 12, "nodeType": "2604", "messageId": "2605", "endLine": 93, "endColumn": 18}, {"ruleId": "2634", "severity": 1, "message": "3238", "line": 239, "column": 8, "nodeType": "2636", "endLine": 239, "endColumn": 51, "suggestions": "3239"}, {"ruleId": "2634", "severity": 1, "message": "3240", "line": 579, "column": 41, "nodeType": "2604", "endLine": 579, "endColumn": 48}, {"ruleId": "2634", "severity": 1, "message": "3241", "line": 595, "column": 8, "nodeType": "2636", "endLine": 595, "endColumn": 64, "suggestions": "3242"}, {"ruleId": "2634", "severity": 1, "message": "3243", "line": 645, "column": 8, "nodeType": "2636", "endLine": 645, "endColumn": 38, "suggestions": "3244"}, {"ruleId": "2602", "severity": 1, "message": "2648", "line": 2003, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 2003, "endColumn": 22}, {"ruleId": "2602", "severity": 1, "message": "2649", "line": 2007, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 2007, "endColumn": 21}, {"ruleId": "2634", "severity": 1, "message": "3245", "line": 53, "column": 8, "nodeType": "2636", "endLine": 53, "endColumn": 72, "suggestions": "3246"}, {"ruleId": "2634", "severity": 1, "message": "3247", "line": 88, "column": 6, "nodeType": "2636", "endLine": 88, "endColumn": 17, "suggestions": "3248"}, {"ruleId": "2634", "severity": 1, "message": "3249", "line": 225, "column": 6, "nodeType": "2636", "endLine": 225, "endColumn": 17, "suggestions": "3250"}, {"ruleId": "2634", "severity": 1, "message": "3251", "line": 78, "column": 6, "nodeType": "2636", "endLine": 78, "endColumn": 29, "suggestions": "3252"}, {"ruleId": "2602", "severity": 1, "message": "3253", "line": 48, "column": 11, "nodeType": "2604", "messageId": "2605", "endLine": 48, "endColumn": 31}, "no-unused-vars", "'hasQueryParams' is assigned a value but never used.", "Identifier", "unusedVar", "'checkLicense' is defined but never used.", "'getEmpresa' is defined but never used.", "'showToast' is assigned a value but never used.", "'setShowToast' is assigned a value but never used.", "'LeftMenu' is defined but never used.", "'setSidebar' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'AsyncSelect' is defined but never used.", "'register' is defined but never used.", "'getEmpresas' is defined but never used.", "'user' is assigned a value but never used.", "'selectData' is assigned a value but never used.", "'mapResponseToValuesAndLabels' is assigned a value but never used.", "'InputMask' is defined but never used.", "'cep' is defined but never used.", "'AuthContext' is defined but never used.", "'BsPlusCircle' is defined but never used.", "'FaTrash' is defined but never used.", "'userImg' is assigned a value but never used.", "'setErrImg' is assigned a value but never used.", "'handleMinimoChange' is assigned a value but never used.", "'handleMaximoChange' is assigned a value but never used.", "'incrementMinimo' is assigned a value but never used.", "'decrementMinimo' is assigned a value but never used.", "'incrementMaximo' is assigned a value but never used.", "'decrementMaximo' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'minimo'. Either include it or remove the dependency array.", "ArrayExpression", ["3254"], "'InputMoney' is defined but never used.", "'valuePreco' is assigned a value but never used.", "'calcularMaiorValor' is assigned a value but never used.", "'setCalcularMaiorValor' is assigned a value but never used.", "'calcularMedia' is assigned a value but never used.", "'setCalcularMedia' is assigned a value but never used.", "'type' is assigned a value but never used.", ["3255"], "'onChange' is assigned a value but never used.", "'isFocused' is assigned a value but never used.", "'handleFocus' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'useEffect' is defined but never used.", "'registerAdicionais' is defined but never used.", "'idEmpresa' is assigned a value but never used.", "'useRef' is defined but never used.", "'useLocation' is defined but never used.", "'Cookies' is defined but never used.", "'imgTeste' is defined but never used.", "React Hook useEffect has a missing dependency: 'EXPIRATION_TIME'. Either include it or remove the dependency array.", ["3256"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'getEstados' is defined but never used.", "'getMunicipios' is defined but never used.", "'userName' is assigned a value but never used.", "'userEmail' is assigned a value but never used.", "'selectedDays' is assigned a value but never used.", "'setSelectedDays' is assigned a value but never used.", "'startTime' is assigned a value but never used.", "'setStartTime' is assigned a value but never used.", "'endTime' is assigned a value but never used.", "'setEndTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'horariosUnicos'. Either include it or remove the dependency array.", ["3257"], "'Input' is defined but never used.", "'selectedOption' is assigned a value but never used.", "'handleDeleteHorario' is assigned a value but never used.", "'handleOptionChange' is assigned a value but never used.", "'horariosUnicos' is assigned a value but never used.", "'handleDayChange' is assigned a value but never used.", "'handleTimeChange' is assigned a value but never used.", "'handleAddHorario' is assigned a value but never used.", "'isDayAlreadySelectedInHorario' is assigned a value but never used.", "'FiIcons' is defined but never used.", "'IoArrowRedo' is defined but never used.", "'IoArrowUndo' is defined but never used.", "'FaRegEdit' is defined but never used.", "'FaRegTrashAlt' is defined but never used.", "'TbDotsVertical' is defined but never used.", "'setEtapaAtiva' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'idEmpresa', 'objIdEmpresa', 'userID', and 'vinculo_empresa'. Either include them or remove the dependency array.", ["3258"], ["3259"], "React Hook useEffect has missing dependencies: 'formik', 'idEmpresa', 'itemObjId', 'objIdEmpresa', 'userID', and 'vinculo_empresa'. Either include them or remove the dependency array.", ["3260"], "React Hook useEffect has missing dependencies: 'callApiAdicionais', 'callApiSabores', 'selectDataAdicionais', and 'selectDataSabores'. Either include them or remove the dependency array.", ["3261"], "React Hook useEffect has missing dependencies: 'idEmpresa', 'location.state?.item', and 'objIdProduto'. Either include them or remove the dependency array.", ["3262"], "'horariosFuncionamento' is assigned a value but never used.", "'currentTime' is assigned a value but never used.", "'loadingData' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'showError' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'openStatus'. Either include it or remove the dependency array.", ["3263"], "React Hook useEffect has a missing dependency: 'isRequirementMet'. Either include it or remove the dependency array.", ["3264"], "'updateQuantity' is assigned a value but never used.", "'useState' is defined but never used.", "'ModalItemAdicionado' is defined but never used.", "'CryptoJS' is defined but never used.", "'toast' is defined but never used.", "'secretKey' is assigned a value but never used.", "'FaPlus' is defined but never used.", "'FaMinus' is defined but never used.", "'ModalConfirmInfoCliente' is defined but never used.", "'Controller' is defined but never used.", "'Select' is defined but never used.", "'setKey' is defined but never used.", "'setLanguage' is defined but never used.", "'setRegion' is defined but never used.", "'fromAddress' is defined but never used.", "'fromLatLng' is defined but never used.", "'fromPlaceId' is defined but never used.", "'setLocationType' is defined but never used.", "'geocode' is defined but never used.", "'RequestType' is defined but never used.", "'GoogleMap' is defined but never used.", "'Marker' is defined but never used.", "'StandaloneSearchBox' is defined but never used.", "'DirectionsService' is defined but never used.", "'DirectionsRenderer' is defined but never used.", "'isBrazilianAddress' is assigned a value but never used.", "'libraries' is assigned a value but never used.", "'register' is assigned a value but never used.", "'control' is assigned a value but never used.", "'handleSubmit' is assigned a value but never used.", "'errors' is assigned a value but never used.", "'itens<PERSON><PERSON><PERSON><PERSON>' is assigned a value but never used.", "'setItensCarrinho' is assigned a value but never used.", "'carrinho' is assigned a value but never used.", "'set<PERSON><PERSON><PERSON><PERSON>' is assigned a value but never used.", "'showConfirmInfo' is assigned a value but never used.", "'setShowConfirmInfo' is assigned a value but never used.", "'infoPersonalData' is assigned a value but never used.", "'setInfoPersonalData' is assigned a value but never used.", "'whatsappData' is assigned a value but never used.", "'setWhatsappData' is assigned a value but never used.", "'namePersonData' is assigned a value but never used.", "'setNamePersonData' is assigned a value but never used.", "'addressComplete' is assigned a value but never used.", "'options' is assigned a value but never used.", "'setOptions' is assigned a value but never used.", "'address' is assigned a value but never used.", "'setAddress' is assigned a value but never used.", "'selectedOptions' is assigned a value but never used.", "'setSelectedOptions' is assigned a value but never used.", "'searchBox' is assigned a value but never used.", "'setSearchBox' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'cepFound', 'ruaFound', 'ruaTemp', 'ruaTemp', 'ruaFound', 'bairroFound', 'bairroTemp', 'bairroTemp', 'bairroFound', 'cepFound', 'ruaFound', 'estadoTemp', 'estadoTemp', 'cepFound', 'ruaFound', 'cidadeTemp', 'cidadeTemp', 'cepFound', 'ruaFound', 'cepTemp', 'cepTemp', 'cepFound'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useEffect has a missing dependency: 'regionType'. Either include it or remove the dependency array.", ["3265"], "React Hook useEffect has missing dependencies: 'calcularValorEntrega', 'carrinho.length', 'enderecoEmpresa', 'idEmpresa', and 'localPersonInfo'. Either include them or remove the dependency array.", ["3266"], "React Hook useEffect has a missing dependency: 'isSearchingClient'. Either include it or remove the dependency array.", ["3267"], "React Hook useEffect has missing dependencies: 'externalId', 'idEmpresa', 'navigate', and 'nomeEmpresaForUrl'. Either include them or remove the dependency array.", ["3268"], "'obtemValorItemTotal' is assigned a value but never used.", "'valorAdicionais' is assigned a value but never used.", "'FaCheck' is defined but never used.", "'animationTrigger' is assigned a value but never used.", "'descriptionRef' is assigned a value but never used.", ["3269"], ["3270"], "'handleButtonClick' is assigned a value but never used.", ["3271"], "'handleIncrement' is assigned a value but never used.", "'totalCurrent' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'handleDecrement' is assigned a value but never used.", "'getAnimationClass' is assigned a value but never used.", "'handleMaxCharDescricao' is assigned a value but never used.", "'AiIcons' is defined but never used.", "'SlIcons' is defined but never used.", "'isSubmitting' is assigned a value but never used.", "'setIsSubmitting' is assigned a value but never used.", "'isLoged' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQrCode'. Either include it or remove the dependency array.", ["3272"], "'handleBack' is assigned a value but never used.", "'getCategorias' is defined but never used.", "'registerItem' is defined but never used.", "'getBairros' is defined but never used.", "'useNavigate' is defined but never used.", "'BiIcons' is defined but never used.", "'MdFastfood' is defined but never used.", "'MdLocationOn' is defined but never used.", "'FaEdit' is defined but never used.", "'FaMapMarkerAlt' is defined but never used.", "'ImageUploading' is defined but never used.", "'CImage' is defined but never used.", "'updateRaioEntregaEmpresa' is defined but never used.", "'deleteRaioEntrega' is defined but never used.", "'updateTypeRegion' is defined but never used.", "'updateBairroEntregaEmpresa' is defined but never used.", "'objIdEmpresa' is assigned a value but never used.", "'setRefresh' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'formik', 'idEmpresa', and 'userID'. Either include them or remove the dependency array.", ["3273"], ["3274"], "jsx-a11y/iframe-has-title", "<iframe> elements must have a unique title property.", "'FaSortNumericDownAlt' is defined but never used.", "'cepNumber' is assigned a value but never used.", "'bairro' is assigned a value but never used.", "'logradouro' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'handleSearchCep'. Either include it or remove the dependency array.", ["3275"], "React Hook useCallback has a missing dependency: 'formik'. Either include it or remove the dependency array.", ["3276"], "'valid' is defined but never used.", "'getAllInvoicesAsaas' is defined but never used.", "'criarAssinaturaAsaasBolPix' is defined but never used.", "'getLastPendingInvoiceAsaas' is defined but never used.", "'useAsync' is defined but never used.", "'navigate' is assigned a value but never used.", "'expirationDate' is assigned a value but never used.", "'createdAt' is assigned a value but never used.", "'cnpj' is assigned a value but never used.", "'razao' is assigned a value but never used.", "'nomeEmpresa' is assigned a value but never used.", "'setIsPromotional' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userID'. Either include it or remove the dependency array.", ["3277"], "'handleDateChange' is assigned a value but never used.", "'ip' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'idEmpresa' and 'userID'. Either include them or remove the dependency array.", ["3278"], "'handleSeePlans' is assigned a value but never used.", "'handleOpenFaturas' is assigned a value but never used.", "'num_cartao' is assigned a value but never used.", "'validade' is assigned a value but never used.", "'cvv' is assigned a value but never used.", "'telefone' is assigned a value but never used.", "'valorCentsConvertido' is assigned a value but never used.", "'ModalEditUser' is defined but never used.", "'BarContext' is defined but never used.", "'useFormik' is defined but never used.", "'Yup' is defined but never used.", "'FaIcons' is defined but never used.", "'date' is defined but never used.", "'order' is assigned a value but never used.", "'setOrder' is assigned a value but never used.", "'columnOrder' is assigned a value but never used.", "'setColumnOrder' is assigned a value but never used.", "'users' is assigned a value but never used.", "'setUsers' is assigned a value but never used.", "'filter' is assigned a value but never used.", "'setFilter' is assigned a value but never used.", "'_idUserEdit' is assigned a value but never used.", "'set_idUserEdit' is assigned a value but never used.", "'usernameEdit' is assigned a value but never used.", "'setUsernameEdit' is assigned a value but never used.", "'emailEdit' is assigned a value but never used.", "'setEmailEdit' is assigned a value but never used.", "'roleEdit' is assigned a value but never used.", "'setRoleEdit' is assigned a value but never used.", "'setResult' is assigned a value but never used.", "'showEditUser' is assigned a value but never used.", "'setEditUser' is assigned a value but never used.", "'setselectData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'user._id'. Either include it or remove the dependency array.", ["3279"], "'handleEdit' is assigned a value but never used.", "'QRCode' is defined but never used.", "'pedidosEmAnalise' is assigned a value but never used.", "'pedidosEmProducao' is assigned a value but never used.", "'pedidosEmEntrega' is assigned a value but never used.", "'pedidosFinalizadosHoje' is assigned a value but never used.", "'faturamentoTotalHoje' is assigned a value but never used.", "'pedidosEntregues' is assigned a value but never used.", "'pedidosRetirados' is assigned a value but never used.", "'periodo' is assigned a value but never used.", "'datePeriod' is assigned a value but never used.", "'handleChangePeriodo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["3280"], "'pedidosCriadosHoje' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPedidosFinalizados'. Either include it or remove the dependency array.", ["3281"], "'moment' is defined but never used.", ["3282"], "React Hook useEffect has missing dependencies: 'idEmpresa', 'pedidosEntregues', 'userID', and 'vinculo_empresa'. Either include them or remove the dependency array.", ["3283"], "'px2vw' is defined but never used.", "'Link' is defined but never used.", "'useInView' is defined but never used.", "'categorias' is assigned a value but never used.", "'setCategorias' is assigned a value but never used.", "'itens' is assigned a value but never used.", "'setItens' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'findNextClosingTime', 'findNextOpeningTime', 'isRestaurantOpen', 'loadingData', and 'toastShown'. Either include them or remove the dependency array.", ["3284"], "'getIndex' is assigned a value but never used.", ["3285"], "'handleCarrin<PERSON>' is assigned a value but never used.", "'handleItemCard' is assigned a value but never used.", "'deleteCategoria' is defined but never used.", "'List' is defined but never used.", "'imageItens' is defined but never used.", "'FaFilter' is defined but never used.", "'ref' is assigned a value but never used.", "'horizontalScrollRef' is assigned a value but never used.", "'empresas' is assigned a value but never used.", "'setEmpresas' is assigned a value but never used.", "'setNum' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'empresaObjId', 'idEmpresa', 'userID', and 'vinculo_empresa'. Either include them or remove the dependency array.", ["3286"], "React Hook useEffect has a missing dependency: 'handleClickOutside'. Either include it or remove the dependency array.", ["3287"], "'handleOrder' is assigned a value but never used.", "'copied' is assigned a value but never used.", "no-eval", "eval can be harmful.", "CallExpression", "unexpected", "'hoverItem' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "'useQuery' is defined but never used.", "'axios' is defined but never used.", "'setLoadingData' is assigned a value but never used.", "'setShowError' is assigned a value but never used.", "'setErrorMessage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'toastShown'. Either include it or remove the dependency array.", ["3288"], ["3289"], ["3290"], "'handleDragStart' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "'Modal' is defined but never used.", "'Button' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleClickOutsideItem'. Either include it or remove the dependency array.", ["3291"], ["3292"], "'vinculo_empresa' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'objIdEmpresa'. Either include it or remove the dependency array.", ["3293"], "React Hook useMemo has missing dependencies: 'handleEdit' and 'user._id'. Either include them or remove the dependency array.", ["3294"], "'ClosedCaixa' is defined but never used.", "'OpenedCaixa' is defined but never used.", "'sidebar' is assigned a value but never used.", "'logoGoogle' is defined but never used.", "no-dupe-keys", "Duplicate key 'backgroundSize'.", "ObjectExpression", "'isDisabled' is defined but never used.", "React Hook useEffect has missing dependencies: 'stateParam', 'type', and 'user._id'. Either include them or remove the dependency array.", ["3295"], "'getIdGrupo' is assigned a value but never used.", "'handleCloseOpt' is defined but never used.", "array-callback-return", "Array.prototype.map() expects a return value from arrow function.", "expectedInside", "'removeFromLocalStorage' is defined but never used.", "'AiOutlineArrowRight' is defined but never used.", "'IoMdExit' is defined but never used.", "'calc' is defined but never used.", "'set' is defined but never used.", "'empresaObjId' is assigned a value but never used.", "'tipoImpressao' is assigned a value but never used.", "'setTipoImpressao' is assigned a value but never used.", "'adicionais' is assigned a value but never used.", "'setAdicionais' is assigned a value but never used.", "'currentEditingItem' is assigned a value but never used.", "'setCurrentEditingItem' is assigned a value but never used.", "'subTotalAdicionais' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'empresas' and 'user._id'. Either include them or remove the dependency array.", ["3296"], "React Hook useMemo has a missing dependency: 'handleEdit'. Either include it or remove the dependency array.", ["3297"], ["3298"], "'focusSearch' is assigned a value but never used.", ["3299"], ["3300"], "'Br' is defined but never used.", "'Cut' is defined but never used.", "'Line' is defined but never used.", "'Printer' is defined but never used.", "'Text' is defined but never used.", "'Row' is defined but never used.", "'render' is defined but never used.", "'pdfMake' is defined but never used.", "'html2canvas' is defined but never used.", "'imageUrl' is defined but never used.", "'getUser' is defined but never used.", "'updateStatusPrint' is defined but never used.", "'getPedidosByStatus' is defined but never used.", "'ModalUserOptions' is assigned a value but never used.", "'isLoadingSimples' is assigned a value but never used.", "'setIsLoadingSimples' is assigned a value but never used.", "'isLoadingFinalizados' is assigned a value but never used.", "'setIsLoadingFinalizados' is assigned a value but never used.", "'imageDataURL' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchPedidosFinalizados' and 'fetchPedidosSimples'. Either include them or remove the dependency array.", ["3301"], "React Hook useEffect has missing dependencies: 'fetchDataAndSetPedidosForFilters' and 'filtroSelecionado'. Either include them or remove the dependency array.", ["3302"], "React Hook useEffect has missing dependencies: 'apiUrl', 'fetchPedidosFinalizados', 'fetchPedidosSimples', 'objIdEmpresa', and 'userID'. Either include them or remove the dependency array.", ["3303"], ["3304"], "'deleteEmpresa' is defined but never used.", "'ModalEditEmpresa' is defined but never used.", "'showEditEmpresa' is assigned a value but never used.", "'setEditEmpresa' is assigned a value but never used.", ["3305"], "'handleCadastro' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'empresaObjId'. Either include it or remove the dependency array.", ["3306"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'qrCodeData' is assigned a value but never used.", "'LuPlusCircle' is defined but never used.", "'userID' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'empresaObjId' and 'idEmpresa'. Either include them or remove the dependency array.", ["3307"], "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "Unexpected mix of '||' and '&&'. Use parentheses to clarify the intended order of operations.", "'FaComments' is defined but never used.", "'IoMdClose' is defined but never used.", "'userParse' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCustomResponses'. Either include it or remove the dependency array.", ["3308"], "'setLinkObj' is assigned a value but never used.", "'socket' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'apiUrl', 'checkImportProgress', 'checkImportStatus', 'objIdEmpresa', and 'updateImportFlag'. Either include them or remove the dependency array.", ["3309"], "'response' is assigned a value but never used.", "The 'arrayUsers' array makes the dependencies of useMemo Hook (at line 296) change on every render. To fix this, wrap the initialization of 'arrayUsers' in its own useMemo() Hook.", "VariableDeclarator", "'CgEnter' is defined but never used.", "'FaPrint' is defined but never used.", "'GiCardExchange' is defined but never used.", "'TbCurrencyDollar' is defined but never used.", "'getInstanceStatus' is defined but never used.", "React Hook useEffect has missing dependencies: 'allEmpresas.length', 'limit', and 'user._id'. Either include them or remove the dependency array.", ["3310"], "'cleanFilter' is assigned a value but never used.", "React Hook useMemo has a missing dependency: 'handleCreateInstance'. Either include it or remove the dependency array.", ["3311"], "'SlRefresh' is defined but never used.", "'IoMdOpen' is defined but never used.", "'isPedidoLoading' is assigned a value but never used.", "'refetch' is assigned a value but never used.", "React Hook useMemo does nothing when called with only one argument. Did you forget to pass an array of dependencies?", "'finalValue' is assigned a value but never used.", "'Pagination' is defined but never used.", "'Navigate' is defined but never used.", "React Hook useEffect has a missing dependency: 'setRedirectToPlanos'. Either include it or remove the dependency array.", ["3312"], "'getDeviceInfo' is defined but never used.", "'uuidv4' is defined but never used.", "'HelpWidget' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'user'. Either include it or remove the dependency array.", ["3313"], "'refresh' is assigned a value but never used.", "'finalizarAtendimento' is assigned a value but never used.", "'cancelarAtendimento' is assigned a value but never used.", "'atualizarFila' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'atendimentosPendentes.length'. Either include it or remove the dependency array.", ["3314"], "'setTotalEtapasConfiguracaoInicial' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'apiUrl' and 'handleNotify'. Either include them or remove the dependency array.", ["3315"], "React Hook useEffect has a missing dependency: 'handleResize'. Either include it or remove the dependency array.", ["3316"], "Duplicate key 'paddingBottom'.", "Duplicate key 'display'.", ["3317"], ["3318"], "'InputPropsAntd' is defined but never used.", "'AppConfigContext' is defined but never used.", "'apiInstancesWpp' is assigned a value but never used.", "'PermissionGate' is defined but never used.", "'useContext' is defined but never used.", "'visibleDropdown' is assigned a value but never used.", "'setVisibleDropdown' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'errImg' is assigned a value but never used.", "'images' is assigned a value but never used.", "'maxNumber' is assigned a value but never used.", "'maxMbFileSize' is assigned a value but never used.", "'onChangeImg' is assigned a value but never used.", ["3319"], ["3320"], "'Circle' is defined but never used.", "'updateLocationInDatabase' is assigned a value but never used.", "'Profile' is defined but never used.", "'Chat' is defined but never used.", "'mockMessages' is defined but never used.", "'contactUserPhoto' is defined but never used.", "'sendMessage' is defined but never used.", "'generateObjectId' is assigned a value but never used.", "'expectingQrCode' is assigned a value but never used.", "'setExpectingQrCode' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'cleanupGlobalSocket' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'apiUrl'. Either include it or remove the dependency array.", ["3321"], "React Hook useEffect has missing dependencies: 'chats.length', 'empresaID', 'fetchChats', 'generatingQrCode', 'isInitialLoad', 'isLoged', 'markChatAsRead', 'qrCodeExpired', 'qrCodeImg', 'selectedChat', and 'userRequestedQrCode'. Either include them or remove the dependency array.", ["3322"], "React Hook useEffect has a missing dependency: 'checkConnectionStatus'. Either include it or remove the dependency array.", ["3323"], "React Hook useEffect has a missing dependency: 'fetchProfilePicture'. Either include it or remove the dependency array.", ["3324"], "no-useless-escape", "Unnecessary escape character: \\(.", "Literal", "unnecessaryEscape", ["3325", "3326"], "Unnecessary escape character: \\).", ["3327", "3328"], "'page' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'empresaID', 'fetchChats', and 'token'. Either include them or remove the dependency array.", ["3329"], "React Hook useEffect has a missing dependency: 'fetchMyProfilePicture'. Either include it or remove the dependency array.", ["3330"], "React Hook useEffect has a missing dependency: 'fetchUnreadCounts'. Either include it or remove the dependency array.", ["3331"], ["3332"], "React Hook useEffect has a missing dependency: 'handleScroll'. Either include it or remove the dependency array.", ["3333"], "React Hook useEffect has missing dependencies: 'empresaID', 'markChatAsRead', 'messagesCache', 'shouldScrollToBottom', and 'token'. Either include them or remove the dependency array.", ["3334"], "React Hook useCallback has a missing dependency: 'showMobileChat'. Either include it or remove the dependency array.", ["3335"], "'inView' is assigned a value but never used.", "'Field' is defined but never used.", "'calcular_maior_valor' is assigned a value but never used.", "'calcular_media' is assigned a value but never used.", ["3336"], ["3337"], "'updateAdicionaisGroup' is defined but never used.", "'location' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'formik' and 'itemAdicionalToEdit'. Either include them or remove the dependency array. If 'setAdicional' needs the current value of 'itemAdicionalToEdit', you can also switch to useReducer instead of useState and read 'itemAdicionalToEdit' in the reducer.", ["3338"], "'existing' is assigned a value but never used.", "'ImCheckboxChecked' is defined but never used.", "'useParams' is defined but never used.", "'handleKeepBuy' is assigned a value but never used.", "'nomeEmpresaForUrl' is assigned a value but never used.", "'getRevendas' is defined but never used.", "'callApi' is defined but never used.", "'setDiaHorarioDisp' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_diaHorarioDispToEdit' and 'setDiaHorarioDispToEdit'. Either include them or remove the dependency array. If 'setDiaHorarioDispToEdit' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["3339"], "React Hook useEffect has missing dependencies: '_modeloToEdit' and 'formik'. Either include them or remove the dependency array.", ["3340"], "React Hook useEffect has missing dependencies: 'caixa', 'data', 'empresaId', 'empresa_id', and 'vinculoEmpresa'. Either include them or remove the dependency array. You can also do a functional update 'setCaixa(c => ...)' if you only need 'caixa' in the 'setCaixa' call.", ["3341"], "'updateTempoEntregaEmpresa' is defined but never used.", "'ImprimirPedido' is defined but never used.", "'updateUser' is defined but never used.", "'FaMotorcycle' is defined but never used.", "'FaStore' is defined but never used.", "'BiComment' is defined but never used.", ["3342"], "'Loading' is defined but never used.", "'calcularDistancia' is defined but never used.", "'deleteEnderecoCliente' is defined but never used.", "'GrLocation' is defined but never used.", "'RiCloseCircleLine' is defined but never used.", "'updateClienteAddress' is defined but never used.", "'valorRestante' is assigned a value but never used.", "'isFocused2' is assigned a value but never used.", "'handleFocus2' is assigned a value but never used.", "'handleBlur2' is assigned a value but never used.", "'onChangeValorTrocoDividido' is assigned a value but never used.", "'onChangeValueToPay' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'calcularValorEntrega', 'formaEntrega', and 'setValorEntrega'. Either include them or remove the dependency array. If 'setValorEntrega' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["3343"], "'resetPassword' is assigned a value but never used.", "'setResetPassword' is assigned a value but never used.", "'tipo' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'tempoBalcaoMaxBD', 'tempoEntregaMaxBD', and 'tempoEntregaMinBD'. Either include them or remove the dependency array. If 'setTempoBalcaoMax' needs the current value of 'tempoBalcaoMaxBD', you can also switch to useReducer instead of useState and read 'tempoBalcaoMaxBD' in the reducer.", ["3344"], ["3345"], "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "'handleSwitchChange' is assigned a value but never used.", "'formik' is assigned a value but never used.", "'handleSubmitAPI' is assigned a value but never used.", "'HiIcons' is defined but never used.", "'HiOutlineSquaresPlus' is defined but never used.", "'CgReorder' is defined but never used.", "'MdPointOfSale' is defined but never used.", "'RiWhatsappLine' is defined but never used.", "'enderecoEmpresa' is assigned a value but never used.", "'handleClickEntrega' is assigned a value but never used.", "'createdBy' is assigned a value but never used.", "'DivModal' is assigned a value but never used.", ["3346"], "'newMargin' is assigned a value but never used.", "'MenuProfileContext' is defined but never used.", "'getFromLocalStorage' is defined but never used.", "'IoIosArrowBack' is defined but never used.", "'BtnSendToCartPdv' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSelectedCliente'. Either include it or remove the dependency array. If 'setSelectedCliente' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["3347"], "'useMemo' is defined but never used.", "'apiCheckImportStatus' is defined but never used.", "'removeWhatsappSession' is defined but never used.", "'AiOutlineSave' is defined but never used.", "'AiOutlineQuestionCircle' is defined but never used.", "'FaClock' is defined but never used.", "'MdHelp' is defined but never used.", "'empresaId' is assigned a value but never used.", "'isNewUser' is assigned a value but never used.", "'bairroEntrega' is assigned a value but never used.", "'setBairroEntrega' is assigned a value but never used.", "'city' is assigned a value but never used.", "'uf' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'empresaParse._id'. Either include it or remove the dependency array.", ["3348"], "The ref value 'timeoutRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'timeoutRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has missing dependencies: 'isWhatsappLoged' and 'whatsappConnected'. Either include them or remove the dependency array.", ["3349"], "React Hook useEffect has a missing dependency: 'formikEndereco'. Either include it or remove the dependency array.", ["3350"], "React Hook useEffect has a missing dependency: 'carregarSolicitacoes'. Either include it or remove the dependency array.", ["3351"], "React Hook useCallback has a missing dependency: 'atendimentosPendentes.length'. Either include it or remove the dependency array.", ["3352"], "React Hook useCallback has a missing dependency: 'isTemporaryId'. Either include it or remove the dependency array.", ["3353"], "React Hook useEffect has a missing dependency: 'loaded'. Either include it or remove the dependency array.", ["3354"], "'handleQuestionChange' is assigned a value but never used.", {"desc": "3355", "fix": "3356"}, {"desc": "3355", "fix": "3357"}, {"desc": "3358", "fix": "3359"}, {"desc": "3360", "fix": "3361"}, {"desc": "3362", "fix": "3363"}, {"desc": "3362", "fix": "3364"}, {"desc": "3365", "fix": "3366"}, {"desc": "3367", "fix": "3368"}, {"desc": "3369", "fix": "3370"}, {"desc": "3371", "fix": "3372"}, {"desc": "3373", "fix": "3374"}, {"desc": "3375", "fix": "3376"}, {"desc": "3377", "fix": "3378"}, {"desc": "3379", "fix": "3380"}, {"desc": "3381", "fix": "3382"}, {"desc": "3369", "fix": "3383"}, {"desc": "3371", "fix": "3384"}, {"desc": "3373", "fix": "3385"}, {"desc": "3386", "fix": "3387"}, {"desc": "3388", "fix": "3389"}, {"kind": "3390", "justification": "3391"}, {"desc": "3392", "fix": "3393"}, {"desc": "3394", "fix": "3395"}, {"desc": "3396", "fix": "3397"}, {"desc": "3398", "fix": "3399"}, {"desc": "3400", "fix": "3401"}, {"desc": "3402", "fix": "3403"}, {"desc": "3404", "fix": "3405"}, {"desc": "3402", "fix": "3406"}, {"desc": "3407", "fix": "3408"}, {"desc": "3409", "fix": "3410"}, {"desc": "3371", "fix": "3411"}, {"desc": "3412", "fix": "3413"}, {"desc": "3414", "fix": "3415"}, {"desc": "3416", "fix": "3417"}, {"desc": "3358", "fix": "3418"}, {"desc": "3412", "fix": "3419"}, {"desc": "3420", "fix": "3421"}, {"desc": "3422", "fix": "3423"}, {"desc": "3424", "fix": "3425"}, {"desc": "3426", "fix": "3427"}, {"desc": "3428", "fix": "3429"}, {"desc": "3430", "fix": "3431"}, {"desc": "3432", "fix": "3433"}, {"desc": "3400", "fix": "3434"}, {"desc": "3435", "fix": "3436"}, {"desc": "3400", "fix": "3437"}, {"desc": "3438", "fix": "3439"}, {"desc": "3440", "fix": "3441"}, {"desc": "3442", "fix": "3443"}, {"desc": "3400", "fix": "3444"}, {"desc": "3400", "fix": "3445"}, {"desc": "3446", "fix": "3447"}, {"desc": "3448", "fix": "3449"}, {"desc": "3450", "fix": "3451"}, {"desc": "3452", "fix": "3453"}, {"desc": "3454", "fix": "3455"}, {"desc": "3456", "fix": "3457"}, {"desc": "3458", "fix": "3459"}, {"desc": "3460", "fix": "3461"}, {"desc": "3462", "fix": "3463"}, {"desc": "3464", "fix": "3465"}, {"desc": "3466", "fix": "3467"}, {"desc": "3468", "fix": "3469"}, {"kind": "3390", "justification": "3391"}, {"desc": "3355", "fix": "3470"}, {"desc": "3355", "fix": "3471"}, {"desc": "3472", "fix": "3473"}, {"desc": "3474", "fix": "3475"}, {"desc": "3476", "fix": "3477"}, {"desc": "3478", "fix": "3479"}, {"messageId": "3480", "fix": "3481", "desc": "3482"}, {"messageId": "3483", "fix": "3484", "desc": "3485"}, {"messageId": "3480", "fix": "3486", "desc": "3482"}, {"messageId": "3483", "fix": "3487", "desc": "3485"}, {"desc": "3488", "fix": "3489"}, {"desc": "3490", "fix": "3491"}, {"desc": "3492", "fix": "3493"}, {"desc": "3494", "fix": "3495"}, {"desc": "3496", "fix": "3497"}, {"desc": "3498", "fix": "3499"}, {"desc": "3500", "fix": "3501"}, {"desc": "3355", "fix": "3502"}, {"desc": "3355", "fix": "3503"}, {"desc": "3504", "fix": "3505"}, {"desc": "3506", "fix": "3507"}, {"desc": "3508", "fix": "3509"}, {"desc": "3510", "fix": "3511"}, {"desc": "3373", "fix": "3512"}, {"desc": "3513", "fix": "3514"}, {"desc": "3515", "fix": "3516"}, {"kind": "3390", "justification": "3391"}, {"desc": "3355", "fix": "3517"}, {"desc": "3518", "fix": "3519"}, {"desc": "3520", "fix": "3521"}, {"desc": "3522", "fix": "3523"}, {"desc": "3524", "fix": "3525"}, {"desc": "3526", "fix": "3527"}, {"desc": "3528", "fix": "3529"}, {"desc": "3530", "fix": "3531"}, {"desc": "3532", "fix": "3533"}, "Update the dependencies array to be: [mandatory, minimo]", {"range": "3534", "text": "3535"}, {"range": "3536", "text": "3535"}, "Update the dependencies array to be: [EXPIRATION_TIME]", {"range": "3537", "text": "3538"}, "Update the dependencies array to be: [diasHorarios, horariosUnicos]", {"range": "3539", "text": "3540"}, "Update the dependencies array to be: [idEmpresa, objIdEmpresa, userID, vinculo_empresa]", {"range": "3541", "text": "3542"}, {"range": "3543", "text": "3542"}, "Update the dependencies array to be: [adicionais, sabores, refresh, tempSabores, tempAdicionais, userID, idEmpresa, vinculo_empresa, objIdEmpresa, itemObjId, formik]", {"range": "3544", "text": "3545"}, "Update the dependencies array to be: [callApiAdicionais, callApiSabores, refresh, selectDataAdicionais, selectDataSabores]", {"range": "3546", "text": "3547"}, "Update the dependencies array to be: [idEmpresa, location.state?.item, objIdProduto]", {"range": "3548", "text": "3549"}, "Update the dependencies array to be: [empresaObjId, openStatus]", {"range": "3550", "text": "3551"}, "Update the dependencies array to be: [isRequirementMet, quantidades]", {"range": "3552", "text": "3553"}, "Update the dependencies array to be: [regionType]", {"range": "3554", "text": "3555"}, "Update the dependencies array to be: [calcularValorEntrega, carrinho.length, enderecoEmpresa, idEmpresa, localPersonInfo]", {"range": "3556", "text": "3557"}, "Update the dependencies array to be: [empresa, currentTime, isSearchingClient]", {"range": "3558", "text": "3559"}, "Update the dependencies array to be: [externalId, idEmpresa, navigate, nomeEmpresaForUrl]", {"range": "3560", "text": "3561"}, {"range": "3562", "text": "3549"}, {"range": "3563", "text": "3551"}, {"range": "3564", "text": "3553"}, "Update the dependencies array to be: [fetchQrCode]", {"range": "3565", "text": "3566"}, "Update the dependencies array to be: [formik, idEmpresa, refresh, userID]", {"range": "3567", "text": "3568"}, "directive", "", "Update the dependencies array to be: [handleSearchCep]", {"range": "3569", "text": "3570"}, "Update the dependencies array to be: [debouncedSearchCep, formik]", {"range": "3571", "text": "3572"}, "Update the dependencies array to be: [refresh, userID]", {"range": "3573", "text": "3574"}, "Update the dependencies array to be: [idEmpresa, refresh, userID]", {"range": "3575", "text": "3576"}, "Update the dependencies array to be: [refresh, user._id]", {"range": "3577", "text": "3578"}, "Update the dependencies array to be: [fetchData]", {"range": "3579", "text": "3580"}, "Update the dependencies array to be: [startDate, endDate, fetchPedidosFinalizados]", {"range": "3581", "text": "3582"}, {"range": "3583", "text": "3580"}, "Update the dependencies array to be: [idEmpresa, pedidosEntregues, periodo, userID, vinculo_empresa]", {"range": "3584", "text": "3585"}, "Update the dependencies array to be: [horariosFuncionamento, currentTime, isRestaurantOpen, findNextClosingTime, findNextOpeningTime, toastShown, loadingData]", {"range": "3586", "text": "3587"}, {"range": "3588", "text": "3551"}, "Update the dependencies array to be: [empresaObjId, idEmpresa, refresh, userID, vinculo_empresa]", {"range": "3589", "text": "3590"}, "Update the dependencies array to be: [visibleInput, currentPrice, itens, handleClickOutside]", {"range": "3591", "text": "3592"}, "Update the dependencies array to be: [empresaInfo, toastShown]", {"range": "3593", "text": "3594"}, {"range": "3595", "text": "3538"}, {"range": "3596", "text": "3590"}, "Update the dependencies array to be: [handleClickOutsideItem]", {"range": "3597", "text": "3598"}, "Update the dependencies array to be: [empresaObjId, idEmpresa, refresh, refreshAdicional, userID, vinculo_empresa]", {"range": "3599", "text": "3600"}, "Update the dependencies array to be: [objIdEmpresa, refresh]", {"range": "3601", "text": "3602"}, "Update the dependencies array to be: [filter, list, arrayEntregadores, handleEdit, user._id]", {"range": "3603", "text": "3604"}, "Update the dependencies array to be: [stateParam, type, user._id]", {"range": "3605", "text": "3606"}, "Update the dependencies array to be: [empresas, refresh, user._id]", {"range": "3607", "text": "3608"}, "Update the dependencies array to be: [filter, list, arrayEmpresas, handleEdit]", {"range": "3609", "text": "3610"}, {"range": "3611", "text": "3578"}, "Update the dependencies array to be: [filter, list, arrayUsers, handleEdit]", {"range": "3612", "text": "3613"}, {"range": "3614", "text": "3578"}, "Update the dependencies array to be: [fetchPedidosFinalizados, fetchPedidosSimples, refresh]", {"range": "3615", "text": "3616"}, "Update the dependencies array to be: [fetchDataAndSetPedidosForFilters, filtroSelecionado, refresh]", {"range": "3617", "text": "3618"}, "Update the dependencies array to be: [apiUrl, fetchPedidosFinalizados, fetchPedidosSimples, idEmpresa, objIdEmpresa, userID]", {"range": "3619", "text": "3620"}, {"range": "3621", "text": "3578"}, {"range": "3622", "text": "3578"}, "Update the dependencies array to be: [empresaObjId]", {"range": "3623", "text": "3624"}, "Update the dependencies array to be: [empresaObjId, idEmpresa, refresh]", {"range": "3625", "text": "3626"}, "Update the dependencies array to be: [fetchCustomResponses]", {"range": "3627", "text": "3628"}, "Update the dependencies array to be: [apiUrl, checkImportProgress, checkImportStatus, objIdEmpresa, updateImportFlag]", {"range": "3629", "text": "3630"}, "Update the dependencies array to be: [refresh, page, allEmpresas.length, user._id, limit]", {"range": "3631", "text": "3632"}, "Update the dependencies array to be: [handleCreateInstance, paginatedEmpresas]", {"range": "3633", "text": "3634"}, "Update the dependencies array to be: [location, user, hasLicense, setHasLicense, setShowToast, pathname, setRedirectToPlanos]", {"range": "3635", "text": "3636"}, "Update the dependencies array to be: [user]", {"range": "3637", "text": "3638"}, "Update the dependencies array to be: [totalPendentes, isLoadingAtendimentos, errorAtendimentos, atendimentosPendentes.length]", {"range": "3639", "text": "3640"}, "Update the dependencies array to be: [apiUrl, handleNotify, objIdEmpresa]", {"range": "3641", "text": "3642"}, "Update the dependencies array to be: [handleResize]", {"range": "3643", "text": "3644"}, "Update the dependencies array to be: [userID]", {"range": "3645", "text": "3646"}, {"range": "3647", "text": "3535"}, {"range": "3648", "text": "3535"}, "Update the dependencies array to be: [apiUrl]", {"range": "3649", "text": "3650"}, "Update the dependencies array to be: [chats.length, empresaID, fetchChats, generatingQrCode, isInitialLoad, isLoged, mainSocket, markChatAsRead, qrCodeExpired, qrCodeImg, selectedChat, userRequestedQrCode]", {"range": "3651", "text": "3652"}, "Update the dependencies array to be: [checkConnectionStatus]", {"range": "3653", "text": "3654"}, "Update the dependencies array to be: [fetchProfilePicture, selectedChat]", {"range": "3655", "text": "3656"}, "removeEscape", {"range": "3657", "text": "3391"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3658", "text": "3659"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "3660", "text": "3391"}, {"range": "3661", "text": "3659"}, "Update the dependencies array to be: [empresaID, fetchChats, searchQuery, token]", {"range": "3662", "text": "3663"}, "Update the dependencies array to be: [empresaID, fetchMyProfilePicture, token]", {"range": "3664", "text": "3665"}, "Update the dependencies array to be: [empresaID, fetchUnreadCounts, token]", {"range": "3666", "text": "3667"}, "Update the dependencies array to be: [empresaID, token, isLoged, checkConnectionStatus]", {"range": "3668", "text": "3669"}, "Update the dependencies array to be: [handleScroll, hasMoreChats, loadingMore]", {"range": "3670", "text": "3671"}, "Update the dependencies array to be: [selectedChat, mainSocket, messagesCache, empresaID, token, markChatAsRead, shouldScrollToBottom]", {"range": "3672", "text": "3673"}, "Update the dependencies array to be: [chats, empresaID, token, isMobile, showMobileChat, unreadCounts, markChatAsRead, location.state]", {"range": "3674", "text": "3675"}, {"range": "3676", "text": "3535"}, {"range": "3677", "text": "3535"}, "Update the dependencies array to be: [formik, itemAdicionalToEdit, showEditItemAdicional]", {"range": "3678", "text": "3679"}, "Update the dependencies array to be: [_diaHorarioDispToEdit, setDiaHorarioDispToEdit]", {"range": "3680", "text": "3681"}, "Update the dependencies array to be: [_modeloToEdit, formik, showEditCategoria]", {"range": "3682", "text": "3683"}, "Update the dependencies array to be: [data.data, currentCaixa, data, caixa, empresaId, empresa_id, vinculoEmpresa]", {"range": "3684", "text": "3685"}, {"range": "3686", "text": "3553"}, "Update the dependencies array to be: [calcularValorEntrega, formaEntrega, selectedAddress, setValorEntrega]", {"range": "3687", "text": "3688"}, "Update the dependencies array to be: [tempoBalcaoMaxBD, tempoBalcaoMinBD, tempoEntregaMaxBD, tempoEntregaMinBD, tipoImpressao]", {"range": "3689", "text": "3690"}, {"range": "3691", "text": "3535"}, "Update the dependencies array to be: [setSelectedCliente, telefone]", {"range": "3692", "text": "3693"}, "Update the dependencies array to be: [isAuthenticated, empresaParse?.id_empresa, empresaParse._id]", {"range": "3694", "text": "3695"}, "Update the dependencies array to be: [currentStep, isAuthenticated, empresaParse?.id_empresa, whatsappConnected, isWhatsappLoged]", {"range": "3696", "text": "3697"}, "Update the dependencies array to be: [empresaData, formikEndereco, isAuthenticated]", {"range": "3698", "text": "3699"}, "Update the dependencies array to be: [carregarSolicitacoes, filtroStatus, paginacao.current_page, paginacao.per_page, user]", {"range": "3700", "text": "3701"}, "Update the dependencies array to be: [atendimentosPendentes.length, empresaId]", {"range": "3702", "text": "3703"}, "Update the dependencies array to be: [empresaId, isTemporaryId]", {"range": "3704", "text": "3705"}, "Update the dependencies array to be: [imageSources, loaded, timeout]", {"range": "3706", "text": "3707"}, [3969, 3980], "[mandatory, minimo]", [12264, 12275], [2846, 2848], "[EXPIRATION_TIME]", [3418, 3432], "[dias<PERSON><PERSON><PERSON>s, horariosUnicos]", [11190, 11192], "[idEmpresa, objIdEmpresa, userID, vinculo_empresa]", [11676, 11678], [18582, 18641], "[adicionais, sabores, refresh, tempSabores, tempAdicionais, userID, idEmpresa, vinculo_empresa, objIdEmpresa, itemObjId, formik]", [19366, 19375], "[callApiAdicionais, callApiSabores, refresh, selectDataAdicionais, selectDataSabores]", [3637, 3639], "[idEmpresa, location.state?.item, objIdProduto]", [7032, 7046], "[empresaObjId, openStatus]", [21650, 21663], "[isRequirementMet, quantidades]", [20454, 20456], "[regionType]", [5710, 5712], "[calcularValor<PERSON>ntrega, carrinho.length, enderecoEmpresa, idEmpresa, localPersonInfo]", [6983, 7005], "[empresa, currentTime, isSearchingClient]", [7789, 7791], "[externalId, idEmpresa, navigate, nomeEmpresaForUrl]", [3378, 3380], [6773, 6787], [19255, 19268], [3457, 3459], "[fetchQrCode]", [3921, 3930], "[formik, idEmpresa, refresh, userID]", [9242, 9244], "[handleSearchCep]", [10064, 10084], "[debouncedSearchCep, formik]", [7826, 7835], "[refresh, userID]", [9540, 9549], "[idEmpresa, refresh, userID]", [2942, 2951], "[refresh, user._id]", [8531, 8533], "[fetchData]", [19008, 19028], "[startDate, endDate, fetchPedidosFinalizados]", [8861, 8863], [13744, 13753], "[idEmpresa, pedidosEntregues, periodo, userID, vinculo_empresa]", [10380, 10416], "[horariosFuncionamento, currentTime, isRestaurantOpen, findNextClosingTime, findNextOpeningTime, toastShown, loadingData]", [16871, 16885], [4360, 4369], "[empresaObjId, idEmpresa, refresh, userID, vinculo_empresa]", [6759, 6794], "[visibleInput, currentPrice, itens, handleClickOutside]", [21625, 21638], "[empresaInfo, toastShown]", [26348, 26350], [3344, 3353], [10247, 10249], "[handleClickOutsideItem]", [10925, 10952], "[empresaObjId, idEmpresa, refresh, refreshAdicional, userID, vinculo_empresa]", [3853, 3862], "[objIdEmpresa, refresh]", [8833, 8866], "[filter, list, arrayEntregadores, handleEdit, user._id]", [6739, 6741], "[stateParam, type, user._id]", [3301, 3310], "[empresas, refresh, user._id]", [10295, 10324], "[filter, list, arrayEmpresas, handleEdit]", [4067, 4076], [10108, 10134], "[filter, list, arrayUsers, handleEdit]", [2287, 2296], [7232, 7241], "[fetchPedidosFinalizados, fetchPedidosSimples, refresh]", [13489, 13498], "[fetchDataAndSetPedidosForFilters, filtroSelecionado, refresh]", [20280, 20291], "[apiUrl, fetchPedidosFinalizados, fetchPedidosSimples, idEmpresa, objIdEmpresa, userID]", [2305, 2314], [3255, 3264], [4550, 4552], "[empresaObjId]", [4613, 4622], "[empresaObjId, idEmpresa, refresh]", [2706, 2708], "[fetchCustomResponses]", [4107, 4109], "[apiUrl, checkImportProgress, checkImportStatus, objIdEmpresa, updateImportFlag]", [15743, 15758], "[refresh, page, allEmpresas.length, user._id, limit]", [29147, 29166], "[handleCreateInstance, paginatedEmpresas]", [2555, 2622], "[location, user, hasLicense, setHasLicense, setShowToast, pathname, setRedirectToPlanos]", [9408, 9410], "[user]", [13229, 13287], "[totalPend<PERSON>s, isLoadingAtendimentos, errorAtendimentos, atendimentosPendentes.length]", [31602, 31616], "[apiUrl, handleNotify, objIdEmpresa]", [35984, 35986], "[handleResize]", [20159, 20161], "[userID]", [8650, 8661], [7602, 7613], [34193, 34195], "[apiUrl]", [43604, 43616], "[chats.length, empresaID, fetchChats, generatingQrCode, isInitialLoad, isLoged, mainSocket, markChatAsRead, qrCodeExpired, qrCodeImg, selectedChat, userRequestedQrCode]", [45974, 45976], "[checkConnectionStatus]", [52179, 52193], "[fetchProfilePicture, selectedChat]", [57492, 57493], [57492, 57492], "\\", [57494, 57495], [57494, 57494], [80767, 80780], "[empresaID, fetchChats, searchQuery, token]", [81326, 81344], "[empresa<PERSON>, fetchMyProfilePicture, token]", [81667, 81685], "[empresa<PERSON>, fetchUnreadCounts, token]", [83192, 83219], "[empresaID, token, isLoged, checkConnectionStatus]", [84112, 84139], "[handleScroll, hasMoreChats, loadingMore]", [97269, 97295], "[selectedChat, mainSocket, messagesCache, empresaID, token, markChatAsRead, shouldScrollToBottom]", [104973, 105054], "[chats, empresaID, token, isMobile, showMobileChat, unreadCounts, markChatAsRead, location.state]", [4203, 4214], [4297, 4308], [5515, 5538], "[formik, itemAdicionalToEdit, showEditItemAdicional]", [4135, 4137], "[_diaHorarioDispToEdit, setDiaHorarioDispToEdit]", [6221, 6240], "[_modeloToEdit, formik, showEditCategoria]", [1577, 1603], "[data.data, currentCaixa, data, caixa, empresaId, empresa_id, vinculoEmpresa]", [7338, 7351], [4877, 4894], "[calcular<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, form<PERSON><PERSON><PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>, setValorEntrega]", [3444, 3477], "[tempoBalcaoMaxBD, tempoBalcaoMinBD, tempoEntregaMaxBD, tempoEntregaMinBD, tipoImpressao]", [4681, 4692], [18605, 18615], "[setSelectedCliente, telefone]", [11510, 11553], "[isAuthenticated, empresaParse?.id_empresa, empresaParse._id]", [26385, 26441], "[currentStep, isAuthenticated, empresaParse?.id_empresa, whatsappConnected, isWhatsappLoged]", [28567, 28597], "[emp<PERSON>a<PERSON><PERSON>, formikEndereco, isAuthenticated]", [1953, 2017], "[carregarSolicita<PERSON>s, filtroStatus, paginacao.current_page, paginacao.per_page, user]", [3129, 3140], "[atendimentosPendentes.length, empresaId]", [8301, 8312], "[empresaId, isTemporaryId]", [2467, 2490], "[imageSources, loaded, timeout]"]