{"ast": null, "code": "import { AppConfigContext } from \"antd/es/app/context\";\nimport axios from \"axios\";\nimport { toast } from 'react-toastify';\nconst isDevelopment = window.location.hostname === \"localhost\" ? true : false;\nconst apiUrl = isDevelopment ? process.env.REACT_APP_SERVER_URL_DEV : process.env.REACT_APP_SERVER_URL_PROD;\nconst apiInstancesWppUrl = process.env.REACT_APP_API_INSTANCES_WPP_URL;\nexport const api = axios.create({\n  baseURL: apiUrl\n});\nconst apiInstancesWpp = axios.create({\n  baseURL: apiInstancesWppUrl,\n  headers: {\n    \"Content-Type\": \"application/json\"\n  }\n});\n\n// Função para configurar o token de autorização\nconst setAuthToken = token => {\n  if (token) {\n    // Aplica o token de autorização a todas as requisições se o token estiver presente\n    api.defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n  } else {\n    // Remove o header de autorização se o token não estiver presente\n    delete api.defaults.headers.common[\"Authorization\"];\n  }\n};\nexport const createSession = async (email, password) => {\n  return api.post(\"/auth/login\", {\n    email,\n    password\n  });\n};\nexport const checkLicense = userId => {\n  return api.get(`/check-license/${userId}`);\n};\nexport const getBotStatusLead = leadId => {\n  return api.get(`/auth/bot-status-lead/${leadId}`);\n};\nexport const getUsers = async userID => {\n  return api.get(\"/list-users/\" + userID);\n};\nexport const getUsersByRole = async role => {\n  return api.get(`/list-users-by-role/${role}`);\n};\nexport const getEntregadores = async id_empresa => {\n  return api.get(\"/list-entregadores/\" + id_empresa);\n};\nexport const listarCaixas = async (id, page, startDate, endDate) => {\n  return api.get(\"/list-caixas/\" + id, {\n    params: {\n      page,\n      per_page: 10,\n      startDate,\n      endDate\n    }\n  });\n};\nexport const getUserRole = async userID => {\n  return api.get(\"/user-role/\" + userID);\n};\nexport const getPlansAsaas = async userID => {\n  return api.get(\"/list-plans/\" + userID);\n};\nexport const getFilPlansIugu = async userID => {\n  return api.get(\"/list-fil-plans/\" + userID);\n};\nexport const getUser = async userID => {\n  return api.get(\"/user/\" + userID);\n};\nexport const getEmpresaWithObjId = async empresaObjId => {\n  return api.get(\"/empresaWithObjId/\" + empresaObjId);\n};\nexport const getEmpresa = async (userID, id_empresa) => {\n  return api.post(\"/get-empresa/\" + userID, {\n    id_empresa\n  });\n};\nexport const getEmpresasAdmin = async (userID, page, limit) => {\n  return api.get(`/get-empresas-admin/${userID}?page=${page}&limit=${limit}`);\n};\nexport const createInstanceAdmin = async (userID, empresaId) => {\n  return api.post(`/createInstance-admin/${userID}/${empresaId}`);\n};\nexport const getCaixaById = async (userID, id_caixa) => {\n  return api.post(\"/get-caixa/\" + userID, {\n    id_caixa\n  });\n};\nexport const testeImpressao = async companyId => {\n  return api.post(\"/print-test\", {\n    companyId\n  });\n};\nexport const getPedidos = async (userID, vinculo_empresa) => {\n  return api.post(\"/get-pedidos/\" + userID, {\n    vinculo_empresa\n  });\n};\nexport const getPedido = async (userID, id_empresa, vinculo_empresa, id_pedido) => {\n  try {\n    return await api.post(\"/get-pedido/\" + userID, {\n      id_empresa,\n      vinculo_empresa,\n      id_pedido\n    });\n  } catch (error) {\n    console.log(\"Erro ao buscar pedido\", error);\n    throw error;\n  }\n};\nexport const getCategorias = async (userID, id_empresa, vinculo_empresa, empresaObjId) => {\n  return api.post(\"/get-categorias/\" + userID, {\n    id_empresa,\n    vinculo_empresa,\n    empresaObjId\n  });\n};\nexport const getAdicionais = async (userID, id_empresa, vinculo_empresa, empresaObjId) => {\n  return api.post(\"/get-adicionais/\" + userID, {\n    id_empresa,\n    vinculo_empresa,\n    empresaObjId\n  });\n};\nexport const getAdicionaisCardapio = async (id_empresa, nomeEmpresa) => {\n  return api.post(\"/get-adicionais-cardapio/\", {\n    id_empresa,\n    nomeEmpresa\n  });\n};\nexport const getCategoriasCardapio = async (id_empresa, nomeEmpresa) => {\n  return api.post(\"/get-categorias-cardapio/\", {\n    id_empresa,\n    nomeEmpresa\n  });\n};\nexport const getEmpresaInfo = async (id_empresa, nomeEmpresa) => {\n  return api.post(\"/get-empresa-info\", {\n    id_empresa,\n    nomeEmpresa\n  });\n};\nexport const getItens = async (userID, id_empresa, vinculo_empresa, empresaObjId) => {\n  return api.post(\"/get-itens/\" + userID, {\n    id_empresa,\n    vinculo_empresa,\n    empresaObjId\n  });\n};\nexport const getItensCardapio = async (id_empresa, nomeEmpresa) => {\n  return api.post(\"/get-itens-cardapio/\", {\n    id_empresa,\n    nomeEmpresa\n  });\n};\nexport const getItensCardapioSalao = async (id_empresa, nomeEmpresa) => {\n  return api.post(\"/get-itens-cardapio-salao/\", {\n    id_empresa,\n    nomeEmpresa\n  });\n};\nexport const getItem = async (userID, id_empresa, vinculo_empresa, empresaObjId, itemObjId) => {\n  return api.post(\"/get-item/\" + userID, {\n    id_empresa,\n    vinculo_empresa,\n    empresaObjId,\n    itemObjId\n  });\n};\nexport const getItemCardapio = async (id_empresa, itemObjId) => {\n  return api.post(\"/get-item-cardapio/\", {\n    id_empresa,\n    itemObjId\n  });\n};\nexport const getPedidosByStatus = async (userID, id_empresa, vinculo_empresa, status_pedido, forReport) => {\n  return api.post(\"/get-pedidosStatus/\" + userID, {\n    id_empresa,\n    vinculo_empresa,\n    status_pedido,\n    forReport\n  });\n};\nexport const getPedidosByStatusSimples = async (userID, id_empresa, vinculo_empresa, status_pedido, forReport) => {\n  return api.post(\"/get-pedidosStatusSimples/\" + userID, {\n    id_empresa,\n    vinculo_empresa,\n    status_pedido,\n    forReport\n  });\n};\nexport const getPedidosByStatusFinalizados = async (userID, id_empresa, vinculo_empresa, forReport) => {\n  return api.post(\"/get-pedidosStatusFinalizados/\" + userID, {\n    id_empresa,\n    vinculo_empresa,\n    forReport\n  });\n};\nexport const getPedidosFinalizadosHistorico = async (userID, id_empresa, vinculo_empresa, status_pedido, periodo) => {\n  return api.post(\"/get-pedidosFinalizadosHistorico/\" + userID, {\n    id_empresa,\n    vinculo_empresa,\n    status_pedido,\n    periodo\n  });\n};\nexport const getPedidosFinalizadosPeriodo = async (userID, id_empresa, vinculo_empresa, status_pedido, startDate, endDate) => {\n  return api.post(\"/get-pedidosFinalizadosPeriodo/\" + userID, {\n    id_empresa,\n    vinculo_empresa,\n    status_pedido,\n    startDate,\n    endDate\n  });\n};\nexport const getPedidosToPrint = async (userID, id_empresa, vinculo_empresa) => {\n  return api.post(\"/get-pedidos-toprint/\" + userID, {\n    id_empresa,\n    vinculo_empresa\n  });\n};\nexport const getCliente = async (userID, id_empresa, telefone) => {\n  return api.post(\"/get-cliente/\" + userID, {\n    id_empresa,\n    telefone\n  });\n};\nexport const getVendedor = async (userID, id_vendedor) => {\n  return api.post(\"/get-vendedor/\" + userID, {\n    id_vendedor\n  });\n};\nexport const getOrcamento = async (userID, id_orcamento) => {\n  return api.post(\"/get-orcamento/\" + userID, {\n    id_orcamento\n  });\n};\nexport const getVinculoEmpresa = async userID => {\n  return api.get(\"/vinculo-empresa/\" + userID);\n};\nexport const getEmpresas = async userID => {\n  return api.get(\"/list-empresas/\" + userID);\n};\nexport const getRevendas = async userID => {\n  return api.get(\"/list-revendas/\" + userID);\n};\nexport const getClientes = async userID => {\n  return api.get(\"/list-clientes/\" + userID);\n};\nexport const getClientesAtivos = async (empresaObjectId, startDate, endDate) => {\n  return api.get(`/clientes-ativos/${empresaObjectId}`, {\n    params: {\n      startDate: startDate.toISOString(),\n      // Converte para formato UTC\n      endDate: endDate.toISOString()\n    }\n  });\n};\nexport const getCompanyResponses = async companyId => {\n  return api.get(\"/company-responses/\" + companyId);\n};\nexport const getVendedores = async userID => {\n  return api.get(\"/list-vendedores/\" + userID);\n};\nexport const getOrcamentos = async userID => {\n  return api.get(\"/list-orcamentos/\" + userID);\n};\nexport const updateUser = async (id, userID, email, password, name) => {\n  return api.post(\"/update-user/\" + userID, {\n    id,\n    email,\n    password,\n    name\n  });\n};\nexport const updateEntregador = async (idToEdit, userID, id_empresa, name, veiculo, telefone, placa) => {\n  return api.post(\"/update-entregador/\" + userID, {\n    idToEdit,\n    id_empresa,\n    name,\n    veiculo,\n    telefone,\n    placa\n  });\n};\nexport const updateTempoEntregaEmpresa = async (userID, _id, id, tempoBalcaoMin, tempoBalcaoMax, tempoEntregaMin, tempoEntregaMax, tipo_impressao) => {\n  return api.post(\"/update-empresaTempoEntrega/\" + userID, {\n    _id,\n    id,\n    tempoBalcaoMin,\n    tempoBalcaoMax,\n    tempoEntregaMin,\n    tempoEntregaMax,\n    tipo_impressao\n  });\n};\nexport const updateTempoHorarioFuncionamento = async (userID, _id, id, status_loja, horario_funcionamento, timezone) => {\n  return api.post(\"/update-empresaHorarioFuncionamento/\" + userID, {\n    _id,\n    id,\n    status_loja,\n    horario_funcionamento,\n    timezone\n  });\n};\nexport const updateTypeRegion = async (_id, id, userID, region_type_delivery) => {\n  return api.post(\"/update-empresa/\" + userID, {\n    _id: _id,\n    id: id,\n    region_type_delivery: region_type_delivery,\n    type_of_region: region_type_delivery\n  });\n};\nexport const updateClienteAddress = async (id_empresa, telefone, endereco, enderecoToEdit) => {\n  return api.post(\"/update-endereco-cliente/\", {\n    id_empresa,\n    telefone,\n    endereco,\n    enderecoToEdit\n  });\n};\nexport const updateRaioEntregaEmpresa = async (_id, id, userID, raio_entrega, valor_entrega) => {\n  return api.post(\"/update-raioEntrega/\" + userID, {\n    _id: _id,\n    id: id,\n    raio_entrega: raio_entrega,\n    valor_entrega: valor_entrega\n  });\n};\nexport const updateBairroEntregaEmpresa = async (_id, id, userID, bairro_entrega, valor_entrega) => {\n  return api.post(\"/update-bairroEntrega/\" + userID, {\n    _id: _id,\n    id: id,\n    bairro_entrega: bairro_entrega,\n    valor_entrega: valor_entrega\n  });\n};\nexport const updateCategoriasOrder = async (userID, _id, id_categoria, id_empresa, order) => {\n  return api.post(\"/update-categoria/\" + userID, {\n    _id: _id,\n    id_categoria: id_categoria,\n    id_empresa: id_empresa,\n    order: order\n  });\n};\nexport const updateItensOrder = async (userID, _id, id_item, id_empresa, order) => {\n  return api.post(\"/update-item/\" + userID, {\n    _id: _id,\n    id_item: id_item,\n    id_empresa: id_empresa,\n    order: order\n  });\n};\nexport const updateItemOut = async (userID, _id, id_item, id_empresa, out) => {\n  return api.post(\"/update-item/\" + userID, {\n    _id: _id,\n    id_item: id_item,\n    id_empresa: id_empresa,\n    out: out\n  });\n};\nexport const updateItemOutSalao = async (userID, _id, id_item, id_empresa, out) => {\n  return api.post(\"/update-item/\" + userID, {\n    _id: _id,\n    id_item: id_item,\n    id_empresa: id_empresa,\n    out_salao: out\n  });\n};\nexport const updateItemAdicionalOut = async (userID, idGrupo, itemID, titulo, price, id_adicional, id_empresa, out) => {\n  return api.post(\"/auth/updateAdicional/\" + userID, {\n    _id: itemID,\n    id_grupo: idGrupo,\n    title: titulo,\n    price: price,\n    id_adicional: id_adicional,\n    id_empresa: id_empresa,\n    out: out\n  });\n};\nexport const updateItemAdicionalOutSalao = async (userID, idGrupo, itemID, titulo, price, id_adicional, id_empresa, out) => {\n  return api.post(\"/auth/updateAdicional/\" + userID, {\n    _id: itemID,\n    id_grupo: idGrupo,\n    title: titulo,\n    price: price,\n    id_adicional: id_adicional,\n    id_empresa: id_empresa,\n    out_salao: out\n  });\n};\nexport const updateItemPrice = async (userID, _id, id_item, id_empresa, price) => {\n  return api.post(\"/update-item/\" + userID, {\n    _id: _id,\n    id_item: id_item,\n    id_empresa: id_empresa,\n    price: price\n  });\n};\nexport const updateItemPriceSalao = async (userID, _id, id_item, id_empresa, price) => {\n  return api.post(\"/update-item/\" + userID, {\n    _id: _id,\n    id_item: id_item,\n    id_empresa: id_empresa,\n    price_salao: price\n  });\n};\nexport const updateCategorias = async (userID, _id, id_categoria, id_empresa, modelo, title, disponibilidade, dia_horario_disponibilidade) => {\n  return api.post(\"/update-categoria/\" + userID, {\n    _id: _id,\n    id_categoria: id_categoria,\n    id_empresa: id_empresa,\n    modelo: modelo,\n    title: title,\n    disponibilidade: disponibilidade,\n    dia_horario_disponibilidade: dia_horario_disponibilidade\n  });\n};\nexport const updateAdicionaisGroup = async ({\n  userID,\n  adicional_objId,\n  idEmpresa,\n  title,\n  minimo,\n  maximo,\n  mandatory,\n  calcularMaiorValor,\n  calcularMedia,\n  precificacao,\n  type\n}) => {\n  return api.post(\"/update-grupo-adicionais/\" + userID, {\n    _id: adicional_objId,\n    id_empresa: idEmpresa,\n    title: title,\n    min: minimo,\n    max: maximo,\n    mandatory: mandatory,\n    calcular_maior_valor: calcularMaiorValor,\n    calcular_media: calcularMedia,\n    precificacao: precificacao,\n    type: type\n  });\n};\nexport const updateItens = async (userID, itemObjId, id_item, idEmpresa, orderItem, category_item_id, category_item_title, title, description, out, images, price, has_adicional, adicionais, type) => {\n  return api.post(\"/update-item/\" + userID, {\n    _id: itemObjId,\n    id_item: id_item,\n    id_empresa: idEmpresa,\n    order: orderItem,\n    category_item_id: category_item_id,\n    category_item_title: category_item_title,\n    title: title,\n    description: description,\n    out: out,\n    images: images,\n    price: price,\n    has_adicional: has_adicional,\n    adicionais: adicionais,\n    type: type\n  });\n};\nexport const removeAdicionalFromItem = async (userID, itemId, grupoAdicionalValue) => {\n  return api.post(\"/remove-grupoAdicionaisFromItem/\" + userID, {\n    itemId,\n    grupoAdicionalValue\n  });\n};\nexport const updateStatusPedido = async (userID, _id, id_pedido, status_pedido) => {\n  return api.post(\"/update-pedido/\" + userID, {\n    _id: _id,\n    id_pedido: id_pedido,\n    status_pedido: status_pedido\n    //\"status_print\":status_print,\n  });\n};\nexport const updatePedidoWithEntregador = async (userID, _id, id_pedido, entregador) => {\n  return axios.post(`${apiUrl}/update-pedido/${userID}`, {\n    _id: _id,\n    id_pedido: id_pedido,\n    entregador: entregador\n  });\n};\nexport const apiUpdateImportFlag = async (idEmpresa, importacao_finalizada) => {\n  return api.post(`/update-import-flag/${idEmpresa}`, {\n    importacao_finalizada\n  });\n};\nexport const ImprimirPedido = async (userID, dadosPedido) => {\n  return api.post(\"/command-print/\" + userID, {\n    dadosPedido\n  });\n};\n_c = ImprimirPedido;\nexport const updateStatusPedidoFinalizado = async (userID, _id, id_pedido, status_pedido, finalizadoAt) => {\n  return api.post(\"/update-pedido/\" + userID, {\n    _id: _id,\n    id_pedido: id_pedido,\n    status_pedido: status_pedido,\n    //\"status_print\":status_print,\n    finalizadoAt: finalizadoAt\n  });\n};\nexport const updateStatusPrint = async (userID, _id, id_pedido) => {\n  //console.log(\"caiuNoUpdateStatusPrint>\", id_pedido);\n  return api.post(\"/update-pedido/\" + userID, {\n    _id: _id,\n    id_pedido: id_pedido,\n    status_pedido: \"2\",\n    status_print: false\n  });\n};\nexport const fecharCaixa = async (id_empresa, objIdCaixa, closedBy, saldo_final, valor_informado_dinheiro, valor_informado_cartao, valor_informado_pix) => {\n  return api.post(\"/closeCaixa/\", {\n    id_empresa,\n    objIdCaixa,\n    closedBy,\n    saldo_final,\n    valor_informado_dinheiro,\n    valor_informado_cartao,\n    valor_informado_pix\n  });\n};\nexport const addLancamentoCaixa = async (id_empresa, objIdCaixa, descricao, tipo_lancamento, valor, createdBy) => {\n  return api.post(\"/addLancamentoCaixa/\", {\n    id_empresa,\n    objIdCaixa,\n    descricao,\n    tipo_lancamento,\n    valor,\n    createdBy\n  });\n};\nexport const updateEmpresa = async (id, userID, cnpj, name, razao, email, cep, estado, municipio, bairro, complemento, telefone, celular) => {\n  console.log(\"caiu no updateEmpresa Normal\");\n  return api.post(\"/update-empresa/\" + userID, {\n    id,\n    cnpj,\n    name,\n    razao,\n    email,\n    cep,\n    estado,\n    municipio,\n    bairro,\n    complemento,\n    telefone,\n    celular\n  });\n};\nexport const updateEmpresaAddress = async (_id, id, userID, cep, estado, municipio, bairro, address_number, complemento, latitude, longitude) => {\n  return api.post(\"/update-empresa/\" + userID, {\n    _id,\n    id,\n    cep,\n    estado,\n    municipio,\n    bairro,\n    address_number,\n    complemento,\n    latitude,\n    longitude\n  });\n};\nexport const updateCliente = async (id, userID, documento, name, razao, contato, email, cep, estado, municipio, bairro, complemento, telefone, celular, type) => {\n  if (type === \"F\") {\n    return api.post(\"/update-cliente/\" + userID, {\n      id,\n      documento,\n      name,\n      contato,\n      email,\n      cep,\n      estado,\n      municipio,\n      bairro,\n      complemento,\n      telefone,\n      celular\n    });\n  } else {\n    return api.post(\"/update-cliente/\" + userID, {\n      id,\n      documento,\n      name,\n      razao,\n      email,\n      cep,\n      estado,\n      municipio,\n      bairro,\n      complemento,\n      telefone,\n      celular\n    });\n  }\n};\nexport const updateVendedor = async (id, userID, documento, name, cep, estado, municipio, bairro, complemento, telefone, celular) => {\n  return api.post(\"/update-vendedor/\" + userID, {\n    id,\n    documento,\n    name,\n    cep,\n    estado,\n    municipio,\n    bairro,\n    complemento,\n    telefone,\n    celular\n  });\n};\nexport const updateOrcamento = async (id, userID, codigo_cliente, nome_cliente, codigo_vendedor, nome_vendedor, total_orc, status_orc, id_grupo, vinculo_empresa, items) => {\n  return api.post(\"/update-orcamento/\" + userID, {\n    id,\n    codigo_cliente,\n    nome_cliente,\n    codigo_vendedor,\n    nome_vendedor,\n    total_orc,\n    status_orc,\n    id_grupo,\n    vinculo_empresa,\n    items\n  });\n};\nexport const updateStatus = async (id_empresa, userID, status) => {\n  return api.post(\"/update-status/\" + userID, {\n    id_empresa,\n    status\n  });\n};\nexport const updateUserImg = async (userID, image) => {\n  return api.post(\"/update-user-img/\" + userID, {\n    image\n  });\n};\nexport const deleteUser = async (id, userID) => {\n  return api.post(\"/delete-user/\" + userID, {\n    id\n  });\n};\nexport const deleteEntregador = async (id, userID) => {\n  return api.post(\"/delete-entregador/\" + userID, {\n    id\n  });\n};\nexport const cancelarPedido = async (id, userID, empresaId, senha) => {\n  return api.post(\"/cancelar-pedido/\" + userID, {\n    id,\n    empresaId,\n    senha\n  });\n};\nexport const updateEmpresaCancelamentoPedido = async payload => {\n  return api.post(\"/configurar-senha-cancelamento\", payload);\n};\nexport const deleteEmpresa = async (userID, id) => {\n  return api.post(\"/delete-empresa/\" + userID, {\n    id\n  });\n};\nexport const deleteEmpresaCompleta = async (empresaObjId, usuario, senha) => {\n  return api.delete(\"/delete-empresa-completa/\" + empresaObjId, {\n    data: {\n      usuario,\n      senha\n    }\n  });\n};\nexport const deleteCategoria = async (userID, id) => {\n  return api.post(\"/delete-categoria/\" + userID, {\n    id\n  });\n};\nexport const deleteGrupoAdicional = async (userID, id) => {\n  return api.post(\"/delete-grupo-adicional/\" + userID, {\n    id\n  });\n};\nexport const deleteItem = async (userID, id) => {\n  return api.post(\"/delete-item/\" + userID, {\n    id\n  });\n};\nexport const deleteEnderecoCliente = async (id_empresa, telefone, enderecoIdToDelete) => {\n  return api.post(\"/delete-endereco-cliente/\", {\n    id_empresa,\n    telefone,\n    enderecoIdToDelete\n  });\n};\nexport const deleteCliente = async (id, userID) => {\n  return api.post(\"/delete-cliente/\" + userID, {\n    id\n  });\n};\nexport const deleteVendedor = async (id, userID) => {\n  return api.post(\"/delete-vendedor/\" + userID, {\n    id\n  });\n};\nexport const deleteOrcamento = async (id, userID) => {\n  return api.post(\"/delete-orcamento/\" + userID, {\n    id\n  });\n};\nexport const deleteRaioEntrega = async (userID, id_empresa, id_raio_entrega) => {\n  return api.post(\"/delete-raioEntrega/\" + userID, {\n    id_empresa,\n    id_raio_entrega\n  });\n};\nexport const deleteBairroEntrega = async (userID, id_empresa, id_bairro_entrega) => {\n  return api.post(\"/delete-bairroEntrega/\" + userID, {\n    id_empresa,\n    id_bairro_entrega\n  });\n};\nexport const register = async (createdBy, name, email, password, confirmpassword, vinculo_empresa, role) => {\n  return api.post(\"/auth/register\", {\n    createdBy,\n    name,\n    email,\n    password,\n    confirmpassword,\n    vinculo_empresa,\n    role\n  });\n};\nexport const registerGarcom = async (name, email, password, number) => {\n  return api.post(\"/users/garcom\", {\n    name,\n    email,\n    password,\n    number\n  });\n};\nexport const registerEntregador = async (createdBy, id_empresa, name, telefone, veiculo, placa) => {\n  return api.post(\"/registerEntregador\", {\n    createdBy,\n    id_empresa,\n    name,\n    telefone,\n    veiculo,\n    placa\n  });\n};\nexport const registerEmpresa = async (createdBy, cnpj, name, razao, email, cep, estado, municipio, bairro, complemento, telefone, celular, id_grupo, type) => {\n  return api.post(\"/auth/registerEmpresa\", {\n    createdBy,\n    cnpj,\n    name,\n    razao,\n    email,\n    cep,\n    estado,\n    municipio,\n    bairro,\n    complemento,\n    telefone,\n    celular,\n    id_grupo,\n    type\n  });\n};\nexport const createEmpresaUser = async (cnpj, nome_empresa, email, telefone, type, nome_pessoa, password, confirmpassword, vinculo_empresa) => {\n  return api.post(\"/createEmpresaUser\", {\n    cnpj,\n    nome_empresa,\n    email,\n    telefone,\n    type,\n    nome_pessoa,\n    password,\n    confirmpassword,\n    vinculo_empresa\n  });\n};\n\n// Função para adicionar endereço à empresa\nexport const addEnderecoEmpresa = async (empresaId, enderecoData) => {\n  return api.put(`/addEnderecoEmpresa/${empresaId}`, enderecoData);\n};\n\n// Função para criar cliente Asaas\nexport const createAsaasCustomer = async (empresaId, dadosEndereco) => {\n  return api.post(`/createAsaasCustomer/${empresaId}`, dadosEndereco);\n};\nexport const registerPedido = async (createdBy, id_empresa, itens, celular_cliente, nome_cliente, tipo_pagamento, entrega, desconto, cpf_cnpj, valor_troco, valor_total, descricao, external_id) => {\n  return api.post(\"/auth/registerPedido\", {\n    createdBy,\n    id_empresa,\n    itens,\n    celular_cliente,\n    nome_cliente,\n    tipo_pagamento,\n    entrega,\n    desconto,\n    cpf_cnpj,\n    valor_troco,\n    valor_total,\n    descricao,\n    external_id\n  });\n};\nexport const toggleBotStatus = async leadId => {\n  return api.put(`/auth/toggle-bot/${leadId}`);\n};\n\n// Função para buscar e salvar imagem de perfil do WhatsApp\nexport const getWhatsappProfilePicture = async (leadChannelID, token) => {\n  setAuthToken(token);\n  return api.post(`/api/v1/whatsapp/get-profile-picture/${leadChannelID}`);\n};\n\n// Função para buscar imagem de perfil do próprio usuário (dono da instância)\nexport const getMyWhatsappProfilePicture = async (empresaID, token) => {\n  setAuthToken(token);\n  return api.get(`/api/v1/whatsapp/get-my-profile-picture/${empresaID}`);\n};\n\n// Função para forçar busca e salvamento do JID\nexport const fetchWhatsappJID = async (empresaID, token) => {\n  setAuthToken(token);\n  return api.post(`/api/v1/whatsapp/fetch-jid/${empresaID}`);\n};\nexport const updateFormasPagamento = async (empresaId, formas_pagamento) => {\n  return api.put(`/empresa/${empresaId}/formas-pagamento`, {\n    formas_pagamento\n  });\n};\nexport const updateConfiguracoesEntrega = async (empresaId, entrega_disabled, retirada_disabled) => {\n  return api.put(`/empresa/${empresaId}/configuracoes-entrega`, {\n    entrega_disabled,\n    retirada_disabled\n  });\n};\nexport const updatePedidoFromPdv = async (objIdPedido, id_empresa, itens, celular_cliente, nome_cliente, tipo_pagamento, entrega, desconto, cpf_cnpj, valor_troco, valor_total, descricao, external_id) => {\n  return api.put(\"/auth/updatePedidoFromPdv\", {\n    objIdPedido,\n    id_empresa,\n    itens,\n    celular_cliente,\n    nome_cliente,\n    tipo_pagamento,\n    entrega,\n    desconto,\n    cpf_cnpj,\n    valor_troco,\n    valor_total,\n    descricao,\n    external_id\n  });\n};\nexport const updatePedidoFromMesas = async (objIdPedido, id_empresa, itens, celular_cliente, nome_cliente, valor_total, descricao, external_id) => {\n  return api.put(\"/auth/updatePedidoFromMesas\", {\n    objIdPedido,\n    id_empresa,\n    itens,\n    celular_cliente,\n    nome_cliente,\n    valor_total,\n    descricao,\n    external_id\n  });\n};\nexport const registerCategoria = async (createdBy, id_empresa, title, disponibilidade, dia_horario_disponibilidade, only_pdv, only_qrcode, only_delivery_take_local, modelo) => {\n  return api.post(\"/auth/registerCategoria\", {\n    createdBy,\n    id_empresa,\n    title,\n    disponibilidade,\n    dia_horario_disponibilidade,\n    only_pdv,\n    only_qrcode,\n    only_delivery_take_local,\n    modelo\n  });\n};\nexport const registerItem = async (createdBy, id_empresa, category_item_id, category_item_title, title, description, out, image, price, disponibilidade, type) => {\n  return api.post(\"/auth/registerItem\", {\n    createdBy,\n    id_empresa,\n    category_item_id,\n    category_item_title,\n    title,\n    description,\n    out,\n    image,\n    price,\n    disponibilidade,\n    type\n  });\n};\nexport const registerGrupoAdicionais = async (createdBy, id_empresa, title, min, max, mandatory, out, calcular_maior_valor, calcular_media, precificacao, type) => {\n  return api.post(\"/auth/registerGrupoAdicionais\", {\n    createdBy,\n    id_empresa,\n    title,\n    min,\n    max,\n    mandatory,\n    out,\n    calcular_maior_valor,\n    calcular_media,\n    precificacao,\n    type\n  });\n};\nexport const registerAdicionais = async (createdBy, id_grupo, id_empresa, title, price, out, image) => {\n  return api.post(\"/auth/registerAdicionais/\" + createdBy, {\n    createdBy,\n    id_grupo,\n    id_empresa,\n    title,\n    price,\n    out,\n    image\n  });\n};\nexport const updateItemAdicional = async data => {\n  return api.post(\"/auth/updateAdicional/\" + data.userID, data);\n};\nexport const deleteItemAdicional = async data => {\n  return api.post(\"/auth/deleteSubadicional/\" + data.userID, data);\n};\nexport const registerCaixaAndOpen = async (createdBy, id_empresa, saldo_inicial) => {\n  return api.post(\"/registerCaixaAndOpen/\", {\n    createdBy,\n    id_empresa,\n    saldo_inicial\n  });\n};\nexport const registerPlanoAdmin = async (createdBy, nome, plan_identifier, order, plan_cycle, maxPayments, valor_plano) => {\n  return api.post(\"/auth/registerPlanoAdmin\", {\n    createdBy,\n    nome,\n    plan_identifier,\n    order,\n    plan_cycle,\n    maxPayments,\n    valor_plano\n  });\n};\nexport const searchClienteAsaasAndGetInvoice = async (createdBy, email, name, cpf_cnpj, zip_code, number, plan_identifier, payment_type, isPromotional) => {\n  return api.post(\"/requestsToAsaas\", {\n    createdBy,\n    email,\n    name,\n    cpf_cnpj,\n    zip_code,\n    number,\n    plan_identifier,\n    payment_type,\n    isPromotional\n  });\n};\nexport const deleteAsaasPlan = async (createdBy, planId) => {\n  return api.post(\"/delete-plan-asaas/\" + createdBy, {\n    planId\n  });\n};\nexport const updateQuestionResponses = async (company_id, responses) => {\n  return api.post(\"/update-company-responses\", {\n    company_id,\n    responses\n  });\n};\nexport const updateQuestionActive = async (companyId, questionIdentifier, active) => {\n  return api.put(\"/update-question-active\", {\n    companyId,\n    questionIdentifier,\n    active\n  });\n};\n\n// ===== NOVAS FUNÇÕES PARA CUSTOM RESPONSES =====\n\n// Buscar respostas personalizadas da empresa\nexport const getCustomResponses = async companyId => {\n  return api.get(\"/custom-responses/\" + companyId);\n};\n\n// Adicionar nova resposta personalizada\nexport const addCustomResponse = async (companyId, question, response) => {\n  return api.post(\"/custom-responses/\" + companyId, {\n    question,\n    response\n  });\n};\n\n// Atualizar resposta personalizada\nexport const updateCustomResponse = async (companyId, responseId, question, response, active = true) => {\n  return api.put(`/custom-responses/${companyId}/${responseId}`, {\n    question,\n    response,\n    active\n  });\n};\n\n// Deletar resposta personalizada\nexport const deleteCustomResponse = async (companyId, responseId) => {\n  return api.delete(`/custom-responses/${companyId}/${responseId}`);\n};\nexport const registerCliente = async (createdBy, documento, name, contato, razao, email, cep, estado, municipio, bairro, complemento, telefone, celular, vinculo_empresa, type) => {\n  if (type === \"F\") {\n    return api.post(\"/auth/registerCliente\", {\n      createdBy,\n      documento,\n      name,\n      contato,\n      email,\n      cep,\n      estado,\n      municipio,\n      bairro,\n      complemento,\n      telefone,\n      celular,\n      vinculo_empresa,\n      type\n    });\n  } else {\n    return api.post(\"/auth/registerCliente\", {\n      createdBy,\n      documento,\n      name,\n      razao,\n      email,\n      cep,\n      estado,\n      municipio,\n      bairro,\n      complemento,\n      telefone,\n      celular,\n      vinculo_empresa,\n      type\n    });\n  }\n};\nexport const registerClienteFromCardapio = async (id_empresa, nome, telefone) => {\n  return api.post(\"/register-cliente-cardapio\", {\n    id_empresa,\n    nome,\n    telefone\n  });\n};\nexport const registerVendedor = async (createdBy, documento, name, cep, estado, municipio, bairro, complemento, telefone, celular, vinculo_empresa, type) => {\n  return api.post(\"/auth/registerVendedor\", {\n    createdBy,\n    documento,\n    name,\n    cep,\n    estado,\n    municipio,\n    bairro,\n    complemento,\n    telefone,\n    celular,\n    vinculo_empresa,\n    type\n  });\n};\nexport const registerOrcamento = async (createdBy, data_emissao, codigo_cliente, nome_cliente, codigo_vendedor, nome_vendedor, total_orc, status_orc, id_grupo, vinculo_empresa, items) => {\n  return api.post(\"/auth/registerOrcamento\", {\n    createdBy,\n    data_emissao,\n    codigo_cliente,\n    nome_cliente,\n    codigo_vendedor,\n    nome_vendedor,\n    total_orc,\n    status_orc,\n    id_grupo,\n    vinculo_empresa,\n    items\n  });\n};\n\n/*export const retornaClienteAsaas = async (nome_cliente, cpf_cnpj, email_cliente, phone, addressNumber, postalCode) => {   \r\n    return api.post(\"/retorna-cliente-asaas\",{nome_cliente, cpf_cnpj, email_cliente, phone, addressNumber, postalCode}); \r\n};*/\n\nexport const criarAssinaturaAsaasBolPix = async (customerID, payment_type, cycle, value, nextDueDate) => {\n  return api.post(\"/criar-assinatura-asaas-bolpix\", {\n    customerID,\n    payment_type,\n    cycle,\n    value,\n    nextDueDate\n  });\n};\nexport const criarAssinaturaAsaasCartao = async (customerID, nome_cliente, cpf_cnpj, cycle, numero_cartao, expiryMonth, expiryYear, ccv, email, postalCode, addressNumber, phone, value, nextDueDate, remoteIp) => {\n  return api.post(\"/criar-assinatura-asaas-cartao\", {\n    customerID,\n    nome_cliente,\n    cpf_cnpj,\n    cycle,\n    numero_cartao,\n    expiryMonth,\n    expiryYear,\n    ccv,\n    email,\n    postalCode,\n    addressNumber,\n    phone,\n    value,\n    nextDueDate,\n    remoteIp\n  });\n};\nexport const criarCobrancaAsaas = async (customerID, nome_cliente, cpf_cnpj, numero_cartao, expiryMonth, expiryYear, ccv, email, postalCode, addressNumber, phone, value, nextDueDate, remoteIp, parcela) => {\n  return api.post(\"/criar-cobranca-asaas-cartao\", {\n    customerID,\n    nome_cliente,\n    cpf_cnpj,\n    numero_cartao,\n    expiryMonth,\n    expiryYear,\n    ccv,\n    email,\n    postalCode,\n    addressNumber,\n    phone,\n    value,\n    nextDueDate,\n    remoteIp,\n    parcela\n  });\n};\nexport const criarCobrancaAsaasBolPix = async (customerID, payment_type, value, nextDueDate) => {\n  return api.post(\"/criar-cobranca-asaas\", {\n    customerID,\n    payment_type,\n    value,\n    nextDueDate\n  });\n};\nexport const listarAssinaturasAsaas = async customer_id => {\n  return api.post(\"/lista-subscription-asaas\", {\n    customer_id\n  });\n};\n\n/*export const listarAssinaturasIugu = async (empresa_id, customer_id) => {\r\n  return api.post(\"/lista-subscription-iugu\", { empresa_id, customer_id });\r\n}*/\n\nexport const getDataCustomerAsaas = async customer_id => {\n  return api.post(\"/getData-customer-asaas\", {\n    customer_id\n  });\n};\nexport const getLastPendingInvoiceAsaas = async subscriptionIdAsaas => {\n  return api.post(\"/lastPendingInvoice\", {\n    subscriptionIdAsaas\n  });\n};\nexport const getAllInvoicesAsaas = async subscriptionIdAsaas => {\n  return api.post(\"/allInvoices\", {\n    subscriptionIdAsaas\n  });\n};\nexport const getInvoiceIugu = async invoiceId => {\n  return api.post(\"/getInvoice-iugu\", {\n    invoiceId\n  });\n};\nexport const importCardapioIfood = async (url_import, id_empresa) => {\n  return api.post(\"/import-cardapio-ifood\", {\n    url_import,\n    id_empresa\n  });\n};\nexport const importCardapioAnotaai = async (url_import, id_empresa) => {\n  //console.log(\"TRECHO API>\",url_import)\n  return api.post(\"/import-cardapio-anotaai\", {\n    url_import,\n    id_empresa\n  });\n};\nexport const changeStatusLoja = async (id, status_loja) => {\n  return api.post(\"/changeStatusLoja\", {\n    id,\n    status_loja\n  });\n};\nexport const getUltimoPedidoID = async id => {\n  return api.get(\"/ultimoPedidoID/\" + id);\n};\nexport const getDaysToExpireLicense = async id => {\n  return api.get(\"/getDaysToExpireLicense/\" + id);\n};\nexport const apiCheckImportStatus = async idEmpresa => {\n  return api.get(`/check-import-status/${idEmpresa}`);\n};\nexport const configurarResetPedido = async (id_empresa, dias_para_reset) => {\n  return api.post(`/configurar-reset-pedido`, {\n    id_empresa,\n    dias_para_reset\n  });\n};\nexport const resetPedidoManual = async id_empresa => {\n  return api.post(`/reset-pedido-counter`, {\n    id_empresa\n  });\n};\nexport const updateStatusBotEmpresa = async (id, status_bot) => {\n  return api.patch(`/api/empresa/${id}/status_bot`, {\n    status_bot\n  });\n};\nexport const updateCallAtendenteEmpresa = async (id, call_atendente) => {\n  return api.patch(`/api/empresa/${id}/call_atendente`, {\n    call_atendente\n  });\n};\nexport const getQrCodeWhatsapp = async (empresaID, token) => {\n  setAuthToken(token);\n  return api.get(\"/api/v1/whatsapp/qrcode/\" + empresaID);\n};\n\n// **📌 NOVA FUNÇÃO PARA RENOVAR QR CODE VIA API**\nexport const renewQrCodeWhatsapp = async (empresaID, token) => {\n  setAuthToken(token);\n  return api.post(\"/api/v1/whatsapp/qrcode/renew/\" + empresaID);\n};\nexport const refreshProfilePicturesWhatsapp = async (empresaID, token) => {\n  setAuthToken(token);\n  return api.post(\"/api/v1/whatsapp/refresh-profile-pictures/\" + empresaID);\n};\nexport const refreshContactInfoWhatsapp = async (leadChannelID, token) => {\n  setAuthToken(token);\n  return api.post(\"/api/v1/whatsapp/refresh-contact-info/\" + leadChannelID);\n};\n\n// **📌 NOVA FUNÇÃO PARA VERIFICAR STATUS DE CONEXÃO DA EVOLUTION API**\nexport const getWhatsappConnectionStatus = async (empresaID, token) => {\n  setAuthToken(token);\n  return api.get(\"/api/v1/whatsapp/connectionStatus/\" + empresaID);\n};\nexport const deleteInstanceAdmin = async (userID, id) => {\n  return api.delete(`/deleteInstance-admin/${userID}`, {\n    data: {\n      id\n    }\n  });\n};\n\n// 🔥 NOVO: Funções para gerenciar fila de atendimento humano\nexport const buscarFilaAtendimento = async empresaId => {\n  return api.get(`/api/v1/whatsapp/fila-atendimento/${empresaId}`);\n};\nexport const iniciarAtendimentoFila = async (empresaId, atendimentoId, userData) => {\n  return api.post(`/api/v1/whatsapp/fila-atendimento/${empresaId}/iniciar/${atendimentoId}`, userData);\n};\nexport const finalizarAtendimentoFila = async (empresaId, atendimentoId, observacoes) => {\n  return api.post(`/api/v1/whatsapp/fila-atendimento/${empresaId}/finalizar/${atendimentoId}`, {\n    observacoes\n  });\n};\nexport const cancelarAtendimentoFila = async (empresaId, atendimentoId, motivo) => {\n  return api.delete(`/api/v1/whatsapp/fila-atendimento/${empresaId}/cancelar/${atendimentoId}`, {\n    data: {\n      motivo\n    }\n  });\n};\nexport const limparAtendimentosFinalizados = async empresaId => {\n  return api.post(`/api/v1/whatsapp/fila-atendimento/${empresaId}/limpar-finalizados`);\n};\nexport const sendTextMessageWhatsapp = async (empresaID, number, message) => {\n  //setAuthToken(token);\n  return api.post(\"/api/v1/whatsapp/sendTextMessage/\" + empresaID, {\n    number,\n    message\n  });\n};\nexport const removeWhatsappSession = async empresaID => {\n  return api.get(`/api/v1/whatsapp/removeSession/${empresaID}`);\n};\n\n// Função para configurar webhook\nexport const configureWhatsappWebhook = async empresaID => {\n  return api.post(`/api/v1/whatsapp/webhook/configure/${empresaID}`);\n};\n\n// Função para verificar status do webhook\nexport const getWhatsappWebhookStatus = async empresaID => {\n  return api.get(`/api/v1/whatsapp/webhook/status/${empresaID}`);\n};\n\n// Função para obter os chats\nexport const getWhatsappChats = async (empresaID, page, pageSize, query, token) => {\n  setAuthToken(token);\n  return api.get(`/api/v1/whatsapp/chats/${empresaID}`, {\n    params: {\n      page,\n      pageSize,\n      query\n    }\n  });\n};\n\n// Função para obter os conversa de um chat com LeadId\nexport const getWhatsappChatLead = async (empresaID, leadID, page, pageSize, token) => {\n  setAuthToken(token);\n  return api.get(`/api/v1/whatsapp/chats/${empresaID}/messages/${leadID}`, {\n    params: {\n      page,\n      pageSize\n    }\n  });\n};\n\n// Função para obter contagem de mensagens não lidas\nexport const getWhatsappUnreadCount = async (empresaID, token) => {\n  setAuthToken(token);\n  return api.get(`/api/v1/whatsapp/chats/${empresaID}/unread-count`);\n};\n\n// Função para marcar mensagens como lidas\nexport const markWhatsappMessagesAsRead = async (empresaID, leadID, token) => {\n  setAuthToken(token);\n  return api.put(`/api/v1/whatsapp/chats/${empresaID}/messages/${leadID}/mark-as-read`);\n};\n\n// 🔥 NOVO: Função para buscar um chat específico por lead_id\nexport const getWhatsappChatById = async (empresaID, leadID, token) => {\n  setAuthToken(token);\n  return api.get(`/api/v1/whatsapp/chats/${empresaID}/chat/${leadID}`);\n};\n\n// 🔥 NOVO: Função para iniciar nova conversa com um contato\nexport const createWhatsappChat = async (empresaID, contactNumber, contactName, token) => {\n  setAuthToken(token);\n  return api.post(`/api/v1/whatsapp/chats/${empresaID}/create`, {\n    contactNumber,\n    contactName\n  });\n};\nexport const getEstados = async () => {\n  return api.get(\"https://servicodados.ibge.gov.br/api/v1/localidades/estados/\");\n};\nexport const getMunicipios = async estadoSelecionado => {\n  return api.get(\"https://servicodados.ibge.gov.br/api/v1/localidades/estados/\" + estadoSelecionado + \"/municipios\");\n};\nexport const getBairros = async (city, uf) => {\n  console.log(\"Chegou no getBairros da api:\", city, uf);\n  return api.get(`/carregar-bairros?city=${city}&uf=${uf}`);\n};\nexport const calcularDistancia = async (coordenadasA, coordenadasB) => {\n  const {\n    lat: latA,\n    lng: lngA\n  } = coordenadasA;\n  const {\n    lat: latB,\n    lng: lngB\n  } = coordenadasB;\n  return api.get(`/calcular-distancia?latA=${latA}&lngA=${lngA}&latB=${latB}&lngB=${lngB}`);\n};\n\n// Mesas\n\nexport const getMesas = async () => {\n  const {\n    data\n  } = await api.get('/mesas');\n  if (!data.status) {\n    return null;\n  }\n  return data.data;\n};\nexport const createMesa = async ({\n  name,\n  qr_code\n}) => {\n  const {\n    data\n  } = await api.post('/mesas', {\n    name,\n    qr_code\n  });\n  if (!data.status) {\n    return null;\n  }\n  return data.data;\n};\nexport const createManyMesa = async ({\n  url,\n  quantity\n}) => {\n  const {\n    data\n  } = await api.post('/many_mesas', {\n    url,\n    quantity\n  });\n  if (!data.status) {\n    return null;\n  }\n  return data.data;\n};\nexport const registerPedidoMesa = async ({\n  mesa_id,\n  id_empresa,\n  itens,\n  celular_cliente,\n  nome_cliente,\n  valor_total,\n  descricao\n}) => {\n  return api.post(\"/mesas/registerPedido\", {\n    id_empresa,\n    itens,\n    mesa_id,\n    celular_cliente,\n    nome_cliente,\n    valor_total,\n    descricao\n  });\n};\nexport const deleteMesa = async ({\n  mesaId\n}) => {\n  const {\n    data\n  } = await api.delete(`/mesas/${mesaId}`);\n  if (!data.status) {\n    return null;\n  }\n  return data.data;\n};\nexport const registerPayment = async ({\n  mesa_id,\n  payment_type,\n  total_payed,\n  customer_name,\n  customer_phone\n}) => {\n  return api.post(\"/register-payment\", {\n    mesa_id,\n    payment_type,\n    total_payed,\n    customer_name,\n    customer_phone\n  });\n};\n\n// Funções para gerenciar progresso da configuração inicial\nexport const getProgressoConfiguracaoInicial = async empresaId => {\n  return api.get(`/getProgressoConfiguracaoInicial/${empresaId}`);\n};\nexport const atualizarProgressoConfiguracaoInicial = async (empresaId, etapaCompleta, proximaEtapa, finalizada = false) => {\n  return api.put(`/atualizarProgressoConfiguracaoInicial/${empresaId}`, {\n    etapaCompleta,\n    proximaEtapa,\n    finalizada\n  });\n};\n\n// Funções para solicitação de cardápio para equipe\nexport const enviarCardapioParaEquipe = async (empresaId, idEmpresa, userId, customLink, pdfFile, observacoes, userLookupName) => {\n  const formData = new FormData();\n  formData.append('empresaId', empresaId);\n  formData.append('idEmpresa', idEmpresa);\n  formData.append('userId', userId);\n  formData.append('customLink', customLink || '');\n  formData.append('observacoes', observacoes || '');\n  formData.append('userLookupName', userLookupName || '');\n  if (pdfFile) {\n    formData.append('pdfFile', pdfFile);\n  }\n  return api.post('/enviar-cardapio-para-equipe', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n};\nexport const listarSolicitacoesCardapio = async (userId, status = 'todos', page = 1, limit = 10) => {\n  return api.get(`/listar-solicitacoes-cardapio/${userId}`, {\n    params: {\n      status,\n      page,\n      limit\n    }\n  });\n};\nexport const atualizarStatusSolicitacaoCardapio = async (userId, empresaId, solicitacaoId, novoStatus, observacoesEquipe, usuarioResponsavel) => {\n  return api.put(`/atualizar-status-solicitacao-cardapio/${userId}`, {\n    empresaId,\n    solicitacaoId,\n    novoStatus,\n    observacoesEquipe,\n    usuarioResponsavel\n  });\n};\nexport const buscarSolicitacoesEmpresa = async (empresaId, userId) => {\n  return api.get(`/solicitacoes-cardapio-empresa/${empresaId}/${userId}`);\n};\nexport const getProgressoImportacao = async idEmpresa => {\n  return api.get(`/progress-import/${idEmpresa}`);\n};\nexport const resetImportStatus = async idEmpresa => {\n  return api.post(`/reset-import-status/${idEmpresa}`);\n};\nvar _c;\n$RefreshReg$(_c, \"ImprimirPedido\");", "map": {"version": 3, "names": ["AppConfigContext", "axios", "toast", "isDevelopment", "window", "location", "hostname", "apiUrl", "process", "env", "REACT_APP_SERVER_URL_DEV", "REACT_APP_SERVER_URL_PROD", "apiInstancesWppUrl", "REACT_APP_API_INSTANCES_WPP_URL", "api", "create", "baseURL", "apiInstancesWpp", "headers", "setAuthToken", "token", "defaults", "common", "createSession", "email", "password", "post", "checkLicense", "userId", "get", "getBotStatusLead", "leadId", "getUsers", "userID", "getUsersByRole", "role", "getEntregadores", "id_empresa", "listarCaixas", "id", "page", "startDate", "endDate", "params", "per_page", "getUserRole", "getPlansAsaas", "getFilPlansIugu", "getUser", "getEmpresaWithObjId", "empresaObjId", "getEmpresa", "getEmpresasAdmin", "limit", "createInstanceAdmin", "empresaId", "getCaixaById", "id_caixa", "testeImpressao", "companyId", "getPedidos", "vinculo_empresa", "getPedido", "id_pedido", "error", "console", "log", "getCategorias", "getAdicionais", "getAdicionaisCardapio", "nomeEmpresa", "getCategoriasCardapio", "getEmpresaInfo", "getItens", "getItensCardapio", "getItensCardapioSalao", "getItem", "itemObjId", "getItemCardapio", "getPedidosByStatus", "status_pedido", "forReport", "getPedidosByStatusSimples", "getPedidosByStatusFinalizados", "getPedidosFinalizadosHistorico", "periodo", "getPedidosFinalizadosPeriodo", "getPedidosToPrint", "getCliente", "telefone", "getV<PERSON>or", "id_vendedor", "getOrcamento", "id_orcamento", "getVinculoEmpresa", "getEmpresas", "getRevendas", "getClientes", "getClientesAtivos", "empresaObjectId", "toISOString", "getCompanyResponses", "getVendedores", "getOrcamentos", "updateUser", "name", "updateEntregador", "idToEdit", "veiculo", "placa", "updateTempoEntregaEmpresa", "_id", "tempoBalcaoMin", "tempoBalcaoMax", "tempoEntregaMin", "tempoEntregaMax", "tipo_impressao", "updateTempoHorarioFuncionamento", "status_loja", "horario_funcionamento", "timezone", "updateTypeRegion", "region_type_delivery", "type_of_region", "updateClienteAddress", "endereco", "enderecoToEdit", "updateRaioEntregaEmpresa", "raio_entrega", "valor_entrega", "updateBairroEntregaEmpresa", "bairro_entrega", "updateCategoriasOrder", "id_categoria", "order", "updateItensOrder", "id_item", "updateItemOut", "out", "updateItemOutSalao", "out_salao", "updateItemAdicionalOut", "idGrupo", "itemID", "titulo", "price", "id_adicional", "id_grupo", "title", "updateItemAdicionalOutSalao", "updateItemPrice", "updateItemPriceSalao", "price_salao", "updateCategorias", "modelo", "disponibilidade", "dia_horario_disponibilidade", "updateAdicionaisGroup", "adicional_objId", "idEmpresa", "minimo", "maximo", "mandatory", "calcularMaiorValor", "calcularMedia", "precificacao", "type", "min", "max", "calcular_maior_valor", "calcular_media", "updateItens", "orderItem", "category_item_id", "category_item_title", "description", "images", "has_adicional", "adicionais", "removeAdicionalFromItem", "itemId", "grupoAdicionalValue", "updateStatusPedido", "updatePedidoWithEntregador", "entregador", "apiUpdateImportFlag", "importacao_finalizada", "ImprimirPedido", "dadosPedido", "_c", "updateStatusPedidoFinalizado", "finalizadoAt", "updateStatusPrint", "status_print", "fecharCaixa", "objIdCaixa", "closedBy", "saldo_final", "valor_informado_dinheiro", "valor_informado_cartao", "valor_informado_pix", "addLancamentoCaixa", "descricao", "tipo_lancamento", "valor", "created<PERSON>y", "updateEmpresa", "cnpj", "razao", "cep", "estado", "municipio", "bairro", "complemento", "celular", "updateEmpresaAddress", "address_number", "latitude", "longitude", "updateCliente", "documento", "contato", "updateVendedor", "updateOrcamento", "codigo_cliente", "nome_cliente", "codigo_vendedor", "nome_vendedor", "total_orc", "status_orc", "items", "updateStatus", "status", "updateUserImg", "image", "deleteUser", "deleteEntregador", "cancelarPedido", "<PERSON><PERSON>a", "updateEmpresaCancelamentoPedido", "payload", "deleteEmpresa", "deleteEmpresaCompleta", "usuario", "delete", "data", "deleteCategoria", "deleteGrupoAdicional", "deleteItem", "deleteEnderecoCliente", "enderecoIdToDelete", "deleteCliente", "deleteVendedor", "deleteOrcamento", "deleteRaioEntrega", "id_raio_entrega", "deleteBairroEntrega", "id_bairro_entrega", "register", "confirmpassword", "registerGarcom", "number", "registerEntregador", "registerEmpresa", "createEmpresaUser", "nome_empresa", "nome_pessoa", "addEnderecoEmpresa", "enderecoData", "put", "createAsaasCustomer", "dadosEndereco", "registerPedido", "itens", "celular_cliente", "tipo_pagamento", "entrega", "desconto", "cpf_cnpj", "valor_troco", "valor_total", "external_id", "toggleBotStatus", "getWhatsappProfilePicture", "leadChannelID", "getMyWhatsappProfilePicture", "empresaID", "fetchWhatsappJID", "updateFormasPagamento", "formas_pagamento", "updateConfiguracoesEntrega", "entrega_disabled", "retirada_disabled", "updatePedidoFromPdv", "objIdPedido", "updatePedidoFromMesas", "registerCategoria", "only_pdv", "only_qrcode", "only_delivery_take_local", "registerItem", "registerGrupoAdicionais", "registerAdicionais", "updateItemAdicional", "deleteItemAdicional", "registerCaixaAndOpen", "saldo_inicial", "registerPlanoAdmin", "nome", "plan_identifier", "plan_cycle", "maxPayments", "valor_plano", "searchClienteAsaasAndGetInvoice", "zip_code", "payment_type", "isPromotional", "deleteAsaasPlan", "planId", "updateQuestionResponses", "company_id", "responses", "updateQuestionActive", "questionIdentifier", "active", "getCustomResponses", "addCustomResponse", "question", "response", "updateCustomResponse", "responseId", "deleteCustomResponse", "registerCliente", "registerClienteFromCardapio", "registerVendedor", "registerOrcamento", "data_emissao", "criarAssinaturaAsaasBolPix", "customerID", "cycle", "value", "nextDueDate", "criar<PERSON><PERSON><PERSON>uraAsaasCartao", "numero_cartao", "expiry<PERSON><PERSON><PERSON>", "expiryYear", "ccv", "postalCode", "addressNumber", "phone", "remoteIp", "criarCobrancaAsaas", "parcela", "criarCobrancaAsaasBolPix", "listarAssinaturasAsaas", "customer_id", "getDataCustomerAsaas", "getLastPendingInvoiceAsaas", "subscriptionIdAsaas", "getAllInvoicesAsaas", "getInvoiceIugu", "invoiceId", "importCardapioIfood", "url_import", "importCardapioAnotaai", "changeStatusLoja", "getUltimoPedidoID", "getDaysToExpireLicense", "apiCheckImportStatus", "configurarResetPedido", "dias_para_reset", "resetPedidoManual", "updateStatusBotEmpresa", "status_bot", "patch", "updateCallAtendenteEmpresa", "call_atendente", "getQrCodeWhatsapp", "renewQrCodeWhatsapp", "refreshProfilePicturesWhatsapp", "refreshContactInfoWhatsapp", "getWhatsappConnectionStatus", "deleteInstanceAdmin", "buscarFilaAtendimento", "iniciarAtendimentoFila", "atendimentoId", "userData", "finalizarAtendimentoFila", "observacoes", "cancelarAtendimentoFila", "motivo", "limparAtendimentosFinalizados", "sendTextMessageWhatsapp", "message", "removeWhatsappSession", "configureWhatsappWebhook", "getWhatsappWebhookStatus", "getWhatsappChats", "pageSize", "query", "getWhatsappChatLead", "leadID", "getWhatsappUnreadCount", "markWhatsappMessagesAsRead", "getWhatsappChatById", "createWhatsappChat", "contactNumber", "contactName", "getEstados", "getMunicipios", "estadoSelecionado", "getBairros", "city", "uf", "calcularDistancia", "coordenadasA", "coordenadasB", "lat", "latA", "lng", "lngA", "latB", "lngB", "getMesas", "createMesa", "qr_code", "createManyMesa", "url", "quantity", "registerPedidoMesa", "mesa_id", "deleteMesa", "mesaId", "registerPayment", "total_payed", "customer_name", "customer_phone", "getProgressoConfiguracaoInicial", "atualizarProgressoConfiguracaoInicial", "etapaCompleta", "proximaEtapa", "finalizada", "enviarCardapioParaEquipe", "customLink", "pdfFile", "userLookupName", "formData", "FormData", "append", "listarSolicitacoesCardapio", "atualizarStatusSolicitacaoCardapio", "solicitacaoId", "novoStatus", "observacoesEquipe", "usuarioResponsavel", "buscarSolicitacoesEmpresa", "getProgressoImportacao", "resetImportStatus", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/services/api.js"], "sourcesContent": ["import { AppConfigContext } from \"antd/es/app/context\";\r\nimport axios from \"axios\";\r\nimport { toast } from 'react-toastify';\r\n\r\nconst isDevelopment = window.location.hostname === \"localhost\" ? true : false;\r\nconst apiUrl = isDevelopment\r\n  ? process.env.REACT_APP_SERVER_URL_DEV\r\n  : process.env.REACT_APP_SERVER_URL_PROD;\r\nconst apiInstancesWppUrl = process.env.REACT_APP_API_INSTANCES_WPP_URL;\r\nexport const api = axios.create({\r\n  baseURL: apiUrl,\r\n});\r\n\r\nconst apiInstancesWpp = axios.create({\r\n  baseURL: apiInstancesWppUrl,\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n});\r\n\r\n// Função para configurar o token de autorização\r\nconst setAuthToken = (token) => {\r\n  if (token) {\r\n    // Aplica o token de autorização a todas as requisições se o token estiver presente\r\n    api.defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\r\n  } else {\r\n    // Remove o header de autorização se o token não estiver presente\r\n    delete api.defaults.headers.common[\"Authorization\"];\r\n  }\r\n};\r\n\r\nexport const createSession = async (email, password) => {\r\n  return api.post(\"/auth/login\", { email, password });\r\n};\r\n\r\nexport const checkLicense = (userId) => {\r\n  return api.get(`/check-license/${userId}`);\r\n};\r\n\r\nexport const getBotStatusLead = (leadId) => {\r\n  return api.get(`/auth/bot-status-lead/${leadId}`);\r\n};\r\n\r\n\r\nexport const getUsers = async (userID) => {\r\n  return api.get(\"/list-users/\" + userID);\r\n};\r\n\r\nexport const getUsersByRole = async (role) => {\r\n  return api.get(`/list-users-by-role/${role}`);\r\n};\r\n\r\nexport const getEntregadores = async (id_empresa) => {\r\n  return api.get(\"/list-entregadores/\" + id_empresa);\r\n};\r\n\r\nexport const listarCaixas = async (id, page, startDate, endDate) => {\r\n  return api.get(\"/list-caixas/\" + id, {\r\n    params: {\r\n      page,\r\n      per_page: 10,\r\n      startDate,\r\n      endDate,\r\n    },\r\n  });\r\n};\r\n\r\nexport const getUserRole = async (userID) => {\r\n  return api.get(\"/user-role/\" + userID);\r\n};\r\n\r\nexport const getPlansAsaas = async (userID) => {\r\n  return api.get(\"/list-plans/\" + userID);\r\n};\r\n\r\nexport const getFilPlansIugu = async (userID) => {\r\n  return api.get(\"/list-fil-plans/\" + userID);\r\n};\r\n\r\n\r\nexport const getUser = async (userID) => {\r\n  return api.get(\"/user/\" + userID);\r\n};\r\n\r\nexport const getEmpresaWithObjId = async (empresaObjId) => {\r\n  return api.get(\"/empresaWithObjId/\" + empresaObjId);\r\n};\r\n\r\nexport const getEmpresa = async (userID, id_empresa) => {\r\n  return api.post(\"/get-empresa/\" + userID, { id_empresa });\r\n};\r\n\r\nexport const getEmpresasAdmin = async (userID, page, limit) => {\r\n  return api.get(`/get-empresas-admin/${userID}?page=${page}&limit=${limit}`);\r\n};\r\n\r\nexport const createInstanceAdmin = async (userID, empresaId) => {\r\n  return api.post(`/createInstance-admin/${userID}/${empresaId}`);\r\n};\r\n\r\nexport const getCaixaById = async (userID, id_caixa) => {\r\n  return api.post(\"/get-caixa/\" + userID, { id_caixa });\r\n};\r\n\r\nexport const testeImpressao = async (companyId) => {\r\n  return api.post(\"/print-test\", { companyId });\r\n}\r\n\r\nexport const getPedidos = async (userID, vinculo_empresa) => {\r\n  return api.post(\"/get-pedidos/\" + userID, { vinculo_empresa });\r\n};\r\n\r\nexport const getPedido = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa,\r\n  id_pedido\r\n) => {\r\n  try {\r\n    return await api.post(\"/get-pedido/\" + userID, {\r\n      id_empresa,\r\n      vinculo_empresa,\r\n      id_pedido,\r\n    });\r\n  } catch (error) {\r\n    console.log(\"Erro ao buscar pedido\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getCategorias = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa,\r\n  empresaObjId\r\n) => {\r\n  return api.post(\"/get-categorias/\" + userID, {\r\n    id_empresa,\r\n    vinculo_empresa,\r\n    empresaObjId,\r\n  });\r\n};\r\n\r\nexport const getAdicionais = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa,\r\n  empresaObjId\r\n) => {\r\n  return api.post(\"/get-adicionais/\" + userID, {\r\n    id_empresa,\r\n    vinculo_empresa,\r\n    empresaObjId,\r\n  });\r\n};\r\n\r\nexport const getAdicionaisCardapio = async (id_empresa, nomeEmpresa) => {\r\n  return api.post(\"/get-adicionais-cardapio/\", { id_empresa, nomeEmpresa });\r\n};\r\n\r\nexport const getCategoriasCardapio = async (id_empresa, nomeEmpresa) => {\r\n  return api.post(\"/get-categorias-cardapio/\", { id_empresa, nomeEmpresa });\r\n};\r\n\r\nexport const getEmpresaInfo = async (id_empresa, nomeEmpresa) => {\r\n  return api.post(\"/get-empresa-info\", { id_empresa, nomeEmpresa });\r\n};\r\n\r\nexport const getItens = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa,\r\n  empresaObjId\r\n) => {\r\n  return api.post(\"/get-itens/\" + userID, {\r\n    id_empresa,\r\n    vinculo_empresa,\r\n    empresaObjId,\r\n  });\r\n};\r\n\r\nexport const getItensCardapio = async (id_empresa, nomeEmpresa) => {\r\n  return api.post(\"/get-itens-cardapio/\", { id_empresa, nomeEmpresa });\r\n};\r\n\r\nexport const getItensCardapioSalao = async (id_empresa, nomeEmpresa) => {\r\n  return api.post(\"/get-itens-cardapio-salao/\", { id_empresa, nomeEmpresa });\r\n};\r\n\r\nexport const getItem = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa,\r\n  empresaObjId,\r\n  itemObjId\r\n) => {\r\n  return api.post(\"/get-item/\" + userID, {\r\n    id_empresa,\r\n    vinculo_empresa,\r\n    empresaObjId,\r\n    itemObjId,\r\n  });\r\n};\r\n\r\nexport const getItemCardapio = async (id_empresa, itemObjId) => {\r\n  return api.post(\"/get-item-cardapio/\", { id_empresa, itemObjId });\r\n};\r\n\r\nexport const getPedidosByStatus = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa,\r\n  status_pedido,\r\n  forReport\r\n) => {\r\n  return api.post(\"/get-pedidosStatus/\" + userID, {\r\n    id_empresa,\r\n    vinculo_empresa,\r\n    status_pedido,\r\n    forReport\r\n  });\r\n};\r\n\r\nexport const getPedidosByStatusSimples = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa,\r\n  status_pedido,\r\n  forReport\r\n) => {\r\n  return api.post(\"/get-pedidosStatusSimples/\" + userID, {\r\n    id_empresa,\r\n    vinculo_empresa,\r\n    status_pedido,\r\n    forReport\r\n  });\r\n};\r\n\r\nexport const getPedidosByStatusFinalizados = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa,\r\n  forReport\r\n) => {\r\n  return api.post(\"/get-pedidosStatusFinalizados/\" + userID, {\r\n    id_empresa,\r\n    vinculo_empresa,\r\n    forReport\r\n  });\r\n};\r\n\r\nexport const getPedidosFinalizadosHistorico = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa,\r\n  status_pedido,\r\n  periodo\r\n) => {\r\n  return api.post(\"/get-pedidosFinalizadosHistorico/\" + userID, {\r\n    id_empresa,\r\n    vinculo_empresa,\r\n    status_pedido,\r\n    periodo,\r\n  });\r\n};\r\n\r\nexport const getPedidosFinalizadosPeriodo = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa,\r\n  status_pedido,\r\n  startDate,\r\n  endDate\r\n) => {\r\n  return api.post(\"/get-pedidosFinalizadosPeriodo/\" + userID, {\r\n    id_empresa,\r\n    vinculo_empresa,\r\n    status_pedido,\r\n    startDate,\r\n    endDate,\r\n  });\r\n};\r\n\r\nexport const getPedidosToPrint = async (\r\n  userID,\r\n  id_empresa,\r\n  vinculo_empresa\r\n) => {\r\n  return api.post(\"/get-pedidos-toprint/\" + userID, {\r\n    id_empresa,\r\n    vinculo_empresa,\r\n  });\r\n};\r\n\r\nexport const getCliente = async (userID, id_empresa, telefone) => {\r\n  return api.post(\"/get-cliente/\" + userID, { id_empresa, telefone });\r\n};\r\n\r\nexport const getVendedor = async (userID, id_vendedor) => {\r\n  return api.post(\"/get-vendedor/\" + userID, { id_vendedor });\r\n};\r\n\r\nexport const getOrcamento = async (userID, id_orcamento) => {\r\n  return api.post(\"/get-orcamento/\" + userID, { id_orcamento });\r\n};\r\n\r\nexport const getVinculoEmpresa = async (userID) => {\r\n  return api.get(\"/vinculo-empresa/\" + userID);\r\n};\r\n\r\nexport const getEmpresas = async (userID) => {\r\n  return api.get(\"/list-empresas/\" + userID);\r\n};\r\n\r\nexport const getRevendas = async (userID) => {\r\n  return api.get(\"/list-revendas/\" + userID);\r\n};\r\n\r\nexport const getClientes = async (userID) => {\r\n  return api.get(\"/list-clientes/\" + userID);\r\n};\r\n\r\nexport const getClientesAtivos = async (empresaObjectId, startDate, endDate) => {\r\n  return api.get(`/clientes-ativos/${empresaObjectId}`, {\r\n    params: {\r\n      startDate: startDate.toISOString(), // Converte para formato UTC\r\n      endDate: endDate.toISOString()\r\n    }\r\n  });\r\n};\r\n\r\n\r\nexport const getCompanyResponses = async (companyId) => {\r\n  return api.get(\"/company-responses/\" + companyId);\r\n};\r\n\r\nexport const getVendedores = async (userID) => {\r\n  return api.get(\"/list-vendedores/\" + userID);\r\n};\r\n\r\nexport const getOrcamentos = async (userID) => {\r\n  return api.get(\"/list-orcamentos/\" + userID);\r\n};\r\n\r\nexport const updateUser = async (\r\n  id,\r\n  userID,\r\n  email,\r\n  password,\r\n  name\r\n) => {\r\n  return api.post(\"/update-user/\" + userID, {\r\n    id,\r\n    email,\r\n    password,\r\n    name\r\n  });\r\n};\r\n\r\nexport const updateEntregador = async (\r\n  idToEdit,\r\n  userID,\r\n  id_empresa,\r\n  name,\r\n  veiculo,\r\n  telefone,\r\n  placa\r\n) => {\r\n  return api.post(\"/update-entregador/\" + userID, {\r\n    idToEdit,\r\n    id_empresa,\r\n    name,\r\n    veiculo,\r\n    telefone,\r\n    placa,\r\n  });\r\n};\r\n\r\nexport const updateTempoEntregaEmpresa = async (\r\n  userID,\r\n  _id,\r\n  id,\r\n  tempoBalcaoMin,\r\n  tempoBalcaoMax,\r\n  tempoEntregaMin,\r\n  tempoEntregaMax,\r\n  tipo_impressao\r\n) => {\r\n  return api.post(\"/update-empresaTempoEntrega/\" + userID, {\r\n    _id,\r\n    id,\r\n    tempoBalcaoMin,\r\n    tempoBalcaoMax,\r\n    tempoEntregaMin,\r\n    tempoEntregaMax,\r\n    tipo_impressao,\r\n  });\r\n};\r\n\r\nexport const updateTempoHorarioFuncionamento = async (\r\n  userID,\r\n  _id,\r\n  id,\r\n  status_loja,\r\n  horario_funcionamento,\r\n  timezone\r\n) => {\r\n  return api.post(\"/update-empresaHorarioFuncionamento/\" + userID, {\r\n    _id,\r\n    id,\r\n    status_loja,\r\n    horario_funcionamento,\r\n    timezone,\r\n  });\r\n};\r\n\r\nexport const updateTypeRegion = async (\r\n  _id,\r\n  id,\r\n  userID,\r\n  region_type_delivery\r\n) => {\r\n  return api.post(\"/update-empresa/\" + userID, {\r\n    _id: _id,\r\n    id: id,\r\n    region_type_delivery: region_type_delivery,\r\n    type_of_region: region_type_delivery,\r\n  });\r\n};\r\n\r\nexport const updateClienteAddress = async (\r\n  id_empresa,\r\n  telefone,\r\n  endereco,\r\n  enderecoToEdit\r\n) => {\r\n  return api.post(\"/update-endereco-cliente/\", {\r\n    id_empresa,\r\n    telefone,\r\n    endereco,\r\n    enderecoToEdit,\r\n  });\r\n};\r\n\r\nexport const updateRaioEntregaEmpresa = async (\r\n  _id,\r\n  id,\r\n  userID,\r\n  raio_entrega,\r\n  valor_entrega\r\n) => {\r\n  return api.post(\"/update-raioEntrega/\" + userID, {\r\n    _id: _id,\r\n    id: id,\r\n    raio_entrega: raio_entrega,\r\n    valor_entrega: valor_entrega,\r\n  });\r\n};\r\n\r\nexport const updateBairroEntregaEmpresa = async (\r\n  _id,\r\n  id,\r\n  userID,\r\n  bairro_entrega,\r\n  valor_entrega\r\n) => {\r\n  return api.post(\"/update-bairroEntrega/\" + userID, {\r\n    _id: _id,\r\n    id: id,\r\n    bairro_entrega: bairro_entrega,\r\n    valor_entrega: valor_entrega,\r\n  });\r\n};\r\n\r\nexport const updateCategoriasOrder = async (\r\n  userID,\r\n  _id,\r\n  id_categoria,\r\n  id_empresa,\r\n  order\r\n) => {\r\n  return api.post(\"/update-categoria/\" + userID, {\r\n    _id: _id,\r\n    id_categoria: id_categoria,\r\n    id_empresa: id_empresa,\r\n    order: order,\r\n  });\r\n};\r\n\r\nexport const updateItensOrder = async (\r\n  userID,\r\n  _id,\r\n  id_item,\r\n  id_empresa,\r\n  order\r\n) => {\r\n  return api.post(\"/update-item/\" + userID, {\r\n    _id: _id,\r\n    id_item: id_item,\r\n    id_empresa: id_empresa,\r\n    order: order,\r\n  });\r\n};\r\n\r\nexport const updateItemOut = async (userID, _id, id_item, id_empresa, out) => {\r\n  return api.post(\"/update-item/\" + userID, {\r\n    _id: _id,\r\n    id_item: id_item,\r\n    id_empresa: id_empresa,\r\n    out: out,\r\n  });\r\n};\r\n\r\nexport const updateItemOutSalao = async (userID, _id, id_item, id_empresa, out) => {\r\n  return api.post(\"/update-item/\" + userID, {\r\n    _id: _id,\r\n    id_item: id_item,\r\n    id_empresa: id_empresa,\r\n    out_salao: out,\r\n  });\r\n};\r\n\r\nexport const updateItemAdicionalOut = async (userID, idGrupo, itemID, titulo, price, id_adicional, id_empresa, out) => {\r\n  return api.post(\"/auth/updateAdicional/\" + userID, {\r\n    _id: itemID,\r\n    id_grupo: idGrupo,\r\n    title: titulo,\r\n    price: price,\r\n    id_adicional: id_adicional,\r\n    id_empresa: id_empresa,\r\n    out: out,\r\n  });\r\n};\r\n\r\nexport const updateItemAdicionalOutSalao = async (userID, idGrupo, itemID, titulo, price, id_adicional, id_empresa, out) => {\r\n  return api.post(\"/auth/updateAdicional/\" + userID, {\r\n    _id: itemID,\r\n    id_grupo: idGrupo,\r\n    title: titulo,\r\n    price: price,\r\n    id_adicional: id_adicional,\r\n    id_empresa: id_empresa,\r\n    out_salao: out,\r\n  });\r\n};\r\n\r\nexport const updateItemPrice = async (\r\n  userID,\r\n  _id,\r\n  id_item,\r\n  id_empresa,\r\n  price\r\n) => {\r\n  return api.post(\"/update-item/\" + userID, {\r\n    _id: _id,\r\n    id_item: id_item,\r\n    id_empresa: id_empresa,\r\n    price: price,\r\n  });\r\n};\r\n\r\nexport const updateItemPriceSalao = async (\r\n  userID,\r\n  _id,\r\n  id_item,\r\n  id_empresa,\r\n  price\r\n) => {\r\n  return api.post(\"/update-item/\" + userID, {\r\n    _id: _id,\r\n    id_item: id_item,\r\n    id_empresa: id_empresa,\r\n    price_salao: price,\r\n  });\r\n};\r\n\r\nexport const updateCategorias = async (\r\n  userID,\r\n  _id,\r\n  id_categoria,\r\n  id_empresa,\r\n  modelo,\r\n  title,\r\n  disponibilidade,\r\n  dia_horario_disponibilidade\r\n) => {\r\n  return api.post(\"/update-categoria/\" + userID, {\r\n    _id: _id,\r\n    id_categoria: id_categoria,\r\n    id_empresa: id_empresa,\r\n    modelo: modelo,\r\n    title: title,\r\n    disponibilidade: disponibilidade,\r\n    dia_horario_disponibilidade: dia_horario_disponibilidade,\r\n  });\r\n};\r\n\r\nexport const updateAdicionaisGroup = async ({\r\n  userID,\r\n  adicional_objId,\r\n  idEmpresa,\r\n  title,\r\n  minimo,\r\n  maximo,\r\n  mandatory,\r\n  calcularMaiorValor,\r\n  calcularMedia,\r\n  precificacao,\r\n  type\r\n}) => {\r\n  return api.post(\"/update-grupo-adicionais/\" + userID, {\r\n    _id: adicional_objId,\r\n    id_empresa: idEmpresa,\r\n    title: title,\r\n    min: minimo,\r\n    max: maximo,\r\n    mandatory: mandatory,\r\n    calcular_maior_valor: calcularMaiorValor,\r\n    calcular_media: calcularMedia,\r\n    precificacao: precificacao,\r\n    type: type\r\n  });\r\n};\r\n\r\nexport const updateItens = async (\r\n  userID,\r\n  itemObjId,\r\n  id_item,\r\n  idEmpresa,\r\n  orderItem,\r\n  category_item_id,\r\n  category_item_title,\r\n  title,\r\n  description,\r\n  out,\r\n  images,\r\n  price,\r\n  has_adicional,\r\n  adicionais,\r\n  type\r\n) => {\r\n  return api.post(\"/update-item/\" + userID, {\r\n    _id: itemObjId,\r\n    id_item: id_item,\r\n    id_empresa: idEmpresa,\r\n    order: orderItem,\r\n    category_item_id: category_item_id,\r\n    category_item_title: category_item_title,\r\n    title: title,\r\n    description: description,\r\n    out: out,\r\n    images: images,\r\n    price: price,\r\n    has_adicional: has_adicional,\r\n    adicionais: adicionais,\r\n    type: type,\r\n  });\r\n};\r\n\r\nexport const removeAdicionalFromItem = async (\r\n  userID,\r\n  itemId,\r\n  grupoAdicionalValue\r\n) => {\r\n  return api.post(\"/remove-grupoAdicionaisFromItem/\" + userID, {\r\n    itemId,\r\n    grupoAdicionalValue\r\n  });\r\n};\r\n\r\nexport const updateStatusPedido = async (\r\n  userID,\r\n  _id,\r\n  id_pedido,\r\n  status_pedido\r\n) => {\r\n  return api.post(\"/update-pedido/\" + userID, {\r\n    _id: _id,\r\n    id_pedido: id_pedido,\r\n    status_pedido: status_pedido,\r\n    //\"status_print\":status_print,\r\n  });\r\n};\r\n\r\nexport const updatePedidoWithEntregador = async (\r\n  userID,\r\n  _id,\r\n  id_pedido,\r\n  entregador\r\n) => {\r\n  return axios.post(`${apiUrl}/update-pedido/${userID}`, {\r\n    _id: _id,\r\n    id_pedido: id_pedido,\r\n    entregador: entregador,\r\n  });\r\n};\r\n\r\nexport const apiUpdateImportFlag = async (\r\n  idEmpresa,\r\n  importacao_finalizada\r\n) => {\r\n  return api.post(`/update-import-flag/${idEmpresa}`, {\r\n    importacao_finalizada\r\n  });\r\n};\r\n\r\nexport const ImprimirPedido = async (userID, dadosPedido) => {\r\n  return api.post(\"/command-print/\" + userID, {\r\n    dadosPedido,\r\n  });\r\n};\r\n\r\nexport const updateStatusPedidoFinalizado = async (\r\n  userID,\r\n  _id,\r\n  id_pedido,\r\n  status_pedido,\r\n  finalizadoAt\r\n) => {\r\n  return api.post(\"/update-pedido/\" + userID, {\r\n    _id: _id,\r\n    id_pedido: id_pedido,\r\n    status_pedido: status_pedido,\r\n    //\"status_print\":status_print,\r\n    finalizadoAt: finalizadoAt,\r\n  });\r\n};\r\n\r\nexport const updateStatusPrint = async (userID, _id, id_pedido) => {\r\n  //console.log(\"caiuNoUpdateStatusPrint>\", id_pedido);\r\n  return api.post(\"/update-pedido/\" + userID, {\r\n    _id: _id,\r\n    id_pedido: id_pedido,\r\n    status_pedido: \"2\",\r\n    status_print: false,\r\n  });\r\n};\r\n\r\nexport const fecharCaixa = async (\r\n  id_empresa,\r\n  objIdCaixa,\r\n  closedBy,\r\n  saldo_final,\r\n  valor_informado_dinheiro,\r\n  valor_informado_cartao,\r\n  valor_informado_pix\r\n) => {\r\n  return api.post(\"/closeCaixa/\", {\r\n    id_empresa,\r\n    objIdCaixa,\r\n    closedBy,\r\n    saldo_final,\r\n    valor_informado_dinheiro,\r\n    valor_informado_cartao,\r\n    valor_informado_pix,\r\n  });\r\n};\r\nexport const addLancamentoCaixa = async (\r\n  id_empresa,\r\n  objIdCaixa,\r\n  descricao,\r\n  tipo_lancamento,\r\n  valor,\r\n  createdBy\r\n) => {\r\n  return api.post(\"/addLancamentoCaixa/\", {\r\n    id_empresa,\r\n    objIdCaixa,\r\n    descricao,\r\n    tipo_lancamento,\r\n    valor,\r\n    createdBy,\r\n  });\r\n};\r\n\r\nexport const updateEmpresa = async (\r\n  id,\r\n  userID,\r\n  cnpj,\r\n  name,\r\n  razao,\r\n  email,\r\n  cep,\r\n  estado,\r\n  municipio,\r\n  bairro,\r\n  complemento,\r\n  telefone,\r\n  celular\r\n) => {\r\n  console.log(\"caiu no updateEmpresa Normal\");\r\n  return api.post(\"/update-empresa/\" + userID, {\r\n    id,\r\n    cnpj,\r\n    name,\r\n    razao,\r\n    email,\r\n    cep,\r\n    estado,\r\n    municipio,\r\n    bairro,\r\n    complemento,\r\n    telefone,\r\n    celular,\r\n  });\r\n};\r\n\r\nexport const updateEmpresaAddress = async (\r\n  _id,\r\n  id,\r\n  userID,\r\n  cep,\r\n  estado,\r\n  municipio,\r\n  bairro,\r\n  address_number,\r\n  complemento,\r\n  latitude,\r\n  longitude\r\n) => {\r\n  return api.post(\"/update-empresa/\" + userID, {\r\n    _id,\r\n    id,\r\n    cep,\r\n    estado,\r\n    municipio,\r\n    bairro,\r\n    address_number,\r\n    complemento,\r\n    latitude,\r\n    longitude\r\n  });\r\n};\r\n\r\nexport const updateCliente = async (\r\n  id,\r\n  userID,\r\n  documento,\r\n  name,\r\n  razao,\r\n  contato,\r\n  email,\r\n  cep,\r\n  estado,\r\n  municipio,\r\n  bairro,\r\n  complemento,\r\n  telefone,\r\n  celular,\r\n  type\r\n) => {\r\n  if (type === \"F\") {\r\n    return api.post(\"/update-cliente/\" + userID, {\r\n      id,\r\n      documento,\r\n      name,\r\n      contato,\r\n      email,\r\n      cep,\r\n      estado,\r\n      municipio,\r\n      bairro,\r\n      complemento,\r\n      telefone,\r\n      celular,\r\n    });\r\n  } else {\r\n    return api.post(\"/update-cliente/\" + userID, {\r\n      id,\r\n      documento,\r\n      name,\r\n      razao,\r\n      email,\r\n      cep,\r\n      estado,\r\n      municipio,\r\n      bairro,\r\n      complemento,\r\n      telefone,\r\n      celular,\r\n    });\r\n  }\r\n};\r\n\r\nexport const updateVendedor = async (\r\n  id,\r\n  userID,\r\n  documento,\r\n  name,\r\n  cep,\r\n  estado,\r\n  municipio,\r\n  bairro,\r\n  complemento,\r\n  telefone,\r\n  celular\r\n) => {\r\n  return api.post(\"/update-vendedor/\" + userID, {\r\n    id,\r\n    documento,\r\n    name,\r\n    cep,\r\n    estado,\r\n    municipio,\r\n    bairro,\r\n    complemento,\r\n    telefone,\r\n    celular,\r\n  });\r\n};\r\n\r\nexport const updateOrcamento = async (\r\n  id,\r\n  userID,\r\n  codigo_cliente,\r\n  nome_cliente,\r\n  codigo_vendedor,\r\n  nome_vendedor,\r\n  total_orc,\r\n  status_orc,\r\n  id_grupo,\r\n  vinculo_empresa,\r\n  items\r\n) => {\r\n  return api.post(\"/update-orcamento/\" + userID, {\r\n    id,\r\n    codigo_cliente,\r\n    nome_cliente,\r\n    codigo_vendedor,\r\n    nome_vendedor,\r\n    total_orc,\r\n    status_orc,\r\n    id_grupo,\r\n    vinculo_empresa,\r\n    items,\r\n  });\r\n};\r\n\r\nexport const updateStatus = async (id_empresa, userID, status) => {\r\n  return api.post(\"/update-status/\" + userID, { id_empresa, status });\r\n};\r\n\r\nexport const updateUserImg = async (userID, image) => {\r\n  return api.post(\"/update-user-img/\" + userID, { image });\r\n};\r\n\r\nexport const deleteUser = async (id, userID) => {\r\n  return api.post(\"/delete-user/\" + userID, { id });\r\n};\r\n\r\nexport const deleteEntregador = async (id, userID) => {\r\n  return api.post(\"/delete-entregador/\" + userID, { id });\r\n};\r\n\r\nexport const cancelarPedido = async (id, userID, empresaId, senha) => {\r\n  return api.post(\"/cancelar-pedido/\" + userID, { id, empresaId, senha });\r\n};\r\n\r\nexport const updateEmpresaCancelamentoPedido = async (payload) => {\r\n  return api.post(\"/configurar-senha-cancelamento\", payload);\r\n};\r\n\r\nexport const deleteEmpresa = async (userID, id) => {\r\n  return api.post(\"/delete-empresa/\" + userID, { id });\r\n};\r\n\r\nexport const deleteEmpresaCompleta = async (empresaObjId, usuario, senha) => {\r\n  return api.delete(\"/delete-empresa-completa/\" + empresaObjId, { \r\n    data: { usuario, senha } \r\n  });\r\n};\r\n\r\nexport const deleteCategoria = async (userID, id) => {\r\n  return api.post(\"/delete-categoria/\" + userID, { id });\r\n};\r\n\r\nexport const deleteGrupoAdicional = async (userID, id) => {\r\n  return api.post(\"/delete-grupo-adicional/\" + userID, { id });\r\n};\r\n\r\nexport const deleteItem = async (userID, id) => {\r\n  return api.post(\"/delete-item/\" + userID, { id });\r\n};\r\n\r\nexport const deleteEnderecoCliente = async (\r\n  id_empresa,\r\n  telefone,\r\n  enderecoIdToDelete\r\n) => {\r\n  return api.post(\"/delete-endereco-cliente/\", {\r\n    id_empresa,\r\n    telefone,\r\n    enderecoIdToDelete,\r\n  });\r\n};\r\n\r\nexport const deleteCliente = async (id, userID) => {\r\n  return api.post(\"/delete-cliente/\" + userID, { id });\r\n};\r\n\r\nexport const deleteVendedor = async (id, userID) => {\r\n  return api.post(\"/delete-vendedor/\" + userID, { id });\r\n};\r\n\r\nexport const deleteOrcamento = async (id, userID) => {\r\n  return api.post(\"/delete-orcamento/\" + userID, { id });\r\n};\r\n\r\nexport const deleteRaioEntrega = async (\r\n  userID,\r\n  id_empresa,\r\n  id_raio_entrega\r\n) => {\r\n  return api.post(\"/delete-raioEntrega/\" + userID, {\r\n    id_empresa,\r\n    id_raio_entrega,\r\n  });\r\n};\r\n\r\nexport const deleteBairroEntrega = async (\r\n  userID,\r\n  id_empresa,\r\n  id_bairro_entrega\r\n) => {\r\n  return api.post(\"/delete-bairroEntrega/\" + userID, {\r\n    id_empresa,\r\n    id_bairro_entrega,\r\n  });\r\n};\r\n\r\nexport const register = async (\r\n  createdBy,\r\n  name,\r\n  email,\r\n  password,\r\n  confirmpassword,\r\n  vinculo_empresa,\r\n  role\r\n) => {\r\n  return api.post(\"/auth/register\", {\r\n    createdBy,\r\n    name,\r\n    email,\r\n    password,\r\n    confirmpassword,\r\n    vinculo_empresa,\r\n    role,\r\n  });\r\n};\r\n\r\nexport const registerGarcom = async (\r\n  name,\r\n  email,\r\n  password,\r\n  number\r\n) => {\r\n  return api.post(\"/users/garcom\", {\r\n    name,\r\n    email,\r\n    password,\r\n    number\r\n  });\r\n};\r\n\r\nexport const registerEntregador = async (\r\n  createdBy,\r\n  id_empresa,\r\n  name,\r\n  telefone,\r\n  veiculo,\r\n  placa\r\n) => {\r\n  return api.post(\"/registerEntregador\", {\r\n    createdBy,\r\n    id_empresa,\r\n    name,\r\n    telefone,\r\n    veiculo,\r\n    placa,\r\n  });\r\n};\r\n\r\nexport const registerEmpresa = async (\r\n  createdBy,\r\n  cnpj,\r\n  name,\r\n  razao,\r\n  email,\r\n  cep,\r\n  estado,\r\n  municipio,\r\n  bairro,\r\n  complemento,\r\n  telefone,\r\n  celular,\r\n  id_grupo,\r\n  type\r\n) => {\r\n  return api.post(\"/auth/registerEmpresa\", {\r\n    createdBy,\r\n    cnpj,\r\n    name,\r\n    razao,\r\n    email,\r\n    cep,\r\n    estado,\r\n    municipio,\r\n    bairro,\r\n    complemento,\r\n    telefone,\r\n    celular,\r\n    id_grupo,\r\n    type,\r\n  });\r\n};\r\n\r\nexport const createEmpresaUser = async (\r\n  cnpj,\r\n  nome_empresa,\r\n  email,\r\n  telefone,\r\n  type,\r\n  nome_pessoa,\r\n  password,\r\n  confirmpassword,\r\n  vinculo_empresa\r\n) => {\r\n  return api.post(\"/createEmpresaUser\", {\r\n    cnpj,\r\n    nome_empresa,\r\n    email,\r\n    telefone,\r\n    type,\r\n    nome_pessoa,\r\n    password,\r\n    confirmpassword,\r\n    vinculo_empresa,\r\n  });\r\n};\r\n\r\n// Função para adicionar endereço à empresa\r\nexport const addEnderecoEmpresa = async (empresaId, enderecoData) => {\r\n  return api.put(`/addEnderecoEmpresa/${empresaId}`, enderecoData);\r\n};\r\n\r\n// Função para criar cliente Asaas\r\nexport const createAsaasCustomer = async (empresaId, dadosEndereco) => {\r\n  return api.post(`/createAsaasCustomer/${empresaId}`, dadosEndereco);\r\n};\r\n\r\nexport const registerPedido = async (\r\n  createdBy,\r\n  id_empresa,\r\n  itens,\r\n  celular_cliente,\r\n  nome_cliente,\r\n  tipo_pagamento,\r\n  entrega,\r\n  desconto,\r\n  cpf_cnpj,\r\n  valor_troco,\r\n  valor_total,\r\n  descricao,\r\n  external_id\r\n) => {\r\n  return api.post(\"/auth/registerPedido\", {\r\n    createdBy,\r\n    id_empresa,\r\n    itens,\r\n    celular_cliente,\r\n    nome_cliente,\r\n    tipo_pagamento,\r\n    entrega,\r\n    desconto,\r\n    cpf_cnpj,\r\n    valor_troco,\r\n    valor_total,\r\n    descricao,\r\n    external_id\r\n  });\r\n};\r\n\r\nexport const toggleBotStatus = async (leadId) => {\r\n  return api.put(`/auth/toggle-bot/${leadId}`);\r\n};\r\n\r\n// Função para buscar e salvar imagem de perfil do WhatsApp\r\nexport const getWhatsappProfilePicture = async (leadChannelID, token) => {\r\n  setAuthToken(token);\r\n  return api.post(`/api/v1/whatsapp/get-profile-picture/${leadChannelID}`);\r\n};\r\n\r\n// Função para buscar imagem de perfil do próprio usuário (dono da instância)\r\nexport const getMyWhatsappProfilePicture = async (empresaID, token) => {\r\n  setAuthToken(token);\r\n  return api.get(`/api/v1/whatsapp/get-my-profile-picture/${empresaID}`);\r\n};\r\n\r\n// Função para forçar busca e salvamento do JID\r\nexport const fetchWhatsappJID = async (empresaID, token) => {\r\n  setAuthToken(token);\r\n  return api.post(`/api/v1/whatsapp/fetch-jid/${empresaID}`);\r\n};\r\n\r\nexport const updateFormasPagamento = async (empresaId, formas_pagamento) => {\r\n  return api.put(`/empresa/${empresaId}/formas-pagamento`, { formas_pagamento });\r\n};\r\n\r\nexport const updateConfiguracoesEntrega = async (empresaId, entrega_disabled, retirada_disabled) => {\r\n  return api.put(`/empresa/${empresaId}/configuracoes-entrega`, { entrega_disabled, retirada_disabled });\r\n};\r\n\r\nexport const updatePedidoFromPdv = async (\r\n  objIdPedido,\r\n  id_empresa,\r\n  itens,\r\n  celular_cliente,\r\n  nome_cliente,\r\n  tipo_pagamento,\r\n  entrega,\r\n  desconto,\r\n  cpf_cnpj,\r\n  valor_troco,\r\n  valor_total,\r\n  descricao,\r\n  external_id\r\n) => {\r\n  return api.put(\"/auth/updatePedidoFromPdv\", {\r\n    objIdPedido,\r\n    id_empresa,\r\n    itens,\r\n    celular_cliente,\r\n    nome_cliente,\r\n    tipo_pagamento,\r\n    entrega,\r\n    desconto,\r\n    cpf_cnpj,\r\n    valor_troco,\r\n    valor_total,\r\n    descricao,\r\n    external_id\r\n  });\r\n};\r\n\r\nexport const updatePedidoFromMesas = async (\r\n  objIdPedido,\r\n  id_empresa,\r\n  itens,\r\n  celular_cliente,\r\n  nome_cliente,\r\n  valor_total,\r\n  descricao,\r\n  external_id\r\n) => {\r\n  return api.put(\"/auth/updatePedidoFromMesas\", {\r\n    objIdPedido,\r\n    id_empresa,\r\n    itens,\r\n    celular_cliente,\r\n    nome_cliente,\r\n    valor_total,\r\n    descricao,\r\n    external_id\r\n  });\r\n};\r\n\r\nexport const registerCategoria = async (\r\n  createdBy,\r\n  id_empresa,\r\n  title,\r\n  disponibilidade,\r\n  dia_horario_disponibilidade,\r\n  only_pdv,\r\n  only_qrcode,\r\n  only_delivery_take_local,\r\n  modelo\r\n) => {\r\n  return api.post(\"/auth/registerCategoria\", {\r\n    createdBy,\r\n    id_empresa,\r\n    title,\r\n    disponibilidade,\r\n    dia_horario_disponibilidade,\r\n    only_pdv,\r\n    only_qrcode,\r\n    only_delivery_take_local,\r\n    modelo\r\n  });\r\n};\r\n\r\nexport const registerItem = async (\r\n  createdBy,\r\n  id_empresa,\r\n  category_item_id,\r\n  category_item_title,\r\n  title,\r\n  description,\r\n  out,\r\n  image,\r\n  price,\r\n  disponibilidade,\r\n  type\r\n) => {\r\n  return api.post(\"/auth/registerItem\", {\r\n    createdBy,\r\n    id_empresa,\r\n    category_item_id,\r\n    category_item_title,\r\n    title,\r\n    description,\r\n    out,\r\n    image,\r\n    price,\r\n    disponibilidade,\r\n    type\r\n  });\r\n};\r\n\r\nexport const registerGrupoAdicionais = async (\r\n  createdBy,\r\n  id_empresa,\r\n  title,\r\n  min,\r\n  max,\r\n  mandatory,\r\n  out,\r\n  calcular_maior_valor,\r\n  calcular_media,\r\n  precificacao,\r\n  type\r\n) => {\r\n  return api.post(\"/auth/registerGrupoAdicionais\", {\r\n    createdBy,\r\n    id_empresa,\r\n    title,\r\n    min,\r\n    max,\r\n    mandatory,\r\n    out,\r\n    calcular_maior_valor,\r\n    calcular_media,\r\n    precificacao,\r\n    type\r\n  });\r\n};\r\n\r\nexport const registerAdicionais = async (\r\n  createdBy,\r\n  id_grupo,\r\n  id_empresa,\r\n  title,\r\n  price,\r\n  out,\r\n  image\r\n) => {\r\n  return api.post(\"/auth/registerAdicionais/\" + createdBy, {\r\n    createdBy,\r\n    id_grupo,\r\n    id_empresa,\r\n    title,\r\n    price,\r\n    out,\r\n    image,\r\n  });\r\n};\r\n\r\nexport const updateItemAdicional = async (data) => {\r\n  return api.post(\"/auth/updateAdicional/\" + data.userID, data);\r\n};\r\n\r\nexport const deleteItemAdicional = async (data) => {\r\n  return api.post(\"/auth/deleteSubadicional/\" + data.userID, data);\r\n};\r\n\r\nexport const registerCaixaAndOpen = async (\r\n  createdBy,\r\n  id_empresa,\r\n  saldo_inicial\r\n) => {\r\n  return api.post(\"/registerCaixaAndOpen/\", {\r\n    createdBy,\r\n    id_empresa,\r\n    saldo_inicial,\r\n  });\r\n};\r\n\r\nexport const registerPlanoAdmin = async (\r\n  createdBy,\r\n  nome,\r\n  plan_identifier,\r\n  order,\r\n  plan_cycle,\r\n  maxPayments,\r\n  valor_plano\r\n) => {\r\n  return api.post(\"/auth/registerPlanoAdmin\", {\r\n    createdBy,\r\n    nome,\r\n    plan_identifier,\r\n    order,\r\n    plan_cycle,\r\n    maxPayments,\r\n    valor_plano\r\n  });\r\n};\r\n\r\nexport const searchClienteAsaasAndGetInvoice = async (\r\n  createdBy,\r\n  email,\r\n  name,\r\n  cpf_cnpj,\r\n  zip_code,\r\n  number,\r\n  plan_identifier,\r\n  payment_type,\r\n  isPromotional\r\n) => {\r\n  return api.post(\"/requestsToAsaas\", {\r\n    createdBy,\r\n    email,\r\n    name,\r\n    cpf_cnpj,\r\n    zip_code,\r\n    number,\r\n    plan_identifier,\r\n    payment_type,\r\n    isPromotional\r\n  });\r\n};\r\n\r\nexport const deleteAsaasPlan = async (\r\n  createdBy,\r\n  planId\r\n) => {\r\n  return api.post(\"/delete-plan-asaas/\" + createdBy, {\r\n    planId\r\n  });\r\n}\r\n\r\nexport const updateQuestionResponses = async (company_id, responses) => {\r\n  return api.post(\"/update-company-responses\", {\r\n    company_id,\r\n    responses,\r\n  });\r\n};\r\n\r\nexport const updateQuestionActive = async (companyId, questionIdentifier, active) => {\r\n  return api.put(\"/update-question-active\", {\r\n    companyId,\r\n    questionIdentifier,\r\n    active,\r\n  });\r\n};\r\n\r\n// ===== NOVAS FUNÇÕES PARA CUSTOM RESPONSES =====\r\n\r\n// Buscar respostas personalizadas da empresa\r\nexport const getCustomResponses = async (companyId) => {\r\n  return api.get(\"/custom-responses/\" + companyId);\r\n};\r\n\r\n// Adicionar nova resposta personalizada\r\nexport const addCustomResponse = async (companyId, question, response) => {\r\n  return api.post(\"/custom-responses/\" + companyId, {\r\n    question,\r\n    response,\r\n  });\r\n};\r\n\r\n// Atualizar resposta personalizada\r\nexport const updateCustomResponse = async (companyId, responseId, question, response, active = true) => {\r\n  return api.put(`/custom-responses/${companyId}/${responseId}`, {\r\n    question,\r\n    response,\r\n    active,\r\n  });\r\n};\r\n\r\n// Deletar resposta personalizada\r\nexport const deleteCustomResponse = async (companyId, responseId) => {\r\n  return api.delete(`/custom-responses/${companyId}/${responseId}`);\r\n};\r\n\r\nexport const registerCliente = async (\r\n  createdBy,\r\n  documento,\r\n  name,\r\n  contato,\r\n  razao,\r\n  email,\r\n  cep,\r\n  estado,\r\n  municipio,\r\n  bairro,\r\n  complemento,\r\n  telefone,\r\n  celular,\r\n  vinculo_empresa,\r\n  type\r\n) => {\r\n  if (type === \"F\") {\r\n    return api.post(\"/auth/registerCliente\", {\r\n      createdBy,\r\n      documento,\r\n      name,\r\n      contato,\r\n      email,\r\n      cep,\r\n      estado,\r\n      municipio,\r\n      bairro,\r\n      complemento,\r\n      telefone,\r\n      celular,\r\n      vinculo_empresa,\r\n      type,\r\n    });\r\n  } else {\r\n    return api.post(\"/auth/registerCliente\", {\r\n      createdBy,\r\n      documento,\r\n      name,\r\n      razao,\r\n      email,\r\n      cep,\r\n      estado,\r\n      municipio,\r\n      bairro,\r\n      complemento,\r\n      telefone,\r\n      celular,\r\n      vinculo_empresa,\r\n      type,\r\n    });\r\n  }\r\n};\r\n\r\nexport const registerClienteFromCardapio = async (\r\n  id_empresa,\r\n  nome,\r\n  telefone\r\n) => {\r\n  return api.post(\"/register-cliente-cardapio\", { id_empresa, nome, telefone });\r\n};\r\n\r\nexport const registerVendedor = async (\r\n  createdBy,\r\n  documento,\r\n  name,\r\n  cep,\r\n  estado,\r\n  municipio,\r\n  bairro,\r\n  complemento,\r\n  telefone,\r\n  celular,\r\n  vinculo_empresa,\r\n  type\r\n) => {\r\n  return api.post(\"/auth/registerVendedor\", {\r\n    createdBy,\r\n    documento,\r\n    name,\r\n    cep,\r\n    estado,\r\n    municipio,\r\n    bairro,\r\n    complemento,\r\n    telefone,\r\n    celular,\r\n    vinculo_empresa,\r\n    type,\r\n  });\r\n};\r\n\r\nexport const registerOrcamento = async (\r\n  createdBy,\r\n  data_emissao,\r\n  codigo_cliente,\r\n  nome_cliente,\r\n  codigo_vendedor,\r\n  nome_vendedor,\r\n  total_orc,\r\n  status_orc,\r\n  id_grupo,\r\n  vinculo_empresa,\r\n  items\r\n) => {\r\n  return api.post(\"/auth/registerOrcamento\", {\r\n    createdBy,\r\n    data_emissao,\r\n    codigo_cliente,\r\n    nome_cliente,\r\n    codigo_vendedor,\r\n    nome_vendedor,\r\n    total_orc,\r\n    status_orc,\r\n    id_grupo,\r\n    vinculo_empresa,\r\n    items,\r\n  });\r\n};\r\n\r\n/*export const retornaClienteAsaas = async (nome_cliente, cpf_cnpj, email_cliente, phone, addressNumber, postalCode) => {   \r\n    return api.post(\"/retorna-cliente-asaas\",{nome_cliente, cpf_cnpj, email_cliente, phone, addressNumber, postalCode}); \r\n};*/\r\n\r\nexport const criarAssinaturaAsaasBolPix = async (\r\n  customerID,\r\n  payment_type,\r\n  cycle,\r\n  value,\r\n  nextDueDate\r\n) => {\r\n  return api.post(\"/criar-assinatura-asaas-bolpix\", {\r\n    customerID,\r\n    payment_type,\r\n    cycle,\r\n    value,\r\n    nextDueDate,\r\n  });\r\n};\r\n\r\nexport const criarAssinaturaAsaasCartao = async (\r\n  customerID,\r\n  nome_cliente,\r\n  cpf_cnpj,\r\n  cycle,\r\n  numero_cartao,\r\n  expiryMonth,\r\n  expiryYear,\r\n  ccv,\r\n  email,\r\n  postalCode,\r\n  addressNumber,\r\n  phone,\r\n  value,\r\n  nextDueDate,\r\n  remoteIp\r\n) => {\r\n  return api.post(\"/criar-assinatura-asaas-cartao\", {\r\n    customerID,\r\n    nome_cliente,\r\n    cpf_cnpj,\r\n    cycle,\r\n    numero_cartao,\r\n    expiryMonth,\r\n    expiryYear,\r\n    ccv,\r\n    email,\r\n    postalCode,\r\n    addressNumber,\r\n    phone,\r\n    value,\r\n    nextDueDate,\r\n    remoteIp,\r\n  });\r\n};\r\n\r\nexport const criarCobrancaAsaas = async (\r\n  customerID,\r\n  nome_cliente,\r\n  cpf_cnpj,\r\n  numero_cartao,\r\n  expiryMonth,\r\n  expiryYear,\r\n  ccv,\r\n  email,\r\n  postalCode,\r\n  addressNumber,\r\n  phone,\r\n  value,\r\n  nextDueDate,\r\n  remoteIp,\r\n  parcela\r\n) => {\r\n  return api.post(\"/criar-cobranca-asaas-cartao\", {\r\n    customerID,\r\n    nome_cliente,\r\n    cpf_cnpj,\r\n    numero_cartao,\r\n    expiryMonth,\r\n    expiryYear,\r\n    ccv,\r\n    email,\r\n    postalCode,\r\n    addressNumber,\r\n    phone,\r\n    value,\r\n    nextDueDate,\r\n    remoteIp,\r\n    parcela,\r\n  });\r\n};\r\n\r\nexport const criarCobrancaAsaasBolPix = async (\r\n  customerID,\r\n  payment_type,\r\n  value,\r\n  nextDueDate\r\n) => {\r\n  return api.post(\"/criar-cobranca-asaas\", {\r\n    customerID,\r\n    payment_type,\r\n    value,\r\n    nextDueDate,\r\n  });\r\n};\r\n\r\nexport const listarAssinaturasAsaas = async (customer_id) => {\r\n  return api.post(\"/lista-subscription-asaas\", { customer_id });\r\n};\r\n\r\n/*export const listarAssinaturasIugu = async (empresa_id, customer_id) => {\r\n  return api.post(\"/lista-subscription-iugu\", { empresa_id, customer_id });\r\n}*/\r\n\r\nexport const getDataCustomerAsaas = async (customer_id) => {\r\n  return api.post(\"/getData-customer-asaas\", { customer_id });\r\n}\r\n\r\nexport const getLastPendingInvoiceAsaas = async (subscriptionIdAsaas) => {\r\n  return api.post(\"/lastPendingInvoice\", { subscriptionIdAsaas });\r\n}\r\n\r\nexport const getAllInvoicesAsaas = async (subscriptionIdAsaas) => {\r\n  return api.post(\"/allInvoices\", { subscriptionIdAsaas });\r\n};\r\n\r\nexport const getInvoiceIugu = async (invoiceId) => {\r\n  return api.post(\"/getInvoice-iugu\", { invoiceId });\r\n}\r\n\r\nexport const importCardapioIfood = async (url_import, id_empresa) => {\r\n  return api.post(\"/import-cardapio-ifood\", { url_import, id_empresa });\r\n};\r\n\r\nexport const importCardapioAnotaai = async (url_import, id_empresa) => {\r\n  //console.log(\"TRECHO API>\",url_import)\r\n  return api.post(\"/import-cardapio-anotaai\", { url_import, id_empresa });\r\n};\r\n\r\nexport const changeStatusLoja = async (id, status_loja) => {\r\n  return api.post(\"/changeStatusLoja\", { id, status_loja });\r\n};\r\n\r\nexport const getUltimoPedidoID = async (id) => {\r\n  return api.get(\"/ultimoPedidoID/\" + id);\r\n}\r\n\r\nexport const getDaysToExpireLicense = async (id) => {\r\n  return api.get(\"/getDaysToExpireLicense/\" + id);\r\n}\r\n\r\nexport const apiCheckImportStatus = async (idEmpresa) => {\r\n  return api.get(`/check-import-status/${idEmpresa}`);\r\n};\r\n\r\nexport const configurarResetPedido = async (id_empresa, dias_para_reset) => {\r\n  return api.post(`/configurar-reset-pedido`, { id_empresa, dias_para_reset });\r\n};\r\n\r\nexport const resetPedidoManual = async (id_empresa) => {\r\n  return api.post(`/reset-pedido-counter`, { id_empresa });\r\n};\r\n\r\nexport const updateStatusBotEmpresa = async (id, status_bot) => {\r\n  return api.patch(`/api/empresa/${id}/status_bot`, { status_bot });\r\n};\r\n\r\nexport const updateCallAtendenteEmpresa = async (id, call_atendente) => {\r\n  return api.patch(`/api/empresa/${id}/call_atendente`, { call_atendente });\r\n};\r\n\r\nexport const getQrCodeWhatsapp = async (empresaID, token) => {\r\n  setAuthToken(token);\r\n  return api.get(\"/api/v1/whatsapp/qrcode/\" + empresaID);\r\n};\r\n\r\n// **📌 NOVA FUNÇÃO PARA RENOVAR QR CODE VIA API**\r\nexport const renewQrCodeWhatsapp = async (empresaID, token) => {\r\n  setAuthToken(token);\r\n  return api.post(\"/api/v1/whatsapp/qrcode/renew/\" + empresaID);\r\n};\r\n\r\nexport const refreshProfilePicturesWhatsapp = async (empresaID, token) => {\r\n  setAuthToken(token);\r\n  return api.post(\"/api/v1/whatsapp/refresh-profile-pictures/\" + empresaID);\r\n};\r\n\r\nexport const refreshContactInfoWhatsapp = async (leadChannelID, token) => {\r\n  setAuthToken(token);\r\n  return api.post(\"/api/v1/whatsapp/refresh-contact-info/\" + leadChannelID);\r\n};\r\n\r\n\r\n\r\n// **📌 NOVA FUNÇÃO PARA VERIFICAR STATUS DE CONEXÃO DA EVOLUTION API**\r\nexport const getWhatsappConnectionStatus = async (empresaID, token) => {\r\n  setAuthToken(token);\r\n  return api.get(\"/api/v1/whatsapp/connectionStatus/\" + empresaID);\r\n};\r\n\r\nexport const deleteInstanceAdmin = async (userID, id) => {\r\n  return api.delete(`/deleteInstance-admin/${userID}`, { data: { id } });\r\n};\r\n\r\n// 🔥 NOVO: Funções para gerenciar fila de atendimento humano\r\nexport const buscarFilaAtendimento = async (empresaId) => {\r\n  return api.get(`/api/v1/whatsapp/fila-atendimento/${empresaId}`);\r\n};\r\n\r\nexport const iniciarAtendimentoFila = async (empresaId, atendimentoId, userData) => {\r\n  return api.post(`/api/v1/whatsapp/fila-atendimento/${empresaId}/iniciar/${atendimentoId}`, userData);\r\n};\r\n\r\nexport const finalizarAtendimentoFila = async (empresaId, atendimentoId, observacoes) => {\r\n  return api.post(`/api/v1/whatsapp/fila-atendimento/${empresaId}/finalizar/${atendimentoId}`, { observacoes });\r\n};\r\n\r\nexport const cancelarAtendimentoFila = async (empresaId, atendimentoId, motivo) => {\r\n  return api.delete(`/api/v1/whatsapp/fila-atendimento/${empresaId}/cancelar/${atendimentoId}`, { data: { motivo } });\r\n};\r\n\r\nexport const limparAtendimentosFinalizados = async (empresaId) => {\r\n  return api.post(`/api/v1/whatsapp/fila-atendimento/${empresaId}/limpar-finalizados`);\r\n};\r\n\r\nexport const sendTextMessageWhatsapp = async (empresaID, number, message) => {\r\n  //setAuthToken(token);\r\n  return api.post(\"/api/v1/whatsapp/sendTextMessage/\" + empresaID, { number, message });\r\n};\r\n\r\nexport const removeWhatsappSession = async (empresaID) => {\r\n  return api.get(`/api/v1/whatsapp/removeSession/${empresaID}`);\r\n};\r\n\r\n// Função para configurar webhook\r\nexport const configureWhatsappWebhook = async (empresaID) => {\r\n  return api.post(`/api/v1/whatsapp/webhook/configure/${empresaID}`);\r\n};\r\n\r\n// Função para verificar status do webhook\r\nexport const getWhatsappWebhookStatus = async (empresaID) => {\r\n  return api.get(`/api/v1/whatsapp/webhook/status/${empresaID}`);\r\n};\r\n\r\n// Função para obter os chats\r\nexport const getWhatsappChats = async (\r\n  empresaID,\r\n  page,\r\n  pageSize,\r\n  query,\r\n  token\r\n) => {\r\n  setAuthToken(token);\r\n  return api.get(`/api/v1/whatsapp/chats/${empresaID}`, {\r\n    params: {\r\n      page,\r\n      pageSize,\r\n      query,\r\n    },\r\n  });\r\n};\r\n\r\n// Função para obter os conversa de um chat com LeadId\r\nexport const getWhatsappChatLead = async (\r\n  empresaID,\r\n  leadID,\r\n  page,\r\n  pageSize,\r\n  token\r\n) => {\r\n  setAuthToken(token);\r\n  return api.get(`/api/v1/whatsapp/chats/${empresaID}/messages/${leadID}`, {\r\n    params: {\r\n      page,\r\n      pageSize,\r\n    },\r\n  });\r\n};\r\n\r\n// Função para obter contagem de mensagens não lidas\r\nexport const getWhatsappUnreadCount = async (empresaID, token) => {\r\n  setAuthToken(token);\r\n  return api.get(`/api/v1/whatsapp/chats/${empresaID}/unread-count`);\r\n};\r\n\r\n// Função para marcar mensagens como lidas\r\nexport const markWhatsappMessagesAsRead = async (empresaID, leadID, token) => {\r\n  setAuthToken(token);\r\n  return api.put(`/api/v1/whatsapp/chats/${empresaID}/messages/${leadID}/mark-as-read`);\r\n};\r\n\r\n// 🔥 NOVO: Função para buscar um chat específico por lead_id\r\nexport const getWhatsappChatById = async (empresaID, leadID, token) => {\r\n  setAuthToken(token);\r\n  return api.get(`/api/v1/whatsapp/chats/${empresaID}/chat/${leadID}`);\r\n};\r\n\r\n// 🔥 NOVO: Função para iniciar nova conversa com um contato\r\nexport const createWhatsappChat = async (empresaID, contactNumber, contactName, token) => {\r\n  setAuthToken(token);\r\n  return api.post(`/api/v1/whatsapp/chats/${empresaID}/create`, {\r\n    contactNumber,\r\n    contactName\r\n  });\r\n};\r\n\r\nexport const getEstados = async () => {\r\n  return api.get(\r\n    \"https://servicodados.ibge.gov.br/api/v1/localidades/estados/\"\r\n  );\r\n};\r\n\r\nexport const getMunicipios = async (estadoSelecionado) => {\r\n  return api.get(\r\n    \"https://servicodados.ibge.gov.br/api/v1/localidades/estados/\" +\r\n    estadoSelecionado +\r\n    \"/municipios\"\r\n  );\r\n};\r\n\r\nexport const getBairros = async (city, uf) => {\r\n  console.log(\"Chegou no getBairros da api:\", city, uf)\r\n  return api.get(`/carregar-bairros?city=${city}&uf=${uf}`);\r\n};\r\n\r\nexport const calcularDistancia = async (coordenadasA, coordenadasB) => {\r\n  const { lat: latA, lng: lngA } = coordenadasA;\r\n  const { lat: latB, lng: lngB } = coordenadasB;\r\n  return api.get(`/calcular-distancia?latA=${latA}&lngA=${lngA}&latB=${latB}&lngB=${lngB}`);\r\n};\r\n\r\n// Mesas\r\n\r\nexport const getMesas = async () => {\r\n  const { data } = await api.get('/mesas');\r\n\r\n  if (!data.status) {\r\n    return null;\r\n  }\r\n\r\n  return data.data;\r\n}\r\n\r\nexport const createMesa = async ({ name, qr_code }) => {\r\n  const { data } = await api.post('/mesas', {\r\n    name,\r\n    qr_code\r\n  });\r\n\r\n  if (!data.status) {\r\n    return null;\r\n  }\r\n\r\n  return data.data;\r\n}\r\n\r\nexport const createManyMesa = async ({ url, quantity }) => {\r\n  const { data } = await api.post('/many_mesas', {\r\n    url,\r\n    quantity\r\n  });\r\n\r\n  if (!data.status) {\r\n    return null;\r\n  }\r\n\r\n  return data.data;\r\n}\r\n\r\n\r\nexport const registerPedidoMesa = async (\r\n  {\r\n    mesa_id,\r\n    id_empresa,\r\n    itens,\r\n    celular_cliente,\r\n    nome_cliente,\r\n    valor_total,\r\n    descricao,\r\n  }\r\n) => {\r\n  return api.post(\"/mesas/registerPedido\", {\r\n    id_empresa,\r\n    itens,\r\n    mesa_id,\r\n    celular_cliente,\r\n    nome_cliente,\r\n    valor_total,\r\n    descricao,\r\n  });\r\n};\r\n\r\nexport const deleteMesa = async ({ mesaId }) => {\r\n  const { data } = await api.delete(`/mesas/${mesaId}`);\r\n\r\n  if (!data.status) {\r\n    return null;\r\n  }\r\n\r\n  return data.data;\r\n}\r\n\r\nexport const registerPayment = async (\r\n  {\r\n    mesa_id,\r\n    payment_type,\r\n    total_payed,\r\n    customer_name,\r\n    customer_phone\r\n  }\r\n) => {\r\n  return api.post(\"/register-payment\", {\r\n    mesa_id,\r\n    payment_type,\r\n    total_payed,\r\n    customer_name,\r\n    customer_phone\r\n  });\r\n};\r\n\r\n// Funções para gerenciar progresso da configuração inicial\r\nexport const getProgressoConfiguracaoInicial = async (empresaId) => {\r\n  return api.get(`/getProgressoConfiguracaoInicial/${empresaId}`);\r\n};\r\n\r\nexport const atualizarProgressoConfiguracaoInicial = async (empresaId, etapaCompleta, proximaEtapa, finalizada = false) => {\r\n  return api.put(`/atualizarProgressoConfiguracaoInicial/${empresaId}`, {\r\n    etapaCompleta,\r\n    proximaEtapa,\r\n    finalizada\r\n  });\r\n};\r\n\r\n// Funções para solicitação de cardápio para equipe\r\nexport const enviarCardapioParaEquipe = async (empresaId, idEmpresa, userId, customLink, pdfFile, observacoes, userLookupName) => {\r\n  const formData = new FormData();\r\n  formData.append('empresaId', empresaId);\r\n  formData.append('idEmpresa', idEmpresa);\r\n  formData.append('userId', userId);\r\n  formData.append('customLink', customLink || '');\r\n  formData.append('observacoes', observacoes || '');\r\n  formData.append('userLookupName', userLookupName || '');\r\n  \r\n  if (pdfFile) {\r\n    formData.append('pdfFile', pdfFile);\r\n  }\r\n\r\n  return api.post('/enviar-cardapio-para-equipe', formData, {\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data',\r\n    },\r\n  });\r\n};\r\n\r\nexport const listarSolicitacoesCardapio = async (userId, status = 'todos', page = 1, limit = 10) => {\r\n  return api.get(`/listar-solicitacoes-cardapio/${userId}`, {\r\n    params: {\r\n      status,\r\n      page,\r\n      limit\r\n    }\r\n  });\r\n};\r\n\r\nexport const atualizarStatusSolicitacaoCardapio = async (userId, empresaId, solicitacaoId, novoStatus, observacoesEquipe, usuarioResponsavel) => {\r\n  return api.put(`/atualizar-status-solicitacao-cardapio/${userId}`, {\r\n    empresaId,\r\n    solicitacaoId,\r\n    novoStatus,\r\n    observacoesEquipe,\r\n    usuarioResponsavel\r\n  });\r\n};\r\n\r\nexport const buscarSolicitacoesEmpresa = async (empresaId, userId) => {\r\n  return api.get(`/solicitacoes-cardapio-empresa/${empresaId}/${userId}`);\r\n};\r\n\r\nexport const getProgressoImportacao = async (idEmpresa) => {\r\n  return api.get(`/progress-import/${idEmpresa}`);\r\n};\r\n\r\nexport const resetImportStatus = async (idEmpresa) => {\r\n  return api.post(`/reset-import-status/${idEmpresa}`);\r\n};\r\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,qBAAqB;AACtD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,MAAMC,aAAa,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,GAAG,IAAI,GAAG,KAAK;AAC7E,MAAMC,MAAM,GAAGJ,aAAa,GACxBK,OAAO,CAACC,GAAG,CAACC,wBAAwB,GACpCF,OAAO,CAACC,GAAG,CAACE,yBAAyB;AACzC,MAAMC,kBAAkB,GAAGJ,OAAO,CAACC,GAAG,CAACI,+BAA+B;AACtE,OAAO,MAAMC,GAAG,GAAGb,KAAK,CAACc,MAAM,CAAC;EAC9BC,OAAO,EAAET;AACX,CAAC,CAAC;AAEF,MAAMU,eAAe,GAAGhB,KAAK,CAACc,MAAM,CAAC;EACnCC,OAAO,EAAEJ,kBAAkB;EAC3BM,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,YAAY,GAAIC,KAAK,IAAK;EAC9B,IAAIA,KAAK,EAAE;IACT;IACAN,GAAG,CAACO,QAAQ,CAACH,OAAO,CAACI,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUF,KAAK,EAAE;EAClE,CAAC,MAAM;IACL;IACA,OAAON,GAAG,CAACO,QAAQ,CAACH,OAAO,CAACI,MAAM,CAAC,eAAe,CAAC;EACrD;AACF,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;EACtD,OAAOX,GAAG,CAACY,IAAI,CAAC,aAAa,EAAE;IAAEF,KAAK;IAAEC;EAAS,CAAC,CAAC;AACrD,CAAC;AAED,OAAO,MAAME,YAAY,GAAIC,MAAM,IAAK;EACtC,OAAOd,GAAG,CAACe,GAAG,CAAC,kBAAkBD,MAAM,EAAE,CAAC;AAC5C,CAAC;AAED,OAAO,MAAME,gBAAgB,GAAIC,MAAM,IAAK;EAC1C,OAAOjB,GAAG,CAACe,GAAG,CAAC,yBAAyBE,MAAM,EAAE,CAAC;AACnD,CAAC;AAGD,OAAO,MAAMC,QAAQ,GAAG,MAAOC,MAAM,IAAK;EACxC,OAAOnB,GAAG,CAACe,GAAG,CAAC,cAAc,GAAGI,MAAM,CAAC;AACzC,CAAC;AAED,OAAO,MAAMC,cAAc,GAAG,MAAOC,IAAI,IAAK;EAC5C,OAAOrB,GAAG,CAACe,GAAG,CAAC,uBAAuBM,IAAI,EAAE,CAAC;AAC/C,CAAC;AAED,OAAO,MAAMC,eAAe,GAAG,MAAOC,UAAU,IAAK;EACnD,OAAOvB,GAAG,CAACe,GAAG,CAAC,qBAAqB,GAAGQ,UAAU,CAAC;AACpD,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAOC,EAAE,EAAEC,IAAI,EAAEC,SAAS,EAAEC,OAAO,KAAK;EAClE,OAAO5B,GAAG,CAACe,GAAG,CAAC,eAAe,GAAGU,EAAE,EAAE;IACnCI,MAAM,EAAE;MACNH,IAAI;MACJI,QAAQ,EAAE,EAAE;MACZH,SAAS;MACTC;IACF;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMG,WAAW,GAAG,MAAOZ,MAAM,IAAK;EAC3C,OAAOnB,GAAG,CAACe,GAAG,CAAC,aAAa,GAAGI,MAAM,CAAC;AACxC,CAAC;AAED,OAAO,MAAMa,aAAa,GAAG,MAAOb,MAAM,IAAK;EAC7C,OAAOnB,GAAG,CAACe,GAAG,CAAC,cAAc,GAAGI,MAAM,CAAC;AACzC,CAAC;AAED,OAAO,MAAMc,eAAe,GAAG,MAAOd,MAAM,IAAK;EAC/C,OAAOnB,GAAG,CAACe,GAAG,CAAC,kBAAkB,GAAGI,MAAM,CAAC;AAC7C,CAAC;AAGD,OAAO,MAAMe,OAAO,GAAG,MAAOf,MAAM,IAAK;EACvC,OAAOnB,GAAG,CAACe,GAAG,CAAC,QAAQ,GAAGI,MAAM,CAAC;AACnC,CAAC;AAED,OAAO,MAAMgB,mBAAmB,GAAG,MAAOC,YAAY,IAAK;EACzD,OAAOpC,GAAG,CAACe,GAAG,CAAC,oBAAoB,GAAGqB,YAAY,CAAC;AACrD,CAAC;AAED,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAOlB,MAAM,EAAEI,UAAU,KAAK;EACtD,OAAOvB,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IAAEI;EAAW,CAAC,CAAC;AAC3D,CAAC;AAED,OAAO,MAAMe,gBAAgB,GAAG,MAAAA,CAAOnB,MAAM,EAAEO,IAAI,EAAEa,KAAK,KAAK;EAC7D,OAAOvC,GAAG,CAACe,GAAG,CAAC,uBAAuBI,MAAM,SAASO,IAAI,UAAUa,KAAK,EAAE,CAAC;AAC7E,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG,MAAAA,CAAOrB,MAAM,EAAEsB,SAAS,KAAK;EAC9D,OAAOzC,GAAG,CAACY,IAAI,CAAC,yBAAyBO,MAAM,IAAIsB,SAAS,EAAE,CAAC;AACjE,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAOvB,MAAM,EAAEwB,QAAQ,KAAK;EACtD,OAAO3C,GAAG,CAACY,IAAI,CAAC,aAAa,GAAGO,MAAM,EAAE;IAAEwB;EAAS,CAAC,CAAC;AACvD,CAAC;AAED,OAAO,MAAMC,cAAc,GAAG,MAAOC,SAAS,IAAK;EACjD,OAAO7C,GAAG,CAACY,IAAI,CAAC,aAAa,EAAE;IAAEiC;EAAU,CAAC,CAAC;AAC/C,CAAC;AAED,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAO3B,MAAM,EAAE4B,eAAe,KAAK;EAC3D,OAAO/C,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IAAE4B;EAAgB,CAAC,CAAC;AAChE,CAAC;AAED,OAAO,MAAMC,SAAS,GAAG,MAAAA,CACvB7B,MAAM,EACNI,UAAU,EACVwB,eAAe,EACfE,SAAS,KACN;EACH,IAAI;IACF,OAAO,MAAMjD,GAAG,CAACY,IAAI,CAAC,cAAc,GAAGO,MAAM,EAAE;MAC7CI,UAAU;MACVwB,eAAe;MACfE;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,KAAK,CAAC;IAC3C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMG,aAAa,GAAG,MAAAA,CAC3BlC,MAAM,EACNI,UAAU,EACVwB,eAAe,EACfX,YAAY,KACT;EACH,OAAOpC,GAAG,CAACY,IAAI,CAAC,kBAAkB,GAAGO,MAAM,EAAE;IAC3CI,UAAU;IACVwB,eAAe;IACfX;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMkB,aAAa,GAAG,MAAAA,CAC3BnC,MAAM,EACNI,UAAU,EACVwB,eAAe,EACfX,YAAY,KACT;EACH,OAAOpC,GAAG,CAACY,IAAI,CAAC,kBAAkB,GAAGO,MAAM,EAAE;IAC3CI,UAAU;IACVwB,eAAe;IACfX;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMmB,qBAAqB,GAAG,MAAAA,CAAOhC,UAAU,EAAEiC,WAAW,KAAK;EACtE,OAAOxD,GAAG,CAACY,IAAI,CAAC,2BAA2B,EAAE;IAAEW,UAAU;IAAEiC;EAAY,CAAC,CAAC;AAC3E,CAAC;AAED,OAAO,MAAMC,qBAAqB,GAAG,MAAAA,CAAOlC,UAAU,EAAEiC,WAAW,KAAK;EACtE,OAAOxD,GAAG,CAACY,IAAI,CAAC,2BAA2B,EAAE;IAAEW,UAAU;IAAEiC;EAAY,CAAC,CAAC;AAC3E,CAAC;AAED,OAAO,MAAME,cAAc,GAAG,MAAAA,CAAOnC,UAAU,EAAEiC,WAAW,KAAK;EAC/D,OAAOxD,GAAG,CAACY,IAAI,CAAC,mBAAmB,EAAE;IAAEW,UAAU;IAAEiC;EAAY,CAAC,CAAC;AACnE,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG,MAAAA,CACtBxC,MAAM,EACNI,UAAU,EACVwB,eAAe,EACfX,YAAY,KACT;EACH,OAAOpC,GAAG,CAACY,IAAI,CAAC,aAAa,GAAGO,MAAM,EAAE;IACtCI,UAAU;IACVwB,eAAe;IACfX;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMwB,gBAAgB,GAAG,MAAAA,CAAOrC,UAAU,EAAEiC,WAAW,KAAK;EACjE,OAAOxD,GAAG,CAACY,IAAI,CAAC,sBAAsB,EAAE;IAAEW,UAAU;IAAEiC;EAAY,CAAC,CAAC;AACtE,CAAC;AAED,OAAO,MAAMK,qBAAqB,GAAG,MAAAA,CAAOtC,UAAU,EAAEiC,WAAW,KAAK;EACtE,OAAOxD,GAAG,CAACY,IAAI,CAAC,4BAA4B,EAAE;IAAEW,UAAU;IAAEiC;EAAY,CAAC,CAAC;AAC5E,CAAC;AAED,OAAO,MAAMM,OAAO,GAAG,MAAAA,CACrB3C,MAAM,EACNI,UAAU,EACVwB,eAAe,EACfX,YAAY,EACZ2B,SAAS,KACN;EACH,OAAO/D,GAAG,CAACY,IAAI,CAAC,YAAY,GAAGO,MAAM,EAAE;IACrCI,UAAU;IACVwB,eAAe;IACfX,YAAY;IACZ2B;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,eAAe,GAAG,MAAAA,CAAOzC,UAAU,EAAEwC,SAAS,KAAK;EAC9D,OAAO/D,GAAG,CAACY,IAAI,CAAC,qBAAqB,EAAE;IAAEW,UAAU;IAAEwC;EAAU,CAAC,CAAC;AACnE,CAAC;AAED,OAAO,MAAME,kBAAkB,GAAG,MAAAA,CAChC9C,MAAM,EACNI,UAAU,EACVwB,eAAe,EACfmB,aAAa,EACbC,SAAS,KACN;EACH,OAAOnE,GAAG,CAACY,IAAI,CAAC,qBAAqB,GAAGO,MAAM,EAAE;IAC9CI,UAAU;IACVwB,eAAe;IACfmB,aAAa;IACbC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,yBAAyB,GAAG,MAAAA,CACvCjD,MAAM,EACNI,UAAU,EACVwB,eAAe,EACfmB,aAAa,EACbC,SAAS,KACN;EACH,OAAOnE,GAAG,CAACY,IAAI,CAAC,4BAA4B,GAAGO,MAAM,EAAE;IACrDI,UAAU;IACVwB,eAAe;IACfmB,aAAa;IACbC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAME,6BAA6B,GAAG,MAAAA,CAC3ClD,MAAM,EACNI,UAAU,EACVwB,eAAe,EACfoB,SAAS,KACN;EACH,OAAOnE,GAAG,CAACY,IAAI,CAAC,gCAAgC,GAAGO,MAAM,EAAE;IACzDI,UAAU;IACVwB,eAAe;IACfoB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMG,8BAA8B,GAAG,MAAAA,CAC5CnD,MAAM,EACNI,UAAU,EACVwB,eAAe,EACfmB,aAAa,EACbK,OAAO,KACJ;EACH,OAAOvE,GAAG,CAACY,IAAI,CAAC,mCAAmC,GAAGO,MAAM,EAAE;IAC5DI,UAAU;IACVwB,eAAe;IACfmB,aAAa;IACbK;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,4BAA4B,GAAG,MAAAA,CAC1CrD,MAAM,EACNI,UAAU,EACVwB,eAAe,EACfmB,aAAa,EACbvC,SAAS,EACTC,OAAO,KACJ;EACH,OAAO5B,GAAG,CAACY,IAAI,CAAC,iCAAiC,GAAGO,MAAM,EAAE;IAC1DI,UAAU;IACVwB,eAAe;IACfmB,aAAa;IACbvC,SAAS;IACTC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAM6C,iBAAiB,GAAG,MAAAA,CAC/BtD,MAAM,EACNI,UAAU,EACVwB,eAAe,KACZ;EACH,OAAO/C,GAAG,CAACY,IAAI,CAAC,uBAAuB,GAAGO,MAAM,EAAE;IAChDI,UAAU;IACVwB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAM2B,UAAU,GAAG,MAAAA,CAAOvD,MAAM,EAAEI,UAAU,EAAEoD,QAAQ,KAAK;EAChE,OAAO3E,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IAAEI,UAAU;IAAEoD;EAAS,CAAC,CAAC;AACrE,CAAC;AAED,OAAO,MAAMC,WAAW,GAAG,MAAAA,CAAOzD,MAAM,EAAE0D,WAAW,KAAK;EACxD,OAAO7E,GAAG,CAACY,IAAI,CAAC,gBAAgB,GAAGO,MAAM,EAAE;IAAE0D;EAAY,CAAC,CAAC;AAC7D,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAO3D,MAAM,EAAE4D,YAAY,KAAK;EAC1D,OAAO/E,GAAG,CAACY,IAAI,CAAC,iBAAiB,GAAGO,MAAM,EAAE;IAAE4D;EAAa,CAAC,CAAC;AAC/D,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG,MAAO7D,MAAM,IAAK;EACjD,OAAOnB,GAAG,CAACe,GAAG,CAAC,mBAAmB,GAAGI,MAAM,CAAC;AAC9C,CAAC;AAED,OAAO,MAAM8D,WAAW,GAAG,MAAO9D,MAAM,IAAK;EAC3C,OAAOnB,GAAG,CAACe,GAAG,CAAC,iBAAiB,GAAGI,MAAM,CAAC;AAC5C,CAAC;AAED,OAAO,MAAM+D,WAAW,GAAG,MAAO/D,MAAM,IAAK;EAC3C,OAAOnB,GAAG,CAACe,GAAG,CAAC,iBAAiB,GAAGI,MAAM,CAAC;AAC5C,CAAC;AAED,OAAO,MAAMgE,WAAW,GAAG,MAAOhE,MAAM,IAAK;EAC3C,OAAOnB,GAAG,CAACe,GAAG,CAAC,iBAAiB,GAAGI,MAAM,CAAC;AAC5C,CAAC;AAED,OAAO,MAAMiE,iBAAiB,GAAG,MAAAA,CAAOC,eAAe,EAAE1D,SAAS,EAAEC,OAAO,KAAK;EAC9E,OAAO5B,GAAG,CAACe,GAAG,CAAC,oBAAoBsE,eAAe,EAAE,EAAE;IACpDxD,MAAM,EAAE;MACNF,SAAS,EAAEA,SAAS,CAAC2D,WAAW,CAAC,CAAC;MAAE;MACpC1D,OAAO,EAAEA,OAAO,CAAC0D,WAAW,CAAC;IAC/B;EACF,CAAC,CAAC;AACJ,CAAC;AAGD,OAAO,MAAMC,mBAAmB,GAAG,MAAO1C,SAAS,IAAK;EACtD,OAAO7C,GAAG,CAACe,GAAG,CAAC,qBAAqB,GAAG8B,SAAS,CAAC;AACnD,CAAC;AAED,OAAO,MAAM2C,aAAa,GAAG,MAAOrE,MAAM,IAAK;EAC7C,OAAOnB,GAAG,CAACe,GAAG,CAAC,mBAAmB,GAAGI,MAAM,CAAC;AAC9C,CAAC;AAED,OAAO,MAAMsE,aAAa,GAAG,MAAOtE,MAAM,IAAK;EAC7C,OAAOnB,GAAG,CAACe,GAAG,CAAC,mBAAmB,GAAGI,MAAM,CAAC;AAC9C,CAAC;AAED,OAAO,MAAMuE,UAAU,GAAG,MAAAA,CACxBjE,EAAE,EACFN,MAAM,EACNT,KAAK,EACLC,QAAQ,EACRgF,IAAI,KACD;EACH,OAAO3F,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IACxCM,EAAE;IACFf,KAAK;IACLC,QAAQ;IACRgF;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAC9BC,QAAQ,EACR1E,MAAM,EACNI,UAAU,EACVoE,IAAI,EACJG,OAAO,EACPnB,QAAQ,EACRoB,KAAK,KACF;EACH,OAAO/F,GAAG,CAACY,IAAI,CAAC,qBAAqB,GAAGO,MAAM,EAAE;IAC9C0E,QAAQ;IACRtE,UAAU;IACVoE,IAAI;IACJG,OAAO;IACPnB,QAAQ;IACRoB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,yBAAyB,GAAG,MAAAA,CACvC7E,MAAM,EACN8E,GAAG,EACHxE,EAAE,EACFyE,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,cAAc,KACX;EACH,OAAOtG,GAAG,CAACY,IAAI,CAAC,8BAA8B,GAAGO,MAAM,EAAE;IACvD8E,GAAG;IACHxE,EAAE;IACFyE,cAAc;IACdC,cAAc;IACdC,eAAe;IACfC,eAAe;IACfC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,+BAA+B,GAAG,MAAAA,CAC7CpF,MAAM,EACN8E,GAAG,EACHxE,EAAE,EACF+E,WAAW,EACXC,qBAAqB,EACrBC,QAAQ,KACL;EACH,OAAO1G,GAAG,CAACY,IAAI,CAAC,sCAAsC,GAAGO,MAAM,EAAE;IAC/D8E,GAAG;IACHxE,EAAE;IACF+E,WAAW;IACXC,qBAAqB;IACrBC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAC9BV,GAAG,EACHxE,EAAE,EACFN,MAAM,EACNyF,oBAAoB,KACjB;EACH,OAAO5G,GAAG,CAACY,IAAI,CAAC,kBAAkB,GAAGO,MAAM,EAAE;IAC3C8E,GAAG,EAAEA,GAAG;IACRxE,EAAE,EAAEA,EAAE;IACNmF,oBAAoB,EAAEA,oBAAoB;IAC1CC,cAAc,EAAED;EAClB,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAME,oBAAoB,GAAG,MAAAA,CAClCvF,UAAU,EACVoD,QAAQ,EACRoC,QAAQ,EACRC,cAAc,KACX;EACH,OAAOhH,GAAG,CAACY,IAAI,CAAC,2BAA2B,EAAE;IAC3CW,UAAU;IACVoD,QAAQ;IACRoC,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,wBAAwB,GAAG,MAAAA,CACtChB,GAAG,EACHxE,EAAE,EACFN,MAAM,EACN+F,YAAY,EACZC,aAAa,KACV;EACH,OAAOnH,GAAG,CAACY,IAAI,CAAC,sBAAsB,GAAGO,MAAM,EAAE;IAC/C8E,GAAG,EAAEA,GAAG;IACRxE,EAAE,EAAEA,EAAE;IACNyF,YAAY,EAAEA,YAAY;IAC1BC,aAAa,EAAEA;EACjB,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,0BAA0B,GAAG,MAAAA,CACxCnB,GAAG,EACHxE,EAAE,EACFN,MAAM,EACNkG,cAAc,EACdF,aAAa,KACV;EACH,OAAOnH,GAAG,CAACY,IAAI,CAAC,wBAAwB,GAAGO,MAAM,EAAE;IACjD8E,GAAG,EAAEA,GAAG;IACRxE,EAAE,EAAEA,EAAE;IACN4F,cAAc,EAAEA,cAAc;IAC9BF,aAAa,EAAEA;EACjB,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMG,qBAAqB,GAAG,MAAAA,CACnCnG,MAAM,EACN8E,GAAG,EACHsB,YAAY,EACZhG,UAAU,EACViG,KAAK,KACF;EACH,OAAOxH,GAAG,CAACY,IAAI,CAAC,oBAAoB,GAAGO,MAAM,EAAE;IAC7C8E,GAAG,EAAEA,GAAG;IACRsB,YAAY,EAAEA,YAAY;IAC1BhG,UAAU,EAAEA,UAAU;IACtBiG,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAC9BtG,MAAM,EACN8E,GAAG,EACHyB,OAAO,EACPnG,UAAU,EACViG,KAAK,KACF;EACH,OAAOxH,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IACxC8E,GAAG,EAAEA,GAAG;IACRyB,OAAO,EAAEA,OAAO;IAChBnG,UAAU,EAAEA,UAAU;IACtBiG,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMG,aAAa,GAAG,MAAAA,CAAOxG,MAAM,EAAE8E,GAAG,EAAEyB,OAAO,EAAEnG,UAAU,EAAEqG,GAAG,KAAK;EAC5E,OAAO5H,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IACxC8E,GAAG,EAAEA,GAAG;IACRyB,OAAO,EAAEA,OAAO;IAChBnG,UAAU,EAAEA,UAAU;IACtBqG,GAAG,EAAEA;EACP,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAAO1G,MAAM,EAAE8E,GAAG,EAAEyB,OAAO,EAAEnG,UAAU,EAAEqG,GAAG,KAAK;EACjF,OAAO5H,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IACxC8E,GAAG,EAAEA,GAAG;IACRyB,OAAO,EAAEA,OAAO;IAChBnG,UAAU,EAAEA,UAAU;IACtBuG,SAAS,EAAEF;EACb,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMG,sBAAsB,GAAG,MAAAA,CAAO5G,MAAM,EAAE6G,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,YAAY,EAAE7G,UAAU,EAAEqG,GAAG,KAAK;EACrH,OAAO5H,GAAG,CAACY,IAAI,CAAC,wBAAwB,GAAGO,MAAM,EAAE;IACjD8E,GAAG,EAAEgC,MAAM;IACXI,QAAQ,EAAEL,OAAO;IACjBM,KAAK,EAAEJ,MAAM;IACbC,KAAK,EAAEA,KAAK;IACZC,YAAY,EAAEA,YAAY;IAC1B7G,UAAU,EAAEA,UAAU;IACtBqG,GAAG,EAAEA;EACP,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMW,2BAA2B,GAAG,MAAAA,CAAOpH,MAAM,EAAE6G,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,YAAY,EAAE7G,UAAU,EAAEqG,GAAG,KAAK;EAC1H,OAAO5H,GAAG,CAACY,IAAI,CAAC,wBAAwB,GAAGO,MAAM,EAAE;IACjD8E,GAAG,EAAEgC,MAAM;IACXI,QAAQ,EAAEL,OAAO;IACjBM,KAAK,EAAEJ,MAAM;IACbC,KAAK,EAAEA,KAAK;IACZC,YAAY,EAAEA,YAAY;IAC1B7G,UAAU,EAAEA,UAAU;IACtBuG,SAAS,EAAEF;EACb,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMY,eAAe,GAAG,MAAAA,CAC7BrH,MAAM,EACN8E,GAAG,EACHyB,OAAO,EACPnG,UAAU,EACV4G,KAAK,KACF;EACH,OAAOnI,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IACxC8E,GAAG,EAAEA,GAAG;IACRyB,OAAO,EAAEA,OAAO;IAChBnG,UAAU,EAAEA,UAAU;IACtB4G,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMM,oBAAoB,GAAG,MAAAA,CAClCtH,MAAM,EACN8E,GAAG,EACHyB,OAAO,EACPnG,UAAU,EACV4G,KAAK,KACF;EACH,OAAOnI,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IACxC8E,GAAG,EAAEA,GAAG;IACRyB,OAAO,EAAEA,OAAO;IAChBnG,UAAU,EAAEA,UAAU;IACtBmH,WAAW,EAAEP;EACf,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMQ,gBAAgB,GAAG,MAAAA,CAC9BxH,MAAM,EACN8E,GAAG,EACHsB,YAAY,EACZhG,UAAU,EACVqH,MAAM,EACNN,KAAK,EACLO,eAAe,EACfC,2BAA2B,KACxB;EACH,OAAO9I,GAAG,CAACY,IAAI,CAAC,oBAAoB,GAAGO,MAAM,EAAE;IAC7C8E,GAAG,EAAEA,GAAG;IACRsB,YAAY,EAAEA,YAAY;IAC1BhG,UAAU,EAAEA,UAAU;IACtBqH,MAAM,EAAEA,MAAM;IACdN,KAAK,EAAEA,KAAK;IACZO,eAAe,EAAEA,eAAe;IAChCC,2BAA2B,EAAEA;EAC/B,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,qBAAqB,GAAG,MAAAA,CAAO;EAC1C5H,MAAM;EACN6H,eAAe;EACfC,SAAS;EACTX,KAAK;EACLY,MAAM;EACNC,MAAM;EACNC,SAAS;EACTC,kBAAkB;EAClBC,aAAa;EACbC,YAAY;EACZC;AACF,CAAC,KAAK;EACJ,OAAOxJ,GAAG,CAACY,IAAI,CAAC,2BAA2B,GAAGO,MAAM,EAAE;IACpD8E,GAAG,EAAE+C,eAAe;IACpBzH,UAAU,EAAE0H,SAAS;IACrBX,KAAK,EAAEA,KAAK;IACZmB,GAAG,EAAEP,MAAM;IACXQ,GAAG,EAAEP,MAAM;IACXC,SAAS,EAAEA,SAAS;IACpBO,oBAAoB,EAAEN,kBAAkB;IACxCO,cAAc,EAAEN,aAAa;IAC7BC,YAAY,EAAEA,YAAY;IAC1BC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMK,WAAW,GAAG,MAAAA,CACzB1I,MAAM,EACN4C,SAAS,EACT2D,OAAO,EACPuB,SAAS,EACTa,SAAS,EACTC,gBAAgB,EAChBC,mBAAmB,EACnB1B,KAAK,EACL2B,WAAW,EACXrC,GAAG,EACHsC,MAAM,EACN/B,KAAK,EACLgC,aAAa,EACbC,UAAU,EACVZ,IAAI,KACD;EACH,OAAOxJ,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IACxC8E,GAAG,EAAElC,SAAS;IACd2D,OAAO,EAAEA,OAAO;IAChBnG,UAAU,EAAE0H,SAAS;IACrBzB,KAAK,EAAEsC,SAAS;IAChBC,gBAAgB,EAAEA,gBAAgB;IAClCC,mBAAmB,EAAEA,mBAAmB;IACxC1B,KAAK,EAAEA,KAAK;IACZ2B,WAAW,EAAEA,WAAW;IACxBrC,GAAG,EAAEA,GAAG;IACRsC,MAAM,EAAEA,MAAM;IACd/B,KAAK,EAAEA,KAAK;IACZgC,aAAa,EAAEA,aAAa;IAC5BC,UAAU,EAAEA,UAAU;IACtBZ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMa,uBAAuB,GAAG,MAAAA,CACrClJ,MAAM,EACNmJ,MAAM,EACNC,mBAAmB,KAChB;EACH,OAAOvK,GAAG,CAACY,IAAI,CAAC,kCAAkC,GAAGO,MAAM,EAAE;IAC3DmJ,MAAM;IACNC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAChCrJ,MAAM,EACN8E,GAAG,EACHhD,SAAS,EACTiB,aAAa,KACV;EACH,OAAOlE,GAAG,CAACY,IAAI,CAAC,iBAAiB,GAAGO,MAAM,EAAE;IAC1C8E,GAAG,EAAEA,GAAG;IACRhD,SAAS,EAAEA,SAAS;IACpBiB,aAAa,EAAEA;IACf;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMuG,0BAA0B,GAAG,MAAAA,CACxCtJ,MAAM,EACN8E,GAAG,EACHhD,SAAS,EACTyH,UAAU,KACP;EACH,OAAOvL,KAAK,CAACyB,IAAI,CAAC,GAAGnB,MAAM,kBAAkB0B,MAAM,EAAE,EAAE;IACrD8E,GAAG,EAAEA,GAAG;IACRhD,SAAS,EAAEA,SAAS;IACpByH,UAAU,EAAEA;EACd,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG,MAAAA,CACjC1B,SAAS,EACT2B,qBAAqB,KAClB;EACH,OAAO5K,GAAG,CAACY,IAAI,CAAC,uBAAuBqI,SAAS,EAAE,EAAE;IAClD2B;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,cAAc,GAAG,MAAAA,CAAO1J,MAAM,EAAE2J,WAAW,KAAK;EAC3D,OAAO9K,GAAG,CAACY,IAAI,CAAC,iBAAiB,GAAGO,MAAM,EAAE;IAC1C2J;EACF,CAAC,CAAC;AACJ,CAAC;AAACC,EAAA,GAJWF,cAAc;AAM3B,OAAO,MAAMG,4BAA4B,GAAG,MAAAA,CAC1C7J,MAAM,EACN8E,GAAG,EACHhD,SAAS,EACTiB,aAAa,EACb+G,YAAY,KACT;EACH,OAAOjL,GAAG,CAACY,IAAI,CAAC,iBAAiB,GAAGO,MAAM,EAAE;IAC1C8E,GAAG,EAAEA,GAAG;IACRhD,SAAS,EAAEA,SAAS;IACpBiB,aAAa,EAAEA,aAAa;IAC5B;IACA+G,YAAY,EAAEA;EAChB,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG,MAAAA,CAAO/J,MAAM,EAAE8E,GAAG,EAAEhD,SAAS,KAAK;EACjE;EACA,OAAOjD,GAAG,CAACY,IAAI,CAAC,iBAAiB,GAAGO,MAAM,EAAE;IAC1C8E,GAAG,EAAEA,GAAG;IACRhD,SAAS,EAAEA,SAAS;IACpBiB,aAAa,EAAE,GAAG;IAClBiH,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,WAAW,GAAG,MAAAA,CACzB7J,UAAU,EACV8J,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,wBAAwB,EACxBC,sBAAsB,EACtBC,mBAAmB,KAChB;EACH,OAAO1L,GAAG,CAACY,IAAI,CAAC,cAAc,EAAE;IAC9BW,UAAU;IACV8J,UAAU;IACVC,QAAQ;IACRC,WAAW;IACXC,wBAAwB;IACxBC,sBAAsB;IACtBC;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAChCpK,UAAU,EACV8J,UAAU,EACVO,SAAS,EACTC,eAAe,EACfC,KAAK,EACLC,SAAS,KACN;EACH,OAAO/L,GAAG,CAACY,IAAI,CAAC,sBAAsB,EAAE;IACtCW,UAAU;IACV8J,UAAU;IACVO,SAAS;IACTC,eAAe;IACfC,KAAK;IACLC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAC3BvK,EAAE,EACFN,MAAM,EACN8K,IAAI,EACJtG,IAAI,EACJuG,KAAK,EACLxL,KAAK,EACLyL,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,WAAW,EACX5H,QAAQ,EACR6H,OAAO,KACJ;EACHrJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;EAC3C,OAAOpD,GAAG,CAACY,IAAI,CAAC,kBAAkB,GAAGO,MAAM,EAAE;IAC3CM,EAAE;IACFwK,IAAI;IACJtG,IAAI;IACJuG,KAAK;IACLxL,KAAK;IACLyL,GAAG;IACHC,MAAM;IACNC,SAAS;IACTC,MAAM;IACNC,WAAW;IACX5H,QAAQ;IACR6H;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,oBAAoB,GAAG,MAAAA,CAClCxG,GAAG,EACHxE,EAAE,EACFN,MAAM,EACNgL,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNI,cAAc,EACdH,WAAW,EACXI,QAAQ,EACRC,SAAS,KACN;EACH,OAAO5M,GAAG,CAACY,IAAI,CAAC,kBAAkB,GAAGO,MAAM,EAAE;IAC3C8E,GAAG;IACHxE,EAAE;IACF0K,GAAG;IACHC,MAAM;IACNC,SAAS;IACTC,MAAM;IACNI,cAAc;IACdH,WAAW;IACXI,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAC3BpL,EAAE,EACFN,MAAM,EACN2L,SAAS,EACTnH,IAAI,EACJuG,KAAK,EACLa,OAAO,EACPrM,KAAK,EACLyL,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,WAAW,EACX5H,QAAQ,EACR6H,OAAO,EACPhD,IAAI,KACD;EACH,IAAIA,IAAI,KAAK,GAAG,EAAE;IAChB,OAAOxJ,GAAG,CAACY,IAAI,CAAC,kBAAkB,GAAGO,MAAM,EAAE;MAC3CM,EAAE;MACFqL,SAAS;MACTnH,IAAI;MACJoH,OAAO;MACPrM,KAAK;MACLyL,GAAG;MACHC,MAAM;MACNC,SAAS;MACTC,MAAM;MACNC,WAAW;MACX5H,QAAQ;MACR6H;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAOxM,GAAG,CAACY,IAAI,CAAC,kBAAkB,GAAGO,MAAM,EAAE;MAC3CM,EAAE;MACFqL,SAAS;MACTnH,IAAI;MACJuG,KAAK;MACLxL,KAAK;MACLyL,GAAG;MACHC,MAAM;MACNC,SAAS;MACTC,MAAM;MACNC,WAAW;MACX5H,QAAQ;MACR6H;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMQ,cAAc,GAAG,MAAAA,CAC5BvL,EAAE,EACFN,MAAM,EACN2L,SAAS,EACTnH,IAAI,EACJwG,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,WAAW,EACX5H,QAAQ,EACR6H,OAAO,KACJ;EACH,OAAOxM,GAAG,CAACY,IAAI,CAAC,mBAAmB,GAAGO,MAAM,EAAE;IAC5CM,EAAE;IACFqL,SAAS;IACTnH,IAAI;IACJwG,GAAG;IACHC,MAAM;IACNC,SAAS;IACTC,MAAM;IACNC,WAAW;IACX5H,QAAQ;IACR6H;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMS,eAAe,GAAG,MAAAA,CAC7BxL,EAAE,EACFN,MAAM,EACN+L,cAAc,EACdC,YAAY,EACZC,eAAe,EACfC,aAAa,EACbC,SAAS,EACTC,UAAU,EACVlF,QAAQ,EACRtF,eAAe,EACfyK,KAAK,KACF;EACH,OAAOxN,GAAG,CAACY,IAAI,CAAC,oBAAoB,GAAGO,MAAM,EAAE;IAC7CM,EAAE;IACFyL,cAAc;IACdC,YAAY;IACZC,eAAe;IACfC,aAAa;IACbC,SAAS;IACTC,UAAU;IACVlF,QAAQ;IACRtF,eAAe;IACfyK;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAOlM,UAAU,EAAEJ,MAAM,EAAEuM,MAAM,KAAK;EAChE,OAAO1N,GAAG,CAACY,IAAI,CAAC,iBAAiB,GAAGO,MAAM,EAAE;IAAEI,UAAU;IAAEmM;EAAO,CAAC,CAAC;AACrE,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAOxM,MAAM,EAAEyM,KAAK,KAAK;EACpD,OAAO5N,GAAG,CAACY,IAAI,CAAC,mBAAmB,GAAGO,MAAM,EAAE;IAAEyM;EAAM,CAAC,CAAC;AAC1D,CAAC;AAED,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAOpM,EAAE,EAAEN,MAAM,KAAK;EAC9C,OAAOnB,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IAAEM;EAAG,CAAC,CAAC;AACnD,CAAC;AAED,OAAO,MAAMqM,gBAAgB,GAAG,MAAAA,CAAOrM,EAAE,EAAEN,MAAM,KAAK;EACpD,OAAOnB,GAAG,CAACY,IAAI,CAAC,qBAAqB,GAAGO,MAAM,EAAE;IAAEM;EAAG,CAAC,CAAC;AACzD,CAAC;AAED,OAAO,MAAMsM,cAAc,GAAG,MAAAA,CAAOtM,EAAE,EAAEN,MAAM,EAAEsB,SAAS,EAAEuL,KAAK,KAAK;EACpE,OAAOhO,GAAG,CAACY,IAAI,CAAC,mBAAmB,GAAGO,MAAM,EAAE;IAAEM,EAAE;IAAEgB,SAAS;IAAEuL;EAAM,CAAC,CAAC;AACzE,CAAC;AAED,OAAO,MAAMC,+BAA+B,GAAG,MAAOC,OAAO,IAAK;EAChE,OAAOlO,GAAG,CAACY,IAAI,CAAC,gCAAgC,EAAEsN,OAAO,CAAC;AAC5D,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAOhN,MAAM,EAAEM,EAAE,KAAK;EACjD,OAAOzB,GAAG,CAACY,IAAI,CAAC,kBAAkB,GAAGO,MAAM,EAAE;IAAEM;EAAG,CAAC,CAAC;AACtD,CAAC;AAED,OAAO,MAAM2M,qBAAqB,GAAG,MAAAA,CAAOhM,YAAY,EAAEiM,OAAO,EAAEL,KAAK,KAAK;EAC3E,OAAOhO,GAAG,CAACsO,MAAM,CAAC,2BAA2B,GAAGlM,YAAY,EAAE;IAC5DmM,IAAI,EAAE;MAAEF,OAAO;MAAEL;IAAM;EACzB,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMQ,eAAe,GAAG,MAAAA,CAAOrN,MAAM,EAAEM,EAAE,KAAK;EACnD,OAAOzB,GAAG,CAACY,IAAI,CAAC,oBAAoB,GAAGO,MAAM,EAAE;IAAEM;EAAG,CAAC,CAAC;AACxD,CAAC;AAED,OAAO,MAAMgN,oBAAoB,GAAG,MAAAA,CAAOtN,MAAM,EAAEM,EAAE,KAAK;EACxD,OAAOzB,GAAG,CAACY,IAAI,CAAC,0BAA0B,GAAGO,MAAM,EAAE;IAAEM;EAAG,CAAC,CAAC;AAC9D,CAAC;AAED,OAAO,MAAMiN,UAAU,GAAG,MAAAA,CAAOvN,MAAM,EAAEM,EAAE,KAAK;EAC9C,OAAOzB,GAAG,CAACY,IAAI,CAAC,eAAe,GAAGO,MAAM,EAAE;IAAEM;EAAG,CAAC,CAAC;AACnD,CAAC;AAED,OAAO,MAAMkN,qBAAqB,GAAG,MAAAA,CACnCpN,UAAU,EACVoD,QAAQ,EACRiK,kBAAkB,KACf;EACH,OAAO5O,GAAG,CAACY,IAAI,CAAC,2BAA2B,EAAE;IAC3CW,UAAU;IACVoD,QAAQ;IACRiK;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAOpN,EAAE,EAAEN,MAAM,KAAK;EACjD,OAAOnB,GAAG,CAACY,IAAI,CAAC,kBAAkB,GAAGO,MAAM,EAAE;IAAEM;EAAG,CAAC,CAAC;AACtD,CAAC;AAED,OAAO,MAAMqN,cAAc,GAAG,MAAAA,CAAOrN,EAAE,EAAEN,MAAM,KAAK;EAClD,OAAOnB,GAAG,CAACY,IAAI,CAAC,mBAAmB,GAAGO,MAAM,EAAE;IAAEM;EAAG,CAAC,CAAC;AACvD,CAAC;AAED,OAAO,MAAMsN,eAAe,GAAG,MAAAA,CAAOtN,EAAE,EAAEN,MAAM,KAAK;EACnD,OAAOnB,GAAG,CAACY,IAAI,CAAC,oBAAoB,GAAGO,MAAM,EAAE;IAAEM;EAAG,CAAC,CAAC;AACxD,CAAC;AAED,OAAO,MAAMuN,iBAAiB,GAAG,MAAAA,CAC/B7N,MAAM,EACNI,UAAU,EACV0N,eAAe,KACZ;EACH,OAAOjP,GAAG,CAACY,IAAI,CAAC,sBAAsB,GAAGO,MAAM,EAAE;IAC/CI,UAAU;IACV0N;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG,MAAAA,CACjC/N,MAAM,EACNI,UAAU,EACV4N,iBAAiB,KACd;EACH,OAAOnP,GAAG,CAACY,IAAI,CAAC,wBAAwB,GAAGO,MAAM,EAAE;IACjDI,UAAU;IACV4N;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAG,MAAAA,CACtBrD,SAAS,EACTpG,IAAI,EACJjF,KAAK,EACLC,QAAQ,EACR0O,eAAe,EACftM,eAAe,EACf1B,IAAI,KACD;EACH,OAAOrB,GAAG,CAACY,IAAI,CAAC,gBAAgB,EAAE;IAChCmL,SAAS;IACTpG,IAAI;IACJjF,KAAK;IACLC,QAAQ;IACR0O,eAAe;IACftM,eAAe;IACf1B;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMiO,cAAc,GAAG,MAAAA,CAC5B3J,IAAI,EACJjF,KAAK,EACLC,QAAQ,EACR4O,MAAM,KACH;EACH,OAAOvP,GAAG,CAACY,IAAI,CAAC,eAAe,EAAE;IAC/B+E,IAAI;IACJjF,KAAK;IACLC,QAAQ;IACR4O;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAChCzD,SAAS,EACTxK,UAAU,EACVoE,IAAI,EACJhB,QAAQ,EACRmB,OAAO,EACPC,KAAK,KACF;EACH,OAAO/F,GAAG,CAACY,IAAI,CAAC,qBAAqB,EAAE;IACrCmL,SAAS;IACTxK,UAAU;IACVoE,IAAI;IACJhB,QAAQ;IACRmB,OAAO;IACPC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAM0J,eAAe,GAAG,MAAAA,CAC7B1D,SAAS,EACTE,IAAI,EACJtG,IAAI,EACJuG,KAAK,EACLxL,KAAK,EACLyL,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,WAAW,EACX5H,QAAQ,EACR6H,OAAO,EACPnE,QAAQ,EACRmB,IAAI,KACD;EACH,OAAOxJ,GAAG,CAACY,IAAI,CAAC,uBAAuB,EAAE;IACvCmL,SAAS;IACTE,IAAI;IACJtG,IAAI;IACJuG,KAAK;IACLxL,KAAK;IACLyL,GAAG;IACHC,MAAM;IACNC,SAAS;IACTC,MAAM;IACNC,WAAW;IACX5H,QAAQ;IACR6H,OAAO;IACPnE,QAAQ;IACRmB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMkG,iBAAiB,GAAG,MAAAA,CAC/BzD,IAAI,EACJ0D,YAAY,EACZjP,KAAK,EACLiE,QAAQ,EACR6E,IAAI,EACJoG,WAAW,EACXjP,QAAQ,EACR0O,eAAe,EACftM,eAAe,KACZ;EACH,OAAO/C,GAAG,CAACY,IAAI,CAAC,oBAAoB,EAAE;IACpCqL,IAAI;IACJ0D,YAAY;IACZjP,KAAK;IACLiE,QAAQ;IACR6E,IAAI;IACJoG,WAAW;IACXjP,QAAQ;IACR0O,eAAe;IACftM;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAM8M,kBAAkB,GAAG,MAAAA,CAAOpN,SAAS,EAAEqN,YAAY,KAAK;EACnE,OAAO9P,GAAG,CAAC+P,GAAG,CAAC,uBAAuBtN,SAAS,EAAE,EAAEqN,YAAY,CAAC;AAClE,CAAC;;AAED;AACA,OAAO,MAAME,mBAAmB,GAAG,MAAAA,CAAOvN,SAAS,EAAEwN,aAAa,KAAK;EACrE,OAAOjQ,GAAG,CAACY,IAAI,CAAC,wBAAwB6B,SAAS,EAAE,EAAEwN,aAAa,CAAC;AACrE,CAAC;AAED,OAAO,MAAMC,cAAc,GAAG,MAAAA,CAC5BnE,SAAS,EACTxK,UAAU,EACV4O,KAAK,EACLC,eAAe,EACfjD,YAAY,EACZkD,cAAc,EACdC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACX9E,SAAS,EACT+E,WAAW,KACR;EACH,OAAO3Q,GAAG,CAACY,IAAI,CAAC,sBAAsB,EAAE;IACtCmL,SAAS;IACTxK,UAAU;IACV4O,KAAK;IACLC,eAAe;IACfjD,YAAY;IACZkD,cAAc;IACdC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC,WAAW;IACXC,WAAW;IACX9E,SAAS;IACT+E;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,eAAe,GAAG,MAAO3P,MAAM,IAAK;EAC/C,OAAOjB,GAAG,CAAC+P,GAAG,CAAC,oBAAoB9O,MAAM,EAAE,CAAC;AAC9C,CAAC;;AAED;AACA,OAAO,MAAM4P,yBAAyB,GAAG,MAAAA,CAAOC,aAAa,EAAExQ,KAAK,KAAK;EACvED,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACY,IAAI,CAAC,wCAAwCkQ,aAAa,EAAE,CAAC;AAC1E,CAAC;;AAED;AACA,OAAO,MAAMC,2BAA2B,GAAG,MAAAA,CAAOC,SAAS,EAAE1Q,KAAK,KAAK;EACrED,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACe,GAAG,CAAC,2CAA2CiQ,SAAS,EAAE,CAAC;AACxE,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAOD,SAAS,EAAE1Q,KAAK,KAAK;EAC1DD,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACY,IAAI,CAAC,8BAA8BoQ,SAAS,EAAE,CAAC;AAC5D,CAAC;AAED,OAAO,MAAME,qBAAqB,GAAG,MAAAA,CAAOzO,SAAS,EAAE0O,gBAAgB,KAAK;EAC1E,OAAOnR,GAAG,CAAC+P,GAAG,CAAC,YAAYtN,SAAS,mBAAmB,EAAE;IAAE0O;EAAiB,CAAC,CAAC;AAChF,CAAC;AAED,OAAO,MAAMC,0BAA0B,GAAG,MAAAA,CAAO3O,SAAS,EAAE4O,gBAAgB,EAAEC,iBAAiB,KAAK;EAClG,OAAOtR,GAAG,CAAC+P,GAAG,CAAC,YAAYtN,SAAS,wBAAwB,EAAE;IAAE4O,gBAAgB;IAAEC;EAAkB,CAAC,CAAC;AACxG,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG,MAAAA,CACjCC,WAAW,EACXjQ,UAAU,EACV4O,KAAK,EACLC,eAAe,EACfjD,YAAY,EACZkD,cAAc,EACdC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACX9E,SAAS,EACT+E,WAAW,KACR;EACH,OAAO3Q,GAAG,CAAC+P,GAAG,CAAC,2BAA2B,EAAE;IAC1CyB,WAAW;IACXjQ,UAAU;IACV4O,KAAK;IACLC,eAAe;IACfjD,YAAY;IACZkD,cAAc;IACdC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC,WAAW;IACXC,WAAW;IACX9E,SAAS;IACT+E;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMc,qBAAqB,GAAG,MAAAA,CACnCD,WAAW,EACXjQ,UAAU,EACV4O,KAAK,EACLC,eAAe,EACfjD,YAAY,EACZuD,WAAW,EACX9E,SAAS,EACT+E,WAAW,KACR;EACH,OAAO3Q,GAAG,CAAC+P,GAAG,CAAC,6BAA6B,EAAE;IAC5CyB,WAAW;IACXjQ,UAAU;IACV4O,KAAK;IACLC,eAAe;IACfjD,YAAY;IACZuD,WAAW;IACX9E,SAAS;IACT+E;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMe,iBAAiB,GAAG,MAAAA,CAC/B3F,SAAS,EACTxK,UAAU,EACV+G,KAAK,EACLO,eAAe,EACfC,2BAA2B,EAC3B6I,QAAQ,EACRC,WAAW,EACXC,wBAAwB,EACxBjJ,MAAM,KACH;EACH,OAAO5I,GAAG,CAACY,IAAI,CAAC,yBAAyB,EAAE;IACzCmL,SAAS;IACTxK,UAAU;IACV+G,KAAK;IACLO,eAAe;IACfC,2BAA2B;IAC3B6I,QAAQ;IACRC,WAAW;IACXC,wBAAwB;IACxBjJ;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMkJ,YAAY,GAAG,MAAAA,CAC1B/F,SAAS,EACTxK,UAAU,EACVwI,gBAAgB,EAChBC,mBAAmB,EACnB1B,KAAK,EACL2B,WAAW,EACXrC,GAAG,EACHgG,KAAK,EACLzF,KAAK,EACLU,eAAe,EACfW,IAAI,KACD;EACH,OAAOxJ,GAAG,CAACY,IAAI,CAAC,oBAAoB,EAAE;IACpCmL,SAAS;IACTxK,UAAU;IACVwI,gBAAgB;IAChBC,mBAAmB;IACnB1B,KAAK;IACL2B,WAAW;IACXrC,GAAG;IACHgG,KAAK;IACLzF,KAAK;IACLU,eAAe;IACfW;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMuI,uBAAuB,GAAG,MAAAA,CACrChG,SAAS,EACTxK,UAAU,EACV+G,KAAK,EACLmB,GAAG,EACHC,GAAG,EACHN,SAAS,EACTxB,GAAG,EACH+B,oBAAoB,EACpBC,cAAc,EACdL,YAAY,EACZC,IAAI,KACD;EACH,OAAOxJ,GAAG,CAACY,IAAI,CAAC,+BAA+B,EAAE;IAC/CmL,SAAS;IACTxK,UAAU;IACV+G,KAAK;IACLmB,GAAG;IACHC,GAAG;IACHN,SAAS;IACTxB,GAAG;IACH+B,oBAAoB;IACpBC,cAAc;IACdL,YAAY;IACZC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMwI,kBAAkB,GAAG,MAAAA,CAChCjG,SAAS,EACT1D,QAAQ,EACR9G,UAAU,EACV+G,KAAK,EACLH,KAAK,EACLP,GAAG,EACHgG,KAAK,KACF;EACH,OAAO5N,GAAG,CAACY,IAAI,CAAC,2BAA2B,GAAGmL,SAAS,EAAE;IACvDA,SAAS;IACT1D,QAAQ;IACR9G,UAAU;IACV+G,KAAK;IACLH,KAAK;IACLP,GAAG;IACHgG;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMqE,mBAAmB,GAAG,MAAO1D,IAAI,IAAK;EACjD,OAAOvO,GAAG,CAACY,IAAI,CAAC,wBAAwB,GAAG2N,IAAI,CAACpN,MAAM,EAAEoN,IAAI,CAAC;AAC/D,CAAC;AAED,OAAO,MAAM2D,mBAAmB,GAAG,MAAO3D,IAAI,IAAK;EACjD,OAAOvO,GAAG,CAACY,IAAI,CAAC,2BAA2B,GAAG2N,IAAI,CAACpN,MAAM,EAAEoN,IAAI,CAAC;AAClE,CAAC;AAED,OAAO,MAAM4D,oBAAoB,GAAG,MAAAA,CAClCpG,SAAS,EACTxK,UAAU,EACV6Q,aAAa,KACV;EACH,OAAOpS,GAAG,CAACY,IAAI,CAAC,wBAAwB,EAAE;IACxCmL,SAAS;IACTxK,UAAU;IACV6Q;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAChCtG,SAAS,EACTuG,IAAI,EACJC,eAAe,EACf/K,KAAK,EACLgL,UAAU,EACVC,WAAW,EACXC,WAAW,KACR;EACH,OAAO1S,GAAG,CAACY,IAAI,CAAC,0BAA0B,EAAE;IAC1CmL,SAAS;IACTuG,IAAI;IACJC,eAAe;IACf/K,KAAK;IACLgL,UAAU;IACVC,WAAW;IACXC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,+BAA+B,GAAG,MAAAA,CAC7C5G,SAAS,EACTrL,KAAK,EACLiF,IAAI,EACJ6K,QAAQ,EACRoC,QAAQ,EACRrD,MAAM,EACNgD,eAAe,EACfM,YAAY,EACZC,aAAa,KACV;EACH,OAAO9S,GAAG,CAACY,IAAI,CAAC,kBAAkB,EAAE;IAClCmL,SAAS;IACTrL,KAAK;IACLiF,IAAI;IACJ6K,QAAQ;IACRoC,QAAQ;IACRrD,MAAM;IACNgD,eAAe;IACfM,YAAY;IACZC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,eAAe,GAAG,MAAAA,CAC7BhH,SAAS,EACTiH,MAAM,KACH;EACH,OAAOhT,GAAG,CAACY,IAAI,CAAC,qBAAqB,GAAGmL,SAAS,EAAE;IACjDiH;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,uBAAuB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,SAAS,KAAK;EACtE,OAAOnT,GAAG,CAACY,IAAI,CAAC,2BAA2B,EAAE;IAC3CsS,UAAU;IACVC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,oBAAoB,GAAG,MAAAA,CAAOvQ,SAAS,EAAEwQ,kBAAkB,EAAEC,MAAM,KAAK;EACnF,OAAOtT,GAAG,CAAC+P,GAAG,CAAC,yBAAyB,EAAE;IACxClN,SAAS;IACTwQ,kBAAkB;IAClBC;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMC,kBAAkB,GAAG,MAAO1Q,SAAS,IAAK;EACrD,OAAO7C,GAAG,CAACe,GAAG,CAAC,oBAAoB,GAAG8B,SAAS,CAAC;AAClD,CAAC;;AAED;AACA,OAAO,MAAM2Q,iBAAiB,GAAG,MAAAA,CAAO3Q,SAAS,EAAE4Q,QAAQ,EAAEC,QAAQ,KAAK;EACxE,OAAO1T,GAAG,CAACY,IAAI,CAAC,oBAAoB,GAAGiC,SAAS,EAAE;IAChD4Q,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,oBAAoB,GAAG,MAAAA,CAAO9Q,SAAS,EAAE+Q,UAAU,EAAEH,QAAQ,EAAEC,QAAQ,EAAEJ,MAAM,GAAG,IAAI,KAAK;EACtG,OAAOtT,GAAG,CAAC+P,GAAG,CAAC,qBAAqBlN,SAAS,IAAI+Q,UAAU,EAAE,EAAE;IAC7DH,QAAQ;IACRC,QAAQ;IACRJ;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMO,oBAAoB,GAAG,MAAAA,CAAOhR,SAAS,EAAE+Q,UAAU,KAAK;EACnE,OAAO5T,GAAG,CAACsO,MAAM,CAAC,qBAAqBzL,SAAS,IAAI+Q,UAAU,EAAE,CAAC;AACnE,CAAC;AAED,OAAO,MAAME,eAAe,GAAG,MAAAA,CAC7B/H,SAAS,EACTe,SAAS,EACTnH,IAAI,EACJoH,OAAO,EACPb,KAAK,EACLxL,KAAK,EACLyL,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,WAAW,EACX5H,QAAQ,EACR6H,OAAO,EACPzJ,eAAe,EACfyG,IAAI,KACD;EACH,IAAIA,IAAI,KAAK,GAAG,EAAE;IAChB,OAAOxJ,GAAG,CAACY,IAAI,CAAC,uBAAuB,EAAE;MACvCmL,SAAS;MACTe,SAAS;MACTnH,IAAI;MACJoH,OAAO;MACPrM,KAAK;MACLyL,GAAG;MACHC,MAAM;MACNC,SAAS;MACTC,MAAM;MACNC,WAAW;MACX5H,QAAQ;MACR6H,OAAO;MACPzJ,eAAe;MACfyG;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAOxJ,GAAG,CAACY,IAAI,CAAC,uBAAuB,EAAE;MACvCmL,SAAS;MACTe,SAAS;MACTnH,IAAI;MACJuG,KAAK;MACLxL,KAAK;MACLyL,GAAG;MACHC,MAAM;MACNC,SAAS;MACTC,MAAM;MACNC,WAAW;MACX5H,QAAQ;MACR6H,OAAO;MACPzJ,eAAe;MACfyG;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMuK,2BAA2B,GAAG,MAAAA,CACzCxS,UAAU,EACV+Q,IAAI,EACJ3N,QAAQ,KACL;EACH,OAAO3E,GAAG,CAACY,IAAI,CAAC,4BAA4B,EAAE;IAAEW,UAAU;IAAE+Q,IAAI;IAAE3N;EAAS,CAAC,CAAC;AAC/E,CAAC;AAED,OAAO,MAAMqP,gBAAgB,GAAG,MAAAA,CAC9BjI,SAAS,EACTe,SAAS,EACTnH,IAAI,EACJwG,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,WAAW,EACX5H,QAAQ,EACR6H,OAAO,EACPzJ,eAAe,EACfyG,IAAI,KACD;EACH,OAAOxJ,GAAG,CAACY,IAAI,CAAC,wBAAwB,EAAE;IACxCmL,SAAS;IACTe,SAAS;IACTnH,IAAI;IACJwG,GAAG;IACHC,MAAM;IACNC,SAAS;IACTC,MAAM;IACNC,WAAW;IACX5H,QAAQ;IACR6H,OAAO;IACPzJ,eAAe;IACfyG;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMyK,iBAAiB,GAAG,MAAAA,CAC/BlI,SAAS,EACTmI,YAAY,EACZhH,cAAc,EACdC,YAAY,EACZC,eAAe,EACfC,aAAa,EACbC,SAAS,EACTC,UAAU,EACVlF,QAAQ,EACRtF,eAAe,EACfyK,KAAK,KACF;EACH,OAAOxN,GAAG,CAACY,IAAI,CAAC,yBAAyB,EAAE;IACzCmL,SAAS;IACTmI,YAAY;IACZhH,cAAc;IACdC,YAAY;IACZC,eAAe;IACfC,aAAa;IACbC,SAAS;IACTC,UAAU;IACVlF,QAAQ;IACRtF,eAAe;IACfyK;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAM2G,0BAA0B,GAAG,MAAAA,CACxCC,UAAU,EACVvB,YAAY,EACZwB,KAAK,EACLC,KAAK,EACLC,WAAW,KACR;EACH,OAAOvU,GAAG,CAACY,IAAI,CAAC,gCAAgC,EAAE;IAChDwT,UAAU;IACVvB,YAAY;IACZwB,KAAK;IACLC,KAAK;IACLC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,0BAA0B,GAAG,MAAAA,CACxCJ,UAAU,EACVjH,YAAY,EACZqD,QAAQ,EACR6D,KAAK,EACLI,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHlU,KAAK,EACLmU,UAAU,EACVC,aAAa,EACbC,KAAK,EACLT,KAAK,EACLC,WAAW,EACXS,QAAQ,KACL;EACH,OAAOhV,GAAG,CAACY,IAAI,CAAC,gCAAgC,EAAE;IAChDwT,UAAU;IACVjH,YAAY;IACZqD,QAAQ;IACR6D,KAAK;IACLI,aAAa;IACbC,WAAW;IACXC,UAAU;IACVC,GAAG;IACHlU,KAAK;IACLmU,UAAU;IACVC,aAAa;IACbC,KAAK;IACLT,KAAK;IACLC,WAAW;IACXS;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAChCb,UAAU,EACVjH,YAAY,EACZqD,QAAQ,EACRiE,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHlU,KAAK,EACLmU,UAAU,EACVC,aAAa,EACbC,KAAK,EACLT,KAAK,EACLC,WAAW,EACXS,QAAQ,EACRE,OAAO,KACJ;EACH,OAAOlV,GAAG,CAACY,IAAI,CAAC,8BAA8B,EAAE;IAC9CwT,UAAU;IACVjH,YAAY;IACZqD,QAAQ;IACRiE,aAAa;IACbC,WAAW;IACXC,UAAU;IACVC,GAAG;IACHlU,KAAK;IACLmU,UAAU;IACVC,aAAa;IACbC,KAAK;IACLT,KAAK;IACLC,WAAW;IACXS,QAAQ;IACRE;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,wBAAwB,GAAG,MAAAA,CACtCf,UAAU,EACVvB,YAAY,EACZyB,KAAK,EACLC,WAAW,KACR;EACH,OAAOvU,GAAG,CAACY,IAAI,CAAC,uBAAuB,EAAE;IACvCwT,UAAU;IACVvB,YAAY;IACZyB,KAAK;IACLC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMa,sBAAsB,GAAG,MAAOC,WAAW,IAAK;EAC3D,OAAOrV,GAAG,CAACY,IAAI,CAAC,2BAA2B,EAAE;IAAEyU;EAAY,CAAC,CAAC;AAC/D,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMC,oBAAoB,GAAG,MAAOD,WAAW,IAAK;EACzD,OAAOrV,GAAG,CAACY,IAAI,CAAC,yBAAyB,EAAE;IAAEyU;EAAY,CAAC,CAAC;AAC7D,CAAC;AAED,OAAO,MAAME,0BAA0B,GAAG,MAAOC,mBAAmB,IAAK;EACvE,OAAOxV,GAAG,CAACY,IAAI,CAAC,qBAAqB,EAAE;IAAE4U;EAAoB,CAAC,CAAC;AACjE,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG,MAAOD,mBAAmB,IAAK;EAChE,OAAOxV,GAAG,CAACY,IAAI,CAAC,cAAc,EAAE;IAAE4U;EAAoB,CAAC,CAAC;AAC1D,CAAC;AAED,OAAO,MAAME,cAAc,GAAG,MAAOC,SAAS,IAAK;EACjD,OAAO3V,GAAG,CAACY,IAAI,CAAC,kBAAkB,EAAE;IAAE+U;EAAU,CAAC,CAAC;AACpD,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG,MAAAA,CAAOC,UAAU,EAAEtU,UAAU,KAAK;EACnE,OAAOvB,GAAG,CAACY,IAAI,CAAC,wBAAwB,EAAE;IAAEiV,UAAU;IAAEtU;EAAW,CAAC,CAAC;AACvE,CAAC;AAED,OAAO,MAAMuU,qBAAqB,GAAG,MAAAA,CAAOD,UAAU,EAAEtU,UAAU,KAAK;EACrE;EACA,OAAOvB,GAAG,CAACY,IAAI,CAAC,0BAA0B,EAAE;IAAEiV,UAAU;IAAEtU;EAAW,CAAC,CAAC;AACzE,CAAC;AAED,OAAO,MAAMwU,gBAAgB,GAAG,MAAAA,CAAOtU,EAAE,EAAE+E,WAAW,KAAK;EACzD,OAAOxG,GAAG,CAACY,IAAI,CAAC,mBAAmB,EAAE;IAAEa,EAAE;IAAE+E;EAAY,CAAC,CAAC;AAC3D,CAAC;AAED,OAAO,MAAMwP,iBAAiB,GAAG,MAAOvU,EAAE,IAAK;EAC7C,OAAOzB,GAAG,CAACe,GAAG,CAAC,kBAAkB,GAAGU,EAAE,CAAC;AACzC,CAAC;AAED,OAAO,MAAMwU,sBAAsB,GAAG,MAAOxU,EAAE,IAAK;EAClD,OAAOzB,GAAG,CAACe,GAAG,CAAC,0BAA0B,GAAGU,EAAE,CAAC;AACjD,CAAC;AAED,OAAO,MAAMyU,oBAAoB,GAAG,MAAOjN,SAAS,IAAK;EACvD,OAAOjJ,GAAG,CAACe,GAAG,CAAC,wBAAwBkI,SAAS,EAAE,CAAC;AACrD,CAAC;AAED,OAAO,MAAMkN,qBAAqB,GAAG,MAAAA,CAAO5U,UAAU,EAAE6U,eAAe,KAAK;EAC1E,OAAOpW,GAAG,CAACY,IAAI,CAAC,0BAA0B,EAAE;IAAEW,UAAU;IAAE6U;EAAgB,CAAC,CAAC;AAC9E,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG,MAAO9U,UAAU,IAAK;EACrD,OAAOvB,GAAG,CAACY,IAAI,CAAC,uBAAuB,EAAE;IAAEW;EAAW,CAAC,CAAC;AAC1D,CAAC;AAED,OAAO,MAAM+U,sBAAsB,GAAG,MAAAA,CAAO7U,EAAE,EAAE8U,UAAU,KAAK;EAC9D,OAAOvW,GAAG,CAACwW,KAAK,CAAC,gBAAgB/U,EAAE,aAAa,EAAE;IAAE8U;EAAW,CAAC,CAAC;AACnE,CAAC;AAED,OAAO,MAAME,0BAA0B,GAAG,MAAAA,CAAOhV,EAAE,EAAEiV,cAAc,KAAK;EACtE,OAAO1W,GAAG,CAACwW,KAAK,CAAC,gBAAgB/U,EAAE,iBAAiB,EAAE;IAAEiV;EAAe,CAAC,CAAC;AAC3E,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG,MAAAA,CAAO3F,SAAS,EAAE1Q,KAAK,KAAK;EAC3DD,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACe,GAAG,CAAC,0BAA0B,GAAGiQ,SAAS,CAAC;AACxD,CAAC;;AAED;AACA,OAAO,MAAM4F,mBAAmB,GAAG,MAAAA,CAAO5F,SAAS,EAAE1Q,KAAK,KAAK;EAC7DD,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACY,IAAI,CAAC,gCAAgC,GAAGoQ,SAAS,CAAC;AAC/D,CAAC;AAED,OAAO,MAAM6F,8BAA8B,GAAG,MAAAA,CAAO7F,SAAS,EAAE1Q,KAAK,KAAK;EACxED,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACY,IAAI,CAAC,4CAA4C,GAAGoQ,SAAS,CAAC;AAC3E,CAAC;AAED,OAAO,MAAM8F,0BAA0B,GAAG,MAAAA,CAAOhG,aAAa,EAAExQ,KAAK,KAAK;EACxED,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACY,IAAI,CAAC,wCAAwC,GAAGkQ,aAAa,CAAC;AAC3E,CAAC;;AAID;AACA,OAAO,MAAMiG,2BAA2B,GAAG,MAAAA,CAAO/F,SAAS,EAAE1Q,KAAK,KAAK;EACrED,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACe,GAAG,CAAC,oCAAoC,GAAGiQ,SAAS,CAAC;AAClE,CAAC;AAED,OAAO,MAAMgG,mBAAmB,GAAG,MAAAA,CAAO7V,MAAM,EAAEM,EAAE,KAAK;EACvD,OAAOzB,GAAG,CAACsO,MAAM,CAAC,yBAAyBnN,MAAM,EAAE,EAAE;IAAEoN,IAAI,EAAE;MAAE9M;IAAG;EAAE,CAAC,CAAC;AACxE,CAAC;;AAED;AACA,OAAO,MAAMwV,qBAAqB,GAAG,MAAOxU,SAAS,IAAK;EACxD,OAAOzC,GAAG,CAACe,GAAG,CAAC,qCAAqC0B,SAAS,EAAE,CAAC;AAClE,CAAC;AAED,OAAO,MAAMyU,sBAAsB,GAAG,MAAAA,CAAOzU,SAAS,EAAE0U,aAAa,EAAEC,QAAQ,KAAK;EAClF,OAAOpX,GAAG,CAACY,IAAI,CAAC,qCAAqC6B,SAAS,YAAY0U,aAAa,EAAE,EAAEC,QAAQ,CAAC;AACtG,CAAC;AAED,OAAO,MAAMC,wBAAwB,GAAG,MAAAA,CAAO5U,SAAS,EAAE0U,aAAa,EAAEG,WAAW,KAAK;EACvF,OAAOtX,GAAG,CAACY,IAAI,CAAC,qCAAqC6B,SAAS,cAAc0U,aAAa,EAAE,EAAE;IAAEG;EAAY,CAAC,CAAC;AAC/G,CAAC;AAED,OAAO,MAAMC,uBAAuB,GAAG,MAAAA,CAAO9U,SAAS,EAAE0U,aAAa,EAAEK,MAAM,KAAK;EACjF,OAAOxX,GAAG,CAACsO,MAAM,CAAC,qCAAqC7L,SAAS,aAAa0U,aAAa,EAAE,EAAE;IAAE5I,IAAI,EAAE;MAAEiJ;IAAO;EAAE,CAAC,CAAC;AACrH,CAAC;AAED,OAAO,MAAMC,6BAA6B,GAAG,MAAOhV,SAAS,IAAK;EAChE,OAAOzC,GAAG,CAACY,IAAI,CAAC,qCAAqC6B,SAAS,qBAAqB,CAAC;AACtF,CAAC;AAED,OAAO,MAAMiV,uBAAuB,GAAG,MAAAA,CAAO1G,SAAS,EAAEzB,MAAM,EAAEoI,OAAO,KAAK;EAC3E;EACA,OAAO3X,GAAG,CAACY,IAAI,CAAC,mCAAmC,GAAGoQ,SAAS,EAAE;IAAEzB,MAAM;IAAEoI;EAAQ,CAAC,CAAC;AACvF,CAAC;AAED,OAAO,MAAMC,qBAAqB,GAAG,MAAO5G,SAAS,IAAK;EACxD,OAAOhR,GAAG,CAACe,GAAG,CAAC,kCAAkCiQ,SAAS,EAAE,CAAC;AAC/D,CAAC;;AAED;AACA,OAAO,MAAM6G,wBAAwB,GAAG,MAAO7G,SAAS,IAAK;EAC3D,OAAOhR,GAAG,CAACY,IAAI,CAAC,sCAAsCoQ,SAAS,EAAE,CAAC;AACpE,CAAC;;AAED;AACA,OAAO,MAAM8G,wBAAwB,GAAG,MAAO9G,SAAS,IAAK;EAC3D,OAAOhR,GAAG,CAACe,GAAG,CAAC,mCAAmCiQ,SAAS,EAAE,CAAC;AAChE,CAAC;;AAED;AACA,OAAO,MAAM+G,gBAAgB,GAAG,MAAAA,CAC9B/G,SAAS,EACTtP,IAAI,EACJsW,QAAQ,EACRC,KAAK,EACL3X,KAAK,KACF;EACHD,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACe,GAAG,CAAC,0BAA0BiQ,SAAS,EAAE,EAAE;IACpDnP,MAAM,EAAE;MACNH,IAAI;MACJsW,QAAQ;MACRC;IACF;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAG,MAAAA,CACjClH,SAAS,EACTmH,MAAM,EACNzW,IAAI,EACJsW,QAAQ,EACR1X,KAAK,KACF;EACHD,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACe,GAAG,CAAC,0BAA0BiQ,SAAS,aAAamH,MAAM,EAAE,EAAE;IACvEtW,MAAM,EAAE;MACNH,IAAI;MACJsW;IACF;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMI,sBAAsB,GAAG,MAAAA,CAAOpH,SAAS,EAAE1Q,KAAK,KAAK;EAChED,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACe,GAAG,CAAC,0BAA0BiQ,SAAS,eAAe,CAAC;AACpE,CAAC;;AAED;AACA,OAAO,MAAMqH,0BAA0B,GAAG,MAAAA,CAAOrH,SAAS,EAAEmH,MAAM,EAAE7X,KAAK,KAAK;EAC5ED,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAAC+P,GAAG,CAAC,0BAA0BiB,SAAS,aAAamH,MAAM,eAAe,CAAC;AACvF,CAAC;;AAED;AACA,OAAO,MAAMG,mBAAmB,GAAG,MAAAA,CAAOtH,SAAS,EAAEmH,MAAM,EAAE7X,KAAK,KAAK;EACrED,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACe,GAAG,CAAC,0BAA0BiQ,SAAS,SAASmH,MAAM,EAAE,CAAC;AACtE,CAAC;;AAED;AACA,OAAO,MAAMI,kBAAkB,GAAG,MAAAA,CAAOvH,SAAS,EAAEwH,aAAa,EAAEC,WAAW,EAAEnY,KAAK,KAAK;EACxFD,YAAY,CAACC,KAAK,CAAC;EACnB,OAAON,GAAG,CAACY,IAAI,CAAC,0BAA0BoQ,SAAS,SAAS,EAAE;IAC5DwH,aAAa;IACbC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;EACpC,OAAO1Y,GAAG,CAACe,GAAG,CACZ,8DACF,CAAC;AACH,CAAC;AAED,OAAO,MAAM4X,aAAa,GAAG,MAAOC,iBAAiB,IAAK;EACxD,OAAO5Y,GAAG,CAACe,GAAG,CACZ,8DAA8D,GAC9D6X,iBAAiB,GACjB,aACF,CAAC;AACH,CAAC;AAED,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAOC,IAAI,EAAEC,EAAE,KAAK;EAC5C5V,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE0V,IAAI,EAAEC,EAAE,CAAC;EACrD,OAAO/Y,GAAG,CAACe,GAAG,CAAC,0BAA0B+X,IAAI,OAAOC,EAAE,EAAE,CAAC;AAC3D,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,YAAY,EAAEC,YAAY,KAAK;EACrE,MAAM;IAAEC,GAAG,EAAEC,IAAI;IAAEC,GAAG,EAAEC;EAAK,CAAC,GAAGL,YAAY;EAC7C,MAAM;IAAEE,GAAG,EAAEI,IAAI;IAAEF,GAAG,EAAEG;EAAK,CAAC,GAAGN,YAAY;EAC7C,OAAOlZ,GAAG,CAACe,GAAG,CAAC,4BAA4BqY,IAAI,SAASE,IAAI,SAASC,IAAI,SAASC,IAAI,EAAE,CAAC;AAC3F,CAAC;;AAED;;AAEA,OAAO,MAAMC,QAAQ,GAAG,MAAAA,CAAA,KAAY;EAClC,MAAM;IAAElL;EAAK,CAAC,GAAG,MAAMvO,GAAG,CAACe,GAAG,CAAC,QAAQ,CAAC;EAExC,IAAI,CAACwN,IAAI,CAACb,MAAM,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,OAAOa,IAAI,CAACA,IAAI;AAClB,CAAC;AAED,OAAO,MAAMmL,UAAU,GAAG,MAAAA,CAAO;EAAE/T,IAAI;EAAEgU;AAAQ,CAAC,KAAK;EACrD,MAAM;IAAEpL;EAAK,CAAC,GAAG,MAAMvO,GAAG,CAACY,IAAI,CAAC,QAAQ,EAAE;IACxC+E,IAAI;IACJgU;EACF,CAAC,CAAC;EAEF,IAAI,CAACpL,IAAI,CAACb,MAAM,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,OAAOa,IAAI,CAACA,IAAI;AAClB,CAAC;AAED,OAAO,MAAMqL,cAAc,GAAG,MAAAA,CAAO;EAAEC,GAAG;EAAEC;AAAS,CAAC,KAAK;EACzD,MAAM;IAAEvL;EAAK,CAAC,GAAG,MAAMvO,GAAG,CAACY,IAAI,CAAC,aAAa,EAAE;IAC7CiZ,GAAG;IACHC;EACF,CAAC,CAAC;EAEF,IAAI,CAACvL,IAAI,CAACb,MAAM,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,OAAOa,IAAI,CAACA,IAAI;AAClB,CAAC;AAGD,OAAO,MAAMwL,kBAAkB,GAAG,MAAAA,CAChC;EACEC,OAAO;EACPzY,UAAU;EACV4O,KAAK;EACLC,eAAe;EACfjD,YAAY;EACZuD,WAAW;EACX9E;AACF,CAAC,KACE;EACH,OAAO5L,GAAG,CAACY,IAAI,CAAC,uBAAuB,EAAE;IACvCW,UAAU;IACV4O,KAAK;IACL6J,OAAO;IACP5J,eAAe;IACfjD,YAAY;IACZuD,WAAW;IACX9E;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMqO,UAAU,GAAG,MAAAA,CAAO;EAAEC;AAAO,CAAC,KAAK;EAC9C,MAAM;IAAE3L;EAAK,CAAC,GAAG,MAAMvO,GAAG,CAACsO,MAAM,CAAC,UAAU4L,MAAM,EAAE,CAAC;EAErD,IAAI,CAAC3L,IAAI,CAACb,MAAM,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,OAAOa,IAAI,CAACA,IAAI;AAClB,CAAC;AAED,OAAO,MAAM4L,eAAe,GAAG,MAAAA,CAC7B;EACEH,OAAO;EACPnH,YAAY;EACZuH,WAAW;EACXC,aAAa;EACbC;AACF,CAAC,KACE;EACH,OAAOta,GAAG,CAACY,IAAI,CAAC,mBAAmB,EAAE;IACnCoZ,OAAO;IACPnH,YAAY;IACZuH,WAAW;IACXC,aAAa;IACbC;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,+BAA+B,GAAG,MAAO9X,SAAS,IAAK;EAClE,OAAOzC,GAAG,CAACe,GAAG,CAAC,oCAAoC0B,SAAS,EAAE,CAAC;AACjE,CAAC;AAED,OAAO,MAAM+X,qCAAqC,GAAG,MAAAA,CAAO/X,SAAS,EAAEgY,aAAa,EAAEC,YAAY,EAAEC,UAAU,GAAG,KAAK,KAAK;EACzH,OAAO3a,GAAG,CAAC+P,GAAG,CAAC,0CAA0CtN,SAAS,EAAE,EAAE;IACpEgY,aAAa;IACbC,YAAY;IACZC;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,wBAAwB,GAAG,MAAAA,CAAOnY,SAAS,EAAEwG,SAAS,EAAEnI,MAAM,EAAE+Z,UAAU,EAAEC,OAAO,EAAExD,WAAW,EAAEyD,cAAc,KAAK;EAChI,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEzY,SAAS,CAAC;EACvCuY,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEjS,SAAS,CAAC;EACvC+R,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEpa,MAAM,CAAC;EACjCka,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEL,UAAU,IAAI,EAAE,CAAC;EAC/CG,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE5D,WAAW,IAAI,EAAE,CAAC;EACjD0D,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEH,cAAc,IAAI,EAAE,CAAC;EAEvD,IAAID,OAAO,EAAE;IACXE,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEJ,OAAO,CAAC;EACrC;EAEA,OAAO9a,GAAG,CAACY,IAAI,CAAC,8BAA8B,EAAEoa,QAAQ,EAAE;IACxD5a,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAM+a,0BAA0B,GAAG,MAAAA,CAAOra,MAAM,EAAE4M,MAAM,GAAG,OAAO,EAAEhM,IAAI,GAAG,CAAC,EAAEa,KAAK,GAAG,EAAE,KAAK;EAClG,OAAOvC,GAAG,CAACe,GAAG,CAAC,iCAAiCD,MAAM,EAAE,EAAE;IACxDe,MAAM,EAAE;MACN6L,MAAM;MACNhM,IAAI;MACJa;IACF;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAM6Y,kCAAkC,GAAG,MAAAA,CAAOta,MAAM,EAAE2B,SAAS,EAAE4Y,aAAa,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,kBAAkB,KAAK;EAC/I,OAAOxb,GAAG,CAAC+P,GAAG,CAAC,0CAA0CjP,MAAM,EAAE,EAAE;IACjE2B,SAAS;IACT4Y,aAAa;IACbC,UAAU;IACVC,iBAAiB;IACjBC;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,yBAAyB,GAAG,MAAAA,CAAOhZ,SAAS,EAAE3B,MAAM,KAAK;EACpE,OAAOd,GAAG,CAACe,GAAG,CAAC,kCAAkC0B,SAAS,IAAI3B,MAAM,EAAE,CAAC;AACzE,CAAC;AAED,OAAO,MAAM4a,sBAAsB,GAAG,MAAOzS,SAAS,IAAK;EACzD,OAAOjJ,GAAG,CAACe,GAAG,CAAC,oBAAoBkI,SAAS,EAAE,CAAC;AACjD,CAAC;AAED,OAAO,MAAM0S,iBAAiB,GAAG,MAAO1S,SAAS,IAAK;EACpD,OAAOjJ,GAAG,CAACY,IAAI,CAAC,wBAAwBqI,SAAS,EAAE,CAAC;AACtD,CAAC;AAAC,IAAA8B,EAAA;AAAA6Q,YAAA,CAAA7Q,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}