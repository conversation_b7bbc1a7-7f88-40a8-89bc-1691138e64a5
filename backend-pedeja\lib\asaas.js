const fetch = require('node-fetch');
require('dotenv').config();
const Planos = require('../models/Planos')
const Assinatura = require('../models/Assinatura')

const apiToken = process.env.API_KEY_ASAAS;
const baseUrl = process.env.PROD_URL_ASAAS;

async function criarRegistroClienteAsaas(
    nome_cliente,
    cpf_cnpj,
    email_cliente,
    phone,
    addressNumber,
    postalCode
) {
    try {
        const createUrl = `${baseUrl}/customers`;
        const createOptions = {
            method: "POST",
            headers: {
                accept: "application/json",
                "Content-Type": "application/json",
                access_token: apiToken,
            },
            body: JSON.stringify({
                name: nome_cliente,
                cpfCnpj: cpf_cnpj,
                email: email_cliente,
                mobilePhone: phone,
                addressNumber: addressNumber,
                postalCode: postalCode,
            }),
        };

        // Log do corpo da requisição
        console.log("Corpo da requisição enviada ao Asaas:", createOptions.body);

        const createResponse = await fetch(createUrl, createOptions);

        // Log do status da resposta
        console.log("Status da resposta da API do Asaas:", createResponse.status);

        // Log do cabeçalho da resposta
        console.log("Headers da resposta da API do Asaas:", createResponse.headers.raw());

        // Verifica se a resposta foi bem-sucedida
        if (!createResponse.ok) {
            const errorMessage = await createResponse.text(); // Obter a resposta como texto
            console.error("Erro na resposta da API do Asaas:", errorMessage);
            throw new Error(
                `Erro ao criar cliente no Asaas: ${createResponse.status} - ${errorMessage}`
            );
        }

        // Log da resposta bruta
        const responseText = await createResponse.text();
        console.log("Resposta bruta da API do Asaas:", responseText);

        // Tente processar como JSON e trate possíveis erros
        try {
            const createJson = JSON.parse(responseText);
            console.log("Resposta processada como JSON:", createJson);
            return createJson;
        } catch (error) {
            console.error("Erro ao processar resposta da API do Asaas como JSON:", error.message);
            throw new Error("Erro ao processar resposta da API do Asaas: Resposta inválida.");
        }
    } catch (err) {
        console.error("Erro na criação do cliente no Asaas:", err.message);
        throw new Error("Erro ao criar cliente no Asaas: " + err.message);
    }
}

async function updateCustomerAsaas(customerId, name, cpfCnpj, email, mobilePhone, addressNumber, postalCode) {
    const url = `${baseUrl}/customers/${customerId}`;
    const options = {
        method: "PUT",
        headers: {
            accept: "application/json",
            "Content-Type": "application/json",
            access_token: apiToken,
        },
        body: JSON.stringify({
            name,
            cpfCnpj,
            email,
            mobilePhone,
            addressNumber,
            postalCode,
        }),
    };

    try {
        const response = await fetch(url, options);
        if (!response.ok) {
            const errorMessage = await response.text();
            throw new Error(
                `Erro ao atualizar cliente no Asaas: ${response.status} - ${errorMessage}`
            );
        }

        const updatedCustomer = await response.json();
        console.log("Cliente atualizado no Asaas:", updatedCustomer);
        return updatedCustomer;
    } catch (error) {
        console.error("Erro ao atualizar cliente no Asaas:", error.message);
        throw new Error("Erro ao atualizar cliente no Asaas: " + error.message);
    }
}


async function getAsaasPlans() {
    try {
        const planos = await Planos.find(); // Obtém todos os planos
    
        if (planos && planos.length > 0) {
          return { planos };
        } else {
          return { msg: 'Nenhum plano encontrado!' };
        }
    } catch (error) {
        console.error('Erro ao obter os planos:', error);
        return { msg: 'Aconteceu um erro no servidor, tente novamente mais tarde!' };
    }
}

async function deleteAsaasPlan(planId) {
  
    try {
      // Excluir o plano do MongoDB usando o campo id_iugu
      const planoDeletado = await Planos.findByIdAndDelete(planId);
                
      if (!planoDeletado) {
          return { msg: 'Plano não encontrado no MongoDB' };
      }
      return { msg: 'Plano removido com sucesso' };
    } catch (error) {
      console.error('Erro ao remover o plano:', error);
      throw error;
    }
}

async function createAsaasSubscription(
    billingType,
    cycle,
    customerId,
    value,
    nextDueDate,
    description,
    externalReference,
    maxPayments,
    isPromotional
) {
    try {
        const createUrl = `${baseUrl}/subscriptions`;

        // Definir o objeto de requisição
        let requestBody = {
            billingType,
            cycle,
            customer: customerId,
            value,
            nextDueDate,
            description,
            externalReference,
            maxPayments,
        };

        // Se for um plano promocional (plano_starter) aplicamos o desconto de R$142
        if (isPromotional && externalReference === "plano_starter") {
            requestBody.discount = {
                value: 142,
                type: "FIXED",
            };
        }

        const createOptions = {
            method: "POST",
            headers: {
                accept: "application/json",
                "Content-Type": "application/json",
                access_token: apiToken,
            },
            body: JSON.stringify(requestBody),
        };

        const createResponse = await fetch(createUrl, createOptions);
        if (!createResponse.ok) {
            throw new Error(
                `Erro ao criar assinatura: ${createResponse.status} - ${createResponse.statusText}`
            );
        }
        const createJson = await createResponse.json();
        return createJson;
    } catch (err) {
        throw new Error("Erro na criação da assinatura: " + err.message);
    }
}

async function removeDiscountFromSubscription(subscriptionId) {
    try {
        const updateUrl = `${baseUrl}/subscriptions/${subscriptionId}`;
        const updateOptions = {
            method: "PUT",
            headers: {
                accept: "application/json",
                "Content-Type": "application/json",
                access_token: apiToken,
            },
            body: JSON.stringify({
                discount: {
                    value: 0,
                },
            }),
        };

        const updateResponse = await fetch(updateUrl, updateOptions);
        if (!updateResponse.ok) {
            throw new Error(
                `Erro ao remover desconto: ${updateResponse.status} - ${updateResponse.statusText}`
            );
        }

        const updateJson = await updateResponse.json();
        return updateJson;
    } catch (err) {
        throw new Error("Erro ao remover desconto da assinatura: " + err.message);
    }
}

async function updateSubscriptionAsaas(subscriptionId, status) {
    const url = `${baseUrl}/subscriptions/${subscriptionId}`;
    const options = {
        method: "PUT",
        headers: {
            accept: "application/json",
            "Content-Type": "application/json",
            access_token: apiToken,
        },
        body: JSON.stringify({
            status: status,
        }),
    };

    try {
        const response = await fetch(url, options);
        if (!response.ok) {
            const errorMessage = await response.text();
            throw new Error(
                `Erro ao atualizar assinatura no Asaas: ${response.status} - ${errorMessage}`
            );
        }

        const updatedSubscription = await response.json();
        console.log("Assinatura atualizada no Asaas:", updatedSubscription);
        return updatedSubscription;
    } catch (error) {
        console.error("Erro ao atualizar assinatura no Asaas:", error.message);
        throw new Error("Erro ao atualizar assinatura no Asaas: " + error.message);
    }
}

async function getLastPendingInvoice(subscriptionId) {
    const url = `${baseUrl}/subscriptions/${subscriptionId}/payments?status=PENDING`;
    const options = {
        method: "GET",
        headers: {
            accept: "application/json",
            access_token: apiToken,
        },
    };

    try {
        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(
                `Erro ao obter cobranças pendentes: ${response.status} - ${response.statusText}`
            );
        }

        const data = await response.json();

        // Verificar se existem cobranças pendentes
        if (data.totalCount > 0 && data.data[0].invoiceUrl) {
            //const invoiceUrl = data.data[0].invoiceUrl;
            //const cobrancaId = data.data[0].id;
            const lastInvoice = data.data[0];
            return { lastInvoice }; // Retorna a URL do primeiro pagamento pendente
        } else {
            throw new Error("Nenhuma cobrança pendente encontrada ou sem URL de boleto.");
        }
    } catch (err) {
        throw new Error("Erro ao buscar invoiceUrl: " + err.message);
    }
}

async function getAllInvoices(subscriptionId) {
    const url = `${baseUrl}/subscriptions/${subscriptionId}/payments`;
    const options = {
        method: "GET",
        headers: {
            accept: "application/json",
            access_token: apiToken,
        },
    };

    try {
        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(
                `Erro ao obter todas as cobranças: ${response.status} - ${response.statusText}`
            );
        }

        const data = await response.json();

        // Verificar se existem cobranças
        if (data.totalCount > 0) {
            return data.data; // Retorna todas as cobranças como um array
        } else {
            throw new Error("Nenhuma cobrança encontrada para esta assinatura.");
        }
    } catch (err) {
        throw new Error("Erro ao buscar cobranças: " + err.message);
    }
}

// Função para excluir uma cobrança no Asaas
async function cancelAsaasInvoice(paymentId) {
    const url = `${baseUrl}/payments/${paymentId}`;
    const options = {
        method: "DELETE",
        headers: {
            accept: "application/json",
            access_token: apiToken,
        },
    };

    try {
        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(
                `Erro ao cancelar cobrança: ${response.status} - ${response.statusText}`
            );
        }
        console.log(`Cobrança ${paymentId} cancelada com sucesso.`);
        return true;
    } catch (error) {
        console.error("Erro ao cancelar a cobrança:", error.message);
        return false;
    }
}

// Função para excluir uma assinatura no Asaas
async function deleteAsaasSubscription(subscriptionId) {
    const url = `${baseUrl}/subscriptions/${subscriptionId}`;
    const options = {
        method: "DELETE",
        headers: {
            accept: "application/json",
            access_token: apiToken,
        },
    };

    try {
        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(
                `Erro ao excluir assinatura: ${response.status} - ${response.statusText}`
            );
        }
        console.log(`Assinatura ${subscriptionId} excluída com sucesso.`);
        return true;
    } catch (error) {
        console.error("Erro ao excluir a assinatura:", error.message);
        return false;
    }
}

async function checkUserInvoices(customerId) {
    try {
        // Busca a assinatura correspondente ao customerId no banco de dados
        const assinatura = await Assinatura.findOne({ "assinatura_obj.customer": customerId });
    
        if (!assinatura) {
            console.warn(`Assinatura não encontrada para o customerId: ${customerId}`);
            return false;
        }
    
        // Verifica se há assinatura ativa
        const { status } = assinatura.assinatura_obj;
        const hasActiveSubscription = status==="ACTIVE" ? true : false;
    
        //if (!recent_invoices || recent_invoices.length === 0) {
        //  throw new Error('Nenhuma fatura encontrada para este cliente');
        //}
    
        // Verifica se há alguma fatura com status 'pending' ou 'paid'
        //const hasActiveInvoice = recent_invoices.some(invoice =>
        //  invoice.status === 'pending' || invoice.status === 'paid'
        //);
    
        return hasActiveSubscription;
    } catch (error) {
        console.error('Erro ao verificar as faturas no banco de dados:', error);
        throw new Error('Erro ao verificar as faturas no MongoDB');
    }
}

async function getCustomerDataAsaas(customerId) {
    const url = `${baseUrl}/customers/${customerId}`;
    const options = {
        method: "GET",
        headers: {
            accept: "application/json",
            access_token: apiToken,
        },
    };

    try {
        const response = await fetch(url, options);

        // Verifica se a resposta foi bem-sucedida
        if (!response.ok) {
            throw new Error(`Erro na requisição: ${response.statusText}`);
        }

        const customerData = await response.json();
        return customerData; // Retorna os dados do cliente
    } catch (error) {
        throw new Error(`Erro ao obter os dados do cliente: ${error.message}`);
    }
}

// Função para lidar com eventos do tipo SUBSCRIPTION_DELETED
async function handleSubscriptionDeleted(data) {
    const subscriptionId = data.subscription.id;
    // Busca a assinatura no banco de dados pelo ID da assinatura
    const existingSubscription = await Assinatura.findOne({
        'assinatura_obj.id': subscriptionId,
    });

    if (existingSubscription) {
        // Exclui a assinatura do banco de dados
        await Assinatura.deleteOne({ _id: existingSubscription._id });
        console.log(`Assinatura ${subscriptionId} excluída com sucesso no banco de dados.`);
        return { status: 200, message: 'Assinatura excluída com sucesso.' };
    } else {
        console.warn(`Assinatura ${subscriptionId} não encontrada no banco de dados.`);
        return { status: 404, message: 'Assinatura não encontrada.' };
    }
}

async function handlePaymentOverdue(data) {
    try {
        const subscriptionId = data.payment.subscription;

        if (!subscriptionId) {
            throw new Error("ID da assinatura não encontrado no evento.");
        }

        console.log(`Atualizando assinatura ${subscriptionId} para status INACTIVE...`);

        // Atualizar a assinatura no Asaas
        const updatedSubscription = await updateSubscriptionAsaas(subscriptionId, "INACTIVE");

        // Atualizar a assinatura no banco de dados
        const updatedAssinatura = await Assinatura.findOneAndUpdate(
            { "assinatura_obj.id": subscriptionId }, // Localiza pelo ID da assinatura
            { assinatura_obj: updatedSubscription }, // Substitui pelo novo objeto
            { new: true } // Retorna o documento atualizado
        );

        if (updatedAssinatura) {
            console.log(`Assinatura ${subscriptionId} atualizada com sucesso no banco de dados.`);
            return { status: 200, message: "Assinatura atualizada com sucesso." };
        } else {
            console.warn(`Assinatura ${subscriptionId} não encontrada no banco de dados.`);
            return { status: 404, message: "Assinatura não encontrada no banco de dados." };
        }
    } catch (error) {
        console.error("Erro ao processar evento PAYMENT_OVERDUE:", error.message);
        return { status: 500, message: "Erro ao processar evento PAYMENT_OVERDUE." };
    }
}

async function handlePaymentReceived(data) {
    try {
        const subscriptionId = data.payment.subscription;

        if (!subscriptionId) {
            console.warn("ID da assinatura não encontrado no evento. Cobrança sem assinatura.");
            return {
                status: 200,
                message: "ID da assinatura não encontrado no evento. Cobrança sem assinatura.",
            };
        }

        console.log(`Verificando cobranças da assinatura ${subscriptionId}...`);

        // Obtém todas as cobranças da assinatura
        const allInvoices = await getAllInvoices(subscriptionId);

        if (!allInvoices || allInvoices.length === 0) {
            return { status: 404, message: "Nenhuma cobrança encontrada para esta assinatura." };
        }

        // Verifica se há cobranças vencidas
        const overdueInvoices = allInvoices.filter((invoice) => invoice.status === "OVERDUE");

        if (overdueInvoices.length > 0) {
            console.log(`Ainda existem ${overdueInvoices.length} cobranças vencidas.`);
            return {
                status: 200,
                message: "Pagamento recebido, mas ainda há cobranças vencidas.",
            };
        }

        console.log("Nenhuma cobrança vencida restante. Atualizando assinatura...");

        // Atualizar a assinatura no Asaas para ACTIVE
        const updatedSubscription = await updateSubscriptionAsaas(subscriptionId, "ACTIVE");

        // Atualizar a assinatura no banco de dados
        const updatedAssinatura = await Assinatura.findOneAndUpdate(
            { "assinatura_obj.id": subscriptionId }, // Localiza pelo ID da assinatura
            { assinatura_obj: updatedSubscription }, // Substitui pelo novo objeto
            { new: true } // Retorna o documento atualizado
        );

        if (updatedAssinatura) {
            console.log(`Assinatura ${subscriptionId} atualizada com sucesso no banco de dados.`);
            return { status: 200, message: "Assinatura atualizada para ACTIVE com sucesso." };
        } else {
            console.warn(`Assinatura ${subscriptionId} não encontrada no banco de dados.`);
            return { status: 404, message: "Assinatura não encontrada no banco de dados." };
        }
    } catch (error) {
        console.error("Erro ao processar evento PAYMENT_RECEIVED:", error.message);
        return { status: 500, message: "Erro ao processar evento PAYMENT_RECEIVED." };
    }
}
  

module.exports = { 
    criarRegistroClienteAsaas,
    updateCustomerAsaas,
    getAsaasPlans,
    deleteAsaasPlan,
    createAsaasSubscription,
    getLastPendingInvoice,
    getAllInvoices,
    cancelAsaasInvoice,
    deleteAsaasSubscription,
    getCustomerDataAsaas,
    checkUserInvoices,
    handleSubscriptionDeleted,
    handlePaymentOverdue,
    handlePaymentReceived,
    removeDiscountFromSubscription
};