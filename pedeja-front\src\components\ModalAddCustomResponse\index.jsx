import React from 'react';
import { Modal } from '../Modal';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import './styles.css';

const ModalAddCustomResponse = ({ isOpen, onClose, onSubmit }) => {
    const validationSchema = Yup.object({
        question: Yup.string()
            .required('Pergunta é obrigatória')
            .min(3, 'Pergunta deve ter pelo menos 3 caracteres')
            .max(100, 'Pergunta deve ter no máximo 100 caracteres'),
        response: Yup.string()
            .required('Resposta é obrigatória')
            .min(10, 'Resposta deve ter pelo menos 10 caracteres')
            .max(300, 'Resposta deve ter no máximo 300 caracteres')
    });

    const PREFIX_TEXT = "Quando perguntarem sobre: ";

    const initialValues = {
        question: '',
        response: ''
    };

    const handleSubmit = async (values, { setSubmitting, resetForm }) => {
        try {
            // Adicionar o prefixo antes de enviar
            const finalValues = {
                ...values,
                question: PREFIX_TEXT + values.question
            };
            await onSubmit(finalValues, { setSubmitting, resetForm });
        } catch (error) {
            setSubmitting(false);
        }
    };

    return (
        <Modal isOpen={isOpen} onClose={onClose}>
            <div className="modal-custom-response">
                <div className="modal-header">
                    <h2>Nova resposta personalizada</h2>
                    <p>Personalize as respostas do seu agente virtual para que ele possa responder perguntas das categorias abaixo baseado no exemplo fornecido.</p>
                </div>

                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                    enableReinitialize
                >
                    {({ isSubmitting, values, setFieldValue }) => (
                        <Form className="modal-form">
                            <div className="form-group">
                                <label htmlFor="question">Quando usar essa resposta</label>
                                <div className="question-input-container">
                                    <span className="question-prefix">{PREFIX_TEXT}</span>
                                    <Field
                                        as="textarea"
                                        id="question"
                                        name="question"
                                        placeholder="promoções, horário de funcionamento, formas de pagamento..."
                                        className="form-textarea question-field-with-prefix"
                                        rows="3"
                                    />
                                </div>
                                <ErrorMessage name="question" component="div" className="error-message" />
                            </div>

                            <div className="form-group">
                                <label htmlFor="response">Instruções para a Resposta</label>
                                <Field
                                    as="textarea"
                                    id="response"
                                    name="response"
                                    placeholder="As promoções são:&#10;Combinado para 1 pessoa por 59,90&#10;Combinado para 2 pessoas por 109,90"
                                    className="form-textarea response-field"
                                    rows="6"
                                />
                                <ErrorMessage name="response" component="div" className="error-message" />
                                <div className="char-counter">
                                    {values.response.length}/300
                                </div>
                            </div>

                            <div className="form-actions">
                                <button
                                    type="button"
                                    className="btn-cancel"
                                    onClick={onClose}
                                    disabled={isSubmitting}
                                >
                                    Cancelar
                                </button>
                                <button
                                    type="submit"
                                    className="btn-submit"
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? 'Adicionando...' : 'Adicionar'}
                                </button>
                            </div>
                        </Form>
                    )}
                </Formik>
            </div>
        </Modal>
    );
};

export default ModalAddCustomResponse;
