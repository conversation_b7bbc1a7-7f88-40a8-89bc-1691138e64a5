class PaymentMesaUseCase {
    constructor(mesaDAO, pedidosDAO){
        this.mesaDAO = mesaDAO
        this.pedidosDAO = pedidosDAO
    }

    async execute({ payment_type, total_payed, mesa_id, id_empresa, user_id, customer_name, customer_phone }) {
        try {
            const mesa = await this.mesaDAO.findMesaById({ mesa_id });

            if(!mesa){
                return {
                    status: false,
                    msg: "Mesa não encontrada"
                }
            }

            if(mesa.status === 'free'){
                return {
                    status: false,
                    msg: "Mesa não está ocupada, não é possível registrar pagamento."
                }
            }

            let newTotalPayed = (Number(mesa.total_payed) || 0) + total_payed;

            mesa.total_payed = newTotalPayed;

            if(mesa.total_payed >= mesa.total){
                for (let pedido of mesa.pedidos){
                    const currentPedido = await this.pedidosDAO.findPedidoById({ pedido_id: pedido.id_pedido._id });
                    currentPedido.tipo_pagamento = payment_type;
                    currentPedido.nome_cliente = customer_name;
                    currentPedido.celular_cliente = customer_phone;

                    await currentPedido.save();
                }

                const newMesa = await this.mesaDAO.resetMesa({ mesa_id });

                return {
                    status: true,
                    mesa: newMesa,
                }
            }

            mesa.status = 'pending';

            await mesa.save();

            return {
                status: true,
                mesa
            }
            
        } catch(error){
            console.error(error);
            return {
                status: false,
                msg: "Error ao registrar pedido"
            }
        }
    }
}

module.exports = PaymentMesaUseCase;