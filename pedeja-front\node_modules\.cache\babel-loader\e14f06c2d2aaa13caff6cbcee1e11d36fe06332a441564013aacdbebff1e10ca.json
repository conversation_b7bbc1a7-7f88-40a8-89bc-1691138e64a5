{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\ImportaCardapio\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport './style.css';\nimport PermissionGate from \"../../services/PermissionGate\";\nimport LeftMenu from \"../../components/LeftMenu\";\nimport styled from 'styled-components';\nimport QRCode from 'qrcode.react';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport { useFormik } from \"formik\";\nimport * as Yup from \"yup\";\nimport { importCardapioAnotaai, importCardapioIfood, apiUpdateImportFlag, apiCheckImportStatus, getProgressoImportacao } from '../../services/api';\nimport * as AiIcons from 'react-icons/ai';\nimport * as SlIcons from 'react-icons/sl';\nimport io from 'socket.io-client';\nimport { ProgressBar } from 'react-bootstrap';\nimport Loading from \"react-loading\";\nimport svgSuccess from \"../../img/check-symbol-success.svg\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Teste = styled.div`\n\n    display: flex;\n    margin-left:  ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'}; \n    height:auto;\n    width:auto;\n    transition: 150ms;\n    background-color:rgb(247,247,247)!important;\n    overflow: initial;\n    z-Index: 9;\n    \n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\n_c = Teste;\nconst ImportaCardapio = () => {\n  _s();\n  const {\n    sidebar,\n    setSidebar\n  } = useContext(SidebarContext);\n  ; //TESTANDO CONST NO AppROUTES\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const navigate = useNavigate();\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const objIdEmpresa = empresaParse._id;\n  const idEmpresa = empresaParse.id_empresa;\n  const [linkObj, setLinkObj] = useState('');\n  const [onIfoodAndAnotaai, setOnIfoodAndAnotaai] = useState(linkObj ? linkObj.onIfoodAndAnotaai : '');\n  const [importType, setImportType] = useState(linkObj ? linkObj.importType : '');\n  const [hasVideo, setHasVideo] = useState(false);\n  const [progress, setProgress] = useState(null);\n  const [importComplete, setImportComplete] = useState(false);\n  const [importMessage, setImportMessage] = useState('');\n  const [importStage, setImportStage] = useState('');\n  const [importDetails, setImportDetails] = useState({});\n  const [socket, setSocket] = useState(null);\n  const isDevelopment = window.location.hostname === 'localhost';\n  const apiUrl = isDevelopment ? process.env.REACT_APP_SERVER_URL_DEV : process.env.REACT_APP_SERVER_URL_PROD;\n  useEffect(() => {\n    // Conectar ao servidor WebSocket\n    const wsUrl = apiUrl;\n    const newSocket = io(wsUrl, {\n      withCredentials: true,\n      transports: ['websocket'],\n      auth: {\n        token: localStorage.getItem('token')\n      }\n    });\n\n    // Entrar na sala da empresa\n    newSocket.emit('joinCompanyRoom', {\n      companyId: objIdEmpresa.toString(),\n      clientId: 'importClient'\n    });\n\n    // Escutar eventos de progresso da importação\n    newSocket.on('import-progress', data => {\n      console.log(\"Progresso recebido:\", data);\n      setProgress(data.progress);\n      setImportMessage(data.message || '');\n      setImportStage(data.stage || '');\n      setImportDetails(data.details || {});\n      if (data.progress === 100 && data.stage === 'finalizado') {\n        setImportComplete(true);\n        updateImportFlag();\n      }\n      if (data.stage === 'erro') {\n        setProgress(null);\n        setImportMessage('');\n        setImportStage('');\n        console.error('Erro durante importação:', data.message);\n      }\n    });\n    setSocket(newSocket);\n\n    // Verificar status/progresso ao carregar\n    checkImportStatus();\n    checkImportProgress();\n\n    // Cleanup quando o componente desmontar\n    return () => {\n      newSocket.off('import-progress');\n      newSocket.disconnect();\n    };\n  }, []);\n\n  // Verificar progresso da importação salvo\n  const checkImportProgress = async () => {\n    try {\n      const response = await getProgressoImportacao(idEmpresa);\n      const {\n        importacao_finalizada,\n        progresso_importacao\n      } = response.data;\n      if (importacao_finalizada) {\n        setImportComplete(true);\n        setProgress(100);\n        setImportMessage('Importação finalizada com sucesso!');\n        setImportStage('finalizado');\n      } else if (progresso_importacao && progresso_importacao.em_andamento) {\n        setProgress(progresso_importacao.porcentagem);\n        setImportMessage(progresso_importacao.etapa_atual);\n        setImportStage('em_andamento');\n      }\n    } catch (error) {\n      console.error(\"Erro ao verificar progresso da importação:\", error);\n    }\n  };\n\n  // Refatorando a função checkImportStatus\n  const checkImportStatus = async () => {\n    try {\n      const response = await apiCheckImportStatus(idEmpresa);\n      const data = response.data; // assumindo que está usando axios, onde a resposta está no campo `data`\n\n      if (data.importacao_finalizada) {\n        setImportComplete(true);\n      }\n    } catch (error) {\n      console.error(\"Erro ao verificar status de importação:\", error);\n    }\n  };\n  const updateImportFlag = async () => {\n    try {\n      const importacao_finalizada = true;\n      const response = await apiUpdateImportFlag(idEmpresa, importacao_finalizada);\n    } catch (error) {\n      console.error(\"Erro ao atualizar flag de importação:\", error);\n    }\n  };\n  const handleBack = () => {\n    //setSidebar(!sidebar)\n    navigate(\"/\");\n  };\n\n  //Validações\n  const SignUpSchema = Yup.object().shape({\n    ifoodLink: importType === 'linkIfood' && Yup.string().required('Campo obrigatório'),\n    anotaAiLink: importType === 'linkAnotaAi' && Yup.string().required('Campo obrigatório')\n  });\n  const formik = useFormik({\n    validationSchema: SignUpSchema,\n    validateOnBlur: false,\n    validateOnChange: false,\n    //enableReinitialize: true,\n    initialValues: {\n      ifoodLink: linkObj && linkObj.importType === \"linkIfood\" ? linkObj.importLink : \"\",\n      anotaAiLink: linkObj && linkObj.importType === \"linkAnotaAi\" ? linkObj.importLink : \"\"\n    },\n    onSubmit: values => {\n      //const password = values.password;\n      //const confirmPassword = values.confirmPassword;\n      const ifoodLink = values.ifoodLink;\n      const anotaAiLink = values.anotaAiLink;\n      var linkObj = {};\n      //handleSubmitAPI();   \n      if (importType === 'linkIfood') {\n        //importaCardapioIfood(ifoodLink)\n        linkObj = {\n          onIfoodAndAnotaai: onIfoodAndAnotaai,\n          importType: 'linkIfood',\n          importLink: ifoodLink\n        };\n        //importCardapioIfood(ifoodLink, 2);\n        //navigate(\"/endereco\", { state: { infoEmpresaObj, passwordObj, linkObj } });\n      } else if (importType === 'linkAnotaAi') {\n        //importaCardapioAnotaai(anotaAiLink)\n        linkObj = {\n          onIfoodAndAnotaai: onIfoodAndAnotaai,\n          importType: 'linkAnotaAi',\n          importLink: anotaAiLink\n        };\n        //importCardapioAnotaai(anotaAiLink, 2);\n        //navigate(\"/endereco\", { state: { infoEmpresaObj, passwordObj, linkObj } });\n      }\n      handleSubmitAPI(linkObj);\n    }\n  });\n  const handleSubmitAPI = async linkObj => {\n    if (linkObj) {\n      console.log(\"linkObj>\", linkObj);\n      try {\n        setIsSubmitting(true);\n        if (linkObj.importType === 'linkIfood') {\n          console.log(\"Caiu no import Ifood\");\n          const response = await importCardapioIfood(linkObj.importLink, idEmpresa);\n          console.log(\"RESPOSTA Ifood:\", response);\n        } else if (linkObj.importType === 'linkAnotaAi') {\n          console.log(\"Caiu no import AnotaAi\");\n          const response = await importCardapioAnotaai(linkObj.importLink, idEmpresa);\n          console.log(\"RESPOSTA AnotaAi:\", response);\n        }\n      } catch (error) {\n        console.error(\"Erro durante o import:\", error);\n      } finally {\n        setIsSubmitting(false);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: /*#__PURE__*/_jsxDEV(Teste, {\n        sidebar: sidebar,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"m-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"containerAddEmpresaForm\",\n            style: {\n              marginRight: 70,\n              minHeight: \"75vh\"\n            },\n            children: importComplete ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 20\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Importa\\xE7\\xE3o do Card\\xE1pio conclu\\xEDda!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                height: 200,\n                src: svgSuccess\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 33\n            }, this) : progress !== null && !importComplete || importStage === 'em_andamento' ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"import-progress\",\n              style: {\n                minWidth: 500,\n                padding: '20px',\n                backgroundColor: '#f8f9fa',\n                borderRadius: '10px',\n                margin: '20px 0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-info\",\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  marginBottom: '10px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"progress-message\",\n                  style: {\n                    fontWeight: 'bold',\n                    color: '#495057'\n                  },\n                  children: importMessage || 'Importação do cardápio em andamento'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"progress-percentage\",\n                  style: {\n                    fontWeight: 'bold',\n                    color: '#28a745'\n                  },\n                  children: progress !== null ? `${progress}%` : '0%'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n                now: progress || 0,\n                animated: progress < 100,\n                variant: \"success\",\n                style: {\n                  fontSize: 19,\n                  height: 30,\n                  marginBottom: '15px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 37\n              }, this), importDetails && importStage === 'categorias' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-details\",\n                style: {\n                  fontSize: '14px',\n                  color: '#6c757d'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"\\uD83D\\uDCC2 Processando categoria: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: importDetails.categoria_atual\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 75\n                  }, this), \"(\", importDetails.categorias_processadas, \"/\", importDetails.total_categorias, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 41\n              }, this), importDetails && importStage === 'adicionais' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-details\",\n                style: {\n                  fontSize: '14px',\n                  color: '#6c757d'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"\\uD83D\\uDEE0\\uFE0F Processando grupo de adicionais: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: importDetails.grupo_atual\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 86\n                  }, this), \"(\", importDetails.grupos_processados, \"/\", importDetails.total_grupos, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 41\n              }, this), importDetails && importStage === 'inicio' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-details\",\n                style: {\n                  fontSize: '14px',\n                  color: '#6c757d'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"\\uD83D\\uDE80 Preparando importa\\xE7\\xE3o...\", importDetails.total_categorias && /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"(\", importDetails.total_categorias, \" categorias, \", importDetails.total_itens, \" itens, \", importDetails.total_adicionais, \" adicionais)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 41\n              }, this), importStage === 'finalizado' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-details\",\n                style: {\n                  fontSize: '14px',\n                  color: '#28a745',\n                  fontWeight: 'bold'\n                },\n                children: \"\\u2705 Importa\\xE7\\xE3o conclu\\xEDda com sucesso!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: formik.handleSubmit,\n              className: \"formAddEmpresa\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"informationsAccount\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontFamily: 'Arial, sans-serif',\n                    padding: '20px',\n                    backgroundColor: 'white',\n                    color: '#333',\n                    margin: 'auto'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Seu estabelecimento est\\xE1 cadastrado no iFood ou AnotaAi?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Selecione 'Sim' para sincronizar automaticamente seu card\\xE1pio existente e evitar o cadastro manual.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      gap: '10px',\n                      marginBottom: '20px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: onIfoodAndAnotaai === 'sim' ? 'btnFwdCadastros' : 'btnBackCadastros',\n                      onClick: () => setOnIfoodAndAnotaai('sim'),\n                      style: {\n                        width: '50%'\n                      },\n                      children: \"Sim\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 41\n                  }, this), onIfoodAndAnotaai === 'sim' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        children: \"Como voc\\xEA deseja importar seu card\\xE1pio?\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 324,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          flexDirection: 'column',\n                          gap: '10px',\n                          marginBottom: '20px'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          type: \"button\",\n                          className: importType === 'linkIfood' ? 'btnFwdCadastros' : 'btnBackCadastros',\n                          style: {\n                            width: '100%'\n                          },\n                          onClick: () => setImportType('linkIfood'),\n                          children: \"Pelo link do meu card\\xE1pio IFood\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 326,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          type: \"button\",\n                          className: importType === 'linkAnotaAi' ? 'btnFwdCadastros' : 'btnBackCadastros',\n                          style: {\n                            width: '100%'\n                          },\n                          onClick: () => setImportType('linkAnotaAi'),\n                          children: \"Pelo link do meu card\\xE1pio AnotaAi\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 331,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 325,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 53\n                    }, this), importType === 'linkIfood' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        children: \"Para finalizar, cole o link do seu card\\xE1pio iFood abaixo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 342,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          marginBottom: '5px',\n                          textDecoration: 'underline',\n                          color: '#007bff',\n                          cursor: 'pointer'\n                        },\n                        onClick: () => setHasVideo(true),\n                        children: \"Onde consigo o link da minha loja do iFood?\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 343,\n                        columnNumber: 65\n                      }, this), hasVideo ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            marginBottom: '20px'\n                          },\n                          children: \"Aprenda como copiar o link do seu card\\xE1pio iFood!\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 346,\n                          columnNumber: 73\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            marginBottom: '20px',\n                            display: 'flex',\n                            justifyContent: 'center'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n                            width: \"560\",\n                            height: \"315\",\n                            src: \"https://www.youtube.com/embed/example\",\n                            allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                            allowFullScreen: true\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 348,\n                            columnNumber: 77\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 347,\n                          columnNumber: 73\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"btnBackCadastros\",\n                          style: {\n                            width: '100%',\n                            marginBottom: '10px'\n                          },\n                          onClick: () => setHasVideo(false),\n                          children: \"Fechar v\\xEDdeo\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 350,\n                          columnNumber: 73\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 69\n                      }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          flexDirection: 'column',\n                          gap: '10px',\n                          marginBottom: '20px'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                          htmlFor: \"ifoodLink\",\n                          children: \"Link do card\\xE1pio iFood *\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 355,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"text\",\n                          id: \"ifoodLink\",\n                          name: \"ifoodLink\",\n                          value: formik.values.ifoodLink,\n                          onChange: formik.handleChange,\n                          style: {\n                            padding: '10px',\n                            border: '1px solid #ccc'\n                          },\n                          className: \"linkToImportInput\",\n                          placeholder: \"https://www.ifood.com.br/delivery/cidade-uf/loja-exemplo-setor-exemplo/...\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 356,\n                          columnNumber: 69\n                        }, this), formik.errors.ifoodLink && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"error\",\n                          children: formik.errors.ifoodLink\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 365,\n                          columnNumber: 73\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          gap: '10px'\n                        },\n                        children: isSubmitting ? /*#__PURE__*/_jsxDEV(\"button\", {\n                          type: \"button\",\n                          disabled: true,\n                          className: \"btnFwdCadastros\",\n                          style: {\n                            width: '50%'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(Loading, {\n                            type: \"spin\",\n                            color: \"white\",\n                            height: 30,\n                            width: 30,\n                            style: {\n                              marginLeft: '45%'\n                            },\n                            className: \"zIndexForLoadingSaveButton\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 371,\n                            columnNumber: 77\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 370,\n                          columnNumber: 73\n                        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                          type: \"submit\",\n                          className: \"btnFwdCadastros\",\n                          style: {\n                            width: '50%'\n                          },\n                          children: \"Importar\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 375,\n                          columnNumber: 73\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 368,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 61\n                    }, this) : importType === 'linkAnotaAi' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        children: \"Para finalizar, cole o link do seu card\\xE1pio AnotaAi abaixo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 381,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          marginBottom: '5px',\n                          textDecoration: 'underline',\n                          color: '#007bff',\n                          cursor: 'pointer'\n                        },\n                        onClick: () => setHasVideo(true),\n                        children: \"Onde consigo o link da minha loja do AnotaAi?\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 382,\n                        columnNumber: 65\n                      }, this), hasVideo ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            marginBottom: '20px'\n                          },\n                          children: \"Aprenda como copiar o link do seu card\\xE1pio AnotaAi!\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 385,\n                          columnNumber: 73\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            marginBottom: '20px',\n                            display: 'flex',\n                            justifyContent: 'center'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n                            width: \"560\",\n                            height: \"315\",\n                            src: \"https://www.youtube.com/embed/example\",\n                            allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                            allowFullScreen: true\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 387,\n                            columnNumber: 77\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 386,\n                          columnNumber: 73\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"btnBackCadastros\",\n                          style: {\n                            width: '100%',\n                            marginBottom: '10px'\n                          },\n                          onClick: () => setHasVideo(false),\n                          children: \"Fechar v\\xEDdeo\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 389,\n                          columnNumber: 73\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 384,\n                        columnNumber: 69\n                      }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          flexDirection: 'column',\n                          gap: '10px',\n                          marginBottom: '20px'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                          htmlFor: \"anotaAiLink\",\n                          children: \"Link do card\\xE1pio AnotaAi *\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 394,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"text\",\n                          id: \"anotaAiLink\",\n                          name: \"anotaAiLink\",\n                          value: formik.values.anotaAiLink,\n                          onChange: formik.handleChange,\n                          style: {\n                            padding: '10px',\n                            border: '1px solid #ccc'\n                          },\n                          className: \"linkToImportInput\",\n                          placeholder: \"https://pedido.anota.ai/loja/nome-da-loja-1?f=msa\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 395,\n                          columnNumber: 69\n                        }, this), formik.errors.anotaAiLink && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"error\",\n                          children: formik.errors.anotaAiLink\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 404,\n                          columnNumber: 73\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          gap: '10px'\n                        },\n                        children: isSubmitting ? /*#__PURE__*/_jsxDEV(\"button\", {\n                          type: \"button\",\n                          disabled: true,\n                          className: \"btnFwdCadastros\",\n                          style: {\n                            width: '50%'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(Loading, {\n                            type: \"spin\",\n                            color: \"white\",\n                            height: 30,\n                            width: 30,\n                            style: {\n                              marginLeft: '45%'\n                            },\n                            className: \"zIndexForLoadingSaveButton\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 411,\n                            columnNumber: 77\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 410,\n                          columnNumber: 73\n                        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                          type: \"submit\",\n                          className: \"btnFwdCadastros\",\n                          style: {\n                            width: '50%'\n                          },\n                          children: \"Importar\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 415,\n                          columnNumber: 73\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 407,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 380,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 49\n                  }, this) : null]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(ImportaCardapio, \"EPKQugZujH+PtyV27kG8T/13ars=\", false, function () {\n  return [useNavigate, useFormik];\n});\n_c2 = ImportaCardapio;\nexport default ImportaCardapio;\nvar _c, _c2;\n$RefreshReg$(_c, \"Teste\");\n$RefreshReg$(_c2, \"ImportaCardapio\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "useNavigate", "PermissionGate", "LeftMenu", "styled", "QRCode", "SidebarContext", "useFormik", "<PERSON><PERSON>", "importCardapioAnotaai", "importCardapioIfood", "apiUpdateImportFlag", "apiCheckImportStatus", "getProgressoImportacao", "AiIcons", "SlIcons", "io", "ProgressBar", "Loading", "svgSuccess", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON>e", "div", "sidebar", "_c", "ImportaCardapio", "_s", "setSidebar", "isSubmitting", "setIsSubmitting", "navigate", "empresa", "localStorage", "getItem", "empresaParse", "JSON", "parse", "objIdEmpresa", "_id", "idEmpresa", "id_empresa", "linkObj", "setLinkObj", "onIfoodAndAnotaai", "setOnIfoodAndAnotaai", "importType", "setImportType", "hasVideo", "setHasVideo", "progress", "setProgress", "importComplete", "setImportComplete", "importMessage", "setImportMessage", "importStage", "setImportStage", "importDetails", "setImportDetails", "socket", "setSocket", "isDevelopment", "window", "location", "hostname", "apiUrl", "process", "env", "REACT_APP_SERVER_URL_DEV", "REACT_APP_SERVER_URL_PROD", "wsUrl", "newSocket", "withCredentials", "transports", "auth", "token", "emit", "companyId", "toString", "clientId", "on", "data", "console", "log", "message", "stage", "details", "updateImportFlag", "error", "checkImportStatus", "checkImportProgress", "off", "disconnect", "response", "importacao_finalizada", "progresso_importacao", "em_andamento", "porcentagem", "etapa_atual", "handleBack", "SignUpSchema", "object", "shape", "ifoodLink", "string", "required", "anotaAiLink", "formik", "validationSchema", "validateOnBlur", "validateOnChange", "initialValues", "importLink", "onSubmit", "values", "handleSubmitAPI", "children", "permissions", "className", "style", "marginRight", "minHeight", "display", "flexDirection", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "height", "src", "min<PERSON><PERSON><PERSON>", "padding", "backgroundColor", "borderRadius", "margin", "justifyContent", "marginBottom", "fontWeight", "color", "now", "animated", "variant", "fontSize", "categoria_atual", "categorias_processadas", "total_categorias", "grupo_atual", "grupos_processados", "total_grupos", "total_itens", "total_adicionais", "handleSubmit", "fontFamily", "type", "onClick", "width", "textDecoration", "cursor", "allow", "allowFullScreen", "htmlFor", "id", "name", "value", "onChange", "handleChange", "border", "placeholder", "errors", "disabled", "marginLeft", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/ImportaCardapio/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport './style.css'\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\nimport styled from 'styled-components';\r\nimport QRCode from 'qrcode.react';\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport { useFormik } from \"formik\";\r\nimport * as Yup from \"yup\";\r\nimport { importCardapioAnotaai, importCardapioIfood, apiUpdateImportFlag, apiCheckImportStatus, getProgressoImportacao } from '../../services/api'; \r\nimport * as AiIcons from 'react-icons/ai'\r\nimport * as SlIcons from 'react-icons/sl'\r\nimport io from 'socket.io-client';\r\nimport { ProgressBar } from 'react-bootstrap';\r\nimport Loading from \"react-loading\";\r\nimport svgSuccess from \"../../img/check-symbol-success.svg\"\r\n\r\nconst Teste = styled.div`\r\n\r\n    display: flex;\r\n    margin-left:  ${({ sidebar }) => (sidebar ? '250px' : '100px')}; \r\n    height:auto;\r\n    width:auto;\r\n    transition: 150ms;\r\n    background-color:rgb(247,247,247)!important;\r\n    overflow: initial;\r\n    z-Index: 9;\r\n    \r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\n\r\nconst ImportaCardapio = () => {\r\n\r\n    const { sidebar, setSidebar } = useContext(SidebarContext);; //TESTANDO CONST NO AppROUTES\r\n    const [isSubmitting, setIsSubmitting] = useState(false);\r\n    const navigate = useNavigate();\r\n\r\n    const empresa = localStorage.getItem('empresa')\r\n    const empresaParse = JSON.parse(empresa)\r\n    const objIdEmpresa = empresaParse._id;\r\n    const idEmpresa = empresaParse.id_empresa;\r\n\r\n    const [linkObj, setLinkObj] = useState('');\r\n\r\n    const [onIfoodAndAnotaai, setOnIfoodAndAnotaai] = useState(linkObj?linkObj.onIfoodAndAnotaai:'');\r\n    const [importType, setImportType] = useState(linkObj?linkObj.importType:'');\r\n    const [hasVideo, setHasVideo] = useState(false);\r\n\r\n    const [progress, setProgress] = useState(null);\r\n    const [importComplete, setImportComplete] = useState(false);\r\n    const [importMessage, setImportMessage] = useState('');\r\n    const [importStage, setImportStage] = useState('');\r\n    const [importDetails, setImportDetails] = useState({});\r\n    const [socket, setSocket] = useState(null);\r\n\r\n    const isDevelopment = window.location.hostname === 'localhost';\r\n    const apiUrl = isDevelopment \r\n    ? process.env.REACT_APP_SERVER_URL_DEV \r\n    : process.env.REACT_APP_SERVER_URL_PROD;\r\n\r\n    useEffect(() => {\r\n        // Conectar ao servidor WebSocket\r\n        const wsUrl = apiUrl;\r\n        const newSocket = io(wsUrl, {\r\n            withCredentials: true, \r\n            transports: ['websocket'], \r\n            auth: { token: localStorage.getItem('token') }\r\n        });\r\n        \r\n        // Entrar na sala da empresa\r\n        newSocket.emit('joinCompanyRoom', { \r\n            companyId: objIdEmpresa.toString(), \r\n            clientId: 'importClient' \r\n        });\r\n\r\n        // Escutar eventos de progresso da importação\r\n        newSocket.on('import-progress', (data) => {\r\n            console.log(\"Progresso recebido:\", data);\r\n            setProgress(data.progress);\r\n            setImportMessage(data.message || '');\r\n            setImportStage(data.stage || '');\r\n            setImportDetails(data.details || {});\r\n            \r\n            if (data.progress === 100 && data.stage === 'finalizado') {\r\n                setImportComplete(true);\r\n                updateImportFlag();\r\n            }\r\n            \r\n            if (data.stage === 'erro') {\r\n                setProgress(null);\r\n                setImportMessage('');\r\n                setImportStage('');\r\n                console.error('Erro durante importação:', data.message);\r\n            }\r\n        });\r\n\r\n        setSocket(newSocket);\r\n        \r\n        // Verificar status/progresso ao carregar\r\n        checkImportStatus();\r\n        checkImportProgress();\r\n        \r\n        // Cleanup quando o componente desmontar\r\n        return () => {\r\n            newSocket.off('import-progress');\r\n            newSocket.disconnect();\r\n        };\r\n    }, []);\r\n\r\n    // Verificar progresso da importação salvo\r\n    const checkImportProgress = async () => {\r\n        try {\r\n            const response = await getProgressoImportacao(idEmpresa);\r\n            const { importacao_finalizada, progresso_importacao } = response.data;\r\n            \r\n            if (importacao_finalizada) {\r\n                setImportComplete(true);\r\n                setProgress(100);\r\n                setImportMessage('Importação finalizada com sucesso!');\r\n                setImportStage('finalizado');\r\n            } else if (progresso_importacao && progresso_importacao.em_andamento) {\r\n                setProgress(progresso_importacao.porcentagem);\r\n                setImportMessage(progresso_importacao.etapa_atual);\r\n                setImportStage('em_andamento');\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Erro ao verificar progresso da importação:\", error);\r\n        }\r\n    };\r\n\r\n    // Refatorando a função checkImportStatus\r\n    const checkImportStatus = async () => {\r\n        try {\r\n        const response = await apiCheckImportStatus(idEmpresa);\r\n        const data = response.data; // assumindo que está usando axios, onde a resposta está no campo `data`\r\n        \r\n        if (data.importacao_finalizada) {\r\n            setImportComplete(true);\r\n        }\r\n        } catch (error) {\r\n        console.error(\"Erro ao verificar status de importação:\", error);\r\n        }\r\n    };\r\n\r\n    const updateImportFlag = async () => {\r\n        try {\r\n            const importacao_finalizada = true;\r\n            const response = await apiUpdateImportFlag(idEmpresa, importacao_finalizada)            \r\n        } catch (error) {\r\n            console.error(\"Erro ao atualizar flag de importação:\", error);\r\n        }\r\n    };\r\n\r\n    const handleBack = () => {\r\n        //setSidebar(!sidebar)\r\n        navigate(\"/\");\r\n    }\r\n\r\n    //Validações\r\n    const SignUpSchema = Yup.object().shape({\r\n        ifoodLink: importType === 'linkIfood' && Yup.string().required('Campo obrigatório'),\r\n        anotaAiLink: importType === 'linkAnotaAi' && Yup.string().required('Campo obrigatório'),\r\n    });\r\n\r\n    const formik = useFormik({\r\n        validationSchema: SignUpSchema,\r\n        validateOnBlur: false,\r\n        validateOnChange: false,\r\n        //enableReinitialize: true,\r\n        initialValues: {\r\n            ifoodLink: linkObj && linkObj.importType === \"linkIfood\" ? linkObj.importLink : \"\",\r\n            anotaAiLink: linkObj && linkObj.importType === \"linkAnotaAi\" ? linkObj.importLink : \"\",\r\n        },\r\n        onSubmit: (values) => {\r\n\r\n            //const password = values.password;\r\n            //const confirmPassword = values.confirmPassword;\r\n            const ifoodLink = values.ifoodLink;\r\n            const anotaAiLink = values.anotaAiLink\r\n            var linkObj = {};\r\n            //handleSubmitAPI();   \r\n            if (importType === 'linkIfood') {\r\n                //importaCardapioIfood(ifoodLink)\r\n                linkObj = {\r\n                    onIfoodAndAnotaai: onIfoodAndAnotaai,\r\n                    importType: 'linkIfood',\r\n                    importLink: ifoodLink\r\n                }\r\n                //importCardapioIfood(ifoodLink, 2);\r\n                //navigate(\"/endereco\", { state: { infoEmpresaObj, passwordObj, linkObj } });\r\n            } else if (importType === 'linkAnotaAi') {\r\n                //importaCardapioAnotaai(anotaAiLink)\r\n                linkObj = {\r\n                    onIfoodAndAnotaai: onIfoodAndAnotaai,\r\n                    importType: 'linkAnotaAi',\r\n                    importLink: anotaAiLink\r\n                }\r\n                //importCardapioAnotaai(anotaAiLink, 2);\r\n                //navigate(\"/endereco\", { state: { infoEmpresaObj, passwordObj, linkObj } });\r\n            }\r\n\r\n            handleSubmitAPI(linkObj)\r\n\r\n        },\r\n    });\r\n\r\n\r\n    const handleSubmitAPI = async (linkObj) => {\r\n        if (linkObj) {\r\n            console.log(\"linkObj>\", linkObj);\r\n            try {\r\n                setIsSubmitting(true);\r\n                if (linkObj.importType === 'linkIfood') {\r\n                    console.log(\"Caiu no import Ifood\");\r\n                    const response = await importCardapioIfood(linkObj.importLink, idEmpresa);\r\n                    console.log(\"RESPOSTA Ifood:\", response);\r\n                } else if (linkObj.importType === 'linkAnotaAi') {\r\n                    console.log(\"Caiu no import AnotaAi\");\r\n                    const response = await importCardapioAnotaai(linkObj.importLink, idEmpresa);\r\n                    console.log(\"RESPOSTA AnotaAi:\", response);\r\n                }\r\n            } catch (error) {\r\n                console.error(\"Erro durante o import:\", error);\r\n            } finally {\r\n                setIsSubmitting(false);\r\n            }\r\n        }    \r\n    };\r\n\r\n    return (\r\n        <>\r\n            <PermissionGate permissions={['default']}>\r\n\r\n\r\n                {/*<LeftMenu setSidebar={setSidebar} sidebar={sidebar} />*/}\r\n\r\n                <Teste sidebar={sidebar}>\r\n                    <div className=\"m-5\">\r\n                       \r\n                        <div className=\"containerAddEmpresaForm\" style={{marginRight:70, minHeight:\"75vh\"}}>\r\n\r\n                        {importComplete ? (\r\n                                <div style={{display:'flex', flexDirection:'column', gap:20}}>\r\n                                    <h3>Importação do Cardápio concluída!</h3>\r\n                                    <img height={200} src={svgSuccess}></img>\r\n                                </div>\r\n                            ) : \r\n                            \r\n                            (progress !== null && !importComplete) || (importStage === 'em_andamento') ? (\r\n                                <div className=\"import-progress\" style={{minWidth:500, padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '10px', margin: '20px 0'}}>\r\n                                    <div className=\"progress-info\" style={{display: 'flex', justifyContent: 'space-between', marginBottom: '10px'}}>\r\n                                        <span className=\"progress-message\" style={{fontWeight: 'bold', color: '#495057'}}>\r\n                                            {importMessage || 'Importação do cardápio em andamento'}\r\n                                        </span>\r\n                                        <span className=\"progress-percentage\" style={{fontWeight: 'bold', color: '#28a745'}}>\r\n                                            {progress !== null ? `${progress}%` : '0%'}\r\n                                        </span>\r\n                                    </div>\r\n                                    <ProgressBar \r\n                                        now={progress || 0} \r\n                                        animated={progress < 100}\r\n                                        variant=\"success\"\r\n                                        style={{fontSize:19, height:30, marginBottom: '15px'}} \r\n                                    />\r\n                                    {importDetails && importStage === 'categorias' && (\r\n                                        <div className=\"progress-details\" style={{fontSize: '14px', color: '#6c757d'}}>\r\n                                            <small>\r\n                                                📂 Processando categoria: <strong>{importDetails.categoria_atual}</strong> \r\n                                                ({importDetails.categorias_processadas}/{importDetails.total_categorias})\r\n                                            </small>\r\n                                        </div>\r\n                                    )}\r\n                                    {importDetails && importStage === 'adicionais' && (\r\n                                        <div className=\"progress-details\" style={{fontSize: '14px', color: '#6c757d'}}>\r\n                                            <small>\r\n                                                🛠️ Processando grupo de adicionais: <strong>{importDetails.grupo_atual}</strong> \r\n                                                ({importDetails.grupos_processados}/{importDetails.total_grupos})\r\n                                            </small>\r\n                                        </div>\r\n                                    )}\r\n                                    {importDetails && importStage === 'inicio' && (\r\n                                        <div className=\"progress-details\" style={{fontSize: '14px', color: '#6c757d'}}>\r\n                                            <small>\r\n                                                🚀 Preparando importação... \r\n                                                {importDetails.total_categorias && (\r\n                                                    <span>\r\n                                                        ({importDetails.total_categorias} categorias, {importDetails.total_itens} itens, {importDetails.total_adicionais} adicionais)\r\n                                                    </span>\r\n                                                )}\r\n                                            </small>\r\n                                        </div>\r\n                                    )}\r\n                                    {importStage === 'finalizado' && (\r\n                                        <div className=\"progress-details\" style={{fontSize: '14px', color: '#28a745', fontWeight: 'bold'}}>\r\n                                            ✅ Importação concluída com sucesso!\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            ) :\r\n\r\n                            (\r\n                            <form onSubmit={formik.handleSubmit} className=\"formAddEmpresa\">\r\n\r\n                                <div className='informationsAccount'>\r\n\r\n                                    <div style={{ fontFamily: 'Arial, sans-serif', padding: '20px', backgroundColor: 'white', color: '#333', margin: 'auto' }}>\r\n                                        <h3>Seu estabelecimento está cadastrado no iFood ou AnotaAi?</h3>\r\n                                        <p>Selecione 'Sim' para sincronizar automaticamente seu cardápio existente e evitar o cadastro manual.</p>\r\n                                        <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>\r\n                                            <button type=\"button\"\r\n                                                className={onIfoodAndAnotaai === 'sim' ? 'btnFwdCadastros' : 'btnBackCadastros'}\r\n                                                onClick={() => setOnIfoodAndAnotaai('sim')} style={{ width: '50%' }}>\r\n                                                Sim\r\n                                            </button>                                            \r\n                                        </div>\r\n                                        {\r\n                                            onIfoodAndAnotaai === 'sim' ?\r\n                                                <div>\r\n                                                    <div>\r\n                                                        <h3>Como você deseja importar seu cardápio?</h3>\r\n                                                        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', marginBottom: '20px' }}>\r\n                                                            <button type=\"button\"\r\n                                                                className={importType === 'linkIfood' ? 'btnFwdCadastros' : 'btnBackCadastros'}\r\n                                                                style={{ width: '100%' }} onClick={() => setImportType('linkIfood')}>\r\n                                                                Pelo link do meu cardápio IFood\r\n                                                            </button>\r\n                                                            <button type=\"button\"\r\n                                                                className={importType === 'linkAnotaAi' ? 'btnFwdCadastros' : 'btnBackCadastros'}\r\n                                                                style={{ width: '100%' }} onClick={() => setImportType('linkAnotaAi')}>\r\n                                                                Pelo link do meu cardápio AnotaAi\r\n                                                            </button>\r\n                                                            {/*<button className='btnBackCadastros' style={{ width: '100%' }}>Não desejo importar meu cardápio</button>*/}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    {\r\n                                                        importType === 'linkIfood' ?\r\n                                                            <div>\r\n                                                                <h3>Para finalizar, cole o link do seu cardápio iFood abaixo</h3>\r\n                                                                <p style={{ marginBottom: '5px', textDecoration: 'underline', color: '#007bff', cursor: 'pointer' }} onClick={() => setHasVideo(true)}>Onde consigo o link da minha loja do iFood?</p>\r\n                                                                {hasVideo ?\r\n                                                                    <div>\r\n                                                                        <p style={{ marginBottom: '20px' }}>Aprenda como copiar o link do seu cardápio iFood!</p>\r\n                                                                        <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'center' }}>\r\n                                                                            <iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/example\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" allowFullScreen></iframe>\r\n                                                                        </div>\r\n                                                                        <button className='btnBackCadastros' style={{ width: '100%', marginBottom: '10px' }} onClick={() => setHasVideo(false)}>Fechar vídeo</button>\r\n                                                                    </div>\r\n                                                                    : null\r\n                                                                }\r\n                                                                <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', marginBottom: '20px' }}>\r\n                                                                    <label htmlFor=\"ifoodLink\">Link do cardápio iFood *</label>\r\n                                                                    <input type=\"text\"\r\n                                                                        id=\"ifoodLink\"\r\n                                                                        name=\"ifoodLink\"\r\n                                                                        value={formik.values.ifoodLink}\r\n                                                                        onChange={formik.handleChange}\r\n                                                                        style={{ padding: '10px', border: '1px solid #ccc' }}\r\n                                                                        className='linkToImportInput'\r\n                                                                        placeholder=\"https://www.ifood.com.br/delivery/cidade-uf/loja-exemplo-setor-exemplo/...\" />\r\n                                                                    {formik.errors.ifoodLink && (\r\n                                                                        <div className=\"error\">{formik.errors.ifoodLink}</div>\r\n                                                                    )}\r\n                                                                </div>\r\n                                                                <div style={{ display: 'flex', gap: '10px' }}>\r\n                                                                    {isSubmitting ?                                                                    \r\n                                                                        <button type=\"button\" disabled={true} className='btnFwdCadastros' style={{ width: '50%' }}>\r\n                                                                            <Loading type=\"spin\" color=\"white\" height={30} width={30} style={{marginLeft:'45%'}}\r\n                                                                            className=\"zIndexForLoadingSaveButton\" />\r\n                                                                        </button>                                                                \r\n                                                                        :\r\n                                                                        <button type=\"submit\" className='btnFwdCadastros' style={{ width: '50%' }}>Importar</button>\r\n                                                                    }\r\n                                                                </div>\r\n                                                            </div>\r\n                                                            : importType === 'linkAnotaAi' &&\r\n                                                            <div>\r\n                                                                <h3>Para finalizar, cole o link do seu cardápio AnotaAi abaixo</h3>\r\n                                                                <p style={{ marginBottom: '5px', textDecoration: 'underline', color: '#007bff', cursor: 'pointer' }} onClick={() => setHasVideo(true)}>Onde consigo o link da minha loja do AnotaAi?</p>\r\n                                                                {hasVideo ?\r\n                                                                    <div>\r\n                                                                        <p style={{ marginBottom: '20px' }}>Aprenda como copiar o link do seu cardápio AnotaAi!</p>\r\n                                                                        <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'center' }}>\r\n                                                                            <iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/example\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\" allowFullScreen></iframe>\r\n                                                                        </div>\r\n                                                                        <button className='btnBackCadastros' style={{ width: '100%', marginBottom: '10px' }} onClick={() => setHasVideo(false)}>Fechar vídeo</button>\r\n                                                                    </div>\r\n                                                                    : null\r\n                                                                }\r\n                                                                <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', marginBottom: '20px' }}>\r\n                                                                    <label htmlFor=\"anotaAiLink\">Link do cardápio AnotaAi *</label>\r\n                                                                    <input type=\"text\"\r\n                                                                        id=\"anotaAiLink\"\r\n                                                                        name=\"anotaAiLink\"\r\n                                                                        value={formik.values.anotaAiLink}\r\n                                                                        onChange={formik.handleChange}\r\n                                                                        style={{ padding: '10px', border: '1px solid #ccc' }}\r\n                                                                        className='linkToImportInput'\r\n                                                                        placeholder=\"https://pedido.anota.ai/loja/nome-da-loja-1?f=msa\" />\r\n                                                                    {formik.errors.anotaAiLink && (\r\n                                                                        <div className=\"error\">{formik.errors.anotaAiLink}</div>\r\n                                                                    )}\r\n                                                                </div>\r\n                                                                <div style={{ display: 'flex', gap: '10px' }}>\r\n                                                                    {/*<button type=\"button\" className='btnBackCadastros' style={{ width: '50%' }}>Voltar</button>*/}\r\n                                                                    {isSubmitting ?                                                                    \r\n                                                                        <button type=\"button\" disabled={true} className='btnFwdCadastros' style={{ width: '50%' }}>\r\n                                                                            <Loading type=\"spin\" color=\"white\" height={30} width={30} style={{marginLeft:'45%'}}\r\n                                                                            className=\"zIndexForLoadingSaveButton\" />\r\n                                                                        </button>                                                                \r\n                                                                        :\r\n                                                                        <button type=\"submit\" className='btnFwdCadastros' style={{ width: '50%' }}>Importar</button>\r\n                                                                    }\r\n                                                                </div>\r\n                                                            </div>\r\n                                                    }\r\n\r\n\r\n                                                </div>\r\n                                                :\r\n                                                null\r\n                                        }\r\n\r\n                                    </div>\r\n\r\n                                </div>\r\n\r\n                            </form>\r\n                            )}\r\n                            \r\n                        </div>\r\n                       \r\n                    </div>\r\n                </Teste>\r\n\r\n            </PermissionGate>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default ImportaCardapio;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,aAAa;AACpB,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,SAAS,QAAQ,QAAQ;AAClC,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,qBAAqB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAQ,oBAAoB;AAClJ,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,UAAU,MAAM,oCAAoC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,KAAK,GAAGpB,MAAM,CAACqB,GAAG;AACxB;AACA;AACA,oBAAoB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAdIH,KAAK;AAiBX,MAAMI,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAE1B,MAAM;IAAEH,OAAO;IAAEI;EAAW,CAAC,GAAG/B,UAAU,CAACO,cAAc,CAAC;EAAC,CAAC,CAAC;EAC7D,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMmC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAE9B,MAAMiC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,CAAC;EACxC,MAAMM,YAAY,GAAGH,YAAY,CAACI,GAAG;EACrC,MAAMC,SAAS,GAAGL,YAAY,CAACM,UAAU;EAEzC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM,CAACgD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjD,QAAQ,CAAC8C,OAAO,GAACA,OAAO,CAACE,iBAAiB,GAAC,EAAE,CAAC;EAChG,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC8C,OAAO,GAACA,OAAO,CAACI,UAAU,GAAC,EAAE,CAAC;EAC3E,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACgE,MAAM,EAAEC,SAAS,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAE1C,MAAMkE,aAAa,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW;EAC9D,MAAMC,MAAM,GAAGJ,aAAa,GAC1BK,OAAO,CAACC,GAAG,CAACC,wBAAwB,GACpCF,OAAO,CAACC,GAAG,CAACE,yBAAyB;EAEvCxE,SAAS,CAAC,MAAM;IACZ;IACA,MAAMyE,KAAK,GAAGL,MAAM;IACpB,MAAMM,SAAS,GAAG1D,EAAE,CAACyD,KAAK,EAAE;MACxBE,eAAe,EAAE,IAAI;MACrBC,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,IAAI,EAAE;QAAEC,KAAK,EAAE3C,YAAY,CAACC,OAAO,CAAC,OAAO;MAAE;IACjD,CAAC,CAAC;;IAEF;IACAsC,SAAS,CAACK,IAAI,CAAC,iBAAiB,EAAE;MAC9BC,SAAS,EAAExC,YAAY,CAACyC,QAAQ,CAAC,CAAC;MAClCC,QAAQ,EAAE;IACd,CAAC,CAAC;;IAEF;IACAR,SAAS,CAACS,EAAE,CAAC,iBAAiB,EAAGC,IAAI,IAAK;MACtCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,IAAI,CAAC;MACxC/B,WAAW,CAAC+B,IAAI,CAAChC,QAAQ,CAAC;MAC1BK,gBAAgB,CAAC2B,IAAI,CAACG,OAAO,IAAI,EAAE,CAAC;MACpC5B,cAAc,CAACyB,IAAI,CAACI,KAAK,IAAI,EAAE,CAAC;MAChC3B,gBAAgB,CAACuB,IAAI,CAACK,OAAO,IAAI,CAAC,CAAC,CAAC;MAEpC,IAAIL,IAAI,CAAChC,QAAQ,KAAK,GAAG,IAAIgC,IAAI,CAACI,KAAK,KAAK,YAAY,EAAE;QACtDjC,iBAAiB,CAAC,IAAI,CAAC;QACvBmC,gBAAgB,CAAC,CAAC;MACtB;MAEA,IAAIN,IAAI,CAACI,KAAK,KAAK,MAAM,EAAE;QACvBnC,WAAW,CAAC,IAAI,CAAC;QACjBI,gBAAgB,CAAC,EAAE,CAAC;QACpBE,cAAc,CAAC,EAAE,CAAC;QAClB0B,OAAO,CAACM,KAAK,CAAC,0BAA0B,EAAEP,IAAI,CAACG,OAAO,CAAC;MAC3D;IACJ,CAAC,CAAC;IAEFxB,SAAS,CAACW,SAAS,CAAC;;IAEpB;IACAkB,iBAAiB,CAAC,CAAC;IACnBC,mBAAmB,CAAC,CAAC;;IAErB;IACA,OAAO,MAAM;MACTnB,SAAS,CAACoB,GAAG,CAAC,iBAAiB,CAAC;MAChCpB,SAAS,CAACqB,UAAU,CAAC,CAAC;IAC1B,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMF,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACA,MAAMG,QAAQ,GAAG,MAAMnF,sBAAsB,CAAC6B,SAAS,CAAC;MACxD,MAAM;QAAEuD,qBAAqB;QAAEC;MAAqB,CAAC,GAAGF,QAAQ,CAACZ,IAAI;MAErE,IAAIa,qBAAqB,EAAE;QACvB1C,iBAAiB,CAAC,IAAI,CAAC;QACvBF,WAAW,CAAC,GAAG,CAAC;QAChBI,gBAAgB,CAAC,oCAAoC,CAAC;QACtDE,cAAc,CAAC,YAAY,CAAC;MAChC,CAAC,MAAM,IAAIuC,oBAAoB,IAAIA,oBAAoB,CAACC,YAAY,EAAE;QAClE9C,WAAW,CAAC6C,oBAAoB,CAACE,WAAW,CAAC;QAC7C3C,gBAAgB,CAACyC,oBAAoB,CAACG,WAAW,CAAC;QAClD1C,cAAc,CAAC,cAAc,CAAC;MAClC;IACJ,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACZN,OAAO,CAACM,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IACtE;EACJ,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACJ,MAAMI,QAAQ,GAAG,MAAMpF,oBAAoB,CAAC8B,SAAS,CAAC;MACtD,MAAM0C,IAAI,GAAGY,QAAQ,CAACZ,IAAI,CAAC,CAAC;;MAE5B,IAAIA,IAAI,CAACa,qBAAqB,EAAE;QAC5B1C,iBAAiB,CAAC,IAAI,CAAC;MAC3B;IACA,CAAC,CAAC,OAAOoC,KAAK,EAAE;MAChBN,OAAO,CAACM,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAC/D;EACJ,CAAC;EAED,MAAMD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACA,MAAMO,qBAAqB,GAAG,IAAI;MAClC,MAAMD,QAAQ,GAAG,MAAMrF,mBAAmB,CAAC+B,SAAS,EAAEuD,qBAAqB,CAAC;IAChF,CAAC,CAAC,OAAON,KAAK,EAAE;MACZN,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IACjE;EACJ,CAAC;EAED,MAAMW,UAAU,GAAGA,CAAA,KAAM;IACrB;IACArE,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;;EAED;EACA,MAAMsE,YAAY,GAAG/F,GAAG,CAACgG,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IACpCC,SAAS,EAAE1D,UAAU,KAAK,WAAW,IAAIxC,GAAG,CAACmG,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,mBAAmB,CAAC;IACnFC,WAAW,EAAE7D,UAAU,KAAK,aAAa,IAAIxC,GAAG,CAACmG,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,mBAAmB;EAC1F,CAAC,CAAC;EAEF,MAAME,MAAM,GAAGvG,SAAS,CAAC;IACrBwG,gBAAgB,EAAER,YAAY;IAC9BS,cAAc,EAAE,KAAK;IACrBC,gBAAgB,EAAE,KAAK;IACvB;IACAC,aAAa,EAAE;MACXR,SAAS,EAAE9D,OAAO,IAAIA,OAAO,CAACI,UAAU,KAAK,WAAW,GAAGJ,OAAO,CAACuE,UAAU,GAAG,EAAE;MAClFN,WAAW,EAAEjE,OAAO,IAAIA,OAAO,CAACI,UAAU,KAAK,aAAa,GAAGJ,OAAO,CAACuE,UAAU,GAAG;IACxF,CAAC;IACDC,QAAQ,EAAGC,MAAM,IAAK;MAElB;MACA;MACA,MAAMX,SAAS,GAAGW,MAAM,CAACX,SAAS;MAClC,MAAMG,WAAW,GAAGQ,MAAM,CAACR,WAAW;MACtC,IAAIjE,OAAO,GAAG,CAAC,CAAC;MAChB;MACA,IAAII,UAAU,KAAK,WAAW,EAAE;QAC5B;QACAJ,OAAO,GAAG;UACNE,iBAAiB,EAAEA,iBAAiB;UACpCE,UAAU,EAAE,WAAW;UACvBmE,UAAU,EAAET;QAChB,CAAC;QACD;QACA;MACJ,CAAC,MAAM,IAAI1D,UAAU,KAAK,aAAa,EAAE;QACrC;QACAJ,OAAO,GAAG;UACNE,iBAAiB,EAAEA,iBAAiB;UACpCE,UAAU,EAAE,aAAa;UACzBmE,UAAU,EAAEN;QAChB,CAAC;QACD;QACA;MACJ;MAEAS,eAAe,CAAC1E,OAAO,CAAC;IAE5B;EACJ,CAAC,CAAC;EAGF,MAAM0E,eAAe,GAAG,MAAO1E,OAAO,IAAK;IACvC,IAAIA,OAAO,EAAE;MACTyC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE1C,OAAO,CAAC;MAChC,IAAI;QACAZ,eAAe,CAAC,IAAI,CAAC;QACrB,IAAIY,OAAO,CAACI,UAAU,KAAK,WAAW,EAAE;UACpCqC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;UACnC,MAAMU,QAAQ,GAAG,MAAMtF,mBAAmB,CAACkC,OAAO,CAACuE,UAAU,EAAEzE,SAAS,CAAC;UACzE2C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEU,QAAQ,CAAC;QAC5C,CAAC,MAAM,IAAIpD,OAAO,CAACI,UAAU,KAAK,aAAa,EAAE;UAC7CqC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;UACrC,MAAMU,QAAQ,GAAG,MAAMvF,qBAAqB,CAACmC,OAAO,CAACuE,UAAU,EAAEzE,SAAS,CAAC;UAC3E2C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEU,QAAQ,CAAC;QAC9C;MACJ,CAAC,CAAC,OAAOL,KAAK,EAAE;QACZN,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAClD,CAAC,SAAS;QACN3D,eAAe,CAAC,KAAK,CAAC;MAC1B;IACJ;EACJ,CAAC;EAED,oBACIX,OAAA,CAAAE,SAAA;IAAAgG,QAAA,eACIlG,OAAA,CAACnB,cAAc;MAACsH,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAD,QAAA,eAKrClG,OAAA,CAACG,KAAK;QAACE,OAAO,EAAEA,OAAQ;QAAA6F,QAAA,eACpBlG,OAAA;UAAKoG,SAAS,EAAC,KAAK;UAAAF,QAAA,eAEhBlG,OAAA;YAAKoG,SAAS,EAAC,yBAAyB;YAACC,KAAK,EAAE;cAACC,WAAW,EAAC,EAAE;cAAEC,SAAS,EAAC;YAAM,CAAE;YAAAL,QAAA,EAElFjE,cAAc,gBACPjC,OAAA;cAAKqG,KAAK,EAAE;gBAACG,OAAO,EAAC,MAAM;gBAAEC,aAAa,EAAC,QAAQ;gBAAEC,GAAG,EAAC;cAAE,CAAE;cAAAR,QAAA,gBACzDlG,OAAA;gBAAAkG,QAAA,EAAI;cAAiC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1C9G,OAAA;gBAAK+G,MAAM,EAAE,GAAI;gBAACC,GAAG,EAAElH;cAAW;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,GAGT/E,QAAQ,KAAK,IAAI,IAAI,CAACE,cAAc,IAAMI,WAAW,KAAK,cAAe,gBACtErC,OAAA;cAAKoG,SAAS,EAAC,iBAAiB;cAACC,KAAK,EAAE;gBAACY,QAAQ,EAAC,GAAG;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,eAAe,EAAE,SAAS;gBAAEC,YAAY,EAAE,MAAM;gBAAEC,MAAM,EAAE;cAAQ,CAAE;cAAAnB,QAAA,gBACxIlG,OAAA;gBAAKoG,SAAS,EAAC,eAAe;gBAACC,KAAK,EAAE;kBAACG,OAAO,EAAE,MAAM;kBAAEc,cAAc,EAAE,eAAe;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAArB,QAAA,gBAC3GlG,OAAA;kBAAMoG,SAAS,EAAC,kBAAkB;kBAACC,KAAK,EAAE;oBAACmB,UAAU,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAS,CAAE;kBAAAvB,QAAA,EAC5E/D,aAAa,IAAI;gBAAqC;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACP9G,OAAA;kBAAMoG,SAAS,EAAC,qBAAqB;kBAACC,KAAK,EAAE;oBAACmB,UAAU,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAS,CAAE;kBAAAvB,QAAA,EAC/EnE,QAAQ,KAAK,IAAI,GAAG,GAAGA,QAAQ,GAAG,GAAG;gBAAI;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN9G,OAAA,CAACJ,WAAW;gBACR8H,GAAG,EAAE3F,QAAQ,IAAI,CAAE;gBACnB4F,QAAQ,EAAE5F,QAAQ,GAAG,GAAI;gBACzB6F,OAAO,EAAC,SAAS;gBACjBvB,KAAK,EAAE;kBAACwB,QAAQ,EAAC,EAAE;kBAAEd,MAAM,EAAC,EAAE;kBAAEQ,YAAY,EAAE;gBAAM;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,EACDvE,aAAa,IAAIF,WAAW,KAAK,YAAY,iBAC1CrC,OAAA;gBAAKoG,SAAS,EAAC,kBAAkB;gBAACC,KAAK,EAAE;kBAACwB,QAAQ,EAAE,MAAM;kBAAEJ,KAAK,EAAE;gBAAS,CAAE;gBAAAvB,QAAA,eAC1ElG,OAAA;kBAAAkG,QAAA,GAAO,sCACuB,eAAAlG,OAAA;oBAAAkG,QAAA,EAAS3D,aAAa,CAACuF;kBAAe;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,KACzE,EAACvE,aAAa,CAACwF,sBAAsB,EAAC,GAAC,EAACxF,aAAa,CAACyF,gBAAgB,EAAC,GAC5E;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACR,EACAvE,aAAa,IAAIF,WAAW,KAAK,YAAY,iBAC1CrC,OAAA;gBAAKoG,SAAS,EAAC,kBAAkB;gBAACC,KAAK,EAAE;kBAACwB,QAAQ,EAAE,MAAM;kBAAEJ,KAAK,EAAE;gBAAS,CAAE;gBAAAvB,QAAA,eAC1ElG,OAAA;kBAAAkG,QAAA,GAAO,sDACkC,eAAAlG,OAAA;oBAAAkG,QAAA,EAAS3D,aAAa,CAAC0F;kBAAW;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,KAChF,EAACvE,aAAa,CAAC2F,kBAAkB,EAAC,GAAC,EAAC3F,aAAa,CAAC4F,YAAY,EAAC,GACpE;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACR,EACAvE,aAAa,IAAIF,WAAW,KAAK,QAAQ,iBACtCrC,OAAA;gBAAKoG,SAAS,EAAC,kBAAkB;gBAACC,KAAK,EAAE;kBAACwB,QAAQ,EAAE,MAAM;kBAAEJ,KAAK,EAAE;gBAAS,CAAE;gBAAAvB,QAAA,eAC1ElG,OAAA;kBAAAkG,QAAA,GAAO,6CAEH,EAAC3D,aAAa,CAACyF,gBAAgB,iBAC3BhI,OAAA;oBAAAkG,QAAA,GAAM,GACD,EAAC3D,aAAa,CAACyF,gBAAgB,EAAC,eAAa,EAACzF,aAAa,CAAC6F,WAAW,EAAC,UAAQ,EAAC7F,aAAa,CAAC8F,gBAAgB,EAAC,cACrH;kBAAA;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACR,EACAzE,WAAW,KAAK,YAAY,iBACzBrC,OAAA;gBAAKoG,SAAS,EAAC,kBAAkB;gBAACC,KAAK,EAAE;kBAACwB,QAAQ,EAAE,MAAM;kBAAEJ,KAAK,EAAE,SAAS;kBAAED,UAAU,EAAE;gBAAM,CAAE;gBAAAtB,QAAA,EAAC;cAEnG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAIV9G,OAAA;cAAM+F,QAAQ,EAAEN,MAAM,CAAC6C,YAAa;cAAClC,SAAS,EAAC,gBAAgB;cAAAF,QAAA,eAE3DlG,OAAA;gBAAKoG,SAAS,EAAC,qBAAqB;gBAAAF,QAAA,eAEhClG,OAAA;kBAAKqG,KAAK,EAAE;oBAAEkC,UAAU,EAAE,mBAAmB;oBAAErB,OAAO,EAAE,MAAM;oBAAEC,eAAe,EAAE,OAAO;oBAAEM,KAAK,EAAE,MAAM;oBAAEJ,MAAM,EAAE;kBAAO,CAAE;kBAAAnB,QAAA,gBACtHlG,OAAA;oBAAAkG,QAAA,EAAI;kBAAwD;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjE9G,OAAA;oBAAAkG,QAAA,EAAG;kBAAmG;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1G9G,OAAA;oBAAKqG,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEE,GAAG,EAAE,MAAM;sBAAEa,YAAY,EAAE;oBAAO,CAAE;oBAAArB,QAAA,eAC/DlG,OAAA;sBAAQwI,IAAI,EAAC,QAAQ;sBACjBpC,SAAS,EAAE3E,iBAAiB,KAAK,KAAK,GAAG,iBAAiB,GAAG,kBAAmB;sBAChFgH,OAAO,EAAEA,CAAA,KAAM/G,oBAAoB,CAAC,KAAK,CAAE;sBAAC2E,KAAK,EAAE;wBAAEqC,KAAK,EAAE;sBAAM,CAAE;sBAAAxC,QAAA,EAAC;oBAEzE;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,EAEFrF,iBAAiB,KAAK,KAAK,gBACvBzB,OAAA;oBAAAkG,QAAA,gBACIlG,OAAA;sBAAAkG,QAAA,gBACIlG,OAAA;wBAAAkG,QAAA,EAAI;sBAAuC;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChD9G,OAAA;wBAAKqG,KAAK,EAAE;0BAAEG,OAAO,EAAE,MAAM;0BAAEC,aAAa,EAAE,QAAQ;0BAAEC,GAAG,EAAE,MAAM;0BAAEa,YAAY,EAAE;wBAAO,CAAE;wBAAArB,QAAA,gBACxFlG,OAAA;0BAAQwI,IAAI,EAAC,QAAQ;0BACjBpC,SAAS,EAAEzE,UAAU,KAAK,WAAW,GAAG,iBAAiB,GAAG,kBAAmB;0BAC/E0E,KAAK,EAAE;4BAAEqC,KAAK,EAAE;0BAAO,CAAE;0BAACD,OAAO,EAAEA,CAAA,KAAM7G,aAAa,CAAC,WAAW,CAAE;0BAAAsE,QAAA,EAAC;wBAEzE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACT9G,OAAA;0BAAQwI,IAAI,EAAC,QAAQ;0BACjBpC,SAAS,EAAEzE,UAAU,KAAK,aAAa,GAAG,iBAAiB,GAAG,kBAAmB;0BACjF0E,KAAK,EAAE;4BAAEqC,KAAK,EAAE;0BAAO,CAAE;0BAACD,OAAO,EAAEA,CAAA,KAAM7G,aAAa,CAAC,aAAa,CAAE;0BAAAsE,QAAA,EAAC;wBAE3E;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAER,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,EAEFnF,UAAU,KAAK,WAAW,gBACtB3B,OAAA;sBAAAkG,QAAA,gBACIlG,OAAA;wBAAAkG,QAAA,EAAI;sBAAwD;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjE9G,OAAA;wBAAGqG,KAAK,EAAE;0BAAEkB,YAAY,EAAE,KAAK;0BAAEoB,cAAc,EAAE,WAAW;0BAAElB,KAAK,EAAE,SAAS;0BAAEmB,MAAM,EAAE;wBAAU,CAAE;wBAACH,OAAO,EAAEA,CAAA,KAAM3G,WAAW,CAAC,IAAI,CAAE;wBAAAoE,QAAA,EAAC;sBAA2C;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,EACrLjF,QAAQ,gBACL7B,OAAA;wBAAAkG,QAAA,gBACIlG,OAAA;0BAAGqG,KAAK,EAAE;4BAAEkB,YAAY,EAAE;0BAAO,CAAE;0BAAArB,QAAA,EAAC;wBAAiD;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACzF9G,OAAA;0BAAKqG,KAAK,EAAE;4BAAEkB,YAAY,EAAE,MAAM;4BAAEf,OAAO,EAAE,MAAM;4BAAEc,cAAc,EAAE;0BAAS,CAAE;0BAAApB,QAAA,eAC5ElG,OAAA;4BAAQ0I,KAAK,EAAC,KAAK;4BAAC3B,MAAM,EAAC,KAAK;4BAACC,GAAG,EAAC,uCAAuC;4BAAC6B,KAAK,EAAC,0FAA0F;4BAACC,eAAe;0BAAA;4BAAAnC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAS;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtM,CAAC,eACN9G,OAAA;0BAAQoG,SAAS,EAAC,kBAAkB;0BAACC,KAAK,EAAE;4BAAEqC,KAAK,EAAE,MAAM;4BAAEnB,YAAY,EAAE;0BAAO,CAAE;0BAACkB,OAAO,EAAEA,CAAA,KAAM3G,WAAW,CAAC,KAAK,CAAE;0BAAAoE,QAAA,EAAC;wBAAY;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5I,CAAC,GACJ,IAAI,eAEV9G,OAAA;wBAAKqG,KAAK,EAAE;0BAAEG,OAAO,EAAE,MAAM;0BAAEC,aAAa,EAAE,QAAQ;0BAAEC,GAAG,EAAE,MAAM;0BAAEa,YAAY,EAAE;wBAAO,CAAE;wBAAArB,QAAA,gBACxFlG,OAAA;0BAAO+I,OAAO,EAAC,WAAW;0BAAA7C,QAAA,EAAC;wBAAwB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC3D9G,OAAA;0BAAOwI,IAAI,EAAC,MAAM;0BACdQ,EAAE,EAAC,WAAW;0BACdC,IAAI,EAAC,WAAW;0BAChBC,KAAK,EAAEzD,MAAM,CAACO,MAAM,CAACX,SAAU;0BAC/B8D,QAAQ,EAAE1D,MAAM,CAAC2D,YAAa;0BAC9B/C,KAAK,EAAE;4BAAEa,OAAO,EAAE,MAAM;4BAAEmC,MAAM,EAAE;0BAAiB,CAAE;0BACrDjD,SAAS,EAAC,mBAAmB;0BAC7BkD,WAAW,EAAC;wBAA4E;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC9FrB,MAAM,CAAC8D,MAAM,CAAClE,SAAS,iBACpBrF,OAAA;0BAAKoG,SAAS,EAAC,OAAO;0BAAAF,QAAA,EAAET,MAAM,CAAC8D,MAAM,CAAClE;wBAAS;0BAAAsB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CACxD;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACN9G,OAAA;wBAAKqG,KAAK,EAAE;0BAAEG,OAAO,EAAE,MAAM;0BAAEE,GAAG,EAAE;wBAAO,CAAE;wBAAAR,QAAA,EACxCxF,YAAY,gBACTV,OAAA;0BAAQwI,IAAI,EAAC,QAAQ;0BAACgB,QAAQ,EAAE,IAAK;0BAACpD,SAAS,EAAC,iBAAiB;0BAACC,KAAK,EAAE;4BAAEqC,KAAK,EAAE;0BAAM,CAAE;0BAAAxC,QAAA,eACtFlG,OAAA,CAACH,OAAO;4BAAC2I,IAAI,EAAC,MAAM;4BAACf,KAAK,EAAC,OAAO;4BAACV,MAAM,EAAE,EAAG;4BAAC2B,KAAK,EAAE,EAAG;4BAACrC,KAAK,EAAE;8BAACoD,UAAU,EAAC;4BAAK,CAAE;4BACpFrD,SAAS,EAAC;0BAA4B;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,gBAET9G,OAAA;0BAAQwI,IAAI,EAAC,QAAQ;0BAACpC,SAAS,EAAC,iBAAiB;0BAACC,KAAK,EAAE;4BAAEqC,KAAK,EAAE;0BAAM,CAAE;0BAAAxC,QAAA,EAAC;wBAAQ;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAE/F,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,GACJnF,UAAU,KAAK,aAAa,iBAC9B3B,OAAA;sBAAAkG,QAAA,gBACIlG,OAAA;wBAAAkG,QAAA,EAAI;sBAA0D;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnE9G,OAAA;wBAAGqG,KAAK,EAAE;0BAAEkB,YAAY,EAAE,KAAK;0BAAEoB,cAAc,EAAE,WAAW;0BAAElB,KAAK,EAAE,SAAS;0BAAEmB,MAAM,EAAE;wBAAU,CAAE;wBAACH,OAAO,EAAEA,CAAA,KAAM3G,WAAW,CAAC,IAAI,CAAE;wBAAAoE,QAAA,EAAC;sBAA6C;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,EACvLjF,QAAQ,gBACL7B,OAAA;wBAAAkG,QAAA,gBACIlG,OAAA;0BAAGqG,KAAK,EAAE;4BAAEkB,YAAY,EAAE;0BAAO,CAAE;0BAAArB,QAAA,EAAC;wBAAmD;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eAC3F9G,OAAA;0BAAKqG,KAAK,EAAE;4BAAEkB,YAAY,EAAE,MAAM;4BAAEf,OAAO,EAAE,MAAM;4BAAEc,cAAc,EAAE;0BAAS,CAAE;0BAAApB,QAAA,eAC5ElG,OAAA;4BAAQ0I,KAAK,EAAC,KAAK;4BAAC3B,MAAM,EAAC,KAAK;4BAACC,GAAG,EAAC,uCAAuC;4BAAC6B,KAAK,EAAC,0FAA0F;4BAACC,eAAe;0BAAA;4BAAAnC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAS;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtM,CAAC,eACN9G,OAAA;0BAAQoG,SAAS,EAAC,kBAAkB;0BAACC,KAAK,EAAE;4BAAEqC,KAAK,EAAE,MAAM;4BAAEnB,YAAY,EAAE;0BAAO,CAAE;0BAACkB,OAAO,EAAEA,CAAA,KAAM3G,WAAW,CAAC,KAAK,CAAE;0BAAAoE,QAAA,EAAC;wBAAY;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5I,CAAC,GACJ,IAAI,eAEV9G,OAAA;wBAAKqG,KAAK,EAAE;0BAAEG,OAAO,EAAE,MAAM;0BAAEC,aAAa,EAAE,QAAQ;0BAAEC,GAAG,EAAE,MAAM;0BAAEa,YAAY,EAAE;wBAAO,CAAE;wBAAArB,QAAA,gBACxFlG,OAAA;0BAAO+I,OAAO,EAAC,aAAa;0BAAA7C,QAAA,EAAC;wBAA0B;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC/D9G,OAAA;0BAAOwI,IAAI,EAAC,MAAM;0BACdQ,EAAE,EAAC,aAAa;0BAChBC,IAAI,EAAC,aAAa;0BAClBC,KAAK,EAAEzD,MAAM,CAACO,MAAM,CAACR,WAAY;0BACjC2D,QAAQ,EAAE1D,MAAM,CAAC2D,YAAa;0BAC9B/C,KAAK,EAAE;4BAAEa,OAAO,EAAE,MAAM;4BAAEmC,MAAM,EAAE;0BAAiB,CAAE;0BACrDjD,SAAS,EAAC,mBAAmB;0BAC7BkD,WAAW,EAAC;wBAAmD;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACrErB,MAAM,CAAC8D,MAAM,CAAC/D,WAAW,iBACtBxF,OAAA;0BAAKoG,SAAS,EAAC,OAAO;0BAAAF,QAAA,EAAET,MAAM,CAAC8D,MAAM,CAAC/D;wBAAW;0BAAAmB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAC1D;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACN9G,OAAA;wBAAKqG,KAAK,EAAE;0BAAEG,OAAO,EAAE,MAAM;0BAAEE,GAAG,EAAE;wBAAO,CAAE;wBAAAR,QAAA,EAExCxF,YAAY,gBACTV,OAAA;0BAAQwI,IAAI,EAAC,QAAQ;0BAACgB,QAAQ,EAAE,IAAK;0BAACpD,SAAS,EAAC,iBAAiB;0BAACC,KAAK,EAAE;4BAAEqC,KAAK,EAAE;0BAAM,CAAE;0BAAAxC,QAAA,eACtFlG,OAAA,CAACH,OAAO;4BAAC2I,IAAI,EAAC,MAAM;4BAACf,KAAK,EAAC,OAAO;4BAACV,MAAM,EAAE,EAAG;4BAAC2B,KAAK,EAAE,EAAG;4BAACrC,KAAK,EAAE;8BAACoD,UAAU,EAAC;4BAAK,CAAE;4BACpFrD,SAAS,EAAC;0BAA4B;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,gBAET9G,OAAA;0BAAQwI,IAAI,EAAC,QAAQ;0BAACpC,SAAS,EAAC,iBAAiB;0BAACC,KAAK,EAAE;4BAAEqC,KAAK,EAAE;0BAAM,CAAE;0BAAAxC,QAAA,EAAC;wBAAQ;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAE/F,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIb,CAAC,GAEN,IAAI;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEJ;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEI;EAAC,gBACnB,CAAC;AAEX,CAAC;AAAAtG,EAAA,CAtZKD,eAAe;EAAA,QAIA3B,WAAW,EAiIbM,SAAS;AAAA;AAAAwK,GAAA,GArItBnJ,eAAe;AAwZrB,eAAeA,eAAe;AAAC,IAAAD,EAAA,EAAAoJ,GAAA;AAAAC,YAAA,CAAArJ,EAAA;AAAAqJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}