const mongoose = require('mongoose')
const { Schema } = mongoose;

const UserSchema = new Schema({
    id_user: Number,
    createdBy: String,
    name: String,
    email: String,
    password: String,
    vinculo_empresa: String,
    //vinculo_empresa: { type: Schema.Types.Number, ref: 'Empresa' },
    role: String,

    email_activated: Boolean,
    inativo: Boolean,
    bloqueado: Boolean,

    user_img: Array,

    createdAt: Date,
    updatedAt: Date,
    deletedAt: Date,
    resetToken: String, // Adicionando campo para token de redefinição de senha
    resetTokenExpiration: Date, // Expiração do token
},
 {
    toJSON: { virtuals: true }, // So `res.json()` and other `JSON.stringify()` functions include virtuals
    toObject: { virtuals: true } // So `console.log()` and other functions that use `toObject()` include virtuals
 },
 /*{
    collection: {clusteredIndex: {
        key: { _id: 1 },
        unique: true,
        }
    }
 }*/
);

UserSchema.virtual('userEmpresa', {
    ref: 'Empresa', // The model to use
    localField: 'vinculo_empresa', // Find people where `localField`
    foreignField: 'cnpj', // is equal to `foreignField`
    //count: true // And only get the number of docs
});

const User = mongoose.model('User', UserSchema)




module.exports = User