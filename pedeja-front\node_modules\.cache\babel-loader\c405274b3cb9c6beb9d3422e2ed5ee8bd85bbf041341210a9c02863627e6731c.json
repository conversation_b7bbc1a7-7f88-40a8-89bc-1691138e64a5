{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\Home\\\\index.jsx\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport Loading from 'react-loading';\nimport { Br, Cut, Line, Printer, Text, Row, render } from 'react-thermal-printer';\nimport './style.css';\nimport './styleResponsive.css';\nimport styled from 'styled-components';\nimport axios from \"axios\";\nimport { toast } from \"react-toastify\";\nimport pdfMake from \"pdfmake/build/pdfmake\";\nimport html2canvas from 'html2canvas';\n//import html2pdf from 'html2pdf.js';\nimport CryptoJS from 'crypto-js';\nimport imageUrl from '../../img/logoBlue.png';\nimport io from 'socket.io-client';\nimport { SidebarContext } from \"../../AppRoutes\";\nimport { AuthContext } from \"../../contexts/auth\";\nimport { getUser, getVinculoEmpresa, getPedido, updateStatusPrint, updateStatusPedido, updateStatusPedidoFinalizado, getPedidosByStatus, getPedidosByStatusSimples, getPedidosByStatusFinalizados } from \"../../services/api\";\nimport PermissionGate from \"../../services/PermissionGate\";\nimport { useNavigate } from \"react-router-dom\";\nimport LeftMenu from \"../../components/LeftMenu\";\nimport { GiFullMotorcycleHelmet } from \"react-icons/gi\";\nimport { MdStorefront } from \"react-icons/md\";\nimport moment from 'moment';\nimport * as AiIcons from 'react-icons/ai';\nimport * as FiIcons from 'react-icons/fi';\nimport { FaMotorcycle, FaRegClock, FaStore } from \"react-icons/fa\";\nimport { MdTableBar } from \"react-icons/md\";\nimport { IoMdPin } from \"react-icons/io\";\nimport ModalEditTempoEntrega from \"../../components/ModalEditTempoEntrega\";\nimport ModalPedido from \"../../components/ModalPedido\";\nimport ModalAddEntregador from \"./ModalAddEntregador\";\nimport { MobileKanban } from \"./MobileKanban\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Teste = styled.div`\n\n    display: flex;\n    margin-left:  ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'}; \n    height:auto;\n    width:auto;\n    transition: 150ms;\n    background-color:rgb(247,247,247)!important;\n    --background-color:white!important;\n    overflow: initial;\n    z-Index: 9;\n\n    @media (max-width: 880px) {\n        margin-left: 0;\n    }\n`;\n_c = Teste;\nconst Ordercolumn = styled.div`\n    min-width: 25%;\n`;\n_c2 = Ordercolumn;\nconst ModalUserOptions = styled.div`\n  font-size: 14px;\n  position: absolute;\n  top: 82px;\n  left: 150px;\n  display: ${({\n  showOptions\n}) => showOptions ? 'none' : ''};\n  float: left;\n  min-width: 160px;\n  margin: 2px 0 0;\n  padding: 5px 0;\n  list-style: none;\n  text-align: left;\n  border: 1px solid #ccc;\n  border: 1px solid rgba(0,0,0,.15);\n  border-radius: 4px;\n  background-color: #fff;\n  --background-color:rgb(247,247,247)!important;\n  background-clip: padding-box;\n  -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);\n  box-shadow: 0 6px 12px rgba(0,0,0,.175);\n  z-index: 25;\n\n  li{\n    font-weight: 400;\n    line-height: 1.42857143;\n    display: block;\n    clear: both;\n    padding: 3px 20px;\n    white-space: nowrap;\n    color: #58595b;\n  }\n  li:hover{\n    background: #f5f5f5\n  }\n`;\nconst User = () => {\n  _s2();\n  var _s = $RefreshSig$();\n  //const { user } = useContext(AuthContext);\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const objIdEmpresa = empresaParse._id;\n  const idEmpresa = empresaParse.id_empresa;\n  const vinculo_empresa = empresaParse.cnpj;\n  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\n  const userEncrypted = localStorage.getItem('user');\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  //const user = localStorage.getItem('user')\n  const userParse = JSON.parse(user);\n  //console.log(userParse)\n  //const userParse = user;\n  const userID = userParse._id;\n  const [showEditTempoEntregaEmpresa, setEditTempoEntregaEmpresa] = useState(true);\n  const [_idEmpresaEdit, set_idEmpresaEdit] = useState('');\n  //const [showOptions, setUserOptions] = useState(true);\n  const INITIAL_DATA = {\n    value: \"\",\n    label: 'Selecione uma empresa'\n  };\n  const [selectData, setselectData] = useState(INITIAL_DATA);\n  const [showPedido, setShowPedido] = useState(true);\n  const {\n    sidebar,\n    setSidebar\n  } = useContext(SidebarContext);\n  ; //TESTANDO CONST NO AppROUTES\n\n  var [tempoBalcaoMinBD, setTempoBalcaoMinBD] = useState(0);\n  var [tempoBalcaoMaxBD, setTempoBalcaoMaxBD] = useState(0);\n  var [tempoEntregaMinBD, setTempoEntregaMinBD] = useState(0);\n  var [tempoEntregaMaxBD, setTempoEntregaMaxBD] = useState(0);\n  var [tipoImpressao, setTipoImpressao] = useState(\"\");\n  const [refresh, setRefresh] = useState(false);\n  const [filtroSelecionado, setFiltroSelecionado] = useState(\"all\");\n  const [pedidosAnalise, setPedidosAnalise] = useState([]);\n  const [pedidosProducao, setPedidosProducao] = useState([]);\n  const [pedidosEntrega, setPedidosEntrega] = useState([]);\n  const [pedidosFinalizado, setPedidosFinalizado] = useState([]);\n\n  //const [pedidos, setPedidos] = useState([]);\n  const [pedidosSimples, setPedidosSimples] = useState([]);\n  const [pedidosFinalizados, setPedidosFinalizados] = useState([]);\n  const [isLoadingSimples, setIsLoadingSimples] = useState(true);\n  const [isLoadingFinalizados, setIsLoadingFinalizados] = useState(true);\n  const navigate = useNavigate();\n  const [infoPedido, setInfoPedido] = useState({});\n  const [filterIsLoading, setFilterIsLoading] = useState(false);\n  const [showModalAddEntregador, setShowModalAddEntregador] = useState(true);\n  const [pedidoToAddEntregador, setPedidoToAddEntregador] = useState({});\n  const [searchQuery, setSearchQuery] = useState('');\n  const [originalPedidos, setOriginalPedidos] = useState([]); // Para armazenar a lista completa de pedidos\n  //const [imageBase64, setImageBase64] = useState(\"\");\n  //const [pdfBase64, setPdfBase64] = useState(\"\");\n  var imageDataURL = null;\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasCancelPassword, setHasCancelPassword] = useState(false);\n  //const [printJobId, setPrintJobId] = useState('');\n\n  // Hook para detectar mobile\n  const useIsMobile = () => {\n    _s();\n    const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n    useEffect(() => {\n      const handleResize = () => {\n        setIsMobile(window.innerWidth <= 768);\n      };\n      window.addEventListener('resize', handleResize);\n      return () => window.removeEventListener('resize', handleResize);\n    }, []);\n    return isMobile;\n  };\n  _s(useIsMobile, \"IPgBv7VuYiSHCPyFLng5ZxH+1OA=\");\n  const isMobile = useIsMobile();\n  useEffect(() => {\n    if (refresh) {\n      const fetchDataAndSetPedidos = async () => {\n        const fetchedPedidosSimples = await fetchPedidosSimples();\n        const fetchedPedidosFinalizados = await fetchPedidosFinalizados();\n        setPedidosSimples(fetchedPedidosSimples);\n        setPedidosFinalizados(fetchedPedidosFinalizados);\n      };\n      fetchDataAndSetPedidos();\n      setRefresh(false);\n    }\n  }, [refresh]);\n  /*\r\n  const fetchDataAndSetPedidosForFilters = async () => {\r\n      const fetchedPedidos = await fetchData();\r\n      setPedidos(fetchedPedidos);\r\n  };\r\n  \r\n  const fetchData = async () => {\r\n      const response = await getVinculoEmpresa(userID);\r\n      //const vinculo = response.data.vinculo.cnpj;\r\n      //console.log(\"response>\",response.data.vinculo);\r\n      //const pedidosResponse = await getPedidos(userID, id_empresa);\r\n      const pedidosAnalise = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '1');\r\n      const pedidosProducao = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '2');\r\n      const pedidosEntrega = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '3');\r\n      const pedidosFinalizados = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '4');\r\n      //console.log(\"pedidosAnaliseResponse>\",pedidosAnaliseResponse.data.pedidos);\r\n      setTipoImpressao(response.data.vinculo.tipo_impressao);\r\n      setTempoBalcaoMinBD(parseInt(response.data.vinculo.tempoBalcaoMin));\r\n      setTempoBalcaoMaxBD(parseInt(response.data.vinculo.tempoBalcaoMax));\r\n      setTempoEntregaMinBD(parseInt(response.data.vinculo.tempoEntregaMin));\r\n      setTempoEntregaMaxBD(parseInt(response.data.vinculo.tempoEntregaMax));\r\n      \r\n  //setPedidos(pedidosResponse.data.pedidos);\r\n      setRefresh(false);\r\n      return [\r\n          ...pedidosAnalise.data.pedidos,\r\n          ...pedidosProducao.data.pedidos,\r\n          ...pedidosEntrega.data.pedidos,\r\n          ...pedidosFinalizados.data.pedidos,\r\n        ];\r\n  };*/\n\n  /*const fetchData = async () => {\r\n      const response = await getVinculoEmpresa(userID);\r\n      const pedidosAnalise = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '1');\r\n      const pedidosProducao = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '2');\r\n      const pedidosEntrega = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '3');\r\n      const pedidosFinalizados = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '4');\r\n      setTipoImpressao(response.data.vinculo.tipo_impressao);\r\n      setTempoBalcaoMinBD(parseInt(response.data.vinculo.tempoBalcaoMin));\r\n      setTempoBalcaoMaxBD(parseInt(response.data.vinculo.tempoBalcaoMax));\r\n      setTempoEntregaMinBD(parseInt(response.data.vinculo.tempoEntregaMin));\r\n      setTempoEntregaMaxBD(parseInt(response.data.vinculo.tempoEntregaMax));\r\n      setHasCancelPassword(response.data.vinculo.has_cancel_password)\r\n        setRefresh(false);\r\n      setFilterIsLoading(false);\r\n      return [...pedidosAnalise.data.pedidos, ...pedidosProducao.data.pedidos, ...pedidosEntrega.data.pedidos, ...pedidosFinalizados.data.pedidos];\r\n  };*/\n\n  /*const fetchDataAndSetPedidosForFilters = async (paramReceived) => {\r\n      const fetchedPedidos = await fetchData();\r\n      paramReceived === \"all\" && setPedidos(fetchedPedidos);\r\n      paramReceived === \"delivery\" && setPedidos(fetchedPedidos.filter(pedido => pedido.entrega.tipo_entrega.toLowerCase() === \"entrega\"));\r\n      paramReceived === \"balcao\" && setPedidos(fetchedPedidos.filter(pedido => pedido.entrega.tipo_entrega.toLowerCase() === \"retirada\"));\r\n    };*/\n\n  // Função para carregar pedidos simples (status 1, 2, 3)\n  const fetchPedidosSimples = async () => {\n    const response = await getVinculoEmpresa(userID);\n    const pedidosStatus123 = await getPedidosByStatusSimples(userID, idEmpresa, vinculo_empresa, ['1', '2', '3'], 'false');\n\n    // Atualizar as informações adicionais\n    setTipoImpressao(response.data.vinculo.tipo_impressao);\n    setTempoBalcaoMinBD(parseInt(response.data.vinculo.tempoBalcaoMin));\n    setTempoBalcaoMaxBD(parseInt(response.data.vinculo.tempoBalcaoMax));\n    setTempoEntregaMinBD(parseInt(response.data.vinculo.tempoEntregaMin));\n    setTempoEntregaMaxBD(parseInt(response.data.vinculo.tempoEntregaMax));\n    setHasCancelPassword(response.data.vinculo.has_cancel_password);\n    setFilterIsLoading(false);\n    return pedidosStatus123.data.pedidos;\n  };\n\n  // Função para carregar pedidos finalizados (status 4)\n  const fetchPedidosFinalizados = async () => {\n    const pedidosStatus4 = await getPedidosByStatusFinalizados(userID, idEmpresa, vinculo_empresa, 'false');\n    setFilterIsLoading(false);\n    return pedidosStatus4.data.pedidos;\n  };\n\n  // Função para aplicar os filtros\n  const fetchDataAndSetPedidosForFilters = async paramReceived => {\n    const fetchedPedidosSimples = await fetchPedidosSimples();\n    const fetchedPedidosFinalizados = await fetchPedidosFinalizados();\n    let filteredPedidosSimples = fetchedPedidosSimples;\n    let filteredPedidosFinalizados = fetchedPedidosFinalizados;\n    if (paramReceived === \"delivery\") {\n      filteredPedidosSimples = fetchedPedidosSimples.filter(pedido => {\n        var _pedido$entrega;\n        return ((_pedido$entrega = pedido.entrega) === null || _pedido$entrega === void 0 ? void 0 : _pedido$entrega.tipo_entrega.toLowerCase()) === \"entrega\";\n      });\n      filteredPedidosFinalizados = fetchedPedidosFinalizados.filter(pedido => {\n        var _pedido$entrega2, _pedido$entrega2$tipo;\n        return ((_pedido$entrega2 = pedido.entrega) === null || _pedido$entrega2 === void 0 ? void 0 : (_pedido$entrega2$tipo = _pedido$entrega2.tipo_entrega) === null || _pedido$entrega2$tipo === void 0 ? void 0 : _pedido$entrega2$tipo.toLowerCase()) === \"entrega\";\n      });\n    } else if (paramReceived === \"balcao\") {\n      filteredPedidosSimples = fetchedPedidosSimples.filter(pedido => {\n        var _pedido$entrega3, _pedido$entrega3$tipo;\n        return ((_pedido$entrega3 = pedido.entrega) === null || _pedido$entrega3 === void 0 ? void 0 : (_pedido$entrega3$tipo = _pedido$entrega3.tipo_entrega) === null || _pedido$entrega3$tipo === void 0 ? void 0 : _pedido$entrega3$tipo.toLowerCase()) === \"retirada\";\n      });\n      filteredPedidosFinalizados = fetchedPedidosFinalizados.filter(pedido => {\n        var _pedido$entrega4, _pedido$entrega4$tipo;\n        return ((_pedido$entrega4 = pedido.entrega) === null || _pedido$entrega4 === void 0 ? void 0 : (_pedido$entrega4$tipo = _pedido$entrega4.tipo_entrega) === null || _pedido$entrega4$tipo === void 0 ? void 0 : _pedido$entrega4$tipo.toLowerCase()) === \"retirada\";\n      });\n    } else if (paramReceived === \"mesas\") {\n      filteredPedidosSimples = fetchedPedidosSimples.filter(pedido => pedido.createdBy.toLowerCase().includes('mesa'));\n      filteredPedidosFinalizados = fetchedPedidosFinalizados.filter(pedido => pedido.createdBy.toLowerCase().includes('mesa'));\n    }\n\n    // Atualizar os estados de pedidos\n    setPedidosSimples(filteredPedidosSimples);\n    setPedidosFinalizados(filteredPedidosFinalizados);\n  };\n  useEffect(() => {\n    // Carregar pedidos com o filtro específico\n    setFilterIsLoading(true);\n    fetchDataAndSetPedidosForFilters(filtroSelecionado);\n  }, [refresh]);\n\n  // Organizar os pedidos simples em suas respectivas categorias\n  useEffect(() => {\n    setPedidosAnalise([]);\n    setPedidosProducao([]);\n    setPedidosEntrega([]);\n    pedidosSimples.forEach(dados => {\n      if (dados.status_pedido === '1') {\n        setPedidosAnalise(pedidosAnteriores => [...pedidosAnteriores, dados]);\n      } else if (dados.status_pedido === '2') {\n        setPedidosProducao(pedidosAnteriores => [...pedidosAnteriores, dados]);\n      } else if (dados.status_pedido === '3') {\n        setPedidosEntrega(pedidosAnteriores => [...pedidosAnteriores, dados]);\n      }\n    });\n  }, [pedidosSimples, refresh]); // Esse efeito é acionado quando 'pedidosSimples' é atualizado\n\n  // Organizar os pedidos finalizados em sua categoria\n  useEffect(() => {\n    setPedidosFinalizado([]);\n    pedidosFinalizados.forEach(dados => {\n      if (dados.status_pedido === '4') {\n        setPedidosFinalizado(pedidosAnteriores => [...pedidosAnteriores, dados]);\n      }\n    });\n  }, [pedidosFinalizados, refresh]); // Esse efeito é acionado quando 'pedidosFinalizados' é atualizado\n\n  /*useEffect(() => {\r\n      setPedidosAnalise([]);\r\n      setPedidosProducao([]);\r\n      setPedidosEntrega([]);\r\n      setPedidosFinalizado([]);\r\n      pedidos.map((dados, i) => {\r\n          if (dados.status_pedido == '1') {\r\n              setPedidosAnalise(pedidosAnteriores => [...pedidosAnteriores, dados]);\r\n          }\r\n          if (dados.status_pedido == '2') {\r\n              setPedidosProducao(pedidosAnteriores => [...pedidosAnteriores, dados])\r\n          }\r\n          if (dados.status_pedido == '3') {\r\n              setPedidosEntrega(pedidosAnteriores => [...pedidosAnteriores, dados])\r\n          }\r\n          if (dados.status_pedido == '4') {\r\n              setPedidosFinalizado(pedidosAnteriores => [...pedidosAnteriores, dados])\r\n          }\r\n      })\r\n  }, [pedidos, refresh]); // Sem dependências, então o efeito será executado apenas uma vez*/\n\n  /*  ///////////////////////// COMENTARIO TEMPORÁRIO SÓ PRA TESTAR  //////////////////////////\r\n  const isDevelopment = window.location.hostname === 'localhost';\r\n  const apiUrl = isDevelopment\r\n      ? process.env.REACT_APP_SERVER_URL_DEV\r\n      : process.env.REACT_APP_SERVER_URL_PROD;\r\n  useEffect(() => {\r\n      const fetchPedidos = async () => {\r\n          const data = await fetchData();\r\n          setOriginalPedidos(data); // Armazena os pedidos originais\r\n          setPedidos(data); // Inicializa os pedidos com os dados originais\r\n      };\r\n        fetchPedidos();\r\n        const wsUrl = apiUrl; // Alterar a URL se o servidor estiver em um endereço diferente\r\n      const socket = io(wsUrl, { withCredentials: true, transports: ['websocket'], auth: { token: localStorage.getItem('token') } });\r\n        socket.emit('joinCompanyRoom', { companyId: idEmpresa.toString(), clientId: 'reactClient' });\r\n        socket.on('novoPedido', async (data) => {\r\n          console.log(\"NOVO PEDIDO RECEBIDO!\");\r\n          const newPedidos = await fetchData();\r\n          setOriginalPedidos(newPedidos);\r\n          setPedidos(newPedidos);\r\n      });\r\n        return () => {\r\n          socket.off('novoPedido');\r\n      };\r\n  }, [idEmpresa]);\r\n    useEffect(() => {\r\n      const filteredPedidos = originalPedidos.filter(dados =>\r\n          dados.nome_cliente.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n          dados.id_pedido_counter?.toString().includes(searchQuery) ||\r\n          dados.id_pedido.toString().includes(searchQuery)\r\n      );\r\n      setPedidos(filteredPedidos);\r\n  }, [searchQuery, originalPedidos]);*/\n  const isDevelopment = window.location.hostname === 'localhost';\n  const apiUrl = isDevelopment ? process.env.REACT_APP_SERVER_URL_DEV : process.env.REACT_APP_SERVER_URL_PROD;\n  useEffect(() => {\n    // Função para carregar os pedidos simples e finalizados\n    const fetchPedidos = async () => {\n      // Carregar pedidos simples (status 1, 2, 3)\n      const allPedidosSimples = await fetchPedidosSimples();\n      // Carregar pedidos finalizados (status 4)\n      const allPedidosFinalizados = await fetchPedidosFinalizados();\n      const allPedidos = [...allPedidosSimples, ...allPedidosFinalizados];\n      setOriginalPedidos(allPedidos);\n    };\n    fetchPedidos(); // Carrega os pedidos na inicialização\n\n    // Configuração do WebSocket\n    const wsUrl = apiUrl;\n    //const socket = io(wsUrl, { withCredentials: true, transports: ['websocket'], auth: { token: localStorage.getItem('token') } });\n    const socket = io(wsUrl, {\n      withCredentials: true,\n      transports: ['websocket'],\n      auth: {\n        token: localStorage.getItem('token')\n      },\n      reconnection: true,\n      // Tenta reconectar automaticamente\n      reconnectionAttempts: 10,\n      // Máximo de 10 tentativas\n      reconnectionDelay: 5000 // Tenta reconectar a cada 5 segundos\n    });\n    socket.on(\"connect\", () => {\n      console.log(\"WebSocket conectado!\");\n    });\n    socket.on(\"disconnect\", reason => {\n      console.error(\"WebSocket desconectado:\", reason);\n    });\n    socket.on(\"reconnect_attempt\", attempt => {\n      console.log(`Tentativa de reconexão ${attempt}`);\n    });\n    socket.once(\"ping\", () => {\n      console.log(\"🔄 Ping recebido do servidor, enviando pong...\");\n      socket.emit(\"pong\");\n    });\n\n    //socket.emit('joinCompanyRoom', { companyId: idEmpresa.toString(), clientId: 'reactClient' });\n    const clientId = `${userID}-${Date.now()}`;\n    socket.emit('joinCompanyRoom', {\n      companyId: objIdEmpresa.toString(),\n      clientId\n    });\n    const handleNovoPedido = async () => {\n      console.log(\"NOVO PEDIDO RECEBIDO!\");\n      const pedidosSimplesData = await fetchPedidosSimples();\n      const pedidosFinalizadosData = await fetchPedidosFinalizados();\n      const newPedidos = [...pedidosSimplesData, ...pedidosFinalizadosData];\n      setOriginalPedidos(newPedidos);\n      setPedidosSimples(pedidosSimplesData);\n      setPedidosFinalizados(pedidosFinalizadosData);\n    };\n    socket.on('novoPedido', handleNovoPedido);\n\n    // Limpeza do WebSocket\n    return () => {\n      socket.off('novoPedido');\n      socket.disconnect(); // 🔹 Fecha a conexão WebSocket ao sair da tela\n    };\n  }, [idEmpresa]); // Dependência para que o efeito seja disparado quando 'idEmpresa' mudar\n\n  useEffect(() => {\n    const filteredPedidos = originalPedidos.filter(dados => {\n      var _dados$id_pedido_coun;\n      return dados.nome_cliente.toLowerCase().includes(searchQuery.toLowerCase()) || ((_dados$id_pedido_coun = dados.id_pedido_counter) === null || _dados$id_pedido_coun === void 0 ? void 0 : _dados$id_pedido_coun.toString().includes(searchQuery));\n    } //||\n    //dados.id_pedido.toString().includes(searchQuery)\n    );\n    // Separa os pedidos filtrados em simples e finalizados\n    const pedidosSimplesFiltrados = filteredPedidos.filter(dados => dados.status_pedido === '1' || dados.status_pedido === '2' || dados.status_pedido === '3');\n    const pedidosFinalizadosFiltrados = filteredPedidos.filter(dados => dados.status_pedido === '4');\n\n    // Atualiza os estados de pedidosSimples e pedidosFinalizados com os pedidos filtrados\n    setPedidosSimples(pedidosSimplesFiltrados);\n    setPedidosFinalizados(pedidosFinalizadosFiltrados);\n  }, [searchQuery, originalPedidos]);\n  const handleCadastroPDV = () => {\n    navigate('/pdv');\n  };\n  const handleAddEntregador = (event, pedido) => {\n    event.stopPropagation();\n    setPedidoToAddEntregador(pedido);\n    setShowModalAddEntregador(!showModalAddEntregador);\n  };\n  const handleAvancar = async (event, _id, id_pedido, status_pedido) => {\n    event.stopPropagation();\n    setIsLoading(true); // Inicia o carregamento\n\n    let newStatus = \"\";\n    let finalizadoAt = \"\";\n    let response = null;\n    switch (status_pedido) {\n      case \"1\":\n        newStatus = \"2\";\n        break;\n      case \"2\":\n        newStatus = \"3\";\n        break;\n      case \"3\":\n        newStatus = \"4\";\n        //finalizadoAt = moment().local().format(\"DD/MM/YYYY\");\n        //finalizadoAt = momentTz().tz('America/Sao_Paulo').format(\"DD/MM/YYYY\");\n        finalizadoAt = new Date();\n        break;\n      default:\n        setIsLoading(false);\n        return;\n      // Se o status_pedido não é 1, 2, ou 3, termina a função\n    }\n    try {\n      // Unifica a chamada da API dependendo do caso\n      if (status_pedido === \"3\") {\n        response = await updateStatusPedidoFinalizado(userID, _id, id_pedido, newStatus, finalizadoAt);\n      } else {\n        response = await updateStatusPedido(userID, _id, id_pedido, newStatus);\n      }\n      setRefresh(true); // Atualiza o estado para forçar recarga dos dados\n      toast(response.data.msg, {\n        autoClose: 2000,\n        type: \"success\"\n      });\n    } catch (err) {\n      var _err$response, _err$response$data;\n      toast(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.msg) || \"An error occurred\", {\n        autoClose: 2000,\n        type: \"error\"\n      });\n    } finally {\n      setRefresh(true); // Atualiza o estado para forçar recarga dos dados\n      setIsLoading(false); // Finaliza o carregamento imediatamente após a conclusão ou falha da chamada da API\n    }\n  };\n\n  /*const totalValuePerOrder = pedidos.map((order) => {\r\n      const totalValue = order.itens.reduce((acc, item) => {\r\n          return acc + (item.valor * item.quantidade);\r\n      }, 0);\r\n      return { ...order, totalValue };\r\n  });*/\n\n  const handleEditTempoEntrega = async idToEdit => {\n    setEditTempoEntregaEmpresa(!showEditTempoEntregaEmpresa);\n    set_idEmpresaEdit(idEmpresa);\n    //setUserOptions(!showOptions);\n    /*const response = await getUser(idToEdit);\r\n    if(showEditTempoEntregaEmpresa){       \r\n        if (response.data.user.vinculo_empresa){                \r\n            const responseVinculo = await getVinculoEmpresa(idToEdit);                     \r\n            //console.log(\"TEM EMPRESA VINCULADA!\",responseVinculo.data.vinculo.id_empresa)\r\n            set_idEmpresaEdit(responseVinculo.data.vinculo.id_empresa);                \r\n        }    \r\n    }*/\n  };\n  const handleFilterAll = async () => {\n    setFilterIsLoading(true);\n    setFiltroSelecionado(\"all\");\n    await fetchDataAndSetPedidosForFilters(\"all\"); // Garantindo que os dados estão atualizados;              \n  };\n  const handleFilterDelivery = async () => {\n    setFilterIsLoading(true);\n    setFiltroSelecionado(\"delivery\");\n    await fetchDataAndSetPedidosForFilters(\"delivery\"); // Garantindo que os dados estão atualizados\n    //setPedidos(currentPedidos => currentPedidos.filter(pedido => pedido.entrega.tipo_entrega?.toLowerCase() === \"entrega\"));        \n  };\n  const handleFilterBalcao = async () => {\n    setFilterIsLoading(true);\n    setFiltroSelecionado(\"balcao\");\n    await fetchDataAndSetPedidosForFilters(\"balcao\"); // Garantindo que os dados estão atualizados\n    //setPedidos(currentPedidos => currentPedidos.filter(pedido => pedido.entrega.tipo_entrega?.toLowerCase() === \"retirada\"));\n  };\n  const handleFilterMesas = async () => {\n    setFilterIsLoading(true);\n    setFiltroSelecionado(\"mesas\");\n    await fetchDataAndSetPedidosForFilters(\"mesas\"); // Garantindo que os dados estão atualizados\n    //setPedidos(currentPedidos => currentPedidos.filter(pedido => pedido.entrega.tipo_entrega?.toLowerCase() === \"retirada\"));\n  };\n  const handleShowPedido = async (idPedidoToOpen, id_empresa) => {\n    setShowPedido(!showPedido);\n    const response = await getPedido(userID, id_empresa, vinculo_empresa, idPedidoToOpen);\n    if (showPedido) {\n      if (response.data.pedido) {\n        setInfoPedido(response.data.pedido[0]);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(PermissionGate, {\n      permissions: ['default'],\n      children: /*#__PURE__*/_jsxDEV(Teste, {\n        sidebar: sidebar,\n        children: [/*#__PURE__*/_jsxDEV(ModalEditTempoEntrega, {\n          setEditTempoEntregaEmpresa: setEditTempoEntregaEmpresa,\n          showEditTempoEntregaEmpresa: showEditTempoEntregaEmpresa,\n          setRefresh: setRefresh,\n          selectData: selectData,\n          setselectData: setselectData,\n          _idEmpresaEdit: _idEmpresaEdit,\n          tempoBalcaoMinBD: tempoBalcaoMinBD,\n          tempoBalcaoMaxBD: tempoBalcaoMaxBD,\n          tempoEntregaMinBD: tempoEntregaMinBD,\n          tempoEntregaMaxBD: tempoEntregaMaxBD,\n          tipoImpressao: tipoImpressao\n          //usernameEdit={usernameEdit} \n          //emailEdit={emailEdit} \n          //roleEdit={roleEdit}    \n          //editPerfil={false}\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ModalPedido, {\n          setShowPedido: setShowPedido,\n          showPedido: showPedido,\n          setRefresh: setRefresh,\n          infoPedido: infoPedido,\n          setInfoPedido: setInfoPedido,\n          handleAvancar: handleAvancar,\n          userID: userID,\n          hasCancelPassword: hasCancelPassword\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(ModalAddEntregador, {\n          showModalAddEntregador: showModalAddEntregador,\n          setShowModalAddEntregador: setShowModalAddEntregador,\n          pedidoToAddEntregador: pedidoToAddEntregador,\n          objIdEmpresa: objIdEmpresa,\n          userID: userID,\n          setRefresh: setRefresh\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"m-5 m5-home\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-header\",\n              style: {\n                marginBottom: \"0px\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"title\",\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  children: \"Meus Pedidos\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"header-mobile-kanban\",\n              style: {\n                display: 'inline-flex',\n                width: '100%',\n                justifyContent: 'space-between',\n                padding: '10px 30px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'inline-flex',\n                  flexDirection: \"column\",\n                  justifyContent: 'space-between',\n                  justifyItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `filter-buttons-container ${isMobile ? 'mobile' : 'desktop'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    onClick: () => handleFilterAll(),\n                    className: `filter-button filter-button-todos ${filtroSelecionado === \"all\" ? 'active' : ''} ${isMobile ? 'mobile' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"filter-button-text\",\n                      children: \"Todos\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    onClick: () => handleFilterDelivery(),\n                    className: `filter-button filter-button-delivery ${filtroSelecionado === \"delivery\" ? 'active' : ''} ${isMobile ? 'mobile' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"filter-button-text\",\n                      children: [/*#__PURE__*/_jsxDEV(GiFullMotorcycleHelmet, {\n                        className: \"filter-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 673,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Delivery\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 674,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 672,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 671,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    onClick: () => handleFilterBalcao(),\n                    className: `filter-button filter-button-balcao ${filtroSelecionado === \"balcao\" ? 'active' : ''} ${isMobile ? 'mobile' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"filter-button-text\",\n                      children: [/*#__PURE__*/_jsxDEV(MdStorefront, {\n                        className: \"filter-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 679,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Balc\\xE3o\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 680,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 678,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 677,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    onClick: () => handleFilterMesas(),\n                    className: `filter-button filter-button-mesas ${filtroSelecionado === \"mesas\" ? 'active' : ''} ${isMobile ? 'mobile' : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"filter-button-text\",\n                      children: [/*#__PURE__*/_jsxDEV(MdTableBar, {\n                        className: \"filter-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 685,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Mesas\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 686,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 683,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"input-box-list home\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      width: '100%',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      style: {\n                        width: '100%'\n                      },\n                      value: searchQuery,\n                      onChange: e => setSearchQuery(e.target.value),\n                      className: \"input-fieldClienteOuPedido\",\n                      placeholder: \"Buscar pelo cliente ou n\\xBA do pedido\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"icon\",\n                      children: /*#__PURE__*/_jsxDEV(FiIcons.FiSearch, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 695,\n                        columnNumber: 65\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 695,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 692,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'inline-flex'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"div-buttons\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"continue-button\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: handleCadastroPDV,\n                      children: [/*#__PURE__*/_jsxDEV(AiIcons.AiOutlinePlusCircle, {\n                        style: {\n                          marginRight: \"5px\",\n                          fontSize: \"22px\",\n                          marginBottom: \"2px\"\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 704,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                        children: \"Novo Pedido\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 704,\n                        columnNumber: 150\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 703,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 25\n          }, this), window.innerWidth > 780 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"Ordercolumn\",\n            style: {\n              display: 'inline-flex',\n              height: '674px',\n              boxShadow: '0px 0px 5px 2px rgb(0,0,0,.1)'\n            },\n            children: [filterIsLoading ? /*#__PURE__*/_jsxDEV(Loading, {\n              type: \"spin\",\n              color: \"rgb(49, 140, 213)\",\n              height: 56,\n              width: 56,\n              className: \"loadingSpinHomeFilters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 37\n            }, this) : null, /*#__PURE__*/_jsxDEV(Ordercolumn, {\n              typecolumn: \"analysis\",\n              className: \"column\"\n              //style={{display:'table-row'}}\n              ,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"containerAnalise\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"header-column-analise\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"header-column--align\",\n                    children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Em an\\xE1lise\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 737,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: pedidosAnalise.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 739,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"blocoAnalise\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"column-container column-container--first\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"titleOptEmp\",\n                          children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                            children: \"Balc\\xE3o: \"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 748,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: [tempoBalcaoMinBD, \" a \", tempoBalcaoMaxBD, \" min\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 749,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 747,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"titleOptEmp\",\n                          children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                            children: \"Delivery: \"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 752,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: [tempoEntregaMinBD, \" a \", tempoEntregaMaxBD, \" min\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 753,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 751,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"titleOptEmp\",\n                          children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                            children: \"Aceite autom\\xE1tico: \"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 756,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: tipoImpressao == 'manual' ? 'Desativado' : tipoImpressao == 'automatico' ? 'Ativado' : 'Não definido'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 757,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 755,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 746,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-edit\",\n                        onClick: () => handleEditTempoEntrega(userID),\n                        style: {\n                          cursor: \"pointer\"\n                        },\n                        children: [\" \", \"Editar\", \" \"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 760,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 745,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 744,\n                    columnNumber: 45\n                  }, this), pedidosAnalise.length == 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"column-container column-container--first\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          marginBottom: 0,\n                          justifyContent: 'center',\n                          textAlign: 'center',\n                          display: 'flex'\n                        },\n                        children: \"Nenhum pedido no momento.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 770,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          marginBottom: 0,\n                          justifyContent: 'center',\n                          textAlign: 'center',\n                          display: 'flex'\n                        },\n                        children: \"Compartilhe os seus links nas redes sociais e receba pedidos!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 771,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 769,\n                      columnNumber: 57\n                    }, this)\n                  }, Math.random(), false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 768,\n                    columnNumber: 53\n                  }, this) :\n                  //pedidos && pedidos.map((dados, i) => (\n                  pedidosSimples && pedidosSimples.map((dados, i) => {\n                    var _dados$entrega, _dados$id_pedido_coun2, _dados$entrega2, _dados$entrega3, _dados$entrega4, _dados$entrega4$ender, _dados$entrega5, _dados$entrega5$ender, _dados$entrega6, _dados$entrega6$ender, _dados$entrega7, _dados$entrega7$ender, _dados$entrega8, _dados$entrega8$ender;\n                    return dados.status_pedido == '1' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bloco-pedidos\",\n                      onClick: () => handleShowPedido(dados._id, dados.id_empresa),\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"pedido-time\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"tag-pedido\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"icon-pedido\",\n                            children: ((_dados$entrega = dados.entrega) === null || _dados$entrega === void 0 ? void 0 : _dados$entrega.tipo_entrega) == \"Retirada\" ? /*#__PURE__*/_jsxDEV(FaStore, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 784,\n                              columnNumber: 122\n                            }, this) : /*#__PURE__*/_jsxDEV(FaMotorcycle, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 784,\n                              columnNumber: 136\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 783,\n                            columnNumber: 73\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"bold\",\n                            children: [\"Pedido #\", (_dados$id_pedido_coun2 = dados.id_pedido_counter) !== null && _dados$id_pedido_coun2 !== void 0 ? _dados$id_pedido_coun2 : dados.id_pedido]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 786,\n                            columnNumber: 73\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 782,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"time-container\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"time-div\",\n                            children: [/*#__PURE__*/_jsxDEV(FaRegClock, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 791,\n                              columnNumber: 77\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: moment.parseZone(dados.createdAt).local().format(\"HH:mm\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 792,\n                              columnNumber: 77\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 790,\n                            columnNumber: 73\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 789,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"cliente-total\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bloco\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.nome_cliente\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 800,\n                              columnNumber: 77\n                            }, this), dados.counter_qtd_pedido > 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"label-recorrencia-pedidoMoreThanOne\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"caixaNumero\",\n                                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    fontSize: 10\n                                  },\n                                  children: dados.counter_qtd_pedido\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 804,\n                                  columnNumber: 89\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 803,\n                                columnNumber: 85\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 802,\n                              columnNumber: 81\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"label-recorrencia-pedido\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"caixaNumero\",\n                                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    fontSize: 10\n                                  },\n                                  children: \"1\\xBA\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 810,\n                                  columnNumber: 89\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 809,\n                                columnNumber: 85\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 808,\n                              columnNumber: 81\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 799,\n                            columnNumber: 73\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.celular_cliente\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 816,\n                              columnNumber: 77\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 815,\n                            columnNumber: 73\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 798,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bloco\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto bold\",\n                              children: \"Total:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 823,\n                              columnNumber: 77\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto bold\",\n                              children: [\"R$ \", dados.valor_total && dados.valor_total.toFixed(2).toString().replace('.', ',')]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 824,\n                              columnNumber: 77\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 822,\n                            columnNumber: 73\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: \"Pagamento:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 829,\n                              columnNumber: 77\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.tipo_pagamento\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 830,\n                              columnNumber: 77\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 828,\n                            columnNumber: 73\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 821,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 797,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bloco-entrega\",\n                        children: ((_dados$entrega2 = dados.entrega) === null || _dados$entrega2 === void 0 ? void 0 : _dados$entrega2.tipo_entrega) == \"Retirada\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-entrega\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bloco-footer\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"pin\",\n                              style: {\n                                display: 'flex',\n                                marginTop: 1\n                              },\n                              children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                className: \"pin\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 840,\n                                columnNumber: 85\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 839,\n                              columnNumber: 81\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: \"Retirada no Local\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 842,\n                              columnNumber: 81\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 838,\n                            columnNumber: 77\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 837,\n                          columnNumber: 73\n                        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-entrega\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bloco-footer\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"pin\",\n                              children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                className: \"pin\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 849,\n                                columnNumber: 85\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 848,\n                              columnNumber: 81\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: ((_dados$entrega3 = dados.entrega) === null || _dados$entrega3 === void 0 ? void 0 : _dados$entrega3.endereco) && `${(_dados$entrega4 = dados.entrega) === null || _dados$entrega4 === void 0 ? void 0 : (_dados$entrega4$ender = _dados$entrega4.endereco) === null || _dados$entrega4$ender === void 0 ? void 0 : _dados$entrega4$ender.rua}, ${(_dados$entrega5 = dados.entrega) === null || _dados$entrega5 === void 0 ? void 0 : (_dados$entrega5$ender = _dados$entrega5.endereco) === null || _dados$entrega5$ender === void 0 ? void 0 : _dados$entrega5$ender.numero} \n                                                                        ${((_dados$entrega6 = dados.entrega) === null || _dados$entrega6 === void 0 ? void 0 : (_dados$entrega6$ender = _dados$entrega6.endereco) === null || _dados$entrega6$ender === void 0 ? void 0 : _dados$entrega6$ender.complemento) && \" - \" + ((_dados$entrega7 = dados.entrega) === null || _dados$entrega7 === void 0 ? void 0 : (_dados$entrega7$ender = _dados$entrega7.endereco) === null || _dados$entrega7$ender === void 0 ? void 0 : _dados$entrega7$ender.complemento)}, ${(_dados$entrega8 = dados.entrega) === null || _dados$entrega8 === void 0 ? void 0 : (_dados$entrega8$ender = _dados$entrega8.endereco) === null || _dados$entrega8$ender === void 0 ? void 0 : _dados$entrega8$ender.bairro}`\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 851,\n                              columnNumber: 81\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 847,\n                            columnNumber: 77\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bloco\",\n                            onClick: event => handleAddEntregador(event, dados),\n                            children: dados.entregador ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto underline\",\n                              children: dados.entregador.name\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 858,\n                              columnNumber: 85\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto underline\",\n                              children: \"Escolher Entregador\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 860,\n                              columnNumber: 85\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 856,\n                            columnNumber: 77\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 846,\n                          columnNumber: 73\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 835,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginTop: 10\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"button-avancar-pedido\",\n                          children: /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: event => handleAvancar(event, dados._id, dados.id_pedido, dados.status_pedido),\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                                children: \"Avan\\xE7ar Pedido\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 871,\n                                columnNumber: 81\n                              }, this), /*#__PURE__*/_jsxDEV(AiIcons.AiOutlineArrowRight, {\n                                style: {\n                                  marginLeft: \"2px\",\n                                  fontSize: \"22px\",\n                                  marginBottom: \"2px\",\n                                  fontColor: 'white'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 872,\n                                columnNumber: 81\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 870,\n                              columnNumber: 77\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 869,\n                            columnNumber: 73\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 868,\n                          columnNumber: 69\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 867,\n                        columnNumber: 65\n                      }, this)]\n                    }, Math.random(), true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 61\n                    }, this) : null;\n                  })]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Ordercolumn, {\n              typecolumn: \"production\",\n              className: \"column\"\n              //style={{display:'table-row'}}\n              ,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"containerProducao\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"header-column-producao\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"header-column--align\",\n                    children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Em produ\\xE7\\xE3o\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 912,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: pedidosProducao.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"blocoProducao\",\n                  children: pedidosProducao.length == 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"column-container column-container--first\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          marginBottom: 0,\n                          justifyContent: 'center',\n                          textAlign: 'center',\n                          display: 'flex'\n                        },\n                        children: \"Nenhum pedido no momento.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 922,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          marginBottom: 0,\n                          justifyContent: 'center',\n                          textAlign: 'center',\n                          display: 'flex'\n                        },\n                        children: \"Receba pedidos e visualize os que est\\xE3o em produ\\xE7\\xE3o.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 923,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 921,\n                      columnNumber: 53\n                    }, this)\n                  }, Math.random(), false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 920,\n                    columnNumber: 49\n                  }, this) : pedidosSimples && pedidosSimples.map((dados, i) => {\n                    var _dados$entrega9, _dados$id_pedido_coun3, _dados$entrega0, _dados$entrega1, _dados$entrega1$ender, _dados$entrega10, _dados$entrega10$ende, _dados$entrega11, _dados$entrega11$ende, _dados$entrega12, _dados$entrega12$ende, _dados$entrega13, _dados$entrega13$ende;\n                    return dados.status_pedido == '2' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bloco-pedidos\",\n                      onClick: () => handleShowPedido(dados._id, dados.id_empresa),\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"pedido-time\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"tag-pedido\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"icon-pedido\",\n                            children: ((_dados$entrega9 = dados.entrega) === null || _dados$entrega9 === void 0 ? void 0 : _dados$entrega9.tipo_entrega) == \"Retirada\" ? /*#__PURE__*/_jsxDEV(FaStore, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 935,\n                              columnNumber: 118\n                            }, this) : /*#__PURE__*/_jsxDEV(FaMotorcycle, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 935,\n                              columnNumber: 132\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 934,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"bold\",\n                            children: [\"Pedido #\", (_dados$id_pedido_coun3 = dados.id_pedido_counter) !== null && _dados$id_pedido_coun3 !== void 0 ? _dados$id_pedido_coun3 : dados.id_pedido]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 937,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 933,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"time-container\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"time-div\",\n                            children: [/*#__PURE__*/_jsxDEV(FaRegClock, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 942,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: moment.parseZone(dados.createdAt).local().format(\"HH:mm\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 943,\n                              columnNumber: 73\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 941,\n                            columnNumber: 69\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 940,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 932,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"cliente-total\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bloco\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.nome_cliente\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 951,\n                              columnNumber: 73\n                            }, this), dados.counter_qtd_pedido > 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"label-recorrencia-pedidoMoreThanOne\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"caixaNumero\",\n                                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    fontSize: 10\n                                  },\n                                  children: dados.counter_qtd_pedido\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 955,\n                                  columnNumber: 85\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 954,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 953,\n                              columnNumber: 77\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"label-recorrencia-pedido\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"caixaNumero\",\n                                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    fontSize: 10\n                                  },\n                                  children: \"1\\xBA\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 961,\n                                  columnNumber: 85\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 960,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 959,\n                              columnNumber: 77\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 950,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.celular_cliente\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 967,\n                              columnNumber: 73\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 966,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 949,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bloco\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto bold\",\n                              children: \"Total:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 974,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto bold\",\n                              children: [\"R$ \", dados.valor_total && dados.valor_total.toFixed(2).toString().replace('.', ',')]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 975,\n                              columnNumber: 73\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 973,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: \"Pagamento:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 980,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.tipo_pagamento\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 981,\n                              columnNumber: 73\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 979,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 972,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 948,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bloco-entrega\",\n                        children: dados.createdBy.toLowerCase().includes(\"mesa\") ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-entrega\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bloco-footer\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"pin\",\n                              style: {\n                                display: 'flex',\n                                marginTop: 1\n                              },\n                              children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                className: \"pin\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 992,\n                                columnNumber: 85\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 991,\n                              columnNumber: 81\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.createdBy\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 994,\n                              columnNumber: 81\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 990,\n                            columnNumber: 77\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 989,\n                          columnNumber: 73\n                        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: ((_dados$entrega0 = dados.entrega) === null || _dados$entrega0 === void 0 ? void 0 : _dados$entrega0.tipo_entrega) == \"Retirada\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-entrega\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"bloco-footer\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"pin\",\n                                style: {\n                                  display: 'flex',\n                                  marginTop: 1\n                                },\n                                children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                  className: \"pin\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1003,\n                                  columnNumber: 93\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1002,\n                                columnNumber: 89\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto\",\n                                children: \"Retirada no Local\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1005,\n                                columnNumber: 89\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1001,\n                              columnNumber: 85\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1000,\n                            columnNumber: 81\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-entrega\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"bloco-footer\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"pin\",\n                                children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                  className: \"pin\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1012,\n                                  columnNumber: 93\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1011,\n                                columnNumber: 89\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto\",\n                                children: dados.entrega && `${(_dados$entrega1 = dados.entrega) === null || _dados$entrega1 === void 0 ? void 0 : (_dados$entrega1$ender = _dados$entrega1.endereco) === null || _dados$entrega1$ender === void 0 ? void 0 : _dados$entrega1$ender.rua}, ${(_dados$entrega10 = dados.entrega) === null || _dados$entrega10 === void 0 ? void 0 : (_dados$entrega10$ende = _dados$entrega10.endereco) === null || _dados$entrega10$ende === void 0 ? void 0 : _dados$entrega10$ende.numero} \n                                                                                ${((_dados$entrega11 = dados.entrega) === null || _dados$entrega11 === void 0 ? void 0 : (_dados$entrega11$ende = _dados$entrega11.endereco) === null || _dados$entrega11$ende === void 0 ? void 0 : _dados$entrega11$ende.complemento) && \" - \" + ((_dados$entrega12 = dados.entrega) === null || _dados$entrega12 === void 0 ? void 0 : (_dados$entrega12$ende = _dados$entrega12.endereco) === null || _dados$entrega12$ende === void 0 ? void 0 : _dados$entrega12$ende.complemento)}, ${(_dados$entrega13 = dados.entrega) === null || _dados$entrega13 === void 0 ? void 0 : (_dados$entrega13$ende = _dados$entrega13.endereco) === null || _dados$entrega13$ende === void 0 ? void 0 : _dados$entrega13$ende.bairro}`\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1014,\n                                columnNumber: 89\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1010,\n                              columnNumber: 85\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"bloco\",\n                              onClick: event => handleAddEntregador(event, dados),\n                              children: dados.entregador ? /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto underline\",\n                                children: dados.entregador.name\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1021,\n                                columnNumber: 93\n                              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto underline\",\n                                children: \"Escolher Entregador\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1023,\n                                columnNumber: 93\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1019,\n                              columnNumber: 85\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1009,\n                            columnNumber: 81\n                          }, this)\n                        }, void 0, false)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 986,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginTop: 10\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"button-avancar-pedido\",\n                          children: /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: event => handleAvancar(event, dados._id, dados.id_pedido, dados.status_pedido),\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                                children: \"Avan\\xE7ar Pedido\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1037,\n                                columnNumber: 77\n                              }, this), /*#__PURE__*/_jsxDEV(AiIcons.AiOutlineArrowRight, {\n                                style: {\n                                  marginLeft: \"2px\",\n                                  fontSize: \"22px\",\n                                  marginBottom: \"2px\",\n                                  fontColor: 'white'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1038,\n                                columnNumber: 77\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1036,\n                              columnNumber: 73\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1035,\n                            columnNumber: 69\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1033,\n                          columnNumber: 65\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1032,\n                        columnNumber: 61\n                      }, this)]\n                    }, Math.random(), true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 931,\n                      columnNumber: 57\n                    }, this) : null;\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 917,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Ordercolumn, {\n              \"_ngcontent-ng-c2041748172\": \"\",\n              typecolumn: \"ready\",\n              className: \"column\",\n              \"_nghost-ng-c2960941283\": \"\",\n              style: {\n                display: 'table-row'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"containerPronto\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"header-column-pronto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"header-column--align\",\n                    style: {\n                      display: 'inline-flex'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Prontos para entrega\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1068,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1067,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: pedidosEntrega.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1066,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"blocoPronto\",\n                  children: pedidosEntrega.length == 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"column-container column-container--first\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          marginBottom: 0,\n                          justifyContent: 'center',\n                          textAlign: 'center',\n                          display: 'flex'\n                        },\n                        children: \"Nenhum pedido no momento.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1077,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          marginBottom: 0,\n                          justifyContent: 'center',\n                          textAlign: 'center',\n                          display: 'flex'\n                        },\n                        children: \"Receba pedidos e visualize os que est\\xE3o prontos para entrega.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1078,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1076,\n                      columnNumber: 53\n                    }, this)\n                  }, Math.random(), false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1075,\n                    columnNumber: 49\n                  }, this) : pedidosSimples && pedidosSimples.map((dados, i) => {\n                    var _dados$entrega14, _dados$id_pedido_coun4, _dados$entrega15, _dados$entrega16, _dados$entrega16$ende, _dados$entrega17, _dados$entrega17$ende, _dados$entrega18, _dados$entrega18$ende, _dados$entrega19, _dados$entrega19$ende, _dados$entrega20, _dados$entrega20$ende;\n                    return dados.status_pedido == '3' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bloco-pedidos\",\n                      onClick: () => handleShowPedido(dados._id, dados.id_empresa),\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"pedido-time\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"tag-pedido\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"icon-pedido\",\n                            children: ((_dados$entrega14 = dados.entrega) === null || _dados$entrega14 === void 0 ? void 0 : _dados$entrega14.tipo_entrega) == \"Retirada\" ? /*#__PURE__*/_jsxDEV(FaStore, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1090,\n                              columnNumber: 118\n                            }, this) : /*#__PURE__*/_jsxDEV(FaMotorcycle, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1090,\n                              columnNumber: 132\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1089,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"bold\",\n                            children: [\"Pedido #\", (_dados$id_pedido_coun4 = dados.id_pedido_counter) !== null && _dados$id_pedido_coun4 !== void 0 ? _dados$id_pedido_coun4 : dados.id_pedido]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1092,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1088,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"time-container\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"time-div\",\n                            children: [/*#__PURE__*/_jsxDEV(FaRegClock, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1097,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: moment.parseZone(dados.createdAt).local().format(\"HH:mm\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1098,\n                              columnNumber: 73\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1096,\n                            columnNumber: 69\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1095,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1087,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"cliente-total\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bloco\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.nome_cliente\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1106,\n                              columnNumber: 73\n                            }, this), dados.counter_qtd_pedido > 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"label-recorrencia-pedidoMoreThanOne\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"caixaNumero\",\n                                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    fontSize: 10\n                                  },\n                                  children: dados.counter_qtd_pedido\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1110,\n                                  columnNumber: 85\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1109,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1108,\n                              columnNumber: 77\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"label-recorrencia-pedido\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"caixaNumero\",\n                                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    fontSize: 10\n                                  },\n                                  children: \"1\\xBA\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1116,\n                                  columnNumber: 85\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1115,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1114,\n                              columnNumber: 77\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1105,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.celular_cliente\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1122,\n                              columnNumber: 73\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1121,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1104,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bloco\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto bold\",\n                              children: \"Total:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1129,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto bold\",\n                              children: [\"R$ \", dados.valor_total && dados.valor_total.toFixed(2).toString().replace('.', ',')]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1130,\n                              columnNumber: 73\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1128,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: \"Pagamento:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1135,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.tipo_pagamento\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1136,\n                              columnNumber: 73\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1134,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1127,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1103,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bloco-entrega\",\n                        children: dados.createdBy.toLowerCase().includes(\"mesa\") ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-entrega\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bloco-footer\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"pin\",\n                              style: {\n                                display: 'flex',\n                                marginTop: 1\n                              },\n                              children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                className: \"pin\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1147,\n                                columnNumber: 85\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1146,\n                              columnNumber: 81\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.createdBy\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1149,\n                              columnNumber: 81\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1145,\n                            columnNumber: 77\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1144,\n                          columnNumber: 73\n                        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: ((_dados$entrega15 = dados.entrega) === null || _dados$entrega15 === void 0 ? void 0 : _dados$entrega15.tipo_entrega) == \"Retirada\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-entrega\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"bloco-footer\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"pin\",\n                                style: {\n                                  display: 'flex',\n                                  marginTop: 1\n                                },\n                                children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                  className: \"pin\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1158,\n                                  columnNumber: 93\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1157,\n                                columnNumber: 89\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto\",\n                                children: \"Retirada no Local\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1160,\n                                columnNumber: 89\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1156,\n                              columnNumber: 85\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1155,\n                            columnNumber: 81\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-entrega\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"bloco-footer\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"pin\",\n                                children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                  className: \"pin\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1167,\n                                  columnNumber: 93\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1166,\n                                columnNumber: 89\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto\",\n                                children: dados.entrega && `${(_dados$entrega16 = dados.entrega) === null || _dados$entrega16 === void 0 ? void 0 : (_dados$entrega16$ende = _dados$entrega16.endereco) === null || _dados$entrega16$ende === void 0 ? void 0 : _dados$entrega16$ende.rua}, ${(_dados$entrega17 = dados.entrega) === null || _dados$entrega17 === void 0 ? void 0 : (_dados$entrega17$ende = _dados$entrega17.endereco) === null || _dados$entrega17$ende === void 0 ? void 0 : _dados$entrega17$ende.numero} \n                                                                                ${((_dados$entrega18 = dados.entrega) === null || _dados$entrega18 === void 0 ? void 0 : (_dados$entrega18$ende = _dados$entrega18.endereco) === null || _dados$entrega18$ende === void 0 ? void 0 : _dados$entrega18$ende.complemento) && \" - \" + ((_dados$entrega19 = dados.entrega) === null || _dados$entrega19 === void 0 ? void 0 : (_dados$entrega19$ende = _dados$entrega19.endereco) === null || _dados$entrega19$ende === void 0 ? void 0 : _dados$entrega19$ende.complemento)}, ${(_dados$entrega20 = dados.entrega) === null || _dados$entrega20 === void 0 ? void 0 : (_dados$entrega20$ende = _dados$entrega20.endereco) === null || _dados$entrega20$ende === void 0 ? void 0 : _dados$entrega20$ende.bairro}`\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1169,\n                                columnNumber: 89\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1165,\n                              columnNumber: 85\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"bloco\",\n                              onClick: event => handleAddEntregador(event, dados),\n                              children: dados.entregador ? /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto underline\",\n                                children: dados.entregador.name\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1176,\n                                columnNumber: 93\n                              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto underline\",\n                                children: \"Escolher Entregador\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1178,\n                                columnNumber: 93\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1174,\n                              columnNumber: 85\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1164,\n                            columnNumber: 81\n                          }, this)\n                        }, void 0, false)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1141,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginTop: 10\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"button-avancar-pedido\",\n                          children: /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: event => handleAvancar(event, dados._id, dados.id_pedido, dados.status_pedido),\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                                children: \"Avan\\xE7ar Pedido\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1192,\n                                columnNumber: 77\n                              }, this), /*#__PURE__*/_jsxDEV(AiIcons.AiOutlineArrowRight, {\n                                style: {\n                                  marginLeft: \"2px\",\n                                  fontSize: \"22px\",\n                                  marginBottom: \"2px\",\n                                  fontColor: 'white'\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1193,\n                                columnNumber: 77\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1191,\n                              columnNumber: 73\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1190,\n                            columnNumber: 69\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1188,\n                          columnNumber: 65\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1187,\n                        columnNumber: 61\n                      }, this)]\n                    }, Math.random(), true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1086,\n                      columnNumber: 57\n                    }, this) : null;\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1073,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Ordercolumn, {\n              \"_ngcontent-ng-c2041748172\": \"\",\n              typecolumn: \"ready\",\n              className: \"column\",\n              \"_nghost-ng-c2960941283\": \"\",\n              style: {\n                display: 'table-row'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"containerFinalizado\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"header-column-finalizado\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"header-column--align\",\n                    style: {\n                      display: 'inline-flex'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"Finalizados\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1222,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"header-column-clear\",\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        marginLeft: 6\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"pedeja-icon\", {\n                        iconname: \"check-circle\",\n                        iconsize: 18,\n                        pedejatooltip: \"Finalizar todos os pedidos n\\xE3o relacionados a mesa\",\n                        iconcolor: \"#FFFFFF\",\n                        style: {\n                          height: 18,\n                          display: 'flex',\n                          alignItems: 'center'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          width: 18,\n                          height: 18,\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          stroke: \"#FFFFFF\",\n                          strokeWidth: 3,\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          className: \"feather feather-check-circle\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M22 11.08V12a10 10 0 1 1-5.93-9.14\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1243,\n                            columnNumber: 61\n                          }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                            points: \"22 4 12 14.01 9 11.01\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1244,\n                            columnNumber: 61\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1231,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1224,\n                        columnNumber: 53\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1223,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1221,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: pedidosFinalizado.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1250,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1220,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"blocoFinalizado\",\n                  children: pedidosFinalizado.length == 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"column-container column-container--first\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          marginBottom: 0,\n                          justifyContent: 'center',\n                          textAlign: 'center',\n                          display: 'flex'\n                        },\n                        children: \"Nenhum pedido no momento.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1257,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          marginBottom: 0,\n                          justifyContent: 'center',\n                          textAlign: 'center',\n                          display: 'flex'\n                        },\n                        children: \"Receba pedidos e visualize os finalizados.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1258,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1256,\n                      columnNumber: 53\n                    }, this)\n                  }, Math.random(), false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1255,\n                    columnNumber: 49\n                  }, this) : pedidosFinalizados && pedidosFinalizados.map((dados, i) => {\n                    var _dados$entrega21, _dados$id_pedido_coun5, _dados$entrega22, _dados$entrega23, _dados$entrega23$ende, _dados$entrega24, _dados$entrega24$ende, _dados$entrega25, _dados$entrega25$ende, _dados$entrega26, _dados$entrega26$ende, _dados$entrega27, _dados$entrega27$ende;\n                    return dados.status_pedido == '4' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bloco-pedidos\",\n                      onClick: () => handleShowPedido(dados._id, dados.id_empresa),\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"pedido-time\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"tag-pedido\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"icon-pedido\",\n                            children: ((_dados$entrega21 = dados.entrega) === null || _dados$entrega21 === void 0 ? void 0 : _dados$entrega21.tipo_entrega) == \"Retirada\" ? /*#__PURE__*/_jsxDEV(FaStore, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1270,\n                              columnNumber: 118\n                            }, this) : /*#__PURE__*/_jsxDEV(FaMotorcycle, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1270,\n                              columnNumber: 132\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1269,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"bold\",\n                            children: [\"Pedido #\", (_dados$id_pedido_coun5 = dados.id_pedido_counter) !== null && _dados$id_pedido_coun5 !== void 0 ? _dados$id_pedido_coun5 : dados.id_pedido]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1272,\n                            columnNumber: 69\n                          }, this), dados.cancelado && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"divSpanCancelado\",\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"CANCELADO\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1275,\n                              columnNumber: 77\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1274,\n                            columnNumber: 73\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1268,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"time-container\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"time-div\",\n                            children: [/*#__PURE__*/_jsxDEV(FaRegClock, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1281,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: moment.parseZone(dados.createdAt).local().format(\"HH:mm\")\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1282,\n                              columnNumber: 73\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1280,\n                            columnNumber: 69\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1279,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1267,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"cliente-total\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bloco\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.nome_cliente\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1290,\n                              columnNumber: 73\n                            }, this), dados.counter_qtd_pedido > 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"label-recorrencia-pedidoMoreThanOne\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"caixaNumero\",\n                                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    fontSize: 10\n                                  },\n                                  children: dados.counter_qtd_pedido\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1294,\n                                  columnNumber: 85\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1293,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1292,\n                              columnNumber: 77\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"label-recorrencia-pedido\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"caixaNumero\",\n                                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                  style: {\n                                    fontSize: 10\n                                  },\n                                  children: \"1\\xBA\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1300,\n                                  columnNumber: 85\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1299,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1298,\n                              columnNumber: 77\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1289,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.celular_cliente\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1306,\n                              columnNumber: 73\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1305,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1288,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bloco\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto bold\",\n                              children: \"Total:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1313,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto bold\",\n                              children: [\"R$ \", dados.valor_total && dados.valor_total.toFixed(2).toString().replace('.', ',')]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1314,\n                              columnNumber: 73\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1312,\n                            columnNumber: 69\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"linha\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: \"Pagamento:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1319,\n                              columnNumber: 73\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.tipo_pagamento\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1320,\n                              columnNumber: 73\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1318,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1311,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1287,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bloco-entrega\",\n                        children: dados.createdBy.toLowerCase().includes(\"mesa\") ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-entrega\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bloco-footer\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"pin\",\n                              style: {\n                                display: 'flex',\n                                marginTop: 1\n                              },\n                              children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                className: \"pin\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1331,\n                                columnNumber: 85\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1330,\n                              columnNumber: 81\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"texto\",\n                              children: dados.createdBy\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1333,\n                              columnNumber: 81\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1329,\n                            columnNumber: 77\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1328,\n                          columnNumber: 73\n                        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: ((_dados$entrega22 = dados.entrega) === null || _dados$entrega22 === void 0 ? void 0 : _dados$entrega22.tipo_entrega) == \"Retirada\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-entrega\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"bloco-footer\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"pin\",\n                                style: {\n                                  display: 'flex',\n                                  marginTop: 1\n                                },\n                                children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                  className: \"pin\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1342,\n                                  columnNumber: 93\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1341,\n                                columnNumber: 89\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto\",\n                                children: \"Retirada no Local\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1344,\n                                columnNumber: 89\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1340,\n                              columnNumber: 85\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1339,\n                            columnNumber: 81\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-entrega\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"bloco-footer\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"pin\",\n                                children: /*#__PURE__*/_jsxDEV(IoMdPin, {\n                                  className: \"pin\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1351,\n                                  columnNumber: 93\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1350,\n                                columnNumber: 89\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto\",\n                                children: dados.entrega && `${(_dados$entrega23 = dados.entrega) === null || _dados$entrega23 === void 0 ? void 0 : (_dados$entrega23$ende = _dados$entrega23.endereco) === null || _dados$entrega23$ende === void 0 ? void 0 : _dados$entrega23$ende.rua}, ${(_dados$entrega24 = dados.entrega) === null || _dados$entrega24 === void 0 ? void 0 : (_dados$entrega24$ende = _dados$entrega24.endereco) === null || _dados$entrega24$ende === void 0 ? void 0 : _dados$entrega24$ende.numero} \n                                                                                ${((_dados$entrega25 = dados.entrega) === null || _dados$entrega25 === void 0 ? void 0 : (_dados$entrega25$ende = _dados$entrega25.endereco) === null || _dados$entrega25$ende === void 0 ? void 0 : _dados$entrega25$ende.complemento) && \" - \" + ((_dados$entrega26 = dados.entrega) === null || _dados$entrega26 === void 0 ? void 0 : (_dados$entrega26$ende = _dados$entrega26.endereco) === null || _dados$entrega26$ende === void 0 ? void 0 : _dados$entrega26$ende.complemento)}, ${(_dados$entrega27 = dados.entrega) === null || _dados$entrega27 === void 0 ? void 0 : (_dados$entrega27$ende = _dados$entrega27.endereco) === null || _dados$entrega27$ende === void 0 ? void 0 : _dados$entrega27$ende.bairro}`\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1353,\n                                columnNumber: 89\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1349,\n                              columnNumber: 85\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"bloco\",\n                              onClick: event => handleAddEntregador(event, dados),\n                              children: dados.entregador ? /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto underline\",\n                                children: dados.entregador.name\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1360,\n                                columnNumber: 93\n                              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"texto underline\",\n                                children: \"Escolher Entregador\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1362,\n                                columnNumber: 93\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1358,\n                              columnNumber: 85\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1348,\n                            columnNumber: 81\n                          }, this)\n                        }, void 0, false)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1325,\n                        columnNumber: 61\n                      }, this)]\n                    }, Math.random(), true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1266,\n                      columnNumber: 57\n                    }, this) : null;\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1253,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1219,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1212,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(MobileKanban, {\n            tempoBalcaoMinBD: tempoBalcaoMinBD,\n            tempoBalcaoMaxBD: tempoBalcaoMaxBD,\n            tempoEntregaMinBD: tempoEntregaMinBD,\n            tempoEntregaMaxBD: tempoEntregaMaxBD,\n            tipoImpressao: tipoImpressao,\n            pedidosAnalise: pedidosAnalise,\n            pedidosFinalizados: pedidosFinalizados,\n            pedidosSimples: pedidosSimples,\n            pedidosProducao: pedidosProducao,\n            pedidosEntrega: pedidosEntrega,\n            pedidosFinalizado: pedidosFinalizado,\n            filterIsLoading: filterIsLoading,\n            handleShowPedido: handleShowPedido,\n            handleEditTempoEntrega: handleEditTempoEntrega,\n            handleAddEntregador: handleAddEntregador,\n            handleAvancar: handleAvancar,\n            userID: userID\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1386,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s2(User, \"QElFr+W69IRoIVoHidQD+V2QKaE=\", true, function () {\n  return [useNavigate];\n});\n_c3 = User;\nexport default User;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Teste\");\n$RefreshReg$(_c2, \"Ordercolumn\");\n$RefreshReg$(_c3, \"User\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "Loading", "Br", "Cut", "Line", "Printer", "Text", "Row", "render", "styled", "axios", "toast", "pdfMake", "html2canvas", "CryptoJS", "imageUrl", "io", "SidebarContext", "AuthContext", "getUser", "getVinculoEmpresa", "getPedido", "updateStatusPrint", "updateStatusPedido", "updateStatusPedidoFinalizado", "getPedidosByStatus", "getPedidosByStatusSimples", "getPedidosByStatusFinalizados", "PermissionGate", "useNavigate", "LeftMenu", "GiFullMotorcycleHelmet", "MdStorefront", "moment", "AiIcons", "FiIcons", "FaMotorcycle", "FaRegClock", "FaStore", "MdTableBar", "IoMdPin", "ModalEditTempoEntrega", "ModalPedido", "ModalAddEntregador", "MobileKanban", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON>e", "div", "sidebar", "_c", "Ordercolumn", "_c2", "ModalUserOptions", "showOptions", "User", "_s2", "_s", "$RefreshSig$", "empresa", "localStorage", "getItem", "empresaParse", "JSON", "parse", "objIdEmpresa", "_id", "idEmpresa", "id_empresa", "vinculo_empresa", "cnpj", "secret<PERSON>ey", "userEncrypted", "user", "AES", "decrypt", "toString", "enc", "Utf8", "userParse", "userID", "showEditTempoEntregaEmpresa", "setEditTempoEntregaEmpresa", "_idEmpresaEdit", "set_idEmpresaEdit", "INITIAL_DATA", "value", "label", "selectData", "setselectData", "showPedido", "setShowPedido", "setSidebar", "tempoBalcaoMinBD", "setTempoBalcaoMinBD", "tempoBalcaoMaxBD", "setTempoBalcaoMaxBD", "tempoEntregaMinBD", "setTempoEntregaMinBD", "tempoEntregaMaxBD", "setTempoEntregaMaxBD", "tipoImpressao", "setTipoImpressao", "refresh", "setRefresh", "filtroSelecionado", "setFiltroSelecionado", "pedidosAnalise", "setPedidosAnalise", "pedidosProducao", "setPedidosProducao", "pedidosEntrega", "setPedidosEntrega", "pedidosFinalizado", "setPedidosFinalizado", "pedidos<PERSON><PERSON><PERSON>", "setPedidosSimples", "pedidosFinalizados", "setPedidosFinalizados", "isLoadingSimples", "setIsLoadingSimples", "isLoadingFinalizados", "setIsLoadingFinalizados", "navigate", "infoPedido", "setInfoPedido", "filterIsLoading", "setFilterIsLoading", "showModalAddEntregador", "setShowModalAddEntregador", "pedidoToAddEntregador", "setPedidoToAddEntregador", "searchQuery", "setSearch<PERSON>uery", "originalPedidos", "setOriginalPedidos", "imageDataURL", "isLoading", "setIsLoading", "hasCancelPassword", "setHasCancelPassword", "useIsMobile", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "fetchDataAndSetPedidos", "fetchedPedidosSimples", "fetchPedidosSimples", "fetchedPedidosFinalizados", "fetchPedidosFinalizados", "response", "pedidosStatus123", "data", "vinculo", "tipo_impressao", "parseInt", "tempoBalcaoMin", "tempoBalcaoMax", "tempoEntregaMin", "tempoEntregaMax", "has_cancel_password", "pedidos", "pedidosStatus4", "fetchDataAndSetPedidosForFilters", "paramReceived", "filteredPedidosSimples", "filteredPedidosFinalizados", "filter", "pedido", "_pedido$entrega", "entrega", "tipo_entrega", "toLowerCase", "_pedido$entrega2", "_pedido$entrega2$tipo", "_pedido$entrega3", "_pedido$entrega3$tipo", "_pedido$entrega4", "_pedido$entrega4$tipo", "created<PERSON>y", "includes", "for<PERSON>ach", "dados", "status_pedido", "pedidosAnteriores", "isDevelopment", "location", "hostname", "apiUrl", "process", "env", "REACT_APP_SERVER_URL_DEV", "REACT_APP_SERVER_URL_PROD", "fetchPedidos", "allPedidosSimples", "allPedidosFinalizados", "allPedidos", "wsUrl", "socket", "withCredentials", "transports", "auth", "token", "reconnection", "reconnectionAttempts", "reconnectionDelay", "on", "console", "log", "reason", "error", "attempt", "once", "emit", "clientId", "Date", "now", "companyId", "handleNovoPedido", "pedidosSimplesData", "pedidosFinalizadosData", "newPedidos", "off", "disconnect", "filteredPedidos", "_dados$id_pedido_coun", "nome_cliente", "id_pedido_counter", "pedidosSimplesFiltrados", "pedidosFinalizadosFiltrados", "handleCadastroPDV", "handleAddEntregador", "event", "stopPropagation", "handleAvancar", "id_pedido", "newStatus", "finalizadoAt", "msg", "autoClose", "type", "err", "_err$response", "_err$response$data", "handleEditTempoEntrega", "idToEdit", "handleFilterAll", "handleFilterDelivery", "handleFilterBalcao", "handleFilterMesas", "handleShowPedido", "idPedidoToOpen", "children", "permissions", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "style", "width", "marginBottom", "display", "justifyContent", "padding", "flexDirection", "justifyItems", "onClick", "alignItems", "onChange", "e", "target", "placeholder", "FiSearch", "AiOutlinePlusCircle", "marginRight", "fontSize", "height", "boxShadow", "color", "typecolumn", "length", "cursor", "textAlign", "Math", "random", "map", "i", "_dados$entrega", "_dados$id_pedido_coun2", "_dados$entrega2", "_dados$entrega3", "_dados$entrega4", "_dados$entrega4$ender", "_dados$entrega5", "_dados$entrega5$ender", "_dados$entrega6", "_dados$entrega6$ender", "_dados$entrega7", "_dados$entrega7$ender", "_dados$entrega8", "_dados$entrega8$ender", "parseZone", "createdAt", "local", "format", "counter_qtd_pedido", "celular_cliente", "valor_total", "toFixed", "replace", "tipo_pagamento", "marginTop", "endereco", "rua", "numero", "complemento", "bairro", "entregador", "name", "AiOutlineArrowRight", "marginLeft", "fontColor", "_dados$entrega9", "_dados$id_pedido_coun3", "_dados$entrega0", "_dados$entrega1", "_dados$entrega1$ender", "_dados$entrega10", "_dados$entrega10$ende", "_dados$entrega11", "_dados$entrega11$ende", "_dados$entrega12", "_dados$entrega12$ende", "_dados$entrega13", "_dados$entrega13$ende", "_dados$entrega14", "_dados$id_pedido_coun4", "_dados$entrega15", "_dados$entrega16", "_dados$entrega16$ende", "_dados$entrega17", "_dados$entrega17$ende", "_dados$entrega18", "_dados$entrega18$ende", "_dados$entrega19", "_dados$entrega19$ende", "_dados$entrega20", "_dados$entrega20$ende", "iconname", "iconsize", "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "iconcolor", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "points", "_dados$entrega21", "_dados$id_pedido_coun5", "_dados$entrega22", "_dados$entrega23", "_dados$entrega23$ende", "_dados$entrega24", "_dados$entrega24$ende", "_dados$entrega25", "_dados$entrega25$ende", "_dados$entrega26", "_dados$entrega26$ende", "_dados$entrega27", "_dados$entrega27$ende", "cancelado", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/Home/index.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\r\nimport Loading from 'react-loading';\r\nimport { Br, Cut, Line, Printer, Text, Row, render } from 'react-thermal-printer';\r\nimport './style.css'\r\nimport './styleResponsive.css'\r\nimport styled from 'styled-components';\r\nimport axios from \"axios\";\r\nimport { toast } from \"react-toastify\";\r\nimport pdfMake from \"pdfmake/build/pdfmake\";\r\nimport html2canvas from 'html2canvas';\r\n//import html2pdf from 'html2pdf.js';\r\nimport CryptoJS from 'crypto-js';\r\nimport imageUrl from '../../img/logoBlue.png'\r\nimport io from 'socket.io-client';\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport { AuthContext } from \"../../contexts/auth\";\r\n\r\nimport {\r\n    getUser,\r\n    getVinculoEmpresa,\r\n    getPedido,\r\n    updateStatusPrint,\r\n    updateStatusPedido,\r\n    updateStatusPedidoFinalizado,\r\n    getPedidosByStatus,\r\n    getPedidosByStatusSimples,\r\n    getPedidosByStatusFinalizados\r\n} from \"../../services/api\";\r\n\r\nimport PermissionGate from \"../../services/PermissionGate\";\r\n\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\nimport { GiFullMotorcycleHelmet } from \"react-icons/gi\";\r\nimport { MdStorefront } from \"react-icons/md\";\r\n\r\nimport moment from 'moment'\r\nimport * as AiIcons from 'react-icons/ai'\r\nimport * as FiIcons from 'react-icons/fi'\r\nimport { FaMotorcycle, FaRegClock, FaStore } from \"react-icons/fa\";\r\nimport { MdTableBar } from \"react-icons/md\";\r\nimport { IoMdPin } from \"react-icons/io\";\r\n\r\nimport ModalEditTempoEntrega from \"../../components/ModalEditTempoEntrega\";\r\nimport ModalPedido from \"../../components/ModalPedido\";\r\nimport ModalAddEntregador from \"./ModalAddEntregador\";\r\n\r\nimport { MobileKanban } from \"./MobileKanban\";\r\n\r\nconst Teste = styled.div`\r\n\r\n    display: flex;\r\n    margin-left:  ${({ sidebar }) => (sidebar ? '250px' : '100px')}; \r\n    height:auto;\r\n    width:auto;\r\n    transition: 150ms;\r\n    background-color:rgb(247,247,247)!important;\r\n    --background-color:white!important;\r\n    overflow: initial;\r\n    z-Index: 9;\r\n\r\n    @media (max-width: 880px) {\r\n        margin-left: 0;\r\n    }\r\n`;\r\n\r\nconst Ordercolumn = styled.div`\r\n    min-width: 25%;\r\n`;\r\n\r\nconst ModalUserOptions = styled.div`\r\n  font-size: 14px;\r\n  position: absolute;\r\n  top: 82px;\r\n  left: 150px;\r\n  display: ${({ showOptions }) => (showOptions ? 'none' : '')};\r\n  float: left;\r\n  min-width: 160px;\r\n  margin: 2px 0 0;\r\n  padding: 5px 0;\r\n  list-style: none;\r\n  text-align: left;\r\n  border: 1px solid #ccc;\r\n  border: 1px solid rgba(0,0,0,.15);\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  --background-color:rgb(247,247,247)!important;\r\n  background-clip: padding-box;\r\n  -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);\r\n  box-shadow: 0 6px 12px rgba(0,0,0,.175);\r\n  z-index: 25;\r\n\r\n  li{\r\n    font-weight: 400;\r\n    line-height: 1.42857143;\r\n    display: block;\r\n    clear: both;\r\n    padding: 3px 20px;\r\n    white-space: nowrap;\r\n    color: #58595b;\r\n  }\r\n  li:hover{\r\n    background: #f5f5f5\r\n  }\r\n`;\r\n\r\n\r\nconst User = () => {\r\n    //const { user } = useContext(AuthContext);\r\n    const empresa = localStorage.getItem('empresa')\r\n    const empresaParse = JSON.parse(empresa)\r\n    const objIdEmpresa = empresaParse._id;\r\n    const idEmpresa = empresaParse.id_empresa;\r\n    const vinculo_empresa = empresaParse.cnpj;\r\n\r\n    const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\r\n    const userEncrypted = localStorage.getItem('user')\r\n    const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\r\n    //const user = localStorage.getItem('user')\r\n    const userParse = JSON.parse(user)\r\n    //console.log(userParse)\r\n    //const userParse = user;\r\n    const userID = userParse._id;\r\n    const [showEditTempoEntregaEmpresa, setEditTempoEntregaEmpresa] = useState(true);\r\n    const [_idEmpresaEdit, set_idEmpresaEdit] = useState('');\r\n    //const [showOptions, setUserOptions] = useState(true);\r\n    const INITIAL_DATA = {\r\n        value: \"\",\r\n        label: 'Selecione uma empresa',\r\n    };\r\n    const [selectData, setselectData] = useState(INITIAL_DATA);\r\n\r\n    const [showPedido, setShowPedido] = useState(true);\r\n\r\n    const { sidebar, setSidebar } = useContext(SidebarContext);; //TESTANDO CONST NO AppROUTES\r\n\r\n    var [tempoBalcaoMinBD, setTempoBalcaoMinBD] = useState(0);\r\n    var [tempoBalcaoMaxBD, setTempoBalcaoMaxBD] = useState(0);\r\n    var [tempoEntregaMinBD, setTempoEntregaMinBD] = useState(0);\r\n    var [tempoEntregaMaxBD, setTempoEntregaMaxBD] = useState(0);\r\n    var [tipoImpressao, setTipoImpressao] = useState(\"\");\r\n    const [refresh, setRefresh] = useState(false);\r\n\r\n    const [filtroSelecionado, setFiltroSelecionado] = useState(\"all\");\r\n\r\n    const [pedidosAnalise, setPedidosAnalise] = useState([]);\r\n    const [pedidosProducao, setPedidosProducao] = useState([]);\r\n    const [pedidosEntrega, setPedidosEntrega] = useState([]);\r\n    const [pedidosFinalizado, setPedidosFinalizado] = useState([]);\r\n\r\n    //const [pedidos, setPedidos] = useState([]);\r\n    const [pedidosSimples, setPedidosSimples] = useState([]);\r\n    const [pedidosFinalizados, setPedidosFinalizados] = useState([]);\r\n    const [isLoadingSimples, setIsLoadingSimples] = useState(true);\r\n    const [isLoadingFinalizados, setIsLoadingFinalizados] = useState(true);\r\n\r\n    const navigate = useNavigate();\r\n    const [infoPedido, setInfoPedido] = useState({});\r\n    const [filterIsLoading, setFilterIsLoading] = useState(false);\r\n    const [showModalAddEntregador, setShowModalAddEntregador] = useState(true);\r\n    const [pedidoToAddEntregador, setPedidoToAddEntregador] = useState({});\r\n    const [searchQuery, setSearchQuery] = useState('');\r\n    const [originalPedidos, setOriginalPedidos] = useState([]); // Para armazenar a lista completa de pedidos\r\n    //const [imageBase64, setImageBase64] = useState(\"\");\r\n    //const [pdfBase64, setPdfBase64] = useState(\"\");\r\n    var imageDataURL = null;\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    const [hasCancelPassword, setHasCancelPassword] = useState(false);\r\n    //const [printJobId, setPrintJobId] = useState('');\r\n\r\n    // Hook para detectar mobile\r\n    const useIsMobile = () => {\r\n        const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n        \r\n        useEffect(() => {\r\n            const handleResize = () => {\r\n                setIsMobile(window.innerWidth <= 768);\r\n            };\r\n            \r\n            window.addEventListener('resize', handleResize);\r\n            return () => window.removeEventListener('resize', handleResize);\r\n        }, []);\r\n        \r\n        return isMobile;\r\n    };\r\n\r\n    const isMobile = useIsMobile();\r\n\r\n    useEffect(() => {\r\n        if (refresh) {\r\n            const fetchDataAndSetPedidos = async () => {\r\n                const fetchedPedidosSimples = await fetchPedidosSimples();\r\n                const fetchedPedidosFinalizados = await fetchPedidosFinalizados();\r\n                setPedidosSimples(fetchedPedidosSimples);\r\n                setPedidosFinalizados(fetchedPedidosFinalizados);\r\n            };\r\n            fetchDataAndSetPedidos();\r\n            setRefresh(false);\r\n        }\r\n    }, [refresh]);\r\n    /*\r\n    const fetchDataAndSetPedidosForFilters = async () => {\r\n        const fetchedPedidos = await fetchData();\r\n        setPedidos(fetchedPedidos);\r\n    };\r\n    \r\n    const fetchData = async () => {\r\n        const response = await getVinculoEmpresa(userID);\r\n        //const vinculo = response.data.vinculo.cnpj;\r\n        //console.log(\"response>\",response.data.vinculo);\r\n        //const pedidosResponse = await getPedidos(userID, id_empresa);\r\n        const pedidosAnalise = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '1');\r\n        const pedidosProducao = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '2');\r\n        const pedidosEntrega = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '3');\r\n        const pedidosFinalizados = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '4');\r\n        //console.log(\"pedidosAnaliseResponse>\",pedidosAnaliseResponse.data.pedidos);\r\n        setTipoImpressao(response.data.vinculo.tipo_impressao);\r\n        setTempoBalcaoMinBD(parseInt(response.data.vinculo.tempoBalcaoMin));\r\n        setTempoBalcaoMaxBD(parseInt(response.data.vinculo.tempoBalcaoMax));\r\n        setTempoEntregaMinBD(parseInt(response.data.vinculo.tempoEntregaMin));\r\n        setTempoEntregaMaxBD(parseInt(response.data.vinculo.tempoEntregaMax));\r\n        \r\n    //setPedidos(pedidosResponse.data.pedidos);\r\n        setRefresh(false);\r\n        return [\r\n            ...pedidosAnalise.data.pedidos,\r\n            ...pedidosProducao.data.pedidos,\r\n            ...pedidosEntrega.data.pedidos,\r\n            ...pedidosFinalizados.data.pedidos,\r\n          ];\r\n    };*/\r\n\r\n\r\n\r\n    /*const fetchData = async () => {\r\n        const response = await getVinculoEmpresa(userID);\r\n        const pedidosAnalise = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '1');\r\n        const pedidosProducao = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '2');\r\n        const pedidosEntrega = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '3');\r\n        const pedidosFinalizados = await getPedidosByStatus(userID, idEmpresa, vinculo_empresa, '4');\r\n        setTipoImpressao(response.data.vinculo.tipo_impressao);\r\n        setTempoBalcaoMinBD(parseInt(response.data.vinculo.tempoBalcaoMin));\r\n        setTempoBalcaoMaxBD(parseInt(response.data.vinculo.tempoBalcaoMax));\r\n        setTempoEntregaMinBD(parseInt(response.data.vinculo.tempoEntregaMin));\r\n        setTempoEntregaMaxBD(parseInt(response.data.vinculo.tempoEntregaMax));\r\n        setHasCancelPassword(response.data.vinculo.has_cancel_password)\r\n\r\n        setRefresh(false);\r\n        setFilterIsLoading(false);\r\n        return [...pedidosAnalise.data.pedidos, ...pedidosProducao.data.pedidos, ...pedidosEntrega.data.pedidos, ...pedidosFinalizados.data.pedidos];\r\n    };*/\r\n\r\n    /*const fetchDataAndSetPedidosForFilters = async (paramReceived) => {\r\n        const fetchedPedidos = await fetchData();\r\n        paramReceived === \"all\" && setPedidos(fetchedPedidos);\r\n        paramReceived === \"delivery\" && setPedidos(fetchedPedidos.filter(pedido => pedido.entrega.tipo_entrega.toLowerCase() === \"entrega\"));\r\n        paramReceived === \"balcao\" && setPedidos(fetchedPedidos.filter(pedido => pedido.entrega.tipo_entrega.toLowerCase() === \"retirada\"));\r\n\r\n    };*/\r\n\r\n    // Função para carregar pedidos simples (status 1, 2, 3)\r\n    const fetchPedidosSimples = async () => {\r\n        const response = await getVinculoEmpresa(userID);\r\n        const pedidosStatus123 = await getPedidosByStatusSimples(userID, idEmpresa, vinculo_empresa, ['1', '2', '3'], 'false');\r\n\r\n        // Atualizar as informações adicionais\r\n        setTipoImpressao(response.data.vinculo.tipo_impressao);\r\n        setTempoBalcaoMinBD(parseInt(response.data.vinculo.tempoBalcaoMin));\r\n        setTempoBalcaoMaxBD(parseInt(response.data.vinculo.tempoBalcaoMax));\r\n        setTempoEntregaMinBD(parseInt(response.data.vinculo.tempoEntregaMin));\r\n        setTempoEntregaMaxBD(parseInt(response.data.vinculo.tempoEntregaMax));\r\n        setHasCancelPassword(response.data.vinculo.has_cancel_password);\r\n        setFilterIsLoading(false);\r\n        return pedidosStatus123.data.pedidos;\r\n    };\r\n\r\n    // Função para carregar pedidos finalizados (status 4)\r\n    const fetchPedidosFinalizados = async () => {\r\n        const pedidosStatus4 = await getPedidosByStatusFinalizados(userID, idEmpresa, vinculo_empresa, 'false');\r\n        setFilterIsLoading(false);\r\n        return pedidosStatus4.data.pedidos;\r\n    };\r\n\r\n    // Função para aplicar os filtros\r\n    const fetchDataAndSetPedidosForFilters = async (paramReceived) => {\r\n        const fetchedPedidosSimples = await fetchPedidosSimples();\r\n        const fetchedPedidosFinalizados = await fetchPedidosFinalizados();\r\n\r\n        let filteredPedidosSimples = fetchedPedidosSimples;\r\n        let filteredPedidosFinalizados = fetchedPedidosFinalizados;\r\n\r\n        if (paramReceived === \"delivery\") {\r\n            filteredPedidosSimples = fetchedPedidosSimples.filter(pedido => pedido.entrega?.tipo_entrega.toLowerCase() === \"entrega\");\r\n            filteredPedidosFinalizados = fetchedPedidosFinalizados.filter(pedido => pedido.entrega?.tipo_entrega?.toLowerCase() === \"entrega\");\r\n        } else if (paramReceived === \"balcao\") {\r\n            filteredPedidosSimples = fetchedPedidosSimples.filter(pedido => pedido.entrega?.tipo_entrega?.toLowerCase() === \"retirada\");\r\n            filteredPedidosFinalizados = fetchedPedidosFinalizados.filter(pedido => pedido.entrega?.tipo_entrega?.toLowerCase() === \"retirada\");\r\n        } else if (paramReceived === \"mesas\") {\r\n            filteredPedidosSimples = fetchedPedidosSimples.filter(pedido => pedido.createdBy.toLowerCase().includes('mesa'));\r\n            filteredPedidosFinalizados = fetchedPedidosFinalizados.filter(pedido => pedido.createdBy.toLowerCase().includes('mesa'));\r\n        }\r\n\r\n        // Atualizar os estados de pedidos\r\n        setPedidosSimples(filteredPedidosSimples);\r\n        setPedidosFinalizados(filteredPedidosFinalizados);\r\n    };\r\n\r\n    useEffect(() => {\r\n        // Carregar pedidos com o filtro específico\r\n        setFilterIsLoading(true);\r\n        fetchDataAndSetPedidosForFilters(filtroSelecionado);\r\n    }, [refresh]);\r\n\r\n    // Organizar os pedidos simples em suas respectivas categorias\r\n    useEffect(() => {\r\n        setPedidosAnalise([]);\r\n        setPedidosProducao([]);\r\n        setPedidosEntrega([]);\r\n\r\n        pedidosSimples.forEach((dados) => {\r\n            if (dados.status_pedido === '1') {\r\n                setPedidosAnalise(pedidosAnteriores => [...pedidosAnteriores, dados]);\r\n            } else if (dados.status_pedido === '2') {\r\n                setPedidosProducao(pedidosAnteriores => [...pedidosAnteriores, dados]);\r\n            } else if (dados.status_pedido === '3') {\r\n                setPedidosEntrega(pedidosAnteriores => [...pedidosAnteriores, dados]);\r\n            }\r\n        });\r\n    }, [pedidosSimples, refresh]);  // Esse efeito é acionado quando 'pedidosSimples' é atualizado\r\n\r\n    // Organizar os pedidos finalizados em sua categoria\r\n    useEffect(() => {\r\n        setPedidosFinalizado([]);\r\n\r\n        pedidosFinalizados.forEach((dados) => {\r\n            if (dados.status_pedido === '4') {\r\n                setPedidosFinalizado(pedidosAnteriores => [...pedidosAnteriores, dados]);\r\n            }\r\n        });\r\n    }, [pedidosFinalizados, refresh]);  // Esse efeito é acionado quando 'pedidosFinalizados' é atualizado\r\n\r\n    /*useEffect(() => {\r\n        setPedidosAnalise([]);\r\n        setPedidosProducao([]);\r\n        setPedidosEntrega([]);\r\n        setPedidosFinalizado([]);\r\n        pedidos.map((dados, i) => {\r\n            if (dados.status_pedido == '1') {\r\n                setPedidosAnalise(pedidosAnteriores => [...pedidosAnteriores, dados]);\r\n            }\r\n            if (dados.status_pedido == '2') {\r\n                setPedidosProducao(pedidosAnteriores => [...pedidosAnteriores, dados])\r\n            }\r\n            if (dados.status_pedido == '3') {\r\n                setPedidosEntrega(pedidosAnteriores => [...pedidosAnteriores, dados])\r\n            }\r\n            if (dados.status_pedido == '4') {\r\n                setPedidosFinalizado(pedidosAnteriores => [...pedidosAnteriores, dados])\r\n            }\r\n        })\r\n    }, [pedidos, refresh]); // Sem dependências, então o efeito será executado apenas uma vez*/\r\n\r\n    /*  ///////////////////////// COMENTARIO TEMPORÁRIO SÓ PRA TESTAR  //////////////////////////\r\n    const isDevelopment = window.location.hostname === 'localhost';\r\n    const apiUrl = isDevelopment\r\n        ? process.env.REACT_APP_SERVER_URL_DEV\r\n        : process.env.REACT_APP_SERVER_URL_PROD;\r\n    useEffect(() => {\r\n        const fetchPedidos = async () => {\r\n            const data = await fetchData();\r\n            setOriginalPedidos(data); // Armazena os pedidos originais\r\n            setPedidos(data); // Inicializa os pedidos com os dados originais\r\n        };\r\n\r\n        fetchPedidos();\r\n\r\n        const wsUrl = apiUrl; // Alterar a URL se o servidor estiver em um endereço diferente\r\n        const socket = io(wsUrl, { withCredentials: true, transports: ['websocket'], auth: { token: localStorage.getItem('token') } });\r\n\r\n        socket.emit('joinCompanyRoom', { companyId: idEmpresa.toString(), clientId: 'reactClient' });\r\n\r\n        socket.on('novoPedido', async (data) => {\r\n            console.log(\"NOVO PEDIDO RECEBIDO!\");\r\n            const newPedidos = await fetchData();\r\n            setOriginalPedidos(newPedidos);\r\n            setPedidos(newPedidos);\r\n        });\r\n\r\n        return () => {\r\n            socket.off('novoPedido');\r\n        };\r\n    }, [idEmpresa]);\r\n\r\n    useEffect(() => {\r\n        const filteredPedidos = originalPedidos.filter(dados =>\r\n            dados.nome_cliente.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n            dados.id_pedido_counter?.toString().includes(searchQuery) ||\r\n            dados.id_pedido.toString().includes(searchQuery)\r\n        );\r\n        setPedidos(filteredPedidos);\r\n    }, [searchQuery, originalPedidos]);*/\r\n    const isDevelopment = window.location.hostname === 'localhost';\r\n    const apiUrl = isDevelopment\r\n        ? process.env.REACT_APP_SERVER_URL_DEV\r\n        : process.env.REACT_APP_SERVER_URL_PROD;\r\n\r\n    useEffect(() => {\r\n        // Função para carregar os pedidos simples e finalizados\r\n        const fetchPedidos = async () => {\r\n            // Carregar pedidos simples (status 1, 2, 3)\r\n            const allPedidosSimples = await fetchPedidosSimples();\r\n            // Carregar pedidos finalizados (status 4)\r\n            const allPedidosFinalizados = await fetchPedidosFinalizados();\r\n            const allPedidos = [...allPedidosSimples, ...allPedidosFinalizados];\r\n            setOriginalPedidos(allPedidos);\r\n        };\r\n\r\n        fetchPedidos(); // Carrega os pedidos na inicialização\r\n\r\n        // Configuração do WebSocket\r\n        const wsUrl = apiUrl;\r\n        //const socket = io(wsUrl, { withCredentials: true, transports: ['websocket'], auth: { token: localStorage.getItem('token') } });\r\n        const socket = io(wsUrl, { \r\n            withCredentials: true, \r\n            transports: ['websocket'], \r\n            auth: { token: localStorage.getItem('token') },\r\n            reconnection: true, // Tenta reconectar automaticamente\r\n            reconnectionAttempts: 10, // Máximo de 10 tentativas\r\n            reconnectionDelay: 5000, // Tenta reconectar a cada 5 segundos\r\n        });\r\n\r\n        socket.on(\"connect\", () => {\r\n            console.log(\"WebSocket conectado!\");\r\n        });\r\n\r\n        socket.on(\"disconnect\", (reason) => {\r\n            console.error(\"WebSocket desconectado:\", reason);\r\n        });\r\n        \r\n        socket.on(\"reconnect_attempt\", (attempt) => {\r\n            console.log(`Tentativa de reconexão ${attempt}`);\r\n        });\r\n\r\n        socket.once(\"ping\", () => {\r\n            console.log(\"🔄 Ping recebido do servidor, enviando pong...\");\r\n            socket.emit(\"pong\");\r\n        });\r\n\r\n        //socket.emit('joinCompanyRoom', { companyId: idEmpresa.toString(), clientId: 'reactClient' });\r\n        const clientId = `${userID}-${Date.now()}`;\r\n        socket.emit('joinCompanyRoom', { companyId: objIdEmpresa.toString(), clientId });\r\n\r\n        const handleNovoPedido = async () => {\r\n            console.log(\"NOVO PEDIDO RECEBIDO!\");\r\n            const pedidosSimplesData = await fetchPedidosSimples();\r\n            const pedidosFinalizadosData = await fetchPedidosFinalizados();\r\n            const newPedidos = [...pedidosSimplesData, ...pedidosFinalizadosData];\r\n            setOriginalPedidos(newPedidos);\r\n            setPedidosSimples(pedidosSimplesData);\r\n            setPedidosFinalizados(pedidosFinalizadosData);\r\n        };\r\n\r\n        socket.on('novoPedido', handleNovoPedido);\r\n\r\n        // Limpeza do WebSocket\r\n        return () => {\r\n            socket.off('novoPedido');\r\n            socket.disconnect(); // 🔹 Fecha a conexão WebSocket ao sair da tela\r\n        };\r\n    }, [idEmpresa]); // Dependência para que o efeito seja disparado quando 'idEmpresa' mudar\r\n\r\n    useEffect(() => {\r\n        const filteredPedidos = originalPedidos.filter(dados =>\r\n            dados.nome_cliente.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n            dados.id_pedido_counter?.toString().includes(searchQuery) //||\r\n            //dados.id_pedido.toString().includes(searchQuery)\r\n        );\r\n        // Separa os pedidos filtrados em simples e finalizados\r\n        const pedidosSimplesFiltrados = filteredPedidos.filter(dados =>\r\n            dados.status_pedido === '1' || dados.status_pedido === '2' || dados.status_pedido === '3'\r\n        );\r\n\r\n        const pedidosFinalizadosFiltrados = filteredPedidos.filter(dados =>\r\n            dados.status_pedido === '4'\r\n        );\r\n\r\n        // Atualiza os estados de pedidosSimples e pedidosFinalizados com os pedidos filtrados\r\n        setPedidosSimples(pedidosSimplesFiltrados);\r\n        setPedidosFinalizados(pedidosFinalizadosFiltrados);\r\n    }, [searchQuery, originalPedidos]);\r\n\r\n\r\n    const handleCadastroPDV = () => {\r\n        navigate('/pdv');\r\n    }\r\n\r\n\r\n    const handleAddEntregador = (event, pedido) => {\r\n        event.stopPropagation();\r\n        setPedidoToAddEntregador(pedido);\r\n        setShowModalAddEntregador(!showModalAddEntregador);\r\n    }\r\n\r\n    const handleAvancar = async (event, _id, id_pedido, status_pedido) => {\r\n        event.stopPropagation();\r\n        setIsLoading(true); // Inicia o carregamento\r\n\r\n        let newStatus = \"\";\r\n        let finalizadoAt = \"\";\r\n        let response = null;\r\n\r\n        switch (status_pedido) {\r\n            case \"1\":\r\n                newStatus = \"2\";\r\n                break;\r\n            case \"2\":\r\n                newStatus = \"3\";\r\n                break;\r\n            case \"3\":\r\n                newStatus = \"4\";\r\n                //finalizadoAt = moment().local().format(\"DD/MM/YYYY\");\r\n                //finalizadoAt = momentTz().tz('America/Sao_Paulo').format(\"DD/MM/YYYY\");\r\n                finalizadoAt = new Date();\r\n                break;\r\n            default:\r\n                setIsLoading(false);\r\n                return; // Se o status_pedido não é 1, 2, ou 3, termina a função\r\n        }\r\n\r\n        try {\r\n            // Unifica a chamada da API dependendo do caso\r\n            if (status_pedido === \"3\") {\r\n                response = await updateStatusPedidoFinalizado(userID, _id, id_pedido, newStatus, finalizadoAt);\r\n            } else {\r\n                response = await updateStatusPedido(userID, _id, id_pedido, newStatus);\r\n            }\r\n            setRefresh(true); // Atualiza o estado para forçar recarga dos dados\r\n            toast(response.data.msg, { autoClose: 2000, type: \"success\" });\r\n        } catch (err) {\r\n            toast(err.response?.data?.msg || \"An error occurred\", { autoClose: 2000, type: \"error\" });\r\n        } finally {\r\n            setRefresh(true); // Atualiza o estado para forçar recarga dos dados\r\n            setIsLoading(false); // Finaliza o carregamento imediatamente após a conclusão ou falha da chamada da API\r\n        }\r\n    };\r\n\r\n\r\n    /*const totalValuePerOrder = pedidos.map((order) => {\r\n        const totalValue = order.itens.reduce((acc, item) => {\r\n            return acc + (item.valor * item.quantidade);\r\n        }, 0);\r\n        return { ...order, totalValue };\r\n    });*/\r\n\r\n    const handleEditTempoEntrega = async idToEdit => {\r\n        setEditTempoEntregaEmpresa(!showEditTempoEntregaEmpresa);\r\n        set_idEmpresaEdit(idEmpresa);\r\n        //setUserOptions(!showOptions);\r\n        /*const response = await getUser(idToEdit);\r\n        if(showEditTempoEntregaEmpresa){       \r\n            if (response.data.user.vinculo_empresa){                \r\n                const responseVinculo = await getVinculoEmpresa(idToEdit);                     \r\n                //console.log(\"TEM EMPRESA VINCULADA!\",responseVinculo.data.vinculo.id_empresa)\r\n                set_idEmpresaEdit(responseVinculo.data.vinculo.id_empresa);                \r\n            }    \r\n        }*/\r\n    }\r\n\r\n    const handleFilterAll = async () => {\r\n        setFilterIsLoading(true);\r\n        setFiltroSelecionado(\"all\");\r\n        await fetchDataAndSetPedidosForFilters(\"all\"); // Garantindo que os dados estão atualizados;              \r\n    }\r\n\r\n    const handleFilterDelivery = async () => {\r\n        setFilterIsLoading(true);\r\n        setFiltroSelecionado(\"delivery\");\r\n        await fetchDataAndSetPedidosForFilters(\"delivery\"); // Garantindo que os dados estão atualizados\r\n        //setPedidos(currentPedidos => currentPedidos.filter(pedido => pedido.entrega.tipo_entrega?.toLowerCase() === \"entrega\"));        \r\n    };\r\n\r\n    const handleFilterBalcao = async () => {\r\n        setFilterIsLoading(true);\r\n        setFiltroSelecionado(\"balcao\");\r\n        await fetchDataAndSetPedidosForFilters(\"balcao\"); // Garantindo que os dados estão atualizados\r\n        //setPedidos(currentPedidos => currentPedidos.filter(pedido => pedido.entrega.tipo_entrega?.toLowerCase() === \"retirada\"));\r\n    };\r\n\r\n    const handleFilterMesas = async () => {\r\n        setFilterIsLoading(true);\r\n        setFiltroSelecionado(\"mesas\");\r\n        await fetchDataAndSetPedidosForFilters(\"mesas\"); // Garantindo que os dados estão atualizados\r\n        //setPedidos(currentPedidos => currentPedidos.filter(pedido => pedido.entrega.tipo_entrega?.toLowerCase() === \"retirada\"));\r\n    };\r\n\r\n    const handleShowPedido = async (idPedidoToOpen, id_empresa) => {\r\n\r\n        setShowPedido(!showPedido);\r\n        const response = await getPedido(userID, id_empresa, vinculo_empresa, idPedidoToOpen);\r\n        if (showPedido) {\r\n            if (response.data.pedido) {\r\n                setInfoPedido(response.data.pedido[0]);\r\n            }\r\n        }\r\n    }\r\n\r\n    return (\r\n\r\n        <>\r\n            <PermissionGate permissions={['default']}>\r\n\r\n\r\n                {/*<LeftMenu setSidebar={setSidebar} sidebar={sidebar} />*/}\r\n\r\n                <Teste sidebar={sidebar}>\r\n\r\n\r\n\r\n\r\n                    <ModalEditTempoEntrega setEditTempoEntregaEmpresa={setEditTempoEntregaEmpresa} showEditTempoEntregaEmpresa={showEditTempoEntregaEmpresa}\r\n                        setRefresh={setRefresh}\r\n                        selectData={selectData}\r\n                        setselectData={setselectData}\r\n                        _idEmpresaEdit={_idEmpresaEdit}\r\n                        tempoBalcaoMinBD={tempoBalcaoMinBD}\r\n                        tempoBalcaoMaxBD={tempoBalcaoMaxBD}\r\n                        tempoEntregaMinBD={tempoEntregaMinBD}\r\n                        tempoEntregaMaxBD={tempoEntregaMaxBD}\r\n                        tipoImpressao={tipoImpressao}\r\n                    //usernameEdit={usernameEdit} \r\n                    //emailEdit={emailEdit} \r\n                    //roleEdit={roleEdit}    \r\n                    //editPerfil={false}\r\n                    />\r\n\r\n                    <ModalPedido setShowPedido={setShowPedido} showPedido={showPedido}\r\n                        setRefresh={setRefresh}\r\n                        infoPedido={infoPedido}\r\n                        setInfoPedido={setInfoPedido}\r\n                        handleAvancar={handleAvancar}\r\n                        userID={userID}\r\n                        hasCancelPassword={hasCancelPassword}\r\n                    />\r\n\r\n                    <ModalAddEntregador showModalAddEntregador={showModalAddEntregador}\r\n                        setShowModalAddEntregador={setShowModalAddEntregador}\r\n                        pedidoToAddEntregador={pedidoToAddEntregador}\r\n                        objIdEmpresa={objIdEmpresa}\r\n                        userID={userID}\r\n                        setRefresh={setRefresh}\r\n                    />\r\n\r\n                    <div className=\"m-5 m5-home\">\r\n                        <div style={{ width: '100%' }}>\r\n\r\n                            <div className=\"form-header\" style={{ marginBottom: \"0px\" }}>\r\n                                <div className=\"title\">\r\n                                    <h1>Meus Pedidos</h1>\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/*<div className=\"imgDashboard\" style={{zIndex:\"10\", marginTop:\"30px\"}}>\r\n                            <img src={imageUrl} width={\"100%\"} height={\"100%\"} className='dashImg' style={{transition:\"100ms\"}}/>\r\n                        </div> */}\r\n                            <div className=\"header-mobile-kanban\" style={{ display: 'inline-flex', width: '100%', justifyContent: 'space-between', padding: '10px 30px' }}>\r\n                                <div style={{ display: 'inline-flex', flexDirection: \"column\", justifyContent: 'space-between', justifyItems: 'center' }}>\r\n                                    <div className={`filter-buttons-container ${isMobile ? 'mobile' : 'desktop'}`}>\r\n                                        <div onClick={() => handleFilterAll()} className={`filter-button filter-button-todos ${filtroSelecionado === \"all\" ? 'active' : ''} ${isMobile ? 'mobile' : ''}`}>\r\n                                            <div className=\"filter-button-text\">Todos</div>\r\n                                        </div>\r\n                                        <div onClick={() => handleFilterDelivery()} className={`filter-button filter-button-delivery ${filtroSelecionado === \"delivery\" ? 'active' : ''} ${isMobile ? 'mobile' : ''}`}>\r\n                                            <div className=\"filter-button-text\">\r\n                                                <GiFullMotorcycleHelmet className=\"filter-icon\" />\r\n                                                <span>Delivery</span>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div onClick={() => handleFilterBalcao()} className={`filter-button filter-button-balcao ${filtroSelecionado === \"balcao\" ? 'active' : ''} ${isMobile ? 'mobile' : ''}`}>\r\n                                            <div className=\"filter-button-text\">\r\n                                                <MdStorefront className=\"filter-icon\" />\r\n                                                <span>Balcão</span>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div onClick={() => handleFilterMesas()} className={`filter-button filter-button-mesas ${filtroSelecionado === \"mesas\" ? 'active' : ''} ${isMobile ? 'mobile' : ''}`}>\r\n                                            <div className=\"filter-button-text\">\r\n                                                <MdTableBar className=\"filter-icon\" />\r\n                                                <span>Mesas</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div className=\"input-box-list home\" >\r\n                                        <div style={{ display: 'flex', width: '100%', alignItems: 'center' }}>\r\n                                            <input style={{ width: '100%' }} value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)}\r\n                                                className=\"input-fieldClienteOuPedido\" placeholder=\"Buscar pelo cliente ou nº do pedido\" />\r\n                                            <i className=\"icon\"><FiIcons.FiSearch /></i>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <div style={{ display: 'inline-flex' }}>\r\n                                    <div className=\"div-buttons\">\r\n                                        <div className=\"continue-button\">\r\n                                            <button onClick={handleCadastroPDV}>\r\n                                                <AiIcons.AiOutlinePlusCircle style={{ marginRight: \"5px\", fontSize: \"22px\", marginBottom: \"2px\" }} /><a>Novo Pedido</a>\r\n                                            </button>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                </div>\r\n\r\n                            </div>\r\n\r\n\r\n                        </div>\r\n\r\n\r\n                        {window.innerWidth > 780 ? (\r\n                            <div className=\"Ordercolumn\" style={{ display: 'inline-flex', height: '674px', boxShadow: '0px 0px 5px 2px rgb(0,0,0,.1)' }}>\r\n\r\n                                {filterIsLoading ? (\r\n                                    <Loading type='spin' color='rgb(49, 140, 213)' height={56} width={56} className=\"loadingSpinHomeFilters\" />\r\n                                )\r\n                                    : null\r\n                                }\r\n\r\n                                {/* Pedidos em Análise */}\r\n                                <Ordercolumn\r\n                                    typecolumn=\"analysis\"\r\n                                    className=\"column\"\r\n                                //style={{display:'table-row'}}\r\n                                >\r\n                                    <div className=\"containerAnalise\">\r\n\r\n                                        <div className=\"header-column-analise\">\r\n\r\n                                            <div className=\"header-column--align\">\r\n                                                <h4>Em análise</h4>\r\n                                            </div>\r\n                                            <h4>{pedidosAnalise.length}</h4>\r\n\r\n                                        </div>\r\n\r\n                                        <div className=\"blocoAnalise\">\r\n                                            <div className=\"column-container column-container--first\">\r\n                                                <div className=\"time\">\r\n                                                    <div>\r\n                                                        <p className=\"titleOptEmp\">\r\n                                                            <b>Balcão: </b>\r\n                                                            <span>{tempoBalcaoMinBD} a {tempoBalcaoMaxBD} min</span>\r\n                                                        </p>\r\n                                                        <p className=\"titleOptEmp\">\r\n                                                            <b>Delivery: </b>\r\n                                                            <span>{tempoEntregaMinBD} a {tempoEntregaMaxBD} min</span>\r\n                                                        </p>\r\n                                                        <p className=\"titleOptEmp\">\r\n                                                            <b>Aceite automático: </b>\r\n                                                            <span>{tipoImpressao == 'manual' ? 'Desativado' : tipoImpressao == 'automatico' ? 'Ativado' : 'Não definido'}</span>\r\n                                                        </p>\r\n                                                    </div>\r\n                                                    <div className=\"text-edit\" onClick={() => handleEditTempoEntrega(userID)} style={{ cursor: \"pointer\" }}>\r\n                                                        {\" \"}\r\n                                                        Editar{\" \"}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                            {\r\n                                                pedidosAnalise.length == 0 ?\r\n                                                    <div key={Math.random()} className=\"column-container column-container--first\">\r\n                                                        <div className=\"text\">\r\n                                                            <p style={{ marginBottom: 0, justifyContent: 'center', textAlign: 'center', display: 'flex' }}>Nenhum pedido no momento.</p>\r\n                                                            <p style={{ marginBottom: 0, justifyContent: 'center', textAlign: 'center', display: 'flex' }}>\r\n                                                                Compartilhe os seus links nas redes sociais e receba pedidos!\r\n                                                            </p>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    :\r\n                                                    //pedidos && pedidos.map((dados, i) => (\r\n                                                    pedidosSimples && pedidosSimples.map((dados, i) => (\r\n                                                        dados.status_pedido == '1' ?\r\n                                                            <div key={Math.random()} className=\"bloco-pedidos\" onClick={() => handleShowPedido(dados._id, dados.id_empresa)}>\r\n                                                                <div className=\"pedido-time\">\r\n                                                                    <div className=\"tag-pedido\">\r\n                                                                        <div className=\"icon-pedido\">\r\n                                                                            {dados.entrega?.tipo_entrega == \"Retirada\" ? <FaStore /> : <FaMotorcycle />}\r\n                                                                        </div>\r\n                                                                        <span className=\"bold\">Pedido #{dados.id_pedido_counter ?? dados.id_pedido}</span>\r\n\r\n                                                                    </div>\r\n                                                                    <div className=\"time-container\">\r\n                                                                        <div className=\"time-div\">\r\n                                                                            <FaRegClock />\r\n                                                                            <span>{moment.parseZone(dados.createdAt).local().format(\"HH:mm\")}</span>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n\r\n                                                                <div className=\"cliente-total\">\r\n                                                                    <div className=\"bloco\">\r\n                                                                        <div className=\"linha\">\r\n                                                                            <div className=\"texto\">{dados.nome_cliente}</div>\r\n                                                                            {dados.counter_qtd_pedido > 1 ?\r\n                                                                                <div className=\"label-recorrencia-pedidoMoreThanOne\">\r\n                                                                                    <div className=\"caixaNumero\">\r\n                                                                                        <span style={{ fontSize: 10 }}>{dados.counter_qtd_pedido}</span>\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                                :\r\n                                                                                <div className=\"label-recorrencia-pedido\">\r\n                                                                                    <div className=\"caixaNumero\">\r\n                                                                                        <span style={{ fontSize: 10 }}>1º</span>\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                            }\r\n                                                                        </div>\r\n                                                                        <div className=\"linha\">\r\n                                                                            <div className=\"texto\">\r\n                                                                                {dados.celular_cliente}\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                    <div className=\"bloco\">\r\n                                                                        <div className=\"linha\">\r\n                                                                            <div className=\"texto bold\">Total:</div>\r\n                                                                            <div className=\"texto bold\">\r\n                                                                                R$ {dados.valor_total && dados.valor_total.toFixed(2).toString().replace('.', ',')}\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                        <div className=\"linha\">\r\n                                                                            <div className=\"texto\">Pagamento:</div>\r\n                                                                            <div className=\"texto\">{dados.tipo_pagamento}</div>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n\r\n                                                                <div className=\"bloco-entrega\">\r\n                                                                    {dados.entrega?.tipo_entrega == \"Retirada\" ?\r\n                                                                        <div className=\"flex-entrega\">\r\n                                                                            <div className=\"bloco-footer\">\r\n                                                                                <div className=\"pin\" style={{ display: 'flex', marginTop: 1 }}>\r\n                                                                                    <IoMdPin className=\"pin\" />\r\n                                                                                </div>\r\n                                                                                <div className=\"texto\">Retirada no Local</div>\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                        :\r\n                                                                        <div className=\"flex-entrega\">\r\n                                                                            <div className=\"bloco-footer\">\r\n                                                                                <div className=\"pin\">\r\n                                                                                    <IoMdPin className=\"pin\" />\r\n                                                                                </div>\r\n                                                                                <div className=\"texto\">\r\n                                                                                    {dados.entrega?.endereco && `${dados.entrega?.endereco?.rua}, ${dados.entrega?.endereco?.numero} \r\n                                                                        ${dados.entrega?.endereco?.complemento && \" - \" + dados.entrega?.endereco?.complemento}, ${dados.entrega?.endereco?.bairro}`}\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                            <div className=\"bloco\" onClick={(event) => handleAddEntregador(event, dados)}>\r\n                                                                                {dados.entregador ?\r\n                                                                                    <div className=\"texto underline\">{dados.entregador.name}</div>\r\n                                                                                    :\r\n                                                                                    <div className=\"texto underline\">Escolher Entregador</div>\r\n                                                                                }\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    }\r\n                                                                </div>\r\n\r\n                                                                <div style={{ marginTop: 10 }}>\r\n                                                                    <div className=\"button-avancar-pedido\">\r\n                                                                        <button onClick={(event) => handleAvancar(event, dados._id, dados.id_pedido, dados.status_pedido)}>\r\n                                                                            <div>\r\n                                                                                <a>Avançar Pedido</a>\r\n                                                                                <AiIcons.AiOutlineArrowRight style={{ marginLeft: \"2px\", fontSize: \"22px\", marginBottom: \"2px\", fontColor: 'white' }} />\r\n                                                                            </div>\r\n                                                                        </button>\r\n                                                                        {/*isLoading ? \r\n                                                                    <button>                                                               \r\n                                                                        <Loading type='spin' color='rgb(49, 140, 213)' height={32} width={32} />\r\n                                                                    </button>\r\n                                                                    :\r\n                                                                    <button onClick={(event) => handleAvancar(event, dados._id, dados.id_pedido, dados.status_pedido)}>                                                                 \r\n                                                                        <div>\r\n                                                                            <a>Avançar Pedido</a>\r\n                                                                            <AiIcons.AiOutlineArrowRight style={{marginLeft: \"2px\",fontSize: \"22px\",marginBottom: \"2px\", fontColor:'white'}}/>\r\n                                                                        </div>                                                                                                                                 \r\n                                                                    </button>\r\n                                                                */}\r\n                                                                    </div>\r\n                                                                </div>\r\n\r\n                                                            </div>\r\n                                                            :\r\n                                                            null\r\n\r\n                                                    ))\r\n\r\n                                            }\r\n\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                </Ordercolumn>\r\n\r\n                                {/* Pedidos em produção */}\r\n                                <Ordercolumn\r\n                                    typecolumn=\"production\"\r\n                                    className=\"column\"\r\n                                //style={{display:'table-row'}}\r\n                                >\r\n                                    <div className=\"containerProducao\">\r\n                                        <div className=\"header-column-producao\">\r\n                                            <div className=\"header-column--align\">\r\n                                                <h4>Em produção</h4>\r\n                                            </div>\r\n                                            <h4>{pedidosProducao.length}</h4>\r\n                                        </div>\r\n\r\n                                        <div className=\"blocoProducao\">\r\n\r\n                                            {pedidosProducao.length == 0 ?\r\n                                                <div key={Math.random()} className=\"column-container column-container--first\">\r\n                                                    <div className=\"text\">\r\n                                                        <p style={{ marginBottom: 0, justifyContent: 'center', textAlign: 'center', display: 'flex' }}>Nenhum pedido no momento.</p>\r\n                                                        <p style={{ marginBottom: 0, justifyContent: 'center', textAlign: 'center', display: 'flex' }}>\r\n                                                            Receba pedidos e visualize os que estão em produção.\r\n                                                        </p>\r\n                                                    </div>\r\n                                                </div>\r\n                                                :\r\n                                                pedidosSimples && pedidosSimples.map((dados, i) => (\r\n                                                    dados.status_pedido == '2' ?\r\n                                                        <div key={Math.random()} className=\"bloco-pedidos\" onClick={() => handleShowPedido(dados._id, dados.id_empresa)}>\r\n                                                            <div className=\"pedido-time\">\r\n                                                                <div className=\"tag-pedido\">\r\n                                                                    <div className=\"icon-pedido\">\r\n                                                                        {dados.entrega?.tipo_entrega == \"Retirada\" ? <FaStore /> : <FaMotorcycle />}\r\n                                                                    </div>\r\n                                                                    <span className=\"bold\">Pedido #{dados.id_pedido_counter ?? dados.id_pedido}</span>\r\n\r\n                                                                </div>\r\n                                                                <div className=\"time-container\">\r\n                                                                    <div className=\"time-div\">\r\n                                                                        <FaRegClock />\r\n                                                                        <span>{moment.parseZone(dados.createdAt).local().format(\"HH:mm\")}</span>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                            <div className=\"cliente-total\">\r\n                                                                <div className=\"bloco\">\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto\">{dados.nome_cliente}</div>\r\n                                                                        {dados.counter_qtd_pedido > 1 ?\r\n                                                                            <div className=\"label-recorrencia-pedidoMoreThanOne\">\r\n                                                                                <div className=\"caixaNumero\">\r\n                                                                                    <span style={{ fontSize: 10 }}>{dados.counter_qtd_pedido}</span>\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                            :\r\n                                                                            <div className=\"label-recorrencia-pedido\">\r\n                                                                                <div className=\"caixaNumero\">\r\n                                                                                    <span style={{ fontSize: 10 }}>1º</span>\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                        }\r\n                                                                    </div>\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto\">\r\n                                                                            {dados.celular_cliente}\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                                <div className=\"bloco\">\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto bold\">Total:</div>\r\n                                                                        <div className=\"texto bold\">\r\n                                                                            R$ {dados.valor_total && dados.valor_total.toFixed(2).toString().replace('.', ',')}\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto\">Pagamento:</div>\r\n                                                                        <div className=\"texto\">{dados.tipo_pagamento}</div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                            <div className=\"bloco-entrega\">\r\n                                                                {dados.createdBy.toLowerCase().includes(\"mesa\") ?\r\n                                                                    (\r\n                                                                        <div className=\"flex-entrega\">\r\n                                                                            <div className=\"bloco-footer\">\r\n                                                                                <div className=\"pin\" style={{ display: 'flex', marginTop: 1 }}>\r\n                                                                                    <IoMdPin className=\"pin\" />\r\n                                                                                </div>\r\n                                                                                <div className=\"texto\">{dados.createdBy}</div>\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    ) : (\r\n                                                                        <>\r\n                                                                            {dados.entrega?.tipo_entrega == \"Retirada\" ?\r\n                                                                                <div className=\"flex-entrega\">\r\n                                                                                    <div className=\"bloco-footer\">\r\n                                                                                        <div className=\"pin\" style={{ display: 'flex', marginTop: 1 }}>\r\n                                                                                            <IoMdPin className=\"pin\" />\r\n                                                                                        </div>\r\n                                                                                        <div className=\"texto\">Retirada no Local</div>\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                                :\r\n                                                                                <div className=\"flex-entrega\">\r\n                                                                                    <div className=\"bloco-footer\">\r\n                                                                                        <div className=\"pin\">\r\n                                                                                            <IoMdPin className=\"pin\" />\r\n                                                                                        </div>\r\n                                                                                        <div className=\"texto\">\r\n                                                                                            {dados.entrega && `${dados.entrega?.endereco?.rua}, ${dados.entrega?.endereco?.numero} \r\n                                                                                ${dados.entrega?.endereco?.complemento && \" - \" + dados.entrega?.endereco?.complemento}, ${dados.entrega?.endereco?.bairro}`}\r\n                                                                                        </div>\r\n                                                                                    </div>\r\n                                                                                    <div className=\"bloco\" onClick={(event) => handleAddEntregador(event, dados)}>\r\n                                                                                        {dados.entregador ?\r\n                                                                                            <div className=\"texto underline\">{dados.entregador.name}</div>\r\n                                                                                            :\r\n                                                                                            <div className=\"texto underline\">Escolher Entregador</div>\r\n                                                                                        }\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                            }\r\n                                                                        </>\r\n                                                                    )}\r\n                                                            </div>\r\n\r\n                                                            <div style={{ marginTop: 10 }}>\r\n                                                                <div className=\"button-avancar-pedido\">\r\n\r\n                                                                    <button onClick={(event) => handleAvancar(event, dados._id, dados.id_pedido, dados.status_pedido)}>\r\n                                                                        <div>\r\n                                                                            <a>Avançar Pedido</a>\r\n                                                                            <AiIcons.AiOutlineArrowRight style={{ marginLeft: \"2px\", fontSize: \"22px\", marginBottom: \"2px\", fontColor: 'white' }} />\r\n                                                                        </div>\r\n                                                                    </button>\r\n\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                        </div>\r\n                                                        :\r\n                                                        null\r\n                                                ))\r\n                                            }\r\n\r\n                                        </div>\r\n\r\n                                    </div>\r\n\r\n                                </Ordercolumn>\r\n\r\n                                {/* Pedidos prontos para Entrega */}\r\n                                <Ordercolumn\r\n                                    _ngcontent-ng-c2041748172=\"\"\r\n                                    typecolumn=\"ready\"\r\n                                    className=\"column\"\r\n                                    _nghost-ng-c2960941283=\"\"\r\n                                    style={{ display: 'table-row' }}\r\n                                >\r\n                                    <div className=\"containerPronto\">\r\n                                        <div className=\"header-column-pronto\">\r\n                                            <div className=\"header-column--align\" style={{ display: 'inline-flex' }}>\r\n                                                <h4>Prontos para entrega</h4>\r\n                                            </div>\r\n                                            <h4>{pedidosEntrega.length}</h4>\r\n                                        </div>\r\n\r\n                                        <div className=\"blocoPronto\">\r\n                                            {pedidosEntrega.length == 0 ?\r\n                                                <div key={Math.random()} className=\"column-container column-container--first\">\r\n                                                    <div className=\"text\">\r\n                                                        <p style={{ marginBottom: 0, justifyContent: 'center', textAlign: 'center', display: 'flex' }}>Nenhum pedido no momento.</p>\r\n                                                        <p style={{ marginBottom: 0, justifyContent: 'center', textAlign: 'center', display: 'flex' }}>\r\n                                                            Receba pedidos e visualize os que estão prontos para entrega.\r\n                                                        </p>\r\n                                                    </div>\r\n                                                </div>\r\n                                                :\r\n                                                pedidosSimples && pedidosSimples.map((dados, i) => (\r\n                                                    dados.status_pedido == '3' ?\r\n                                                        <div key={Math.random()} className=\"bloco-pedidos\" onClick={() => handleShowPedido(dados._id, dados.id_empresa)}>\r\n                                                            <div className=\"pedido-time\">\r\n                                                                <div className=\"tag-pedido\">\r\n                                                                    <div className=\"icon-pedido\">\r\n                                                                        {dados.entrega?.tipo_entrega == \"Retirada\" ? <FaStore /> : <FaMotorcycle />}\r\n                                                                    </div>\r\n                                                                    <span className=\"bold\">Pedido #{dados.id_pedido_counter ?? dados.id_pedido}</span>\r\n\r\n                                                                </div>\r\n                                                                <div className=\"time-container\">\r\n                                                                    <div className=\"time-div\">\r\n                                                                        <FaRegClock />\r\n                                                                        <span>{moment.parseZone(dados.createdAt).local().format(\"HH:mm\")}</span>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                            <div className=\"cliente-total\">\r\n                                                                <div className=\"bloco\">\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto\">{dados.nome_cliente}</div>\r\n                                                                        {dados.counter_qtd_pedido > 1 ?\r\n                                                                            <div className=\"label-recorrencia-pedidoMoreThanOne\">\r\n                                                                                <div className=\"caixaNumero\">\r\n                                                                                    <span style={{ fontSize: 10 }}>{dados.counter_qtd_pedido}</span>\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                            :\r\n                                                                            <div className=\"label-recorrencia-pedido\">\r\n                                                                                <div className=\"caixaNumero\">\r\n                                                                                    <span style={{ fontSize: 10 }}>1º</span>\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                        }\r\n                                                                    </div>\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto\">\r\n                                                                            {dados.celular_cliente}\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                                <div className=\"bloco\">\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto bold\">Total:</div>\r\n                                                                        <div className=\"texto bold\">\r\n                                                                            R$ {dados.valor_total && dados.valor_total.toFixed(2).toString().replace('.', ',')}\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto\">Pagamento:</div>\r\n                                                                        <div className=\"texto\">{dados.tipo_pagamento}</div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                            <div className=\"bloco-entrega\">\r\n                                                                {dados.createdBy.toLowerCase().includes(\"mesa\") ?\r\n                                                                    (\r\n                                                                        <div className=\"flex-entrega\">\r\n                                                                            <div className=\"bloco-footer\">\r\n                                                                                <div className=\"pin\" style={{ display: 'flex', marginTop: 1 }}>\r\n                                                                                    <IoMdPin className=\"pin\" />\r\n                                                                                </div>\r\n                                                                                <div className=\"texto\">{dados.createdBy}</div>\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    ) : (\r\n                                                                        <>\r\n                                                                            {dados.entrega?.tipo_entrega == \"Retirada\" ?\r\n                                                                                <div className=\"flex-entrega\">\r\n                                                                                    <div className=\"bloco-footer\">\r\n                                                                                        <div className=\"pin\" style={{ display: 'flex', marginTop: 1 }}>\r\n                                                                                            <IoMdPin className=\"pin\" />\r\n                                                                                        </div>\r\n                                                                                        <div className=\"texto\">Retirada no Local</div>\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                                :\r\n                                                                                <div className=\"flex-entrega\">\r\n                                                                                    <div className=\"bloco-footer\">\r\n                                                                                        <div className=\"pin\">\r\n                                                                                            <IoMdPin className=\"pin\" />\r\n                                                                                        </div>\r\n                                                                                        <div className=\"texto\">\r\n                                                                                            {dados.entrega && `${dados.entrega?.endereco?.rua}, ${dados.entrega?.endereco?.numero} \r\n                                                                                ${dados.entrega?.endereco?.complemento && \" - \" + dados.entrega?.endereco?.complemento}, ${dados.entrega?.endereco?.bairro}`}\r\n                                                                                        </div>\r\n                                                                                    </div>\r\n                                                                                    <div className=\"bloco\" onClick={(event) => handleAddEntregador(event, dados)}>\r\n                                                                                        {dados.entregador ?\r\n                                                                                            <div className=\"texto underline\">{dados.entregador.name}</div>\r\n                                                                                            :\r\n                                                                                            <div className=\"texto underline\">Escolher Entregador</div>\r\n                                                                                        }\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                            }\r\n                                                                        </>\r\n                                                                    )}\r\n                                                            </div>\r\n\r\n                                                            <div style={{ marginTop: 10 }}>\r\n                                                                <div className=\"button-avancar-pedido\">\r\n\r\n                                                                    <button onClick={(event) => handleAvancar(event, dados._id, dados.id_pedido, dados.status_pedido)}>\r\n                                                                        <div>\r\n                                                                            <a>Avançar Pedido</a>\r\n                                                                            <AiIcons.AiOutlineArrowRight style={{ marginLeft: \"2px\", fontSize: \"22px\", marginBottom: \"2px\", fontColor: 'white' }} />\r\n                                                                        </div>\r\n                                                                    </button>\r\n\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                        </div>\r\n                                                        :\r\n                                                        null\r\n                                                ))\r\n                                            }\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                </Ordercolumn>\r\n\r\n\r\n                                {/* Pedidos Finalizados */}\r\n                                <Ordercolumn\r\n                                    _ngcontent-ng-c2041748172=\"\"\r\n                                    typecolumn=\"ready\"\r\n                                    className=\"column\"\r\n                                    _nghost-ng-c2960941283=\"\"\r\n                                    style={{ display: 'table-row' }}\r\n                                >\r\n                                    <div className=\"containerFinalizado\">\r\n                                        <div className=\"header-column-finalizado\">\r\n                                            <div className=\"header-column--align\" style={{ display: 'inline-flex' }}>\r\n                                                <h4>Finalizados</h4>\r\n                                                <div className=\"header-column-clear\" style={{ display: 'flex', alignItems: 'center', marginLeft: 6 }}>\r\n                                                    <pedeja-icon\r\n                                                        iconname=\"check-circle\"\r\n                                                        iconsize={18}\r\n                                                        pedejatooltip=\"Finalizar todos os pedidos não relacionados a mesa\"\r\n                                                        iconcolor=\"#FFFFFF\"\r\n                                                        style={{ height: 18, display: 'flex', alignItems: 'center' }}\r\n                                                    >\r\n                                                        <svg\r\n                                                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                            width={18}\r\n                                                            height={18}\r\n                                                            viewBox=\"0 0 24 24\"\r\n                                                            fill=\"none\"\r\n                                                            stroke=\"#FFFFFF\"\r\n                                                            strokeWidth={3}\r\n                                                            strokeLinecap=\"round\"\r\n                                                            strokeLinejoin=\"round\"\r\n                                                            className=\"feather feather-check-circle\"\r\n                                                        >\r\n                                                            <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\" />\r\n                                                            <polyline points=\"22 4 12 14.01 9 11.01\" />\r\n                                                        </svg>\r\n                                                    </pedeja-icon>\r\n                                                    {/**/}\r\n                                                </div>\r\n                                            </div>\r\n                                            <h4>{pedidosFinalizado.length}</h4>\r\n                                        </div>\r\n\r\n                                        <div className=\"blocoFinalizado\">\r\n                                            {pedidosFinalizado.length == 0 ?\r\n                                                <div key={Math.random()} className=\"column-container column-container--first\">\r\n                                                    <div className=\"text\">\r\n                                                        <p style={{ marginBottom: 0, justifyContent: 'center', textAlign: 'center', display: 'flex' }}>Nenhum pedido no momento.</p>\r\n                                                        <p style={{ marginBottom: 0, justifyContent: 'center', textAlign: 'center', display: 'flex' }}>\r\n                                                            Receba pedidos e visualize os finalizados.\r\n                                                        </p>\r\n                                                    </div>\r\n                                                </div>\r\n                                                :\r\n                                                pedidosFinalizados && pedidosFinalizados.map((dados, i) => (\r\n                                                    dados.status_pedido == '4' ?\r\n                                                        <div key={Math.random()} className=\"bloco-pedidos\" onClick={() => handleShowPedido(dados._id, dados.id_empresa)}>\r\n                                                            <div className=\"pedido-time\">\r\n                                                                <div className=\"tag-pedido\">\r\n                                                                    <div className=\"icon-pedido\">\r\n                                                                        {dados.entrega?.tipo_entrega == \"Retirada\" ? <FaStore /> : <FaMotorcycle />}\r\n                                                                    </div>\r\n                                                                    <span className=\"bold\">Pedido #{dados.id_pedido_counter ?? dados.id_pedido}</span>\r\n                                                                    {dados.cancelado &&\r\n                                                                        <div className=\"divSpanCancelado\">\r\n                                                                            <span>CANCELADO</span>\r\n                                                                        </div>\r\n                                                                    }\r\n                                                                </div>\r\n                                                                <div className=\"time-container\">\r\n                                                                    <div className=\"time-div\">\r\n                                                                        <FaRegClock />\r\n                                                                        <span>{moment.parseZone(dados.createdAt).local().format(\"HH:mm\")}</span>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                            <div className=\"cliente-total\">\r\n                                                                <div className=\"bloco\">\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto\">{dados.nome_cliente}</div>\r\n                                                                        {dados.counter_qtd_pedido > 1 ?\r\n                                                                            <div className=\"label-recorrencia-pedidoMoreThanOne\">\r\n                                                                                <div className=\"caixaNumero\">\r\n                                                                                    <span style={{ fontSize: 10 }}>{dados.counter_qtd_pedido}</span>\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                            :\r\n                                                                            <div className=\"label-recorrencia-pedido\">\r\n                                                                                <div className=\"caixaNumero\">\r\n                                                                                    <span style={{ fontSize: 10 }}>1º</span>\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                        }\r\n                                                                    </div>\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto\">\r\n                                                                            {dados.celular_cliente}\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                                <div className=\"bloco\">\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto bold\">Total:</div>\r\n                                                                        <div className=\"texto bold\">\r\n                                                                            R$ {dados.valor_total && dados.valor_total.toFixed(2).toString().replace('.', ',')}\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                    <div className=\"linha\">\r\n                                                                        <div className=\"texto\">Pagamento:</div>\r\n                                                                        <div className=\"texto\">{dados.tipo_pagamento}</div>\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </div>\r\n\r\n                                                            <div className=\"bloco-entrega\">\r\n                                                                {dados.createdBy.toLowerCase().includes(\"mesa\") ?\r\n                                                                    (\r\n                                                                        <div className=\"flex-entrega\">\r\n                                                                            <div className=\"bloco-footer\">\r\n                                                                                <div className=\"pin\" style={{ display: 'flex', marginTop: 1 }}>\r\n                                                                                    <IoMdPin className=\"pin\" />\r\n                                                                                </div>\r\n                                                                                <div className=\"texto\">{dados.createdBy}</div>\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    ) : (\r\n                                                                        <>\r\n                                                                            {dados.entrega?.tipo_entrega == \"Retirada\" ?\r\n                                                                                <div className=\"flex-entrega\">\r\n                                                                                    <div className=\"bloco-footer\">\r\n                                                                                        <div className=\"pin\" style={{ display: 'flex', marginTop: 1 }}>\r\n                                                                                            <IoMdPin className=\"pin\" />\r\n                                                                                        </div>\r\n                                                                                        <div className=\"texto\">Retirada no Local</div>\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                                :\r\n                                                                                <div className=\"flex-entrega\">\r\n                                                                                    <div className=\"bloco-footer\">\r\n                                                                                        <div className=\"pin\">\r\n                                                                                            <IoMdPin className=\"pin\" />\r\n                                                                                        </div>\r\n                                                                                        <div className=\"texto\">\r\n                                                                                            {dados.entrega && `${dados.entrega?.endereco?.rua}, ${dados.entrega?.endereco?.numero} \r\n                                                                                ${dados.entrega?.endereco?.complemento && \" - \" + dados.entrega?.endereco?.complemento}, ${dados.entrega?.endereco?.bairro}`}\r\n                                                                                        </div>\r\n                                                                                    </div>\r\n                                                                                    <div className=\"bloco\" onClick={(event) => handleAddEntregador(event, dados)}>\r\n                                                                                        {dados.entregador ?\r\n                                                                                            <div className=\"texto underline\">{dados.entregador.name}</div>\r\n                                                                                            :\r\n                                                                                            <div className=\"texto underline\">Escolher Entregador</div>\r\n                                                                                        }\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                            }\r\n                                                                        </>\r\n                                                                    )}\r\n                                                            </div>\r\n\r\n\r\n\r\n                                                        </div>\r\n                                                        :\r\n                                                        null\r\n\r\n                                                ))\r\n                                            }\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                </Ordercolumn>\r\n\r\n                            </div>\r\n                        ) : (\r\n                            <MobileKanban\r\n                                tempoBalcaoMinBD={tempoBalcaoMinBD}\r\n                                tempoBalcaoMaxBD={tempoBalcaoMaxBD}\r\n                                tempoEntregaMinBD={tempoEntregaMinBD}\r\n                                tempoEntregaMaxBD={tempoEntregaMaxBD}\r\n                                tipoImpressao={tipoImpressao}\r\n                                pedidosAnalise={pedidosAnalise}\r\n                                pedidosFinalizados={pedidosFinalizados}\r\n                                pedidosSimples={pedidosSimples}\r\n                                pedidosProducao={pedidosProducao}\r\n                                pedidosEntrega={pedidosEntrega}\r\n                                pedidosFinalizado={pedidosFinalizado}\r\n                                filterIsLoading={filterIsLoading}\r\n                                handleShowPedido={handleShowPedido}\r\n                                handleEditTempoEntrega={handleEditTempoEntrega}\r\n                                handleAddEntregador={handleAddEntregador}\r\n                                handleAvancar={handleAvancar}\r\n                                userID={userID}\r\n                            />\r\n                        )}\r\n                    </div>\r\n                </Teste>\r\n\r\n            </PermissionGate>\r\n        </>\r\n\r\n\r\n    );\r\n};\r\n\r\nexport default User;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,OAAOC,OAAO,MAAM,eAAe;AACnC,SAASC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAEC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,uBAAuB;AACjF,OAAO,aAAa;AACpB,OAAO,uBAAuB;AAC9B,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,WAAW,MAAM,aAAa;AACrC;AACA,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,WAAW,QAAQ,qBAAqB;AAEjD,SACIC,OAAO,EACPC,iBAAiB,EACjBC,SAAS,EACTC,iBAAiB,EACjBC,kBAAkB,EAClBC,4BAA4B,EAC5BC,kBAAkB,EAClBC,yBAAyB,EACzBC,6BAA6B,QAC1B,oBAAoB;AAE3B,OAAOC,cAAc,MAAM,+BAA+B;AAE1D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,sBAAsB,QAAQ,gBAAgB;AACvD,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,OAAO,KAAKC,OAAO,MAAM,gBAAgB;AACzC,SAASC,YAAY,EAAEC,UAAU,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,OAAO,QAAQ,gBAAgB;AAExC,OAAOC,qBAAqB,MAAM,wCAAwC;AAC1E,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,kBAAkB,MAAM,sBAAsB;AAErD,SAASC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,KAAK,GAAGxC,MAAM,CAACyC,GAAG;AACxB;AACA;AACA,oBAAoB,CAAC;EAAEC;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAfIH,KAAK;AAiBX,MAAMI,WAAW,GAAG5C,MAAM,CAACyC,GAAG;AAC9B;AACA,CAAC;AAACI,GAAA,GAFID,WAAW;AAIjB,MAAME,gBAAgB,GAAG9C,MAAM,CAACyC,GAAG;AACnC;AACA;AACA;AACA;AACA,aAAa,CAAC;EAAEM;AAAY,CAAC,KAAMA,WAAW,GAAG,MAAM,GAAG,EAAG;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAGD,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACf;EACA,MAAMC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,CAAC;EACxC,MAAMM,YAAY,GAAGH,YAAY,CAACI,GAAG;EACrC,MAAMC,SAAS,GAAGL,YAAY,CAACM,UAAU;EACzC,MAAMC,eAAe,GAAGP,YAAY,CAACQ,IAAI;EAEzC,MAAMC,SAAS,GAAG,uFAAuF;EACzG,MAAMC,aAAa,GAAGZ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMY,IAAI,GAAG7D,QAAQ,CAAC8D,GAAG,CAACC,OAAO,CAACH,aAAa,EAAED,SAAS,CAAC,CAACK,QAAQ,CAAChE,QAAQ,CAACiE,GAAG,CAACC,IAAI,CAAC;EACvF;EACA,MAAMC,SAAS,GAAGhB,IAAI,CAACC,KAAK,CAACS,IAAI,CAAC;EAClC;EACA;EACA,MAAMO,MAAM,GAAGD,SAAS,CAACb,GAAG;EAC5B,MAAM,CAACe,2BAA2B,EAAEC,0BAA0B,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAChF,MAAM,CAACuF,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EACxD;EACA,MAAMyF,YAAY,GAAG;IACjBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACX,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7F,QAAQ,CAACyF,YAAY,CAAC;EAE1D,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,IAAI,CAAC;EAElD,MAAM;IAAEqD,OAAO;IAAE2C;EAAW,CAAC,GAAG/F,UAAU,CAACkB,cAAc,CAAC;EAAC,CAAC,CAAC;;EAE7D,IAAI,CAAC8E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlG,QAAQ,CAAC,CAAC,CAAC;EACzD,IAAI,CAACmG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpG,QAAQ,CAAC,CAAC,CAAC;EACzD,IAAI,CAACqG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtG,QAAQ,CAAC,CAAC,CAAC;EAC3D,IAAI,CAACuG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxG,QAAQ,CAAC,CAAC,CAAC;EAC3D,IAAI,CAACyG,aAAa,EAAEC,gBAAgB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2G,OAAO,EAAEC,UAAU,CAAC,GAAG5G,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM,CAAC6G,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9G,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAM,CAAC+G,cAAc,EAAEC,iBAAiB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiH,eAAe,EAAEC,kBAAkB,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmH,cAAc,EAAEC,iBAAiB,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACA,MAAM,CAACuH,cAAc,EAAEC,iBAAiB,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyH,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC2H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5H,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC6H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9H,QAAQ,CAAC,IAAI,CAAC;EAEtE,MAAM+H,QAAQ,GAAGhG,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiG,UAAU,EAAEC,aAAa,CAAC,GAAGjI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACkI,eAAe,EAAEC,kBAAkB,CAAC,GAAGnI,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoI,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrI,QAAQ,CAAC,IAAI,CAAC;EAC1E,MAAM,CAACsI,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvI,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtE,MAAM,CAACwI,WAAW,EAAEC,cAAc,CAAC,GAAGzI,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0I,eAAe,EAAEC,kBAAkB,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5D;EACA;EACA,IAAI4I,YAAY,GAAG,IAAI;EACvB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9I,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhJ,QAAQ,CAAC,KAAK,CAAC;EACjE;;EAEA;EACA,MAAMiJ,WAAW,GAAGA,CAAA,KAAM;IAAApF,EAAA;IACtB,MAAM,CAACqF,QAAQ,EAAEC,WAAW,CAAC,GAAGnJ,QAAQ,CAACoJ,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAElEnJ,SAAS,CAAC,MAAM;MACZ,MAAMoJ,YAAY,GAAGA,CAAA,KAAM;QACvBH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;MACzC,CAAC;MAEDD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;MAC/C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IACnE,CAAC,EAAE,EAAE,CAAC;IAEN,OAAOJ,QAAQ;EACnB,CAAC;EAACrF,EAAA,CAbIoF,WAAW;EAejB,MAAMC,QAAQ,GAAGD,WAAW,CAAC,CAAC;EAE9B/I,SAAS,CAAC,MAAM;IACZ,IAAIyG,OAAO,EAAE;MACT,MAAM8C,sBAAsB,GAAG,MAAAA,CAAA,KAAY;QACvC,MAAMC,qBAAqB,GAAG,MAAMC,mBAAmB,CAAC,CAAC;QACzD,MAAMC,yBAAyB,GAAG,MAAMC,uBAAuB,CAAC,CAAC;QACjErC,iBAAiB,CAACkC,qBAAqB,CAAC;QACxChC,qBAAqB,CAACkC,yBAAyB,CAAC;MACpD,CAAC;MACDH,sBAAsB,CAAC,CAAC;MACxB7C,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,CAACD,OAAO,CAAC,CAAC;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAII;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGI;AACJ;AACA;AACA;AACA;AACA;;EAGI;EACA,MAAMgD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAMG,QAAQ,GAAG,MAAMxI,iBAAiB,CAAC8D,MAAM,CAAC;IAChD,MAAM2E,gBAAgB,GAAG,MAAMnI,yBAAyB,CAACwD,MAAM,EAAEb,SAAS,EAAEE,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,OAAO,CAAC;;IAEtH;IACAiC,gBAAgB,CAACoD,QAAQ,CAACE,IAAI,CAACC,OAAO,CAACC,cAAc,CAAC;IACtDhE,mBAAmB,CAACiE,QAAQ,CAACL,QAAQ,CAACE,IAAI,CAACC,OAAO,CAACG,cAAc,CAAC,CAAC;IACnEhE,mBAAmB,CAAC+D,QAAQ,CAACL,QAAQ,CAACE,IAAI,CAACC,OAAO,CAACI,cAAc,CAAC,CAAC;IACnE/D,oBAAoB,CAAC6D,QAAQ,CAACL,QAAQ,CAACE,IAAI,CAACC,OAAO,CAACK,eAAe,CAAC,CAAC;IACrE9D,oBAAoB,CAAC2D,QAAQ,CAACL,QAAQ,CAACE,IAAI,CAACC,OAAO,CAACM,eAAe,CAAC,CAAC;IACrEvB,oBAAoB,CAACc,QAAQ,CAACE,IAAI,CAACC,OAAO,CAACO,mBAAmB,CAAC;IAC/DrC,kBAAkB,CAAC,KAAK,CAAC;IACzB,OAAO4B,gBAAgB,CAACC,IAAI,CAACS,OAAO;EACxC,CAAC;;EAED;EACA,MAAMZ,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IACxC,MAAMa,cAAc,GAAG,MAAM7I,6BAA6B,CAACuD,MAAM,EAAEb,SAAS,EAAEE,eAAe,EAAE,OAAO,CAAC;IACvG0D,kBAAkB,CAAC,KAAK,CAAC;IACzB,OAAOuC,cAAc,CAACV,IAAI,CAACS,OAAO;EACtC,CAAC;;EAED;EACA,MAAME,gCAAgC,GAAG,MAAOC,aAAa,IAAK;IAC9D,MAAMlB,qBAAqB,GAAG,MAAMC,mBAAmB,CAAC,CAAC;IACzD,MAAMC,yBAAyB,GAAG,MAAMC,uBAAuB,CAAC,CAAC;IAEjE,IAAIgB,sBAAsB,GAAGnB,qBAAqB;IAClD,IAAIoB,0BAA0B,GAAGlB,yBAAyB;IAE1D,IAAIgB,aAAa,KAAK,UAAU,EAAE;MAC9BC,sBAAsB,GAAGnB,qBAAqB,CAACqB,MAAM,CAACC,MAAM;QAAA,IAAAC,eAAA;QAAA,OAAI,EAAAA,eAAA,GAAAD,MAAM,CAACE,OAAO,cAAAD,eAAA,uBAAdA,eAAA,CAAgBE,YAAY,CAACC,WAAW,CAAC,CAAC,MAAK,SAAS;MAAA,EAAC;MACzHN,0BAA0B,GAAGlB,yBAAyB,CAACmB,MAAM,CAACC,MAAM;QAAA,IAAAK,gBAAA,EAAAC,qBAAA;QAAA,OAAI,EAAAD,gBAAA,GAAAL,MAAM,CAACE,OAAO,cAAAG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBF,YAAY,cAAAG,qBAAA,uBAA5BA,qBAAA,CAA8BF,WAAW,CAAC,CAAC,MAAK,SAAS;MAAA,EAAC;IACtI,CAAC,MAAM,IAAIR,aAAa,KAAK,QAAQ,EAAE;MACnCC,sBAAsB,GAAGnB,qBAAqB,CAACqB,MAAM,CAACC,MAAM;QAAA,IAAAO,gBAAA,EAAAC,qBAAA;QAAA,OAAI,EAAAD,gBAAA,GAAAP,MAAM,CAACE,OAAO,cAAAK,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,YAAY,cAAAK,qBAAA,uBAA5BA,qBAAA,CAA8BJ,WAAW,CAAC,CAAC,MAAK,UAAU;MAAA,EAAC;MAC3HN,0BAA0B,GAAGlB,yBAAyB,CAACmB,MAAM,CAACC,MAAM;QAAA,IAAAS,gBAAA,EAAAC,qBAAA;QAAA,OAAI,EAAAD,gBAAA,GAAAT,MAAM,CAACE,OAAO,cAAAO,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBN,YAAY,cAAAO,qBAAA,uBAA5BA,qBAAA,CAA8BN,WAAW,CAAC,CAAC,MAAK,UAAU;MAAA,EAAC;IACvI,CAAC,MAAM,IAAIR,aAAa,KAAK,OAAO,EAAE;MAClCC,sBAAsB,GAAGnB,qBAAqB,CAACqB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACW,SAAS,CAACP,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAAC,MAAM,CAAC,CAAC;MAChHd,0BAA0B,GAAGlB,yBAAyB,CAACmB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACW,SAAS,CAACP,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC5H;;IAEA;IACApE,iBAAiB,CAACqD,sBAAsB,CAAC;IACzCnD,qBAAqB,CAACoD,0BAA0B,CAAC;EACrD,CAAC;EAED5K,SAAS,CAAC,MAAM;IACZ;IACAiI,kBAAkB,CAAC,IAAI,CAAC;IACxBwC,gCAAgC,CAAC9D,iBAAiB,CAAC;EACvD,CAAC,EAAE,CAACF,OAAO,CAAC,CAAC;;EAEb;EACAzG,SAAS,CAAC,MAAM;IACZ8G,iBAAiB,CAAC,EAAE,CAAC;IACrBE,kBAAkB,CAAC,EAAE,CAAC;IACtBE,iBAAiB,CAAC,EAAE,CAAC;IAErBG,cAAc,CAACsE,OAAO,CAAEC,KAAK,IAAK;MAC9B,IAAIA,KAAK,CAACC,aAAa,KAAK,GAAG,EAAE;QAC7B/E,iBAAiB,CAACgF,iBAAiB,IAAI,CAAC,GAAGA,iBAAiB,EAAEF,KAAK,CAAC,CAAC;MACzE,CAAC,MAAM,IAAIA,KAAK,CAACC,aAAa,KAAK,GAAG,EAAE;QACpC7E,kBAAkB,CAAC8E,iBAAiB,IAAI,CAAC,GAAGA,iBAAiB,EAAEF,KAAK,CAAC,CAAC;MAC1E,CAAC,MAAM,IAAIA,KAAK,CAACC,aAAa,KAAK,GAAG,EAAE;QACpC3E,iBAAiB,CAAC4E,iBAAiB,IAAI,CAAC,GAAGA,iBAAiB,EAAEF,KAAK,CAAC,CAAC;MACzE;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,CAACvE,cAAc,EAAEZ,OAAO,CAAC,CAAC,CAAC,CAAE;;EAEhC;EACAzG,SAAS,CAAC,MAAM;IACZoH,oBAAoB,CAAC,EAAE,CAAC;IAExBG,kBAAkB,CAACoE,OAAO,CAAEC,KAAK,IAAK;MAClC,IAAIA,KAAK,CAACC,aAAa,KAAK,GAAG,EAAE;QAC7BzE,oBAAoB,CAAC0E,iBAAiB,IAAI,CAAC,GAAGA,iBAAiB,EAAEF,KAAK,CAAC,CAAC;MAC5E;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,CAACrE,kBAAkB,EAAEd,OAAO,CAAC,CAAC,CAAC,CAAE;;EAEpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAOI,MAAMsF,aAAa,GAAG7C,MAAM,CAAC8C,QAAQ,CAACC,QAAQ,KAAK,WAAW;EAC9D,MAAMC,MAAM,GAAGH,aAAa,GACtBI,OAAO,CAACC,GAAG,CAACC,wBAAwB,GACpCF,OAAO,CAACC,GAAG,CAACE,yBAAyB;EAE3CtM,SAAS,CAAC,MAAM;IACZ;IACA,MAAMuM,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC7B;MACA,MAAMC,iBAAiB,GAAG,MAAM/C,mBAAmB,CAAC,CAAC;MACrD;MACA,MAAMgD,qBAAqB,GAAG,MAAM9C,uBAAuB,CAAC,CAAC;MAC7D,MAAM+C,UAAU,GAAG,CAAC,GAAGF,iBAAiB,EAAE,GAAGC,qBAAqB,CAAC;MACnEhE,kBAAkB,CAACiE,UAAU,CAAC;IAClC,CAAC;IAEDH,YAAY,CAAC,CAAC,CAAC,CAAC;;IAEhB;IACA,MAAMI,KAAK,GAAGT,MAAM;IACpB;IACA,MAAMU,MAAM,GAAG5L,EAAE,CAAC2L,KAAK,EAAE;MACrBE,eAAe,EAAE,IAAI;MACrBC,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,IAAI,EAAE;QAAEC,KAAK,EAAElJ,YAAY,CAACC,OAAO,CAAC,OAAO;MAAE,CAAC;MAC9CkJ,YAAY,EAAE,IAAI;MAAE;MACpBC,oBAAoB,EAAE,EAAE;MAAE;MAC1BC,iBAAiB,EAAE,IAAI,CAAE;IAC7B,CAAC,CAAC;IAEFP,MAAM,CAACQ,EAAE,CAAC,SAAS,EAAE,MAAM;MACvBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACvC,CAAC,CAAC;IAEFV,MAAM,CAACQ,EAAE,CAAC,YAAY,EAAGG,MAAM,IAAK;MAChCF,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAED,MAAM,CAAC;IACpD,CAAC,CAAC;IAEFX,MAAM,CAACQ,EAAE,CAAC,mBAAmB,EAAGK,OAAO,IAAK;MACxCJ,OAAO,CAACC,GAAG,CAAC,0BAA0BG,OAAO,EAAE,CAAC;IACpD,CAAC,CAAC;IAEFb,MAAM,CAACc,IAAI,CAAC,MAAM,EAAE,MAAM;MACtBL,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DV,MAAM,CAACe,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC,CAAC;;IAEF;IACA,MAAMC,QAAQ,GAAG,GAAG1I,MAAM,IAAI2I,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;IAC1ClB,MAAM,CAACe,IAAI,CAAC,iBAAiB,EAAE;MAAEI,SAAS,EAAE5J,YAAY,CAACW,QAAQ,CAAC,CAAC;MAAE8I;IAAS,CAAC,CAAC;IAEhF,MAAMI,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACjCX,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpC,MAAMW,kBAAkB,GAAG,MAAMxE,mBAAmB,CAAC,CAAC;MACtD,MAAMyE,sBAAsB,GAAG,MAAMvE,uBAAuB,CAAC,CAAC;MAC9D,MAAMwE,UAAU,GAAG,CAAC,GAAGF,kBAAkB,EAAE,GAAGC,sBAAsB,CAAC;MACrEzF,kBAAkB,CAAC0F,UAAU,CAAC;MAC9B7G,iBAAiB,CAAC2G,kBAAkB,CAAC;MACrCzG,qBAAqB,CAAC0G,sBAAsB,CAAC;IACjD,CAAC;IAEDtB,MAAM,CAACQ,EAAE,CAAC,YAAY,EAAEY,gBAAgB,CAAC;;IAEzC;IACA,OAAO,MAAM;MACTpB,MAAM,CAACwB,GAAG,CAAC,YAAY,CAAC;MACxBxB,MAAM,CAACyB,UAAU,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;EACL,CAAC,EAAE,CAAChK,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEjBrE,SAAS,CAAC,MAAM;IACZ,MAAMsO,eAAe,GAAG9F,eAAe,CAACqC,MAAM,CAACe,KAAK;MAAA,IAAA2C,qBAAA;MAAA,OAChD3C,KAAK,CAAC4C,YAAY,CAACtD,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAACpD,WAAW,CAAC4C,WAAW,CAAC,CAAC,CAAC,MAAAqD,qBAAA,GACpE3C,KAAK,CAAC6C,iBAAiB,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBzJ,QAAQ,CAAC,CAAC,CAAC4G,QAAQ,CAACpD,WAAW,CAAC;IAAA,EAAC;IAC1D;IACJ,CAAC;IACD;IACA,MAAMoG,uBAAuB,GAAGJ,eAAe,CAACzD,MAAM,CAACe,KAAK,IACxDA,KAAK,CAACC,aAAa,KAAK,GAAG,IAAID,KAAK,CAACC,aAAa,KAAK,GAAG,IAAID,KAAK,CAACC,aAAa,KAAK,GAC1F,CAAC;IAED,MAAM8C,2BAA2B,GAAGL,eAAe,CAACzD,MAAM,CAACe,KAAK,IAC5DA,KAAK,CAACC,aAAa,KAAK,GAC5B,CAAC;;IAED;IACAvE,iBAAiB,CAACoH,uBAAuB,CAAC;IAC1ClH,qBAAqB,CAACmH,2BAA2B,CAAC;EACtD,CAAC,EAAE,CAACrG,WAAW,EAAEE,eAAe,CAAC,CAAC;EAGlC,MAAMoG,iBAAiB,GAAGA,CAAA,KAAM;IAC5B/G,QAAQ,CAAC,MAAM,CAAC;EACpB,CAAC;EAGD,MAAMgH,mBAAmB,GAAGA,CAACC,KAAK,EAAEhE,MAAM,KAAK;IAC3CgE,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB1G,wBAAwB,CAACyC,MAAM,CAAC;IAChC3C,yBAAyB,CAAC,CAACD,sBAAsB,CAAC;EACtD,CAAC;EAED,MAAM8G,aAAa,GAAG,MAAAA,CAAOF,KAAK,EAAE1K,GAAG,EAAE6K,SAAS,EAAEpD,aAAa,KAAK;IAClEiD,KAAK,CAACC,eAAe,CAAC,CAAC;IACvBnG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;;IAEpB,IAAIsG,SAAS,GAAG,EAAE;IAClB,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAIvF,QAAQ,GAAG,IAAI;IAEnB,QAAQiC,aAAa;MACjB,KAAK,GAAG;QACJqD,SAAS,GAAG,GAAG;QACf;MACJ,KAAK,GAAG;QACJA,SAAS,GAAG,GAAG;QACf;MACJ,KAAK,GAAG;QACJA,SAAS,GAAG,GAAG;QACf;QACA;QACAC,YAAY,GAAG,IAAItB,IAAI,CAAC,CAAC;QACzB;MACJ;QACIjF,YAAY,CAAC,KAAK,CAAC;QACnB;MAAQ;IAChB;IAEA,IAAI;MACA;MACA,IAAIiD,aAAa,KAAK,GAAG,EAAE;QACvBjC,QAAQ,GAAG,MAAMpI,4BAA4B,CAAC0D,MAAM,EAAEd,GAAG,EAAE6K,SAAS,EAAEC,SAAS,EAAEC,YAAY,CAAC;MAClG,CAAC,MAAM;QACHvF,QAAQ,GAAG,MAAMrI,kBAAkB,CAAC2D,MAAM,EAAEd,GAAG,EAAE6K,SAAS,EAAEC,SAAS,CAAC;MAC1E;MACAxI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;MAClB/F,KAAK,CAACiJ,QAAQ,CAACE,IAAI,CAACsF,GAAG,EAAE;QAAEC,SAAS,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;IAClE,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACV9O,KAAK,CAAC,EAAA6O,aAAA,GAAAD,GAAG,CAAC3F,QAAQ,cAAA4F,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAc1F,IAAI,cAAA2F,kBAAA,uBAAlBA,kBAAA,CAAoBL,GAAG,KAAI,mBAAmB,EAAE;QAAEC,SAAS,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAQ,CAAC,CAAC;IAC7F,CAAC,SAAS;MACN5I,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;MAClBkC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IACzB;EACJ,CAAC;;EAGD;AACJ;AACA;AACA;AACA;AACA;;EAEI,MAAM8G,sBAAsB,GAAG,MAAMC,QAAQ,IAAI;IAC7CvK,0BAA0B,CAAC,CAACD,2BAA2B,CAAC;IACxDG,iBAAiB,CAACjB,SAAS,CAAC;IAC5B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;EACI,CAAC;EAED,MAAMuL,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC3H,kBAAkB,CAAC,IAAI,CAAC;IACxBrB,oBAAoB,CAAC,KAAK,CAAC;IAC3B,MAAM6D,gCAAgC,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMoF,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC5H,kBAAkB,CAAC,IAAI,CAAC;IACxBrB,oBAAoB,CAAC,UAAU,CAAC;IAChC,MAAM6D,gCAAgC,CAAC,UAAU,CAAC,CAAC,CAAC;IACpD;EACJ,CAAC;EAED,MAAMqF,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC7H,kBAAkB,CAAC,IAAI,CAAC;IACxBrB,oBAAoB,CAAC,QAAQ,CAAC;IAC9B,MAAM6D,gCAAgC,CAAC,QAAQ,CAAC,CAAC,CAAC;IAClD;EACJ,CAAC;EAED,MAAMsF,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC9H,kBAAkB,CAAC,IAAI,CAAC;IACxBrB,oBAAoB,CAAC,OAAO,CAAC;IAC7B,MAAM6D,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC;IACjD;EACJ,CAAC;EAED,MAAMuF,gBAAgB,GAAG,MAAAA,CAAOC,cAAc,EAAE3L,UAAU,KAAK;IAE3DuB,aAAa,CAAC,CAACD,UAAU,CAAC;IAC1B,MAAMgE,QAAQ,GAAG,MAAMvI,SAAS,CAAC6D,MAAM,EAAEZ,UAAU,EAAEC,eAAe,EAAE0L,cAAc,CAAC;IACrF,IAAIrK,UAAU,EAAE;MACZ,IAAIgE,QAAQ,CAACE,IAAI,CAACgB,MAAM,EAAE;QACtB/C,aAAa,CAAC6B,QAAQ,CAACE,IAAI,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAC;MAC1C;IACJ;EACJ,CAAC;EAED,oBAEIhI,OAAA,CAAAE,SAAA;IAAAkN,QAAA,eACIpN,OAAA,CAAClB,cAAc;MAACuO,WAAW,EAAE,CAAC,SAAS,CAAE;MAAAD,QAAA,eAKrCpN,OAAA,CAACG,KAAK;QAACE,OAAO,EAAEA,OAAQ;QAAA+M,QAAA,gBAKpBpN,OAAA,CAACL,qBAAqB;UAAC2C,0BAA0B,EAAEA,0BAA2B;UAACD,2BAA2B,EAAEA,2BAA4B;UACpIuB,UAAU,EAAEA,UAAW;UACvBhB,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7BN,cAAc,EAAEA,cAAe;UAC/BU,gBAAgB,EAAEA,gBAAiB;UACnCE,gBAAgB,EAAEA,gBAAiB;UACnCE,iBAAiB,EAAEA,iBAAkB;UACrCE,iBAAiB,EAAEA,iBAAkB;UACrCE,aAAa,EAAEA;UACnB;UACA;UACA;UACA;QAAA;UAAA6J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEFzN,OAAA,CAACJ,WAAW;UAACmD,aAAa,EAAEA,aAAc;UAACD,UAAU,EAAEA,UAAW;UAC9Dc,UAAU,EAAEA,UAAW;UACvBoB,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7BiH,aAAa,EAAEA,aAAc;UAC7B9J,MAAM,EAAEA,MAAO;UACf2D,iBAAiB,EAAEA;QAAkB;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eAEFzN,OAAA,CAACH,kBAAkB;UAACuF,sBAAsB,EAAEA,sBAAuB;UAC/DC,yBAAyB,EAAEA,yBAA0B;UACrDC,qBAAqB,EAAEA,qBAAsB;UAC7CjE,YAAY,EAAEA,YAAa;UAC3Be,MAAM,EAAEA,MAAO;UACfwB,UAAU,EAAEA;QAAW;UAAA0J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAEFzN,OAAA;UAAK0N,SAAS,EAAC,aAAa;UAAAN,QAAA,gBACxBpN,OAAA;YAAK2N,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,gBAE1BpN,OAAA;cAAK0N,SAAS,EAAC,aAAa;cAACC,KAAK,EAAE;gBAAEE,YAAY,EAAE;cAAM,CAAE;cAAAT,QAAA,eACxDpN,OAAA;gBAAK0N,SAAS,EAAC,OAAO;gBAAAN,QAAA,eAClBpN,OAAA;kBAAAoN,QAAA,EAAI;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAKNzN,OAAA;cAAK0N,SAAS,EAAC,sBAAsB;cAACC,KAAK,EAAE;gBAAEG,OAAO,EAAE,aAAa;gBAAEF,KAAK,EAAE,MAAM;gBAAEG,cAAc,EAAE,eAAe;gBAAEC,OAAO,EAAE;cAAY,CAAE;cAAAZ,QAAA,gBAC1IpN,OAAA;gBAAK2N,KAAK,EAAE;kBAAEG,OAAO,EAAE,aAAa;kBAAEG,aAAa,EAAE,QAAQ;kBAAEF,cAAc,EAAE,eAAe;kBAAEG,YAAY,EAAE;gBAAS,CAAE;gBAAAd,QAAA,gBACrHpN,OAAA;kBAAK0N,SAAS,EAAE,4BAA4BxH,QAAQ,GAAG,QAAQ,GAAG,SAAS,EAAG;kBAAAkH,QAAA,gBAC1EpN,OAAA;oBAAKmO,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,CAAE;oBAACY,SAAS,EAAE,qCAAqC7J,iBAAiB,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAE,IAAIqC,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;oBAAAkH,QAAA,eAC7JpN,OAAA;sBAAK0N,SAAS,EAAC,oBAAoB;sBAAAN,QAAA,EAAC;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACNzN,OAAA;oBAAKmO,OAAO,EAAEA,CAAA,KAAMpB,oBAAoB,CAAC,CAAE;oBAACW,SAAS,EAAE,wCAAwC7J,iBAAiB,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,IAAIqC,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;oBAAAkH,QAAA,eAC1KpN,OAAA;sBAAK0N,SAAS,EAAC,oBAAoB;sBAAAN,QAAA,gBAC/BpN,OAAA,CAACf,sBAAsB;wBAACyO,SAAS,EAAC;sBAAa;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAClDzN,OAAA;wBAAAoN,QAAA,EAAM;sBAAQ;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNzN,OAAA;oBAAKmO,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAAC,CAAE;oBAACU,SAAS,EAAE,sCAAsC7J,iBAAiB,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,IAAIqC,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;oBAAAkH,QAAA,eACpKpN,OAAA;sBAAK0N,SAAS,EAAC,oBAAoB;sBAAAN,QAAA,gBAC/BpN,OAAA,CAACd,YAAY;wBAACwO,SAAS,EAAC;sBAAa;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACxCzN,OAAA;wBAAAoN,QAAA,EAAM;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNzN,OAAA;oBAAKmO,OAAO,EAAEA,CAAA,KAAMlB,iBAAiB,CAAC,CAAE;oBAACS,SAAS,EAAE,qCAAqC7J,iBAAiB,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,IAAIqC,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;oBAAAkH,QAAA,eACjKpN,OAAA;sBAAK0N,SAAS,EAAC,oBAAoB;sBAAAN,QAAA,gBAC/BpN,OAAA,CAACP,UAAU;wBAACiO,SAAS,EAAC;sBAAa;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACtCzN,OAAA;wBAAAoN,QAAA,EAAM;sBAAK;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAENzN,OAAA;kBAAK0N,SAAS,EAAC,qBAAqB;kBAAAN,QAAA,eAChCpN,OAAA;oBAAK2N,KAAK,EAAE;sBAAEG,OAAO,EAAE,MAAM;sBAAEF,KAAK,EAAE,MAAM;sBAAEQ,UAAU,EAAE;oBAAS,CAAE;oBAAAhB,QAAA,gBACjEpN,OAAA;sBAAO2N,KAAK,EAAE;wBAAEC,KAAK,EAAE;sBAAO,CAAE;sBAAClL,KAAK,EAAE8C,WAAY;sBAAC6I,QAAQ,EAAGC,CAAC,IAAK7I,cAAc,CAAC6I,CAAC,CAACC,MAAM,CAAC7L,KAAK,CAAE;sBACjGgL,SAAS,EAAC,4BAA4B;sBAACc,WAAW,EAAC;oBAAqC;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/FzN,OAAA;sBAAG0N,SAAS,EAAC,MAAM;sBAAAN,QAAA,eAACpN,OAAA,CAACX,OAAO,CAACoP,QAAQ;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENzN,OAAA;gBAAK2N,KAAK,EAAE;kBAAEG,OAAO,EAAE;gBAAc,CAAE;gBAAAV,QAAA,eACnCpN,OAAA;kBAAK0N,SAAS,EAAC,aAAa;kBAAAN,QAAA,eACxBpN,OAAA;oBAAK0N,SAAS,EAAC,iBAAiB;oBAAAN,QAAA,eAC5BpN,OAAA;sBAAQmO,OAAO,EAAErC,iBAAkB;sBAAAsB,QAAA,gBAC/BpN,OAAA,CAACZ,OAAO,CAACsP,mBAAmB;wBAACf,KAAK,EAAE;0BAAEgB,WAAW,EAAE,KAAK;0BAAEC,QAAQ,EAAE,MAAM;0BAAEf,YAAY,EAAE;wBAAM;sBAAE;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAAAzN,OAAA;wBAAAoN,QAAA,EAAG;sBAAW;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGL,CAAC,EAGLrH,MAAM,CAACC,UAAU,GAAG,GAAG,gBACpBrG,OAAA;YAAK0N,SAAS,EAAC,aAAa;YAACC,KAAK,EAAE;cAAEG,OAAO,EAAE,aAAa;cAAEe,MAAM,EAAE,OAAO;cAAEC,SAAS,EAAE;YAAgC,CAAE;YAAA1B,QAAA,GAEvHlI,eAAe,gBACZlF,OAAA,CAAC7C,OAAO;cAACqP,IAAI,EAAC,MAAM;cAACuC,KAAK,EAAC,mBAAmB;cAACF,MAAM,EAAE,EAAG;cAACjB,KAAK,EAAE,EAAG;cAACF,SAAS,EAAC;YAAwB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAEzG,IAAI,eAIVzN,OAAA,CAACO,WAAW;cACRyO,UAAU,EAAC,UAAU;cACrBtB,SAAS,EAAC;cACd;cAAA;cAAAN,QAAA,eAEIpN,OAAA;gBAAK0N,SAAS,EAAC,kBAAkB;gBAAAN,QAAA,gBAE7BpN,OAAA;kBAAK0N,SAAS,EAAC,uBAAuB;kBAAAN,QAAA,gBAElCpN,OAAA;oBAAK0N,SAAS,EAAC,sBAAsB;oBAAAN,QAAA,eACjCpN,OAAA;sBAAAoN,QAAA,EAAI;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACNzN,OAAA;oBAAAoN,QAAA,EAAKrJ,cAAc,CAACkL;kBAAM;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE/B,CAAC,eAENzN,OAAA;kBAAK0N,SAAS,EAAC,cAAc;kBAAAN,QAAA,gBACzBpN,OAAA;oBAAK0N,SAAS,EAAC,0CAA0C;oBAAAN,QAAA,eACrDpN,OAAA;sBAAK0N,SAAS,EAAC,MAAM;sBAAAN,QAAA,gBACjBpN,OAAA;wBAAAoN,QAAA,gBACIpN,OAAA;0BAAG0N,SAAS,EAAC,aAAa;0BAAAN,QAAA,gBACtBpN,OAAA;4BAAAoN,QAAA,EAAG;0BAAQ;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eACfzN,OAAA;4BAAAoN,QAAA,GAAOnK,gBAAgB,EAAC,KAAG,EAACE,gBAAgB,EAAC,MAAI;0BAAA;4BAAAmK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzD,CAAC,eACJzN,OAAA;0BAAG0N,SAAS,EAAC,aAAa;0BAAAN,QAAA,gBACtBpN,OAAA;4BAAAoN,QAAA,EAAG;0BAAU;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eACjBzN,OAAA;4BAAAoN,QAAA,GAAO/J,iBAAiB,EAAC,KAAG,EAACE,iBAAiB,EAAC,MAAI;0BAAA;4BAAA+J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3D,CAAC,eACJzN,OAAA;0BAAG0N,SAAS,EAAC,aAAa;0BAAAN,QAAA,gBACtBpN,OAAA;4BAAAoN,QAAA,EAAG;0BAAmB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eAC1BzN,OAAA;4BAAAoN,QAAA,EAAO3J,aAAa,IAAI,QAAQ,GAAG,YAAY,GAAGA,aAAa,IAAI,YAAY,GAAG,SAAS,GAAG;0BAAc;4BAAA6J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNzN,OAAA;wBAAK0N,SAAS,EAAC,WAAW;wBAACS,OAAO,EAAEA,CAAA,KAAMvB,sBAAsB,CAACxK,MAAM,CAAE;wBAACuL,KAAK,EAAE;0BAAEuB,MAAM,EAAE;wBAAU,CAAE;wBAAA9B,QAAA,GAClG,GAAG,EAAC,QACC,EAAC,GAAG;sBAAA;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,EAEF1J,cAAc,CAACkL,MAAM,IAAI,CAAC,gBACtBjP,OAAA;oBAAyB0N,SAAS,EAAC,0CAA0C;oBAAAN,QAAA,eACzEpN,OAAA;sBAAK0N,SAAS,EAAC,MAAM;sBAAAN,QAAA,gBACjBpN,OAAA;wBAAG2N,KAAK,EAAE;0BAAEE,YAAY,EAAE,CAAC;0BAAEE,cAAc,EAAE,QAAQ;0BAAEoB,SAAS,EAAE,QAAQ;0BAAErB,OAAO,EAAE;wBAAO,CAAE;wBAAAV,QAAA,EAAC;sBAAyB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC5HzN,OAAA;wBAAG2N,KAAK,EAAE;0BAAEE,YAAY,EAAE,CAAC;0BAAEE,cAAc,EAAE,QAAQ;0BAAEoB,SAAS,EAAE,QAAQ;0BAAErB,OAAO,EAAE;wBAAO,CAAE;wBAAAV,QAAA,EAAC;sBAE/F;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GANA2B,IAAI,CAACC,MAAM,CAAC,CAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOlB,CAAC;kBAEN;kBACAlJ,cAAc,IAAIA,cAAc,CAAC+K,GAAG,CAAC,CAACxG,KAAK,EAAEyG,CAAC;oBAAA,IAAAC,cAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA;oBAAA,OAC1CvH,KAAK,CAACC,aAAa,IAAI,GAAG,gBACtB/I,OAAA;sBAAyB0N,SAAS,EAAC,eAAe;sBAACS,OAAO,EAAEA,CAAA,KAAMjB,gBAAgB,CAACpE,KAAK,CAACxH,GAAG,EAAEwH,KAAK,CAACtH,UAAU,CAAE;sBAAA4L,QAAA,gBAC5GpN,OAAA;wBAAK0N,SAAS,EAAC,aAAa;wBAAAN,QAAA,gBACxBpN,OAAA;0BAAK0N,SAAS,EAAC,YAAY;0BAAAN,QAAA,gBACvBpN,OAAA;4BAAK0N,SAAS,EAAC,aAAa;4BAAAN,QAAA,EACvB,EAAAoC,cAAA,GAAA1G,KAAK,CAACZ,OAAO,cAAAsH,cAAA,uBAAbA,cAAA,CAAerH,YAAY,KAAI,UAAU,gBAAGnI,OAAA,CAACR,OAAO;8BAAA8N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,gBAAGzN,OAAA,CAACV,YAAY;8BAAAgO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1E,CAAC,eACNzN,OAAA;4BAAM0N,SAAS,EAAC,MAAM;4BAAAN,QAAA,GAAC,UAAQ,GAAAqC,sBAAA,GAAC3G,KAAK,CAAC6C,iBAAiB,cAAA8D,sBAAA,cAAAA,sBAAA,GAAI3G,KAAK,CAACqD,SAAS;0BAAA;4BAAAmB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEjF,CAAC,eACNzN,OAAA;0BAAK0N,SAAS,EAAC,gBAAgB;0BAAAN,QAAA,eAC3BpN,OAAA;4BAAK0N,SAAS,EAAC,UAAU;4BAAAN,QAAA,gBACrBpN,OAAA,CAACT,UAAU;8BAAA+N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACdzN,OAAA;8BAAAoN,QAAA,EAAOjO,MAAM,CAACmR,SAAS,CAACxH,KAAK,CAACyH,SAAS,CAAC,CAACC,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,OAAO;4BAAC;8BAAAnD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eAENzN,OAAA;wBAAK0N,SAAS,EAAC,eAAe;wBAAAN,QAAA,gBAC1BpN,OAAA;0BAAK0N,SAAS,EAAC,OAAO;0BAAAN,QAAA,gBAClBpN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAAC4C;4BAAY;8BAAA4B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,EAChD3E,KAAK,CAAC4H,kBAAkB,GAAG,CAAC,gBACzB1Q,OAAA;8BAAK0N,SAAS,EAAC,qCAAqC;8BAAAN,QAAA,eAChDpN,OAAA;gCAAK0N,SAAS,EAAC,aAAa;gCAAAN,QAAA,eACxBpN,OAAA;kCAAM2N,KAAK,EAAE;oCAAEiB,QAAQ,EAAE;kCAAG,CAAE;kCAAAxB,QAAA,EAAEtE,KAAK,CAAC4H;gCAAkB;kCAAApD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/D;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,gBAENzN,OAAA;8BAAK0N,SAAS,EAAC,0BAA0B;8BAAAN,QAAA,eACrCpN,OAAA;gCAAK0N,SAAS,EAAC,aAAa;gCAAAN,QAAA,eACxBpN,OAAA;kCAAM2N,KAAK,EAAE;oCAAEiB,QAAQ,EAAE;kCAAG,CAAE;kCAAAxB,QAAA,EAAC;gCAAE;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACvC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAET,CAAC,eACNzN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,eAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EACjBtE,KAAK,CAAC6H;4BAAe;8BAAArD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNzN,OAAA;0BAAK0N,SAAS,EAAC,OAAO;0BAAAN,QAAA,gBAClBpN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,YAAY;8BAAAN,QAAA,EAAC;4BAAM;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACxCzN,OAAA;8BAAK0N,SAAS,EAAC,YAAY;8BAAAN,QAAA,GAAC,KACrB,EAACtE,KAAK,CAAC8H,WAAW,IAAI9H,KAAK,CAAC8H,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC7O,QAAQ,CAAC,CAAC,CAAC8O,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;4BAAA;8BAAAxD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,eACNzN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAC;4BAAU;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACvCzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAACiI;4BAAc;8BAAAzD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eAENzN,OAAA;wBAAK0N,SAAS,EAAC,eAAe;wBAAAN,QAAA,EACzB,EAAAsC,eAAA,GAAA5G,KAAK,CAACZ,OAAO,cAAAwH,eAAA,uBAAbA,eAAA,CAAevH,YAAY,KAAI,UAAU,gBACtCnI,OAAA;0BAAK0N,SAAS,EAAC,cAAc;0BAAAN,QAAA,eACzBpN,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,gBACzBpN,OAAA;8BAAK0N,SAAS,EAAC,KAAK;8BAACC,KAAK,EAAE;gCAAEG,OAAO,EAAE,MAAM;gCAAEkD,SAAS,EAAE;8BAAE,CAAE;8BAAA5D,QAAA,eAC1DpN,OAAA,CAACN,OAAO;gCAACgO,SAAS,EAAC;8BAAK;gCAAAJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,eACNzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAC;4BAAiB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,gBAENzN,OAAA;0BAAK0N,SAAS,EAAC,cAAc;0BAAAN,QAAA,gBACzBpN,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,gBACzBpN,OAAA;8BAAK0N,SAAS,EAAC,KAAK;8BAAAN,QAAA,eAChBpN,OAAA,CAACN,OAAO;gCAACgO,SAAS,EAAC;8BAAK;gCAAAJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,eACNzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EACjB,EAAAuC,eAAA,GAAA7G,KAAK,CAACZ,OAAO,cAAAyH,eAAA,uBAAbA,eAAA,CAAesB,QAAQ,KAAI,IAAArB,eAAA,GAAG9G,KAAK,CAACZ,OAAO,cAAA0H,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAeqB,QAAQ,cAAApB,qBAAA,uBAAvBA,qBAAA,CAAyBqB,GAAG,MAAApB,eAAA,GAAKhH,KAAK,CAACZ,OAAO,cAAA4H,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAemB,QAAQ,cAAAlB,qBAAA,uBAAvBA,qBAAA,CAAyBoB,MAAM;AACnL,0EAA0E,EAAAnB,eAAA,GAAAlH,KAAK,CAACZ,OAAO,cAAA8H,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAeiB,QAAQ,cAAAhB,qBAAA,uBAAvBA,qBAAA,CAAyBmB,WAAW,KAAI,KAAK,KAAAlB,eAAA,GAAGpH,KAAK,CAACZ,OAAO,cAAAgI,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAee,QAAQ,cAAAd,qBAAA,uBAAvBA,qBAAA,CAAyBiB,WAAW,OAAAhB,eAAA,GAAKtH,KAAK,CAACZ,OAAO,cAAAkI,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAea,QAAQ,cAAAZ,qBAAA,uBAAvBA,qBAAA,CAAyBgB,MAAM;4BAAE;8BAAA/D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/G,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,eACNzN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAACS,OAAO,EAAGnC,KAAK,IAAKD,mBAAmB,CAACC,KAAK,EAAElD,KAAK,CAAE;4BAAAsE,QAAA,EACxEtE,KAAK,CAACwI,UAAU,gBACbtR,OAAA;8BAAK0N,SAAS,EAAC,iBAAiB;8BAAAN,QAAA,EAAEtE,KAAK,CAACwI,UAAU,CAACC;4BAAI;8BAAAjE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,gBAE9DzN,OAAA;8BAAK0N,SAAS,EAAC,iBAAiB;8BAAAN,QAAA,EAAC;4BAAmB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAE7D,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAET,CAAC,eAENzN,OAAA;wBAAK2N,KAAK,EAAE;0BAAEqD,SAAS,EAAE;wBAAG,CAAE;wBAAA5D,QAAA,eAC1BpN,OAAA;0BAAK0N,SAAS,EAAC,uBAAuB;0BAAAN,QAAA,eAClCpN,OAAA;4BAAQmO,OAAO,EAAGnC,KAAK,IAAKE,aAAa,CAACF,KAAK,EAAElD,KAAK,CAACxH,GAAG,EAAEwH,KAAK,CAACqD,SAAS,EAAErD,KAAK,CAACC,aAAa,CAAE;4BAAAqE,QAAA,eAC9FpN,OAAA;8BAAAoN,QAAA,gBACIpN,OAAA;gCAAAoN,QAAA,EAAG;8BAAc;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,eACrBzN,OAAA,CAACZ,OAAO,CAACoS,mBAAmB;gCAAC7D,KAAK,EAAE;kCAAE8D,UAAU,EAAE,KAAK;kCAAE7C,QAAQ,EAAE,MAAM;kCAAEf,YAAY,EAAE,KAAK;kCAAE6D,SAAS,EAAE;gCAAQ;8BAAE;gCAAApE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAaR;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA,GA5GA2B,IAAI,CAACC,MAAM,CAAC,CAAC;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA8GlB,CAAC,GAEN,IAAI;kBAAA,CAEX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC,eAGdzN,OAAA,CAACO,WAAW;cACRyO,UAAU,EAAC,YAAY;cACvBtB,SAAS,EAAC;cACd;cAAA;cAAAN,QAAA,eAEIpN,OAAA;gBAAK0N,SAAS,EAAC,mBAAmB;gBAAAN,QAAA,gBAC9BpN,OAAA;kBAAK0N,SAAS,EAAC,wBAAwB;kBAAAN,QAAA,gBACnCpN,OAAA;oBAAK0N,SAAS,EAAC,sBAAsB;oBAAAN,QAAA,eACjCpN,OAAA;sBAAAoN,QAAA,EAAI;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACNzN,OAAA;oBAAAoN,QAAA,EAAKnJ,eAAe,CAACgL;kBAAM;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eAENzN,OAAA;kBAAK0N,SAAS,EAAC,eAAe;kBAAAN,QAAA,EAEzBnJ,eAAe,CAACgL,MAAM,IAAI,CAAC,gBACxBjP,OAAA;oBAAyB0N,SAAS,EAAC,0CAA0C;oBAAAN,QAAA,eACzEpN,OAAA;sBAAK0N,SAAS,EAAC,MAAM;sBAAAN,QAAA,gBACjBpN,OAAA;wBAAG2N,KAAK,EAAE;0BAAEE,YAAY,EAAE,CAAC;0BAAEE,cAAc,EAAE,QAAQ;0BAAEoB,SAAS,EAAE,QAAQ;0BAAErB,OAAO,EAAE;wBAAO,CAAE;wBAAAV,QAAA,EAAC;sBAAyB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC5HzN,OAAA;wBAAG2N,KAAK,EAAE;0BAAEE,YAAY,EAAE,CAAC;0BAAEE,cAAc,EAAE,QAAQ;0BAAEoB,SAAS,EAAE,QAAQ;0BAAErB,OAAO,EAAE;wBAAO,CAAE;wBAAAV,QAAA,EAAC;sBAE/F;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GANA2B,IAAI,CAACC,MAAM,CAAC,CAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOlB,CAAC,GAENlJ,cAAc,IAAIA,cAAc,CAAC+K,GAAG,CAAC,CAACxG,KAAK,EAAEyG,CAAC;oBAAA,IAAAoC,eAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;oBAAA,OAC1CzJ,KAAK,CAACC,aAAa,IAAI,GAAG,gBACtB/I,OAAA;sBAAyB0N,SAAS,EAAC,eAAe;sBAACS,OAAO,EAAEA,CAAA,KAAMjB,gBAAgB,CAACpE,KAAK,CAACxH,GAAG,EAAEwH,KAAK,CAACtH,UAAU,CAAE;sBAAA4L,QAAA,gBAC5GpN,OAAA;wBAAK0N,SAAS,EAAC,aAAa;wBAAAN,QAAA,gBACxBpN,OAAA;0BAAK0N,SAAS,EAAC,YAAY;0BAAAN,QAAA,gBACvBpN,OAAA;4BAAK0N,SAAS,EAAC,aAAa;4BAAAN,QAAA,EACvB,EAAAuE,eAAA,GAAA7I,KAAK,CAACZ,OAAO,cAAAyJ,eAAA,uBAAbA,eAAA,CAAexJ,YAAY,KAAI,UAAU,gBAAGnI,OAAA,CAACR,OAAO;8BAAA8N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,gBAAGzN,OAAA,CAACV,YAAY;8BAAAgO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1E,CAAC,eACNzN,OAAA;4BAAM0N,SAAS,EAAC,MAAM;4BAAAN,QAAA,GAAC,UAAQ,GAAAwE,sBAAA,GAAC9I,KAAK,CAAC6C,iBAAiB,cAAAiG,sBAAA,cAAAA,sBAAA,GAAI9I,KAAK,CAACqD,SAAS;0BAAA;4BAAAmB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEjF,CAAC,eACNzN,OAAA;0BAAK0N,SAAS,EAAC,gBAAgB;0BAAAN,QAAA,eAC3BpN,OAAA;4BAAK0N,SAAS,EAAC,UAAU;4BAAAN,QAAA,gBACrBpN,OAAA,CAACT,UAAU;8BAAA+N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACdzN,OAAA;8BAAAoN,QAAA,EAAOjO,MAAM,CAACmR,SAAS,CAACxH,KAAK,CAACyH,SAAS,CAAC,CAACC,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,OAAO;4BAAC;8BAAAnD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eAENzN,OAAA;wBAAK0N,SAAS,EAAC,eAAe;wBAAAN,QAAA,gBAC1BpN,OAAA;0BAAK0N,SAAS,EAAC,OAAO;0BAAAN,QAAA,gBAClBpN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAAC4C;4BAAY;8BAAA4B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,EAChD3E,KAAK,CAAC4H,kBAAkB,GAAG,CAAC,gBACzB1Q,OAAA;8BAAK0N,SAAS,EAAC,qCAAqC;8BAAAN,QAAA,eAChDpN,OAAA;gCAAK0N,SAAS,EAAC,aAAa;gCAAAN,QAAA,eACxBpN,OAAA;kCAAM2N,KAAK,EAAE;oCAAEiB,QAAQ,EAAE;kCAAG,CAAE;kCAAAxB,QAAA,EAAEtE,KAAK,CAAC4H;gCAAkB;kCAAApD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/D;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,gBAENzN,OAAA;8BAAK0N,SAAS,EAAC,0BAA0B;8BAAAN,QAAA,eACrCpN,OAAA;gCAAK0N,SAAS,EAAC,aAAa;gCAAAN,QAAA,eACxBpN,OAAA;kCAAM2N,KAAK,EAAE;oCAAEiB,QAAQ,EAAE;kCAAG,CAAE;kCAAAxB,QAAA,EAAC;gCAAE;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACvC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAET,CAAC,eACNzN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,eAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EACjBtE,KAAK,CAAC6H;4BAAe;8BAAArD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNzN,OAAA;0BAAK0N,SAAS,EAAC,OAAO;0BAAAN,QAAA,gBAClBpN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,YAAY;8BAAAN,QAAA,EAAC;4BAAM;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACxCzN,OAAA;8BAAK0N,SAAS,EAAC,YAAY;8BAAAN,QAAA,GAAC,KACrB,EAACtE,KAAK,CAAC8H,WAAW,IAAI9H,KAAK,CAAC8H,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC7O,QAAQ,CAAC,CAAC,CAAC8O,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;4BAAA;8BAAAxD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,eACNzN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAC;4BAAU;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACvCzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAACiI;4BAAc;8BAAAzD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eAENzN,OAAA;wBAAK0N,SAAS,EAAC,eAAe;wBAAAN,QAAA,EACzBtE,KAAK,CAACH,SAAS,CAACP,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAAC,MAAM,CAAC,gBAEvC5I,OAAA;0BAAK0N,SAAS,EAAC,cAAc;0BAAAN,QAAA,eACzBpN,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,gBACzBpN,OAAA;8BAAK0N,SAAS,EAAC,KAAK;8BAACC,KAAK,EAAE;gCAAEG,OAAO,EAAE,MAAM;gCAAEkD,SAAS,EAAE;8BAAE,CAAE;8BAAA5D,QAAA,eAC1DpN,OAAA,CAACN,OAAO;gCAACgO,SAAS,EAAC;8BAAK;gCAAAJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,eACNzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAACH;4BAAS;8BAAA2E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,gBAENzN,OAAA,CAAAE,SAAA;0BAAAkN,QAAA,EACK,EAAAyE,eAAA,GAAA/I,KAAK,CAACZ,OAAO,cAAA2J,eAAA,uBAAbA,eAAA,CAAe1J,YAAY,KAAI,UAAU,gBACtCnI,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,eACzBpN,OAAA;8BAAK0N,SAAS,EAAC,cAAc;8BAAAN,QAAA,gBACzBpN,OAAA;gCAAK0N,SAAS,EAAC,KAAK;gCAACC,KAAK,EAAE;kCAAEG,OAAO,EAAE,MAAM;kCAAEkD,SAAS,EAAE;gCAAE,CAAE;gCAAA5D,QAAA,eAC1DpN,OAAA,CAACN,OAAO;kCAACgO,SAAS,EAAC;gCAAK;kCAAAJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1B,CAAC,eACNzN,OAAA;gCAAK0N,SAAS,EAAC,OAAO;gCAAAN,QAAA,EAAC;8BAAiB;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC7C;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,gBAENzN,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,gBACzBpN,OAAA;8BAAK0N,SAAS,EAAC,cAAc;8BAAAN,QAAA,gBACzBpN,OAAA;gCAAK0N,SAAS,EAAC,KAAK;gCAAAN,QAAA,eAChBpN,OAAA,CAACN,OAAO;kCAACgO,SAAS,EAAC;gCAAK;kCAAAJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1B,CAAC,eACNzN,OAAA;gCAAK0N,SAAS,EAAC,OAAO;gCAAAN,QAAA,EACjBtE,KAAK,CAACZ,OAAO,IAAI,IAAA4J,eAAA,GAAGhJ,KAAK,CAACZ,OAAO,cAAA4J,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAeb,QAAQ,cAAAc,qBAAA,uBAAvBA,qBAAA,CAAyBb,GAAG,MAAAc,gBAAA,GAAKlJ,KAAK,CAACZ,OAAO,cAAA8J,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAef,QAAQ,cAAAgB,qBAAA,uBAAvBA,qBAAA,CAAyBd,MAAM;AACjL,kFAAkF,EAAAe,gBAAA,GAAApJ,KAAK,CAACZ,OAAO,cAAAgK,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAejB,QAAQ,cAAAkB,qBAAA,uBAAvBA,qBAAA,CAAyBf,WAAW,KAAI,KAAK,KAAAgB,gBAAA,GAAGtJ,KAAK,CAACZ,OAAO,cAAAkK,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAenB,QAAQ,cAAAoB,qBAAA,uBAAvBA,qBAAA,CAAyBjB,WAAW,OAAAkB,gBAAA,GAAKxJ,KAAK,CAACZ,OAAO,cAAAoK,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAerB,QAAQ,cAAAsB,qBAAA,uBAAvBA,qBAAA,CAAyBlB,MAAM;8BAAE;gCAAA/D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/G,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACNzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAACS,OAAO,EAAGnC,KAAK,IAAKD,mBAAmB,CAACC,KAAK,EAAElD,KAAK,CAAE;8BAAAsE,QAAA,EACxEtE,KAAK,CAACwI,UAAU,gBACbtR,OAAA;gCAAK0N,SAAS,EAAC,iBAAiB;gCAAAN,QAAA,EAAEtE,KAAK,CAACwI,UAAU,CAACC;8BAAI;gCAAAjE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,gBAE9DzN,OAAA;gCAAK0N,SAAS,EAAC,iBAAiB;gCAAAN,QAAA,EAAC;8BAAmB;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAE7D,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL;wBAAC,gBAEZ;sBACL;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eAENzN,OAAA;wBAAK2N,KAAK,EAAE;0BAAEqD,SAAS,EAAE;wBAAG,CAAE;wBAAA5D,QAAA,eAC1BpN,OAAA;0BAAK0N,SAAS,EAAC,uBAAuB;0BAAAN,QAAA,eAElCpN,OAAA;4BAAQmO,OAAO,EAAGnC,KAAK,IAAKE,aAAa,CAACF,KAAK,EAAElD,KAAK,CAACxH,GAAG,EAAEwH,KAAK,CAACqD,SAAS,EAAErD,KAAK,CAACC,aAAa,CAAE;4BAAAqE,QAAA,eAC9FpN,OAAA;8BAAAoN,QAAA,gBACIpN,OAAA;gCAAAoN,QAAA,EAAG;8BAAc;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,eACrBzN,OAAA,CAACZ,OAAO,CAACoS,mBAAmB;gCAAC7D,KAAK,EAAE;kCAAE8D,UAAU,EAAE,KAAK;kCAAE7C,QAAQ,EAAE,MAAM;kCAAEf,YAAY,EAAE,KAAK;kCAAE6D,SAAS,EAAE;gCAAQ;8BAAE;gCAAApE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAER;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA,GAhHA2B,IAAI,CAACC,MAAM,CAAC,CAAC;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkHlB,CAAC,GAEN,IAAI;kBAAA,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC,eAGdzN,OAAA,CAACO,WAAW;cACR,6BAA0B,EAAE;cAC5ByO,UAAU,EAAC,OAAO;cAClBtB,SAAS,EAAC,QAAQ;cAClB,0BAAuB,EAAE;cACzBC,KAAK,EAAE;gBAAEG,OAAO,EAAE;cAAY,CAAE;cAAAV,QAAA,eAEhCpN,OAAA;gBAAK0N,SAAS,EAAC,iBAAiB;gBAAAN,QAAA,gBAC5BpN,OAAA;kBAAK0N,SAAS,EAAC,sBAAsB;kBAAAN,QAAA,gBACjCpN,OAAA;oBAAK0N,SAAS,EAAC,sBAAsB;oBAACC,KAAK,EAAE;sBAAEG,OAAO,EAAE;oBAAc,CAAE;oBAAAV,QAAA,eACpEpN,OAAA;sBAAAoN,QAAA,EAAI;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNzN,OAAA;oBAAAoN,QAAA,EAAKjJ,cAAc,CAAC8K;kBAAM;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eAENzN,OAAA;kBAAK0N,SAAS,EAAC,aAAa;kBAAAN,QAAA,EACvBjJ,cAAc,CAAC8K,MAAM,IAAI,CAAC,gBACvBjP,OAAA;oBAAyB0N,SAAS,EAAC,0CAA0C;oBAAAN,QAAA,eACzEpN,OAAA;sBAAK0N,SAAS,EAAC,MAAM;sBAAAN,QAAA,gBACjBpN,OAAA;wBAAG2N,KAAK,EAAE;0BAAEE,YAAY,EAAE,CAAC;0BAAEE,cAAc,EAAE,QAAQ;0BAAEoB,SAAS,EAAE,QAAQ;0BAAErB,OAAO,EAAE;wBAAO,CAAE;wBAAAV,QAAA,EAAC;sBAAyB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC5HzN,OAAA;wBAAG2N,KAAK,EAAE;0BAAEE,YAAY,EAAE,CAAC;0BAAEE,cAAc,EAAE,QAAQ;0BAAEoB,SAAS,EAAE,QAAQ;0BAAErB,OAAO,EAAE;wBAAO,CAAE;wBAAAV,QAAA,EAAC;sBAE/F;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GANA2B,IAAI,CAACC,MAAM,CAAC,CAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOlB,CAAC,GAENlJ,cAAc,IAAIA,cAAc,CAAC+K,GAAG,CAAC,CAACxG,KAAK,EAAEyG,CAAC;oBAAA,IAAAiD,gBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;oBAAA,OAC1CtK,KAAK,CAACC,aAAa,IAAI,GAAG,gBACtB/I,OAAA;sBAAyB0N,SAAS,EAAC,eAAe;sBAACS,OAAO,EAAEA,CAAA,KAAMjB,gBAAgB,CAACpE,KAAK,CAACxH,GAAG,EAAEwH,KAAK,CAACtH,UAAU,CAAE;sBAAA4L,QAAA,gBAC5GpN,OAAA;wBAAK0N,SAAS,EAAC,aAAa;wBAAAN,QAAA,gBACxBpN,OAAA;0BAAK0N,SAAS,EAAC,YAAY;0BAAAN,QAAA,gBACvBpN,OAAA;4BAAK0N,SAAS,EAAC,aAAa;4BAAAN,QAAA,EACvB,EAAAoF,gBAAA,GAAA1J,KAAK,CAACZ,OAAO,cAAAsK,gBAAA,uBAAbA,gBAAA,CAAerK,YAAY,KAAI,UAAU,gBAAGnI,OAAA,CAACR,OAAO;8BAAA8N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,gBAAGzN,OAAA,CAACV,YAAY;8BAAAgO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1E,CAAC,eACNzN,OAAA;4BAAM0N,SAAS,EAAC,MAAM;4BAAAN,QAAA,GAAC,UAAQ,GAAAqF,sBAAA,GAAC3J,KAAK,CAAC6C,iBAAiB,cAAA8G,sBAAA,cAAAA,sBAAA,GAAI3J,KAAK,CAACqD,SAAS;0BAAA;4BAAAmB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEjF,CAAC,eACNzN,OAAA;0BAAK0N,SAAS,EAAC,gBAAgB;0BAAAN,QAAA,eAC3BpN,OAAA;4BAAK0N,SAAS,EAAC,UAAU;4BAAAN,QAAA,gBACrBpN,OAAA,CAACT,UAAU;8BAAA+N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACdzN,OAAA;8BAAAoN,QAAA,EAAOjO,MAAM,CAACmR,SAAS,CAACxH,KAAK,CAACyH,SAAS,CAAC,CAACC,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,OAAO;4BAAC;8BAAAnD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eAENzN,OAAA;wBAAK0N,SAAS,EAAC,eAAe;wBAAAN,QAAA,gBAC1BpN,OAAA;0BAAK0N,SAAS,EAAC,OAAO;0BAAAN,QAAA,gBAClBpN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAAC4C;4BAAY;8BAAA4B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,EAChD3E,KAAK,CAAC4H,kBAAkB,GAAG,CAAC,gBACzB1Q,OAAA;8BAAK0N,SAAS,EAAC,qCAAqC;8BAAAN,QAAA,eAChDpN,OAAA;gCAAK0N,SAAS,EAAC,aAAa;gCAAAN,QAAA,eACxBpN,OAAA;kCAAM2N,KAAK,EAAE;oCAAEiB,QAAQ,EAAE;kCAAG,CAAE;kCAAAxB,QAAA,EAAEtE,KAAK,CAAC4H;gCAAkB;kCAAApD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/D;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,gBAENzN,OAAA;8BAAK0N,SAAS,EAAC,0BAA0B;8BAAAN,QAAA,eACrCpN,OAAA;gCAAK0N,SAAS,EAAC,aAAa;gCAAAN,QAAA,eACxBpN,OAAA;kCAAM2N,KAAK,EAAE;oCAAEiB,QAAQ,EAAE;kCAAG,CAAE;kCAAAxB,QAAA,EAAC;gCAAE;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACvC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAET,CAAC,eACNzN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,eAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EACjBtE,KAAK,CAAC6H;4BAAe;8BAAArD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNzN,OAAA;0BAAK0N,SAAS,EAAC,OAAO;0BAAAN,QAAA,gBAClBpN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,YAAY;8BAAAN,QAAA,EAAC;4BAAM;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACxCzN,OAAA;8BAAK0N,SAAS,EAAC,YAAY;8BAAAN,QAAA,GAAC,KACrB,EAACtE,KAAK,CAAC8H,WAAW,IAAI9H,KAAK,CAAC8H,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC7O,QAAQ,CAAC,CAAC,CAAC8O,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;4BAAA;8BAAAxD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,eACNzN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAC;4BAAU;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACvCzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAACiI;4BAAc;8BAAAzD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eAENzN,OAAA;wBAAK0N,SAAS,EAAC,eAAe;wBAAAN,QAAA,EACzBtE,KAAK,CAACH,SAAS,CAACP,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAAC,MAAM,CAAC,gBAEvC5I,OAAA;0BAAK0N,SAAS,EAAC,cAAc;0BAAAN,QAAA,eACzBpN,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,gBACzBpN,OAAA;8BAAK0N,SAAS,EAAC,KAAK;8BAACC,KAAK,EAAE;gCAAEG,OAAO,EAAE,MAAM;gCAAEkD,SAAS,EAAE;8BAAE,CAAE;8BAAA5D,QAAA,eAC1DpN,OAAA,CAACN,OAAO;gCAACgO,SAAS,EAAC;8BAAK;gCAAAJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,eACNzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAACH;4BAAS;8BAAA2E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,gBAENzN,OAAA,CAAAE,SAAA;0BAAAkN,QAAA,EACK,EAAAsF,gBAAA,GAAA5J,KAAK,CAACZ,OAAO,cAAAwK,gBAAA,uBAAbA,gBAAA,CAAevK,YAAY,KAAI,UAAU,gBACtCnI,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,eACzBpN,OAAA;8BAAK0N,SAAS,EAAC,cAAc;8BAAAN,QAAA,gBACzBpN,OAAA;gCAAK0N,SAAS,EAAC,KAAK;gCAACC,KAAK,EAAE;kCAAEG,OAAO,EAAE,MAAM;kCAAEkD,SAAS,EAAE;gCAAE,CAAE;gCAAA5D,QAAA,eAC1DpN,OAAA,CAACN,OAAO;kCAACgO,SAAS,EAAC;gCAAK;kCAAAJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1B,CAAC,eACNzN,OAAA;gCAAK0N,SAAS,EAAC,OAAO;gCAAAN,QAAA,EAAC;8BAAiB;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC7C;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,gBAENzN,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,gBACzBpN,OAAA;8BAAK0N,SAAS,EAAC,cAAc;8BAAAN,QAAA,gBACzBpN,OAAA;gCAAK0N,SAAS,EAAC,KAAK;gCAAAN,QAAA,eAChBpN,OAAA,CAACN,OAAO;kCAACgO,SAAS,EAAC;gCAAK;kCAAAJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1B,CAAC,eACNzN,OAAA;gCAAK0N,SAAS,EAAC,OAAO;gCAAAN,QAAA,EACjBtE,KAAK,CAACZ,OAAO,IAAI,IAAAyK,gBAAA,GAAG7J,KAAK,CAACZ,OAAO,cAAAyK,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAe1B,QAAQ,cAAA2B,qBAAA,uBAAvBA,qBAAA,CAAyB1B,GAAG,MAAA2B,gBAAA,GAAK/J,KAAK,CAACZ,OAAO,cAAA2K,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAe5B,QAAQ,cAAA6B,qBAAA,uBAAvBA,qBAAA,CAAyB3B,MAAM;AACjL,kFAAkF,EAAA4B,gBAAA,GAAAjK,KAAK,CAACZ,OAAO,cAAA6K,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAe9B,QAAQ,cAAA+B,qBAAA,uBAAvBA,qBAAA,CAAyB5B,WAAW,KAAI,KAAK,KAAA6B,gBAAA,GAAGnK,KAAK,CAACZ,OAAO,cAAA+K,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAehC,QAAQ,cAAAiC,qBAAA,uBAAvBA,qBAAA,CAAyB9B,WAAW,OAAA+B,gBAAA,GAAKrK,KAAK,CAACZ,OAAO,cAAAiL,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAelC,QAAQ,cAAAmC,qBAAA,uBAAvBA,qBAAA,CAAyB/B,MAAM;8BAAE;gCAAA/D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/G,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACNzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAACS,OAAO,EAAGnC,KAAK,IAAKD,mBAAmB,CAACC,KAAK,EAAElD,KAAK,CAAE;8BAAAsE,QAAA,EACxEtE,KAAK,CAACwI,UAAU,gBACbtR,OAAA;gCAAK0N,SAAS,EAAC,iBAAiB;gCAAAN,QAAA,EAAEtE,KAAK,CAACwI,UAAU,CAACC;8BAAI;gCAAAjE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,gBAE9DzN,OAAA;gCAAK0N,SAAS,EAAC,iBAAiB;gCAAAN,QAAA,EAAC;8BAAmB;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAE7D,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL;wBAAC,gBAEZ;sBACL;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eAENzN,OAAA;wBAAK2N,KAAK,EAAE;0BAAEqD,SAAS,EAAE;wBAAG,CAAE;wBAAA5D,QAAA,eAC1BpN,OAAA;0BAAK0N,SAAS,EAAC,uBAAuB;0BAAAN,QAAA,eAElCpN,OAAA;4BAAQmO,OAAO,EAAGnC,KAAK,IAAKE,aAAa,CAACF,KAAK,EAAElD,KAAK,CAACxH,GAAG,EAAEwH,KAAK,CAACqD,SAAS,EAAErD,KAAK,CAACC,aAAa,CAAE;4BAAAqE,QAAA,eAC9FpN,OAAA;8BAAAoN,QAAA,gBACIpN,OAAA;gCAAAoN,QAAA,EAAG;8BAAc;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,eACrBzN,OAAA,CAACZ,OAAO,CAACoS,mBAAmB;gCAAC7D,KAAK,EAAE;kCAAE8D,UAAU,EAAE,KAAK;kCAAE7C,QAAQ,EAAE,MAAM;kCAAEf,YAAY,EAAE,KAAK;kCAAE6D,SAAS,EAAE;gCAAQ;8BAAE;gCAAApE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAER;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA,GAhHA2B,IAAI,CAACC,MAAM,CAAC,CAAC;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkHlB,CAAC,GAEN,IAAI;kBAAA,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC,eAIdzN,OAAA,CAACO,WAAW;cACR,6BAA0B,EAAE;cAC5ByO,UAAU,EAAC,OAAO;cAClBtB,SAAS,EAAC,QAAQ;cAClB,0BAAuB,EAAE;cACzBC,KAAK,EAAE;gBAAEG,OAAO,EAAE;cAAY,CAAE;cAAAV,QAAA,eAEhCpN,OAAA;gBAAK0N,SAAS,EAAC,qBAAqB;gBAAAN,QAAA,gBAChCpN,OAAA;kBAAK0N,SAAS,EAAC,0BAA0B;kBAAAN,QAAA,gBACrCpN,OAAA;oBAAK0N,SAAS,EAAC,sBAAsB;oBAACC,KAAK,EAAE;sBAAEG,OAAO,EAAE;oBAAc,CAAE;oBAAAV,QAAA,gBACpEpN,OAAA;sBAAAoN,QAAA,EAAI;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpBzN,OAAA;sBAAK0N,SAAS,EAAC,qBAAqB;sBAACC,KAAK,EAAE;wBAAEG,OAAO,EAAE,MAAM;wBAAEM,UAAU,EAAE,QAAQ;wBAAEqD,UAAU,EAAE;sBAAE,CAAE;sBAAArE,QAAA,eACjGpN,OAAA;wBACIqT,QAAQ,EAAC,cAAc;wBACvBC,QAAQ,EAAE,EAAG;wBACbC,aAAa,EAAC,uDAAoD;wBAClEC,SAAS,EAAC,SAAS;wBACnB7F,KAAK,EAAE;0BAAEkB,MAAM,EAAE,EAAE;0BAAEf,OAAO,EAAE,MAAM;0BAAEM,UAAU,EAAE;wBAAS,CAAE;wBAAAhB,QAAA,eAE7DpN,OAAA;0BACIyT,KAAK,EAAC,4BAA4B;0BAClC7F,KAAK,EAAE,EAAG;0BACViB,MAAM,EAAE,EAAG;0BACX6E,OAAO,EAAC,WAAW;0BACnBC,IAAI,EAAC,MAAM;0BACXC,MAAM,EAAC,SAAS;0BAChBC,WAAW,EAAE,CAAE;0BACfC,aAAa,EAAC,OAAO;0BACrBC,cAAc,EAAC,OAAO;0BACtBrG,SAAS,EAAC,8BAA8B;0BAAAN,QAAA,gBAExCpN,OAAA;4BAAMgU,CAAC,EAAC;0BAAoC;4BAAA1G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC/CzN,OAAA;4BAAUiU,MAAM,EAAC;0BAAuB;4BAAA3G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNzN,OAAA;oBAAAoN,QAAA,EAAK/I,iBAAiB,CAAC4K;kBAAM;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eAENzN,OAAA;kBAAK0N,SAAS,EAAC,iBAAiB;kBAAAN,QAAA,EAC3B/I,iBAAiB,CAAC4K,MAAM,IAAI,CAAC,gBAC1BjP,OAAA;oBAAyB0N,SAAS,EAAC,0CAA0C;oBAAAN,QAAA,eACzEpN,OAAA;sBAAK0N,SAAS,EAAC,MAAM;sBAAAN,QAAA,gBACjBpN,OAAA;wBAAG2N,KAAK,EAAE;0BAAEE,YAAY,EAAE,CAAC;0BAAEE,cAAc,EAAE,QAAQ;0BAAEoB,SAAS,EAAE,QAAQ;0BAAErB,OAAO,EAAE;wBAAO,CAAE;wBAAAV,QAAA,EAAC;sBAAyB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC5HzN,OAAA;wBAAG2N,KAAK,EAAE;0BAAEE,YAAY,EAAE,CAAC;0BAAEE,cAAc,EAAE,QAAQ;0BAAEoB,SAAS,EAAE,QAAQ;0BAAErB,OAAO,EAAE;wBAAO,CAAE;wBAAAV,QAAA,EAAC;sBAE/F;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GANA2B,IAAI,CAACC,MAAM,CAAC,CAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOlB,CAAC,GAENhJ,kBAAkB,IAAIA,kBAAkB,CAAC6K,GAAG,CAAC,CAACxG,KAAK,EAAEyG,CAAC;oBAAA,IAAA2E,gBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;oBAAA,OAClDhM,KAAK,CAACC,aAAa,IAAI,GAAG,gBACtB/I,OAAA;sBAAyB0N,SAAS,EAAC,eAAe;sBAACS,OAAO,EAAEA,CAAA,KAAMjB,gBAAgB,CAACpE,KAAK,CAACxH,GAAG,EAAEwH,KAAK,CAACtH,UAAU,CAAE;sBAAA4L,QAAA,gBAC5GpN,OAAA;wBAAK0N,SAAS,EAAC,aAAa;wBAAAN,QAAA,gBACxBpN,OAAA;0BAAK0N,SAAS,EAAC,YAAY;0BAAAN,QAAA,gBACvBpN,OAAA;4BAAK0N,SAAS,EAAC,aAAa;4BAAAN,QAAA,EACvB,EAAA8G,gBAAA,GAAApL,KAAK,CAACZ,OAAO,cAAAgM,gBAAA,uBAAbA,gBAAA,CAAe/L,YAAY,KAAI,UAAU,gBAAGnI,OAAA,CAACR,OAAO;8BAAA8N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,gBAAGzN,OAAA,CAACV,YAAY;8BAAAgO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1E,CAAC,eACNzN,OAAA;4BAAM0N,SAAS,EAAC,MAAM;4BAAAN,QAAA,GAAC,UAAQ,GAAA+G,sBAAA,GAACrL,KAAK,CAAC6C,iBAAiB,cAAAwI,sBAAA,cAAAA,sBAAA,GAAIrL,KAAK,CAACqD,SAAS;0BAAA;4BAAAmB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EACjF3E,KAAK,CAACiM,SAAS,iBACZ/U,OAAA;4BAAK0N,SAAS,EAAC,kBAAkB;4BAAAN,QAAA,eAC7BpN,OAAA;8BAAAoN,QAAA,EAAM;4BAAS;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAET,CAAC,eACNzN,OAAA;0BAAK0N,SAAS,EAAC,gBAAgB;0BAAAN,QAAA,eAC3BpN,OAAA;4BAAK0N,SAAS,EAAC,UAAU;4BAAAN,QAAA,gBACrBpN,OAAA,CAACT,UAAU;8BAAA+N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACdzN,OAAA;8BAAAoN,QAAA,EAAOjO,MAAM,CAACmR,SAAS,CAACxH,KAAK,CAACyH,SAAS,CAAC,CAACC,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,OAAO;4BAAC;8BAAAnD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eAENzN,OAAA;wBAAK0N,SAAS,EAAC,eAAe;wBAAAN,QAAA,gBAC1BpN,OAAA;0BAAK0N,SAAS,EAAC,OAAO;0BAAAN,QAAA,gBAClBpN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAAC4C;4BAAY;8BAAA4B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,EAChD3E,KAAK,CAAC4H,kBAAkB,GAAG,CAAC,gBACzB1Q,OAAA;8BAAK0N,SAAS,EAAC,qCAAqC;8BAAAN,QAAA,eAChDpN,OAAA;gCAAK0N,SAAS,EAAC,aAAa;gCAAAN,QAAA,eACxBpN,OAAA;kCAAM2N,KAAK,EAAE;oCAAEiB,QAAQ,EAAE;kCAAG,CAAE;kCAAAxB,QAAA,EAAEtE,KAAK,CAAC4H;gCAAkB;kCAAApD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/D;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,gBAENzN,OAAA;8BAAK0N,SAAS,EAAC,0BAA0B;8BAAAN,QAAA,eACrCpN,OAAA;gCAAK0N,SAAS,EAAC,aAAa;gCAAAN,QAAA,eACxBpN,OAAA;kCAAM2N,KAAK,EAAE;oCAAEiB,QAAQ,EAAE;kCAAG,CAAE;kCAAAxB,QAAA,EAAC;gCAAE;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACvC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAET,CAAC,eACNzN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,eAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EACjBtE,KAAK,CAAC6H;4BAAe;8BAAArD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNzN,OAAA;0BAAK0N,SAAS,EAAC,OAAO;0BAAAN,QAAA,gBAClBpN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,YAAY;8BAAAN,QAAA,EAAC;4BAAM;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACxCzN,OAAA;8BAAK0N,SAAS,EAAC,YAAY;8BAAAN,QAAA,GAAC,KACrB,EAACtE,KAAK,CAAC8H,WAAW,IAAI9H,KAAK,CAAC8H,WAAW,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC7O,QAAQ,CAAC,CAAC,CAAC8O,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;4BAAA;8BAAAxD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,eACNzN,OAAA;4BAAK0N,SAAS,EAAC,OAAO;4BAAAN,QAAA,gBAClBpN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAC;4BAAU;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACvCzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAACiI;4BAAc;8BAAAzD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eAENzN,OAAA;wBAAK0N,SAAS,EAAC,eAAe;wBAAAN,QAAA,EACzBtE,KAAK,CAACH,SAAS,CAACP,WAAW,CAAC,CAAC,CAACQ,QAAQ,CAAC,MAAM,CAAC,gBAEvC5I,OAAA;0BAAK0N,SAAS,EAAC,cAAc;0BAAAN,QAAA,eACzBpN,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,gBACzBpN,OAAA;8BAAK0N,SAAS,EAAC,KAAK;8BAACC,KAAK,EAAE;gCAAEG,OAAO,EAAE,MAAM;gCAAEkD,SAAS,EAAE;8BAAE,CAAE;8BAAA5D,QAAA,eAC1DpN,OAAA,CAACN,OAAO;gCAACgO,SAAS,EAAC;8BAAK;gCAAAJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,eACNzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAAAN,QAAA,EAAEtE,KAAK,CAACH;4BAAS;8BAAA2E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,gBAENzN,OAAA,CAAAE,SAAA;0BAAAkN,QAAA,EACK,EAAAgH,gBAAA,GAAAtL,KAAK,CAACZ,OAAO,cAAAkM,gBAAA,uBAAbA,gBAAA,CAAejM,YAAY,KAAI,UAAU,gBACtCnI,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,eACzBpN,OAAA;8BAAK0N,SAAS,EAAC,cAAc;8BAAAN,QAAA,gBACzBpN,OAAA;gCAAK0N,SAAS,EAAC,KAAK;gCAACC,KAAK,EAAE;kCAAEG,OAAO,EAAE,MAAM;kCAAEkD,SAAS,EAAE;gCAAE,CAAE;gCAAA5D,QAAA,eAC1DpN,OAAA,CAACN,OAAO;kCAACgO,SAAS,EAAC;gCAAK;kCAAAJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1B,CAAC,eACNzN,OAAA;gCAAK0N,SAAS,EAAC,OAAO;gCAAAN,QAAA,EAAC;8BAAiB;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC7C;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC,gBAENzN,OAAA;4BAAK0N,SAAS,EAAC,cAAc;4BAAAN,QAAA,gBACzBpN,OAAA;8BAAK0N,SAAS,EAAC,cAAc;8BAAAN,QAAA,gBACzBpN,OAAA;gCAAK0N,SAAS,EAAC,KAAK;gCAAAN,QAAA,eAChBpN,OAAA,CAACN,OAAO;kCAACgO,SAAS,EAAC;gCAAK;kCAAAJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1B,CAAC,eACNzN,OAAA;gCAAK0N,SAAS,EAAC,OAAO;gCAAAN,QAAA,EACjBtE,KAAK,CAACZ,OAAO,IAAI,IAAAmM,gBAAA,GAAGvL,KAAK,CAACZ,OAAO,cAAAmM,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAepD,QAAQ,cAAAqD,qBAAA,uBAAvBA,qBAAA,CAAyBpD,GAAG,MAAAqD,gBAAA,GAAKzL,KAAK,CAACZ,OAAO,cAAAqM,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAetD,QAAQ,cAAAuD,qBAAA,uBAAvBA,qBAAA,CAAyBrD,MAAM;AACjL,kFAAkF,EAAAsD,gBAAA,GAAA3L,KAAK,CAACZ,OAAO,cAAAuM,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAexD,QAAQ,cAAAyD,qBAAA,uBAAvBA,qBAAA,CAAyBtD,WAAW,KAAI,KAAK,KAAAuD,gBAAA,GAAG7L,KAAK,CAACZ,OAAO,cAAAyM,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAe1D,QAAQ,cAAA2D,qBAAA,uBAAvBA,qBAAA,CAAyBxD,WAAW,OAAAyD,gBAAA,GAAK/L,KAAK,CAACZ,OAAO,cAAA2M,gBAAA,wBAAAC,qBAAA,GAAbD,gBAAA,CAAe5D,QAAQ,cAAA6D,qBAAA,uBAAvBA,qBAAA,CAAyBzD,MAAM;8BAAE;gCAAA/D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/G,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACNzN,OAAA;8BAAK0N,SAAS,EAAC,OAAO;8BAACS,OAAO,EAAGnC,KAAK,IAAKD,mBAAmB,CAACC,KAAK,EAAElD,KAAK,CAAE;8BAAAsE,QAAA,EACxEtE,KAAK,CAACwI,UAAU,gBACbtR,OAAA;gCAAK0N,SAAS,EAAC,iBAAiB;gCAAAN,QAAA,EAAEtE,KAAK,CAACwI,UAAU,CAACC;8BAAI;gCAAAjE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,gBAE9DzN,OAAA;gCAAK0N,SAAS,EAAC,iBAAiB;gCAAAN,QAAA,EAAC;8BAAmB;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAE7D,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL;wBAAC,gBAEZ;sBACL;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA,GAvGA2B,IAAI,CAACC,MAAM,CAAC,CAAC;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA2GlB,CAAC,GAEN,IAAI;kBAAA,CAEX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CAAC,gBAENzN,OAAA,CAACF,YAAY;YACTmD,gBAAgB,EAAEA,gBAAiB;YACnCE,gBAAgB,EAAEA,gBAAiB;YACnCE,iBAAiB,EAAEA,iBAAkB;YACrCE,iBAAiB,EAAEA,iBAAkB;YACrCE,aAAa,EAAEA,aAAc;YAC7BM,cAAc,EAAEA,cAAe;YAC/BU,kBAAkB,EAAEA,kBAAmB;YACvCF,cAAc,EAAEA,cAAe;YAC/BN,eAAe,EAAEA,eAAgB;YACjCE,cAAc,EAAEA,cAAe;YAC/BE,iBAAiB,EAAEA,iBAAkB;YACrCa,eAAe,EAAEA,eAAgB;YACjCgI,gBAAgB,EAAEA,gBAAiB;YACnCN,sBAAsB,EAAEA,sBAAuB;YAC/Cb,mBAAmB,EAAEA,mBAAoB;YACzCG,aAAa,EAAEA,aAAc;YAC7B9J,MAAM,EAAEA;UAAO;YAAAkL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEI;EAAC,gBACnB,CAAC;AAIX,CAAC;AAAC7M,GAAA,CA1xCID,IAAI;EAAA,QAiDW5B,WAAW;AAAA;AAAAiW,GAAA,GAjD1BrU,IAAI;AA4xCV,eAAeA,IAAI;AAAC,IAAAL,EAAA,EAAAE,GAAA,EAAAwU,GAAA;AAAAC,YAAA,CAAA3U,EAAA;AAAA2U,YAAA,CAAAzU,GAAA;AAAAyU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}