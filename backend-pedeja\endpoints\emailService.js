const nodemailer = require('nodemailer');

const FRONTEND_URL = process.env.NODE_ENV === 'production' 
    ? "https://app.pedeja.chat" // URL de produção
    : "http://localhost:3000";  // URL de desenvolvimento/local
const EMAIL_USER = '<EMAIL>'
const transporter = nodemailer.createTransport({
    host: 'smtp-relay.brevo.com', // Servidor SMTP da Brevo
    port: 587, // Porta fornecida
    secure: false, // TLS desativado (não é necessário para porta 587)
    auth: {
        user: '<EMAIL>', // Seu login SMTP
        pass: '9KJs7OWrqLt6HBmT', // Sua senha/chave SMTP
    }
});

const sendResetEmail = async (email, token) => {
    const resetUrl = `${FRONTEND_URL}/redefinir-senha/${token}`;

    const mailOptions = {
        from: EMAIL_USER,
        to: email,
        subject: 'Recuperação de senha PedeJá',
        html: `<p>Para redefinir sua senha, clique no link abaixo:</p>
               <a href="${resetUrl}">${resetUrl}</a>
               <p>Se você não solicitou esta alteração, ignore este e-mail.</p>`,
    };

    try {
        await transporter.sendMail(mailOptions);
        console.log('Email enviado com sucesso.')
    } catch (error) {
        console.error('Erro ao enviar e-mail:', error);
    }
};

module.exports = { sendResetEmail };
