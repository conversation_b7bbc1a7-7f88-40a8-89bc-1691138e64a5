/**
 * 🎯 EXEMPLO PRÁTICO - Como usar o Console Wrapper
 * 
 * Este arquivo mostra como migrar console.log existente para o sistema otimizado
 */

import { 
  log, 
  whatsapp, 
  api, 
  socket, 
  pedido, 
  auth, 
  success, 
  warning, 
  danger, 
  error,
  group
} from './console';

// ❌ ANTES (será mantido em produção)
function exemploAntes() {
  console.log('🔍 Debug: carregando chats');
  console.log('📡 Socket conectado');
  console.log('📞 Chamada da API: GET /chats');
  console.log('✅ Login realizado com sucesso');
  console.log('⚠️ Sessão expirando em 5 minutos');
  console.error('❌ Erro na autenticação');
}

// ✅ DEPOIS (removido automaticamente em produção)
function exemploDepois() {
  log('Debug: carregando chats');              // ❌ Removido em produção
  socket('Socket conectado');                  // ❌ Removido em produção  
  api('Chamada da API: GET /chats');           // ❌ Removido em produção
  success('Login realizado com sucesso');      // ❌ Removido em produção
  warning('Sessão expirando em 5 minutos');   // ❌ Removido em produção
  error('Erro na autenticação');               // ✅ Mantido em produção
}

// 🎨 EXEMPLO: WhatsApp com logs coloridos
function exemploWhatsApp() {
  group('💬 Processo WhatsApp', () => {
    whatsapp('Gerando QR Code...');
    whatsapp('QR Code gerado com sucesso');
    socket('Conectado ao WebSocket');
    whatsapp('Primeira mensagem recebida');
    success('WhatsApp totalmente conectado!');
  });
}

// 🛒 EXEMPLO: Sistema de Pedidos  
function exemploPedidos() {
  group('🛒 Novo Pedido', () => {
    pedido('Validando itens do carrinho');
    api('POST /pedidos - Criando pedido');
    pedido('Pedido #123 criado com sucesso');
    whatsapp('Notificação enviada ao cliente');
    success('Pedido processado completamente!');
  });
}

// 🔐 EXEMPLO: Autenticação
function exemploAuth() {
  group('🔐 Processo de Login', () => {
    auth('Validando credenciais...');
    api('POST /auth/login');
    auth('Token JWT recebido');
    auth('Salvando no localStorage');
    success('Usuário autenticado!');
  });
}

// 📊 EXEMPLO: Sistema de Relatórios
function exemploRelatorios() {
  group('📊 Gerando Relatório', () => {
    log('Coletando dados dos últimos 30 dias');
    api('GET /relatorios/vendas');
    log('Processando 1.240 registros');
    log('Calculando métricas...');
    success('Relatório gerado em 2.3s');
  });
}

// 🚨 EXEMPLO: Tratamento de Erro
function exemploErros() {
  try {
    api('GET /dados-importantes');
    // Simular erro
    throw new Error('Conexão perdida');
  } catch (err) {
    // ✅ Este erro SEMPRE aparecerá, mesmo em produção
    error('Erro crítico capturado:', err.message);
    warning('Tentando reconectar...');
    
    // ❌ Este debug só aparece em desenvolvimento  
    log('Stack trace completo:', err.stack);
  }
}

// 🔄 EXEMPLO: Integração com useEffect
function exemploReactHook() {
  // Em um componente React
  /*
  useEffect(() => {
    group('🔄 Componente Mounted', () => {
      log('Componente WhatsApp montado');
      api('Buscando configurações iniciais');
      socket('Conectando ao WebSocket...');
      
      // Cleanup  
      return () => {
        log('Limpando recursos do componente');
        socket('Desconectando WebSocket');
      };
    });
  }, []);
  */
}

// 🎯 EXEMPLO: Performance Monitoring
function exemploPerformance() {
  const startTime = performance.now();
  
  group('⚡ Performance Check', () => {
    log('Iniciando operação pesada...');
    
    // Simular processamento
    setTimeout(() => {
      const endTime = performance.now();
      const duration = (endTime - startTime).toFixed(2);
      
      if (duration > 1000) {
        warning(`Operação lenta: ${duration}ms`);
      } else {
        success(`Operação rápida: ${duration}ms`);
      }
    }, 100);
  });
}

export {
  exemploAntes,
  exemploDepois,
  exemploWhatsApp,
  exemploPedidos,
  exemploAuth,
  exemploRelatorios,
  exemploErros,
  exemploReactHook,
  exemploPerformance
}; 