import { NodeHttpHandler as RequestHandler } from "@smithy/node-http-handler";
import { CognitoIdentityClientConfig } from "./CognitoIdentityClient";
export declare const getRuntimeConfig: (
  config: CognitoIdentityClientConfig
) => {
  runtime: string;
  defaultsMode: import("@smithy/types").Provider<
    import("@smithy/smithy-client").ResolvedDefaultsMode
  >;
  bodyLengthChecker: import("@smithy/types").BodyLengthCalculator;
  credentialDefaultProvider: (
    init?:
      | import("@aws-sdk/credential-provider-node").DefaultProviderInit
      | undefined
  ) => import("@smithy/types").MemoizedProvider<
    import("@smithy/types").AwsCredentialIdentity
  >;
  defaultUserAgentProvider: import("@smithy/types").Provider<
    import("@smithy/types").UserAgent
  >;
  maxAttempts: number | import("@smithy/types").Provider<number>;
  region: string | import("@smithy/types").Provider<string>;
  requestHandler:
    | RequestHandler
    | import("@smithy/protocol-http").HttpHandler<any>;
  retryMode: string | import("@smithy/types").Provider<string>;
  sha256: import("@smithy/types").HashConstructor;
  streamCollector: import("@smithy/types").StreamCollector;
  useDualstackEndpoint: boolean | import("@smithy/types").Provider<boolean>;
  useFipsEndpoint: boolean | import("@smithy/types").Provider<boolean>;
  apiVersion: string;
  urlParser: import("@smithy/types").UrlParser;
  base64Decoder: import("@smithy/types").Decoder;
  base64Encoder: (_input: string | Uint8Array) => string;
  utf8Decoder: import("@smithy/types").Decoder;
  utf8Encoder: (input: string | Uint8Array) => string;
  disableHostPrefix: boolean;
  serviceId: string;
  logger: import("@smithy/types").Logger;
  extensions: import("./runtimeExtensions").RuntimeExtension[];
  endpoint?:
    | ((
        | string
        | import("@smithy/types").Endpoint
        | import("@smithy/types").Provider<import("@smithy/types").Endpoint>
        | import("@smithy/types").EndpointV2
        | import("@smithy/types").Provider<import("@smithy/types").EndpointV2>
      ) &
        (
          | string
          | import("@smithy/types").Provider<string>
          | import("@smithy/types").Endpoint
          | import("@smithy/types").Provider<import("@smithy/types").Endpoint>
          | import("@smithy/types").EndpointV2
          | import("@smithy/types").Provider<import("@smithy/types").EndpointV2>
        ))
    | undefined;
  endpointProvider: (
    endpointParams: import("./endpoint/EndpointParameters").EndpointParameters,
    context?: {
      logger?: import("@smithy/types").Logger | undefined;
    }
  ) => import("@smithy/types").EndpointV2;
  tls?: boolean | undefined;
  retryStrategy?:
    | import("@smithy/types").RetryStrategy
    | import("@smithy/types").RetryStrategyV2
    | undefined;
  customUserAgent?: string | import("@smithy/types").UserAgent | undefined;
  httpAuthSchemes:
    | import("@smithy/types").HttpAuthScheme[]
    | (
        | {
            schemeId: string;
            identityProvider: (
              ipc: import("@smithy/types").IdentityProviderConfig
            ) =>
              | import("@smithy/types").IdentityProvider<
                  import("@smithy/types").Identity
                >
              | undefined;
            signer: import("@aws-sdk/core").AwsSdkSigV4Signer;
          }
        | {
            schemeId: string;
            identityProvider: (
              ipc: import("@smithy/types").IdentityProviderConfig
            ) =>
              | import("@smithy/types").IdentityProvider<
                  import("@smithy/types").Identity
                >
              | (() => Promise<{}>);
            signer: import("@smithy/core").NoAuthSigner;
          }
      )[];
  httpAuthSchemeProvider: import("./auth/httpAuthSchemeProvider").CognitoIdentityHttpAuthSchemeProvider;
  credentials?:
    | import("@smithy/types").AwsCredentialIdentity
    | import("@smithy/types").AwsCredentialIdentityProvider
    | undefined;
  signer?:
    | import("@smithy/types").RequestSigner
    | ((
        authScheme?: import("@smithy/types").AuthScheme | undefined
      ) => Promise<import("@smithy/types").RequestSigner>)
    | undefined;
  signingEscapePath?: boolean | undefined;
  systemClockOffset?: number | undefined;
  signingRegion?: string | undefined;
  signerConstructor?:
    | (new (
        options: import("@smithy/signature-v4").SignatureV4Init &
          import("@smithy/signature-v4").SignatureV4CryptoInit
      ) => import("@smithy/types").RequestSigner)
    | undefined;
};
