{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\components\\\\LeftMenu\\\\AtendimentoModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { AuthContext } from \"../../contexts/auth\";\nimport { toast } from \"react-toastify\";\nimport ConfirmDialog from \"../ConfirmDialog\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AtendimentoModal = ({\n  atendimentosPendentes,\n  removerAtendimento,\n  iniciarAtendimento,\n  cancelarTodosAtendimentos,\n  modalOpen,\n  setModalOpen\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useContext(AuthContext);\n  const [atendimentoIniciando, setAtendimentoIniciando] = useState(null);\n\n  // Estados para modal de confirmação individual\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  const [atendimentoParaRemover, setAtendimentoParaRemover] = useState(null);\n\n  // Estados para confirmação de \"Marcar Todos como Resolvidos\"\n  const [confirmResolverTodosOpen, setConfirmResolverTodosOpen] = useState(false);\n  const [resolvendoTodos, setResolvendoTodos] = useState(false);\n  if (!modalOpen) return null;\n\n  // Função para abrir confirmação individual\n  const handleConfirmarRemocao = atendimento => {\n    setAtendimentoParaRemover(atendimento);\n    setConfirmDialogOpen(true);\n  };\n\n  // Função para executar remoção individual\n  const executarRemocao = () => {\n    if (atendimentoParaRemover) {\n      removerAtendimento(atendimentoParaRemover.atendimento_id);\n      setAtendimentoParaRemover(null);\n    }\n  };\n\n  // Função para resolver todos os atendimentos\n  const handleResolverTodos = async () => {\n    if (atendimentosPendentes.length === 0) return;\n    const totalAtendimentos = atendimentosPendentes.length;\n    setResolvendoTodos(true);\n    try {\n      // Usar função específica para operação em lote (sem toasts individuais)\n      const resultado = await cancelarTodosAtendimentos();\n      if (resultado.sucesso) {\n        toast(`✅ Todos os ${resultado.total} atendimentos foram resolvidos!`, {\n          type: 'success',\n          autoClose: 3000\n        });\n      } else if (resultado.cancelados > 0) {\n        toast(`⚠️ ${resultado.cancelados} de ${resultado.total} atendimentos foram resolvidos. ${resultado.falharam} falharam.`, {\n          type: 'warning',\n          autoClose: 5000\n        });\n      } else {\n        toast(`❌ Erro ao resolver os atendimentos. Tente novamente.`, {\n          type: 'error',\n          autoClose: 5000\n        });\n      }\n    } catch (error) {\n      console.error('Erro ao resolver todos os atendimentos:', error);\n      toast(`❌ Erro inesperado ao resolver ${totalAtendimentos} atendimentos`, {\n        type: 'error'\n      });\n    } finally {\n      setResolvendoTodos(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"atendimento-modal\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"atendimento-modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"atendimento-modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83D\\uDCE2 Atendimentos Pendentes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 21\n        }, this), atendimentosPendentes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"atendimento-counter-badge\",\n          children: atendimentosPendentes.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this), atendimentosPendentes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-atendimentos\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-atendimentos-icon\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Nenhum atendimento pendente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          children: \"Quando houver solicita\\xE7\\xF5es de atendimento, elas aparecer\\xE3o aqui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"atendimentos-list\",\n          children: atendimentosPendentes.map(atendimento => {\n            // **📌 Normalizar número de telefone**\n            const telefone = atendimento.celular.split(\"@\")[0]; // Remove o domínio\n            const telefoneFormatado = `+${telefone.slice(0, 2)} ${telefone.slice(2, 4)} ${telefone.slice(4, 8)}-${telefone.slice(8)}`;\n\n            // **📌 Função para iniciar atendimento**\n            const handleIniciarAtendimento = async () => {\n              if (!user) {\n                toast('Erro: dados do usuário não disponíveis', {\n                  type: 'error'\n                });\n                return;\n              }\n              setAtendimentoIniciando(atendimento.atendimento_id);\n              try {\n                const sucesso = await iniciarAtendimento(atendimento.atendimento_id, {\n                  user_id: user._id,\n                  user_name: user.name\n                });\n                if (sucesso) {\n                  var _atendimento$celular;\n                  // Navegar para o WhatsApp e fechar modal\n                  setModalOpen(false);\n                  navigate(\"/whatsapp\", {\n                    state: {\n                      lead_id: atendimento.lead_id,\n                      // Passar dados do contato para fallback\n                      nome: atendimento.nome,\n                      celular: (_atendimento$celular = atendimento.celular) === null || _atendimento$celular === void 0 ? void 0 : _atendimento$celular.split('@')[0],\n                      // Remove domínio @s.whatsapp.net\n                      mensagem: atendimento.mensagem,\n                      timestamp: atendimento.timestamp\n                    }\n                  });\n                }\n              } catch (error) {\n                console.error('Erro ao iniciar atendimento:', error);\n              } finally {\n                setAtendimentoIniciando(null);\n              }\n            };\n\n            // **📌 Função para direcionar ao WhatsApp (atualizada)**\n            const direcionarParaWhatsApp = () => {\n              handleIniciarAtendimento(); // Usar nova lógica com persistência\n            };\n            const isIniciando = atendimentoIniciando === atendimento.atendimento_id;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"atendimento-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"atendimento-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"atendimento-cliente\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"cliente-icon\",\n                    children: \"\\uD83D\\uDC64\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: atendimento.nome\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 153,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      children: telefoneFormatado\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"atendimento-mensagem\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mensagem-icon\",\n                    children: \"\\uD83D\\uDCAC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: atendimento.mensagem\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"atendimento-horario\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"horario-icon\",\n                    children: \"\\u23F0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: new Date(atendimento.timestamp).toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"atendimento-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"atendimento-whatsapp-btn\",\n                  onClick: direcionarParaWhatsApp,\n                  disabled: isIniciando,\n                  style: {\n                    opacity: isIniciando ? 0.7 : 1\n                  },\n                  children: isIniciando ? '⏳ Iniciando...' : '📲 Responder no WhatsApp'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"atendimento-resolvido-btn\",\n                  onClick: () => handleConfirmarRemocao(atendimento),\n                  disabled: isIniciando,\n                  children: \"\\u2705 Resolvido\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 41\n              }, this)]\n            }, atendimento.atendimento_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 37\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"atendimento-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"atendimento-resolver-todos-btn\",\n            onClick: () => setConfirmResolverTodosOpen(true),\n            disabled: resolvendoTodos,\n            children: resolvendoTodos ? '⏳ Resolvendo...' : '✅ Marcar Todos como Resolvidos'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"atendimento-modal-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"atendimento-fechar-btn\",\n          onClick: () => setModalOpen(false),\n          children: \"\\u274C Fechar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {\n      title: \"Confirmar Resolu\\xE7\\xE3o\",\n      open: confirmDialogOpen,\n      setOpen: setConfirmDialogOpen,\n      onConfirm: executarRemocao,\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Tem certeza que deseja marcar este atendimento como resolvido?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 17\n      }, this), atendimentoParaRemover && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '10px',\n          padding: '10px',\n          backgroundColor: '#f5f5f5',\n          borderRadius: '4px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Cliente:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 25\n        }, this), \" \", atendimentoParaRemover.nome, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 80\n        }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Mensagem:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 25\n        }, this), \" \", atendimentoParaRemover.mensagem]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {\n      title: \"Resolver Todos os Atendimentos\",\n      open: confirmResolverTodosOpen,\n      setOpen: setConfirmResolverTodosOpen,\n      onConfirm: handleResolverTodos,\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Tem certeza que deseja marcar \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [\"todos os \", atendimentosPendentes.length, \" atendimento(s)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 50\n        }, this), \" como resolvidos?\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '10px',\n          padding: '10px',\n          backgroundColor: '#fff3cd',\n          borderRadius: '4px',\n          border: '1px solid #ffeaa7'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"\\u26A0\\uFE0F Aten\\xE7\\xE3o:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 21\n        }, this), \" Esta a\\xE7\\xE3o n\\xE3o pode ser desfeita. Todos os atendimentos da fila ser\\xE3o removidos.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 9\n  }, this);\n};\n_s(AtendimentoModal, \"7JInMx1QiipuuwdstOUK1oA+XdY=\", false, function () {\n  return [useNavigate];\n});\n_c = AtendimentoModal;\nexport default AtendimentoModal;\nvar _c;\n$RefreshReg$(_c, \"AtendimentoModal\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "useNavigate", "AuthContext", "toast", "ConfirmDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AtendimentoModal", "atendimentosPendentes", "remover<PERSON><PERSON><PERSON><PERSON>", "iniciarAtendimento", "cancelarTodosAtendimentos", "modalOpen", "setModalOpen", "_s", "navigate", "user", "atendimentoIniciando", "setAtendimentoIniciando", "confirmDialogOpen", "setConfirmDialogOpen", "atendimentoParaRemover", "setAtendimentoParaRemover", "confirmResolverTodosOpen", "setConfirmResolverTodosOpen", "resolvendoTodos", "setResolvendoTodos", "handleConfirmarRemocao", "atendimento", "executarRemocao", "atendimento_id", "handleResolverTodos", "length", "totalAtendimentos", "resultado", "sucesso", "total", "type", "autoClose", "cancelados", "falharam", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "telefone", "celular", "split", "telefoneFormatado", "slice", "handleIniciarAtendimento", "user_id", "_id", "user_name", "name", "_atendimento$celular", "state", "lead_id", "nome", "mensagem", "timestamp", "direcionarParaWhatsApp", "isIniciando", "Date", "toLocaleString", "onClick", "disabled", "style", "opacity", "title", "open", "<PERSON><PERSON><PERSON>", "onConfirm", "marginTop", "padding", "backgroundColor", "borderRadius", "border", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/components/LeftMenu/AtendimentoModal.jsx"], "sourcesContent": ["import React, { useContext, useState } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { AuthContext } from \"../../contexts/auth\";\r\nimport { toast } from \"react-toastify\";\r\nimport ConfirmDialog from \"../ConfirmDialog\";\r\n\r\nconst AtendimentoModal = ({ \r\n    atendimentosPendentes, \r\n    removerAtendimento, \r\n    iniciarAtendimento,\r\n    cancelarTodosAtendimentos,\r\n    modalOpen, \r\n    setModalOpen \r\n}) => {\r\n    const navigate = useNavigate();\r\n    const { user } = useContext(AuthContext);\r\n    const [atendimentoIniciando, setAtendimentoIniciando] = useState(null);\r\n    \r\n    // Estados para modal de confirmação individual\r\n    const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n    const [atendimentoParaRemover, setAtendimentoParaRemover] = useState(null);\r\n    \r\n    // Estados para confirmação de \"Marcar Todos como Resolvidos\"\r\n    const [confirmResolverTodosOpen, setConfirmResolverTodosOpen] = useState(false);\r\n    const [resolvendoTodos, setResolvendoTodos] = useState(false);\r\n\r\n    if (!modalOpen) return null;\r\n\r\n    // Função para abrir confirmação individual\r\n    const handleConfirmarRemocao = (atendimento) => {\r\n        setAtendimentoParaRemover(atendimento);\r\n        setConfirmDialogOpen(true);\r\n    };\r\n\r\n    // Função para executar remoção individual\r\n    const executarRemocao = () => {\r\n        if (atendimentoParaRemover) {\r\n            removerAtendimento(atendimentoParaRemover.atendimento_id);\r\n            setAtendimentoParaRemover(null);\r\n        }\r\n    };\r\n\r\n    // Função para resolver todos os atendimentos\r\n    const handleResolverTodos = async () => {\r\n        if (atendimentosPendentes.length === 0) return;\r\n        \r\n        const totalAtendimentos = atendimentosPendentes.length;\r\n        setResolvendoTodos(true);\r\n        \r\n        try {\r\n            // Usar função específica para operação em lote (sem toasts individuais)\r\n            const resultado = await cancelarTodosAtendimentos();\r\n            \r\n            if (resultado.sucesso) {\r\n                toast(`✅ Todos os ${resultado.total} atendimentos foram resolvidos!`, { \r\n                    type: 'success',\r\n                    autoClose: 3000\r\n                });\r\n            } else if (resultado.cancelados > 0) {\r\n                toast(`⚠️ ${resultado.cancelados} de ${resultado.total} atendimentos foram resolvidos. ${resultado.falharam} falharam.`, { \r\n                    type: 'warning',\r\n                    autoClose: 5000\r\n                });\r\n            } else {\r\n                toast(`❌ Erro ao resolver os atendimentos. Tente novamente.`, { \r\n                    type: 'error',\r\n                    autoClose: 5000\r\n                });\r\n            }\r\n        } catch (error) {\r\n            console.error('Erro ao resolver todos os atendimentos:', error);\r\n            toast(`❌ Erro inesperado ao resolver ${totalAtendimentos} atendimentos`, { type: 'error' });\r\n        } finally {\r\n            setResolvendoTodos(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"atendimento-modal\">\r\n            <div className=\"atendimento-modal-content\">\r\n                <div className=\"atendimento-modal-header\">\r\n                    <h3>📢 Atendimentos Pendentes</h3>\r\n                    {atendimentosPendentes.length > 0 && (\r\n                        <div className=\"atendimento-counter-badge\">\r\n                            {atendimentosPendentes.length}\r\n                        </div>\r\n                    )}\r\n                </div>\r\n\r\n                {atendimentosPendentes.length === 0 ? (\r\n                    <div className=\"no-atendimentos\">\r\n                        <div className=\"no-atendimentos-icon\">✅</div>\r\n                        <p>Nenhum atendimento pendente</p>\r\n                        <small>Quando houver solicitações de atendimento, elas aparecerão aqui.</small>\r\n                    </div>\r\n                ) : (\r\n                    <>\r\n                        <div className=\"atendimentos-list\">\r\n                            {atendimentosPendentes.map((atendimento) => {\r\n                                // **📌 Normalizar número de telefone**\r\n                                const telefone = atendimento.celular.split(\"@\")[0]; // Remove o domínio\r\n                                const telefoneFormatado = `+${telefone.slice(0, 2)} ${telefone.slice(2, 4)} ${telefone.slice(4, 8)}-${telefone.slice(8)}`;\r\n\r\n                                // **📌 Função para iniciar atendimento**\r\n                                const handleIniciarAtendimento = async () => {\r\n                                    if (!user) {\r\n                                        toast('Erro: dados do usuário não disponíveis', { type: 'error' });\r\n                                        return;\r\n                                    }\r\n\r\n                                    setAtendimentoIniciando(atendimento.atendimento_id);\r\n                                    \r\n                                    try {\r\n                                        const sucesso = await iniciarAtendimento(atendimento.atendimento_id, {\r\n                                            user_id: user._id,\r\n                                            user_name: user.name\r\n                                        });\r\n\r\n                                        if (sucesso) {\r\n                                            // Navegar para o WhatsApp e fechar modal\r\n                                            setModalOpen(false);\r\n                                            navigate(\"/whatsapp\", { \r\n                                                state: { \r\n                                                    lead_id: atendimento.lead_id,\r\n                                                    // Passar dados do contato para fallback\r\n                                                    nome: atendimento.nome,\r\n                                                    celular: atendimento.celular?.split('@')[0], // Remove domínio @s.whatsapp.net\r\n                                                    mensagem: atendimento.mensagem,\r\n                                                    timestamp: atendimento.timestamp\r\n                                                } \r\n                                            });\r\n                                        }\r\n                                    } catch (error) {\r\n                                        console.error('Erro ao iniciar atendimento:', error);\r\n                                    } finally {\r\n                                        setAtendimentoIniciando(null);\r\n                                    }\r\n                                };\r\n\r\n                                // **📌 Função para direcionar ao WhatsApp (atualizada)**\r\n                                const direcionarParaWhatsApp = () => {\r\n                                    handleIniciarAtendimento(); // Usar nova lógica com persistência\r\n                                };\r\n\r\n                                const isIniciando = atendimentoIniciando === atendimento.atendimento_id;\r\n\r\n                                return (\r\n                                    <div key={atendimento.atendimento_id} className=\"atendimento-card\">\r\n                                        <div className=\"atendimento-info\">\r\n                                            <div className=\"atendimento-cliente\">\r\n                                                <span className=\"cliente-icon\">👤</span>\r\n                                                <div>\r\n                                                    <strong>{atendimento.nome}</strong>\r\n                                                    <small>{telefoneFormatado}</small>\r\n                                                </div>\r\n                                            </div>\r\n                                            \r\n                                            <div className=\"atendimento-mensagem\">\r\n                                                <span className=\"mensagem-icon\">💬</span>\r\n                                                <p>{atendimento.mensagem}</p>\r\n                                            </div>\r\n                                            \r\n                                            <div className=\"atendimento-horario\">\r\n                                                <span className=\"horario-icon\">⏰</span>\r\n                                                <small>{new Date(atendimento.timestamp).toLocaleString()}</small>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <div className=\"atendimento-buttons\">\r\n                                            <button \r\n                                                className=\"atendimento-whatsapp-btn\" \r\n                                                onClick={direcionarParaWhatsApp}\r\n                                                disabled={isIniciando}\r\n                                                style={{ opacity: isIniciando ? 0.7 : 1 }}\r\n                                            >\r\n                                                {isIniciando ? '⏳ Iniciando...' : '📲 Responder no WhatsApp'}\r\n                                            </button>\r\n                                            <button \r\n                                                className=\"atendimento-resolvido-btn\" \r\n                                                onClick={() => handleConfirmarRemocao(atendimento)}\r\n                                                disabled={isIniciando}\r\n                                            >\r\n                                                ✅ Resolvido\r\n                                            </button>\r\n                                        </div>\r\n                                    </div>\r\n                                );\r\n                            })}\r\n                        </div>\r\n\r\n                        {/* Botão para resolver todos */}\r\n                        <div className=\"atendimento-actions\">\r\n                            <button \r\n                                className=\"atendimento-resolver-todos-btn\" \r\n                                onClick={() => setConfirmResolverTodosOpen(true)}\r\n                                disabled={resolvendoTodos}\r\n                            >\r\n                                {resolvendoTodos ? '⏳ Resolvendo...' : '✅ Marcar Todos como Resolvidos'}\r\n                            </button>\r\n                        </div>\r\n                    </>\r\n                )}\r\n\r\n                <div className=\"atendimento-modal-footer\">\r\n                    <button \r\n                        className=\"atendimento-fechar-btn\" \r\n                        onClick={() => setModalOpen(false)}\r\n                    >\r\n                        ❌ Fechar\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Modal de confirmação individual */}\r\n            <ConfirmDialog\r\n                title=\"Confirmar Resolução\"\r\n                open={confirmDialogOpen}\r\n                setOpen={setConfirmDialogOpen}\r\n                onConfirm={executarRemocao}\r\n            >\r\n                <p>Tem certeza que deseja marcar este atendimento como resolvido?</p>\r\n                {atendimentoParaRemover && (\r\n                    <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>\r\n                        <strong>Cliente:</strong> {atendimentoParaRemover.nome}<br/>\r\n                        <strong>Mensagem:</strong> {atendimentoParaRemover.mensagem}\r\n                    </div>\r\n                )}\r\n            </ConfirmDialog>\r\n\r\n            {/* Modal de confirmação para resolver todos */}\r\n            <ConfirmDialog\r\n                title=\"Resolver Todos os Atendimentos\"\r\n                open={confirmResolverTodosOpen}\r\n                setOpen={setConfirmResolverTodosOpen}\r\n                onConfirm={handleResolverTodos}\r\n            >\r\n                <p>Tem certeza que deseja marcar <strong>todos os {atendimentosPendentes.length} atendimento(s)</strong> como resolvidos?</p>\r\n                <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#fff3cd', borderRadius: '4px', border: '1px solid #ffeaa7' }}>\r\n                    <strong>⚠️ Atenção:</strong> Esta ação não pode ser desfeita. Todos os atendimentos da fila serão removidos.\r\n                </div>\r\n            </ConfirmDialog>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AtendimentoModal;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,aAAa,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7C,MAAMC,gBAAgB,GAAGA,CAAC;EACtBC,qBAAqB;EACrBC,kBAAkB;EAClBC,kBAAkB;EAClBC,yBAAyB;EACzBC,SAAS;EACTC;AACJ,CAAC,KAAK;EAAAC,EAAA;EACF,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB;EAAK,CAAC,GAAGnB,UAAU,CAACG,WAAW,CAAC;EACxC,MAAM,CAACiB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuB,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;;EAE1E;EACA,MAAM,CAACyB,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAE7D,IAAI,CAACc,SAAS,EAAE,OAAO,IAAI;;EAE3B;EACA,MAAMe,sBAAsB,GAAIC,WAAW,IAAK;IAC5CN,yBAAyB,CAACM,WAAW,CAAC;IACtCR,oBAAoB,CAAC,IAAI,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMS,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAIR,sBAAsB,EAAE;MACxBZ,kBAAkB,CAACY,sBAAsB,CAACS,cAAc,CAAC;MACzDR,yBAAyB,CAAC,IAAI,CAAC;IACnC;EACJ,CAAC;;EAED;EACA,MAAMS,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAIvB,qBAAqB,CAACwB,MAAM,KAAK,CAAC,EAAE;IAExC,MAAMC,iBAAiB,GAAGzB,qBAAqB,CAACwB,MAAM;IACtDN,kBAAkB,CAAC,IAAI,CAAC;IAExB,IAAI;MACA;MACA,MAAMQ,SAAS,GAAG,MAAMvB,yBAAyB,CAAC,CAAC;MAEnD,IAAIuB,SAAS,CAACC,OAAO,EAAE;QACnBlC,KAAK,CAAC,cAAciC,SAAS,CAACE,KAAK,iCAAiC,EAAE;UAClEC,IAAI,EAAE,SAAS;UACfC,SAAS,EAAE;QACf,CAAC,CAAC;MACN,CAAC,MAAM,IAAIJ,SAAS,CAACK,UAAU,GAAG,CAAC,EAAE;QACjCtC,KAAK,CAAC,MAAMiC,SAAS,CAACK,UAAU,OAAOL,SAAS,CAACE,KAAK,mCAAmCF,SAAS,CAACM,QAAQ,YAAY,EAAE;UACrHH,IAAI,EAAE,SAAS;UACfC,SAAS,EAAE;QACf,CAAC,CAAC;MACN,CAAC,MAAM;QACHrC,KAAK,CAAC,sDAAsD,EAAE;UAC1DoC,IAAI,EAAE,OAAO;UACbC,SAAS,EAAE;QACf,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DxC,KAAK,CAAC,iCAAiCgC,iBAAiB,eAAe,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAC,CAAC;IAC/F,CAAC,SAAS;MACNX,kBAAkB,CAAC,KAAK,CAAC;IAC7B;EACJ,CAAC;EAED,oBACItB,OAAA;IAAKuC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAC9BxC,OAAA;MAAKuC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACtCxC,OAAA;QAAKuC,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACrCxC,OAAA;UAAAwC,QAAA,EAAI;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACjCxC,qBAAqB,CAACwB,MAAM,GAAG,CAAC,iBAC7B5B,OAAA;UAAKuC,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EACrCpC,qBAAqB,CAACwB;QAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAELxC,qBAAqB,CAACwB,MAAM,KAAK,CAAC,gBAC/B5B,OAAA;QAAKuC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BxC,OAAA;UAAKuC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7C5C,OAAA;UAAAwC,QAAA,EAAG;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClC5C,OAAA;UAAAwC,QAAA,EAAO;QAAgE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,gBAEN5C,OAAA,CAAAE,SAAA;QAAAsC,QAAA,gBACIxC,OAAA;UAAKuC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC7BpC,qBAAqB,CAACyC,GAAG,CAAErB,WAAW,IAAK;YACxC;YACA,MAAMsB,QAAQ,GAAGtB,WAAW,CAACuB,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,MAAMC,iBAAiB,GAAG,IAAIH,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,QAAQ,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE;;YAEzH;YACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;cACzC,IAAI,CAACvC,IAAI,EAAE;gBACPf,KAAK,CAAC,wCAAwC,EAAE;kBAAEoC,IAAI,EAAE;gBAAQ,CAAC,CAAC;gBAClE;cACJ;cAEAnB,uBAAuB,CAACU,WAAW,CAACE,cAAc,CAAC;cAEnD,IAAI;gBACA,MAAMK,OAAO,GAAG,MAAMzB,kBAAkB,CAACkB,WAAW,CAACE,cAAc,EAAE;kBACjE0B,OAAO,EAAExC,IAAI,CAACyC,GAAG;kBACjBC,SAAS,EAAE1C,IAAI,CAAC2C;gBACpB,CAAC,CAAC;gBAEF,IAAIxB,OAAO,EAAE;kBAAA,IAAAyB,oBAAA;kBACT;kBACA/C,YAAY,CAAC,KAAK,CAAC;kBACnBE,QAAQ,CAAC,WAAW,EAAE;oBAClB8C,KAAK,EAAE;sBACHC,OAAO,EAAElC,WAAW,CAACkC,OAAO;sBAC5B;sBACAC,IAAI,EAAEnC,WAAW,CAACmC,IAAI;sBACtBZ,OAAO,GAAAS,oBAAA,GAAEhC,WAAW,CAACuB,OAAO,cAAAS,oBAAA,uBAAnBA,oBAAA,CAAqBR,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;sBAAE;sBAC7CY,QAAQ,EAAEpC,WAAW,CAACoC,QAAQ;sBAC9BC,SAAS,EAAErC,WAAW,CAACqC;oBAC3B;kBACJ,CAAC,CAAC;gBACN;cACJ,CAAC,CAAC,OAAOxB,KAAK,EAAE;gBACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;cACxD,CAAC,SAAS;gBACNvB,uBAAuB,CAAC,IAAI,CAAC;cACjC;YACJ,CAAC;;YAED;YACA,MAAMgD,sBAAsB,GAAGA,CAAA,KAAM;cACjCX,wBAAwB,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;YAED,MAAMY,WAAW,GAAGlD,oBAAoB,KAAKW,WAAW,CAACE,cAAc;YAEvE,oBACI1B,OAAA;cAAsCuC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC9DxC,OAAA;gBAAKuC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC7BxC,OAAA;kBAAKuC,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAChCxC,OAAA;oBAAMuC,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxC5C,OAAA;oBAAAwC,QAAA,gBACIxC,OAAA;sBAAAwC,QAAA,EAAShB,WAAW,CAACmC;oBAAI;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eACnC5C,OAAA;sBAAAwC,QAAA,EAAQS;oBAAiB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAEN5C,OAAA;kBAAKuC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACjCxC,OAAA;oBAAMuC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzC5C,OAAA;oBAAAwC,QAAA,EAAIhB,WAAW,CAACoC;kBAAQ;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eAEN5C,OAAA;kBAAKuC,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAChCxC,OAAA;oBAAMuC,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC5C,OAAA;oBAAAwC,QAAA,EAAQ,IAAIwB,IAAI,CAACxC,WAAW,CAACqC,SAAS,CAAC,CAACI,cAAc,CAAC;kBAAC;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN5C,OAAA;gBAAKuC,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAChCxC,OAAA;kBACIuC,SAAS,EAAC,0BAA0B;kBACpC2B,OAAO,EAAEJ,sBAAuB;kBAChCK,QAAQ,EAAEJ,WAAY;kBACtBK,KAAK,EAAE;oBAAEC,OAAO,EAAEN,WAAW,GAAG,GAAG,GAAG;kBAAE,CAAE;kBAAAvB,QAAA,EAEzCuB,WAAW,GAAG,gBAAgB,GAAG;gBAA0B;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACT5C,OAAA;kBACIuC,SAAS,EAAC,2BAA2B;kBACrC2B,OAAO,EAAEA,CAAA,KAAM3C,sBAAsB,CAACC,WAAW,CAAE;kBACnD2C,QAAQ,EAAEJ,WAAY;kBAAAvB,QAAA,EACzB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA,GArCApB,WAAW,CAACE,cAAc;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsC/B,CAAC;UAEd,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN5C,OAAA;UAAKuC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAChCxC,OAAA;YACIuC,SAAS,EAAC,gCAAgC;YAC1C2B,OAAO,EAAEA,CAAA,KAAM9C,2BAA2B,CAAC,IAAI,CAAE;YACjD+C,QAAQ,EAAE9C,eAAgB;YAAAmB,QAAA,EAEzBnB,eAAe,GAAG,iBAAiB,GAAG;UAAgC;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA,eACR,CACL,eAED5C,OAAA;QAAKuC,SAAS,EAAC,0BAA0B;QAAAC,QAAA,eACrCxC,OAAA;UACIuC,SAAS,EAAC,wBAAwB;UAClC2B,OAAO,EAAEA,CAAA,KAAMzD,YAAY,CAAC,KAAK,CAAE;UAAA+B,QAAA,EACtC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN5C,OAAA,CAACF,aAAa;MACVwE,KAAK,EAAC,2BAAqB;MAC3BC,IAAI,EAAExD,iBAAkB;MACxByD,OAAO,EAAExD,oBAAqB;MAC9ByD,SAAS,EAAEhD,eAAgB;MAAAe,QAAA,gBAE3BxC,OAAA;QAAAwC,QAAA,EAAG;MAA8D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EACpE3B,sBAAsB,iBACnBjB,OAAA;QAAKoE,KAAK,EAAE;UAAEM,SAAS,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAArC,QAAA,gBAChGxC,OAAA;UAAAwC,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC3B,sBAAsB,CAAC0C,IAAI,eAAC3D,OAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5D5C,OAAA;UAAAwC,QAAA,EAAQ;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC3B,sBAAsB,CAAC2C,QAAQ;MAAA;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC,eAGhB5C,OAAA,CAACF,aAAa;MACVwE,KAAK,EAAC,gCAAgC;MACtCC,IAAI,EAAEpD,wBAAyB;MAC/BqD,OAAO,EAAEpD,2BAA4B;MACrCqD,SAAS,EAAE9C,mBAAoB;MAAAa,QAAA,gBAE/BxC,OAAA;QAAAwC,QAAA,GAAG,gCAA8B,eAAAxC,OAAA;UAAAwC,QAAA,GAAQ,WAAS,EAACpC,qBAAqB,CAACwB,MAAM,EAAC,iBAAe;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,qBAAiB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC7H5C,OAAA;QAAKoE,KAAK,EAAE;UAAEM,SAAS,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAoB,CAAE;QAAAtC,QAAA,gBAC7HxC,OAAA;UAAAwC,QAAA,EAAQ;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gGAChC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEd,CAAC;AAAClC,EAAA,CA7OIP,gBAAgB;EAAA,QAQDR,WAAW;AAAA;AAAAoF,EAAA,GAR1B5E,gBAAgB;AA+OtB,eAAeA,gBAAgB;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}