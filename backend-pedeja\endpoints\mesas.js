const CreateMesasUseCase = require('../application/mesas/useCases/createMesa');
const PaymentMesaUseCase = require('../application/mesas/useCases/paymentMesa');
const MesasDAO = require('../DAOS/MesasDAO');
const Mesas = require('../models/Mesas');
const middlewareAuth = require('../lib/middlewareAuth');
const GetMesasUseCase = require('../application/mesas/useCases/getMesas');

const bcrypt = require('bcrypt')
const jwt = require('jsonwebtoken')

const AppExpressPedeja = require('../AppExpressPedeja');
const RegisterPedidoMesaUseCase = require('../application/mesas/useCases/registerPedidoMesa');
const PedidosDAO = require('../DAOS/PedidosDAO');
const CounterDAO = require('../DAOS/CounterDAO');
const ClienteDAO = require('../DAOS/ClienteDAO');
const EmpresaDAO = require('../DAOS/EmpresaDAO');
const UserDAO = require('../DAOS/UserDAO');
const User = require('../models/User');
const Empresa = require('../models/Empresa');
const Cliente = require('../models/Cliente');
const Counter = require('../models/Counter');
const Pedidos = require('../models/Pedidos');
const TransferMesaUseCase = require('../application/mesas/useCases/transferMesa');
const CreateManyMesasUseCase = require('../application/mesas/useCases/createManyMesas');
const DeleteMesaUseCase = require('../application/mesas/useCases/deleteMesa');
const GeneratePrintUseCase = require('../application/mesas/useCases/generatePrint');
const io = AppExpressPedeja.AppExpressPedeja.getSocket();

const router = require('express').Router();

router.post("/garcom/login", async (req, res) => {
    const { email, password } = req.body

    //validations
    if (!email) {
        return res.status(422).json({ msg: 'O email é obrigatório!' })
    }

    if (!password) {
        return res.status(422).json({ msg: 'A senha é obrigatória!' })
    }

    //Checar se usuario existe
    const user = await User.findOne({ email: email })

    if (!user) {
        return res.status(404).json({ msg: 'Usuário não encontrado!' })
    }

    //Check if password match
    const checkPassword = await bcrypt.compare(password, user.password)

    if (!checkPassword) {
        return res.status(422).json({ msg: 'Senha inválida!' })
    }

    //TESTE
    const userSS = await User.findOne({ email: email }, 'name email vinculo_empresa role user_img bloqueado inativo')
    //const userSS = await User.findOne({ email: email},'name email vinculo_empresa user_img')

    const empresa = await Empresa.findOne({ "cnpj": userSS.vinculo_empresa })

    if (userSS.bloqueado || userSS.inativo) {
        return res.status(401).json({ msg: 'Usuário bloqueado/inativo ou sem permissão!' })
    }
    //const userSS = await User.findById(user.id, '-password')
    //FIM TESTE

    try {

        const secret = process.env.SECRET

        const token = jwt.sign({
            id: user.id,
            email: user.email,
            vinculo_empresa: user.vinculo_empresa,
            empresa_id: empresa.id,
        },
            secret,
        )

        return res.status(200).json({ msg: 'Autenticação realizada com sucesso!', user: userSS, empresa, token })

    } catch (error) {

        console.log(error)

        res
            .status(500)
            .json({
                msg: 'Aconteceu um erro no servidor, tente novamente mais tarde!',
            })
    }
})

router.get("/mesas", middlewareAuth, async (req, res) => {

    const { vinculo_empresa } = req.context.currentUser;

    try {
        if(!vinculo_empresa){
            return res.status(400).json({message: "Empresa não informada."})
        }

        const mesaDAO = new MesasDAO(Mesas);
        const empresaDAO = new EmpresaDAO(Empresa);
        const getMesasUseCase = new GetMesasUseCase(mesaDAO, empresaDAO);

        const result = await getMesasUseCase.execute(vinculo_empresa);

        return res.json(result)

    } catch(error){
        console.error(error);
        return res.status(500);
    }
    
})

router.post("/mesas", middlewareAuth, async (req, res) => {
    const { qr_code, name } = req.body

    const { empresa_id, id: user_id, vinculo_empresa } = req.context.currentUser;

    try {
        if(!empresa_id){
            return res.status(400).json({message: "Empresa não informada."})
        }

        if(!name){
            return res.status(400).json({message: "Empresa não informada."})
        }

        const mesaDAO = new MesasDAO(Mesas);
        const empresaDAO = new EmpresaDAO(Empresa);
        const createMesaUseCase = new CreateMesasUseCase(mesaDAO, empresaDAO);

        const result = await createMesaUseCase.execute({ qr_code, vinculo_empresa, name });

        return res.json(result)

    } catch(error){
        console.error(error);
        return res.status(500);
    }
})

router.post("/many_mesas", middlewareAuth, async (req, res) => {
    const { url, quantity } = req.body

    const { empresa_id } = req.context.currentUser;

    try {
        if(!empresa_id){
            return res.status(400).json({message: "Usuário sem empresa vinculada."})
        }

        if(!quantity){
            return res.status(400).json({message: "Quantidade não informada."})
        }

        if(!url){
            return res.status(400).json({message: "URL não informada."})
        }

        const mesaDAO = new MesasDAO(Mesas);
        const createMesaUseCase = new CreateManyMesasUseCase(mesaDAO);

        const result = await createMesaUseCase.execute({ empresa_id, url, quantity });

        return res.json(result)

    } catch(error){
        console.error(error);
        return res.status(500);
    }
})

router.post('/mesas/registerPedido', middlewareAuth, async (req, res) => {
    try {
        const {
            itens,
            celular_cliente,
            nome_cliente,
            valor_total,
            descricao,
            mesa_id,
        } = req.body
    
        const { id: user_id, vinculo_empresa } = req.context.currentUser;
    
        //validacoes
    
        if(!itens){
            return res.status(422).json({msg: 'O Item é obrigatório!'})
        }
    
        const userDAO = new UserDAO(User);
        const mesaDAO = new MesasDAO(Mesas);
        const empresaDAO = new EmpresaDAO(Empresa);
        const clienteDAO = new ClienteDAO(Cliente);
        const counterDAO = new CounterDAO(Counter);
        const pedidosDAO = new PedidosDAO(Pedidos);
    
        const registerPedidoMesaUseCase = new RegisterPedidoMesaUseCase(userDAO, mesaDAO, empresaDAO, clienteDAO, counterDAO, pedidosDAO, io);
    
        const result = await registerPedidoMesaUseCase.execute({ id_empresa: vinculo_empresa, user_id, itens, celular_cliente, nome_cliente, valor_total, descricao, mesaId: mesa_id });

        if(result.status === false){
            return res.status(400).json(result)
        }
    
        return res.status(201).json(result)

    } catch(error){
        console.log('error on controller', error);
        res.status(500).send();
    }
});

router.post('/mesas/payment', middlewareAuth, async (req, res) => {
    try {
        const {
            payment_type,
            total_payed,
            mesa_id,
            customer_name,
            customer_phone
        } = req.body
    
        const { empresa_id, id: user_id } = req.context.currentUser;
    
        //validacoes

        if(!mesa_id){
            return res.status(422).json({msg: 'Mesa não informada!'})
        }
    
        if(!payment_type){
            return res.status(422).json({msg: 'O método de pagamento não foi informado!'})
        }
    
        if(!total_payed){
            return res.status(422).json({msg: 'O valor pago não foi informado!'})
        }
    
    
        const mesaDAO = new MesasDAO(Mesas);
        const pedidosDAO = new PedidosDAO(Pedidos);
    
        const paymentMesaUseCase = new PaymentMesaUseCase(mesaDAO, pedidosDAO);
    
        const result = await paymentMesaUseCase.execute({ payment_type, total_payed, mesa_id, id_empresa: empresa_id, user_id, customer_name, customer_phone });

        if(result.status === false){
            return res.status(400).json(result)
        }
    
        return res.status(201).json(result)

    } catch(error){
        console.log('error on controller', error);
        res.status(500).send();
    }
});

router.post('/mesas/transfer', middlewareAuth, async (req, res) => {
    try {
        const {
            current_mesa_id,
            next_mesa_id
        } = req.body
    
        const { empresa_id, id: user_id } = req.context.currentUser;
    
        //validacoes

        if(!current_mesa_id){
            return res.status(422).json({msg: 'Mesa não informada!'})
        }

        if(!next_mesa_id){
            return res.status(422).json({msg: 'Mesa de destino não informada!'})
        }
    
        const mesaDAO = new MesasDAO(Mesas);
    
        const transferMesaUseCase = new TransferMesaUseCase(mesaDAO);
    
        const result = await transferMesaUseCase.execute({ current_mesa_id, next_mesa_id, id_empresa: empresa_id });

        if(result.status === false){
            return res.status(400).json(result)
        }
    
        return res.status(201).json(result)

    } catch(error){
        console.log('error on controller', error);
        res.status(500).send();
    }
});

router.delete('/mesas/:id', middlewareAuth, async (req, res) => {
    try {
        const { id: mesa_id } = req.params;

        if(!mesa_id){
            return res.status(422).json({msg: 'Mesa não informada!'})
        }
    
        const mesaDAO = new MesasDAO(Mesas);
    
        const deleteMesaUseCase = new DeleteMesaUseCase(mesaDAO);
    
        const result = await deleteMesaUseCase.execute({ mesa_id });

        if(result.status === false){
            return res.status(400).json(result)
        }
    
        return res.status(201).json(result)

    } catch(error){
        console.log('error on controller', error);
        res.status(500).send();
    }
});

router.post("/command-print/mesa/:mesaId", middlewareAuth, async (req, res) => {
    
    console.log('cheguei aqui?')

    const { vinculo_empresa } = req.context.currentUser;
    const mesaId  = req.params.mesaId

    if(!mesaId){
        return res.status(400).json({
            status: false,
            msg: 'Mesa não informada.'
        }) 
    }

    const mesaDAO = new MesasDAO(Mesas);
    const empresaDAO = new EmpresaDAO(Empresa);
    const generatePrintUseCase = new GeneratePrintUseCase(mesaDAO, empresaDAO);

    const result = await generatePrintUseCase.execute({ vinculo_empresa, mesa_id: mesaId });

    if(result.status === false){
        return res.status(400).json(result)
    }

    return res.status(201).json(result)
});



module.exports = router;