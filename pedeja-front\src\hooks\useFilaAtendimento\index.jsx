import { useState, useEffect, useRef, useCallback } from 'react';
import { toast } from 'react-toastify';
import { 
  buscarFilaAtendimento as apiBuscarFila,
  iniciarAtendimentoFila,
  finalizarAtendimentoFila,
  cancelarAtendimentoFila,
  limparAtendimentosFinalizados as apiLimparFinalizados
} from '../../services/api';

/**
 * Hook customizado para gerenciar fila de atendimento humano
 * Combina WebSocket (tempo real) + polling inteligente (confiabilidade)
 * 
 * 🔌 Quando socket conectado: Usa principalmente WebSocket + polling raro (10min)
 * 📡 Quando socket desconectado: Polling frequente de backup (2min)
 * 
 * 🚀 ESTRATÉGIA DE ATUALIZAÇÃO INSTANTÂNEA:
 * - Socket recebe atendimento → Adiciona temporariamente (feedback visual)
 * - Após 300ms → Busca fila completa (IDs reais do banco)
 * - Usuário clica resolver → Já tem dados atualizados, sem espera frustrante!
 * 
 * ⚠️ FALLBACK PARA IDs TEMPORÁRIOS:
 * - Se ainda existir ID temporário, busca automaticamente o real
 * - Operações sempre tentam resolver automaticamente
 */
const useFilaAtendimento = (empresaId, socket) => {
  const [atendimentosPendentes, setAtendimentosPendentes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Ref para evitar chamadas desnecessárias
  const lastFetchRef = useRef(Date.now());
  const intervalRef = useRef(null);

  /**
   * 🔍 VERIFICAR SE É ID TEMPORÁRIO
   */
  const isTemporaryId = useCallback((id) => {
    return typeof id === 'string' && id.startsWith('temp-');
  }, []);

  /**
   * 🔍 BUSCAR FILA DE ATENDIMENTO DO BANCO DE DADOS
   */
  const buscarFilaAtendimento = useCallback(async () => {
    if (!empresaId) return;

    try {
      setIsLoading(true);
      setError(null);

      console.log('🔍 Buscando fila de atendimento do banco para empresa:', empresaId);
      
      const response = await apiBuscarFila(empresaId);
      
      if (response.data.status === 200) {
        const { atendimentos, total_pendentes } = response.data.data;
        
        console.log('✅ Fila de atendimento obtida do banco:', {
          total_pendentes,
          atendimentos: atendimentos.length
        });

        // Converter para formato compatível com o frontend existente
        const atendimentosFormatados = atendimentos.map(item => ({
          ...item,
          atendimento_id: item._id || item.atendimento_id,
          company_id: empresaId
        }));

        setAtendimentosPendentes(atendimentosFormatados);
        lastFetchRef.current = Date.now();
        
        return atendimentosFormatados;
      }
    } catch (err) {
      console.error('❌ Erro ao buscar fila de atendimento:', err);
      setError(err.message || 'Erro ao carregar fila de atendimento');
      
      // Toast apenas em casos críticos, não em atualizações periódicas
      if (atendimentosPendentes.length === 0) {
        toast('Erro ao carregar fila de atendimento', { type: 'error' });
      }
    } finally {
      setIsLoading(false);
    }
  }, [empresaId]);

  /**
   * 🔄 INICIAR ATENDIMENTO
   */
  const iniciarAtendimento = useCallback(async (atendimentoId, userData) => {
    try {
      console.log('🔄 Iniciando atendimento:', atendimentoId);
      
      // ⚠️ VALIDAÇÃO: Se ainda for ID temporário, buscar fila uma última vez
      if (isTemporaryId(atendimentoId)) {
        console.log('⚠️ ID temporário detectado, buscando fila atualizada...');
        await buscarFilaAtendimento();
        
        // Encontrar o atendimento real na lista atualizada
        const atendimentoReal = atendimentosPendentes.find(item => 
          !isTemporaryId(item.atendimento_id) && 
          (item.lead_id === userData?.lead_id || item.celular === userData?.celular)
        );
        
        if (atendimentoReal) {
          console.log('✅ Encontrado atendimento real:', atendimentoReal.atendimento_id);
          // Recursivamente chamar com o ID real
          return iniciarAtendimento(atendimentoReal.atendimento_id, userData);
        } else {
          toast('Atendimento não encontrado, tente novamente', { type: 'error' });
          return false;
        }
      }
      
      const response = await iniciarAtendimentoFila(empresaId, atendimentoId, userData);

      if (response.data.status === 200) {
        // Remover da lista de pendentes
        setAtendimentosPendentes(prev => 
          prev.filter(item => item.atendimento_id !== atendimentoId)
        );
        
        toast('Atendimento iniciado!', { type: 'success' });
        return true;
      }
    } catch (err) {
      console.error('❌ Erro ao iniciar atendimento:', err);
      toast('Erro ao iniciar atendimento', { type: 'error' });
      return false;
    }
  }, [empresaId, buscarFilaAtendimento, atendimentosPendentes, isTemporaryId]);

  /**
   * ✅ FINALIZAR ATENDIMENTO
   */
  const finalizarAtendimento = useCallback(async (atendimentoId, observacoes = '') => {
    try {
      console.log('✅ Finalizando atendimento:', atendimentoId);
      
      // ⚠️ VALIDAÇÃO: Se ainda for ID temporário, buscar fila uma última vez
      if (isTemporaryId(atendimentoId)) {
        console.log('⚠️ ID temporário detectado, buscando fila atualizada...');
        await buscarFilaAtendimento();
        
        // Para finalizar, tentar encontrar pelo ID temporário ou buscar o primeiro disponível
        const atendimentoReal = atendimentosPendentes.find(item => 
          !isTemporaryId(item.atendimento_id)
        );
        
        if (atendimentoReal) {
          console.log('✅ Usando primeiro atendimento real encontrado:', atendimentoReal.atendimento_id);
          // Recursivamente chamar com o ID real
          return finalizarAtendimento(atendimentoReal.atendimento_id, observacoes);
        } else {
          toast('Atendimento não encontrado, tente novamente', { type: 'error' });
          return false;
        }
      }
      
      const response = await finalizarAtendimentoFila(empresaId, atendimentoId, observacoes);

      if (response.data.status === 200) {
        // Remover da lista de pendentes
        setAtendimentosPendentes(prev => 
          prev.filter(item => item.atendimento_id !== atendimentoId)
        );
        
        toast('Atendimento finalizado!', { type: 'success' });
        return true;
      }
    } catch (err) {
      console.error('❌ Erro ao finalizar atendimento:', err);
      toast('Erro ao finalizar atendimento', { type: 'error' });
      return false;
    }
  }, [empresaId, buscarFilaAtendimento, atendimentosPendentes, isTemporaryId]);

  /**
   * ❌ CANCELAR ATENDIMENTO
   */
  const cancelarAtendimento = useCallback(async (atendimentoId, motivo = '', showToast = true) => {
    try {
      console.log('❌ Cancelando atendimento:', atendimentoId);
      
      // ⚠️ VALIDAÇÃO: Verificar se é ID temporário
      if (isTemporaryId(atendimentoId)) {
        console.log('⚠️ ID temporário detectado, removendo da lista local apenas');
        
        // Para IDs temporários, apenas remove da lista local
        setAtendimentosPendentes(prev => 
          prev.filter(item => item.atendimento_id !== atendimentoId)
        );
        
        if (showToast) {
          toast('Atendimento removido', { type: 'info' });
        }
        return true;
      }
      
      const response = await cancelarAtendimentoFila(empresaId, atendimentoId, motivo);

      if (response.data.status === 200) {
        // Remover da lista de pendentes
        setAtendimentosPendentes(prev => 
          prev.filter(item => item.atendimento_id !== atendimentoId)
        );
        
        // Só mostra toast se solicitado (padrão é mostrar)
        if (showToast) {
          toast('Atendimento cancelado', { type: 'info' });
        }
        return true;
      }
    } catch (err) {
      console.error('❌ Erro ao cancelar atendimento:', err);
      // Toast de erro sempre mostra, independente do parâmetro
      if (showToast) {
        toast('Erro ao cancelar atendimento', { type: 'error' });
      }
      return false;
    }
  }, [empresaId]);

  /**
   * 🧹 LIMPAR ATENDIMENTOS FINALIZADOS (limpeza periódica)
   */
  const limparAtendimentosFinalizados = useCallback(async () => {
    try {
      console.log('🧹 Limpando atendimentos finalizados...');
      
      await apiLimparFinalizados(empresaId);
      console.log('✅ Limpeza realizada com sucesso');
    } catch (err) {
      console.error('⚠️ Erro na limpeza (não crítico):', err);
    }
  }, [empresaId]);

  /**
   * 🚀 CANCELAR TODOS OS ATENDIMENTOS (operação em lote)
   */
  const cancelarTodosAtendimentos = useCallback(async () => {
    const totalAtendimentos = atendimentosPendentes.length;
    
    if (totalAtendimentos === 0) {
      return { sucesso: true, total: 0, cancelados: 0 };
    }

    try {
      console.log(`🚀 Cancelando ${totalAtendimentos} atendimentos em lote...`);
      
      // Cancelar todos sem mostrar toast individual
      const promessas = atendimentosPendentes.map(atendimento => 
        cancelarAtendimento(atendimento.atendimento_id, 'Resolvido em lote pelo usuário', false)
      );
      
      const resultados = await Promise.allSettled(promessas);
      
      // Contar sucessos e falhas
      const cancelados = resultados.filter(r => r.status === 'fulfilled' && r.value === true).length;
      const falharam = totalAtendimentos - cancelados;
      
      console.log(`✅ Resultado do lote: ${cancelados} cancelados, ${falharam} falharam`);
      
      return { 
        sucesso: falharam === 0, 
        total: totalAtendimentos, 
        cancelados, 
        falharam 
      };
      
    } catch (err) {
      console.error('❌ Erro na operação em lote:', err);
      return { 
        sucesso: false, 
        total: totalAtendimentos, 
        cancelados: 0, 
        falharam: totalAtendimentos,
        erro: err.message 
      };
    }
  }, [atendimentosPendentes, cancelarAtendimento]);

  /**
   * 🔄 ATUALIZAR FILA (manual)
   */
  const atualizarFila = useCallback(() => {
    buscarFilaAtendimento();
  }, [buscarFilaAtendimento]);

  /**
   * ➕ ADICIONAR ATENDIMENTO VIA SOCKET (TEMPORÁRIO)
   * Adiciona imediatamente para feedback visual, mas será substituído pela busca completa
   */
  const adicionarAtendimentoSocket = useCallback((novoAtendimento) => {
    console.log('📨 Adicionando atendimento temporário via socket:', novoAtendimento);
    
    setAtendimentosPendentes(prev => {
      // Verificar se já existe para evitar duplicados
      const jaExiste = prev.some(item => 
        item.lead_id === novoAtendimento.lead_id ||
        item.atendimento_id === novoAtendimento.atendimento_id ||
        item._id === novoAtendimento._id ||
        (item.celular && novoAtendimento.celular && item.celular === novoAtendimento.celular)
      );
      
      if (jaExiste) {
        console.log('⚠️ Atendimento já existe, ignorando socket');
        return prev;
      }
      
      // Priorizar _id do MongoDB, depois atendimento_id, só criar temp como último recurso
      const atendimentoId = novoAtendimento._id || 
                           novoAtendimento.atendimento_id || 
                           `temp-${Date.now()}`;
      
              console.log('📨 ID do atendimento socket (temporário):', { 
          _id: novoAtendimento._id, 
          atendimento_id: novoAtendimento.atendimento_id,
          final: atendimentoId,
          nota: 'Será substituído pela busca completa em 300ms'
        });
      
      return [...prev, {
        ...novoAtendimento,
        atendimento_id: atendimentoId,
        _isTemporary: !novoAtendimento._id // Marcar como temporário se não tem _id real
      }];
    });
  }, []);

  /**
   * 🚀 INICIALIZAÇÃO E CONFIGURAÇÃO DOS LISTENERS
   */
  useEffect(() => {
    if (!empresaId) return;

    // Buscar fila inicial do banco
    buscarFilaAtendimento();

    // Configurar polling inteligente baseado no status do socket
    intervalRef.current = setInterval(() => {
      const isSocketConnected = socket && socket.connected;
      
      if (!isSocketConnected) {
        // Socket desconectado: polling mais frequente (2 minutos)
        console.log('🔄 Polling de backup (socket desconectado)...');
        buscarFilaAtendimento();
      } else {
        // Socket conectado: polling muito raro apenas como failsafe (10 minutos)
        if (Date.now() - lastFetchRef.current > 600000) { // 10 minutos
          console.log('🔄 Polling de failsafe (socket conectado mas sem atualizações há muito tempo)...');
          buscarFilaAtendimento();
        }
      }
    }, 120000); // Verificar a cada 2 minutos

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [empresaId, socket, buscarFilaAtendimento]);

  /**
   * 📡 CONFIGURAR LISTENERS DO SOCKET
   */
  useEffect(() => {
    if (!socket) return;

    console.log('📡 Configurando listeners de atendimento no socket...');

    // Listener para novos atendimentos
    const handleAtendimentoPendente = (data) => {
      console.log('🚨 Novo atendimento via socket:', data);
      
      // Adicionar temporariamente para feedback visual imediato
      adicionarAtendimentoSocket(data);
      
      // 🚀 IMEDIATAMENTE buscar fila atualizada para obter dados reais
      console.log('🔄 Atualizando fila automaticamente após socket...');
      setTimeout(() => {
        buscarFilaAtendimento();
      }, 300); // Delay pequeno para garantir que o backend processou
    };

    // Listener para atendimento iniciado
    const handleAtendimentoIniciado = (data) => {
      console.log('🔄 Atendimento iniciado via socket:', data);
      setAtendimentosPendentes(prev =>
        prev.filter(item => item.atendimento_id !== data.atendimento_id)
      );
    };

    // Listener para atendimento finalizado
    const handleAtendimentoFinalizado = (data) => {
      console.log('✅ Atendimento finalizado via socket:', data);
      setAtendimentosPendentes(prev =>
        prev.filter(item => item.atendimento_id !== data.atendimento_id)
      );
    };

    // Listener para atendimento cancelado
    const handleAtendimentoCancelado = (data) => {
      console.log('❌ Atendimento cancelado via socket:', data);
      setAtendimentosPendentes(prev =>
        prev.filter(item => item.atendimento_id !== data.atendimento_id)
      );
    };

    // Registrar listeners
    socket.on('atendimento_pendente', handleAtendimentoPendente);
    socket.on('atendimento_iniciado', handleAtendimentoIniciado);
    socket.on('atendimento_finalizado', handleAtendimentoFinalizado);
    socket.on('atendimento_cancelado', handleAtendimentoCancelado);

    // Cleanup
    return () => {
      socket.off('atendimento_pendente', handleAtendimentoPendente);
      socket.off('atendimento_iniciado', handleAtendimentoIniciado);
      socket.off('atendimento_finalizado', handleAtendimentoFinalizado);
      socket.off('atendimento_cancelado', handleAtendimentoCancelado);
    };
  }, [socket, adicionarAtendimentoSocket, buscarFilaAtendimento]);

  /**
   * 🧹 LIMPEZA AUTOMÁTICA REMOVIDA (evitar sobrecarga no servidor)
   * Limpeza será manual via botão ou endpoint específico
   */
  // useEffect(() => {
  //   const limpezaInterval = setInterval(() => {
  //     limparAtendimentosFinalizados();
  //   }, 60 * 60 * 1000); // 1 hora
  //   return () => clearInterval(limpezaInterval);
  // }, [limparAtendimentosFinalizados]);

  return {
    // Estados
    atendimentosPendentes,
    isLoading,
    error,
    totalPendentes: atendimentosPendentes.length,
    
    // Ações
    iniciarAtendimento,
    finalizarAtendimento,
    cancelarAtendimento,
    cancelarTodosAtendimentos,
    atualizarFila,
    limparAtendimentosFinalizados,
    
    // Para compatibilidade com o código existente
    removerAtendimento: cancelarAtendimento,
  };
};

export default useFilaAtendimento; 