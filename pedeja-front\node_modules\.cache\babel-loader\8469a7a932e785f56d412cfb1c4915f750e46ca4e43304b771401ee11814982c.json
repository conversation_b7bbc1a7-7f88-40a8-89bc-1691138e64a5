{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\pages\\\\Whatsapp\\\\whatsapp.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useContext, useCallback } from \"react\";\nimport { useRef } from \"react\";\nimport { useLocation } from \"react-router-dom\";\nimport LeftMenu from \"../../components/LeftMenu\";\n//import { MenuProfile } from \"../../contexts/MenuProfileContext\";\nimport './style.css';\nimport styled from \"styled-components\";\nimport Profile from \"../../components/Profile\";\nimport Chat from \"../../components/Chat\";\nimport io from 'socket.io-client';\nimport mockMessages from \"./mock.json\";\nimport mockOptions from \"./mockOptions.json\";\nimport contactUserPhoto from \"../../assets/img/contact-user-photo.png\";\nimport backgroundChat from \"../../assets/img/chat-background.png\";\nimport SearchBar from \"../../components/SearchBar\";\nimport * as S from \"./stylesProfile\";\nimport UserProfile from \"./UserProfile\"; // Importa o modal\nimport { SidebarContext } from \"../../AppRoutes\";\nimport { MenuProfileContext } from \"../../contexts/MenuProfileContext\";\nimport { getQrCodeWhatsapp, renewQrCodeWhatsapp, getWhatsappChats, getWhatsappChatLead, sendMessage, sendTextMessageWhatsapp, removeWhatsappSession, toggleBotStatus, getBotStatusLead, getWhatsappProfilePicture, getMyWhatsappProfilePicture, getWhatsappUnreadCount, markWhatsappMessagesAsRead, getWhatsappConnectionStatus, refreshContactInfoWhatsapp, getWhatsappChatById, createWhatsappChat } from \"../../services/api\";\nimport Loading from \"react-loading\";\nimport CryptoJS from 'crypto-js';\nimport moment from 'moment-timezone';\nimport 'moment/locale/pt-br'; // Adiciona a localidade em português\nimport useImagePreloader from '../../hooks/useImagePreloader';\n//import ChatMenu from \"../Menu\";\nimport userIcon from \"../../assets/img/user-icon.png\";\nimport { ReactComponent as CheckIcon } from \"../../assets/svg/check-icon.svg\";\nimport { ReactComponent as StatusIcon } from \"../../assets/svg/status-icon.svg\";\nimport { ReactComponent as ChatIcon } from \"../../assets/svg/chat-icon.svg\";\nimport { ReactComponent as MenuIcon } from \"../../assets/svg/menu-icon.svg\";\nimport { ReactComponent as BackIcon } from \"../../assets/svg/arrow-back-icon.svg\";\nimport { FaWhatsapp } from \"react-icons/fa\";\nimport { FiSend, FiPauseCircle, FiPlayCircle } from \"react-icons/fi\"; // Importando o ícone de envio\nimport { toast } from 'react-toastify';\nimport Input from \"../../components/Input/index\";\nimport UnreadBadge from \"../../components/UnreadBadge\";\nimport GlobalStyles, { Container } from \"../../styles/global\";\n\n// Hook para detectar mobile\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction useIsMobile() {\n  _s();\n  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);\n  React.useEffect(() => {\n    const onResize = () => setIsMobile(window.innerWidth <= 768);\n    window.addEventListener('resize', onResize);\n    return () => window.removeEventListener('resize', onResize);\n  }, []);\n  return isMobile;\n}\n_s(useIsMobile, \"IPgBv7VuYiSHCPyFLng5ZxH+1OA=\");\nexport const WrapperContainerAll = styled.div`\n  display: flex;\n  height: 100%;\n  \n  @media (max-width: 768px) {\n    flex-direction: column;\n    height: 100vh;\n  }\n`;\n_c = WrapperContainerAll;\nexport const WrapperContainerConversa = styled.div`\n  width: 100%;\n  background-size: cover;\n  background-repeat: no-repeat;\n  background-position: center;\n  background-attachment: fixed;\n  background-color: #e5ddd5; /* Cor de fallback com padrão do WhatsApp */\n  background-image: ${props => props.backgroundLoaded ? `url(${backgroundChat})` : `linear-gradient(45deg, \n      #e5ddd5 0%, \n      #f0f0f0 25%, \n      #e5ddd5 50%, \n      #f0f0f0 75%, \n      #e5ddd5 100%)`}; /* Padrão sutil enquanto carrega */\n  transition: background-image 0.3s ease-in-out; /* Transição suave quando a imagem carrega */\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  height: calc(100vh - 120px);\n  \n  @media (max-width: 768px) {\n    height: 100%;\n    background-attachment: scroll; /* Melhor performance no mobile */\n    position: relative;\n    top: auto;\n    left: ${props => props.isMobile && props.showMobileChat ? '0' : 'auto'};\n    z-index: auto;\n    display: ${props => props.isMobile && !props.showMobileChat ? 'none' : 'flex'};\n  }\n  \n  /* Indicador sutil de carregamento se a imagem não carregou */\n  ${props => !props.backgroundLoaded && `\n    &::after {\n      content: '📸 Carregando background...';\n      position: absolute;\n      bottom: 20px;\n      right: 20px;\n      background: rgba(255, 255, 255, 0.9);\n      padding: 8px 12px;\n      border-radius: 20px;\n      font-size: 12px;\n      color: #666;\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n      z-index: 1000;\n      animation: pulse 2s infinite;\n    }\n    \n    @keyframes pulse {\n      0%, 100% { opacity: 0.7; }\n      50% { opacity: 1; }\n    }\n  `}\n`;\n_c2 = WrapperContainerConversa;\nconst WrapperContent = styled.div`\n  padding: 1rem 4rem;\n  height: 100%;\n  overflow-y: scroll;\n  margin-bottom: 60px;\n  background-color: transparent; /* Remove background duplicado */\n  \n  @media (max-width: 768px) {\n    padding: 1rem 1rem;\n    margin-bottom: 70px;\n  }\n`;\n_c3 = WrapperContent;\nconst ChatDate = styled.time`\n  display: block;\n  background: #e1f3fb;\n  padding: 10px;\n  border-radius: 7.5px;\n  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);\n  font-size: 1.42rem;\n  margin: 10px auto;\n  width: fit-content;\n  text-transform: uppercase;\n  \n  @media (max-width: 768px) {\n    font-size: 1.2rem;\n    padding: 8px;\n  }\n`;\n_c4 = ChatDate;\nconst Message = styled.div`\n  background: ${props => props.fromMe ? '#dcf8c6' : '#ffffff'};\n  padding: 10px;\n  border-radius: 7.5px;\n  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);\n  font-size: 1.42rem;\n  margin: 1px 0px 1px ${props => props.fromMe ? 'auto' : '0px'};\n  width: fit-content;\n  max-width: 60%;\n  margin-bottom: ${props => props.fromMe ? '3px' : '10px'};\n  white-space: pre-wrap;\n  word-break: break-word; /* Adicionado para quebra de palavras longas */\n  span {\n    font-size: 1.1rem;\n    color: rgba(0, 0, 0, 0.45);\n    display: block;\n    text-align: right;\n  }\n  \n  @media (max-width: 768px) {\n    font-size: 1.3rem;\n    max-width: 80%;\n    padding: 8px;\n    \n    span {\n      font-size: 1rem;\n    }\n  }\n`;\n_c5 = Message;\nexport const Warn = styled.div`\n  background: #fdf4c4;\n  width: fit-content;\n  margin: 30px auto;\n  padding: 5px 10px;\n  font-size: 1.6rem;\n  border-radius: 5px;\n  \n  @media (max-width: 768px) {\n    font-size: 1.4rem;\n    margin: 20px auto;\n    padding: 8px 12px;\n  }\n`;\n_c6 = Warn;\nexport const WrapperHMC = styled.aside`\n  position: relative;\n  width: 55rem;\n  height: 100%;\n  background: #fff;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n\n  @media (max-width: 1200px) {\n    width: 45rem;\n  }\n  \n  @media (max-width: 768px) {\n    width: 100%;\n    height: 100%;\n    position: relative;\n    left: ${props => props.isMobile && props.showMobileChat ? '-100%' : '0'};\n    transition: left 0.3s ease;\n    z-index: auto;\n    display: ${props => props.isMobile && props.showMobileChat ? 'none' : 'flex'};\n  }\n`;\n_c7 = WrapperHMC;\nexport const WrapperMenu = styled.div`\n  position: absolute;\n  z-index: 10;\n  top: 60px;\n  right: 20px;\n  background: #fff;\n  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);\n  border-radius: 2px;\n\n  transition: transform 0.1s ease;\n  transform-origin: right top;\n  transform: scale(0);\n\n  &.active {\n    transform: scale(1);\n  }\n`;\n_c8 = WrapperMenu;\nexport const ActionWrapperMenu = styled.ul`\n  padding: 10px 0px;\n`;\n_c9 = ActionWrapperMenu;\nexport const ActionMenu = styled.li`\n  padding: 10px 20px;\n  cursor: pointer;\n  font-size: 1.4rem;\n  color: #333;\n  transition: background 0.2s ease-in-out;\n\n  &:hover {\n    background: #f5f5f5;\n  }\n\n  &.disabled {\n    cursor: not-allowed;\n    opacity: 0.3;\n\n    &:hover {\n      background: none;\n    }\n  }\n`;\n_c0 = ActionMenu;\nexport const Header = styled.header`\n  display: flex;\n  align-items: center;\n\n  padding: 13px;\n  background: #ededed;\n  \n  @media (max-width: 768px) {\n    padding: 15px;\n    position: relative;\n  }\n`;\n_c1 = Header;\nexport const ContactPhoto = styled.div`\n  margin-right: 15px;\n\n  img {\n    border-radius: 100%;\n  }\n  \n  @media (max-width: 768px) {\n    margin-right: 12px;\n  }\n`;\n_c10 = ContactPhoto;\nexport const ContactName = styled.span`\n  font-size: 1.7rem;\n  \n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n  }\n`;\n_c11 = ContactName;\nexport const MobileBackButton = styled.button`\n  display: none;\n  \n  @media (max-width: 768px) {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background: transparent;\n    border: none;\n    padding: 8px;\n    margin-right: 10px;\n    cursor: pointer;\n    color: #333;\n    font-size: 1.8rem;\n  }\n`;\n_c12 = MobileBackButton;\nexport const WrapperChat = styled.div`\n  padding: 10px;\n  margin-top: 15px;\n  background: #f0f0f0;\n  position: absolute;\n  bottom: 0;\n  width: 100%;\n  \n  @media (max-width: 768px) {\n    padding: 15px;\n    margin-top: 0;\n    \n    form {\n      gap: 8px;\n    }\n  }\n`;\n_c13 = WrapperChat;\nexport const WrapperListContacts = styled.div`\n  display: flex;\n  align-items: center;\n  background: ${props => props.isSelected ? '#eaeaea' : 'white'};\n  cursor: pointer;\n  padding: 10px;  /* Adicionado padding para espaçamento */\n  border-bottom: 1px solid #eee;\n  &:hover {\n    background: #f4f4f4;\n  }\n  \n  @media (max-width: 768px) {\n    padding: 15px 10px;\n    \n    &:active {\n      background: #e0e0e0;\n    }\n  }\n`;\n_c14 = WrapperListContacts;\nexport const ContactPhotoListContacts = styled.div`\n  flex: 0 0 60px;  /* Define a largura fixa da área da foto */\n  padding: 10px;\n\n  img {\n    border-radius: 100%;\n    width: 100%;  /* Garante que a imagem ocupe a largura total do contêiner */\n  }\n  \n  @media (max-width: 768px) {\n    flex: 0 0 50px;\n    padding: 8px;\n  }\n`;\n_c15 = ContactPhotoListContacts;\nexport const ContactNameAndTime = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n\n  span {\n    font-size: 14px;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    max-width: 70%;\n  }\n\n  p {\n    font-size: 1.2rem;\n    color: rgba(0, 0, 0, 0.45);\n    margin-left: 10px;  /* Adiciona espaço entre o nome e a data */\n    flex-shrink: 0;  /* Garante que a data não encolha */\n  }\n  \n  @media (max-width: 768px) {\n    span {\n      font-size: 16px;\n      max-width: 65%;\n    }\n    \n    p {\n      font-size: 1.1rem;\n    }\n  }\n`;\n_c16 = ContactNameAndTime;\nexport const ContactMessage = styled.div`\n  display: flex;\n  align-items: center;\n  width: 100%;\n  svg {\n    margin-right: 5px;\n  }\n\n  P {\n    font-size: 12px;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    max-width: 100%;  /* Ajusta a largura máxima para evitar overflow */\n  }\n  \n  @media (max-width: 768px) {\n    P {\n      font-size: 14px;\n    }\n  }\n`;\n_c17 = ContactMessage;\nexport const MessageDataWrapper = styled.div`\n  flex: 1;\n  padding-right: 15px;\n  overflow: hidden;  /* Garante que o conteúdo não extrapole os limites */\n  display: flex;\n  flex-direction: column;\n  \n  @media (max-width: 768px) {\n    padding-right: 10px;\n  }\n`;\n_c18 = MessageDataWrapper;\nexport const WrapperListHeader = styled.div`\n  border-right: 1px solid rgba(0, 0, 0, 0.1);\n  \n  @media (max-width: 768px) {\n    border-right: none;\n  }\n`;\n_c19 = WrapperListHeader;\nexport const HeaderListHeader = styled.header`\n  background-color: #ededed;\n  width: 100%;\n  padding: 15px;\n\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  \n  @media (max-width: 768px) {\n    padding: 12px 15px;\n  }\n`;\n_c20 = HeaderListHeader;\nexport const UserImage = styled.div`\n  margin-right: auto;\n  cursor: pointer;\n\n  img {\n    border-radius: 100%;\n  }\n  \n  @media (max-width: 768px) {\n    img {\n      width: 35px;\n      height: 35px;\n    }\n  }\n`;\n_c21 = UserImage;\nexport const Actions = styled.div`\n  display: flex;\n  align-items: center;\n\n  svg {\n    width: 50px;\n    cursor: pointer;\n\n    &:not(:last-child) {\n      cursor: not-allowed;\n      opacity: 0.3;\n    }\n  }\n  \n  @media (max-width: 768px) {\n    svg {\n      width: 40px;\n    }\n  }\n`;\n_c22 = Actions;\nconst Teste = styled.div`\n  display: flex;\n  margin-left:  ${({\n  sidebar\n}) => sidebar ? '250px' : '100px'}; \n  height:auto;\n  width:auto;\n  transition: 150ms;\n  background-color:rgb(247,247,247)!important;\n  --background-color:white!important;\n  overflow: initial;\n  z-Index: 9;\n  \n  @media (max-width: 768px) {\n    margin-left: 0;\n    height: calc(100vh - 60px); /* Deixa espaço para o navbar */\n    width: 100vw;\n    position: relative; /* Muda de fixed para relative */\n    top: auto;\n    left: 0;\n    z-index: 1;\n    margin-top: 0; /* Remove margem superior */\n  }\n`;\n\n// Helper Function to Group Messages by Date\n_c23 = Teste;\nconst groupMessagesByDate = messages => {\n  return messages.reduce((acc, message) => {\n    const date = moment(message.createdAt).format('YYYY-MM-DD');\n    if (!acc[date]) {\n      acc[date] = [];\n    }\n    acc[date].push(message);\n    return acc;\n  }, {});\n};\n\n// Função para gerar ObjectId aleatório\nconst generateObjectId = () => {\n  return Math.floor(Date.now() / 1000).toString(16) + 'xxxxxxxxxxxxxxxx'.replace(/[x]/g, function () {\n    return Math.floor(Math.random() * 16).toString(16);\n  }).toLowerCase();\n};\nconst WhatsAppWeb = () => {\n  _s2();\n  var _location$state, _selectedChat$channel, _selectedChat$channel2;\n  moment.locale('pt-br'); // Define o locale para português\n  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\n  const userEncrypted = localStorage.getItem('user');\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\n  const userParse = JSON.parse(user);\n  const userID = userParse._id;\n  const empresa = localStorage.getItem('empresa');\n  const empresaParse = JSON.parse(empresa);\n  const empresaID = empresaParse._id;\n  const token = localStorage.getItem('token');\n  const location = useLocation(); // Captura o state passado pelo navigate\n  const leadIdParaAbrir = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.lead_id) || null; // Verifica se recebeu um lead_id\n\n  const isMobile = useIsMobile();\n  const [showMobileChat, setShowMobileChat] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  //const { openMenu } = useContext(MenuProfileContext);\n  const {\n    isOpen,\n    openMenu,\n    closeMenu\n  } = useContext(MenuProfileContext);\n  //const openMenu = () => setIsMenuOpen(true);\n  const menuRef = useRef(null);\n  const [message, setMessage] = useState(\"\");\n  const {\n    sidebar,\n    setSidebar\n  } = useContext(SidebarContext);\n  ; //TESTANDO CONST NO AppROUTES\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  // 📱 Estados do QR Code - MELHORADO\n  const [qrCodeImg, setQrCodeImg] = useState('');\n  const [generatingQrCode, setGeneratingQrCode] = useState(false);\n  const [isLoged, setIsLoged] = useState(true); // Inicializar como true para melhor UX (evita flash do QR Code)\n  const [qrCodeExpired, setQrCodeExpired] = useState(false);\n  const [qrCodeTimeLeft, setQrCodeTimeLeft] = useState(0);\n  const [userRequestedQrCode, setUserRequestedQrCode] = useState(false); // 🔧 NOVO: Controla se usuário solicitou QR Code\n  const [showRegenerateButton, setShowRegenerateButton] = useState(false);\n  const [expectingQrCode, setExpectingQrCode] = useState(false); // Flag para controlar quando esperamos QR Code\n  //const [socket, setSocket] = useState(null);\n  const [chats, setChats] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedChat, setSelectedChat] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [messagesLeadChannel, setMessagesLeadChannel] = useState([]);\n  const timeoutRef = useRef(null); // UseRef to store the timeout ID\n  const isMountedRef = useRef(true); // Para saber se o componente está montado\n  const cancelQrCodeFetchRef = useRef(false); // Flag para cancelar a execução do fetch\n  // 📱 Refs para QR Code melhorado\n  const qrCodeTimerRef = useRef(null);\n  const countdownTimerRef = useRef(null);\n  const [botPausado, setBotPausado] = useState(false);\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n  const [messagesCache, setMessagesCache] = useState(new Map());\n  const [loadingMessages, setLoadingMessages] = useState(false);\n\n  // **📌 ESTADOS PARA PAGINAÇÃO DE MENSAGENS**\n  const [messagesPage, setMessagesPage] = useState(0);\n  const [hasMoreMessages, setHasMoreMessages] = useState(true);\n  const [loadingMoreMessages, setLoadingMoreMessages] = useState(false);\n  const messagesContainerRef = useRef(null);\n  const [preloadingChats, setPreloadingChats] = useState(false);\n  const [preloadProgress, setPreloadProgress] = useState({\n    current: 0,\n    total: 0\n  });\n  const [hasPreloaded, setHasPreloaded] = useState(false);\n  const [raceConditionsFixed, setRaceConditionsFixed] = useState(0);\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\n  const fetchingChatsRef = useRef(false);\n\n  // **📌 ESTADO PARA IMAGEM DE PERFIL DO PRÓPRIO USUÁRIO**\n  const [myProfilePicture, setMyProfilePicture] = useState(null);\n\n  // **📌 ESTADO PARA CONTROLAR SE DEVE ROLAR PARA O BOTTOM**\n  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);\n\n  // **📌 ESTADO PARA CONTROLAR SE ACABOU DE ABRIR UMA CONVERSA**\n  const [justOpenedChat, setJustOpenedChat] = useState(false);\n  const justOpenedTimerRef = useRef(null);\n\n  // **📌 ESTADO PARA CONTAGEM DE MENSAGENS NÃO LIDAS**\n  const [unreadCounts, setUnreadCounts] = useState({});\n\n  // **📌 PRELOAD DA IMAGEM DE BACKGROUND USANDO HOOK CUSTOMIZADO**\n  const {\n    loaded: backgroundLoaded,\n    error: backgroundError,\n    progress: backgroundProgress\n  } = useImagePreloader(backgroundChat, 3000);\n\n  // **📌 DEBUG: LOG DE ERROS DO BACKGROUND**\n  useEffect(() => {\n    if (backgroundError) {\n      console.error('🚨 [BACKGROUND ERROR]:', backgroundError);\n      console.log('📊 [BACKGROUND DEBUG]:', {\n        backgroundLoaded,\n        backgroundProgress: `${backgroundProgress}%`,\n        backgroundChat: (backgroundChat === null || backgroundChat === void 0 ? void 0 : backgroundChat.substring(0, 50)) + '...',\n        isDevelopment: process.env.NODE_ENV === 'development',\n        baseURL: window.location.origin\n      });\n\n      // Tentar carregar a imagem diretamente no console para debug\n      console.log('🔍 [DEBUG] Testando carregamento direto da imagem:');\n      const testImg = new Image();\n      testImg.onload = () => console.log('✅ [DEBUG] Imagem carrega OK diretamente');\n      testImg.onerror = e => console.error('❌ [DEBUG] Imagem falha ao carregar diretamente:', e);\n      testImg.src = backgroundChat;\n    } else if (backgroundLoaded && backgroundProgress === 100) {\n      console.log('🎉 [BACKGROUND] Carregamento concluído com sucesso!');\n      // Toast sutil apenas no desenvolvimento para debug\n      /*if (process.env.NODE_ENV === 'development') {\r\n        setTimeout(() => {\r\n          toast.success('🎨 Background do chat carregado', {\r\n            position: \"bottom-right\",\r\n            autoClose: 2000,\r\n            hideProgressBar: true,\r\n            closeOnClick: true,\r\n            pauseOnHover: false,\r\n            draggable: false,\r\n          });\r\n        }, 500);\r\n      }*/\n    }\n  }, [backgroundError, backgroundLoaded, backgroundProgress]);\n\n  // Função para navegar para o chat no mobile\n  const handleSelectChatMobile = chat => {\n    setSelectedChat(chat);\n    if (isMobile) {\n      setShowMobileChat(true);\n    }\n\n    // **📌 MARCAR MENSAGENS COMO LIDAS AO SELECIONAR CHAT**\n    if (unreadCounts[chat._id] && unreadCounts[chat._id] > 0) {\n      markChatAsRead(chat._id);\n    }\n  };\n\n  // Função para voltar para a lista no mobile\n  const handleBackToList = () => {\n    if (isMobile) {\n      setShowMobileChat(false);\n      setSelectedChat(null);\n    }\n  };\n\n  // Função para abrir perfil apenas quando clicar na foto\n  const handleOpenProfile = e => {\n    e.stopPropagation(); // Previne propagação do evento\n    setIsProfileOpen(true);\n  };\n\n  // Resetar estado mobile quando a tela mudar de tamanho\n  useEffect(() => {\n    if (!isMobile) {\n      setShowMobileChat(false);\n    }\n  }, [isMobile]);\n\n  // Sort messages by date\n  const sortedMessages = [...messagesLeadChannel].sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));\n  const groupedMessages = groupMessagesByDate(sortedMessages);\n  const messageEndRef = useRef(null);\n  useEffect(() => {\n    // **📌 SÓ ROLAR PARA O BOTTOM SE NÃO ESTIVER CARREGANDO MENSAGENS ANTIGAS**\n    if (messageEndRef.current && !loadingMoreMessages && shouldScrollToBottom) {\n      // **📌 GARANTIR SCROLL INSTANTÂNEO PARA O BOTTOM, ESPECIALMENTE QUANDO ABRE CONVERSA**\n      const container = messagesContainerRef.current;\n      if (container && messagesLeadChannel.length > 0) {\n        // Forçar scroll para o bottom imediatamente\n        container.scrollTop = container.scrollHeight;\n\n        // Backup com scrollIntoView para garantir\n        setTimeout(() => {\n          if (messageEndRef.current && shouldScrollToBottom) {\n            messageEndRef.current.scrollIntoView({\n              behavior: \"instant\"\n            });\n          }\n        }, 50);\n      }\n    }\n  }, [messagesLeadChannel, loadingMoreMessages, shouldScrollToBottom]);\n  const handleClickOutside = event => {\n    if (menuRef.current && !menuRef.current.contains(event.target)) setIsMenuOpen(false);\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!message.trim() || isSubmitting) return;\n    if (!selectedChat || !selectedChat.mobile_number) {\n      console.error('Nenhum chat selecionado ou número inválido');\n      return;\n    }\n    const messageToSend = message.trim();\n\n    // **📌 ENVIO OTIMISTA: Adicionar mensagem imediatamente**\n    const optimisticMessage = {\n      _id: `temp_${Date.now()}`,\n      text: messageToSend,\n      fromMe: true,\n      createdAt: new Date().toISOString(),\n      messageDate: new Date().toISOString(),\n      leadChannel: selectedChat._id,\n      isOptimistic: true,\n      // Flag para identificar mensagens otimistas\n      status: 'sending'\n    };\n\n    // Adicionar mensagem otimista na conversa\n    setMessagesLeadChannel(oldMessages => [optimisticMessage, ...oldMessages]);\n\n    // **📌 PARAR LOADING JÁ QUE TEMOS MENSAGEM OTIMISTA**\n    setLoadingMessages(false);\n\n    // **📌 GARANTIR QUE O SCROLL PERMANEÇA NO BOTTOM APÓS MENSAGEM OTIMISTA**\n    setTimeout(() => {\n      const container = messagesContainerRef.current;\n      if (container) {\n        container.scrollTop = container.scrollHeight;\n      }\n    }, 50);\n\n    // Atualizar cache das mensagens\n    setMessagesCache(prevCache => {\n      const newCache = new Map(prevCache);\n      const cachedMessages = newCache.get(selectedChat._id) || [];\n      newCache.set(selectedChat._id, [optimisticMessage, ...cachedMessages]);\n      return newCache;\n    });\n\n    // Atualizar a lista de chats com a mensagem otimista\n    setChats(oldChats => {\n      const updatedChats = oldChats.map(chat => {\n        if (chat._id === selectedChat._id) {\n          return {\n            ...chat,\n            message: {\n              text: messageToSend,\n              message: messageToSend,\n              messageDate: new Date().toISOString(),\n              createdAt: new Date().toISOString(),\n              fromMe: true\n            },\n            updatedAt: new Date().toISOString()\n          };\n        }\n        return chat;\n      });\n\n      // Mover o chat atualizado para o topo da lista\n      const chatIndex = updatedChats.findIndex(chat => chat._id === selectedChat._id);\n      if (chatIndex > 0) {\n        const [chatToMove] = updatedChats.splice(chatIndex, 1);\n        updatedChats.unshift(chatToMove);\n      }\n      return updatedChats;\n    });\n    try {\n      setIsSubmitting(true);\n      setMessage(''); // Limpa o campo de mensagem antes do envio\n\n      const response = await sendTextMessageWhatsapp(empresaID, selectedChat.mobile_number, messageToSend);\n\n      // Atualizar status da mensagem otimista para 'sent'\n      setMessagesLeadChannel(oldMessages => oldMessages.map(msg => msg._id === optimisticMessage._id ? {\n        ...msg,\n        status: 'sent'\n      } : msg));\n\n      // A mensagem real será adicionada automaticamente via webhook quando for processada\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response3$data;\n      console.error('❌ Erro ao enviar a mensagem:', error);\n      console.error('❌ Detalhes do erro:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n      console.error('❌ Status:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status);\n\n      // Remover mensagem otimista em caso de erro\n      setMessagesLeadChannel(oldMessages => oldMessages.filter(msg => msg._id !== optimisticMessage._id));\n\n      // Remover do cache também\n      setMessagesCache(prevCache => {\n        const newCache = new Map(prevCache);\n        const cachedMessages = newCache.get(selectedChat._id) || [];\n        newCache.set(selectedChat._id, cachedMessages.filter(msg => msg._id !== optimisticMessage._id));\n        return newCache;\n      });\n\n      // Restaurar a mensagem em caso de erro\n      setMessage(messageToSend);\n\n      // Mostrar toast de erro com detalhes\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.error) || 'Erro ao enviar mensagem. Tente novamente.';\n      toast.error(errorMessage);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const isDevelopment = window.location.hostname === 'localhost';\n  const apiUrl = isDevelopment ? process.env.REACT_APP_SERVER_URL_DEV : process.env.REACT_APP_SERVER_URL_PROD;\n\n  // **📌 SOCKET PRINCIPAL PARA WHATSAPP**\n  const [mainSocket, setMainSocket] = useState(null);\n\n  // **📌 FUNÇÃO PARA MARCAR MENSAGENS COMO LIDAS**\n  const markChatAsRead = useCallback(async chatId => {\n    try {\n      console.log(`📖 Marcando mensagens como lidas para chat: ${chatId}`);\n      const response = await markWhatsappMessagesAsRead(empresaID, chatId, token);\n      if (response.data && response.data.status === 200) {\n        console.log(`✅ Mensagens marcadas como lidas com sucesso para chat: ${chatId}`);\n\n        // Atualizar contagem local\n        setUnreadCounts(prevCounts => {\n          const newCounts = {\n            ...prevCounts\n          };\n          delete newCounts[chatId]; // Remove a contagem para este chat\n          return newCounts;\n        });\n\n        // Atualizar contagem geral\n        try {\n          const response = await getWhatsappUnreadCount(empresaID, token);\n          if (response.data && response.data.status === 200) {\n            setUnreadCounts(response.data.unreadCounts);\n          }\n        } catch (error) {\n          console.error('❌ Erro ao buscar contagem de mensagens não lidas:', error);\n        }\n      } else {\n        console.log(`⚠️ Resposta inesperada ao marcar como lidas:`, response.data);\n      }\n    } catch (error) {\n      console.error(`❌ Erro ao marcar mensagens como lidas para chat ${chatId}:`, error);\n    }\n  }, [empresaID, token]);\n\n  // 📱 FUNÇÃO MELHORADA PARA GERENCIAR QR CODE COM TIMER DE 45 SEGUNDOS (ALINHADO COM BACKEND)\n  const startQrCodeTimer = (initialTtl = 45) => {\n    // Limpar timers existentes\n    if (qrCodeTimerRef.current) {\n      clearTimeout(qrCodeTimerRef.current);\n    }\n    if (countdownTimerRef.current) {\n      clearInterval(countdownTimerRef.current);\n    }\n\n    // Resetar estados\n    setQrCodeExpired(false);\n    setShowRegenerateButton(false);\n    setQrCodeTimeLeft(initialTtl); // TTL dinâmico (padrão 45 segundos)\n\n    console.log(`⏱️ Iniciando timer de QR Code: ${initialTtl} segundos`);\n\n    // Timer de countdown (atualiza a cada segundo)\n    countdownTimerRef.current = setInterval(() => {\n      setQrCodeTimeLeft(prev => {\n        if (prev <= 1) {\n          // QR Code expirou\n          setQrCodeExpired(true);\n          setShowRegenerateButton(true);\n          clearInterval(countdownTimerRef.current);\n          console.log(`⏰ QR Code expirou após ${initialTtl} segundos`);\n\n          // 🔧 REGENERAÇÃO AUTOMÁTICA DESABILITADA - Usuário deve clicar manualmente\n          // setTimeout(() => {\n          //   if (!isLoged && isMountedRef.current) {\n          //     console.log('🔄 Regenerando QR Code automaticamente via nova rota...');\n          //     regenerateQrCodeViaAPI('automatic_expiry');\n          //   }\n          // }, 2000); // Delay de 2 segundos antes de regenerar\n\n          return 0;\n        }\n        return prev - 1;\n      });\n    }, 1000);\n\n    // Timer principal baseado no TTL\n    qrCodeTimerRef.current = setTimeout(() => {\n      setQrCodeExpired(true);\n      setShowRegenerateButton(true);\n      clearInterval(countdownTimerRef.current);\n    }, initialTtl * 1000);\n  };\n\n  // 📱 NOVA FUNÇÃO PARA GERAR QR CODE MANUALMENTE (PRIMEIRA VEZ)\n  const generateQrCodeManually = async () => {\n    console.log('🔄 Usuário solicitou geração manual do QR Code...');\n    setUserRequestedQrCode(true);\n    await fetchQrCode('manual_user_request');\n  };\n\n  // 📱 NOVA FUNÇÃO PARA REGENERAR QR CODE VIA API (BACKEND MELHORADO)\n  const regenerateQrCodeViaAPI = async (source = 'manual') => {\n    console.log(`🔄 Regenerando QR Code via nova API (${source})...`);\n\n    // Verificar se componente ainda está montado\n    if (!isMountedRef.current) {\n      console.log('🛑 Componente desmontado, cancelando regeneração');\n      return;\n    }\n\n    // Limpar timers existentes\n    if (qrCodeTimerRef.current) {\n      clearTimeout(qrCodeTimerRef.current);\n    }\n    if (countdownTimerRef.current) {\n      clearInterval(countdownTimerRef.current);\n    }\n    setQrCodeExpired(false);\n    setShowRegenerateButton(false);\n    setGeneratingQrCode(true);\n    try {\n      // 🚀 USAR NOVA FUNÇÃO DE API PARA RENOVAÇÃO\n      const response = await renewQrCodeWhatsapp(empresaID, token);\n      const data = response.data;\n      if (!isMountedRef.current) return;\n      if (data.status === 200) {\n        if (data.isConnected) {\n          // Já conectado\n          console.log('✅ WhatsApp já está conectado!');\n          setIsLoged(true);\n          setGeneratingQrCode(false);\n          setQrCodeImg('');\n          clearQrCodeTimers();\n          return;\n        }\n        if (data.qrcode && data.qrcode.qr) {\n          // QR Code renovado com sucesso\n          console.log('✅ QR Code renovado via API:', data.qrcode);\n          setQrCodeImg(data.qrcode.qr);\n          setGeneratingQrCode(false);\n\n          // Usar TTL do backend se disponível\n          const ttl = data.qrcode.ttl ? Math.max(1, data.qrcode.ttl - Math.floor(Date.now() / 1000)) : 45;\n          startQrCodeTimer(ttl);\n          return;\n        }\n      }\n\n      // Se chegou aqui, algo deu errado\n      console.warn('⚠️ Resposta inesperada da API de renovação:', data);\n      throw new Error(data.error || 'Erro desconhecido na renovação');\n    } catch (error) {\n      console.error(`❌ Erro ao regenerar QR Code via API (${source}):`, error);\n      if (!isMountedRef.current) return;\n      setGeneratingQrCode(false);\n      setShowRegenerateButton(true);\n\n      // Fallback: tentar método antigo\n      console.log('🔄 Tentando método de fallback...');\n      setTimeout(() => {\n        if (!isLoged && isMountedRef.current) {\n          regenerateQrCodeFallback(source);\n        }\n      }, 2000);\n    }\n  };\n\n  // 📱 FUNÇÃO DE FALLBACK PARA REGENERAR QR CODE (MÉTODO ANTIGO)\n  const regenerateQrCodeFallback = async (source = 'manual') => {\n    console.log(`🔄 Regenerando QR Code via fallback (${source})...`);\n\n    // Verificar se componente ainda está montado\n    if (!isMountedRef.current) {\n      console.log('🛑 Componente desmontado, cancelando regeneração');\n      return;\n    }\n    setQrCodeExpired(false);\n    setShowRegenerateButton(false);\n    setGeneratingQrCode(true);\n    try {\n      await fetchQrCode(`fallback_${source}`);\n    } catch (error) {\n      console.error(`❌ Erro ao regenerar QR Code via fallback (${source}):`, error);\n      setGeneratingQrCode(false);\n      setShowRegenerateButton(true);\n    }\n  };\n\n  // 📱 FUNÇÃO PARA LIMPAR TIMERS DO QR CODE\n  const clearQrCodeTimers = () => {\n    if (qrCodeTimerRef.current) {\n      clearTimeout(qrCodeTimerRef.current);\n      qrCodeTimerRef.current = null;\n    }\n    if (countdownTimerRef.current) {\n      clearInterval(countdownTimerRef.current);\n      countdownTimerRef.current = null;\n    }\n  };\n\n  // 🔌 FUNÇÃO PARA LIMPAR SOCKET GLOBAL (usar apenas em logout/erro crítico)\n  const cleanupGlobalSocket = () => {\n    if (window.whatsappSocket) {\n      console.log(\"🛑 Limpando socket WhatsApp global...\");\n      window.whatsappSocket.removeAllListeners();\n      window.whatsappSocket.disconnect();\n      window.whatsappSocket = null;\n    }\n  };\n\n  // **📌 CRIAR SOCKET GLOBAL QUE PERSISTE ENTRE NAVEGAÇÕES**\n  useEffect(() => {\n    const socketNamespace = \"/whatsapp\";\n    const wsUrl = apiUrl.replace(/\\/$/, '') + socketNamespace;\n\n    // Verificar se já existe um socket global conectado\n    if (window.whatsappSocket && window.whatsappSocket.connected) {\n      console.log(\"✅ Reutilizando socket WhatsApp existente\");\n      setMainSocket(window.whatsappSocket);\n      return;\n    }\n    console.log(\"🔌 Criando novo socket WhatsApp global\");\n    const socket = io(wsUrl, {\n      withCredentials: true,\n      transports: ['websocket'],\n      auth: {\n        token: localStorage.getItem('token')\n      },\n      reconnection: true,\n      reconnectionAttempts: 10,\n      reconnectionDelay: 5000\n    });\n\n    // Armazenar socket globalmente para reutilização\n    window.whatsappSocket = socket;\n    setMainSocket(socket);\n    return () => {\n      console.log(\"🛑 Desmontando componente WhatsApp - mantendo socket conectado...\");\n      // ❌ NÃO DESCONECTAR O SOCKET - apenas limpar listeners específicos\n      // O socket deve permanecer conectado para outras telas\n      setMainSocket(null);\n    };\n  }, []);\n\n  // **📌 CONFIGURAR EVENTOS DE CHATS NO SOCKET PRINCIPAL**\n  useEffect(() => {\n    if (!mainSocket) return;\n\n    // Conectar ao namespace WhatsApp\n    mainSocket.on('connect', () => {\n      console.log(\"✅ Socket WhatsApp conectado\");\n      // **📌 IMPORTANTE: O join na sala é automático no backend quando o socket conecta**\n      const roomName = `chats:${empresaID}`;\n      console.log(`🏠 Socket automaticamente na sala: ${roomName}`);\n    });\n    mainSocket.on('disconnect', reason => {\n      console.log(\"❌ Socket WhatsApp desconectado:\", reason);\n    });\n    mainSocket.on('connect_error', error => {\n      console.error(\"🚨 Erro de conexão socket WhatsApp:\", error);\n    });\n    mainSocket.on('chats', newChat => {\n      console.log(\"📨 Novo chat recebido:\", newChat.name);\n      setChats(oldChats => {\n        // Remove o chat existente se já estiver na lista e adiciona o novo no início\n        const filteredChats = oldChats.filter(chat => chat._id !== newChat._id);\n        return [newChat, ...filteredChats];\n      });\n\n      // **📌 ATUALIZAR CACHE SE A MENSAGEM FOR DE UMA CONVERSA ATIVA**\n      if (newChat.message && !newChat.message.fromMe) {\n        setMessagesCache(prevCache => {\n          const newCache = new Map(prevCache);\n          const cachedMessages = newCache.get(newChat._id) || [];\n\n          // Criar objeto de mensagem para o cache\n          const messageForCache = {\n            _id: newChat.message.id || `msg_${Date.now()}`,\n            text: newChat.message.text || newChat.message.message,\n            fromMe: newChat.message.fromMe,\n            createdAt: newChat.message.createdAt || newChat.message.messageDate,\n            messageDate: newChat.message.messageDate || newChat.message.createdAt,\n            leadChannel: newChat._id\n          };\n\n          // Verificar se a mensagem já existe no cache\n          const messageExists = cachedMessages.some(msg => msg.text === messageForCache.text && msg.fromMe === messageForCache.fromMe && Math.abs(new Date(msg.createdAt) - new Date(messageForCache.createdAt)) < 5000 // 5 segundos de tolerância\n          );\n          if (!messageExists) {\n            newCache.set(newChat._id, [messageForCache, ...cachedMessages]);\n          }\n          return newCache;\n        });\n\n        // **📌 ATUALIZAR CONTAGEM DE MENSAGENS NÃO LIDAS EM TEMPO REAL**\n        if (!selectedChat || selectedChat._id !== newChat._id) {\n          // Se o chat não está selecionado, incrementar contador\n          setUnreadCounts(prevCounts => ({\n            ...prevCounts,\n            [newChat._id]: (prevCounts[newChat._id] || 0) + 1\n          }));\n        } else {\n          // **📌 SE O CHAT ESTÁ ABERTO, MARCAR MENSAGEM COMO LIDA AUTOMATICAMENTE**\n          console.log(`📖 Chat ${newChat.name} está aberto, marcando nova mensagem como lida automaticamente`);\n          setTimeout(() => {\n            markChatAsRead(newChat._id);\n          }, 1000); // Delay de 1 segundo para garantir que a mensagem foi salva no banco\n        }\n      }\n      console.log(\"✅ Lista de chats atualizada com nova mensagem de:\", newChat.name);\n    });\n\n    // 📱 LISTENER MELHORADO PARA QR CODE UPDATES VIA SOCKET - COM FILTRO POR EMPRESA\n    mainSocket.on('whatsapp_qrcode_update', data => {\n      console.log('📱 [SOCKET] QR Code atualizado via socket:', {\n        instanceId: data.instanceId,\n        empresaID: data.empresaID,\n        empresaName: data.empresaName,\n        hasQrCode: !!data.qrcode,\n        renewed: data.renewed,\n        timestamp: data.timestamp\n      });\n      console.log('📊 [SOCKET] Estado atual:', {\n        qrCodeImg: !!qrCodeImg,\n        qrCodeExpired,\n        generatingQrCode,\n        isLoged,\n        empresaAtual: empresaID\n      });\n\n      // **🎯 FILTRO CRÍTICO: IGNORAR QR CODES DE OUTRAS EMPRESAS**\n      if (data.empresaID && data.empresaID !== empresaID) {\n        console.log(`🚫 [SOCKET] QR Code ignorado - empresa diferente! Recebido: ${data.empresaName} (${data.empresaID}), Atual: ${empresaID}`);\n        return;\n      }\n\n      // 🚫 IGNORAR QR CODES AUTOMÁTICOS SE JÁ ESTAMOS CONECTADOS\n      if (isLoged) {\n        console.log('🚫 [SOCKET] Ignorando QR Code - já conectado (isLoged=true)');\n        return;\n      }\n\n      // 🔧 NOVO: IGNORAR QR CODES SE USUÁRIO NÃO SOLICITOU\n      if (!userRequestedQrCode) {\n        console.log('🚫 [SOCKET] Ignorando QR Code - usuário não solicitou geração');\n        return;\n      }\n      if (data.qrcode && data.instanceId) {\n        // 🔧 MELHOR VALIDAÇÃO DO QR CODE RECEBIDO VIA SOCKET\n        const qrData = data.qrcode;\n        let validQrCode = null;\n        if (typeof qrData === 'string' && qrData.startsWith('data:image/')) {\n          validQrCode = qrData;\n        } else if (qrData.qr && typeof qrData.qr === 'string' && qrData.qr.startsWith('data:image/')) {\n          validQrCode = qrData.qr;\n        } else if (qrData.base64 && typeof qrData.base64 === 'string' && qrData.base64.startsWith('data:image/')) {\n          validQrCode = qrData.base64;\n        }\n        if (validQrCode) {\n          console.log('✅ [SOCKET] QR Code válido recebido via socket');\n          setQrCodeImg(validQrCode);\n          setGeneratingQrCode(false);\n\n          // Usar TTL do backend se disponível, senão usar 45 segundos\n          let ttl = 45;\n          if (qrData.ttl) {\n            ttl = Math.max(1, qrData.ttl - Math.floor(Date.now() / 1000));\n          }\n\n          // Reiniciar timer com TTL correto\n          startQrCodeTimer(ttl);\n\n          // Mostrar toast se foi renovado\n          if (data.renewed) {\n            console.log('🔄 QR Code renovado automaticamente via socket');\n          }\n        } else {\n          console.error('❌ [SOCKET] QR Code inválido recebido via socket:', JSON.stringify(qrData, null, 2));\n        }\n      }\n    });\n\n    // 📱 LISTENER MELHORADO PARA CONNECTION UPDATES VIA SOCKET - COM FILTRO POR EMPRESA\n    mainSocket.on('whatsapp_connection_update', data => {\n      console.log('🔗 [SOCKET] Status de conexão atualizado:', {\n        instanceId: data.instanceId,\n        empresaID: data.empresaID,\n        state: data.state,\n        isConnected: data.isConnected,\n        statusCode: data.statusCode,\n        timestamp: data.timestamp\n      });\n\n      // **🎯 FILTRO CRÍTICO: IGNORAR CONNECTION UPDATES DE OUTRAS EMPRESAS**\n      if (data.empresaID && data.empresaID !== empresaID) {\n        console.log(`🚫 [SOCKET] Connection update ignorado - empresa diferente! Recebido: ${data.empresaID}, Atual: ${empresaID}`);\n        return;\n      }\n      if (data.isConnected || data.state === 'open') {\n        console.log('✅ [SOCKET] WhatsApp conectado via connection update!');\n        setIsLoged(true);\n        setGeneratingQrCode(false);\n        setQrCodeImg('');\n        clearQrCodeTimers();\n      }\n    });\n\n    // 📱 NOVO LISTENER PARA EVENTO DE CONEXÃO ESTABELECIDA - COM FILTRO POR EMPRESA\n    mainSocket.on('whatsapp_connected', data => {\n      console.log('🎉 [SOCKET] WhatsApp conectado com sucesso!', {\n        instanceId: data.instanceId,\n        empresaID: data.empresaID,\n        message: data.message,\n        timestamp: data.timestamp\n      });\n\n      // **🎯 FILTRO CRÍTICO: IGNORAR CONEXÕES DE OUTRAS EMPRESAS**\n      if (data.empresaID && data.empresaID !== empresaID) {\n        console.log(`🚫 [SOCKET] Conexão ignorada - empresa diferente! Recebido: ${data.empresaID}, Atual: ${empresaID}`);\n        return;\n      }\n\n      // Atualizar interface imediatamente\n      setIsLoged(true);\n      setGeneratingQrCode(false);\n      setQrCodeImg('');\n      clearQrCodeTimers();\n\n      // Mostrar toast de sucesso\n      /*toast.success('🎉 WhatsApp conectado com sucesso!', {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n        hideProgressBar: false,\r\n        closeOnClick: true,\r\n        pauseOnHover: true,\r\n        draggable: true,\r\n      });*/\n\n      // Recarregar chats apenas se não foi o carregamento inicial\n      setTimeout(() => {\n        console.log('🔄 Verificando se precisa recarregar chats após conexão...');\n        if (!isInitialLoad && chats.length > 0) {\n          console.log('♻️ Recarregando chats após reconexão...');\n          setChats([]);\n          setPage(0);\n          setHasMoreChats(true);\n          fetchChats(0, false, 'socket_reconnection');\n        } else {\n          console.log('✅ Carregamento inicial ainda em andamento, não duplicando...');\n        }\n      }, 1000);\n    });\n    return () => {\n      console.log(\"🛑 Limpando eventos específicos do componente WhatsApp...\");\n      // ✅ LIMPEZA ESPECÍFICA - não afetar outros componentes que usam o socket\n      if (mainSocket) {\n        mainSocket.off('chats');\n        mainSocket.off('connect');\n        mainSocket.off('disconnect');\n        mainSocket.off('connect_error');\n        mainSocket.off('reconnect');\n        mainSocket.off('whatsapp_qrcode_update');\n        mainSocket.off('whatsapp_connection_update');\n        mainSocket.off('whatsapp_connected'); // 📱 NOVO EVENTO\n\n        console.log(\"✅ Eventos específicos do WhatsApp removidos - socket mantido para outras telas\");\n      }\n    };\n  }, [mainSocket]); // Remover dependências desnecessárias que causam re-execução\n\n  useEffect(() => {\n    document.addEventListener(\"click\", handleClickOutside);\n    return () => document.removeEventListener(\"click\", handleClickOutside);\n  });\n  useEffect(() => {\n    // Defina isMounted como true ao montar o componente\n    isMountedRef.current = true;\n    cancelQrCodeFetchRef.current = false; // Redefine o cancelamento ao montar o componente\n\n    // **📌 VERIFICAR STATUS REAL DA EVOLUTION API PRIMEIRO**\n    console.log('🚀 Iniciando verificação de status REAL da Evolution API...');\n\n    // Função assíncrona para verificar status e decidir o que fazer\n    const initializeWhatsApp = async () => {\n      try {\n        // Primeiro verificar se realmente está conectado\n        const isConnected = await checkConnectionStatus('component_mount');\n        if (!isConnected) {\n          // Se não estiver conectado, NÃO gerar QR code automaticamente\n          console.log('📱 Não conectado, aguardando comando manual do usuário para gerar QR Code...');\n          // 🔧 REMOVIDO: await fetchQrCode('component_mount_disconnected');\n        } else {\n          console.log('✅ Já conectado, mantendo interface de chat');\n        }\n      } catch (error) {\n        console.error('❌ Erro na inicialização do WhatsApp:', error);\n        // Em caso de erro, NÃO gerar QR code automaticamente\n        console.log('⚠️ Erro na verificação, aguardando comando manual do usuário...');\n        // 🔧 REMOVIDO: await fetchQrCode('component_mount_error');\n      }\n    };\n    initializeWhatsApp();\n\n    // Função de limpeza ao desmontar o componente\n    return () => {\n      isMountedRef.current = false; // Define como false quando o componente desmonta\n      cancelQrCodeFetchRef.current = true; // Cancela qualquer execução futura de fetchQrCode\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n        console.log('🛑 Timeout cleared on unmount');\n      }\n      // **📌 LIMPAR TIMER DE CONVERSA RECÉM ABERTA**\n      if (justOpenedTimerRef.current) {\n        clearTimeout(justOpenedTimerRef.current);\n        console.log('🛑 justOpenedTimer cleared on unmount');\n      }\n      // 📱 Limpar timers do QR Code\n      clearQrCodeTimers();\n      console.log('🛑 QR Code timers cleared on unmount');\n    };\n  }, []);\n\n  // **📌 FUNÇÃO PARA VERIFICAR STATUS REAL DA EVOLUTION API**\n  const checkConnectionStatus = async (source = 'unknown') => {\n    if (!isMountedRef.current) return false;\n    try {\n      console.log(`🔍 [CHECK STATUS] Verificando status REAL da Evolution API... (fonte: ${source})`);\n      const response = await getWhatsappConnectionStatus(empresaID);\n      console.log('📡 [CHECK STATUS] Resposta da Evolution API:', response.data);\n      const isConnected = response.data.isConnected;\n      const connectionState = response.data.connectionState;\n\n      // ✅ SEMPRE USAR STATUS REAL DA EVOLUTION API (NUNCA DO BANCO)\n      if (isConnected && connectionState === 'open') {\n        console.log('✅ [CHECK STATUS] WhatsApp REALMENTE conectado na Evolution API!');\n        setIsLoged(true);\n        setGeneratingQrCode(false);\n        setQrCodeImg('');\n        clearQrCodeTimers(); // Limpar timers se conectado\n        return true; // Conectado\n      } else {\n        console.log(`📱 [CHECK STATUS] WhatsApp REALMENTE desconectado na Evolution API (${connectionState})`);\n        setIsLoged(false);\n        return false; // Desconectado\n      }\n    } catch (error) {\n      console.error('❌ [CHECK STATUS] Erro ao verificar status na Evolution API:', error);\n      console.log('📱 [CHECK STATUS] Assumindo desconectado devido ao erro');\n      setIsLoged(false);\n      return false; // Em caso de erro, assumir desconectado\n    }\n  };\n  const fetchQrCode = async (source = 'unknown') => {\n    console.log(`🔍 [FETCH QR CODE] Iniciado por: ${source}`);\n    if (!isMountedRef.current) {\n      console.log('🚫 [FETCH QR CODE] Componente não montado, cancelando');\n      return;\n    }\n\n    // **📌 PRIMEIRO VERIFICAR STATUS DE CONEXÃO**\n    console.log('🔍 [FETCH QR CODE] Verificando status de conexão...');\n    const isConnected = await checkConnectionStatus(`fetchQrCode_${source}`);\n    if (isConnected) {\n      console.log('✅ [FETCH QR CODE] Já conectado, cancelando geração de QR Code');\n      clearQrCodeTimers(); // Limpar timers se conectado\n      return; // Se conectado, não precisa gerar QR Code\n    }\n\n    // **📌 SE DESCONECTADO, GERAR QR CODE**\n    console.log(`🔄 [FETCH QR CODE] Desconectado, gerando QR Code... (fonte: ${source})`);\n    setGeneratingQrCode(true);\n    getQrCodeWhatsapp(empresaID).then(response => {\n      console.log('📡 QR Code Response completa:', response.data);\n      if (!isMountedRef.current) return;\n\n      // 🔧 TRATAMENTO MELHORADO DA RESPOSTA DO QR CODE (ALINHADO COM BACKEND)\n      console.log('📊 Resposta completa da API:', response.data);\n\n      // Verificar se já está conectado\n      if (response.data.isConnected) {\n        console.log('✅ WhatsApp já está conectado segundo a API');\n        setIsLoged(true);\n        setGeneratingQrCode(false);\n        setQrCodeImg('');\n        clearQrCodeTimers();\n        return;\n      }\n      let qrCodeData = null;\n      let ttl = 45; // TTL padrão\n\n      // Verificar diferentes formatos de resposta da Evolution API\n      if (response.data.qrcode) {\n        const qrData = response.data.qrcode;\n        if (qrData.qr) {\n          // Formato: { qrcode: { qr: \"data:image/png;base64,...\" } }\n          qrCodeData = qrData.qr;\n          console.log('📱 QR Code encontrado em qrcode.qr');\n        } else if (qrData.base64) {\n          // Formato: { qrcode: { base64: \"data:image/png;base64,...\" } }\n          qrCodeData = qrData.base64;\n          console.log('📱 QR Code encontrado em qrcode.base64');\n        } else if (typeof qrData === 'string') {\n          // Formato: { qrcode: \"data:image/png;base64,...\" }\n          qrCodeData = qrData;\n          console.log('📱 QR Code encontrado como string direta');\n        }\n\n        // Extrair TTL se disponível\n        if (qrData.ttl) {\n          ttl = Math.max(1, qrData.ttl - Math.floor(Date.now() / 1000));\n          console.log(`⏱️ TTL extraído do backend: ${ttl} segundos`);\n        }\n      }\n      if (qrCodeData) {\n        // Validar se é uma imagem base64 válida\n        if (qrCodeData.startsWith('data:image/')) {\n          setQrCodeImg(qrCodeData);\n          console.log('✅ QR Code definido com sucesso - Tamanho:', qrCodeData.length);\n          setGeneratingQrCode(false);\n\n          // 📱 INICIAR TIMER COM TTL DO BACKEND\n          startQrCodeTimer(ttl);\n        } else {\n          console.error('❌ QR Code não é uma imagem base64 válida:', qrCodeData.substring(0, 100));\n          setGeneratingQrCode(false);\n        }\n      } else {\n        console.error('❌ QR Code não encontrado na resposta:', response.data);\n        setGeneratingQrCode(false);\n      }\n\n      // ❌ REMOVIDO: Lógica antiga de regeneração automática\n      // Agora usamos timer fixo de 20 segundos com botão manual de regeneração\n\n      // ❌ REMOVIDO: Verificação periódica antiga - agora usamos socket listeners e timer fixo de 20s\n    }).catch(error => {\n      console.error('Erro ao buscar QR Code:', error);\n      setGeneratingQrCode(false);\n\n      // Em caso de erro, tenta novamente após 10 segundos\n      if (isMountedRef.current && !cancelQrCodeFetchRef.current) {\n        console.log('🔄 [FETCH QR CODE] Erro - tentando novamente em 10 segundos...');\n        timeoutRef.current = setTimeout(() => {\n          if (isMountedRef.current && !cancelQrCodeFetchRef.current) {\n            console.log('🔄 [FETCH QR CODE] Retry após erro');\n            fetchQrCode('error_retry');\n          }\n        }, 10000);\n      }\n    }).finally(() => {\n      if (isMountedRef.current) {\n        setGeneratingQrCode(false);\n      }\n    });\n  };\n  useEffect(() => {\n    if (selectedChat !== null && selectedChat !== void 0 && selectedChat._id) {\n      getBotStatusLead(selectedChat._id).then(response => {\n        console.log(\"Status desse bot:\", response.data.bot_pausado);\n        setBotPausado(response.data.bot_pausado);\n      }).catch(error => {\n        console.error(\"Erro ao obter status do bot:\", error);\n      });\n\n      // Buscar imagem de perfil quando um chat é selecionado\n      fetchProfilePicture(selectedChat);\n    }\n  }, [selectedChat]);\n\n  // **📌 FUNÇÃO PARA BUSCAR IMAGEM DE PERFIL (OTIMIZADA)**\n  const fetchProfilePicture = async (chat, forceUpdate = false, priority = 'normal') => {\n    var _chat$channel_data, _chat$channel_data$wh, _chat$channel_data2, _chat$channel_data2$w;\n    // **📌 CACHE MAIS INTELIGENTE - 7 DIAS AO INVÉS DE 24H**\n    const cacheValidityDays = 7;\n    const hasRecentProfilePicture = ((_chat$channel_data = chat.channel_data) === null || _chat$channel_data === void 0 ? void 0 : (_chat$channel_data$wh = _chat$channel_data.whatsapp) === null || _chat$channel_data$wh === void 0 ? void 0 : _chat$channel_data$wh.profile_picture_updateAt) && new Date() - new Date(chat.channel_data.whatsapp.profile_picture_updateAt) < cacheValidityDays * 24 * 60 * 60 * 1000;\n\n    // **📌 VERIFICAÇÕES PARA EVITAR REQUISIÇÕES DESNECESSÁRIAS**\n    if (!forceUpdate && (_chat$channel_data2 = chat.channel_data) !== null && _chat$channel_data2 !== void 0 && (_chat$channel_data2$w = _chat$channel_data2.whatsapp) !== null && _chat$channel_data2$w !== void 0 && _chat$channel_data2$w.profile_picture && hasRecentProfilePicture) {\n      return;\n    }\n\n    // **📌 EVITAR MÚLTIPLAS REQUISIÇÕES SIMULTÂNEAS PARA O MESMO CHAT**\n    const requestKey = `profile_picture_${chat._id}`;\n    if (window.profilePictureRequests && window.profilePictureRequests[requestKey]) {\n      return;\n    }\n\n    // Marcar requisição como em andamento\n    if (!window.profilePictureRequests) window.profilePictureRequests = {};\n    window.profilePictureRequests[requestKey] = true;\n    try {\n      const response = await getWhatsappProfilePicture(chat._id, token);\n      if (response.data && response.data.status === 200 && response.data.profilePictureUrl) {\n        // Atualizar o chat na lista local\n        setChats(prevChats => prevChats.map(c => {\n          var _c$channel_data;\n          return c._id === chat._id ? {\n            ...c,\n            channel_data: {\n              ...c.channel_data,\n              whatsapp: {\n                ...((_c$channel_data = c.channel_data) === null || _c$channel_data === void 0 ? void 0 : _c$channel_data.whatsapp),\n                profile_picture: response.data.profilePictureUrl,\n                profile_picture_updateAt: new Date().toISOString()\n              }\n            }\n          } : c;\n        }));\n\n        // Se é o chat selecionado, atualizar também\n        if (selectedChat && selectedChat._id === chat._id) {\n          setSelectedChat(prev => {\n            var _prev$channel_data;\n            return {\n              ...prev,\n              channel_data: {\n                ...prev.channel_data,\n                whatsapp: {\n                  ...((_prev$channel_data = prev.channel_data) === null || _prev$channel_data === void 0 ? void 0 : _prev$channel_data.whatsapp),\n                  profile_picture: response.data.profilePictureUrl,\n                  profile_picture_updateAt: new Date().toISOString()\n                }\n              }\n            };\n          });\n        }\n      }\n    } catch (error) {\n      console.error(`❌ Erro ao buscar imagem de perfil para ${chat.name}:`, error);\n    } finally {\n      // Remover marcação de requisição em andamento\n      if (window.profilePictureRequests) {\n        delete window.profilePictureRequests[requestKey];\n      }\n    }\n  };\n\n  // **📌 FUNÇÃO PARA BUSCAR IMAGENS EM LOTE DE FORMA INTELIGENTE**\n  const fetchProfilePicturesBatch = (chats, batchName = 'batch') => {\n    const chatsNeedingImages = chats.filter(chat => {\n      var _chat$channel_data3, _chat$channel_data3$w, _chat$channel_data4, _chat$channel_data4$w;\n      const hasImage = (_chat$channel_data3 = chat.channel_data) === null || _chat$channel_data3 === void 0 ? void 0 : (_chat$channel_data3$w = _chat$channel_data3.whatsapp) === null || _chat$channel_data3$w === void 0 ? void 0 : _chat$channel_data3$w.profile_picture;\n      const cacheValidityDays = 7;\n      const hasRecentUpdate = ((_chat$channel_data4 = chat.channel_data) === null || _chat$channel_data4 === void 0 ? void 0 : (_chat$channel_data4$w = _chat$channel_data4.whatsapp) === null || _chat$channel_data4$w === void 0 ? void 0 : _chat$channel_data4$w.profile_picture_updateAt) && new Date() - new Date(chat.channel_data.whatsapp.profile_picture_updateAt) < cacheValidityDays * 24 * 60 * 60 * 1000;\n      return !hasImage || !hasRecentUpdate;\n    });\n    if (chatsNeedingImages.length === 0) {\n      return;\n    }\n\n    // **📌 PRIORIZAR PRIMEIROS 5 CHATS (MAIS VISÍVEIS)**\n    const priorityChats = chatsNeedingImages.slice(0, 5);\n    const regularChats = chatsNeedingImages.slice(5);\n\n    // Buscar imagens prioritárias primeiro (delay menor)\n    priorityChats.forEach((chat, index) => {\n      setTimeout(() => {\n        fetchProfilePicture(chat, false, 'high');\n      }, index * 300); // 300ms entre cada (mais rápido)\n    });\n\n    // Buscar imagens regulares depois (delay maior)\n    regularChats.forEach((chat, index) => {\n      setTimeout(() => {\n        fetchProfilePicture(chat, false, 'normal');\n      }, priorityChats.length * 300 + index * 800); // 800ms entre cada (mais devagar)\n    });\n  };\n\n  // **📌 FUNÇÃO PARA BUSCAR CONTAGEM DE MENSAGENS NÃO LIDAS**\n  const fetchUnreadCounts = async () => {\n    try {\n      const response = await getWhatsappUnreadCount(empresaID, token);\n      if (response.data && response.data.status === 200) {\n        setUnreadCounts(response.data.unreadCounts);\n        //console.log(`📊 Contagens de mensagens não lidas atualizadas:`, response.data.unreadCounts);\n      }\n    } catch (error) {\n      console.error('❌ Erro ao buscar contagem de mensagens não lidas:', error);\n    }\n  };\n\n  // **📌 FUNÇÃO PARA DETECTAR CONTATOS COM NOMES GENÉRICOS**\n  const hasGenericName = (contactName, mobileNumber) => {\n    if (!contactName || !mobileNumber) return false;\n\n    // Verificar se o nome é genérico\n    const cleanName = contactName.trim();\n    const cleanNumber = mobileNumber.toString();\n    const genericPatterns = [cleanName === cleanNumber,\n    // Nome igual ao número\n    cleanName.startsWith('Contato '),\n    // Nome começa com \"Contato \"\n    /^\\d{10,}$/.test(cleanName),\n    // Nome é só números (10+ dígitos)\n    cleanName === `Contato ${cleanNumber}`,\n    // Formato exato \"Contato 556299999\"\n    cleanName.length < 3,\n    // Nomes muito curtos provavelmente são genéricos\n    /^[+\\-\\(\\)\\s\\d]+$/.test(cleanName) // Nome contém apenas números e símbolos telefônicos\n    ];\n    return genericPatterns.some(pattern => pattern);\n  };\n\n  // **📌 FUNÇÃO PARA ATUALIZAR INFORMAÇÕES DE UM CONTATO ESPECÍFICO**\n  const refreshContactInfo = async (chatId, chatName) => {\n    try {\n      console.log(`🔄 Atualizando informações do contato ${chatName}...`);\n      const response = await refreshContactInfoWhatsapp(chatId, token);\n      if (response.data && response.data.status === 200) {\n        console.log(`✅ Informações atualizadas:`, response.data);\n\n        // Atualizar o chat na lista local se houve mudança\n        if (response.data.contactName !== response.data.oldName || response.data.profilePictureUpdated) {\n          setChats(prevChats => prevChats.map(c => {\n            var _c$channel_data2, _c$channel_data3, _c$channel_data3$what, _c$channel_data4, _c$channel_data4$what;\n            return c._id === chatId ? {\n              ...c,\n              name: response.data.contactName,\n              channel_data: {\n                ...c.channel_data,\n                whatsapp: {\n                  ...((_c$channel_data2 = c.channel_data) === null || _c$channel_data2 === void 0 ? void 0 : _c$channel_data2.whatsapp),\n                  profile_picture: response.data.profilePictureUrl || ((_c$channel_data3 = c.channel_data) === null || _c$channel_data3 === void 0 ? void 0 : (_c$channel_data3$what = _c$channel_data3.whatsapp) === null || _c$channel_data3$what === void 0 ? void 0 : _c$channel_data3$what.profile_picture),\n                  profile_picture_updateAt: response.data.profilePictureUpdated ? new Date().toISOString() : (_c$channel_data4 = c.channel_data) === null || _c$channel_data4 === void 0 ? void 0 : (_c$channel_data4$what = _c$channel_data4.whatsapp) === null || _c$channel_data4$what === void 0 ? void 0 : _c$channel_data4$what.profile_picture_updateAt,\n                  pushName: response.data.contactName\n                }\n              }\n            } : c;\n          }));\n\n          // Se é o chat selecionado, atualizar também\n          if (selectedChat && selectedChat._id === chatId) {\n            setSelectedChat(prev => {\n              var _prev$channel_data2, _prev$channel_data3, _prev$channel_data3$w, _prev$channel_data4, _prev$channel_data4$w;\n              return {\n                ...prev,\n                name: response.data.contactName,\n                channel_data: {\n                  ...prev.channel_data,\n                  whatsapp: {\n                    ...((_prev$channel_data2 = prev.channel_data) === null || _prev$channel_data2 === void 0 ? void 0 : _prev$channel_data2.whatsapp),\n                    profile_picture: response.data.profilePictureUrl || ((_prev$channel_data3 = prev.channel_data) === null || _prev$channel_data3 === void 0 ? void 0 : (_prev$channel_data3$w = _prev$channel_data3.whatsapp) === null || _prev$channel_data3$w === void 0 ? void 0 : _prev$channel_data3$w.profile_picture),\n                    profile_picture_updateAt: response.data.profilePictureUpdated ? new Date().toISOString() : (_prev$channel_data4 = prev.channel_data) === null || _prev$channel_data4 === void 0 ? void 0 : (_prev$channel_data4$w = _prev$channel_data4.whatsapp) === null || _prev$channel_data4$w === void 0 ? void 0 : _prev$channel_data4$w.profile_picture_updateAt,\n                    pushName: response.data.contactName\n                  }\n                }\n              };\n            });\n          }\n        }\n\n        // Mostrar toast de sucesso\n        toast.success(response.data.message, {\n          position: \"top-right\",\n          autoClose: 3000,\n          hideProgressBar: false,\n          closeOnClick: true,\n          pauseOnHover: true,\n          draggable: true\n        });\n      }\n    } catch (error) {\n      console.error(`❌ Erro ao atualizar informações do contato ${chatName}:`, error);\n      toast.error('Erro ao atualizar informações do contato', {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n    }\n  };\n\n  // **📌 FUNÇÃO PARA BUSCAR MINHA IMAGEM DE PERFIL (PRÓPRIO USUÁRIO)**\n  const fetchMyProfilePicture = async (forceUpdate = false) => {\n    // **📌 VERIFICAR CACHE LOCAL PRIMEIRO**\n    const cachedImage = localStorage.getItem(`my_profile_picture_${empresaID}`);\n    const cachedTimestamp = localStorage.getItem(`my_profile_picture_timestamp_${empresaID}`);\n    if (!forceUpdate && cachedImage && cachedTimestamp) {\n      const cacheAge = new Date() - new Date(cachedTimestamp);\n      const cacheValidityDays = 7;\n      if (cacheAge < cacheValidityDays * 24 * 60 * 60 * 1000) {\n        console.log(`✅ Minha imagem de perfil já existe no cache local (válida por ${cacheValidityDays} dias)`);\n        setMyProfilePicture(cachedImage);\n        return;\n      }\n    }\n    try {\n      const response = await getMyWhatsappProfilePicture(empresaID, token);\n      if (response.data && response.data.status === 200 && response.data.profilePictureUrl) {\n        // Salvar no estado e no cache local\n        setMyProfilePicture(response.data.profilePictureUrl);\n        localStorage.setItem(`my_profile_picture_${empresaID}`, response.data.profilePictureUrl);\n        localStorage.setItem(`my_profile_picture_timestamp_${empresaID}`, new Date().toISOString());\n      } else {\n        setMyProfilePicture(null);\n        // Limpar cache se não há imagem\n        localStorage.removeItem(`my_profile_picture_${empresaID}`);\n        localStorage.removeItem(`my_profile_picture_timestamp_${empresaID}`);\n      }\n    } catch (error) {\n      var _error$response4, _error$response4$data, _error$response4$data2, _error$response4$data3;\n      console.error(`❌ Erro ao buscar minha imagem de perfil:`, error);\n\n      // **🔧 RECUPERAÇÃO AUTOMÁTICA: Se erro for por falta de JID, tentar buscar JID automaticamente**\n      if ((_error$response4 = error.response) !== null && _error$response4 !== void 0 && (_error$response4$data = _error$response4.data) !== null && _error$response4$data !== void 0 && (_error$response4$data2 = _error$response4$data.error) !== null && _error$response4$data2 !== void 0 && (_error$response4$data3 = _error$response4$data2.includes) !== null && _error$response4$data3 !== void 0 && _error$response4$data3.call(_error$response4$data2, 'JID do WhatsApp não encontrado')) {\n        console.log(`🔄 JID não encontrado. Tentando buscar e salvar JID automaticamente...`);\n        try {\n          var _jidResponse$data;\n          const {\n            fetchWhatsappJID\n          } = await import('../../services/api');\n          const jidResponse = await fetchWhatsappJID(empresaID, token);\n          if (((_jidResponse$data = jidResponse.data) === null || _jidResponse$data === void 0 ? void 0 : _jidResponse$data.status) === 200) {\n            var _retryResponse$data;\n            console.log(`✅ JID recuperado com sucesso: ${jidResponse.data.jid}`);\n            console.log(`🔄 Tentando buscar foto de perfil novamente...`);\n\n            // Tentar buscar a foto de perfil novamente agora que o JID foi salvo\n            const retryResponse = await getMyWhatsappProfilePicture(empresaID, token);\n            if (((_retryResponse$data = retryResponse.data) === null || _retryResponse$data === void 0 ? void 0 : _retryResponse$data.status) === 200 && retryResponse.data.profilePictureUrl) {\n              setMyProfilePicture(retryResponse.data.profilePictureUrl);\n              localStorage.setItem(`my_profile_picture_${empresaID}`, retryResponse.data.profilePictureUrl);\n              localStorage.setItem(`my_profile_picture_timestamp_${empresaID}`, new Date().toISOString());\n              console.log(`🎉 Foto de perfil carregada com sucesso após recuperar JID!`);\n              return;\n            }\n          } else {\n            console.log(`⚠️ Não foi possível recuperar o JID:`, jidResponse.data);\n          }\n        } catch (jidError) {\n          console.error(`❌ Erro ao tentar recuperar JID:`, jidError);\n        }\n      }\n      setMyProfilePicture(null);\n    }\n  };\n\n  // **📌 FUNÇÃO PARA CARREGAR MAIS MENSAGENS (PAGINAÇÃO)**\n  const loadMoreMessages = async () => {\n    if (!selectedChat || !hasMoreMessages || loadingMoreMessages) {\n      return;\n    }\n    setLoadingMoreMessages(true);\n    setShouldScrollToBottom(false); // **📌 NÃO ROLAR PARA O BOTTOM DURANTE PAGINAÇÃO**\n\n    try {\n      const nextPage = messagesPage + 1;\n      const pageSize = 30;\n      const response = await getWhatsappChatLead(empresaID, selectedChat._id, nextPage, pageSize, token);\n      if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {\n        var _response$data$messag;\n        const newMessages = response.data.messages[0].data;\n        const totalCount = ((_response$data$messag = response.data.messages[0].metadata[0]) === null || _response$data$messag === void 0 ? void 0 : _response$data$messag.totalCount) || 0;\n        if (newMessages.length === 0) {\n          setHasMoreMessages(false);\n        } else {\n          // Adicionar novas mensagens ao início da lista (mensagens mais antigas)\n          setMessagesLeadChannel(prevMessages => {\n            // Evitar duplicatas\n            const existingIds = new Set(prevMessages.map(msg => msg._id));\n            const uniqueNewMessages = newMessages.filter(msg => !existingIds.has(msg._id));\n\n            // Adicionar mensagens mais antigas no início\n            return [...uniqueNewMessages, ...prevMessages];\n          });\n\n          // Atualizar cache\n          const cacheKey = selectedChat._id;\n          setMessagesCache(prevCache => {\n            const newCache = new Map(prevCache);\n            const existingMessages = newCache.get(cacheKey) || [];\n            const existingIds = new Set(existingMessages.map(msg => msg._id));\n            const uniqueNewMessages = newMessages.filter(msg => !existingIds.has(msg._id));\n            const finalCacheMessages = [...uniqueNewMessages, ...existingMessages];\n            newCache.set(cacheKey, finalCacheMessages);\n            return newCache;\n          });\n          setMessagesPage(nextPage);\n\n          // **📌 VERIFICAR SE AINDA HÁ MAIS MENSAGENS USANDO CALLBACK**\n          setMessagesLeadChannel(currentMessages => {\n            const currentTotal = currentMessages.length;\n            if (currentTotal >= totalCount) {\n              setHasMoreMessages(false);\n            }\n            return currentMessages; // Não modificar, apenas verificar\n          });\n        }\n      } else {\n        setHasMoreMessages(false);\n        console.log(`⚠️ Resposta inválida ao carregar mais mensagens para ${selectedChat.name}`);\n      }\n    } catch (error) {\n      console.error(`❌ Erro ao carregar mais mensagens para ${selectedChat.name}:`, error);\n    } finally {\n      setLoadingMoreMessages(false);\n      setShouldScrollToBottom(true); // **📌 REATIVAR SCROLL PARA O BOTTOM APÓS PAGINAÇÃO**\n    }\n  };\n\n  // **📌 HANDLER PARA SCROLL DAS MENSAGENS (CARREGAR MAIS AO ROLAR PARA CIMA)**\n  const handleMessagesScroll = e => {\n    const container = e.target;\n    const scrollTop = container.scrollTop;\n    const scrollThreshold = 100; // Pixels do topo para disparar carregamento\n\n    // **📌 NÃO CARREGAR MENSAGENS SE ACABOU DE ABRIR A CONVERSA**\n    if (justOpenedChat) {\n      console.log(`🚫 Conversa acabou de abrir, ignorando scroll no topo para ${selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.name}`);\n      return;\n    }\n\n    // **📌 IGNORAR SCROLL AUTOMÁTICO DURANTE CARREGAMENTO**\n    if (loadingMessages) {\n      return;\n    }\n\n    // Se rolou próximo ao topo e há mais mensagens para carregar\n    if (scrollTop <= scrollThreshold && hasMoreMessages && !loadingMoreMessages) {\n      console.log(`📜 Scroll detectado no topo - carregando mais mensagens para ${selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.name}`);\n\n      // **📌 SALVAR POSIÇÃO ATUAL DO SCROLL PARA RESTAURAR APÓS CARREGAR**\n      const currentScrollHeight = container.scrollHeight;\n      const currentScrollTop = scrollTop;\n      loadMoreMessages().then(() => {\n        // **📌 RESTAURAR POSIÇÃO DO SCROLL DE FORMA MAIS ROBUSTA**\n        const restoreScroll = () => {\n          const newScrollHeight = container.scrollHeight;\n          const scrollDifference = newScrollHeight - currentScrollHeight;\n          const newScrollTop = currentScrollTop + scrollDifference;\n\n          // **📌 APLICAR NOVA POSIÇÃO IMEDIATAMENTE**\n          container.scrollTop = newScrollTop;\n\n          // **📌 VERIFICAR SE A POSIÇÃO FOI APLICADA CORRETAMENTE**\n          requestAnimationFrame(() => {\n            if (Math.abs(container.scrollTop - newScrollTop) > 5) {\n              container.scrollTop = newScrollTop;\n            }\n          });\n        };\n\n        // **📌 TENTAR RESTAURAR IMEDIATAMENTE E DEPOIS COM DELAY**\n        restoreScroll();\n        setTimeout(restoreScroll, 50); // Backup caso o primeiro não funcione\n      });\n    }\n  };\n\n  /*useEffect(() => {\r\n    const fetchChats = async () => {\r\n      try {\r\n        const page = 0;\r\n        const pageSize = 30;\r\n        const query = searchQuery; // Adicione seu termo de busca aqui, se houver\r\n          //console.log(\"O que está sendo enviado:\",{empresaID, page, pageSize, query, token})\r\n        const response = await getWhatsappChats(empresaID, page, pageSize, query, token);\r\n        //console.log(\"O que está sendo Recebido!:\",response)\r\n        if (query === '' || query.length >= 3) {\r\n          setChats(response.data.messages[0].data);\r\n        }\r\n        if (response.data.messages[0].data.length > 0 && query !== '') {\r\n          const data = response.data.messages[0].data;\r\n          if (data.length > 0 && query.length >= 3) {\r\n            //console.log(\"Oq tem aqui?\", data);\r\n            setChats([data[0]]); // Armazena apenas o primeiro elemento como array\r\n          }\r\n        }\r\n        //console.log(response.data.messages[0].data);\r\n        setLoading(false);\r\n      } catch (err) {\r\n        setError(err);\r\n        setLoading(false);\r\n      }\r\n    };\r\n      // Chame fetchChats inicialmente\r\n    fetchChats();\r\n    }, [searchQuery]);*/\n  const [page, setPage] = useState(0); // Página inicial\n  const [hasMoreChats, setHasMoreChats] = useState(true); // Se ainda há chats para carregar\n  const chatsContainerRef = useRef(null); // Referência ao container de chats\n  const [loadingMore, setLoadingMore] = useState(false); // Para evitar requisições duplicadas\n\n  const fetchChats = async (newPage, append = false, source = 'unknown') => {\n    console.log(\"🎯 fetchChats INICIADO - newPage:\", newPage, \"append:\", append, \"source:\", source);\n    console.log(\"📋 Parâmetros - empresaID:\", empresaID, \"token:\", token ? \"presente\" : \"ausente\");\n\n    // 🛡️ EVITAR EXECUÇÕES DUPLICADAS\n    if (fetchingChatsRef.current && !append) {\n      console.log(\"🚫 fetchChats já em execução, ignorando chamada duplicada de:\", source);\n      return;\n    }\n    if (!append) {\n      fetchingChatsRef.current = true;\n    }\n    try {\n      const pageSize = 30;\n      const query = searchQuery;\n      console.log(\"🔄 Carregando página:\", newPage);\n      console.log(\"🔍 Query de busca:\", query);\n      const response = await getWhatsappChats(empresaID, newPage, pageSize, query, token);\n      const newChats = response.data.messages[0].data;\n      if (newChats.length === 0) {\n        setHasMoreChats(false); // Se não há mais chats, parar carregamento\n      }\n      setChats(prevChats => {\n        let finalChats;\n        if (append) {\n          // Remove duplicatas ao adicionar novos chats\n          const existingIds = new Set(prevChats.map(chat => chat._id));\n          const uniqueNewChats = newChats.filter(chat => !existingIds.has(chat._id));\n          finalChats = [...prevChats, ...uniqueNewChats];\n        } else {\n          // Remove duplicatas nos chats iniciais\n          const uniqueChats = newChats.filter((chat, index, self) => index === self.findIndex(c => c._id === chat._id));\n          finalChats = uniqueChats;\n\n          // **📌 INICIAR PRÉ-CARREGAMENTO APENAS NO CARREGAMENTO INICIAL**\n          if (uniqueChats.length > 0 && !preloadingChats && !hasPreloaded && searchQuery === '' && isInitialLoad) {\n            console.log(\"🚀 Iniciando pré-carregamento único...\");\n            setHasPreloaded(true);\n            setIsInitialLoad(false);\n            setTimeout(() => {\n              preloadAllChats(uniqueChats);\n            }, 1000); // Delay de 1 segundo para não interferir com a UI\n          }\n        }\n        // **📌 BUSCAR IMAGENS DE PERFIL SIMULTANEAMENTE PARA TODOS OS CHATS CARREGADOS**\n        if (!append) {\n          // Para carregamento inicial, usar função de lote inteligente\n          setTimeout(() => {\n            fetchProfilePicturesBatch(finalChats, 'carregamento inicial');\n          }, 800); // Delay menor para começar mais cedo\n        }\n\n        // **📌 BUSCAR IMAGENS TAMBÉM PARA CHATS CARREGADOS VIA PAGINAÇÃO**\n        if (append && finalChats.length > 0) {\n          setTimeout(() => {\n            fetchProfilePicturesBatch(finalChats, 'paginação');\n          }, 300); // Delay menor para paginação\n        }\n\n        // **📌 BUSCAR CONTAGEM DE MENSAGENS NÃO LIDAS APÓS CARREGAR CHATS**\n        if (!append && finalChats.length > 0) {\n          setTimeout(() => {\n            fetchUnreadCounts();\n          }, 500); // Delay para não sobrecarregar\n        }\n        return finalChats;\n      });\n      setLoading(false);\n      setLoadingMore(false);\n    } catch (err) {\n      var _err$response, _err$response2;\n      console.error(\"❌ Erro ao carregar chats:\", err);\n      console.error(\"📊 Detalhes do erro:\", {\n        message: err.message,\n        status: (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status,\n        data: (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data,\n        config: err.config\n      });\n      setLoading(false);\n      setLoadingMore(false);\n    } finally {\n      // 🛡️ SEMPRE RESETAR FLAG DE FETCHING\n      if (!append) {\n        fetchingChatsRef.current = false;\n      }\n    }\n  };\n\n  // **📌 Detectar scroll e carregar mais chats**\n  const handleScroll = () => {\n    if (!hasMoreChats || loadingMore) return;\n    const container = chatsContainerRef.current;\n    if (!container) return;\n    const bottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 10;\n    if (bottom) {\n      console.log(\"📢 Rolou até o fim, carregando mais chats...\");\n      setLoadingMore(true);\n      setPage(prevPage => {\n        const nextPage = prevPage + 1;\n        fetchChats(nextPage, true, 'pagination');\n        return nextPage;\n      });\n    }\n  };\n  const formatDate = date => {\n    const now = moment().tz(\"America/Sao_Paulo\");\n    const inputDate = moment(date).tz(\"America/Sao_Paulo\");\n    if (now.isSame(inputDate, 'day')) {\n      return inputDate.format('HH:mm');\n    } else if (now.subtract(1, 'days').isSame(inputDate, 'day')) {\n      return 'Ontem';\n    } else if (now.subtract(6, 'days').isBefore(inputDate)) {\n      return inputDate.format('dddd');\n    } else {\n      return inputDate.format('DD/MM/YYYY');\n    }\n  };\n  const handleDisconnect = async () => {\n    try {\n      const res = await removeWhatsappSession(empresaID); // Aguarda a resposta da API\n\n      // Exibe um toast de sucesso com a mensagem da resposta da API\n      toast(res.data.msg, {\n        autoClose: 3000,\n        type: \"success\"\n      });\n\n      // 🔧 REMOVIDO: geração automática de QR Code após desconexão\n      // Agora o usuário deve clicar manualmente para gerar\n      setUserRequestedQrCode(false); // Resetar flag para exibir botão de geração\n      setQrCodeImg(''); // Limpar QR Code atual\n      setIsLoged(false); // Marcar como desconectado\n      // fetchQrCode(); // REMOVIDO\n    } catch (error) {\n      var _error$response5, _error$response6, _error$response6$data;\n      console.error(\"Erro ao encerrar sessão:\", ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.data) || error.message);\n\n      // Exibe um toast de erro com a mensagem da API ou um fallback padrão\n      toast(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.msg) || \"Erro ao encerrar sessão!\", {\n        autoClose: 3000,\n        type: \"error\"\n      });\n    }\n  };\n  const handleToggleBot = async lead_id => {\n    try {\n      const response = await toggleBotStatus(lead_id);\n      setBotPausado(response.data.bot_pausado);\n      toast(response.data.msg, {\n        autoClose: 3000,\n        type: \"success\"\n      });\n    } catch (error) {\n      console.error(\"Erro ao alternar o status do bot:\", error);\n    }\n  };\n\n  // **📌 FUNÇÃO PARA PRÉ-CARREGAR TODAS AS CONVERSAS**\n  const preloadAllChats = async chatList => {\n    if (!chatList || chatList.length === 0) return;\n    setPreloadingChats(true);\n\n    // Priorizar conversas com mensagens mais recentes\n    const sortedChats = [...chatList].sort((a, b) => {\n      var _a$message, _a$message2, _b$message, _b$message2;\n      const dateA = new Date(((_a$message = a.message) === null || _a$message === void 0 ? void 0 : _a$message.messageDate) || ((_a$message2 = a.message) === null || _a$message2 === void 0 ? void 0 : _a$message2.createdAt) || 0);\n      const dateB = new Date(((_b$message = b.message) === null || _b$message === void 0 ? void 0 : _b$message.messageDate) || ((_b$message2 = b.message) === null || _b$message2 === void 0 ? void 0 : _b$message2.createdAt) || 0);\n      return dateB - dateA;\n    });\n\n    // Limitar a 15 conversas mais recentes para não sobrecarregar\n    const chatsToPreload = sortedChats.slice(0, 15);\n    setPreloadProgress({\n      current: 0,\n      total: chatsToPreload.length\n    });\n    const batchSize = 3; // Carregar 3 conversas por vez\n\n    try {\n      for (let i = 0; i < chatsToPreload.length; i += batchSize) {\n        const batch = chatsToPreload.slice(i, i + batchSize);\n\n        // Carregar batch em paralelo\n        const batchPromises = batch.map(async chat => {\n          try {\n            // Verificar se já não está no cache\n            if (messagesCache.has(chat._id)) {\n              return;\n            }\n            const response = await getWhatsappChatLead(empresaID, chat._id, 0, 30, token);\n            if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {\n              const messages = response.data.messages[0].data;\n\n              // Armazenar no cache\n              setMessagesCache(prevCache => {\n                const newCache = new Map(prevCache);\n                newCache.set(chat._id, messages);\n                return newCache;\n              });\n\n              //console.log(`✅ ${messages.length} mensagens pré-carregadas para ${chat.name}`);\n            } else {\n              // Armazenar array vazio para conversas sem mensagens\n              setMessagesCache(prevCache => {\n                const newCache = new Map(prevCache);\n                newCache.set(chat._id, []);\n                return newCache;\n              });\n              //console.log(`📭 Conversa vazia pré-carregada: ${chat.name}`);\n            }\n          } catch (error) {\n            console.error(`❌ Erro ao pré-carregar ${chat.name}:`, error);\n            // Armazenar array vazio em caso de erro\n            setMessagesCache(prevCache => {\n              const newCache = new Map(prevCache);\n              newCache.set(chat._id, []);\n              return newCache;\n            });\n          }\n        });\n\n        // Aguardar batch atual\n        await Promise.all(batchPromises);\n\n        // Atualizar progresso\n        const completed = Math.min(i + batchSize, chatsToPreload.length);\n        setPreloadProgress({\n          current: completed,\n          total: chatsToPreload.length\n        });\n\n        // Delay dinâmico baseado na quantidade de conversas\n        if (i + batchSize < chatsToPreload.length) {\n          const delay = chatsToPreload.length > 10 ? 400 : 200; // Delay maior para listas maiores\n          await new Promise(resolve => setTimeout(resolve, delay));\n        }\n      }\n      console.log(`🎉 Pré-carregamento concluído! ${chatsToPreload.length} conversas armazenadas no cache.`);\n      console.log(`💾 Cache atual contém ${messagesCache.size} conversas.`);\n\n      // Feedback visual de sucesso\n      /*setTimeout(() => {\r\n        toast.success(`✅ ${chatsToPreload.length} conversas pré-carregadas com sucesso!`, {\r\n          position: \"top-right\",\r\n          autoClose: 3000,\r\n          hideProgressBar: false,\r\n          closeOnClick: true,\r\n          pauseOnHover: true,\r\n          draggable: true,\r\n        });\r\n      }, 500);*/\n    } catch (error) {\n      console.error('❌ Erro no pré-carregamento:', error);\n      toast.error('⚠️ Erro ao pré-carregar conversas', {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n    } finally {\n      setPreloadingChats(false);\n      setPreloadProgress({\n        current: 0,\n        total: 0\n      });\n    }\n  };\n\n  // **📌 Carregar os chats ao iniciar e ao buscar**\n  useEffect(() => {\n    console.log(\"🚀 useEffect disparado - searchQuery:\", searchQuery);\n    console.log(\"📊 Estado atual - empresaID:\", empresaID, \"token:\", token ? \"presente\" : \"ausente\");\n    setChats([]); // Resetar chats ao buscar\n    setPage(0);\n    setHasMoreChats(true);\n    fetchingChatsRef.current = false; // Reset da flag de controle\n\n    const source = searchQuery ? 'search' : 'initial_load';\n    console.log(\"📞 Chamando fetchChats(0, false) -\", source);\n    fetchChats(0, false, source);\n  }, [searchQuery]);\n\n  // **📌 Resetar pré-carregamento quando empresa ou token mudar**\n  useEffect(() => {\n    setHasPreloaded(false);\n    setMessagesCache(new Map());\n    setPreloadingChats(false);\n    setPreloadProgress({\n      current: 0,\n      total: 0\n    });\n    setRaceConditionsFixed(0);\n    setIsInitialLoad(true); // Reset para nova empresa\n    fetchingChatsRef.current = false; // Reset da flag de controle\n\n    // **📌 BUSCAR MINHA IMAGEM DE PERFIL QUANDO EMPRESA/TOKEN MUDAR**\n    if (empresaID && token) {\n      fetchMyProfilePicture();\n    }\n  }, [empresaID, token]);\n\n  // **📌 ATUALIZAÇÃO PERIÓDICA DAS CONTAGENS DE MENSAGENS NÃO LIDAS**\n  useEffect(() => {\n    if (!empresaID || !token) return;\n\n    // Atualizar contagens a cada 30 segundos\n    const interval = setInterval(() => {\n      fetchUnreadCounts();\n    }, 30000);\n    return () => clearInterval(interval);\n  }, [empresaID, token]);\n\n  // **📌 MONITORAMENTO INTELIGENTE DE STATUS E AUTO-RENOVAÇÃO**\n  useEffect(() => {\n    if (!empresaID || !token || isLoged) return;\n    let monitoringInterval = null;\n    const startMonitoring = () => {\n      // Verificar status a cada 10 segundos quando desconectado\n      monitoringInterval = setInterval(async () => {\n        if (isLoged || !isMountedRef.current) {\n          clearInterval(monitoringInterval);\n          return;\n        }\n        try {\n          // Verificação silenciosa de status\n          const isConnected = await checkConnectionStatus('monitoring');\n          if (isConnected) {\n            // Se conectou, limpar monitoramento\n            clearInterval(monitoringInterval);\n            console.log('📊 Monitoramento detectou conexão estabelecida');\n          }\n        } catch (error) {\n          // Ignorar erros silenciosamente no monitoramento\n          console.debug('Erro silencioso no monitoramento:', error.message);\n        }\n      }, 10000); // 10 segundos\n    };\n\n    // Iniciar monitoramento após 30 segundos (dar tempo para conexão inicial)\n    const startTimer = setTimeout(() => {\n      if (!isLoged && isMountedRef.current) {\n        console.log('📊 Iniciando monitoramento de status inteligente');\n        startMonitoring();\n      }\n    }, 30000);\n    return () => {\n      clearTimeout(startTimer);\n      if (monitoringInterval) {\n        clearInterval(monitoringInterval);\n      }\n    };\n  }, [empresaID, token, isLoged]);\n\n  // **📌 Mostrar estatísticas de race conditions evitadas**\n  useEffect(() => {\n    if (raceConditionsFixed > 0 && raceConditionsFixed % 10 === 0) {\n      console.log(`🛡️ Race Conditions evitadas: ${raceConditionsFixed}`);\n      toast.success(`🛡️ Sistema anti-bug funcionando! ${raceConditionsFixed} conflitos evitados`, {\n        position: \"top-right\",\n        autoClose: 2000,\n        hideProgressBar: false,\n        closeOnClick: true,\n        pauseOnHover: true,\n        draggable: true\n      });\n    }\n  }, [raceConditionsFixed]);\n\n  // **📌 Adicionar listener de scroll**\n  useEffect(() => {\n    const container = chatsContainerRef.current;\n    if (container) {\n      container.addEventListener(\"scroll\", handleScroll);\n    }\n    return () => {\n      if (container) {\n        container.removeEventListener(\"scroll\", handleScroll);\n      }\n    };\n  }, [hasMoreChats, loadingMore]);\n\n  // **📌 GERENCIAR MENSAGENS DO CHAT SELECIONADO (USANDO SOCKET PRINCIPAL)**\n\n  // **📌 GERENCIAR MENSAGENS DO CHAT SELECIONADO**\n  useEffect(() => {\n    if (!mainSocket || !selectedChat) {\n      return;\n    }\n    if (selectedChat) {\n      // **📌 CONTROLE DE REQUISIÇÕES ATIVAS PARA EVITAR RACE CONDITIONS**\n      let isCurrentConversation = true;\n      const currentChatId = selectedChat._id;\n      const currentChatName = selectedChat.name;\n\n      // **📌 LIMPAR MENSAGENS E INICIAR LOADING IMEDIATAMENTE AO TROCAR CONVERSA**\n      setMessagesLeadChannel([]);\n      setLoadingMessages(true);\n      setShouldScrollToBottom(true); // **📌 REATIVAR SCROLL PARA O BOTTOM EM NOVA CONVERSA**\n\n      // **📌 MARCAR QUE ACABOU DE ABRIR UMA CONVERSA**\n      setJustOpenedChat(true);\n      if (justOpenedTimerRef.current) {\n        clearTimeout(justOpenedTimerRef.current);\n      }\n\n      // **📌 LIMPAR FLAG APÓS 3 SEGUNDOS PARA PERMITIR CARREGAMENTO DE MENSAGENS ANTIGAS**\n      justOpenedTimerRef.current = setTimeout(() => {\n        setJustOpenedChat(false);\n        console.log(`✅ Conversa ${currentChatName} liberada para carregamento de mensagens antigas após 3s`);\n      }, 3000);\n      mainSocket.emit('join-lead-messages', {\n        leadID: selectedChat._id\n      });\n      const fetchChatLead = async () => {\n        // **📌 PEQUENO DELAY PARA EVITAR RACE CONDITIONS EM TROCAS MUITO RÁPIDAS**\n        await new Promise(resolve => setTimeout(resolve, 50));\n\n        // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA APÓS DELAY**\n        if (!isCurrentConversation) {\n          //console.log(`🚫 Fetchamento cancelado após delay - conversa ${currentChatName} não é mais ativa`);\n          setRaceConditionsFixed(prev => prev + 1);\n          return;\n        }\n        try {\n          // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA**\n          if (!isCurrentConversation) {\n            //console.log(`🚫 Requisição cancelada - conversa ${currentChatName} não é mais ativa`);\n            return;\n          }\n\n          // **📌 VERIFICAR CACHE PRIMEIRO**\n          const cachedMessages = messagesCache.get(currentChatId);\n          if (cachedMessages && cachedMessages.length > 0) {\n            // **📌 VERIFICAR NOVAMENTE SE AINDA É A CONVERSA ATIVA ANTES DE APLICAR**\n            if (isCurrentConversation && (selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat._id) === currentChatId) {\n              setMessagesLeadChannel(cachedMessages);\n\n              // **📌 RESETAR ESTADOS DE PAGINAÇÃO PARA CACHE**\n              // Assumir que cache tem apenas primeira página, pode haver mais mensagens\n              setMessagesPage(0);\n              setHasMoreMessages(cachedMessages.length >= 30); // Se tem 30 ou mais, provavelmente há mais\n\n              setLoadingMessages(false);\n\n              // **📌 FORÇAR SCROLL PARA BOTTOM IMEDIATAMENTE APÓS CARREGAR CACHE**\n              setTimeout(() => {\n                const container = messagesContainerRef.current;\n                if (container && cachedMessages.length > 0) {\n                  container.scrollTop = container.scrollHeight;\n                }\n              }, 100);\n            } else {\n              //console.log(`🚫 Cache descartado - conversa ${currentChatName} não é mais ativa`);\n              setRaceConditionsFixed(prev => prev + 1);\n            }\n\n            // **📌 OPCIONAL: Fazer requisição em background para atualizar cache**\n            // Isso garante que sempre temos as mensagens mais recentes\n            setTimeout(async () => {\n              try {\n                if (!isCurrentConversation) return; // Cancelar se não é mais ativa\n\n                const page = 0;\n                const pageSize = 30;\n                const response = await getWhatsappChatLead(empresaID, currentChatId, page, pageSize, token);\n                if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {\n                  const freshMessages = response.data.messages[0].data;\n\n                  // Atualizar cache com mensagens frescas\n                  setMessagesCache(prevCache => {\n                    const newCache = new Map(prevCache);\n                    newCache.set(currentChatId, freshMessages);\n                    return newCache;\n                  });\n\n                  // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA ANTES DE ATUALIZAR TELA**\n                  if (isCurrentConversation && (selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat._id) === currentChatId) {\n                    setMessagesLeadChannel(freshMessages);\n                  } else {\n                    //console.log(`🚫 Mensagens frescas descartadas - conversa ${currentChatName} não é mais ativa`);\n                  }\n                }\n              } catch (error) {\n                if (!isCurrentConversation) {\n                  //console.log(`⏹️ Requisição de background cancelada para ${currentChatName}`);\n                } else {\n                  //console.log('Erro ao atualizar cache em background:', error);\n                }\n              }\n            }, 1000); // 1 segundo de delay\n\n            return; // Usar cache, não faz requisição imediata\n          }\n\n          //console.log(`❌ Cache não encontrado para ${currentChatName} - fazendo requisição à API`);\n\n          // **📌 INICIAR LOADING SE NÃO TIVER CACHE**\n          if (isCurrentConversation) {\n            //console.log(`⏳ Iniciando loading para conversa sem cache: ${currentChatName}`);\n            setLoadingMessages(true);\n          }\n          const page = 0;\n          const pageSize = 30;\n          const response = await getWhatsappChatLead(empresaID, currentChatId, page, pageSize, token);\n\n          // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA ANTES DE APLICAR RESULTADOS**\n          if (!isCurrentConversation) {\n            setRaceConditionsFixed(prev => prev + 1);\n            return;\n          }\n          if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {\n            const messages = response.data.messages[0].data;\n\n            // **📌 VERIFICAR NOVAMENTE ANTES DE APLICAR**\n            if (isCurrentConversation && (selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat._id) === currentChatId) {\n              var _response$data$messag2;\n              setMessagesLeadChannel(messages);\n\n              // **📌 RESETAR ESTADOS DE PAGINAÇÃO PARA NOVA CONVERSA**\n              const totalCount = ((_response$data$messag2 = response.data.messages[0].metadata[0]) === null || _response$data$messag2 === void 0 ? void 0 : _response$data$messag2.totalCount) || messages.length;\n              setMessagesPage(0); // Primeira página carregada\n              setHasMoreMessages(messages.length < totalCount); // Há mais mensagens se carregamos menos que o total\n\n              // **📌 ARMAZENAR NO CACHE**\n              setMessagesCache(prevCache => {\n                const newCache = new Map(prevCache);\n                newCache.set(currentChatId, messages);\n                return newCache;\n              });\n\n              // **📌 FORÇAR SCROLL PARA BOTTOM IMEDIATAMENTE APÓS CARREGAR DA API**\n              setTimeout(() => {\n                const container = messagesContainerRef.current;\n                if (container && messages.length > 0) {\n                  container.scrollTop = container.scrollHeight;\n                }\n              }, 100);\n            } else {\n              //console.log(`🚫 Mensagens da API descartadas - conversa ${currentChatName} não é mais ativa`);\n            }\n          } else {\n            // **📌 VERIFICAR ANTES DE APLICAR ARRAY VAZIO**\n            if (isCurrentConversation && (selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat._id) === currentChatId) {\n              console.log(`📭 Aplicando array vazio para conversa ativa: ${currentChatName}`);\n              setMessagesLeadChannel([]);\n\n              // **📌 RESETAR ESTADOS DE PAGINAÇÃO PARA CONVERSA VAZIA**\n              setMessagesPage(0);\n              setHasMoreMessages(false);\n\n              // **📌 ARMAZENAR ARRAY VAZIO NO CACHE**\n              setMessagesCache(prevCache => {\n                const newCache = new Map(prevCache);\n                newCache.set(currentChatId, []);\n                return newCache;\n              });\n            } else {\n              console.log(`🚫 Array vazio descartado - conversa ${currentChatName} não é mais ativa`);\n            }\n          }\n        } catch (err) {\n          if (!isCurrentConversation) {\n            console.log(`⏹️ Requisição cancelada para ${currentChatName}`);\n          } else {\n            console.error(`❌ Erro ao carregar mensagens para ${currentChatName}:`, err);\n\n            // **📌 VERIFICAR ANTES DE APLICAR ARRAY VAZIO EM ERRO**\n            if (isCurrentConversation && (selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat._id) === currentChatId) {\n              console.log(`💥 Aplicando array vazio por erro para conversa ativa: ${currentChatName}`);\n              setMessagesLeadChannel([]);\n            } else {\n              console.log(`🚫 Array vazio por erro descartado - conversa ${currentChatName} não é mais ativa`);\n            }\n          }\n        } finally {\n          // **📌 FINALIZAR LOADING APENAS SE AINDA FOR A CONVERSA ATIVA**\n          if (isCurrentConversation && (selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat._id) === currentChatId) {\n            setLoadingMessages(false);\n          }\n        }\n      };\n\n      // **📌 ENTRAR NA SALA DE MENSAGENS ESPECÍFICA DO LEAD**\n      mainSocket.emit('join-lead-messages', {\n        leadID: currentChatId\n      });\n\n      // **📌 ESCUTAR MENSAGENS DESTA CONVERSA ESPECÍFICA**\n      mainSocket.on('messages', newMessage => {\n        // **📌 VERIFICAR SE A MENSAGEM É DA CONVERSA ATIVA**\n        if (!isCurrentConversation || (selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat._id) !== currentChatId) {\n          setRaceConditionsFixed(prev => prev + 1);\n          return;\n        }\n\n        // **📌 PARAR LOADING SE ESTIVER ATIVO**\n        setLoadingMessages(false);\n\n        // Remover mensagem otimista se for a mesma\n        setMessagesLeadChannel(oldMessages => {\n          // Filtrar mensagens duplicadas e otimistas do mesmo texto\n          const filteredMessages = oldMessages.filter(message => {\n            // Remover duplicatas por ID\n            if (message._id === newMessage._id) return false;\n            // Remover mensagens otimistas se a mensagem real chegou\n            if (message.isOptimistic && message.text === newMessage.text && message.fromMe === newMessage.fromMe) {\n              return false;\n            }\n            return true;\n          });\n          return [newMessage, ...filteredMessages];\n        });\n\n        // **📌 ATUALIZAR CACHE COM NOVA MENSAGEM**\n        setMessagesCache(prevCache => {\n          const newCache = new Map(prevCache);\n          const cachedMessages = newCache.get(currentChatId) || [];\n\n          // Filtrar mensagens duplicadas e otimistas do cache\n          const filteredCachedMessages = cachedMessages.filter(message => {\n            if (message._id === newMessage._id) return false;\n            if (message.isOptimistic && message.text === newMessage.text && message.fromMe === newMessage.fromMe) {\n              return false;\n            }\n            return true;\n          });\n          newCache.set(currentChatId, [newMessage, ...filteredCachedMessages]);\n          return newCache;\n        });\n\n        // **📌 MARCAR MENSAGEM COMO LIDA SE NÃO FOR MINHA E O CHAT ESTIVER ABERTO**\n        if (!newMessage.fromMe) {\n          console.log(`📖 Nova mensagem recebida no chat aberto, marcando como lida automaticamente`);\n          setTimeout(() => {\n            markChatAsRead(currentChatId);\n          }, 1500); // Delay maior para garantir que a mensagem foi processada no backend\n        }\n\n        // **📌 GARANTIR QUE O SCROLL PERMANEÇA NO BOTTOM APÓS NOVA MENSAGEM**\n        setTimeout(() => {\n          const container = messagesContainerRef.current;\n          if (container && shouldScrollToBottom) {\n            container.scrollTop = container.scrollHeight;\n          }\n        }, 100);\n      });\n      fetchChatLead();\n\n      // **📌 TIMEOUT PARA FORÇAR PARADA DO LOADING EM CASO DE DEMORA**\n      const loadingTimeout = setTimeout(() => {\n        if (isCurrentConversation && (selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat._id) === currentChatId) {\n          setLoadingMessages(false);\n        }\n      }, 10000); // 10 segundos\n\n      return () => {\n        // **📌 MARCAR CONVERSA COMO INATIVA PARA CANCELAR REQUISIÇÕES**\n        isCurrentConversation = false;\n        clearTimeout(loadingTimeout);\n\n        // **📌 LIMPAR TIMER DE CONVERSA RECÉM ABERTA**\n        if (justOpenedTimerRef.current) {\n          clearTimeout(justOpenedTimerRef.current);\n        }\n        setJustOpenedChat(false);\n        mainSocket.emit('leave-lead-messages', {\n          leadID: currentChatId\n        });\n        mainSocket.off('messages');\n        // **📌 NÃO DESCONECTAR O SOCKET PRINCIPAL, APENAS LIMPAR EVENTOS**\n      };\n    } else {\n      // **📌 LIMPAR LOADING QUANDO NÃO HÁ CHAT SELECIONADO**\n      setLoadingMessages(false);\n      // **📌 NÃO DESCONECTAR O SOCKET PRINCIPAL**\n    }\n  }, [selectedChat, mainSocket]);\n\n  // **📌 FUNÇÃO ROBUSTA PARA ABRIR CONVERSA POR LEAD_ID**\n  const abrirConversaPorLeadId = useCallback(async leadId => {\n    if (!leadId) return;\n    console.log(`🎯 [ABRIR CONVERSA] Tentando abrir conversa para lead_id: ${leadId}`);\n    console.log(`📊 [ABRIR CONVERSA] Contexto atual:`, {\n      chatsCarregados: chats.length,\n      empresaID,\n      token: token ? 'presente' : 'ausente',\n      isMobile,\n      showMobileChat\n    });\n    try {\n      // **ESTRATÉGIA 1: Procurar na lista atual**\n      const chatNaLista = chats.find(chat => chat._id === leadId);\n      if (chatNaLista) {\n        console.log(`✅ Chat encontrado na lista atual: ${chatNaLista.name}`);\n        setSelectedChat(chatNaLista);\n        if (isMobile) {\n          setShowMobileChat(true);\n        }\n        // Marcar como lido se necessário\n        if (unreadCounts[leadId] && unreadCounts[leadId] > 0) {\n          markChatAsRead(leadId);\n        }\n        return;\n      }\n\n      // **ESTRATÉGIA 2: Buscar especificamente na API**\n      console.log(`🔍 Chat não encontrado na lista, buscando na API...`);\n      try {\n        const response = await getWhatsappChatById(empresaID, leadId, token);\n        if (response.data && response.data.status === 200 && response.data.chat) {\n          const chatEncontrado = response.data.chat;\n          console.log(`✅ Chat encontrado na API: ${chatEncontrado.name}`);\n\n          // Adicionar o chat à lista no início para futuras consultas\n          setChats(prevChats => {\n            const chatJaExiste = prevChats.some(chat => chat._id === leadId);\n            if (!chatJaExiste) {\n              return [chatEncontrado, ...prevChats];\n            }\n            return prevChats;\n          });\n\n          // Selecionar o chat\n          setSelectedChat(chatEncontrado);\n          if (isMobile) {\n            setShowMobileChat(true);\n          }\n\n          // Mostrar toast de sucesso\n          toast.success(`💬 Conversa encontrada: ${chatEncontrado.name}`, {\n            position: \"top-right\",\n            autoClose: 3000,\n            hideProgressBar: false,\n            closeOnClick: true,\n            pauseOnHover: true,\n            draggable: true\n          });\n          return;\n        }\n      } catch (apiError) {\n        var _apiError$response;\n        console.warn(`⚠️ Erro ao buscar chat na API:`, apiError);\n\n        // Se o erro for 404, significa que o chat não existe mais\n        if (((_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status) === 404) {\n          console.log(`📭 Chat não existe mais no sistema`);\n        }\n      }\n\n      // **ESTRATÉGIA 3: Buscar por texto em todas as conversas**\n      console.log(`🔍 Tentando busca textual em todas as conversas...`);\n      try {\n        var _searchResponse$data, _searchResponse$data$, _searchResponse$data$2;\n        // Fazer uma busca mais ampla sem paginação para encontrar a conversa\n        const searchResponse = await getWhatsappChats(empresaID, 0, 100, '', token);\n        if ((_searchResponse$data = searchResponse.data) !== null && _searchResponse$data !== void 0 && (_searchResponse$data$ = _searchResponse$data.messages) !== null && _searchResponse$data$ !== void 0 && (_searchResponse$data$2 = _searchResponse$data$[0]) !== null && _searchResponse$data$2 !== void 0 && _searchResponse$data$2.data) {\n          const todasConversas = searchResponse.data.messages[0].data;\n          const chatEncontradoPorBusca = todasConversas.find(chat => chat._id === leadId);\n          if (chatEncontradoPorBusca) {\n            console.log(`✅ Chat encontrado por busca ampla: ${chatEncontradoPorBusca.name}`);\n\n            // Adicionar à lista atual\n            setChats(prevChats => {\n              const chatJaExiste = prevChats.some(chat => chat._id === leadId);\n              if (!chatJaExiste) {\n                return [chatEncontradoPorBusca, ...prevChats];\n              }\n              return prevChats;\n            });\n\n            // Selecionar o chat\n            setSelectedChat(chatEncontradoPorBusca);\n            if (isMobile) {\n              setShowMobileChat(true);\n            }\n            toast.success(`💬 Conversa localizada: ${chatEncontradoPorBusca.name}`, {\n              position: \"top-right\",\n              autoClose: 3000\n            });\n            return;\n          }\n        }\n      } catch (searchError) {\n        console.warn(`⚠️ Erro na busca ampla:`, searchError);\n      }\n\n      // **ESTRATÉGIA 4: Última tentativa - criar nova conversa**\n      console.log(`❌ Chat não encontrado em lugar nenhum para lead_id: ${leadId}`);\n\n      // Mostrar modal de confirmação para criar nova conversa\n      const confirmarNovaConversa = window.confirm('❓ Conversa não encontrada.\\n\\n' + 'Isso pode acontecer quando:\\n' + '• A conversa é muito antiga\\n' + '• O contato foi removido do WhatsApp\\n' + '• Houve algum problema na sincronização\\n\\n' + 'Deseja tentar localizar o contato e iniciar uma nova conversa?');\n      if (confirmarNovaConversa) {\n        // Tentar extrair informações do location.state se disponível\n        const stateData = location.state;\n        if (stateData && (stateData.nome || stateData.celular)) {\n          console.log(`🆕 Tentando criar nova conversa com dados do atendimento:`, stateData);\n\n          // Formatar número corretamente (adicionar código do país se necessário)\n          let numeroFormatado = stateData.celular;\n          if (numeroFormatado && !numeroFormatado.startsWith('55')) {\n            numeroFormatado = '55' + numeroFormatado;\n          }\n          try {\n            var _createResponse$data;\n            const createResponse = await createWhatsappChat(empresaID, numeroFormatado, stateData.nome, token);\n            if (((_createResponse$data = createResponse.data) === null || _createResponse$data === void 0 ? void 0 : _createResponse$data.status) === 200 && createResponse.data.chat) {\n              const novaConversa = createResponse.data.chat;\n              console.log(`✅ Nova conversa criada: ${novaConversa.name}`);\n\n              // Adicionar à lista\n              setChats(prevChats => [novaConversa, ...prevChats]);\n\n              // Selecionar\n              setSelectedChat(novaConversa);\n              if (isMobile) {\n                setShowMobileChat(true);\n              }\n              toast.success(`🎉 Nova conversa iniciada com ${novaConversa.name}!`, {\n                position: \"top-right\",\n                autoClose: 4000\n              });\n              return;\n            }\n          } catch (createError) {\n            console.error(`❌ Erro ao criar nova conversa:`, createError);\n            toast.error(`❌ Não foi possível criar nova conversa. Verifique se o número ainda existe.`, {\n              position: \"top-right\",\n              autoClose: 5000\n            });\n          }\n        } else {\n          toast.info(`ℹ️ Não há dados suficientes para criar uma nova conversa. Tente localizar o contato manualmente.`, {\n            position: \"top-right\",\n            autoClose: 5000\n          });\n        }\n      }\n\n      // Toast final se nada funcionou\n      if (!confirmarNovaConversa) {\n        toast.warning(`⚠️ Conversa não localizada. Pode ser uma conversa muito antiga ou o contato não existe mais.`, {\n          position: \"top-right\",\n          autoClose: 6000,\n          hideProgressBar: false,\n          closeOnClick: true,\n          pauseOnHover: true,\n          draggable: true\n        });\n      }\n    } catch (error) {\n      console.error(`❌ Erro geral ao tentar abrir conversa:`, error);\n      toast.error(`❌ Erro ao abrir conversa. Tente novamente.`, {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n    }\n  }, [chats, empresaID, token, isMobile, unreadCounts, markChatAsRead, location.state]);\n\n  // **📌 Verificar se há um chat correspondente ao lead_id**\n  useEffect(() => {\n    if (leadIdParaAbrir && chats.length > 0) {\n      // Usar a nova função robusta\n      abrirConversaPorLeadId(leadIdParaAbrir);\n    }\n  }, [leadIdParaAbrir, chats.length, abrirConversaPorLeadId]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Teste, {\n      sidebar: sidebar,\n      children: [!isLoged ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"contentItemComplete\",\n        style: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"input-group inputGroup-adicinaItem container-whatsapp-custom\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"formGroupRow\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: 'white',\n                padding: isMobile ? '15px' : '20px',\n                display: 'flex',\n                flexDirection: 'column',\n                gap: isMobile ? '15px' : '20px',\n                margin: 'auto',\n                maxWidth: isMobile ? '100%' : 'none'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                style: {\n                  fontSize: isMobile ? '1.3rem' : '1.5rem',\n                  textAlign: isMobile ? 'center' : 'left',\n                  marginBottom: isMobile ? '10px' : '15px'\n                },\n                children: \"Para sincronizar o WhatsApp fa\\xE7a os seguintes passos\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2925,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n                style: {\n                  paddingLeft: isMobile ? '15px' : '20px',\n                  fontSize: isMobile ? '14px' : '16px',\n                  lineHeight: '1.5'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  style: {\n                    marginBottom: isMobile ? '8px' : '5px'\n                  },\n                  children: \"Abra o WhatsApp no seu celular.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2935,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: {\n                    marginBottom: isMobile ? '8px' : '5px'\n                  },\n                  children: \"Toque em Mais op\\xE7\\xF5es no Android ou em Configura\\xE7\\xF5es no iPhone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2936,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: {\n                    marginBottom: isMobile ? '8px' : '5px'\n                  },\n                  children: \"Toque em Dispositivos conectados e, em seguida, em Conectar dispositivo.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2937,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: {\n                    marginBottom: isMobile ? '8px' : '5px'\n                  },\n                  children: \"Aponte seu celular para esta tela para escanear o QR code.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2938,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2930,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'center',\n                  margin: '20px 0'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: isMobile ? '20px' : '30px',\n                    backgroundColor: 'white',\n                    border: '1px solid #E0E0E0',\n                    borderRadius: '12px',\n                    maxWidth: '100%',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: isMobile ? '260px' : '320px',\n                      height: isMobile ? '260px' : '320px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      borderRadius: '8px',\n                      maxWidth: '100%',\n                      backgroundColor: '#fafafa',\n                      border: '1px solid #f0f0f0'\n                    },\n                    children: generatingQrCode ? /*#__PURE__*/_jsxDEV(Loading, {\n                      type: \"spinningBubbles\",\n                      className: \"generatingQrCodeLoading\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2961,\n                      columnNumber: 46\n                    }, this) : qrCodeImg ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: `${qrCodeImg}`,\n                        alt: \"QR Code\",\n                        style: {\n                          maxWidth: '100%',\n                          maxHeight: '100%',\n                          width: 'auto',\n                          height: 'auto',\n                          opacity: qrCodeExpired ? 0.5 : 1,\n                          transition: 'opacity 0.3s ease'\n                        },\n                        onLoad: () => console.log('QR Code carregado com sucesso'),\n                        onError: e => {\n                          console.error('Erro ao carregar QR Code:', e);\n                          console.log('URL da imagem:', qrCodeImg);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2965,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginTop: '15px'\n                        },\n                        children: [!qrCodeExpired && qrCodeTimeLeft > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            color: qrCodeTimeLeft <= 10 ? '#e74c3c' : '#666',\n                            fontSize: '13px',\n                            marginBottom: '10px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '5px',\n                            fontWeight: qrCodeTimeLeft <= 10 ? '600' : 'normal'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"\\u23F1\\uFE0F\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2996,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: [\"Expira em: \", Math.floor(qrCodeTimeLeft / 60), \":\", (qrCodeTimeLeft % 60).toString().padStart(2, '0'), qrCodeTimeLeft <= 10 && ' ⚠️']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2997,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2986,\n                          columnNumber: 37\n                        }, this), qrCodeExpired && !generatingQrCode && /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            color: '#e74c3c',\n                            fontSize: '14px',\n                            marginBottom: '10px',\n                            fontWeight: '500',\n                            textAlign: 'center'\n                          },\n                          children: [\"\\u26A0\\uFE0F QR Code expirado\", /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              fontSize: '12px',\n                              color: '#666',\n                              marginTop: '5px',\n                              fontWeight: 'normal'\n                            },\n                            children: \"Clique para gerar um novo\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 3013,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 3005,\n                          columnNumber: 37\n                        }, this), generatingQrCode && /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            color: '#25D366',\n                            fontSize: '14px',\n                            marginBottom: '10px',\n                            fontWeight: '500',\n                            textAlign: 'center',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '8px'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Loading, {\n                            type: \"spin\",\n                            color: \"#25D366\",\n                            height: 16,\n                            width: 16\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 3036,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Gerando novo QR Code...\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 3037,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 3025,\n                          columnNumber: 37\n                        }, this), (qrCodeExpired || showRegenerateButton) && /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => regenerateQrCodeViaAPI('manual'),\n                          disabled: generatingQrCode,\n                          style: {\n                            backgroundColor: generatingQrCode ? '#95a5a6' : '#25D366',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '8px',\n                            padding: '12px 24px',\n                            cursor: generatingQrCode ? 'not-allowed' : 'pointer',\n                            fontSize: '14px',\n                            fontWeight: '500',\n                            transition: 'all 0.3s ease',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '8px',\n                            margin: '0 auto'\n                          },\n                          children: generatingQrCode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"\\u23F3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 3064,\n                              columnNumber: 43\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Gerando...\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 3065,\n                              columnNumber: 43\n                            }, this)]\n                          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"\\uD83D\\uDD04\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 3069,\n                              columnNumber: 43\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: \"Gerar novo QR Code\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 3070,\n                              columnNumber: 43\n                            }, this)]\n                          }, void 0, true)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 3042,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2984,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2964,\n                      columnNumber: 31\n                    }, this) : userRequestedQrCode ?\n                    /*#__PURE__*/\n                    // 🔧 NOVO: Estado quando usuário solicitou QR Code mas ainda está carregando\n                    _jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        flexDirection: 'column',\n                        gap: '15px',\n                        color: '#666',\n                        textAlign: 'center',\n                        padding: '20px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Loading, {\n                        type: \"spin\",\n                        color: \"#25D366\",\n                        height: 40,\n                        width: 40\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3089,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: isMobile ? '14px' : '16px',\n                          fontWeight: '500'\n                        },\n                        children: \"Gerando QR Code...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3090,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: isMobile ? '12px' : '14px',\n                          color: '#999'\n                        },\n                        children: \"Aguarde enquanto preparamos seu c\\xF3digo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3093,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 3079,\n                      columnNumber: 31\n                    }, this) :\n                    /*#__PURE__*/\n                    // 🔧 NOVO: Estado inicial - Botão para gerar QR Code manualmente\n                    _jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        flexDirection: 'column',\n                        gap: '20px',\n                        color: '#666',\n                        textAlign: 'center',\n                        padding: '20px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '48px',\n                          color: '#25D366',\n                          marginBottom: '10px'\n                        },\n                        children: \"\\uD83D\\uDCF1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3109,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: isMobile ? '16px' : '18px',\n                          fontWeight: '500',\n                          color: '#333'\n                        },\n                        children: \"Pronto para conectar?\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3116,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: isMobile ? '13px' : '14px',\n                          color: '#666',\n                          lineHeight: '1.5',\n                          marginBottom: '10px'\n                        },\n                        children: \"Clique no bot\\xE3o abaixo para gerar seu QR Code e conectar o WhatsApp\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3119,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: generateQrCodeManually,\n                        style: {\n                          backgroundColor: '#25D366',\n                          color: 'white',\n                          border: 'none',\n                          borderRadius: '12px',\n                          padding: '16px 32px',\n                          cursor: 'pointer',\n                          fontSize: '16px',\n                          fontWeight: '600',\n                          transition: 'all 0.3s ease',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          gap: '10px',\n                          boxShadow: '0 4px 12px rgba(37, 211, 102, 0.3)',\n                          minWidth: '200px'\n                        },\n                        onMouseOver: e => {\n                          e.target.style.backgroundColor = '#128C7E';\n                          e.target.style.transform = 'translateY(-2px)';\n                        },\n                        onMouseOut: e => {\n                          e.target.style.backgroundColor = '#25D366';\n                          e.target.style.transform = 'translateY(0)';\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            fontSize: '20px'\n                          },\n                          children: \"\\uD83D\\uDCF1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 3155,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Gerar QR Code\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 3156,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3127,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 3099,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2949,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2941,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2940,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2916,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2915,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2914,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2913,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Container, {\n        children: [/*#__PURE__*/_jsxDEV(WrapperContainerAll, {\n          style: {\n            marginTop: window.innerWidth < 1024 ? '25px' : '0px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(WrapperHMC, {\n            isMobile: isMobile,\n            showMobileChat: showMobileChat,\n            children: [/*#__PURE__*/_jsxDEV(WrapperListHeader, {\n              children: /*#__PURE__*/_jsxDEV(HeaderListHeader, {\n                children: [/*#__PURE__*/_jsxDEV(UserImage, {\n                  onClick: openMenu,\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: myProfilePicture || (userParse === null || userParse === void 0 ? void 0 : userParse.user_img[0]) || userIcon,\n                    width: \"35\",\n                    height: \"35\",\n                    alt: \"Foto de perfil do usu\\xE1rio\",\n                    onError: e => {\n                      // Se a imagem falhar ao carregar, tentar buscar uma nova\n                      if (e.target.src !== userIcon) {\n                        console.log(`❌ Erro ao carregar minha imagem de perfil, buscando nova...`);\n                        e.target.src = userIcon;\n                        fetchMyProfilePicture(true); // Forçar atualização\n                      }\n                    },\n                    onDoubleClick: () => {\n                      // Duplo clique para forçar atualização da imagem\n                      console.log(`🔄 Forçando atualização da minha imagem de perfil`);\n                      fetchMyProfilePicture(true);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3177,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3176,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Actions, {\n                  ref: menuRef,\n                  children: [/*#__PURE__*/_jsxDEV(StatusIcon, {\n                    role: \"img\",\n                    \"aria-label\": \"\\xCDcone de abrir status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3198,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(ChatIcon, {\n                    role: \"img\",\n                    \"aria-label\": \"\\xCDcone de iniciar nova conversa\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3199,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(MenuIcon, {\n                    role: \"img\",\n                    \"aria-label\": \"\\xCDcone de expandir menu\",\n                    onClick: () => setIsMenuOpen(true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3200,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3197,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3175,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(WrapperMenu, {\n              className: isMenuOpen ? \"active\" : \"\",\n              children: /*#__PURE__*/_jsxDEV(ActionWrapperMenu, {\n                children: mockOptions.map(({\n                  id,\n                  action\n                }) => /*#__PURE__*/_jsxDEV(ActionMenu, {\n                  onClick: action === \"Desconectar\" ? handleDisconnect : openMenu,\n                  className: action === \"Desconectar\" ? \"\" : \"\" // Apenas desativa se necessário\n                  ,\n                  children: action\n                }, id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3212,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3210,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(SearchBar, {\n              searchQuery: searchQuery,\n              setSearchQuery: setSearchQuery\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3223,\n              columnNumber: 17\n            }, this), preloadingChats && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '8px 16px',\n                backgroundColor: '#f0f8f0',\n                borderLeft: '3px solid #25D366',\n                fontSize: '12px',\n                color: '#666',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Loading, {\n                type: \"spin\",\n                color: \"#25D366\",\n                height: 12,\n                width: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3237,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Pr\\xE9-carregando conversas... (\", preloadProgress.current, \"/\", preloadProgress.total, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3238,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3227,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"containerChatsWhatsapp\",\n              ref: chatsContainerRef,\n              children: [loading && chats.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  padding: '40px 20px',\n                  color: '#666'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Loading, {\n                  type: \"spin\",\n                  color: \"#25D366\",\n                  height: 40,\n                  width: 40\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3256,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    marginTop: '15px',\n                    fontSize: '14px'\n                  },\n                  children: \"Carregando conversas...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3257,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3248,\n                columnNumber: 21\n              }, this), chats.map(chat => {\n                var _chat$channel_data5, _chat$channel_data5$w;\n                return /*#__PURE__*/_jsxDEV(WrapperListContacts, {\n                  isSelected: chat._id === (selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat._id),\n                  onClick: () => {\n                    if (isMobile) {\n                      handleSelectChatMobile(chat);\n                    } else {\n                      setSelectedChat(chat);\n                      // **📌 MARCAR MENSAGENS COMO LIDAS AO SELECIONAR CHAT (DESKTOP)**\n                      if (unreadCounts[chat._id] && unreadCounts[chat._id] > 0) {\n                        markChatAsRead(chat._id);\n                      }\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ContactPhotoListContacts, {\n                    children: /*#__PURE__*/_jsxDEV(UnreadBadge, {\n                      count: unreadCounts[chat._id] || 0,\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: ((_chat$channel_data5 = chat.channel_data) === null || _chat$channel_data5 === void 0 ? void 0 : (_chat$channel_data5$w = _chat$channel_data5.whatsapp) === null || _chat$channel_data5$w === void 0 ? void 0 : _chat$channel_data5$w.profile_picture) || userIcon,\n                        width: \"60\",\n                        alt: \"Foto de perfil do usu\\xE1rio\",\n                        onError: e => {\n                          // Se a imagem falhar ao carregar, tentar buscar uma nova\n                          if (e.target.src !== userIcon) {\n                            console.log(`❌ Erro ao carregar imagem de perfil para ${chat.name}, buscando nova...`);\n                            e.target.src = userIcon;\n                            fetchProfilePicture(chat);\n                          }\n                        },\n                        onDoubleClick: () => {\n                          // Duplo clique para forçar atualização da imagem\n                          console.log(`🔄 Forçando atualização da imagem de perfil para ${chat.name}`);\n                          fetchProfilePicture(chat, true); // forceUpdate = true\n                        },\n                        title: \"Duplo clique para atualizar foto de perfil\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3277,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 3276,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3275,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(MessageDataWrapper, {\n                    children: [/*#__PURE__*/_jsxDEV(ContactNameAndTime, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        onDoubleClick: () => {\n                          console.log(`🔄 Atualizando informações do contato ${chat.name} via duplo clique`);\n                          refreshContactInfo(chat._id, chat.name);\n                        },\n                        title: \"Duplo clique para atualizar informa\\xE7\\xF5es do contato\",\n                        style: {\n                          cursor: 'pointer',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '5px'\n                        },\n                        children: [chat.name, hasGenericName(chat.name, chat.mobile_number) && /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => {\n                            e.stopPropagation();\n                            refreshContactInfo(chat._id, chat.name);\n                          },\n                          style: {\n                            background: 'transparent',\n                            border: 'none',\n                            cursor: 'pointer',\n                            color: '#FFA500',\n                            fontSize: '0.8rem',\n                            padding: '0',\n                            margin: '0',\n                            opacity: 0.7,\n                            transition: 'opacity 0.2s ease'\n                          },\n                          onMouseOver: e => e.target.style.opacity = 1,\n                          onMouseOut: e => e.target.style.opacity = 0.7,\n                          title: \"Este contato tem um nome gen\\xE9rico. Clique para buscar o nome real.\",\n                          children: \"\\uD83D\\uDD0D\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 3310,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3300,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: chat.message ? formatDate(chat.message.messageDate || chat.message.createdAt) : ''\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3334,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 3299,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(ContactMessage, {\n                      children: [chat.message && chat.message.fromMe && /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3337,\n                        columnNumber: 67\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: chat.message ? chat.message.message || chat.message.text || 'Nova conversa' : 'Nova conversa'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3338,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 3336,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3298,\n                    columnNumber: 23\n                  }, this)]\n                }, chat._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3262,\n                  columnNumber: 21\n                }, this);\n              }), loadingMore && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"loadingChats-indicator\",\n                children: \"\\uD83D\\uDD04 Carregando mais conversas...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3345,\n                columnNumber: 35\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3245,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3172,\n            columnNumber: 15\n          }, this), selectedChat && (!isMobile || showMobileChat) ? /*#__PURE__*/_jsxDEV(WrapperContainerConversa, {\n            isMobile: isMobile,\n            showMobileChat: showMobileChat,\n            backgroundLoaded: backgroundLoaded,\n            children: [/*#__PURE__*/_jsxDEV(Header, {\n              children: [/*#__PURE__*/_jsxDEV(MobileBackButton, {\n                onClick: handleBackToList,\n                children: /*#__PURE__*/_jsxDEV(BackIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3359,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3358,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ContactPhoto, {\n                onClick: handleOpenProfile,\n                style: {\n                  cursor: \"pointer\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: ((_selectedChat$channel = selectedChat.channel_data) === null || _selectedChat$channel === void 0 ? void 0 : (_selectedChat$channel2 = _selectedChat$channel.whatsapp) === null || _selectedChat$channel2 === void 0 ? void 0 : _selectedChat$channel2.profile_picture) || userIcon,\n                  width: \"40\",\n                  alt: \"Foto de perfil do usu\\xE1rio\",\n                  onError: e => {\n                    // Se a imagem falhar ao carregar, tentar buscar uma nova\n                    if (e.target.src !== userIcon) {\n                      console.log(`❌ Erro ao carregar imagem de perfil para ${selectedChat.name}, buscando nova...`);\n                      e.target.src = userIcon;\n                      fetchProfilePicture(selectedChat);\n                    }\n                  },\n                  onDoubleClick: e => {\n                    // Duplo clique para forçar atualização da imagem (evitar abrir perfil)\n                    e.stopPropagation();\n                    console.log(`🔄 Forçando atualização da imagem de perfil para ${selectedChat.name}`);\n                    fetchProfilePicture(selectedChat, true); // forceUpdate = true\n                  },\n                  title: \"Duplo clique para atualizar foto de perfil\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3362,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3361,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ContactName, {\n                children: [selectedChat.name, /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => refreshContactInfo(selectedChat._id, selectedChat.name),\n                  style: {\n                    background: 'transparent',\n                    border: 'none',\n                    marginLeft: '10px',\n                    cursor: 'pointer',\n                    color: '#666',\n                    fontSize: '1.2rem',\n                    padding: '2px',\n                    borderRadius: '3px',\n                    transition: 'color 0.2s ease'\n                  },\n                  onMouseOver: e => e.target.style.color = '#25D366',\n                  onMouseOut: e => e.target.style.color = '#666',\n                  title: \"Atualizar informa\\xE7\\xF5es do contato\",\n                  children: \"\\uD83D\\uDD04 Atualizar informa\\xE7\\xF5es do contato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3385,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3383,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3357,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(WrapperContent, {\n              ref: messagesContainerRef,\n              onScroll: handleMessagesScroll,\n              children: loadingMessages ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  height: '100%',\n                  color: '#666',\n                  gap: '15px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Loading, {\n                  type: \"spin\",\n                  color: \"#25D366\",\n                  height: 40,\n                  width: 40\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3421,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    fontSize: '14px',\n                    textAlign: 'center'\n                  },\n                  children: [\"Carregando mensagens de \", selectedChat.name, \"...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3422,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3412,\n                columnNumber: 23\n              }, this) : Object.keys(groupedMessages).length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [loadingMoreMessages && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    padding: '15px',\n                    color: '#666',\n                    fontSize: '14px',\n                    gap: '10px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Loading, {\n                    type: \"spin\",\n                    color: \"#25D366\",\n                    height: 20,\n                    width: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3439,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Carregando mensagens anteriores...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3440,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3430,\n                  columnNumber: 27\n                }, this), !hasMoreMessages && messagesLeadChannel.length > 30 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    padding: '15px',\n                    color: '#999',\n                    fontSize: '12px',\n                    fontStyle: 'italic'\n                  },\n                  children: \"\\uD83D\\uDCDC In\\xEDcio da conversa\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3446,\n                  columnNumber: 27\n                }, this), Object.keys(groupedMessages).map(date => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(ChatDate, {\n                    children: moment(date).calendar(null, {\n                      sameDay: '[Hoje]',\n                      lastDay: '[Ontem]',\n                      lastWeek: 'dddd',\n                      sameElse: 'DD/MM/YYYY'\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3461,\n                    columnNumber: 29\n                  }, this), groupedMessages[date].map(message => {\n                    const {\n                      _id,\n                      text,\n                      createdAt,\n                      fromMe,\n                      isOptimistic,\n                      status\n                    } = message;\n                    return /*#__PURE__*/_jsxDEV(Message, {\n                      fromMe: fromMe,\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        children: text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3471,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '4px'\n                        },\n                        children: [moment(createdAt).format('HH:mm'), fromMe && isOptimistic && status === 'sending' && /*#__PURE__*/_jsxDEV(\"span\", {\n                          title: \"Enviando...\",\n                          style: {\n                            color: '#999',\n                            fontSize: '10px'\n                          },\n                          children: \"\\u23F3\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 3475,\n                          columnNumber: 39\n                        }, this), fromMe && isOptimistic && status === 'sent' && /*#__PURE__*/_jsxDEV(\"span\", {\n                          title: \"Enviado\",\n                          style: {\n                            color: '#25D366',\n                            fontSize: '10px'\n                          },\n                          children: \"\\u2713\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 3478,\n                          columnNumber: 39\n                        }, this), fromMe && !isOptimistic && /*#__PURE__*/_jsxDEV(\"span\", {\n                          title: \"Entregue\",\n                          style: {\n                            color: '#25D366',\n                            fontSize: '10px'\n                          },\n                          children: \"\\u2713\\u2713\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 3481,\n                          columnNumber: 39\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 3472,\n                        columnNumber: 35\n                      }, this)]\n                    }, _id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 3470,\n                      columnNumber: 33\n                    }, this);\n                  })]\n                }, date, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3460,\n                  columnNumber: 27\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  ref: messageEndRef\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3489,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  height: '100%',\n                  color: '#999',\n                  textAlign: 'center',\n                  padding: '40px 20px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '48px',\n                    marginBottom: '20px',\n                    opacity: 0.5\n                  },\n                  children: \"\\uD83D\\uDCAC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3502,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    fontSize: '18px',\n                    marginBottom: '8px',\n                    fontWeight: '500',\n                    color: '#666'\n                  },\n                  children: \"Nenhuma mensagem ainda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3509,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    fontSize: '14px',\n                    lineHeight: '1.4',\n                    maxWidth: '280px'\n                  },\n                  children: [\"Esta \\xE9 uma nova conversa com \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedChat.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3522,\n                    columnNumber: 56\n                  }, this), \". Envie uma mensagem para come\\xE7ar!\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3517,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3492,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3407,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(WrapperChat, {\n              children: /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSubmit,\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 10\n                },\n                children: [/*#__PURE__*/_jsxDEV(Input, {\n                  type: \"text\",\n                  placeholder: \"Digite uma mensagem\",\n                  onChange: ({\n                    target\n                  }) => setMessage(target.value),\n                  value: message,\n                  style: {\n                    fontSize: isMobile ? '16px' : '14px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3531,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  disabled: isSubmitting || !message.trim(),\n                  style: {\n                    background: \"transparent\",\n                    border: \"none\",\n                    cursor: isSubmitting || !message.trim() ? \"not-allowed\" : \"pointer\",\n                    color: isSubmitting || !message.trim() ? \"#ccc\" : \"#25D366\",\n                    // Cor do ícone (verde do WhatsApp)\n                    fontSize: isMobile ? \"1.8rem\" : \"2rem\",\n                    opacity: isSubmitting || !message.trim() ? 0.6 : 1\n                  },\n                  children: isSubmitting ? /*#__PURE__*/_jsxDEV(Loading, {\n                    type: \"spin\",\n                    color: \"#25D366\",\n                    height: 20,\n                    width: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3552,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(FiSend, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3554,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3539,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleToggleBot(selectedChat._id),\n                  style: {\n                    background: \"transparent\",\n                    border: botPausado ? \"1px solid #FF4D4D\" : \"1px solid #25D366\",\n                    cursor: \"pointer\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 5,\n                    color: botPausado ? \"#FF4D4D\" : \"#25D366\",\n                    // Vermelho se pausado, branco se ativo\n                    fontSize: isMobile ? \"12px\" : \"14px\",\n                    fontWeight: '600',\n                    minWidth: isMobile ? 120 : 140,\n                    justifyContent: 'center',\n                    borderRadius: 8,\n                    padding: isMobile ? '8px' : '10px'\n                  },\n                  children: [botPausado ? /*#__PURE__*/_jsxDEV(FiPauseCircle, {\n                    style: {\n                      fontSize: 17\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3578,\n                    columnNumber: 39\n                  }, this) : /*#__PURE__*/_jsxDEV(FiPlayCircle, {\n                    style: {\n                      fontSize: 17\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3578,\n                    columnNumber: 84\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: botPausado ? \"Robô Pausado\" : \"Robô Ativado\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3579,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3559,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3530,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3529,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(UserProfile, {\n              isOpen: isProfileOpen,\n              onClose: () => setIsProfileOpen(false),\n              user: selectedChat\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3584,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3351,\n            columnNumber: 17\n          }, this) : (!isMobile || !showMobileChat) && /*#__PURE__*/_jsxDEV(WrapperContainerConversa, {\n            backgroundLoaded: backgroundLoaded,\n            style: {\n              background: 'white',\n              justifyContent: 'center',\n              display: isMobile ? 'none' : 'flex',\n              flexDirection: 'column',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaWhatsapp, {\n              style: {\n                fontSize: '100px',\n                color: 'rgba(0, 0, 0, 0.45)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3597,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Warn, {\n              style: {\n                background: 'white'\n              },\n              children: \"Selecione uma conversa para come\\xE7ar a conversar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3598,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3587,\n            columnNumber: 51\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3170,\n          columnNumber: 13\n        }, this), isOpen && /*#__PURE__*/_jsxDEV(S.Wrapper, {\n          children: [/*#__PURE__*/_jsxDEV(S.Header, {\n            children: /*#__PURE__*/_jsxDEV(S.Back, {\n              children: [/*#__PURE__*/_jsxDEV(BackIcon, {\n                onClick: closeMenu\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3608,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Perfil\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3609,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3607,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3606,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(S.UserImage, {\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: myProfilePicture || (userParse === null || userParse === void 0 ? void 0 : userParse.user_img[0]) || userIcon,\n              width: \"200\",\n              height: \"200\",\n              alt: \"Foto de perfil do usu\\xE1rio\",\n              onError: e => {\n                // Se a imagem falhar ao carregar, tentar buscar uma nova\n                if (e.target.src !== userIcon) {\n                  console.log(`❌ Erro ao carregar minha imagem de perfil no modal, buscando nova...`);\n                  e.target.src = userIcon;\n                  fetchMyProfilePicture(true); // Forçar atualização\n                }\n              },\n              onDoubleClick: () => {\n                // Duplo clique para forçar atualização da imagem\n                console.log(`🔄 Forçando atualização da minha imagem de perfil no modal`);\n                fetchMyProfilePicture(true);\n              },\n              style: {\n                cursor: 'pointer'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3614,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3613,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(S.UserData, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Nome\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3637,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: empresaParse.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3638,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3636,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(S.UserData, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Recado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3642,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Ol\\xE1! Eu estou usando o WhatsApp.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3643,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3641,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3605,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3169,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(GlobalStyles, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 3650,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2911,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s2(WhatsAppWeb, \"T1Hw6POrTys0tVc9TshCB4fxijA=\", false, function () {\n  return [useLocation, useIsMobile, useImagePreloader];\n});\n_c24 = WhatsAppWeb;\nexport default WhatsAppWeb;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24;\n$RefreshReg$(_c, \"WrapperContainerAll\");\n$RefreshReg$(_c2, \"WrapperContainerConversa\");\n$RefreshReg$(_c3, \"WrapperContent\");\n$RefreshReg$(_c4, \"ChatDate\");\n$RefreshReg$(_c5, \"Message\");\n$RefreshReg$(_c6, \"Warn\");\n$RefreshReg$(_c7, \"WrapperHMC\");\n$RefreshReg$(_c8, \"WrapperMenu\");\n$RefreshReg$(_c9, \"ActionWrapperMenu\");\n$RefreshReg$(_c0, \"ActionMenu\");\n$RefreshReg$(_c1, \"Header\");\n$RefreshReg$(_c10, \"ContactPhoto\");\n$RefreshReg$(_c11, \"ContactName\");\n$RefreshReg$(_c12, \"MobileBackButton\");\n$RefreshReg$(_c13, \"WrapperChat\");\n$RefreshReg$(_c14, \"WrapperListContacts\");\n$RefreshReg$(_c15, \"ContactPhotoListContacts\");\n$RefreshReg$(_c16, \"ContactNameAndTime\");\n$RefreshReg$(_c17, \"ContactMessage\");\n$RefreshReg$(_c18, \"MessageDataWrapper\");\n$RefreshReg$(_c19, \"WrapperListHeader\");\n$RefreshReg$(_c20, \"HeaderListHeader\");\n$RefreshReg$(_c21, \"UserImage\");\n$RefreshReg$(_c22, \"Actions\");\n$RefreshReg$(_c23, \"Teste\");\n$RefreshReg$(_c24, \"WhatsAppWeb\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "useCallback", "useRef", "useLocation", "LeftMenu", "styled", "Profile", "Cha<PERSON>", "io", "mockMessages", "mockOptions", "contactUserPhoto", "backgroundChat", "SearchBar", "S", "UserProfile", "SidebarContext", "MenuProfileContext", "getQrCodeWhatsapp", "renewQrCodeWhatsapp", "getWhatsappChats", "getWhatsappChatLead", "sendMessage", "sendTextMessageWhatsapp", "removeWhatsappSession", "toggleBotStatus", "getBotStatusLead", "getWhatsappProfilePicture", "getMyWhatsappProfilePicture", "getWhatsappUnreadCount", "markWhatsappMessagesAsRead", "getWhatsappConnectionStatus", "refreshContactInfoWhatsapp", "getWhatsappChatById", "createWhatsappChat", "Loading", "CryptoJS", "moment", "useImagePreloader", "userIcon", "ReactComponent", "CheckIcon", "StatusIcon", "ChatIcon", "MenuIcon", "BackIcon", "FaWhatsapp", "FiSend", "FiPauseCircle", "FiPlayCircle", "toast", "Input", "UnreadBadge", "GlobalStyles", "Container", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "useIsMobile", "_s", "isMobile", "setIsMobile", "window", "innerWidth", "onResize", "addEventListener", "removeEventListener", "WrapperContainerAll", "div", "_c", "WrapperContainerConversa", "props", "backgroundLoaded", "showMobileChat", "_c2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c3", "ChatDate", "time", "_c4", "Message", "fromMe", "_c5", "<PERSON><PERSON>", "_c6", "WrapperHMC", "aside", "_c7", "WrapperMenu", "_c8", "ActionWrapperMenu", "ul", "_c9", "ActionMenu", "li", "_c0", "Header", "header", "_c1", "ContactPhoto", "_c10", "ContactName", "span", "_c11", "MobileBackButton", "button", "_c12", "WrapperChat", "_c13", "WrapperListContacts", "isSelected", "_c14", "ContactPhotoListContacts", "_c15", "ContactNameAndTime", "_c16", "ContactMessage", "_c17", "MessageDataWrapper", "_c18", "Wrapper<PERSON>ist<PERSON><PERSON>er", "_c19", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c20", "UserImage", "_c21", "Actions", "_c22", "<PERSON>e", "sidebar", "_c23", "groupMessagesByDate", "messages", "reduce", "acc", "message", "date", "createdAt", "format", "push", "generateObjectId", "Math", "floor", "Date", "now", "toString", "replace", "random", "toLowerCase", "WhatsAppWeb", "_s2", "_location$state", "_selectedChat$channel", "_selectedChat$channel2", "locale", "secret<PERSON>ey", "userEncrypted", "localStorage", "getItem", "user", "AES", "decrypt", "enc", "Utf8", "userParse", "JSON", "parse", "userID", "_id", "empresa", "empresaParse", "empresaID", "token", "location", "leadIdParaAbrir", "state", "lead_id", "setShowMobileChat", "isMenuOpen", "setIsMenuOpen", "isOpen", "openMenu", "closeMenu", "menuRef", "setMessage", "setSidebar", "isSubmitting", "setIsSubmitting", "qrCodeImg", "setQrCodeImg", "generatingQrCode", "setGeneratingQrCode", "isLoged", "setIsLoged", "qrCodeExpired", "setQrCodeExpired", "qrCodeTimeLeft", "setQrCodeTimeLeft", "userRequestedQrCode", "setUserRequestedQrCode", "showRegenerateButton", "setShowRegenerateButton", "expectingQrCode", "setExpectingQrCode", "chats", "setChats", "loading", "setLoading", "error", "setError", "selectedC<PERSON>", "setSelectedChat", "searchQuery", "setSearch<PERSON>uery", "messagesLeadChannel", "setMessagesLeadChannel", "timeoutRef", "isMountedRef", "cancelQrCodeFetchRef", "qrCodeTimerRef", "countdownTimerRef", "botPausado", "setBotPausado", "isProfileOpen", "setIsProfileOpen", "messagesCache", "setMessagesCache", "Map", "loadingMessages", "setLoadingMessages", "messagesPage", "setMessagesPage", "hasMoreMessages", "setHasMoreMessages", "loadingMoreMessages", "setLoadingMoreMessages", "messagesContainerRef", "preloadingChats", "setPreloadingChats", "preloadProgress", "setPreloadProgress", "current", "total", "hasPreloaded", "setHasPreloaded", "raceConditionsFixed", "setRaceConditionsFixed", "isInitialLoad", "setIsInitialLoad", "fetchingChatsRef", "myProfilePicture", "setMyProfilePicture", "shouldScrollToBottom", "setShouldScrollToBottom", "justOpenedChat", "setJustOpenedChat", "justOpenedTimerRef", "unreadCounts", "setUnreadCounts", "loaded", "backgroundError", "progress", "backgroundProgress", "console", "log", "substring", "isDevelopment", "process", "env", "NODE_ENV", "baseURL", "origin", "testImg", "Image", "onload", "onerror", "e", "src", "handleSelectChatMobile", "chat", "markChatAsRead", "handleBackToList", "handleOpenProfile", "stopPropagation", "sortedMessages", "sort", "a", "b", "groupedMessages", "messageEndRef", "container", "length", "scrollTop", "scrollHeight", "setTimeout", "scrollIntoView", "behavior", "handleClickOutside", "event", "contains", "target", "handleSubmit", "preventDefault", "trim", "mobile_number", "messageToSend", "optimisticMessage", "text", "toISOString", "messageDate", "leadChannel", "isOptimistic", "status", "oldMessages", "prevCache", "newCache", "cachedMessages", "get", "set", "oldChats", "updatedChats", "map", "updatedAt", "chatIndex", "findIndex", "chatToMove", "splice", "unshift", "response", "msg", "_error$response", "_error$response2", "_error$response3", "_error$response3$data", "data", "filter", "errorMessage", "hostname", "apiUrl", "REACT_APP_SERVER_URL_DEV", "REACT_APP_SERVER_URL_PROD", "mainSocket", "setMainSocket", "chatId", "prevCounts", "newCounts", "startQrCodeTimer", "initialTtl", "clearTimeout", "clearInterval", "setInterval", "prev", "generateQrCodeManually", "fetchQrCode", "regenerateQrCodeViaAPI", "source", "isConnected", "clearQrCodeTimers", "qrcode", "qr", "ttl", "max", "warn", "Error", "regenerateQr<PERSON>ode<PERSON><PERSON><PERSON>", "cleanupGlobalSocket", "whatsappSocket", "removeAllListeners", "disconnect", "socketNamespace", "wsUrl", "connected", "socket", "withCredentials", "transports", "auth", "reconnection", "reconnectionAttempts", "reconnectionDelay", "on", "roomName", "reason", "newChat", "name", "filteredChats", "messageForCache", "id", "messageExists", "some", "abs", "instanceId", "empresaName", "hasQrCode", "renewed", "timestamp", "empresaAtual", "qrData", "validQrCode", "startsWith", "base64", "stringify", "statusCode", "setPage", "setHasMoreChats", "fetchChats", "off", "document", "initializeWhatsApp", "checkConnectionStatus", "connectionState", "then", "qrCodeData", "catch", "finally", "bot_pausado", "fetchProfilePicture", "forceUpdate", "priority", "_chat$channel_data", "_chat$channel_data$wh", "_chat$channel_data2", "_chat$channel_data2$w", "cacheValidityDays", "hasRecentProfilePicture", "channel_data", "whatsapp", "profile_picture_updateAt", "profile_picture", "request<PERSON>ey", "profilePictureRequests", "profilePictureUrl", "prevChats", "c", "_c$channel_data", "_prev$channel_data", "fetchProfilePicturesBatch", "batchName", "chatsNeedingImages", "_chat$channel_data3", "_chat$channel_data3$w", "_chat$channel_data4", "_chat$channel_data4$w", "hasImage", "hasRecentUpdate", "priorityChats", "slice", "regularChats", "for<PERSON>ach", "index", "fetchUnreadCounts", "hasGenericName", "contactName", "mobileNumber", "cleanName", "cleanNumber", "genericPatterns", "test", "pattern", "refreshContactInfo", "chatName", "old<PERSON>ame", "profilePictureUpdated", "_c$channel_data2", "_c$channel_data3", "_c$channel_data3$what", "_c$channel_data4", "_c$channel_data4$what", "pushName", "_prev$channel_data2", "_prev$channel_data3", "_prev$channel_data3$w", "_prev$channel_data4", "_prev$channel_data4$w", "success", "position", "autoClose", "hideProgressBar", "closeOnClick", "pauseOnHover", "draggable", "fetchMyProfilePicture", "cachedImage", "cachedTimestamp", "cacheAge", "setItem", "removeItem", "_error$response4", "_error$response4$data", "_error$response4$data2", "_error$response4$data3", "includes", "call", "_jidResponse$data", "fetchWhatsappJID", "jidResponse", "_retryResponse$data", "jid", "retryResponse", "jid<PERSON><PERSON><PERSON>", "loadMoreMessages", "nextPage", "pageSize", "_response$data$messag", "newMessages", "totalCount", "metadata", "prevMessages", "existingIds", "Set", "uniqueNewMessages", "has", "cache<PERSON>ey", "existingMessages", "finalCacheMessages", "currentMessages", "currentTotal", "handleMessagesScroll", "scrollThreshold", "currentScrollHeight", "currentScrollTop", "restoreScroll", "newScrollHeight", "scrollDifference", "newScrollTop", "requestAnimationFrame", "page", "hasMoreChats", "chatsContainerRef", "loadingMore", "setLoadingMore", "newPage", "append", "query", "newChats", "finalChats", "uniqueNewChats", "uniqueChats", "self", "preloadAllChats", "err", "_err$response", "_err$response2", "config", "handleScroll", "bottom", "clientHeight", "prevPage", "formatDate", "tz", "inputDate", "isSame", "subtract", "isBefore", "handleDisconnect", "res", "type", "_error$response5", "_error$response6", "_error$response6$data", "handleToggleBot", "chatList", "sortedChats", "_a$message", "_a$message2", "_b$message", "_b$message2", "dateA", "dateB", "chatsToPreload", "batchSize", "i", "batch", "batchPromises", "Promise", "all", "completed", "min", "delay", "resolve", "size", "interval", "monitoringInterval", "startMonitoring", "debug", "startTimer", "isCurrentConversation", "currentChatId", "currentChatName", "emit", "leadID", "fetchChatLead", "freshMessages", "_response$data$messag2", "newMessage", "filteredMessages", "filteredCachedMessages", "loadingTimeout", "abrirConversaPorLeadId", "leadId", "chats<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatNaLista", "find", "chatEncontrado", "chatJaExiste", "apiError", "_apiError$response", "_searchResponse$data", "_searchResponse$data$", "_searchResponse$data$2", "searchResponse", "todasConversas", "chatEncontradoPorBusca", "searchError", "confirmarNovaConversa", "confirm", "stateData", "nome", "celular", "numeroFormatado", "_createResponse$data", "createResponse", "novaConversa", "createError", "info", "warning", "children", "className", "style", "width", "backgroundColor", "padding", "display", "flexDirection", "gap", "margin", "max<PERSON><PERSON><PERSON>", "fontSize", "textAlign", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paddingLeft", "lineHeight", "justifyContent", "border", "borderRadius", "boxShadow", "height", "alignItems", "alt", "maxHeight", "opacity", "transition", "onLoad", "onError", "marginTop", "color", "fontWeight", "padStart", "onClick", "disabled", "cursor", "min<PERSON><PERSON><PERSON>", "onMouseOver", "transform", "onMouseOut", "user_img", "onDoubleClick", "ref", "role", "action", "borderLeft", "_chat$channel_data5", "_chat$channel_data5$w", "count", "title", "background", "marginLeft", "onScroll", "Object", "keys", "fontStyle", "calendar", "sameDay", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "onSubmit", "placeholder", "onChange", "value", "onClose", "Wrapper", "Back", "UserData", "_c24", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/pages/Whatsapp/whatsapp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext, useCallback } from \"react\";\r\nimport { useRef } from \"react\";\r\nimport { useLocation } from \"react-router-dom\";\r\nimport LeftMenu from \"../../components/LeftMenu\";\r\n//import { MenuProfile } from \"../../contexts/MenuProfileContext\";\r\nimport './style.css';\r\nimport styled from \"styled-components\";\r\nimport Profile from \"../../components/Profile\";\r\nimport Chat from \"../../components/Chat\";\r\nimport io from 'socket.io-client';\r\nimport mockMessages from \"./mock.json\";\r\nimport mockOptions from \"./mockOptions.json\";\r\nimport contactUserPhoto from \"../../assets/img/contact-user-photo.png\";\r\nimport backgroundChat from \"../../assets/img/chat-background.png\";\r\nimport SearchBar from \"../../components/SearchBar\";\r\nimport * as S from \"./stylesProfile\";\r\nimport UserProfile from \"./UserProfile\"; // Importa o modal\r\nimport { SidebarContext } from \"../../AppRoutes\";\r\nimport { MenuProfileContext } from \"../../contexts/MenuProfileContext\";\r\nimport { getQrCodeWhatsapp, renewQrCodeWhatsapp, getWhatsappChats, getWhatsappChatLead, sendMessage, sendTextMessageWhatsapp, removeWhatsappSession, toggleBotStatus, getBotStatusLead, getWhatsappProfilePicture, getMyWhatsappProfilePicture, getWhatsappUnreadCount, markWhatsappMessagesAsRead, getWhatsappConnectionStatus, refreshContactInfoWhatsapp, getWhatsappChatById, createWhatsappChat } from \"../../services/api\";\r\nimport Loading from \"react-loading\";\r\nimport CryptoJS from 'crypto-js';\r\nimport moment from 'moment-timezone';\r\nimport 'moment/locale/pt-br';  // Adiciona a localidade em português\r\nimport useImagePreloader from '../../hooks/useImagePreloader';\r\n//import ChatMenu from \"../Menu\";\r\nimport userIcon from \"../../assets/img/user-icon.png\";\r\nimport { ReactComponent as CheckIcon } from \"../../assets/svg/check-icon.svg\";\r\nimport { ReactComponent as StatusIcon } from \"../../assets/svg/status-icon.svg\";\r\nimport { ReactComponent as ChatIcon } from \"../../assets/svg/chat-icon.svg\";\r\nimport { ReactComponent as MenuIcon } from \"../../assets/svg/menu-icon.svg\";\r\nimport { ReactComponent as BackIcon } from \"../../assets/svg/arrow-back-icon.svg\";\r\nimport { FaWhatsapp } from \"react-icons/fa\";\r\nimport { FiSend, FiPauseCircle, FiPlayCircle } from \"react-icons/fi\"; // Importando o ícone de envio\r\nimport { toast } from 'react-toastify';\r\n\r\nimport Input from \"../../components/Input/index\";\r\nimport UnreadBadge from \"../../components/UnreadBadge\";\r\n\r\nimport GlobalStyles, { Container } from \"../../styles/global\";\r\n\r\n// Hook para detectar mobile\r\nfunction useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);\r\n  React.useEffect(() => {\r\n    const onResize = () => setIsMobile(window.innerWidth <= 768);\r\n    window.addEventListener('resize', onResize);\r\n    return () => window.removeEventListener('resize', onResize);\r\n  }, []);\r\n  return isMobile;\r\n}\r\n\r\nexport const WrapperContainerAll = styled.div`\r\n  display: flex;\r\n  height: 100%;\r\n  \r\n  @media (max-width: 768px) {\r\n    flex-direction: column;\r\n    height: 100vh;\r\n  }\r\n`;\r\n\r\nexport const WrapperContainerConversa = styled.div`\r\n  width: 100%;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  background-attachment: fixed;\r\n  background-color: #e5ddd5; /* Cor de fallback com padrão do WhatsApp */\r\n  background-image: ${props => props.backgroundLoaded ? \r\n    `url(${backgroundChat})` : \r\n    `linear-gradient(45deg, \r\n      #e5ddd5 0%, \r\n      #f0f0f0 25%, \r\n      #e5ddd5 50%, \r\n      #f0f0f0 75%, \r\n      #e5ddd5 100%)`}; /* Padrão sutil enquanto carrega */\r\n  transition: background-image 0.3s ease-in-out; /* Transição suave quando a imagem carrega */\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: calc(100vh - 120px);\r\n  \r\n  @media (max-width: 768px) {\r\n    height: 100%;\r\n    background-attachment: scroll; /* Melhor performance no mobile */\r\n    position: relative;\r\n    top: auto;\r\n    left: ${props => props.isMobile && props.showMobileChat ? '0' : 'auto'};\r\n    z-index: auto;\r\n    display: ${props => props.isMobile && !props.showMobileChat ? 'none' : 'flex'};\r\n  }\r\n  \r\n  /* Indicador sutil de carregamento se a imagem não carregou */\r\n  ${props => !props.backgroundLoaded && `\r\n    &::after {\r\n      content: '📸 Carregando background...';\r\n      position: absolute;\r\n      bottom: 20px;\r\n      right: 20px;\r\n      background: rgba(255, 255, 255, 0.9);\r\n      padding: 8px 12px;\r\n      border-radius: 20px;\r\n      font-size: 12px;\r\n      color: #666;\r\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n      z-index: 1000;\r\n      animation: pulse 2s infinite;\r\n    }\r\n    \r\n    @keyframes pulse {\r\n      0%, 100% { opacity: 0.7; }\r\n      50% { opacity: 1; }\r\n    }\r\n  `}\r\n`;\r\n\r\nconst WrapperContent = styled.div`\r\n  padding: 1rem 4rem;\r\n  height: 100%;\r\n  overflow-y: scroll;\r\n  margin-bottom: 60px;\r\n  background-color: transparent; /* Remove background duplicado */\r\n  \r\n  @media (max-width: 768px) {\r\n    padding: 1rem 1rem;\r\n    margin-bottom: 70px;\r\n  }\r\n`;\r\n\r\nconst ChatDate = styled.time`\r\n  display: block;\r\n  background: #e1f3fb;\r\n  padding: 10px;\r\n  border-radius: 7.5px;\r\n  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);\r\n  font-size: 1.42rem;\r\n  margin: 10px auto;\r\n  width: fit-content;\r\n  text-transform: uppercase;\r\n  \r\n  @media (max-width: 768px) {\r\n    font-size: 1.2rem;\r\n    padding: 8px;\r\n  }\r\n`;\r\n\r\nconst Message = styled.div`\r\n  background: ${(props) => (props.fromMe ? '#dcf8c6' : '#ffffff')};\r\n  padding: 10px;\r\n  border-radius: 7.5px;\r\n  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);\r\n  font-size: 1.42rem;\r\n  margin: 1px 0px 1px ${(props) => (props.fromMe ? 'auto' : '0px')};\r\n  width: fit-content;\r\n  max-width: 60%;\r\n  margin-bottom: ${(props) => (props.fromMe ? '3px' : '10px')};\r\n  white-space: pre-wrap;\r\n  word-break: break-word; /* Adicionado para quebra de palavras longas */\r\n  span {\r\n    font-size: 1.1rem;\r\n    color: rgba(0, 0, 0, 0.45);\r\n    display: block;\r\n    text-align: right;\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    font-size: 1.3rem;\r\n    max-width: 80%;\r\n    padding: 8px;\r\n    \r\n    span {\r\n      font-size: 1rem;\r\n    }\r\n  }\r\n`;\r\n\r\nexport const Warn = styled.div`\r\n  background: #fdf4c4;\r\n  width: fit-content;\r\n  margin: 30px auto;\r\n  padding: 5px 10px;\r\n  font-size: 1.6rem;\r\n  border-radius: 5px;\r\n  \r\n  @media (max-width: 768px) {\r\n    font-size: 1.4rem;\r\n    margin: 20px auto;\r\n    padding: 8px 12px;\r\n  }\r\n`;\r\n\r\nexport const WrapperHMC = styled.aside`\r\n  position: relative;\r\n  width: 55rem;\r\n  height: 100%;\r\n  background: #fff;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  @media (max-width: 1200px) {\r\n    width: 45rem;\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: relative;\r\n    left: ${props => props.isMobile && props.showMobileChat ? '-100%' : '0'};\r\n    transition: left 0.3s ease;\r\n    z-index: auto;\r\n    display: ${props => props.isMobile && props.showMobileChat ? 'none' : 'flex'};\r\n  }\r\n`;\r\n\r\nexport const WrapperMenu = styled.div`\r\n  position: absolute;\r\n  z-index: 10;\r\n  top: 60px;\r\n  right: 20px;\r\n  background: #fff;\r\n  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);\r\n  border-radius: 2px;\r\n\r\n  transition: transform 0.1s ease;\r\n  transform-origin: right top;\r\n  transform: scale(0);\r\n\r\n  &.active {\r\n    transform: scale(1);\r\n  }\r\n`;\r\n\r\nexport const ActionWrapperMenu = styled.ul`\r\n  padding: 10px 0px;\r\n`;\r\n\r\nexport const ActionMenu = styled.li`\r\n  padding: 10px 20px;\r\n  cursor: pointer;\r\n  font-size: 1.4rem;\r\n  color: #333;\r\n  transition: background 0.2s ease-in-out;\r\n\r\n  &:hover {\r\n    background: #f5f5f5;\r\n  }\r\n\r\n  &.disabled {\r\n    cursor: not-allowed;\r\n    opacity: 0.3;\r\n\r\n    &:hover {\r\n      background: none;\r\n    }\r\n  }\r\n`;\r\n\r\nexport const Header = styled.header`\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  padding: 13px;\r\n  background: #ededed;\r\n  \r\n  @media (max-width: 768px) {\r\n    padding: 15px;\r\n    position: relative;\r\n  }\r\n`;\r\n\r\nexport const ContactPhoto = styled.div`\r\n  margin-right: 15px;\r\n\r\n  img {\r\n    border-radius: 100%;\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    margin-right: 12px;\r\n  }\r\n`;\r\n\r\nexport const ContactName = styled.span`\r\n  font-size: 1.7rem;\r\n  \r\n  @media (max-width: 768px) {\r\n    font-size: 1.5rem;\r\n  }\r\n`;\r\n\r\nexport const MobileBackButton = styled.button`\r\n  display: none;\r\n  \r\n  @media (max-width: 768px) {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: transparent;\r\n    border: none;\r\n    padding: 8px;\r\n    margin-right: 10px;\r\n    cursor: pointer;\r\n    color: #333;\r\n    font-size: 1.8rem;\r\n  }\r\n`;\r\n\r\nexport const WrapperChat = styled.div`\r\n  padding: 10px;\r\n  margin-top: 15px;\r\n  background: #f0f0f0;\r\n  position: absolute;\r\n  bottom: 0;\r\n  width: 100%;\r\n  \r\n  @media (max-width: 768px) {\r\n    padding: 15px;\r\n    margin-top: 0;\r\n    \r\n    form {\r\n      gap: 8px;\r\n    }\r\n  }\r\n`;\r\n\r\nexport const WrapperListContacts = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  background: ${(props) => (props.isSelected ? '#eaeaea' : 'white')};\r\n  cursor: pointer;\r\n  padding: 10px;  /* Adicionado padding para espaçamento */\r\n  border-bottom: 1px solid #eee;\r\n  &:hover {\r\n    background: #f4f4f4;\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    padding: 15px 10px;\r\n    \r\n    &:active {\r\n      background: #e0e0e0;\r\n    }\r\n  }\r\n`;\r\n\r\nexport const ContactPhotoListContacts = styled.div`\r\n  flex: 0 0 60px;  /* Define a largura fixa da área da foto */\r\n  padding: 10px;\r\n\r\n  img {\r\n    border-radius: 100%;\r\n    width: 100%;  /* Garante que a imagem ocupe a largura total do contêiner */\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    flex: 0 0 50px;\r\n    padding: 8px;\r\n  }\r\n`;\r\n\r\nexport const ContactNameAndTime = styled.div`\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n\r\n  span {\r\n    font-size: 14px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 70%;\r\n  }\r\n\r\n  p {\r\n    font-size: 1.2rem;\r\n    color: rgba(0, 0, 0, 0.45);\r\n    margin-left: 10px;  /* Adiciona espaço entre o nome e a data */\r\n    flex-shrink: 0;  /* Garante que a data não encolha */\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    span {\r\n      font-size: 16px;\r\n      max-width: 65%;\r\n    }\r\n    \r\n    p {\r\n      font-size: 1.1rem;\r\n    }\r\n  }\r\n`;\r\n\r\nexport const ContactMessage = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  svg {\r\n    margin-right: 5px;\r\n  }\r\n\r\n  P {\r\n    font-size: 12px;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: 100%;  /* Ajusta a largura máxima para evitar overflow */\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    P {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n`;\r\n\r\nexport const MessageDataWrapper = styled.div`\r\n  flex: 1;\r\n  padding-right: 15px;\r\n  overflow: hidden;  /* Garante que o conteúdo não extrapole os limites */\r\n  display: flex;\r\n  flex-direction: column;\r\n  \r\n  @media (max-width: 768px) {\r\n    padding-right: 10px;\r\n  }\r\n`;\r\n\r\nexport const WrapperListHeader = styled.div`\r\n  border-right: 1px solid rgba(0, 0, 0, 0.1);\r\n  \r\n  @media (max-width: 768px) {\r\n    border-right: none;\r\n  }\r\n`;\r\n\r\nexport const HeaderListHeader = styled.header`\r\n  background-color: #ededed;\r\n  width: 100%;\r\n  padding: 15px;\r\n\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  \r\n  @media (max-width: 768px) {\r\n    padding: 12px 15px;\r\n  }\r\n`;\r\n\r\nexport const UserImage = styled.div`\r\n  margin-right: auto;\r\n  cursor: pointer;\r\n\r\n  img {\r\n    border-radius: 100%;\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    img {\r\n      width: 35px;\r\n      height: 35px;\r\n    }\r\n  }\r\n`;\r\n\r\nexport const Actions = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  svg {\r\n    width: 50px;\r\n    cursor: pointer;\r\n\r\n    &:not(:last-child) {\r\n      cursor: not-allowed;\r\n      opacity: 0.3;\r\n    }\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    svg {\r\n      width: 40px;\r\n    }\r\n  }\r\n`;\r\n\r\nconst Teste = styled.div`\r\n  display: flex;\r\n  margin-left:  ${({ sidebar }) => (sidebar ? '250px' : '100px')}; \r\n  height:auto;\r\n  width:auto;\r\n  transition: 150ms;\r\n  background-color:rgb(247,247,247)!important;\r\n  --background-color:white!important;\r\n  overflow: initial;\r\n  z-Index: 9;\r\n  \r\n  @media (max-width: 768px) {\r\n    margin-left: 0;\r\n    height: calc(100vh - 60px); /* Deixa espaço para o navbar */\r\n    width: 100vw;\r\n    position: relative; /* Muda de fixed para relative */\r\n    top: auto;\r\n    left: 0;\r\n    z-index: 1;\r\n    margin-top: 0; /* Remove margem superior */\r\n  }\r\n`;\r\n\r\n// Helper Function to Group Messages by Date\r\nconst groupMessagesByDate = (messages) => {\r\n  return messages.reduce((acc, message) => {\r\n    const date = moment(message.createdAt).format('YYYY-MM-DD');\r\n    if (!acc[date]) {\r\n      acc[date] = [];\r\n    }\r\n    acc[date].push(message);\r\n    return acc;\r\n  }, {});\r\n};\r\n\r\n// Função para gerar ObjectId aleatório\r\nconst generateObjectId = () => {\r\n  return Math.floor(Date.now() / 1000).toString(16) + 'xxxxxxxxxxxxxxxx'.replace(/[x]/g, function () {\r\n    return Math.floor(Math.random() * 16).toString(16);\r\n  }).toLowerCase();\r\n};\r\n\r\nconst WhatsAppWeb = () => {\r\n  moment.locale('pt-br');  // Define o locale para português\r\n  const secretKey = 'my-custom-secret-key:1af0addcce4b56f753ed10c285667ad031bae57af19747f576b05312b8cf85be';\r\n  const userEncrypted = localStorage.getItem('user');\r\n  const user = CryptoJS.AES.decrypt(userEncrypted, secretKey).toString(CryptoJS.enc.Utf8);\r\n  const userParse = JSON.parse(user);\r\n  const userID = userParse._id;\r\n  const empresa = localStorage.getItem('empresa');\r\n  const empresaParse = JSON.parse(empresa);\r\n  const empresaID = empresaParse._id;\r\n  const token = localStorage.getItem('token');\r\n\r\n  const location = useLocation(); // Captura o state passado pelo navigate\r\n  const leadIdParaAbrir = location.state?.lead_id || null; // Verifica se recebeu um lead_id\r\n\r\n  const isMobile = useIsMobile();\r\n  const [showMobileChat, setShowMobileChat] = useState(false);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  //const { openMenu } = useContext(MenuProfileContext);\r\n  const { isOpen, openMenu, closeMenu } = useContext(MenuProfileContext);\r\n  //const openMenu = () => setIsMenuOpen(true);\r\n  const menuRef = useRef(null);\r\n  const [message, setMessage] = useState(\"\");\r\n  const { sidebar, setSidebar } = useContext(SidebarContext);; //TESTANDO CONST NO AppROUTES\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  // 📱 Estados do QR Code - MELHORADO\r\n  const [qrCodeImg, setQrCodeImg] = useState('');\r\n  const [generatingQrCode, setGeneratingQrCode] = useState(false);\r\n  const [isLoged, setIsLoged] = useState(true); // Inicializar como true para melhor UX (evita flash do QR Code)\r\n  const [qrCodeExpired, setQrCodeExpired] = useState(false);\r\n  const [qrCodeTimeLeft, setQrCodeTimeLeft] = useState(0);\r\n  const [userRequestedQrCode, setUserRequestedQrCode] = useState(false); // 🔧 NOVO: Controla se usuário solicitou QR Code\r\n  const [showRegenerateButton, setShowRegenerateButton] = useState(false);\r\n  const [expectingQrCode, setExpectingQrCode] = useState(false); // Flag para controlar quando esperamos QR Code\r\n  //const [socket, setSocket] = useState(null);\r\n  const [chats, setChats] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [selectedChat, setSelectedChat] = useState(null);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [messagesLeadChannel, setMessagesLeadChannel] = useState([]);\r\n  const timeoutRef = useRef(null); // UseRef to store the timeout ID\r\n  const isMountedRef = useRef(true); // Para saber se o componente está montado\r\n  const cancelQrCodeFetchRef = useRef(false); // Flag para cancelar a execução do fetch\r\n  // 📱 Refs para QR Code melhorado\r\n  const qrCodeTimerRef = useRef(null);\r\n  const countdownTimerRef = useRef(null);\r\n  const [botPausado, setBotPausado] = useState(false);\r\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\r\n  const [messagesCache, setMessagesCache] = useState(new Map());\r\n  const [loadingMessages, setLoadingMessages] = useState(false);\r\n\r\n  // **📌 ESTADOS PARA PAGINAÇÃO DE MENSAGENS**\r\n  const [messagesPage, setMessagesPage] = useState(0);\r\n  const [hasMoreMessages, setHasMoreMessages] = useState(true);\r\n  const [loadingMoreMessages, setLoadingMoreMessages] = useState(false);\r\n  const messagesContainerRef = useRef(null);\r\n  const [preloadingChats, setPreloadingChats] = useState(false);\r\n  const [preloadProgress, setPreloadProgress] = useState({ current: 0, total: 0 });\r\n  const [hasPreloaded, setHasPreloaded] = useState(false);\r\n  const [raceConditionsFixed, setRaceConditionsFixed] = useState(0);\r\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\r\n  const fetchingChatsRef = useRef(false);\r\n\r\n  // **📌 ESTADO PARA IMAGEM DE PERFIL DO PRÓPRIO USUÁRIO**\r\n  const [myProfilePicture, setMyProfilePicture] = useState(null);\r\n\r\n  // **📌 ESTADO PARA CONTROLAR SE DEVE ROLAR PARA O BOTTOM**\r\n  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);\r\n  \r\n  // **📌 ESTADO PARA CONTROLAR SE ACABOU DE ABRIR UMA CONVERSA**\r\n  const [justOpenedChat, setJustOpenedChat] = useState(false);\r\n  const justOpenedTimerRef = useRef(null);\r\n\r\n  // **📌 ESTADO PARA CONTAGEM DE MENSAGENS NÃO LIDAS**\r\n  const [unreadCounts, setUnreadCounts] = useState({});\r\n\r\n  // **📌 PRELOAD DA IMAGEM DE BACKGROUND USANDO HOOK CUSTOMIZADO**\r\n  const { loaded: backgroundLoaded, error: backgroundError, progress: backgroundProgress } = useImagePreloader(backgroundChat, 3000);\r\n\r\n  // **📌 DEBUG: LOG DE ERROS DO BACKGROUND**\r\n  useEffect(() => {\r\n    if (backgroundError) {\r\n      console.error('🚨 [BACKGROUND ERROR]:', backgroundError);\r\n      console.log('📊 [BACKGROUND DEBUG]:', {\r\n        backgroundLoaded,\r\n        backgroundProgress: `${backgroundProgress}%`,\r\n        backgroundChat: backgroundChat?.substring(0, 50) + '...',\r\n        isDevelopment: process.env.NODE_ENV === 'development',\r\n        baseURL: window.location.origin\r\n      });\r\n      \r\n      // Tentar carregar a imagem diretamente no console para debug\r\n      console.log('🔍 [DEBUG] Testando carregamento direto da imagem:');\r\n      const testImg = new Image();\r\n      testImg.onload = () => console.log('✅ [DEBUG] Imagem carrega OK diretamente');\r\n      testImg.onerror = (e) => console.error('❌ [DEBUG] Imagem falha ao carregar diretamente:', e);\r\n      testImg.src = backgroundChat;\r\n    } else if (backgroundLoaded && backgroundProgress === 100) {\r\n      console.log('🎉 [BACKGROUND] Carregamento concluído com sucesso!');\r\n      // Toast sutil apenas no desenvolvimento para debug\r\n      /*if (process.env.NODE_ENV === 'development') {\r\n        setTimeout(() => {\r\n          toast.success('🎨 Background do chat carregado', {\r\n            position: \"bottom-right\",\r\n            autoClose: 2000,\r\n            hideProgressBar: true,\r\n            closeOnClick: true,\r\n            pauseOnHover: false,\r\n            draggable: false,\r\n          });\r\n        }, 500);\r\n      }*/\r\n    }\r\n  }, [backgroundError, backgroundLoaded, backgroundProgress]);\r\n\r\n  // Função para navegar para o chat no mobile\r\n  const handleSelectChatMobile = (chat) => {\r\n    setSelectedChat(chat);\r\n    if (isMobile) {\r\n      setShowMobileChat(true);\r\n    }\r\n\r\n    // **📌 MARCAR MENSAGENS COMO LIDAS AO SELECIONAR CHAT**\r\n    if (unreadCounts[chat._id] && unreadCounts[chat._id] > 0) {\r\n      markChatAsRead(chat._id);\r\n    }\r\n  };\r\n\r\n  // Função para voltar para a lista no mobile\r\n  const handleBackToList = () => {\r\n    if (isMobile) {\r\n      setShowMobileChat(false);\r\n      setSelectedChat(null);\r\n    }\r\n  };\r\n\r\n  // Função para abrir perfil apenas quando clicar na foto\r\n  const handleOpenProfile = (e) => {\r\n    e.stopPropagation(); // Previne propagação do evento\r\n    setIsProfileOpen(true);\r\n  };\r\n\r\n  // Resetar estado mobile quando a tela mudar de tamanho\r\n  useEffect(() => {\r\n    if (!isMobile) {\r\n      setShowMobileChat(false);\r\n    }\r\n  }, [isMobile]);\r\n\r\n  // Sort messages by date\r\n  const sortedMessages = [...messagesLeadChannel].sort((a, b) =>\r\n    new Date(a.createdAt) - new Date(b.createdAt)\r\n  );\r\n\r\n  const groupedMessages = groupMessagesByDate(sortedMessages);\r\n  const messageEndRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    // **📌 SÓ ROLAR PARA O BOTTOM SE NÃO ESTIVER CARREGANDO MENSAGENS ANTIGAS**\r\n    if (messageEndRef.current && !loadingMoreMessages && shouldScrollToBottom) {\r\n      // **📌 GARANTIR SCROLL INSTANTÂNEO PARA O BOTTOM, ESPECIALMENTE QUANDO ABRE CONVERSA**\r\n      const container = messagesContainerRef.current;\r\n      if (container && messagesLeadChannel.length > 0) {\r\n        // Forçar scroll para o bottom imediatamente\r\n        container.scrollTop = container.scrollHeight;\r\n        \r\n        // Backup com scrollIntoView para garantir\r\n        setTimeout(() => {\r\n          if (messageEndRef.current && shouldScrollToBottom) {\r\n            messageEndRef.current.scrollIntoView({ behavior: \"instant\" });\r\n          }\r\n        }, 50);\r\n      }\r\n    }\r\n  }, [messagesLeadChannel, loadingMoreMessages, shouldScrollToBottom]);\r\n\r\n  const handleClickOutside = event => {\r\n    if (menuRef.current && !menuRef.current.contains(event.target))\r\n      setIsMenuOpen(false);\r\n  };\r\n\r\n  const handleSubmit = async (event) => {\r\n    event.preventDefault();\r\n    if (!message.trim() || isSubmitting) return;\r\n\r\n    if (!selectedChat || !selectedChat.mobile_number) {\r\n      console.error('Nenhum chat selecionado ou número inválido');\r\n      return;\r\n    }\r\n\r\n\r\n\r\n    const messageToSend = message.trim();\r\n\r\n    // **📌 ENVIO OTIMISTA: Adicionar mensagem imediatamente**\r\n    const optimisticMessage = {\r\n      _id: `temp_${Date.now()}`,\r\n      text: messageToSend,\r\n      fromMe: true,\r\n      createdAt: new Date().toISOString(),\r\n      messageDate: new Date().toISOString(),\r\n      leadChannel: selectedChat._id,\r\n      isOptimistic: true, // Flag para identificar mensagens otimistas\r\n      status: 'sending'\r\n    };\r\n\r\n    // Adicionar mensagem otimista na conversa\r\n    setMessagesLeadChannel(oldMessages => [optimisticMessage, ...oldMessages]);\r\n\r\n    // **📌 PARAR LOADING JÁ QUE TEMOS MENSAGEM OTIMISTA**\r\n    setLoadingMessages(false);\r\n    \r\n    // **📌 GARANTIR QUE O SCROLL PERMANEÇA NO BOTTOM APÓS MENSAGEM OTIMISTA**\r\n    setTimeout(() => {\r\n      const container = messagesContainerRef.current;\r\n      if (container) {\r\n        container.scrollTop = container.scrollHeight;\r\n      }\r\n    }, 50);\r\n\r\n    // Atualizar cache das mensagens\r\n    setMessagesCache(prevCache => {\r\n      const newCache = new Map(prevCache);\r\n      const cachedMessages = newCache.get(selectedChat._id) || [];\r\n      newCache.set(selectedChat._id, [optimisticMessage, ...cachedMessages]);\r\n      return newCache;\r\n    });\r\n\r\n    // Atualizar a lista de chats com a mensagem otimista\r\n    setChats(oldChats => {\r\n      const updatedChats = oldChats.map(chat => {\r\n        if (chat._id === selectedChat._id) {\r\n          return {\r\n            ...chat,\r\n            message: {\r\n              text: messageToSend,\r\n              message: messageToSend,\r\n              messageDate: new Date().toISOString(),\r\n              createdAt: new Date().toISOString(),\r\n              fromMe: true\r\n            },\r\n            updatedAt: new Date().toISOString()\r\n          };\r\n        }\r\n        return chat;\r\n      });\r\n\r\n      // Mover o chat atualizado para o topo da lista\r\n      const chatIndex = updatedChats.findIndex(chat => chat._id === selectedChat._id);\r\n      if (chatIndex > 0) {\r\n        const [chatToMove] = updatedChats.splice(chatIndex, 1);\r\n        updatedChats.unshift(chatToMove);\r\n      }\r\n\r\n      return updatedChats;\r\n    });\r\n\r\n    try {\r\n      setIsSubmitting(true);\r\n      setMessage('');  // Limpa o campo de mensagem antes do envio\r\n\r\n      const response = await sendTextMessageWhatsapp(empresaID, selectedChat.mobile_number, messageToSend);\r\n\r\n      // Atualizar status da mensagem otimista para 'sent'\r\n      setMessagesLeadChannel(oldMessages =>\r\n        oldMessages.map(msg =>\r\n          msg._id === optimisticMessage._id\r\n            ? { ...msg, status: 'sent' }\r\n            : msg\r\n        )\r\n      );\r\n\r\n      // A mensagem real será adicionada automaticamente via webhook quando for processada\r\n\r\n    } catch (error) {\r\n      console.error('❌ Erro ao enviar a mensagem:', error);\r\n      console.error('❌ Detalhes do erro:', error.response?.data);\r\n      console.error('❌ Status:', error.response?.status);\r\n\r\n      // Remover mensagem otimista em caso de erro\r\n      setMessagesLeadChannel(oldMessages =>\r\n        oldMessages.filter(msg => msg._id !== optimisticMessage._id)\r\n      );\r\n\r\n      // Remover do cache também\r\n      setMessagesCache(prevCache => {\r\n        const newCache = new Map(prevCache);\r\n        const cachedMessages = newCache.get(selectedChat._id) || [];\r\n        newCache.set(selectedChat._id, cachedMessages.filter(msg => msg._id !== optimisticMessage._id));\r\n        return newCache;\r\n      });\r\n\r\n      // Restaurar a mensagem em caso de erro\r\n      setMessage(messageToSend);\r\n\r\n      // Mostrar toast de erro com detalhes\r\n      const errorMessage = error.response?.data?.error || 'Erro ao enviar mensagem. Tente novamente.';\r\n      toast.error(errorMessage);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const isDevelopment = window.location.hostname === 'localhost';\r\n  const apiUrl = isDevelopment\r\n    ? process.env.REACT_APP_SERVER_URL_DEV\r\n    : process.env.REACT_APP_SERVER_URL_PROD;\r\n\r\n  // **📌 SOCKET PRINCIPAL PARA WHATSAPP**\r\n  const [mainSocket, setMainSocket] = useState(null);\r\n\r\n  // **📌 FUNÇÃO PARA MARCAR MENSAGENS COMO LIDAS**\r\n  const markChatAsRead = useCallback(async (chatId) => {\r\n    try {\r\n      console.log(`📖 Marcando mensagens como lidas para chat: ${chatId}`);\r\n      const response = await markWhatsappMessagesAsRead(empresaID, chatId, token);\r\n      if (response.data && response.data.status === 200) {\r\n        console.log(`✅ Mensagens marcadas como lidas com sucesso para chat: ${chatId}`);\r\n\r\n        // Atualizar contagem local\r\n        setUnreadCounts(prevCounts => {\r\n          const newCounts = { ...prevCounts };\r\n          delete newCounts[chatId]; // Remove a contagem para este chat\r\n          return newCounts;\r\n        });\r\n\r\n        // Atualizar contagem geral\r\n        try {\r\n          const response = await getWhatsappUnreadCount(empresaID, token);\r\n          if (response.data && response.data.status === 200) {\r\n            setUnreadCounts(response.data.unreadCounts);\r\n          }\r\n        } catch (error) {\r\n          console.error('❌ Erro ao buscar contagem de mensagens não lidas:', error);\r\n        }\r\n      } else {\r\n        console.log(`⚠️ Resposta inesperada ao marcar como lidas:`, response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error(`❌ Erro ao marcar mensagens como lidas para chat ${chatId}:`, error);\r\n    }\r\n  }, [empresaID, token]);\r\n\r\n  // 📱 FUNÇÃO MELHORADA PARA GERENCIAR QR CODE COM TIMER DE 45 SEGUNDOS (ALINHADO COM BACKEND)\r\n  const startQrCodeTimer = (initialTtl = 45) => {\r\n    // Limpar timers existentes\r\n    if (qrCodeTimerRef.current) {\r\n      clearTimeout(qrCodeTimerRef.current);\r\n    }\r\n    if (countdownTimerRef.current) {\r\n      clearInterval(countdownTimerRef.current);\r\n    }\r\n\r\n    // Resetar estados\r\n    setQrCodeExpired(false);\r\n    setShowRegenerateButton(false);\r\n    setQrCodeTimeLeft(initialTtl); // TTL dinâmico (padrão 45 segundos)\r\n\r\n    console.log(`⏱️ Iniciando timer de QR Code: ${initialTtl} segundos`);\r\n\r\n    // Timer de countdown (atualiza a cada segundo)\r\n    countdownTimerRef.current = setInterval(() => {\r\n      setQrCodeTimeLeft(prev => {\r\n        if (prev <= 1) {\r\n          // QR Code expirou\r\n          setQrCodeExpired(true);\r\n          setShowRegenerateButton(true);\r\n          clearInterval(countdownTimerRef.current);\r\n          \r\n          console.log(`⏰ QR Code expirou após ${initialTtl} segundos`);\r\n          \r\n          // 🔧 REGENERAÇÃO AUTOMÁTICA DESABILITADA - Usuário deve clicar manualmente\r\n          // setTimeout(() => {\r\n          //   if (!isLoged && isMountedRef.current) {\r\n          //     console.log('🔄 Regenerando QR Code automaticamente via nova rota...');\r\n          //     regenerateQrCodeViaAPI('automatic_expiry');\r\n          //   }\r\n          // }, 2000); // Delay de 2 segundos antes de regenerar\r\n          \r\n          return 0;\r\n        }\r\n        return prev - 1;\r\n      });\r\n    }, 1000);\r\n\r\n    // Timer principal baseado no TTL\r\n    qrCodeTimerRef.current = setTimeout(() => {\r\n      setQrCodeExpired(true);\r\n      setShowRegenerateButton(true);\r\n      clearInterval(countdownTimerRef.current);\r\n    }, initialTtl * 1000);\r\n  };\r\n\r\n  // 📱 NOVA FUNÇÃO PARA GERAR QR CODE MANUALMENTE (PRIMEIRA VEZ)\r\n  const generateQrCodeManually = async () => {\r\n    console.log('🔄 Usuário solicitou geração manual do QR Code...');\r\n    setUserRequestedQrCode(true);\r\n    await fetchQrCode('manual_user_request');\r\n  };\r\n\r\n  // 📱 NOVA FUNÇÃO PARA REGENERAR QR CODE VIA API (BACKEND MELHORADO)\r\n  const regenerateQrCodeViaAPI = async (source = 'manual') => {\r\n    console.log(`🔄 Regenerando QR Code via nova API (${source})...`);\r\n\r\n    // Verificar se componente ainda está montado\r\n    if (!isMountedRef.current) {\r\n      console.log('🛑 Componente desmontado, cancelando regeneração');\r\n      return;\r\n    }\r\n\r\n    // Limpar timers existentes\r\n    if (qrCodeTimerRef.current) {\r\n      clearTimeout(qrCodeTimerRef.current);\r\n    }\r\n    if (countdownTimerRef.current) {\r\n      clearInterval(countdownTimerRef.current);\r\n    }\r\n\r\n    setQrCodeExpired(false);\r\n    setShowRegenerateButton(false);\r\n    setGeneratingQrCode(true);\r\n\r\n    try {\r\n      // 🚀 USAR NOVA FUNÇÃO DE API PARA RENOVAÇÃO\r\n      const response = await renewQrCodeWhatsapp(empresaID, token);\r\n      const data = response.data;\r\n\r\n      if (!isMountedRef.current) return;\r\n\r\n      if (data.status === 200) {\r\n        if (data.isConnected) {\r\n          // Já conectado\r\n          console.log('✅ WhatsApp já está conectado!');\r\n          setIsLoged(true);\r\n          setGeneratingQrCode(false);\r\n          setQrCodeImg('');\r\n          clearQrCodeTimers();\r\n          return;\r\n        }\r\n\r\n        if (data.qrcode && data.qrcode.qr) {\r\n          // QR Code renovado com sucesso\r\n          console.log('✅ QR Code renovado via API:', data.qrcode);\r\n          setQrCodeImg(data.qrcode.qr);\r\n          setGeneratingQrCode(false);\r\n          \r\n          // Usar TTL do backend se disponível\r\n          const ttl = data.qrcode.ttl ? \r\n            Math.max(1, data.qrcode.ttl - Math.floor(Date.now() / 1000)) : 45;\r\n          \r\n          startQrCodeTimer(ttl);\r\n          return;\r\n        }\r\n      }\r\n\r\n      // Se chegou aqui, algo deu errado\r\n      console.warn('⚠️ Resposta inesperada da API de renovação:', data);\r\n      throw new Error(data.error || 'Erro desconhecido na renovação');\r\n\r\n    } catch (error) {\r\n      console.error(`❌ Erro ao regenerar QR Code via API (${source}):`, error);\r\n      \r\n      if (!isMountedRef.current) return;\r\n      \r\n      setGeneratingQrCode(false);\r\n      setShowRegenerateButton(true);\r\n      \r\n      // Fallback: tentar método antigo\r\n      console.log('🔄 Tentando método de fallback...');\r\n      setTimeout(() => {\r\n        if (!isLoged && isMountedRef.current) {\r\n          regenerateQrCodeFallback(source);\r\n        }\r\n      }, 2000);\r\n    }\r\n  };\r\n\r\n  // 📱 FUNÇÃO DE FALLBACK PARA REGENERAR QR CODE (MÉTODO ANTIGO)\r\n  const regenerateQrCodeFallback = async (source = 'manual') => {\r\n    console.log(`🔄 Regenerando QR Code via fallback (${source})...`);\r\n\r\n    // Verificar se componente ainda está montado\r\n    if (!isMountedRef.current) {\r\n      console.log('🛑 Componente desmontado, cancelando regeneração');\r\n      return;\r\n    }\r\n\r\n    setQrCodeExpired(false);\r\n    setShowRegenerateButton(false);\r\n    setGeneratingQrCode(true);\r\n\r\n    try {\r\n      await fetchQrCode(`fallback_${source}`);\r\n    } catch (error) {\r\n      console.error(`❌ Erro ao regenerar QR Code via fallback (${source}):`, error);\r\n      setGeneratingQrCode(false);\r\n      setShowRegenerateButton(true);\r\n    }\r\n  };\r\n\r\n  // 📱 FUNÇÃO PARA LIMPAR TIMERS DO QR CODE\r\n  const clearQrCodeTimers = () => {\r\n    if (qrCodeTimerRef.current) {\r\n      clearTimeout(qrCodeTimerRef.current);\r\n      qrCodeTimerRef.current = null;\r\n    }\r\n    if (countdownTimerRef.current) {\r\n      clearInterval(countdownTimerRef.current);\r\n      countdownTimerRef.current = null;\r\n    }\r\n  };\r\n\r\n  // 🔌 FUNÇÃO PARA LIMPAR SOCKET GLOBAL (usar apenas em logout/erro crítico)\r\n  const cleanupGlobalSocket = () => {\r\n    if (window.whatsappSocket) {\r\n      console.log(\"🛑 Limpando socket WhatsApp global...\");\r\n      window.whatsappSocket.removeAllListeners();\r\n      window.whatsappSocket.disconnect();\r\n      window.whatsappSocket = null;\r\n    }\r\n  };\r\n\r\n  // **📌 CRIAR SOCKET GLOBAL QUE PERSISTE ENTRE NAVEGAÇÕES**\r\n  useEffect(() => {\r\n    const socketNamespace = \"/whatsapp\";\r\n    const wsUrl = apiUrl.replace(/\\/$/, '') + socketNamespace;\r\n\r\n    // Verificar se já existe um socket global conectado\r\n    if (window.whatsappSocket && window.whatsappSocket.connected) {\r\n      console.log(\"✅ Reutilizando socket WhatsApp existente\");\r\n      setMainSocket(window.whatsappSocket);\r\n      return;\r\n    }\r\n\r\n    console.log(\"🔌 Criando novo socket WhatsApp global\");\r\n    const socket = io(wsUrl, {\r\n      withCredentials: true,\r\n      transports: ['websocket'],\r\n      auth: { token: localStorage.getItem('token') },\r\n      reconnection: true,\r\n      reconnectionAttempts: 10,\r\n      reconnectionDelay: 5000\r\n    });\r\n\r\n    // Armazenar socket globalmente para reutilização\r\n    window.whatsappSocket = socket;\r\n    setMainSocket(socket);\r\n\r\n    return () => {\r\n      console.log(\"🛑 Desmontando componente WhatsApp - mantendo socket conectado...\");\r\n      // ❌ NÃO DESCONECTAR O SOCKET - apenas limpar listeners específicos\r\n      // O socket deve permanecer conectado para outras telas\r\n      setMainSocket(null);\r\n    };\r\n  }, []);\r\n\r\n  // **📌 CONFIGURAR EVENTOS DE CHATS NO SOCKET PRINCIPAL**\r\n  useEffect(() => {\r\n    if (!mainSocket) return;\r\n\r\n    // Conectar ao namespace WhatsApp\r\n    mainSocket.on('connect', () => {\r\n      console.log(\"✅ Socket WhatsApp conectado\");\r\n      // **📌 IMPORTANTE: O join na sala é automático no backend quando o socket conecta**\r\n      const roomName = `chats:${empresaID}`;\r\n      console.log(`🏠 Socket automaticamente na sala: ${roomName}`);\r\n    });\r\n\r\n    mainSocket.on('disconnect', (reason) => {\r\n      console.log(\"❌ Socket WhatsApp desconectado:\", reason);\r\n    });\r\n\r\n    mainSocket.on('connect_error', (error) => {\r\n      console.error(\"🚨 Erro de conexão socket WhatsApp:\", error);\r\n    });\r\n\r\n    mainSocket.on('chats', (newChat) => {\r\n      console.log(\"📨 Novo chat recebido:\", newChat.name);\r\n      setChats(oldChats => {\r\n        // Remove o chat existente se já estiver na lista e adiciona o novo no início\r\n        const filteredChats = oldChats.filter(chat => chat._id !== newChat._id);\r\n        return [newChat, ...filteredChats];\r\n      });\r\n\r\n      // **📌 ATUALIZAR CACHE SE A MENSAGEM FOR DE UMA CONVERSA ATIVA**\r\n      if (newChat.message && !newChat.message.fromMe) {\r\n        setMessagesCache(prevCache => {\r\n          const newCache = new Map(prevCache);\r\n          const cachedMessages = newCache.get(newChat._id) || [];\r\n\r\n          // Criar objeto de mensagem para o cache\r\n          const messageForCache = {\r\n            _id: newChat.message.id || `msg_${Date.now()}`,\r\n            text: newChat.message.text || newChat.message.message,\r\n            fromMe: newChat.message.fromMe,\r\n            createdAt: newChat.message.createdAt || newChat.message.messageDate,\r\n            messageDate: newChat.message.messageDate || newChat.message.createdAt,\r\n            leadChannel: newChat._id\r\n          };\r\n\r\n          // Verificar se a mensagem já existe no cache\r\n          const messageExists = cachedMessages.some(msg =>\r\n            msg.text === messageForCache.text &&\r\n            msg.fromMe === messageForCache.fromMe &&\r\n            Math.abs(new Date(msg.createdAt) - new Date(messageForCache.createdAt)) < 5000 // 5 segundos de tolerância\r\n          );\r\n\r\n          if (!messageExists) {\r\n            newCache.set(newChat._id, [messageForCache, ...cachedMessages]);\r\n          }\r\n\r\n          return newCache;\r\n        });\r\n\r\n        // **📌 ATUALIZAR CONTAGEM DE MENSAGENS NÃO LIDAS EM TEMPO REAL**\r\n        if (!selectedChat || selectedChat._id !== newChat._id) {\r\n          // Se o chat não está selecionado, incrementar contador\r\n          setUnreadCounts(prevCounts => ({\r\n            ...prevCounts,\r\n            [newChat._id]: (prevCounts[newChat._id] || 0) + 1\r\n          }));\r\n        } else {\r\n          // **📌 SE O CHAT ESTÁ ABERTO, MARCAR MENSAGEM COMO LIDA AUTOMATICAMENTE**\r\n          console.log(`📖 Chat ${newChat.name} está aberto, marcando nova mensagem como lida automaticamente`);\r\n          setTimeout(() => {\r\n            markChatAsRead(newChat._id);\r\n          }, 1000); // Delay de 1 segundo para garantir que a mensagem foi salva no banco\r\n        }\r\n      }\r\n\r\n      console.log(\"✅ Lista de chats atualizada com nova mensagem de:\", newChat.name);\r\n    });\r\n\r\n    // 📱 LISTENER MELHORADO PARA QR CODE UPDATES VIA SOCKET - COM FILTRO POR EMPRESA\r\n    mainSocket.on('whatsapp_qrcode_update', (data) => {\r\n      console.log('📱 [SOCKET] QR Code atualizado via socket:', {\r\n        instanceId: data.instanceId,\r\n        empresaID: data.empresaID,\r\n        empresaName: data.empresaName,\r\n        hasQrCode: !!data.qrcode,\r\n        renewed: data.renewed,\r\n        timestamp: data.timestamp\r\n      });\r\n\r\n      console.log('📊 [SOCKET] Estado atual:', {\r\n        qrCodeImg: !!qrCodeImg,\r\n        qrCodeExpired,\r\n        generatingQrCode,\r\n        isLoged,\r\n        empresaAtual: empresaID\r\n      });\r\n\r\n      // **🎯 FILTRO CRÍTICO: IGNORAR QR CODES DE OUTRAS EMPRESAS**\r\n      if (data.empresaID && data.empresaID !== empresaID) {\r\n        console.log(`🚫 [SOCKET] QR Code ignorado - empresa diferente! Recebido: ${data.empresaName} (${data.empresaID}), Atual: ${empresaID}`);\r\n        return;\r\n      }\r\n\r\n      // 🚫 IGNORAR QR CODES AUTOMÁTICOS SE JÁ ESTAMOS CONECTADOS\r\n      if (isLoged) {\r\n        console.log('🚫 [SOCKET] Ignorando QR Code - já conectado (isLoged=true)');\r\n        return;\r\n      }\r\n\r\n      // 🔧 NOVO: IGNORAR QR CODES SE USUÁRIO NÃO SOLICITOU\r\n      if (!userRequestedQrCode) {\r\n        console.log('🚫 [SOCKET] Ignorando QR Code - usuário não solicitou geração');\r\n        return;\r\n      }\r\n\r\n      if (data.qrcode && data.instanceId) {\r\n        // 🔧 MELHOR VALIDAÇÃO DO QR CODE RECEBIDO VIA SOCKET\r\n        const qrData = data.qrcode;\r\n        let validQrCode = null;\r\n\r\n        if (typeof qrData === 'string' && qrData.startsWith('data:image/')) {\r\n          validQrCode = qrData;\r\n        } else if (qrData.qr && typeof qrData.qr === 'string' && qrData.qr.startsWith('data:image/')) {\r\n          validQrCode = qrData.qr;\r\n        } else if (qrData.base64 && typeof qrData.base64 === 'string' && qrData.base64.startsWith('data:image/')) {\r\n          validQrCode = qrData.base64;\r\n        }\r\n\r\n        if (validQrCode) {\r\n          console.log('✅ [SOCKET] QR Code válido recebido via socket');\r\n          setQrCodeImg(validQrCode);\r\n          setGeneratingQrCode(false);\r\n          \r\n          // Usar TTL do backend se disponível, senão usar 45 segundos\r\n          let ttl = 45;\r\n          if (qrData.ttl) {\r\n            ttl = Math.max(1, qrData.ttl - Math.floor(Date.now() / 1000));\r\n          }\r\n          \r\n          // Reiniciar timer com TTL correto\r\n          startQrCodeTimer(ttl);\r\n          \r\n          // Mostrar toast se foi renovado\r\n          if (data.renewed) {\r\n            console.log('🔄 QR Code renovado automaticamente via socket');\r\n          }\r\n        } else {\r\n          console.error('❌ [SOCKET] QR Code inválido recebido via socket:', JSON.stringify(qrData, null, 2));\r\n        }\r\n      }\r\n    });\r\n\r\n    // 📱 LISTENER MELHORADO PARA CONNECTION UPDATES VIA SOCKET - COM FILTRO POR EMPRESA\r\n    mainSocket.on('whatsapp_connection_update', (data) => {\r\n      console.log('🔗 [SOCKET] Status de conexão atualizado:', {\r\n        instanceId: data.instanceId,\r\n        empresaID: data.empresaID,\r\n        state: data.state,\r\n        isConnected: data.isConnected,\r\n        statusCode: data.statusCode,\r\n        timestamp: data.timestamp\r\n      });\r\n\r\n      // **🎯 FILTRO CRÍTICO: IGNORAR CONNECTION UPDATES DE OUTRAS EMPRESAS**\r\n      if (data.empresaID && data.empresaID !== empresaID) {\r\n        console.log(`🚫 [SOCKET] Connection update ignorado - empresa diferente! Recebido: ${data.empresaID}, Atual: ${empresaID}`);\r\n        return;\r\n      }\r\n\r\n      if (data.isConnected || data.state === 'open') {\r\n        console.log('✅ [SOCKET] WhatsApp conectado via connection update!');\r\n        setIsLoged(true);\r\n        setGeneratingQrCode(false);\r\n        setQrCodeImg('');\r\n        clearQrCodeTimers();\r\n      }\r\n    });\r\n\r\n    // 📱 NOVO LISTENER PARA EVENTO DE CONEXÃO ESTABELECIDA - COM FILTRO POR EMPRESA\r\n    mainSocket.on('whatsapp_connected', (data) => {\r\n      console.log('🎉 [SOCKET] WhatsApp conectado com sucesso!', {\r\n        instanceId: data.instanceId,\r\n        empresaID: data.empresaID,\r\n        message: data.message,\r\n        timestamp: data.timestamp\r\n      });\r\n\r\n      // **🎯 FILTRO CRÍTICO: IGNORAR CONEXÕES DE OUTRAS EMPRESAS**\r\n      if (data.empresaID && data.empresaID !== empresaID) {\r\n        console.log(`🚫 [SOCKET] Conexão ignorada - empresa diferente! Recebido: ${data.empresaID}, Atual: ${empresaID}`);\r\n        return;\r\n      }\r\n\r\n      // Atualizar interface imediatamente\r\n      setIsLoged(true);\r\n      setGeneratingQrCode(false);\r\n      setQrCodeImg('');\r\n      clearQrCodeTimers();\r\n\r\n      // Mostrar toast de sucesso\r\n      /*toast.success('🎉 WhatsApp conectado com sucesso!', {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n        hideProgressBar: false,\r\n        closeOnClick: true,\r\n        pauseOnHover: true,\r\n        draggable: true,\r\n      });*/\r\n\r\n      // Recarregar chats apenas se não foi o carregamento inicial\r\n      setTimeout(() => {\r\n        console.log('🔄 Verificando se precisa recarregar chats após conexão...');\r\n        if (!isInitialLoad && chats.length > 0) {\r\n          console.log('♻️ Recarregando chats após reconexão...');\r\n          setChats([]);\r\n          setPage(0);\r\n          setHasMoreChats(true);\r\n          fetchChats(0, false, 'socket_reconnection');\r\n        } else {\r\n          console.log('✅ Carregamento inicial ainda em andamento, não duplicando...');\r\n        }\r\n      }, 1000);\r\n    });\r\n\r\n    return () => {\r\n      console.log(\"🛑 Limpando eventos específicos do componente WhatsApp...\");\r\n      // ✅ LIMPEZA ESPECÍFICA - não afetar outros componentes que usam o socket\r\n      if (mainSocket) {\r\n        mainSocket.off('chats');\r\n        mainSocket.off('connect');\r\n        mainSocket.off('disconnect');\r\n        mainSocket.off('connect_error');\r\n        mainSocket.off('reconnect');\r\n        mainSocket.off('whatsapp_qrcode_update');\r\n        mainSocket.off('whatsapp_connection_update');\r\n        mainSocket.off('whatsapp_connected'); // 📱 NOVO EVENTO\r\n\r\n        console.log(\"✅ Eventos específicos do WhatsApp removidos - socket mantido para outras telas\");\r\n      }\r\n    };\r\n  }, [mainSocket]); // Remover dependências desnecessárias que causam re-execução\r\n\r\n  useEffect(() => {\r\n    document.addEventListener(\"click\", handleClickOutside);\r\n    return () => document.removeEventListener(\"click\", handleClickOutside);\r\n  });\r\n\r\n  useEffect(() => {\r\n    // Defina isMounted como true ao montar o componente\r\n    isMountedRef.current = true;\r\n    cancelQrCodeFetchRef.current = false; // Redefine o cancelamento ao montar o componente\r\n\r\n    // **📌 VERIFICAR STATUS REAL DA EVOLUTION API PRIMEIRO**\r\n    console.log('🚀 Iniciando verificação de status REAL da Evolution API...');\r\n\r\n    // Função assíncrona para verificar status e decidir o que fazer\r\n    const initializeWhatsApp = async () => {\r\n      try {\r\n        // Primeiro verificar se realmente está conectado\r\n        const isConnected = await checkConnectionStatus('component_mount');\r\n\r\n        if (!isConnected) {\r\n          // Se não estiver conectado, NÃO gerar QR code automaticamente\r\n          console.log('📱 Não conectado, aguardando comando manual do usuário para gerar QR Code...');\r\n          // 🔧 REMOVIDO: await fetchQrCode('component_mount_disconnected');\r\n        } else {\r\n          console.log('✅ Já conectado, mantendo interface de chat');\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ Erro na inicialização do WhatsApp:', error);\r\n        // Em caso de erro, NÃO gerar QR code automaticamente\r\n        console.log('⚠️ Erro na verificação, aguardando comando manual do usuário...');\r\n        // 🔧 REMOVIDO: await fetchQrCode('component_mount_error');\r\n      }\r\n    };\r\n\r\n    initializeWhatsApp();\r\n\r\n    // Função de limpeza ao desmontar o componente\r\n    return () => {\r\n      isMountedRef.current = false; // Define como false quando o componente desmonta\r\n      cancelQrCodeFetchRef.current = true; // Cancela qualquer execução futura de fetchQrCode\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current);\r\n        console.log('🛑 Timeout cleared on unmount');\r\n      }\r\n      // **📌 LIMPAR TIMER DE CONVERSA RECÉM ABERTA**\r\n      if (justOpenedTimerRef.current) {\r\n        clearTimeout(justOpenedTimerRef.current);\r\n        console.log('🛑 justOpenedTimer cleared on unmount');\r\n      }\r\n      // 📱 Limpar timers do QR Code\r\n      clearQrCodeTimers();\r\n      console.log('🛑 QR Code timers cleared on unmount');\r\n    };\r\n  }, []);\r\n\r\n  // **📌 FUNÇÃO PARA VERIFICAR STATUS REAL DA EVOLUTION API**\r\n  const checkConnectionStatus = async (source = 'unknown') => {\r\n    if (!isMountedRef.current) return false;\r\n\r\n    try {\r\n      console.log(`🔍 [CHECK STATUS] Verificando status REAL da Evolution API... (fonte: ${source})`);\r\n      const response = await getWhatsappConnectionStatus(empresaID);\r\n\r\n      console.log('📡 [CHECK STATUS] Resposta da Evolution API:', response.data);\r\n\r\n      const isConnected = response.data.isConnected;\r\n      const connectionState = response.data.connectionState;\r\n\r\n      // ✅ SEMPRE USAR STATUS REAL DA EVOLUTION API (NUNCA DO BANCO)\r\n      if (isConnected && connectionState === 'open') {\r\n        console.log('✅ [CHECK STATUS] WhatsApp REALMENTE conectado na Evolution API!');\r\n        setIsLoged(true);\r\n        setGeneratingQrCode(false);\r\n        setQrCodeImg('');\r\n        clearQrCodeTimers(); // Limpar timers se conectado\r\n        return true; // Conectado\r\n      } else {\r\n        console.log(`📱 [CHECK STATUS] WhatsApp REALMENTE desconectado na Evolution API (${connectionState})`);\r\n        setIsLoged(false);\r\n        return false; // Desconectado\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ [CHECK STATUS] Erro ao verificar status na Evolution API:', error);\r\n      console.log('📱 [CHECK STATUS] Assumindo desconectado devido ao erro');\r\n      setIsLoged(false);\r\n      return false; // Em caso de erro, assumir desconectado\r\n    }\r\n  };\r\n\r\n  const fetchQrCode = async (source = 'unknown') => {\r\n    console.log(`🔍 [FETCH QR CODE] Iniciado por: ${source}`);\r\n\r\n    if (!isMountedRef.current) {\r\n      console.log('🚫 [FETCH QR CODE] Componente não montado, cancelando');\r\n      return;\r\n    }\r\n\r\n    // **📌 PRIMEIRO VERIFICAR STATUS DE CONEXÃO**\r\n    console.log('🔍 [FETCH QR CODE] Verificando status de conexão...');\r\n    const isConnected = await checkConnectionStatus(`fetchQrCode_${source}`);\r\n    if (isConnected) {\r\n      console.log('✅ [FETCH QR CODE] Já conectado, cancelando geração de QR Code');\r\n      clearQrCodeTimers(); // Limpar timers se conectado\r\n      return; // Se conectado, não precisa gerar QR Code\r\n    }\r\n\r\n    // **📌 SE DESCONECTADO, GERAR QR CODE**\r\n    console.log(`🔄 [FETCH QR CODE] Desconectado, gerando QR Code... (fonte: ${source})`);\r\n    setGeneratingQrCode(true);\r\n\r\n    getQrCodeWhatsapp(empresaID).then((response) => {\r\n      console.log('📡 QR Code Response completa:', response.data);\r\n\r\n      if (!isMountedRef.current) return;\r\n\r\n      // 🔧 TRATAMENTO MELHORADO DA RESPOSTA DO QR CODE (ALINHADO COM BACKEND)\r\n      console.log('📊 Resposta completa da API:', response.data);\r\n\r\n      // Verificar se já está conectado\r\n      if (response.data.isConnected) {\r\n        console.log('✅ WhatsApp já está conectado segundo a API');\r\n        setIsLoged(true);\r\n        setGeneratingQrCode(false);\r\n        setQrCodeImg('');\r\n        clearQrCodeTimers();\r\n        return;\r\n      }\r\n\r\n      let qrCodeData = null;\r\n      let ttl = 45; // TTL padrão\r\n\r\n      // Verificar diferentes formatos de resposta da Evolution API\r\n      if (response.data.qrcode) {\r\n        const qrData = response.data.qrcode;\r\n        \r\n        if (qrData.qr) {\r\n          // Formato: { qrcode: { qr: \"data:image/png;base64,...\" } }\r\n          qrCodeData = qrData.qr;\r\n          console.log('📱 QR Code encontrado em qrcode.qr');\r\n        } else if (qrData.base64) {\r\n          // Formato: { qrcode: { base64: \"data:image/png;base64,...\" } }\r\n          qrCodeData = qrData.base64;\r\n          console.log('📱 QR Code encontrado em qrcode.base64');\r\n        } else if (typeof qrData === 'string') {\r\n          // Formato: { qrcode: \"data:image/png;base64,...\" }\r\n          qrCodeData = qrData;\r\n          console.log('📱 QR Code encontrado como string direta');\r\n        }\r\n\r\n        // Extrair TTL se disponível\r\n        if (qrData.ttl) {\r\n          ttl = Math.max(1, qrData.ttl - Math.floor(Date.now() / 1000));\r\n          console.log(`⏱️ TTL extraído do backend: ${ttl} segundos`);\r\n        }\r\n      }\r\n\r\n      if (qrCodeData) {\r\n        // Validar se é uma imagem base64 válida\r\n        if (qrCodeData.startsWith('data:image/')) {\r\n          setQrCodeImg(qrCodeData);\r\n          console.log('✅ QR Code definido com sucesso - Tamanho:', qrCodeData.length);\r\n          setGeneratingQrCode(false);\r\n\r\n          // 📱 INICIAR TIMER COM TTL DO BACKEND\r\n          startQrCodeTimer(ttl);\r\n        } else {\r\n          console.error('❌ QR Code não é uma imagem base64 válida:', qrCodeData.substring(0, 100));\r\n          setGeneratingQrCode(false);\r\n        }\r\n      } else {\r\n        console.error('❌ QR Code não encontrado na resposta:', response.data);\r\n        setGeneratingQrCode(false);\r\n      }\r\n\r\n      // ❌ REMOVIDO: Lógica antiga de regeneração automática\r\n      // Agora usamos timer fixo de 20 segundos com botão manual de regeneração\r\n\r\n      // ❌ REMOVIDO: Verificação periódica antiga - agora usamos socket listeners e timer fixo de 20s\r\n\r\n    }).catch((error) => {\r\n      console.error('Erro ao buscar QR Code:', error);\r\n      setGeneratingQrCode(false);\r\n\r\n      // Em caso de erro, tenta novamente após 10 segundos\r\n      if (isMountedRef.current && !cancelQrCodeFetchRef.current) {\r\n        console.log('🔄 [FETCH QR CODE] Erro - tentando novamente em 10 segundos...');\r\n        timeoutRef.current = setTimeout(() => {\r\n          if (isMountedRef.current && !cancelQrCodeFetchRef.current) {\r\n            console.log('🔄 [FETCH QR CODE] Retry após erro');\r\n            fetchQrCode('error_retry');\r\n          }\r\n        }, 10000);\r\n      }\r\n    }).finally(() => {\r\n      if (isMountedRef.current) {\r\n        setGeneratingQrCode(false);\r\n      }\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (selectedChat?._id) {\r\n      getBotStatusLead(selectedChat._id)\r\n        .then(response => {\r\n          console.log(\"Status desse bot:\", response.data.bot_pausado)\r\n          setBotPausado(response.data.bot_pausado);\r\n        })\r\n        .catch(error => {\r\n          console.error(\"Erro ao obter status do bot:\", error);\r\n        });\r\n\r\n      // Buscar imagem de perfil quando um chat é selecionado\r\n      fetchProfilePicture(selectedChat);\r\n    }\r\n  }, [selectedChat]);\r\n\r\n  // **📌 FUNÇÃO PARA BUSCAR IMAGEM DE PERFIL (OTIMIZADA)**\r\n  const fetchProfilePicture = async (chat, forceUpdate = false, priority = 'normal') => {\r\n    // **📌 CACHE MAIS INTELIGENTE - 7 DIAS AO INVÉS DE 24H**\r\n    const cacheValidityDays = 7;\r\n    const hasRecentProfilePicture = chat.channel_data?.whatsapp?.profile_picture_updateAt &&\r\n      new Date() - new Date(chat.channel_data.whatsapp.profile_picture_updateAt) < cacheValidityDays * 24 * 60 * 60 * 1000;\r\n\r\n    // **📌 VERIFICAÇÕES PARA EVITAR REQUISIÇÕES DESNECESSÁRIAS**\r\n    if (!forceUpdate && chat.channel_data?.whatsapp?.profile_picture && hasRecentProfilePicture) {\r\n\r\n      return;\r\n    }\r\n\r\n    // **📌 EVITAR MÚLTIPLAS REQUISIÇÕES SIMULTÂNEAS PARA O MESMO CHAT**\r\n    const requestKey = `profile_picture_${chat._id}`;\r\n    if (window.profilePictureRequests && window.profilePictureRequests[requestKey]) {\r\n\r\n      return;\r\n    }\r\n\r\n    // Marcar requisição como em andamento\r\n    if (!window.profilePictureRequests) window.profilePictureRequests = {};\r\n    window.profilePictureRequests[requestKey] = true;\r\n\r\n    try {\r\n      const response = await getWhatsappProfilePicture(chat._id, token);\r\n\r\n      if (response.data && response.data.status === 200 && response.data.profilePictureUrl) {\r\n\r\n        // Atualizar o chat na lista local\r\n        setChats(prevChats =>\r\n          prevChats.map(c =>\r\n            c._id === chat._id\r\n              ? {\r\n                ...c,\r\n                channel_data: {\r\n                  ...c.channel_data,\r\n                  whatsapp: {\r\n                    ...c.channel_data?.whatsapp,\r\n                    profile_picture: response.data.profilePictureUrl,\r\n                    profile_picture_updateAt: new Date().toISOString()\r\n                  }\r\n                }\r\n              }\r\n              : c\r\n          )\r\n        );\r\n\r\n        // Se é o chat selecionado, atualizar também\r\n        if (selectedChat && selectedChat._id === chat._id) {\r\n          setSelectedChat(prev => ({\r\n            ...prev,\r\n            channel_data: {\r\n              ...prev.channel_data,\r\n              whatsapp: {\r\n                ...prev.channel_data?.whatsapp,\r\n                profile_picture: response.data.profilePictureUrl,\r\n                profile_picture_updateAt: new Date().toISOString()\r\n              }\r\n            }\r\n          }));\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(`❌ Erro ao buscar imagem de perfil para ${chat.name}:`, error);\r\n    } finally {\r\n      // Remover marcação de requisição em andamento\r\n      if (window.profilePictureRequests) {\r\n        delete window.profilePictureRequests[requestKey];\r\n      }\r\n    }\r\n  };\r\n\r\n  // **📌 FUNÇÃO PARA BUSCAR IMAGENS EM LOTE DE FORMA INTELIGENTE**\r\n  const fetchProfilePicturesBatch = (chats, batchName = 'batch') => {\r\n    const chatsNeedingImages = chats.filter(chat => {\r\n      const hasImage = chat.channel_data?.whatsapp?.profile_picture;\r\n      const cacheValidityDays = 7;\r\n      const hasRecentUpdate = chat.channel_data?.whatsapp?.profile_picture_updateAt &&\r\n        new Date() - new Date(chat.channel_data.whatsapp.profile_picture_updateAt) < cacheValidityDays * 24 * 60 * 60 * 1000;\r\n\r\n      return !hasImage || !hasRecentUpdate;\r\n    });\r\n\r\n    if (chatsNeedingImages.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // **📌 PRIORIZAR PRIMEIROS 5 CHATS (MAIS VISÍVEIS)**\r\n    const priorityChats = chatsNeedingImages.slice(0, 5);\r\n    const regularChats = chatsNeedingImages.slice(5);\r\n\r\n    // Buscar imagens prioritárias primeiro (delay menor)\r\n    priorityChats.forEach((chat, index) => {\r\n      setTimeout(() => {\r\n        fetchProfilePicture(chat, false, 'high');\r\n      }, index * 300); // 300ms entre cada (mais rápido)\r\n    });\r\n\r\n    // Buscar imagens regulares depois (delay maior)\r\n    regularChats.forEach((chat, index) => {\r\n      setTimeout(() => {\r\n        fetchProfilePicture(chat, false, 'normal');\r\n      }, (priorityChats.length * 300) + (index * 800)); // 800ms entre cada (mais devagar)\r\n    });\r\n  };\r\n\r\n  // **📌 FUNÇÃO PARA BUSCAR CONTAGEM DE MENSAGENS NÃO LIDAS**\r\n  const fetchUnreadCounts = async () => {\r\n    try {\r\n      const response = await getWhatsappUnreadCount(empresaID, token);\r\n      if (response.data && response.data.status === 200) {\r\n        setUnreadCounts(response.data.unreadCounts);\r\n        //console.log(`📊 Contagens de mensagens não lidas atualizadas:`, response.data.unreadCounts);\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Erro ao buscar contagem de mensagens não lidas:', error);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // **📌 FUNÇÃO PARA DETECTAR CONTATOS COM NOMES GENÉRICOS**\r\n  const hasGenericName = (contactName, mobileNumber) => {\r\n    if (!contactName || !mobileNumber) return false;\r\n    \r\n    // Verificar se o nome é genérico\r\n    const cleanName = contactName.trim();\r\n    const cleanNumber = mobileNumber.toString();\r\n    \r\n    const genericPatterns = [\r\n      cleanName === cleanNumber, // Nome igual ao número\r\n      cleanName.startsWith('Contato '), // Nome começa com \"Contato \"\r\n      /^\\d{10,}$/.test(cleanName), // Nome é só números (10+ dígitos)\r\n      cleanName === `Contato ${cleanNumber}`, // Formato exato \"Contato 556299999\"\r\n      cleanName.length < 3, // Nomes muito curtos provavelmente são genéricos\r\n      /^[+\\-\\(\\)\\s\\d]+$/.test(cleanName) // Nome contém apenas números e símbolos telefônicos\r\n    ];\r\n    \r\n    return genericPatterns.some(pattern => pattern);\r\n  };\r\n\r\n\r\n\r\n  // **📌 FUNÇÃO PARA ATUALIZAR INFORMAÇÕES DE UM CONTATO ESPECÍFICO**\r\n  const refreshContactInfo = async (chatId, chatName) => {\r\n    try {\r\n      console.log(`🔄 Atualizando informações do contato ${chatName}...`);\r\n      const response = await refreshContactInfoWhatsapp(chatId, token);\r\n      \r\n      if (response.data && response.data.status === 200) {\r\n        console.log(`✅ Informações atualizadas:`, response.data);\r\n\r\n        // Atualizar o chat na lista local se houve mudança\r\n        if (response.data.contactName !== response.data.oldName || response.data.profilePictureUpdated) {\r\n          setChats(prevChats =>\r\n            prevChats.map(c =>\r\n              c._id === chatId\r\n                ? {\r\n                  ...c,\r\n                  name: response.data.contactName,\r\n                  channel_data: {\r\n                    ...c.channel_data,\r\n                    whatsapp: {\r\n                      ...c.channel_data?.whatsapp,\r\n                      profile_picture: response.data.profilePictureUrl || c.channel_data?.whatsapp?.profile_picture,\r\n                      profile_picture_updateAt: response.data.profilePictureUpdated ? new Date().toISOString() : c.channel_data?.whatsapp?.profile_picture_updateAt,\r\n                      pushName: response.data.contactName\r\n                    }\r\n                  }\r\n                }\r\n                : c\r\n            )\r\n          );\r\n\r\n          // Se é o chat selecionado, atualizar também\r\n          if (selectedChat && selectedChat._id === chatId) {\r\n            setSelectedChat(prev => ({\r\n              ...prev,\r\n              name: response.data.contactName,\r\n              channel_data: {\r\n                ...prev.channel_data,\r\n                whatsapp: {\r\n                  ...prev.channel_data?.whatsapp,\r\n                  profile_picture: response.data.profilePictureUrl || prev.channel_data?.whatsapp?.profile_picture,\r\n                  profile_picture_updateAt: response.data.profilePictureUpdated ? new Date().toISOString() : prev.channel_data?.whatsapp?.profile_picture_updateAt,\r\n                  pushName: response.data.contactName\r\n                }\r\n              }\r\n            }));\r\n          }\r\n        }\r\n\r\n        // Mostrar toast de sucesso\r\n        toast.success(response.data.message, {\r\n          position: \"top-right\",\r\n          autoClose: 3000,\r\n          hideProgressBar: false,\r\n          closeOnClick: true,\r\n          pauseOnHover: true,\r\n          draggable: true,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(`❌ Erro ao atualizar informações do contato ${chatName}:`, error);\r\n      toast.error('Erro ao atualizar informações do contato', {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n    }\r\n  };\r\n\r\n  // **📌 FUNÇÃO PARA BUSCAR MINHA IMAGEM DE PERFIL (PRÓPRIO USUÁRIO)**\r\n  const fetchMyProfilePicture = async (forceUpdate = false) => {\r\n    // **📌 VERIFICAR CACHE LOCAL PRIMEIRO**\r\n    const cachedImage = localStorage.getItem(`my_profile_picture_${empresaID}`);\r\n    const cachedTimestamp = localStorage.getItem(`my_profile_picture_timestamp_${empresaID}`);\r\n\r\n    if (!forceUpdate && cachedImage && cachedTimestamp) {\r\n      const cacheAge = new Date() - new Date(cachedTimestamp);\r\n      const cacheValidityDays = 7;\r\n\r\n      if (cacheAge < cacheValidityDays * 24 * 60 * 60 * 1000) {\r\n        console.log(`✅ Minha imagem de perfil já existe no cache local (válida por ${cacheValidityDays} dias)`);\r\n        setMyProfilePicture(cachedImage);\r\n        return;\r\n      }\r\n    }\r\n\r\n    try {\r\n      const response = await getMyWhatsappProfilePicture(empresaID, token);\r\n\r\n      if (response.data && response.data.status === 200 && response.data.profilePictureUrl) {\r\n\r\n        // Salvar no estado e no cache local\r\n        setMyProfilePicture(response.data.profilePictureUrl);\r\n        localStorage.setItem(`my_profile_picture_${empresaID}`, response.data.profilePictureUrl);\r\n        localStorage.setItem(`my_profile_picture_timestamp_${empresaID}`, new Date().toISOString());\r\n\r\n      } else {\r\n        setMyProfilePicture(null);\r\n        // Limpar cache se não há imagem\r\n        localStorage.removeItem(`my_profile_picture_${empresaID}`);\r\n        localStorage.removeItem(`my_profile_picture_timestamp_${empresaID}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(`❌ Erro ao buscar minha imagem de perfil:`, error);\r\n      \r\n      // **🔧 RECUPERAÇÃO AUTOMÁTICA: Se erro for por falta de JID, tentar buscar JID automaticamente**\r\n      if (error.response?.data?.error?.includes?.('JID do WhatsApp não encontrado')) {\r\n        console.log(`🔄 JID não encontrado. Tentando buscar e salvar JID automaticamente...`);\r\n        \r\n        try {\r\n          const { fetchWhatsappJID } = await import('../../services/api');\r\n          const jidResponse = await fetchWhatsappJID(empresaID, token);\r\n          \r\n          if (jidResponse.data?.status === 200) {\r\n            console.log(`✅ JID recuperado com sucesso: ${jidResponse.data.jid}`);\r\n            console.log(`🔄 Tentando buscar foto de perfil novamente...`);\r\n            \r\n            // Tentar buscar a foto de perfil novamente agora que o JID foi salvo\r\n            const retryResponse = await getMyWhatsappProfilePicture(empresaID, token);\r\n            \r\n            if (retryResponse.data?.status === 200 && retryResponse.data.profilePictureUrl) {\r\n              setMyProfilePicture(retryResponse.data.profilePictureUrl);\r\n              localStorage.setItem(`my_profile_picture_${empresaID}`, retryResponse.data.profilePictureUrl);\r\n              localStorage.setItem(`my_profile_picture_timestamp_${empresaID}`, new Date().toISOString());\r\n              console.log(`🎉 Foto de perfil carregada com sucesso após recuperar JID!`);\r\n              return;\r\n            }\r\n          } else {\r\n            console.log(`⚠️ Não foi possível recuperar o JID:`, jidResponse.data);\r\n          }\r\n        } catch (jidError) {\r\n          console.error(`❌ Erro ao tentar recuperar JID:`, jidError);\r\n        }\r\n      }\r\n      \r\n      setMyProfilePicture(null);\r\n    }\r\n  };\r\n\r\n  // **📌 FUNÇÃO PARA CARREGAR MAIS MENSAGENS (PAGINAÇÃO)**\r\n  const loadMoreMessages = async () => {\r\n    if (!selectedChat || !hasMoreMessages || loadingMoreMessages) {\r\n      return;\r\n    }\r\n\r\n    setLoadingMoreMessages(true);\r\n    setShouldScrollToBottom(false); // **📌 NÃO ROLAR PARA O BOTTOM DURANTE PAGINAÇÃO**\r\n\r\n    try {\r\n      const nextPage = messagesPage + 1;\r\n      const pageSize = 30;\r\n\r\n      const response = await getWhatsappChatLead(empresaID, selectedChat._id, nextPage, pageSize, token);\r\n\r\n      if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {\r\n        const newMessages = response.data.messages[0].data;\r\n        const totalCount = response.data.messages[0].metadata[0]?.totalCount || 0;\r\n\r\n        if (newMessages.length === 0) {\r\n          setHasMoreMessages(false);\r\n        } else {\r\n          // Adicionar novas mensagens ao início da lista (mensagens mais antigas)\r\n          setMessagesLeadChannel(prevMessages => {\r\n            // Evitar duplicatas\r\n            const existingIds = new Set(prevMessages.map(msg => msg._id));\r\n            const uniqueNewMessages = newMessages.filter(msg => !existingIds.has(msg._id));\r\n\r\n            // Adicionar mensagens mais antigas no início\r\n            return [...uniqueNewMessages, ...prevMessages];\r\n          });\r\n\r\n          // Atualizar cache\r\n          const cacheKey = selectedChat._id;\r\n          setMessagesCache(prevCache => {\r\n            const newCache = new Map(prevCache);\r\n            const existingMessages = newCache.get(cacheKey) || [];\r\n\r\n            const existingIds = new Set(existingMessages.map(msg => msg._id));\r\n            const uniqueNewMessages = newMessages.filter(msg => !existingIds.has(msg._id));\r\n\r\n            const finalCacheMessages = [...uniqueNewMessages, ...existingMessages];\r\n\r\n            newCache.set(cacheKey, finalCacheMessages);\r\n            return newCache;\r\n          });\r\n\r\n          setMessagesPage(nextPage);\r\n\r\n          // **📌 VERIFICAR SE AINDA HÁ MAIS MENSAGENS USANDO CALLBACK**\r\n          setMessagesLeadChannel(currentMessages => {\r\n            const currentTotal = currentMessages.length;\r\n\r\n            if (currentTotal >= totalCount) {\r\n              setHasMoreMessages(false);\r\n            }\r\n\r\n            return currentMessages; // Não modificar, apenas verificar\r\n          });\r\n        }\r\n      } else {\r\n        setHasMoreMessages(false);\r\n        console.log(`⚠️ Resposta inválida ao carregar mais mensagens para ${selectedChat.name}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(`❌ Erro ao carregar mais mensagens para ${selectedChat.name}:`, error);\r\n    } finally {\r\n      setLoadingMoreMessages(false);\r\n      setShouldScrollToBottom(true); // **📌 REATIVAR SCROLL PARA O BOTTOM APÓS PAGINAÇÃO**\r\n    }\r\n  };\r\n\r\n  // **📌 HANDLER PARA SCROLL DAS MENSAGENS (CARREGAR MAIS AO ROLAR PARA CIMA)**\r\n  const handleMessagesScroll = (e) => {\r\n    const container = e.target;\r\n    const scrollTop = container.scrollTop;\r\n    const scrollThreshold = 100; // Pixels do topo para disparar carregamento\r\n\r\n    // **📌 NÃO CARREGAR MENSAGENS SE ACABOU DE ABRIR A CONVERSA**\r\n    if (justOpenedChat) {\r\n      console.log(`🚫 Conversa acabou de abrir, ignorando scroll no topo para ${selectedChat?.name}`);\r\n      return;\r\n    }\r\n\r\n    // **📌 IGNORAR SCROLL AUTOMÁTICO DURANTE CARREGAMENTO**\r\n    if (loadingMessages) {\r\n      return;\r\n    }\r\n\r\n    // Se rolou próximo ao topo e há mais mensagens para carregar\r\n    if (scrollTop <= scrollThreshold && hasMoreMessages && !loadingMoreMessages) {\r\n      console.log(`📜 Scroll detectado no topo - carregando mais mensagens para ${selectedChat?.name}`);\r\n\r\n      // **📌 SALVAR POSIÇÃO ATUAL DO SCROLL PARA RESTAURAR APÓS CARREGAR**\r\n      const currentScrollHeight = container.scrollHeight;\r\n      const currentScrollTop = scrollTop;\r\n\r\n      loadMoreMessages().then(() => {\r\n        // **📌 RESTAURAR POSIÇÃO DO SCROLL DE FORMA MAIS ROBUSTA**\r\n        const restoreScroll = () => {\r\n          const newScrollHeight = container.scrollHeight;\r\n          const scrollDifference = newScrollHeight - currentScrollHeight;\r\n          const newScrollTop = currentScrollTop + scrollDifference;\r\n\r\n          // **📌 APLICAR NOVA POSIÇÃO IMEDIATAMENTE**\r\n          container.scrollTop = newScrollTop;\r\n\r\n          // **📌 VERIFICAR SE A POSIÇÃO FOI APLICADA CORRETAMENTE**\r\n          requestAnimationFrame(() => {\r\n            if (Math.abs(container.scrollTop - newScrollTop) > 5) {\r\n              container.scrollTop = newScrollTop;\r\n            }\r\n          });\r\n        };\r\n\r\n        // **📌 TENTAR RESTAURAR IMEDIATAMENTE E DEPOIS COM DELAY**\r\n        restoreScroll();\r\n        setTimeout(restoreScroll, 50); // Backup caso o primeiro não funcione\r\n      });\r\n    }\r\n  };\r\n\r\n  /*useEffect(() => {\r\n    const fetchChats = async () => {\r\n      try {\r\n        const page = 0;\r\n        const pageSize = 30;\r\n        const query = searchQuery; // Adicione seu termo de busca aqui, se houver\r\n\r\n        //console.log(\"O que está sendo enviado:\",{empresaID, page, pageSize, query, token})\r\n        const response = await getWhatsappChats(empresaID, page, pageSize, query, token);\r\n        //console.log(\"O que está sendo Recebido!:\",response)\r\n        if (query === '' || query.length >= 3) {\r\n          setChats(response.data.messages[0].data);\r\n        }\r\n        if (response.data.messages[0].data.length > 0 && query !== '') {\r\n          const data = response.data.messages[0].data;\r\n          if (data.length > 0 && query.length >= 3) {\r\n            //console.log(\"Oq tem aqui?\", data);\r\n            setChats([data[0]]); // Armazena apenas o primeiro elemento como array\r\n          }\r\n        }\r\n        //console.log(response.data.messages[0].data);\r\n        setLoading(false);\r\n      } catch (err) {\r\n        setError(err);\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    // Chame fetchChats inicialmente\r\n    fetchChats();\r\n\r\n  }, [searchQuery]);*/\r\n  const [page, setPage] = useState(0); // Página inicial\r\n  const [hasMoreChats, setHasMoreChats] = useState(true); // Se ainda há chats para carregar\r\n  const chatsContainerRef = useRef(null); // Referência ao container de chats\r\n  const [loadingMore, setLoadingMore] = useState(false); // Para evitar requisições duplicadas\r\n\r\n  const fetchChats = async (newPage, append = false, source = 'unknown') => {\r\n    console.log(\"🎯 fetchChats INICIADO - newPage:\", newPage, \"append:\", append, \"source:\", source);\r\n    console.log(\"📋 Parâmetros - empresaID:\", empresaID, \"token:\", token ? \"presente\" : \"ausente\");\r\n\r\n    // 🛡️ EVITAR EXECUÇÕES DUPLICADAS\r\n    if (fetchingChatsRef.current && !append) {\r\n      console.log(\"🚫 fetchChats já em execução, ignorando chamada duplicada de:\", source);\r\n      return;\r\n    }\r\n\r\n    if (!append) {\r\n      fetchingChatsRef.current = true;\r\n    }\r\n\r\n    try {\r\n      const pageSize = 30;\r\n      const query = searchQuery;\r\n\r\n      console.log(\"🔄 Carregando página:\", newPage);\r\n      console.log(\"🔍 Query de busca:\", query);\r\n\r\n      const response = await getWhatsappChats(empresaID, newPage, pageSize, query, token);\r\n\r\n      const newChats = response.data.messages[0].data;\r\n\r\n      if (newChats.length === 0) {\r\n        setHasMoreChats(false); // Se não há mais chats, parar carregamento\r\n      }\r\n\r\n      setChats((prevChats) => {\r\n        let finalChats;\r\n        if (append) {\r\n          // Remove duplicatas ao adicionar novos chats\r\n          const existingIds = new Set(prevChats.map(chat => chat._id));\r\n          const uniqueNewChats = newChats.filter(chat => !existingIds.has(chat._id));\r\n          finalChats = [...prevChats, ...uniqueNewChats];\r\n        } else {\r\n          // Remove duplicatas nos chats iniciais\r\n          const uniqueChats = newChats.filter((chat, index, self) =>\r\n            index === self.findIndex(c => c._id === chat._id)\r\n          );\r\n          finalChats = uniqueChats;\r\n\r\n          // **📌 INICIAR PRÉ-CARREGAMENTO APENAS NO CARREGAMENTO INICIAL**\r\n          if (uniqueChats.length > 0 && !preloadingChats && !hasPreloaded && searchQuery === '' && isInitialLoad) {\r\n            console.log(\"🚀 Iniciando pré-carregamento único...\");\r\n            setHasPreloaded(true);\r\n            setIsInitialLoad(false);\r\n            setTimeout(() => {\r\n              preloadAllChats(uniqueChats);\r\n            }, 1000); // Delay de 1 segundo para não interferir com a UI\r\n          }\r\n        }\r\n        // **📌 BUSCAR IMAGENS DE PERFIL SIMULTANEAMENTE PARA TODOS OS CHATS CARREGADOS**\r\n        if (!append) {\r\n          // Para carregamento inicial, usar função de lote inteligente\r\n          setTimeout(() => {\r\n            fetchProfilePicturesBatch(finalChats, 'carregamento inicial');\r\n          }, 800); // Delay menor para começar mais cedo\r\n        }\r\n\r\n        // **📌 BUSCAR IMAGENS TAMBÉM PARA CHATS CARREGADOS VIA PAGINAÇÃO**\r\n        if (append && finalChats.length > 0) {\r\n          setTimeout(() => {\r\n            fetchProfilePicturesBatch(finalChats, 'paginação');\r\n          }, 300); // Delay menor para paginação\r\n        }\r\n\r\n        // **📌 BUSCAR CONTAGEM DE MENSAGENS NÃO LIDAS APÓS CARREGAR CHATS**\r\n        if (!append && finalChats.length > 0) {\r\n          setTimeout(() => {\r\n            fetchUnreadCounts();\r\n          }, 500); // Delay para não sobrecarregar\r\n        }\r\n\r\n        return finalChats;\r\n      });\r\n      setLoading(false);\r\n      setLoadingMore(false);\r\n    } catch (err) {\r\n      console.error(\"❌ Erro ao carregar chats:\", err);\r\n      console.error(\"📊 Detalhes do erro:\", {\r\n        message: err.message,\r\n        status: err.response?.status,\r\n        data: err.response?.data,\r\n        config: err.config\r\n      });\r\n      setLoading(false);\r\n      setLoadingMore(false);\r\n    } finally {\r\n      // 🛡️ SEMPRE RESETAR FLAG DE FETCHING\r\n      if (!append) {\r\n        fetchingChatsRef.current = false;\r\n      }\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // **📌 Detectar scroll e carregar mais chats**\r\n  const handleScroll = () => {\r\n    if (!hasMoreChats || loadingMore) return;\r\n\r\n    const container = chatsContainerRef.current;\r\n    if (!container) return;\r\n\r\n    const bottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 10;\r\n    if (bottom) {\r\n      console.log(\"📢 Rolou até o fim, carregando mais chats...\");\r\n      setLoadingMore(true);\r\n      setPage((prevPage) => {\r\n        const nextPage = prevPage + 1;\r\n        fetchChats(nextPage, true, 'pagination');\r\n        return nextPage;\r\n      });\r\n    }\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n\r\n  const formatDate = (date) => {\r\n    const now = moment().tz(\"America/Sao_Paulo\");\r\n    const inputDate = moment(date).tz(\"America/Sao_Paulo\");\r\n\r\n    if (now.isSame(inputDate, 'day')) {\r\n      return inputDate.format('HH:mm');\r\n    } else if (now.subtract(1, 'days').isSame(inputDate, 'day')) {\r\n      return 'Ontem';\r\n    } else if (now.subtract(6, 'days').isBefore(inputDate)) {\r\n      return inputDate.format('dddd');\r\n    } else {\r\n      return inputDate.format('DD/MM/YYYY');\r\n    }\r\n  };\r\n\r\n\r\n  const handleDisconnect = async () => {\r\n    try {\r\n      const res = await removeWhatsappSession(empresaID); // Aguarda a resposta da API\r\n\r\n      // Exibe um toast de sucesso com a mensagem da resposta da API\r\n      toast(res.data.msg, {\r\n        autoClose: 3000,\r\n        type: \"success\"\r\n      });\r\n\r\n      // 🔧 REMOVIDO: geração automática de QR Code após desconexão\r\n      // Agora o usuário deve clicar manualmente para gerar\r\n      setUserRequestedQrCode(false); // Resetar flag para exibir botão de geração\r\n      setQrCodeImg(''); // Limpar QR Code atual\r\n      setIsLoged(false); // Marcar como desconectado\r\n      // fetchQrCode(); // REMOVIDO\r\n    } catch (error) {\r\n      console.error(\"Erro ao encerrar sessão:\", error.response?.data || error.message);\r\n\r\n      // Exibe um toast de erro com a mensagem da API ou um fallback padrão\r\n      toast(error.response?.data?.msg || \"Erro ao encerrar sessão!\", {\r\n        autoClose: 3000,\r\n        type: \"error\"\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleToggleBot = async (lead_id) => {\r\n    try {\r\n      const response = await toggleBotStatus(lead_id);\r\n      setBotPausado(response.data.bot_pausado);\r\n      toast(response.data.msg, {\r\n        autoClose: 3000,\r\n        type: \"success\"\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao alternar o status do bot:\", error);\r\n    }\r\n  };\r\n\r\n  // **📌 FUNÇÃO PARA PRÉ-CARREGAR TODAS AS CONVERSAS**\r\n  const preloadAllChats = async (chatList) => {\r\n    if (!chatList || chatList.length === 0) return;\r\n\r\n\r\n    setPreloadingChats(true);\r\n\r\n    // Priorizar conversas com mensagens mais recentes\r\n    const sortedChats = [...chatList].sort((a, b) => {\r\n      const dateA = new Date(a.message?.messageDate || a.message?.createdAt || 0);\r\n      const dateB = new Date(b.message?.messageDate || b.message?.createdAt || 0);\r\n      return dateB - dateA;\r\n    });\r\n\r\n    // Limitar a 15 conversas mais recentes para não sobrecarregar\r\n    const chatsToPreload = sortedChats.slice(0, 15);\r\n    setPreloadProgress({ current: 0, total: chatsToPreload.length });\r\n\r\n    const batchSize = 3; // Carregar 3 conversas por vez\r\n\r\n    try {\r\n      for (let i = 0; i < chatsToPreload.length; i += batchSize) {\r\n        const batch = chatsToPreload.slice(i, i + batchSize);\r\n\r\n        // Carregar batch em paralelo\r\n        const batchPromises = batch.map(async (chat) => {\r\n          try {\r\n            // Verificar se já não está no cache\r\n            if (messagesCache.has(chat._id)) {\r\n              return;\r\n            }\r\n            const response = await getWhatsappChatLead(empresaID, chat._id, 0, 30, token);\r\n\r\n            if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {\r\n              const messages = response.data.messages[0].data;\r\n\r\n              // Armazenar no cache\r\n              setMessagesCache(prevCache => {\r\n                const newCache = new Map(prevCache);\r\n                newCache.set(chat._id, messages);\r\n                return newCache;\r\n              });\r\n\r\n              //console.log(`✅ ${messages.length} mensagens pré-carregadas para ${chat.name}`);\r\n            } else {\r\n              // Armazenar array vazio para conversas sem mensagens\r\n              setMessagesCache(prevCache => {\r\n                const newCache = new Map(prevCache);\r\n                newCache.set(chat._id, []);\r\n                return newCache;\r\n              });\r\n              //console.log(`📭 Conversa vazia pré-carregada: ${chat.name}`);\r\n            }\r\n          } catch (error) {\r\n            console.error(`❌ Erro ao pré-carregar ${chat.name}:`, error);\r\n            // Armazenar array vazio em caso de erro\r\n            setMessagesCache(prevCache => {\r\n              const newCache = new Map(prevCache);\r\n              newCache.set(chat._id, []);\r\n              return newCache;\r\n            });\r\n          }\r\n        });\r\n\r\n        // Aguardar batch atual\r\n        await Promise.all(batchPromises);\r\n\r\n        // Atualizar progresso\r\n        const completed = Math.min(i + batchSize, chatsToPreload.length);\r\n        setPreloadProgress({ current: completed, total: chatsToPreload.length });\r\n\r\n        // Delay dinâmico baseado na quantidade de conversas\r\n        if (i + batchSize < chatsToPreload.length) {\r\n          const delay = chatsToPreload.length > 10 ? 400 : 200; // Delay maior para listas maiores\r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n        }\r\n      }\r\n\r\n      console.log(`🎉 Pré-carregamento concluído! ${chatsToPreload.length} conversas armazenadas no cache.`);\r\n      console.log(`💾 Cache atual contém ${messagesCache.size} conversas.`);\r\n\r\n      // Feedback visual de sucesso\r\n      /*setTimeout(() => {\r\n        toast.success(`✅ ${chatsToPreload.length} conversas pré-carregadas com sucesso!`, {\r\n          position: \"top-right\",\r\n          autoClose: 3000,\r\n          hideProgressBar: false,\r\n          closeOnClick: true,\r\n          pauseOnHover: true,\r\n          draggable: true,\r\n        });\r\n      }, 500);*/\r\n\r\n    } catch (error) {\r\n      console.error('❌ Erro no pré-carregamento:', error);\r\n      toast.error('⚠️ Erro ao pré-carregar conversas', {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n    } finally {\r\n      setPreloadingChats(false);\r\n      setPreloadProgress({ current: 0, total: 0 });\r\n    }\r\n  };\r\n\r\n  // **📌 Carregar os chats ao iniciar e ao buscar**\r\n  useEffect(() => {\r\n    console.log(\"🚀 useEffect disparado - searchQuery:\", searchQuery);\r\n    console.log(\"📊 Estado atual - empresaID:\", empresaID, \"token:\", token ? \"presente\" : \"ausente\");\r\n\r\n    setChats([]); // Resetar chats ao buscar\r\n    setPage(0);\r\n    setHasMoreChats(true);\r\n    fetchingChatsRef.current = false; // Reset da flag de controle\r\n\r\n    const source = searchQuery ? 'search' : 'initial_load';\r\n    console.log(\"📞 Chamando fetchChats(0, false) -\", source);\r\n    fetchChats(0, false, source);\r\n  }, [searchQuery]);\r\n\r\n  // **📌 Resetar pré-carregamento quando empresa ou token mudar**\r\n  useEffect(() => {\r\n    setHasPreloaded(false);\r\n    setMessagesCache(new Map());\r\n    setPreloadingChats(false);\r\n    setPreloadProgress({ current: 0, total: 0 });\r\n    setRaceConditionsFixed(0);\r\n    setIsInitialLoad(true); // Reset para nova empresa\r\n    fetchingChatsRef.current = false; // Reset da flag de controle\r\n\r\n    // **📌 BUSCAR MINHA IMAGEM DE PERFIL QUANDO EMPRESA/TOKEN MUDAR**\r\n    if (empresaID && token) {\r\n      fetchMyProfilePicture();\r\n    }\r\n  }, [empresaID, token]);\r\n\r\n  // **📌 ATUALIZAÇÃO PERIÓDICA DAS CONTAGENS DE MENSAGENS NÃO LIDAS**\r\n  useEffect(() => {\r\n    if (!empresaID || !token) return;\r\n\r\n    // Atualizar contagens a cada 30 segundos\r\n    const interval = setInterval(() => {\r\n      fetchUnreadCounts();\r\n    }, 30000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [empresaID, token]);\r\n\r\n\r\n\r\n  // **📌 MONITORAMENTO INTELIGENTE DE STATUS E AUTO-RENOVAÇÃO**\r\n  useEffect(() => {\r\n    if (!empresaID || !token || isLoged) return;\r\n\r\n    let monitoringInterval = null;\r\n\r\n    const startMonitoring = () => {\r\n      // Verificar status a cada 10 segundos quando desconectado\r\n      monitoringInterval = setInterval(async () => {\r\n        if (isLoged || !isMountedRef.current) {\r\n          clearInterval(monitoringInterval);\r\n          return;\r\n        }\r\n\r\n        try {\r\n          // Verificação silenciosa de status\r\n          const isConnected = await checkConnectionStatus('monitoring');\r\n          \r\n          if (isConnected) {\r\n            // Se conectou, limpar monitoramento\r\n            clearInterval(monitoringInterval);\r\n            console.log('📊 Monitoramento detectou conexão estabelecida');\r\n          }\r\n        } catch (error) {\r\n          // Ignorar erros silenciosamente no monitoramento\r\n          console.debug('Erro silencioso no monitoramento:', error.message);\r\n        }\r\n      }, 10000); // 10 segundos\r\n    };\r\n\r\n    // Iniciar monitoramento após 30 segundos (dar tempo para conexão inicial)\r\n    const startTimer = setTimeout(() => {\r\n      if (!isLoged && isMountedRef.current) {\r\n        console.log('📊 Iniciando monitoramento de status inteligente');\r\n        startMonitoring();\r\n      }\r\n    }, 30000);\r\n\r\n    return () => {\r\n      clearTimeout(startTimer);\r\n      if (monitoringInterval) {\r\n        clearInterval(monitoringInterval);\r\n      }\r\n    };\r\n  }, [empresaID, token, isLoged]);\r\n\r\n  // **📌 Mostrar estatísticas de race conditions evitadas**\r\n  useEffect(() => {\r\n    if (raceConditionsFixed > 0 && raceConditionsFixed % 10 === 0) {\r\n      console.log(`🛡️ Race Conditions evitadas: ${raceConditionsFixed}`);\r\n      toast.success(`🛡️ Sistema anti-bug funcionando! ${raceConditionsFixed} conflitos evitados`, {\r\n        position: \"top-right\",\r\n        autoClose: 2000,\r\n        hideProgressBar: false,\r\n        closeOnClick: true,\r\n        pauseOnHover: true,\r\n        draggable: true,\r\n      });\r\n    }\r\n  }, [raceConditionsFixed]);\r\n\r\n  // **📌 Adicionar listener de scroll**\r\n  useEffect(() => {\r\n    const container = chatsContainerRef.current;\r\n    if (container) {\r\n      container.addEventListener(\"scroll\", handleScroll);\r\n    }\r\n    return () => {\r\n      if (container) {\r\n        container.removeEventListener(\"scroll\", handleScroll);\r\n      }\r\n    };\r\n  }, [hasMoreChats, loadingMore]);\r\n\r\n  // **📌 GERENCIAR MENSAGENS DO CHAT SELECIONADO (USANDO SOCKET PRINCIPAL)**\r\n\r\n  // **📌 GERENCIAR MENSAGENS DO CHAT SELECIONADO**\r\n  useEffect(() => {\r\n    if (!mainSocket || !selectedChat) {\r\n      return;\r\n    }\r\n\r\n    if (selectedChat) {\r\n      // **📌 CONTROLE DE REQUISIÇÕES ATIVAS PARA EVITAR RACE CONDITIONS**\r\n      let isCurrentConversation = true;\r\n      const currentChatId = selectedChat._id;\r\n      const currentChatName = selectedChat.name;\r\n\r\n      // **📌 LIMPAR MENSAGENS E INICIAR LOADING IMEDIATAMENTE AO TROCAR CONVERSA**\r\n      setMessagesLeadChannel([]);\r\n      setLoadingMessages(true);\r\n      setShouldScrollToBottom(true); // **📌 REATIVAR SCROLL PARA O BOTTOM EM NOVA CONVERSA**\r\n      \r\n      // **📌 MARCAR QUE ACABOU DE ABRIR UMA CONVERSA**\r\n      setJustOpenedChat(true);\r\n      if (justOpenedTimerRef.current) {\r\n        clearTimeout(justOpenedTimerRef.current);\r\n      }\r\n      \r\n      // **📌 LIMPAR FLAG APÓS 3 SEGUNDOS PARA PERMITIR CARREGAMENTO DE MENSAGENS ANTIGAS**\r\n      justOpenedTimerRef.current = setTimeout(() => {\r\n        setJustOpenedChat(false);\r\n        console.log(`✅ Conversa ${currentChatName} liberada para carregamento de mensagens antigas após 3s`);\r\n      }, 3000);\r\n\r\n      mainSocket.emit('join-lead-messages', { leadID: selectedChat._id });\r\n\r\n      const fetchChatLead = async () => {\r\n        // **📌 PEQUENO DELAY PARA EVITAR RACE CONDITIONS EM TROCAS MUITO RÁPIDAS**\r\n        await new Promise(resolve => setTimeout(resolve, 50));\r\n\r\n        // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA APÓS DELAY**\r\n        if (!isCurrentConversation) {\r\n          //console.log(`🚫 Fetchamento cancelado após delay - conversa ${currentChatName} não é mais ativa`);\r\n          setRaceConditionsFixed(prev => prev + 1);\r\n          return;\r\n        }\r\n\r\n        try {\r\n\r\n          // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA**\r\n          if (!isCurrentConversation) {\r\n            //console.log(`🚫 Requisição cancelada - conversa ${currentChatName} não é mais ativa`);\r\n            return;\r\n          }\r\n\r\n          // **📌 VERIFICAR CACHE PRIMEIRO**\r\n          const cachedMessages = messagesCache.get(currentChatId);\r\n          if (cachedMessages && cachedMessages.length > 0) {\r\n\r\n            // **📌 VERIFICAR NOVAMENTE SE AINDA É A CONVERSA ATIVA ANTES DE APLICAR**\r\n            if (isCurrentConversation && selectedChat?._id === currentChatId) {\r\n              setMessagesLeadChannel(cachedMessages);\r\n\r\n              // **📌 RESETAR ESTADOS DE PAGINAÇÃO PARA CACHE**\r\n              // Assumir que cache tem apenas primeira página, pode haver mais mensagens\r\n              setMessagesPage(0);\r\n              setHasMoreMessages(cachedMessages.length >= 30); // Se tem 30 ou mais, provavelmente há mais\r\n\r\n              setLoadingMessages(false);\r\n              \r\n              // **📌 FORÇAR SCROLL PARA BOTTOM IMEDIATAMENTE APÓS CARREGAR CACHE**\r\n              setTimeout(() => {\r\n                const container = messagesContainerRef.current;\r\n                if (container && cachedMessages.length > 0) {\r\n                  container.scrollTop = container.scrollHeight;\r\n                }\r\n              }, 100);\r\n            } else {\r\n              //console.log(`🚫 Cache descartado - conversa ${currentChatName} não é mais ativa`);\r\n              setRaceConditionsFixed(prev => prev + 1);\r\n            }\r\n\r\n            // **📌 OPCIONAL: Fazer requisição em background para atualizar cache**\r\n            // Isso garante que sempre temos as mensagens mais recentes\r\n            setTimeout(async () => {\r\n              try {\r\n                if (!isCurrentConversation) return; // Cancelar se não é mais ativa\r\n\r\n                const page = 0;\r\n                const pageSize = 30;\r\n                const response = await getWhatsappChatLead(empresaID, currentChatId, page, pageSize, token);\r\n\r\n                if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {\r\n                  const freshMessages = response.data.messages[0].data;\r\n\r\n                  // Atualizar cache com mensagens frescas\r\n                  setMessagesCache(prevCache => {\r\n                    const newCache = new Map(prevCache);\r\n                    newCache.set(currentChatId, freshMessages);\r\n                    return newCache;\r\n                  });\r\n\r\n                  // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA ANTES DE ATUALIZAR TELA**\r\n                  if (isCurrentConversation && selectedChat?._id === currentChatId) {\r\n                    setMessagesLeadChannel(freshMessages);\r\n                  } else {\r\n                    //console.log(`🚫 Mensagens frescas descartadas - conversa ${currentChatName} não é mais ativa`);\r\n                  }\r\n                }\r\n              } catch (error) {\r\n                if (!isCurrentConversation) {\r\n                  //console.log(`⏹️ Requisição de background cancelada para ${currentChatName}`);\r\n                } else {\r\n                  //console.log('Erro ao atualizar cache em background:', error);\r\n                }\r\n              }\r\n            }, 1000); // 1 segundo de delay\r\n\r\n            return; // Usar cache, não faz requisição imediata\r\n          }\r\n\r\n          //console.log(`❌ Cache não encontrado para ${currentChatName} - fazendo requisição à API`);\r\n\r\n          // **📌 INICIAR LOADING SE NÃO TIVER CACHE**\r\n          if (isCurrentConversation) {\r\n            //console.log(`⏳ Iniciando loading para conversa sem cache: ${currentChatName}`);\r\n            setLoadingMessages(true);\r\n          }\r\n\r\n          const page = 0;\r\n          const pageSize = 30;\r\n\r\n          const response = await getWhatsappChatLead(empresaID, currentChatId, page, pageSize, token);\r\n\r\n          // **📌 VERIFICAR SE AINDA É A CONVERSA ATIVA ANTES DE APLICAR RESULTADOS**\r\n          if (!isCurrentConversation) {\r\n            setRaceConditionsFixed(prev => prev + 1);\r\n            return;\r\n          }\r\n\r\n          if (response.data && response.data.messages && response.data.messages[0] && response.data.messages[0].data) {\r\n            const messages = response.data.messages[0].data;\r\n\r\n            // **📌 VERIFICAR NOVAMENTE ANTES DE APLICAR**\r\n            if (isCurrentConversation && selectedChat?._id === currentChatId) {\r\n              setMessagesLeadChannel(messages);\r\n\r\n              // **📌 RESETAR ESTADOS DE PAGINAÇÃO PARA NOVA CONVERSA**\r\n              const totalCount = response.data.messages[0].metadata[0]?.totalCount || messages.length;\r\n              setMessagesPage(0); // Primeira página carregada\r\n              setHasMoreMessages(messages.length < totalCount); // Há mais mensagens se carregamos menos que o total\r\n\r\n              // **📌 ARMAZENAR NO CACHE**\r\n              setMessagesCache(prevCache => {\r\n                const newCache = new Map(prevCache);\r\n                newCache.set(currentChatId, messages);\r\n                return newCache;\r\n              });\r\n              \r\n              // **📌 FORÇAR SCROLL PARA BOTTOM IMEDIATAMENTE APÓS CARREGAR DA API**\r\n              setTimeout(() => {\r\n                const container = messagesContainerRef.current;\r\n                if (container && messages.length > 0) {\r\n                  container.scrollTop = container.scrollHeight;\r\n                }\r\n              }, 100);\r\n            } else {\r\n              //console.log(`🚫 Mensagens da API descartadas - conversa ${currentChatName} não é mais ativa`);\r\n            }\r\n          } else {\r\n\r\n            // **📌 VERIFICAR ANTES DE APLICAR ARRAY VAZIO**\r\n            if (isCurrentConversation && selectedChat?._id === currentChatId) {\r\n              console.log(`📭 Aplicando array vazio para conversa ativa: ${currentChatName}`);\r\n              setMessagesLeadChannel([]);\r\n\r\n              // **📌 RESETAR ESTADOS DE PAGINAÇÃO PARA CONVERSA VAZIA**\r\n              setMessagesPage(0);\r\n              setHasMoreMessages(false);\r\n\r\n              // **📌 ARMAZENAR ARRAY VAZIO NO CACHE**\r\n              setMessagesCache(prevCache => {\r\n                const newCache = new Map(prevCache);\r\n                newCache.set(currentChatId, []);\r\n                return newCache;\r\n              });\r\n            } else {\r\n              console.log(`🚫 Array vazio descartado - conversa ${currentChatName} não é mais ativa`);\r\n            }\r\n          }\r\n        } catch (err) {\r\n          if (!isCurrentConversation) {\r\n            console.log(`⏹️ Requisição cancelada para ${currentChatName}`);\r\n          } else {\r\n            console.error(`❌ Erro ao carregar mensagens para ${currentChatName}:`, err);\r\n\r\n            // **📌 VERIFICAR ANTES DE APLICAR ARRAY VAZIO EM ERRO**\r\n            if (isCurrentConversation && selectedChat?._id === currentChatId) {\r\n              console.log(`💥 Aplicando array vazio por erro para conversa ativa: ${currentChatName}`);\r\n              setMessagesLeadChannel([]);\r\n            } else {\r\n              console.log(`🚫 Array vazio por erro descartado - conversa ${currentChatName} não é mais ativa`);\r\n            }\r\n          }\r\n        } finally {\r\n          // **📌 FINALIZAR LOADING APENAS SE AINDA FOR A CONVERSA ATIVA**\r\n          if (isCurrentConversation && selectedChat?._id === currentChatId) {\r\n            setLoadingMessages(false);\r\n          }\r\n        }\r\n      };\r\n\r\n      // **📌 ENTRAR NA SALA DE MENSAGENS ESPECÍFICA DO LEAD**\r\n      mainSocket.emit('join-lead-messages', { leadID: currentChatId });\r\n\r\n      // **📌 ESCUTAR MENSAGENS DESTA CONVERSA ESPECÍFICA**\r\n      mainSocket.on('messages', (newMessage) => {\r\n        // **📌 VERIFICAR SE A MENSAGEM É DA CONVERSA ATIVA**\r\n        if (!isCurrentConversation || selectedChat?._id !== currentChatId) {\r\n          setRaceConditionsFixed(prev => prev + 1);\r\n          return;\r\n        }\r\n\r\n        // **📌 PARAR LOADING SE ESTIVER ATIVO**\r\n        setLoadingMessages(false);\r\n\r\n        // Remover mensagem otimista se for a mesma\r\n        setMessagesLeadChannel(oldMessages => {\r\n          // Filtrar mensagens duplicadas e otimistas do mesmo texto\r\n          const filteredMessages = oldMessages.filter(message => {\r\n            // Remover duplicatas por ID\r\n            if (message._id === newMessage._id) return false;\r\n            // Remover mensagens otimistas se a mensagem real chegou\r\n            if (message.isOptimistic && message.text === newMessage.text && message.fromMe === newMessage.fromMe) {\r\n              return false;\r\n            }\r\n            return true;\r\n          });\r\n          return [newMessage, ...filteredMessages];\r\n        });\r\n\r\n        // **📌 ATUALIZAR CACHE COM NOVA MENSAGEM**\r\n        setMessagesCache(prevCache => {\r\n          const newCache = new Map(prevCache);\r\n          const cachedMessages = newCache.get(currentChatId) || [];\r\n\r\n          // Filtrar mensagens duplicadas e otimistas do cache\r\n          const filteredCachedMessages = cachedMessages.filter(message => {\r\n            if (message._id === newMessage._id) return false;\r\n            if (message.isOptimistic && message.text === newMessage.text && message.fromMe === newMessage.fromMe) {\r\n              return false;\r\n            }\r\n            return true;\r\n          });\r\n\r\n          newCache.set(currentChatId, [newMessage, ...filteredCachedMessages]);\r\n          return newCache;\r\n        });\r\n\r\n        // **📌 MARCAR MENSAGEM COMO LIDA SE NÃO FOR MINHA E O CHAT ESTIVER ABERTO**\r\n        if (!newMessage.fromMe) {\r\n          console.log(`📖 Nova mensagem recebida no chat aberto, marcando como lida automaticamente`);\r\n          setTimeout(() => {\r\n            markChatAsRead(currentChatId);\r\n          }, 1500); // Delay maior para garantir que a mensagem foi processada no backend\r\n        }\r\n        \r\n        // **📌 GARANTIR QUE O SCROLL PERMANEÇA NO BOTTOM APÓS NOVA MENSAGEM**\r\n        setTimeout(() => {\r\n          const container = messagesContainerRef.current;\r\n          if (container && shouldScrollToBottom) {\r\n            container.scrollTop = container.scrollHeight;\r\n          }\r\n        }, 100);\r\n      });\r\n\r\n      fetchChatLead();\r\n\r\n      // **📌 TIMEOUT PARA FORÇAR PARADA DO LOADING EM CASO DE DEMORA**\r\n      const loadingTimeout = setTimeout(() => {\r\n        if (isCurrentConversation && selectedChat?._id === currentChatId) {\r\n          setLoadingMessages(false);\r\n        }\r\n      }, 10000); // 10 segundos\r\n\r\n      return () => {\r\n        // **📌 MARCAR CONVERSA COMO INATIVA PARA CANCELAR REQUISIÇÕES**\r\n        isCurrentConversation = false;\r\n        clearTimeout(loadingTimeout);\r\n        \r\n        // **📌 LIMPAR TIMER DE CONVERSA RECÉM ABERTA**\r\n        if (justOpenedTimerRef.current) {\r\n          clearTimeout(justOpenedTimerRef.current);\r\n        }\r\n        setJustOpenedChat(false);\r\n        \r\n        mainSocket.emit('leave-lead-messages', { leadID: currentChatId });\r\n        mainSocket.off('messages');\r\n        // **📌 NÃO DESCONECTAR O SOCKET PRINCIPAL, APENAS LIMPAR EVENTOS**\r\n      };\r\n    } else {\r\n      // **📌 LIMPAR LOADING QUANDO NÃO HÁ CHAT SELECIONADO**\r\n      setLoadingMessages(false);\r\n      // **📌 NÃO DESCONECTAR O SOCKET PRINCIPAL**\r\n    }\r\n  }, [selectedChat, mainSocket]);\r\n\r\n  // **📌 FUNÇÃO ROBUSTA PARA ABRIR CONVERSA POR LEAD_ID**\r\n  const abrirConversaPorLeadId = useCallback(async (leadId) => {\r\n    if (!leadId) return;\r\n\r\n    console.log(`🎯 [ABRIR CONVERSA] Tentando abrir conversa para lead_id: ${leadId}`);\r\n    console.log(`📊 [ABRIR CONVERSA] Contexto atual:`, {\r\n      chatsCarregados: chats.length,\r\n      empresaID,\r\n      token: token ? 'presente' : 'ausente',\r\n      isMobile,\r\n      showMobileChat\r\n    });\r\n\r\n    try {\r\n      // **ESTRATÉGIA 1: Procurar na lista atual**\r\n      const chatNaLista = chats.find(chat => chat._id === leadId);\r\n      \r\n      if (chatNaLista) {\r\n        console.log(`✅ Chat encontrado na lista atual: ${chatNaLista.name}`);\r\n        setSelectedChat(chatNaLista);\r\n        if (isMobile) {\r\n          setShowMobileChat(true);\r\n        }\r\n        // Marcar como lido se necessário\r\n        if (unreadCounts[leadId] && unreadCounts[leadId] > 0) {\r\n          markChatAsRead(leadId);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // **ESTRATÉGIA 2: Buscar especificamente na API**\r\n      console.log(`🔍 Chat não encontrado na lista, buscando na API...`);\r\n      \r\n      try {\r\n        const response = await getWhatsappChatById(empresaID, leadId, token);\r\n        \r\n        if (response.data && response.data.status === 200 && response.data.chat) {\r\n          const chatEncontrado = response.data.chat;\r\n          console.log(`✅ Chat encontrado na API: ${chatEncontrado.name}`);\r\n          \r\n          // Adicionar o chat à lista no início para futuras consultas\r\n          setChats(prevChats => {\r\n            const chatJaExiste = prevChats.some(chat => chat._id === leadId);\r\n            if (!chatJaExiste) {\r\n              return [chatEncontrado, ...prevChats];\r\n            }\r\n            return prevChats;\r\n          });\r\n          \r\n          // Selecionar o chat\r\n          setSelectedChat(chatEncontrado);\r\n          if (isMobile) {\r\n            setShowMobileChat(true);\r\n          }\r\n          \r\n          // Mostrar toast de sucesso\r\n          toast.success(`💬 Conversa encontrada: ${chatEncontrado.name}`, {\r\n            position: \"top-right\",\r\n            autoClose: 3000,\r\n            hideProgressBar: false,\r\n            closeOnClick: true,\r\n            pauseOnHover: true,\r\n            draggable: true,\r\n          });\r\n          \r\n          return;\r\n        }\r\n      } catch (apiError) {\r\n        console.warn(`⚠️ Erro ao buscar chat na API:`, apiError);\r\n        \r\n        // Se o erro for 404, significa que o chat não existe mais\r\n        if (apiError.response?.status === 404) {\r\n          console.log(`📭 Chat não existe mais no sistema`);\r\n        }\r\n      }\r\n\r\n      // **ESTRATÉGIA 3: Buscar por texto em todas as conversas**\r\n      console.log(`🔍 Tentando busca textual em todas as conversas...`);\r\n      \r\n      try {\r\n        // Fazer uma busca mais ampla sem paginação para encontrar a conversa\r\n        const searchResponse = await getWhatsappChats(empresaID, 0, 100, '', token);\r\n        \r\n        if (searchResponse.data?.messages?.[0]?.data) {\r\n          const todasConversas = searchResponse.data.messages[0].data;\r\n          const chatEncontradoPorBusca = todasConversas.find(chat => chat._id === leadId);\r\n          \r\n          if (chatEncontradoPorBusca) {\r\n            console.log(`✅ Chat encontrado por busca ampla: ${chatEncontradoPorBusca.name}`);\r\n            \r\n            // Adicionar à lista atual\r\n            setChats(prevChats => {\r\n              const chatJaExiste = prevChats.some(chat => chat._id === leadId);\r\n              if (!chatJaExiste) {\r\n                return [chatEncontradoPorBusca, ...prevChats];\r\n              }\r\n              return prevChats;\r\n            });\r\n            \r\n            // Selecionar o chat\r\n            setSelectedChat(chatEncontradoPorBusca);\r\n            if (isMobile) {\r\n              setShowMobileChat(true);\r\n            }\r\n            \r\n            toast.success(`💬 Conversa localizada: ${chatEncontradoPorBusca.name}`, {\r\n              position: \"top-right\",\r\n              autoClose: 3000,\r\n            });\r\n            \r\n            return;\r\n          }\r\n        }\r\n      } catch (searchError) {\r\n        console.warn(`⚠️ Erro na busca ampla:`, searchError);\r\n      }\r\n\r\n      // **ESTRATÉGIA 4: Última tentativa - criar nova conversa**\r\n      console.log(`❌ Chat não encontrado em lugar nenhum para lead_id: ${leadId}`);\r\n      \r\n      // Mostrar modal de confirmação para criar nova conversa\r\n      const confirmarNovaConversa = window.confirm(\r\n        '❓ Conversa não encontrada.\\n\\n' +\r\n        'Isso pode acontecer quando:\\n' +\r\n        '• A conversa é muito antiga\\n' +\r\n        '• O contato foi removido do WhatsApp\\n' +\r\n        '• Houve algum problema na sincronização\\n\\n' +\r\n        'Deseja tentar localizar o contato e iniciar uma nova conversa?'\r\n      );\r\n      \r\n      if (confirmarNovaConversa) {\r\n        // Tentar extrair informações do location.state se disponível\r\n        const stateData = location.state;\r\n        if (stateData && (stateData.nome || stateData.celular)) {\r\n          console.log(`🆕 Tentando criar nova conversa com dados do atendimento:`, stateData);\r\n          \r\n          // Formatar número corretamente (adicionar código do país se necessário)\r\n          let numeroFormatado = stateData.celular;\r\n          if (numeroFormatado && !numeroFormatado.startsWith('55')) {\r\n            numeroFormatado = '55' + numeroFormatado;\r\n          }\r\n          \r\n          try {\r\n            const createResponse = await createWhatsappChat(\r\n              empresaID, \r\n              numeroFormatado, \r\n              stateData.nome, \r\n              token\r\n            );\r\n            \r\n            if (createResponse.data?.status === 200 && createResponse.data.chat) {\r\n              const novaConversa = createResponse.data.chat;\r\n              console.log(`✅ Nova conversa criada: ${novaConversa.name}`);\r\n              \r\n              // Adicionar à lista\r\n              setChats(prevChats => [novaConversa, ...prevChats]);\r\n              \r\n              // Selecionar\r\n              setSelectedChat(novaConversa);\r\n              if (isMobile) {\r\n                setShowMobileChat(true);\r\n              }\r\n              \r\n              toast.success(`🎉 Nova conversa iniciada com ${novaConversa.name}!`, {\r\n                position: \"top-right\",\r\n                autoClose: 4000,\r\n              });\r\n              \r\n              return;\r\n            }\r\n          } catch (createError) {\r\n            console.error(`❌ Erro ao criar nova conversa:`, createError);\r\n            toast.error(`❌ Não foi possível criar nova conversa. Verifique se o número ainda existe.`, {\r\n              position: \"top-right\",\r\n              autoClose: 5000,\r\n            });\r\n          }\r\n        } else {\r\n          toast.info(`ℹ️ Não há dados suficientes para criar uma nova conversa. Tente localizar o contato manualmente.`, {\r\n            position: \"top-right\",\r\n            autoClose: 5000,\r\n          });\r\n        }\r\n      }\r\n      \r\n      // Toast final se nada funcionou\r\n      if (!confirmarNovaConversa) {\r\n        toast.warning(`⚠️ Conversa não localizada. Pode ser uma conversa muito antiga ou o contato não existe mais.`, {\r\n          position: \"top-right\",\r\n          autoClose: 6000,\r\n          hideProgressBar: false,\r\n          closeOnClick: true,\r\n          pauseOnHover: true,\r\n          draggable: true,\r\n        });\r\n      }\r\n      \r\n    } catch (error) {\r\n      console.error(`❌ Erro geral ao tentar abrir conversa:`, error);\r\n      toast.error(`❌ Erro ao abrir conversa. Tente novamente.`, {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n    }\r\n  }, [chats, empresaID, token, isMobile, unreadCounts, markChatAsRead, location.state]);\r\n\r\n  // **📌 Verificar se há um chat correspondente ao lead_id**\r\n  useEffect(() => {\r\n    if (leadIdParaAbrir && chats.length > 0) {\r\n      // Usar a nova função robusta\r\n      abrirConversaPorLeadId(leadIdParaAbrir);\r\n    }\r\n  }, [leadIdParaAbrir, chats.length, abrirConversaPorLeadId]);\r\n\r\n  return (\r\n    <>\r\n      {/*<LeftMenu setSidebar={setSidebar} sidebar={sidebar} />*/}\r\n      <Teste sidebar={sidebar}>\r\n        {!isLoged ?\r\n          <div className=\"contentItemComplete\" style={{ width: '100%' }}>\r\n            <div className=\"input-group inputGroup-adicinaItem container-whatsapp-custom\">\r\n              <div className=\"formGroupRow\">\r\n                <div style={{\r\n                  backgroundColor: 'white',\r\n                  padding: isMobile ? '15px' : '20px',\r\n                  display: 'flex',\r\n                  flexDirection: 'column',\r\n                  gap: isMobile ? '15px' : '20px',\r\n                  margin: 'auto',\r\n                  maxWidth: isMobile ? '100%' : 'none'\r\n                }}>\r\n                  <h2 style={{\r\n                    fontSize: isMobile ? '1.3rem' : '1.5rem',\r\n                    textAlign: isMobile ? 'center' : 'left',\r\n                    marginBottom: isMobile ? '10px' : '15px'\r\n                  }}>Para sincronizar o WhatsApp faça os seguintes passos</h2>\r\n                  <ol style={{\r\n                    paddingLeft: isMobile ? '15px' : '20px',\r\n                    fontSize: isMobile ? '14px' : '16px',\r\n                    lineHeight: '1.5'\r\n                  }}>\r\n                    <li style={{ marginBottom: isMobile ? '8px' : '5px' }}>Abra o WhatsApp no seu celular.</li>\r\n                    <li style={{ marginBottom: isMobile ? '8px' : '5px' }}>Toque em Mais opções no Android ou em Configurações no iPhone</li>\r\n                    <li style={{ marginBottom: isMobile ? '8px' : '5px' }}>Toque em Dispositivos conectados e, em seguida, em Conectar dispositivo.</li>\r\n                    <li style={{ marginBottom: isMobile ? '8px' : '5px' }}>Aponte seu celular para esta tela para escanear o QR code.</li>\r\n                  </ol>\r\n                  <div style={{ display: 'flex', justifyContent: 'center', margin: '20px 0' }}>\r\n                    <div style={{\r\n                      padding: isMobile ? '20px' : '30px',\r\n                      backgroundColor: 'white',\r\n                      border: '1px solid #E0E0E0',\r\n                      borderRadius: '12px',\r\n                      maxWidth: '100%',\r\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\r\n                    }}>\r\n                      <div style={{\r\n                        width: isMobile ? '260px' : '320px',\r\n                        height: isMobile ? '260px' : '320px',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center',\r\n                        borderRadius: '8px',\r\n                        maxWidth: '100%',\r\n                        backgroundColor: '#fafafa',\r\n                        border: '1px solid #f0f0f0'\r\n                      }}>\r\n                        {\r\n                          generatingQrCode ? <Loading type={\"spinningBubbles\"} className=\"generatingQrCodeLoading\" />\r\n                            :\r\n                            qrCodeImg ? (\r\n                              <div style={{ textAlign: 'center' }}>\r\n                                <img\r\n                                  src={`${qrCodeImg}`}\r\n                                  alt=\"QR Code\"\r\n                                  style={{\r\n                                    maxWidth: '100%',\r\n                                    maxHeight: '100%',\r\n                                    width: 'auto',\r\n                                    height: 'auto',\r\n                                    opacity: qrCodeExpired ? 0.5 : 1,\r\n                                    transition: 'opacity 0.3s ease'\r\n                                  }}\r\n                                  onLoad={() => console.log('QR Code carregado com sucesso')}\r\n                                  onError={(e) => {\r\n                                    console.error('Erro ao carregar QR Code:', e);\r\n                                    console.log('URL da imagem:', qrCodeImg);\r\n                                  }}\r\n                                />\r\n\r\n                                {/* 📱 TIMER E CONTROLES MELHORADOS */}\r\n                                <div style={{ marginTop: '15px' }}>\r\n                                  {!qrCodeExpired && qrCodeTimeLeft > 0 && (\r\n                                    <div style={{\r\n                                      color: qrCodeTimeLeft <= 10 ? '#e74c3c' : '#666',\r\n                                      fontSize: '13px',\r\n                                      marginBottom: '10px',\r\n                                      display: 'flex',\r\n                                      alignItems: 'center',\r\n                                      justifyContent: 'center',\r\n                                      gap: '5px',\r\n                                      fontWeight: qrCodeTimeLeft <= 10 ? '600' : 'normal'\r\n                                    }}>\r\n                                      <span>⏱️</span>\r\n                                      <span>\r\n                                        Expira em: {Math.floor(qrCodeTimeLeft / 60)}:{(qrCodeTimeLeft % 60).toString().padStart(2, '0')}\r\n                                        {qrCodeTimeLeft <= 10 && ' ⚠️'}\r\n                                      </span>\r\n                                    </div>\r\n                                  )}\r\n\r\n                                  {qrCodeExpired && !generatingQrCode && (\r\n                                    <div style={{\r\n                                      color: '#e74c3c',\r\n                                      fontSize: '14px',\r\n                                      marginBottom: '10px',\r\n                                      fontWeight: '500',\r\n                                      textAlign: 'center'\r\n                                    }}>\r\n                                      ⚠️ QR Code expirado\r\n                                      <div style={{\r\n                                        fontSize: '12px',\r\n                                        color: '#666',\r\n                                        marginTop: '5px',\r\n                                        fontWeight: 'normal'\r\n                                      }}>\r\n                                        Clique para gerar um novo\r\n                                      </div>\r\n                                    </div>\r\n                                  )}\r\n\r\n                                  {generatingQrCode && (\r\n                                    <div style={{\r\n                                      color: '#25D366',\r\n                                      fontSize: '14px',\r\n                                      marginBottom: '10px',\r\n                                      fontWeight: '500',\r\n                                      textAlign: 'center',\r\n                                      display: 'flex',\r\n                                      alignItems: 'center',\r\n                                      justifyContent: 'center',\r\n                                      gap: '8px'\r\n                                    }}>\r\n                                      <Loading type=\"spin\" color=\"#25D366\" height={16} width={16} />\r\n                                      <span>Gerando novo QR Code...</span>\r\n                                    </div>\r\n                                  )}\r\n\r\n                                  {(qrCodeExpired || showRegenerateButton) && (\r\n                                    <button\r\n                                      onClick={() => regenerateQrCodeViaAPI('manual')}\r\n                                      disabled={generatingQrCode}\r\n                                      style={{\r\n                                        backgroundColor: generatingQrCode ? '#95a5a6' : '#25D366',\r\n                                        color: 'white',\r\n                                        border: 'none',\r\n                                        borderRadius: '8px',\r\n                                        padding: '12px 24px',\r\n                                        cursor: generatingQrCode ? 'not-allowed' : 'pointer',\r\n                                        fontSize: '14px',\r\n                                        fontWeight: '500',\r\n                                        transition: 'all 0.3s ease',\r\n                                        display: 'flex',\r\n                                        alignItems: 'center',\r\n                                        justifyContent: 'center',\r\n                                        gap: '8px',\r\n                                        margin: '0 auto'\r\n                                      }}\r\n                                    >\r\n                                      {generatingQrCode ? (\r\n                                        <>\r\n                                          <span>⏳</span>\r\n                                          <span>Gerando...</span>\r\n                                        </>\r\n                                      ) : (\r\n                                        <>\r\n                                          <span>🔄</span>\r\n                                          <span>Gerar novo QR Code</span>\r\n                                        </>\r\n                                      )}\r\n                                    </button>\r\n                                  )}\r\n                                </div>\r\n                              </div>\r\n                            ) : userRequestedQrCode ? (\r\n                              // 🔧 NOVO: Estado quando usuário solicitou QR Code mas ainda está carregando\r\n                              <div style={{\r\n                                display: 'flex',\r\n                                alignItems: 'center',\r\n                                justifyContent: 'center',\r\n                                flexDirection: 'column',\r\n                                gap: '15px',\r\n                                color: '#666',\r\n                                textAlign: 'center',\r\n                                padding: '20px'\r\n                              }}>\r\n                                <Loading type=\"spin\" color=\"#25D366\" height={40} width={40} />\r\n                                <div style={{ fontSize: isMobile ? '14px' : '16px', fontWeight: '500' }}>\r\n                                  Gerando QR Code...\r\n                                </div>\r\n                                <div style={{ fontSize: isMobile ? '12px' : '14px', color: '#999' }}>\r\n                                  Aguarde enquanto preparamos seu código\r\n                                </div>\r\n                              </div>\r\n                            ) : (\r\n                              // 🔧 NOVO: Estado inicial - Botão para gerar QR Code manualmente\r\n                              <div style={{\r\n                                display: 'flex',\r\n                                alignItems: 'center',\r\n                                justifyContent: 'center',\r\n                                flexDirection: 'column',\r\n                                gap: '20px',\r\n                                color: '#666',\r\n                                textAlign: 'center',\r\n                                padding: '20px'\r\n                              }}>\r\n                                <div style={{\r\n                                  fontSize: '48px',\r\n                                  color: '#25D366',\r\n                                  marginBottom: '10px'\r\n                                }}>\r\n                                  📱\r\n                                </div>\r\n                                <div style={{ fontSize: isMobile ? '16px' : '18px', fontWeight: '500', color: '#333' }}>\r\n                                  Pronto para conectar?\r\n                                </div>\r\n                                <div style={{ \r\n                                  fontSize: isMobile ? '13px' : '14px', \r\n                                  color: '#666',\r\n                                  lineHeight: '1.5',\r\n                                  marginBottom: '10px'\r\n                                }}>\r\n                                  Clique no botão abaixo para gerar seu QR Code e conectar o WhatsApp\r\n                                </div>\r\n                                <button\r\n                                  onClick={generateQrCodeManually}\r\n                                  style={{\r\n                                    backgroundColor: '#25D366',\r\n                                    color: 'white',\r\n                                    border: 'none',\r\n                                    borderRadius: '12px',\r\n                                    padding: '16px 32px',\r\n                                    cursor: 'pointer',\r\n                                    fontSize: '16px',\r\n                                    fontWeight: '600',\r\n                                    transition: 'all 0.3s ease',\r\n                                    display: 'flex',\r\n                                    alignItems: 'center',\r\n                                    justifyContent: 'center',\r\n                                    gap: '10px',\r\n                                    boxShadow: '0 4px 12px rgba(37, 211, 102, 0.3)',\r\n                                    minWidth: '200px'\r\n                                  }}\r\n                                  onMouseOver={(e) => {\r\n                                    e.target.style.backgroundColor = '#128C7E';\r\n                                    e.target.style.transform = 'translateY(-2px)';\r\n                                  }}\r\n                                  onMouseOut={(e) => {\r\n                                    e.target.style.backgroundColor = '#25D366';\r\n                                    e.target.style.transform = 'translateY(0)';\r\n                                  }}\r\n                                >\r\n                                  <span style={{ fontSize: '20px' }}>📱</span>\r\n                                  <span>Gerar QR Code</span>\r\n                                </button>\r\n                              </div>\r\n                            )\r\n                        }\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          :\r\n          <Container>\r\n            <WrapperContainerAll style={{ marginTop: window.innerWidth < 1024 ? '25px' : '0px' }}>\r\n\r\n              <WrapperHMC isMobile={isMobile} showMobileChat={showMobileChat}>\r\n\r\n                <WrapperListHeader>\r\n                  <HeaderListHeader>\r\n                    <UserImage onClick={openMenu}>\r\n                      <img\r\n                        src={myProfilePicture || userParse?.user_img[0] || userIcon}\r\n                        width=\"35\"\r\n                        height=\"35\"\r\n                        alt=\"Foto de perfil do usuário\"\r\n                        onError={(e) => {\r\n                          // Se a imagem falhar ao carregar, tentar buscar uma nova\r\n                          if (e.target.src !== userIcon) {\r\n                            console.log(`❌ Erro ao carregar minha imagem de perfil, buscando nova...`);\r\n                            e.target.src = userIcon;\r\n                            fetchMyProfilePicture(true); // Forçar atualização\r\n                          }\r\n                        }}\r\n                        onDoubleClick={() => {\r\n                          // Duplo clique para forçar atualização da imagem\r\n                          console.log(`🔄 Forçando atualização da minha imagem de perfil`);\r\n                          fetchMyProfilePicture(true);\r\n                        }}\r\n                      />\r\n                    </UserImage>\r\n                    <Actions ref={menuRef}>\r\n                      <StatusIcon role=\"img\" aria-label=\"Ícone de abrir status\" />\r\n                      <ChatIcon role=\"img\" aria-label=\"Ícone de iniciar nova conversa\" />\r\n                      <MenuIcon\r\n                        role=\"img\"\r\n                        aria-label=\"Ícone de expandir menu\"\r\n                        onClick={() => setIsMenuOpen(true)}\r\n                      />\r\n                    </Actions>\r\n                  </HeaderListHeader>\r\n                </WrapperListHeader>\r\n\r\n                <WrapperMenu className={isMenuOpen ? \"active\" : \"\"}>\r\n                  <ActionWrapperMenu>\r\n                    {mockOptions.map(({ id, action }) => (\r\n                      <ActionMenu\r\n                        key={id}\r\n                        onClick={action === \"Desconectar\" ? handleDisconnect : openMenu}\r\n                        className={action === \"Desconectar\" ? \"\" : \"\"} // Apenas desativa se necessário\r\n                      >\r\n                        {action}\r\n                      </ActionMenu>\r\n                    ))}\r\n                  </ActionWrapperMenu>\r\n                </WrapperMenu>\r\n\r\n                <SearchBar searchQuery={searchQuery} setSearchQuery={setSearchQuery} />\r\n\r\n                {/* 📌 INDICADOR DE PRÉ-CARREGAMENTO */}\r\n                {preloadingChats && (\r\n                  <div style={{\r\n                    padding: '8px 16px',\r\n                    backgroundColor: '#f0f8f0',\r\n                    borderLeft: '3px solid #25D366',\r\n                    fontSize: '12px',\r\n                    color: '#666',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    gap: '8px'\r\n                  }}>\r\n                    <Loading type=\"spin\" color=\"#25D366\" height={12} width={12} />\r\n                    <span>\r\n                      Pré-carregando conversas... ({preloadProgress.current}/{preloadProgress.total})\r\n                    </span>\r\n                  </div>\r\n                )}\r\n\r\n                {/* 📌 Adicionamos `ref={chatsContainerRef}` para detectar o scroll */}\r\n                <div className=\"containerChatsWhatsapp\" ref={chatsContainerRef}>\r\n                  {/* Loading inicial dos chats */}\r\n                  {loading && chats.length === 0 && (\r\n                    <div style={{\r\n                      display: 'flex',\r\n                      flexDirection: 'column',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      padding: '40px 20px',\r\n                      color: '#666'\r\n                    }}>\r\n                      <Loading type=\"spin\" color=\"#25D366\" height={40} width={40} />\r\n                      <p style={{ marginTop: '15px', fontSize: '14px' }}>Carregando conversas...</p>\r\n                    </div>\r\n                  )}\r\n\r\n                  {chats.map((chat) => (\r\n                    <WrapperListContacts key={chat._id}\r\n                      isSelected={chat._id === selectedChat?._id}\r\n                      onClick={() => {\r\n                        if (isMobile) {\r\n                          handleSelectChatMobile(chat);\r\n                        } else {\r\n                          setSelectedChat(chat);\r\n                          // **📌 MARCAR MENSAGENS COMO LIDAS AO SELECIONAR CHAT (DESKTOP)**\r\n                          if (unreadCounts[chat._id] && unreadCounts[chat._id] > 0) {\r\n                            markChatAsRead(chat._id);\r\n                          }\r\n                        }\r\n                      }}>\r\n                      <ContactPhotoListContacts>\r\n                        <UnreadBadge count={unreadCounts[chat._id] || 0}>\r\n                          <img\r\n                            src={chat.channel_data?.whatsapp?.profile_picture || userIcon}\r\n                            width=\"60\"\r\n                            alt=\"Foto de perfil do usuário\"\r\n                            onError={(e) => {\r\n                              // Se a imagem falhar ao carregar, tentar buscar uma nova\r\n                              if (e.target.src !== userIcon) {\r\n                                console.log(`❌ Erro ao carregar imagem de perfil para ${chat.name}, buscando nova...`);\r\n                                e.target.src = userIcon;\r\n                                fetchProfilePicture(chat);\r\n                              }\r\n                            }}\r\n                            onDoubleClick={() => {\r\n                              // Duplo clique para forçar atualização da imagem\r\n                              console.log(`🔄 Forçando atualização da imagem de perfil para ${chat.name}`);\r\n                              fetchProfilePicture(chat, true); // forceUpdate = true\r\n                            }}\r\n                            title=\"Duplo clique para atualizar foto de perfil\"\r\n                          />\r\n                        </UnreadBadge>\r\n                      </ContactPhotoListContacts>\r\n                      <MessageDataWrapper>\r\n                        <ContactNameAndTime>\r\n                          <span \r\n                            onDoubleClick={() => {\r\n                              console.log(`🔄 Atualizando informações do contato ${chat.name} via duplo clique`);\r\n                              refreshContactInfo(chat._id, chat.name);\r\n                            }}\r\n                            title=\"Duplo clique para atualizar informações do contato\"\r\n                            style={{ cursor: 'pointer', display: 'flex', alignItems: 'center', gap: '5px' }}\r\n                          >\r\n                            {chat.name}\r\n                            {hasGenericName(chat.name, chat.mobile_number) && (\r\n                              <button\r\n                                onClick={(e) => {\r\n                                  e.stopPropagation();\r\n                                  refreshContactInfo(chat._id, chat.name);\r\n                                }}\r\n                                style={{\r\n                                  background: 'transparent',\r\n                                  border: 'none',\r\n                                  cursor: 'pointer',\r\n                                  color: '#FFA500',\r\n                                  fontSize: '0.8rem',\r\n                                  padding: '0',\r\n                                  margin: '0',\r\n                                  opacity: 0.7,\r\n                                  transition: 'opacity 0.2s ease'\r\n                                }}\r\n                                onMouseOver={(e) => e.target.style.opacity = 1}\r\n                                onMouseOut={(e) => e.target.style.opacity = 0.7}\r\n                                title=\"Este contato tem um nome genérico. Clique para buscar o nome real.\"\r\n                              >\r\n                                🔍\r\n                              </button>\r\n                            )}\r\n                          </span>\r\n                          <p>{chat.message ? formatDate(chat.message.messageDate || chat.message.createdAt) : ''}</p>\r\n                        </ContactNameAndTime>\r\n                        <ContactMessage>\r\n                          {chat.message && chat.message.fromMe && <CheckIcon />}\r\n                          <p>{chat.message ? (chat.message.message || chat.message.text || 'Nova conversa') : 'Nova conversa'}</p>\r\n                        </ContactMessage>\r\n                      </MessageDataWrapper>\r\n                    </WrapperListContacts>\r\n                  ))}\r\n\r\n                  {/* **📌 Indicador de carregamento enquanto busca mais chats** */}\r\n                  {loadingMore && <p className=\"loadingChats-indicator\">🔄 Carregando mais conversas...</p>}\r\n                </div>\r\n\r\n              </WrapperHMC>\r\n\r\n              {(selectedChat && (!isMobile || showMobileChat)) ?\r\n                <WrapperContainerConversa \r\n                  isMobile={isMobile} \r\n                  showMobileChat={showMobileChat}\r\n                  backgroundLoaded={backgroundLoaded}\r\n                >\r\n\r\n                  <Header>\r\n                    <MobileBackButton onClick={handleBackToList}>\r\n                      <BackIcon />\r\n                    </MobileBackButton>\r\n                    <ContactPhoto onClick={handleOpenProfile} style={{ cursor: \"pointer\" }}>\r\n                      <img\r\n                        src={selectedChat.channel_data?.whatsapp?.profile_picture || userIcon}\r\n                        width=\"40\"\r\n                        alt=\"Foto de perfil do usuário\"\r\n                        onError={(e) => {\r\n                          // Se a imagem falhar ao carregar, tentar buscar uma nova\r\n                          if (e.target.src !== userIcon) {\r\n                            console.log(`❌ Erro ao carregar imagem de perfil para ${selectedChat.name}, buscando nova...`);\r\n                            e.target.src = userIcon;\r\n                            fetchProfilePicture(selectedChat);\r\n                          }\r\n                        }}\r\n                        onDoubleClick={(e) => {\r\n                          // Duplo clique para forçar atualização da imagem (evitar abrir perfil)\r\n                          e.stopPropagation();\r\n                          console.log(`🔄 Forçando atualização da imagem de perfil para ${selectedChat.name}`);\r\n                          fetchProfilePicture(selectedChat, true); // forceUpdate = true\r\n                        }}\r\n                        title=\"Duplo clique para atualizar foto de perfil\"\r\n                      />\r\n                    </ContactPhoto>\r\n                    <ContactName>\r\n                      {selectedChat.name}\r\n                      <button\r\n                        onClick={() => refreshContactInfo(selectedChat._id, selectedChat.name)}\r\n                        style={{\r\n                          background: 'transparent',\r\n                          border: 'none',\r\n                          marginLeft: '10px',\r\n                          cursor: 'pointer',\r\n                          color: '#666',\r\n                          fontSize: '1.2rem',\r\n                          padding: '2px',\r\n                          borderRadius: '3px',\r\n                          transition: 'color 0.2s ease'\r\n                        }}\r\n                        onMouseOver={(e) => e.target.style.color = '#25D366'}\r\n                        onMouseOut={(e) => e.target.style.color = '#666'}\r\n                        title=\"Atualizar informações do contato\"\r\n                      >\r\n                        🔄 Atualizar informações do contato\r\n                      </button>\r\n                    </ContactName>\r\n                  </Header>\r\n\r\n                  <WrapperContent\r\n                    ref={messagesContainerRef}\r\n                    onScroll={handleMessagesScroll}\r\n                  >\r\n                    {loadingMessages ? (\r\n                      <div style={{\r\n                        display: 'flex',\r\n                        flexDirection: 'column',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center',\r\n                        height: '100%',\r\n                        color: '#666',\r\n                        gap: '15px'\r\n                      }}>\r\n                        <Loading type=\"spin\" color=\"#25D366\" height={40} width={40} />\r\n                        <p style={{ fontSize: '14px', textAlign: 'center' }}>\r\n                          Carregando mensagens de {selectedChat.name}...\r\n                        </p>\r\n                      </div>\r\n                    ) : Object.keys(groupedMessages).length > 0 ? (\r\n                      <>\r\n                        {/* **📌 INDICADOR DE CARREGAMENTO DE MAIS MENSAGENS** */}\r\n                        {loadingMoreMessages && (\r\n                          <div style={{\r\n                            display: 'flex',\r\n                            justifyContent: 'center',\r\n                            alignItems: 'center',\r\n                            padding: '15px',\r\n                            color: '#666',\r\n                            fontSize: '14px',\r\n                            gap: '10px'\r\n                          }}>\r\n                            <Loading type=\"spin\" color=\"#25D366\" height={20} width={20} />\r\n                            <span>Carregando mensagens anteriores...</span>\r\n                          </div>\r\n                        )}\r\n\r\n                        {/* **📌 INDICADOR QUANDO NÃO HÁ MAIS MENSAGENS** */}\r\n                        {!hasMoreMessages && messagesLeadChannel.length > 30 && (\r\n                          <div style={{\r\n                            display: 'flex',\r\n                            justifyContent: 'center',\r\n                            alignItems: 'center',\r\n                            padding: '15px',\r\n                            color: '#999',\r\n                            fontSize: '12px',\r\n                            fontStyle: 'italic'\r\n                          }}>\r\n                            📜 Início da conversa\r\n                          </div>\r\n                        )}\r\n\r\n                        {Object.keys(groupedMessages).map((date) => (\r\n                          <React.Fragment key={date}>\r\n                            <ChatDate>{moment(date).calendar(null, {\r\n                              sameDay: '[Hoje]',\r\n                              lastDay: '[Ontem]',\r\n                              lastWeek: 'dddd',\r\n                              sameElse: 'DD/MM/YYYY'\r\n                            })}</ChatDate>\r\n                            {groupedMessages[date].map((message) => {\r\n                              const { _id, text, createdAt, fromMe, isOptimistic, status } = message;\r\n                              return (\r\n                                <Message key={_id} fromMe={fromMe}>\r\n                                  <p>{text}</p>\r\n                                  <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>\r\n                                    {moment(createdAt).format('HH:mm')}\r\n                                    {fromMe && isOptimistic && status === 'sending' && (\r\n                                      <span title=\"Enviando...\" style={{ color: '#999', fontSize: '10px' }}>⏳</span>\r\n                                    )}\r\n                                    {fromMe && isOptimistic && status === 'sent' && (\r\n                                      <span title=\"Enviado\" style={{ color: '#25D366', fontSize: '10px' }}>✓</span>\r\n                                    )}\r\n                                    {fromMe && !isOptimistic && (\r\n                                      <span title=\"Entregue\" style={{ color: '#25D366', fontSize: '10px' }}>✓✓</span>\r\n                                    )}\r\n                                  </span>\r\n                                </Message>\r\n                              );\r\n                            })}\r\n                          </React.Fragment>\r\n                        ))}\r\n                        <div ref={messageEndRef} />\r\n                      </>\r\n                    ) : (\r\n                      <div style={{\r\n                        display: 'flex',\r\n                        flexDirection: 'column',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center',\r\n                        height: '100%',\r\n                        color: '#999',\r\n                        textAlign: 'center',\r\n                        padding: '40px 20px'\r\n                      }}>\r\n                        <div style={{\r\n                          fontSize: '48px',\r\n                          marginBottom: '20px',\r\n                          opacity: 0.5\r\n                        }}>\r\n                          💬\r\n                        </div>\r\n                        <p style={{\r\n                          fontSize: '18px',\r\n                          marginBottom: '8px',\r\n                          fontWeight: '500',\r\n                          color: '#666'\r\n                        }}>\r\n                          Nenhuma mensagem ainda\r\n                        </p>\r\n                        <p style={{\r\n                          fontSize: '14px',\r\n                          lineHeight: '1.4',\r\n                          maxWidth: '280px'\r\n                        }}>\r\n                          Esta é uma nova conversa com <strong>{selectedChat.name}</strong>.\r\n                          Envie uma mensagem para começar!\r\n                        </p>\r\n                      </div>\r\n                    )}\r\n                  </WrapperContent>\r\n\r\n                  <WrapperChat>\r\n                    <form onSubmit={handleSubmit} style={{ display: \"flex\", alignItems: \"center\", gap: 10 }}>\r\n                      <Input\r\n                        type=\"text\"\r\n                        placeholder=\"Digite uma mensagem\"\r\n                        onChange={({ target }) => setMessage(target.value)}\r\n                        value={message}\r\n                        style={{ fontSize: isMobile ? '16px' : '14px' }}\r\n                      />\r\n\r\n                      <button\r\n                        type=\"submit\"\r\n                        disabled={isSubmitting || !message.trim()}\r\n                        style={{\r\n                          background: \"transparent\",\r\n                          border: \"none\",\r\n                          cursor: isSubmitting || !message.trim() ? \"not-allowed\" : \"pointer\",\r\n                          color: isSubmitting || !message.trim() ? \"#ccc\" : \"#25D366\", // Cor do ícone (verde do WhatsApp)\r\n                          fontSize: isMobile ? \"1.8rem\" : \"2rem\",\r\n                          opacity: isSubmitting || !message.trim() ? 0.6 : 1,\r\n                        }}\r\n                      >\r\n                        {isSubmitting ? (\r\n                          <Loading type=\"spin\" color=\"#25D366\" height={20} width={20} />\r\n                        ) : (\r\n                          <FiSend />\r\n                        )}\r\n                      </button>\r\n\r\n                      {/* Botão de Pausar/Reativar o Bot */}\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => handleToggleBot(selectedChat._id)}\r\n                        style={{\r\n                          background: \"transparent\",\r\n                          border: botPausado ? \"1px solid #FF4D4D\" : \"1px solid #25D366\",\r\n                          cursor: \"pointer\",\r\n                          display: \"flex\",\r\n                          alignItems: \"center\",\r\n                          gap: 5,\r\n                          color: botPausado ? \"#FF4D4D\" : \"#25D366\", // Vermelho se pausado, branco se ativo\r\n                          fontSize: isMobile ? \"12px\" : \"14px\",\r\n                          fontWeight: '600',\r\n                          minWidth: isMobile ? 120 : 140,\r\n                          justifyContent: 'center',\r\n                          borderRadius: 8,\r\n                          padding: isMobile ? '8px' : '10px'\r\n                        }}\r\n                      >\r\n                        {botPausado ? <FiPauseCircle style={{ fontSize: 17 }} /> : <FiPlayCircle style={{ fontSize: 17 }} />}\r\n                        <span>{botPausado ? \"Robô Pausado\" : \"Robô Ativado\"}</span>\r\n                      </button>\r\n                    </form>\r\n                  </WrapperChat>\r\n                  {/* Modal do Perfil */}\r\n                  <UserProfile isOpen={isProfileOpen} onClose={() => setIsProfileOpen(false)} user={selectedChat} />\r\n                </WrapperContainerConversa>\r\n                :\r\n                (!isMobile || !showMobileChat) && <WrapperContainerConversa \r\n                  backgroundLoaded={backgroundLoaded}\r\n                  style={{\r\n                    background: 'white', \r\n                    justifyContent: 'center',\r\n                    display: isMobile ? 'none' : 'flex',\r\n                    flexDirection: 'column',\r\n                    alignItems: 'center'\r\n                  }}\r\n                >\r\n                  <FaWhatsapp style={{ fontSize: '100px', color: 'rgba(0, 0, 0, 0.45)' }} />\r\n                  <Warn style={{ background: 'white' }}>Selecione uma conversa para começar a conversar</Warn>\r\n                </WrapperContainerConversa>\r\n              }\r\n\r\n            </WrapperContainerAll>\r\n\r\n            {isOpen && (\r\n              <S.Wrapper>\r\n                <S.Header>\r\n                  <S.Back>\r\n                    <BackIcon onClick={closeMenu} />\r\n                    <h3>Perfil</h3>\r\n                  </S.Back>\r\n                </S.Header>\r\n\r\n                <S.UserImage>\r\n                  <img\r\n                    src={myProfilePicture || userParse?.user_img[0] || userIcon}\r\n                    width=\"200\"\r\n                    height=\"200\"\r\n                    alt=\"Foto de perfil do usuário\"\r\n                    onError={(e) => {\r\n                      // Se a imagem falhar ao carregar, tentar buscar uma nova\r\n                      if (e.target.src !== userIcon) {\r\n                        console.log(`❌ Erro ao carregar minha imagem de perfil no modal, buscando nova...`);\r\n                        e.target.src = userIcon;\r\n                        fetchMyProfilePicture(true); // Forçar atualização\r\n                      }\r\n                    }}\r\n                    onDoubleClick={() => {\r\n                      // Duplo clique para forçar atualização da imagem\r\n                      console.log(`🔄 Forçando atualização da minha imagem de perfil no modal`);\r\n                      fetchMyProfilePicture(true);\r\n                    }}\r\n                    style={{ cursor: 'pointer' }}\r\n                  />\r\n                </S.UserImage>\r\n\r\n                <S.UserData>\r\n                  <span>Nome</span>\r\n                  <p>{empresaParse.name}</p>\r\n                </S.UserData>\r\n\r\n                <S.UserData>\r\n                  <span>Recado</span>\r\n                  <p>Olá! Eu estou usando o WhatsApp.</p>\r\n                </S.UserData>\r\n              </S.Wrapper>\r\n            )}\r\n          </Container>\r\n        }\r\n\r\n        <GlobalStyles />\r\n\r\n      </Teste>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default WhatsAppWeb;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,QAAQ,OAAO;AAC3E,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD;AACA,OAAO,aAAa;AACpB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,IAAI,MAAM,uBAAuB;AACxC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,OAAOC,YAAY,MAAM,aAAa;AACtC,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAO,KAAKC,CAAC,MAAM,iBAAiB;AACpC,OAAOC,WAAW,MAAM,eAAe,CAAC,CAAC;AACzC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,iBAAiB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,WAAW,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,yBAAyB,EAAEC,2BAA2B,EAAEC,sBAAsB,EAAEC,0BAA0B,EAAEC,2BAA2B,EAAEC,0BAA0B,EAAEC,mBAAmB,EAAEC,kBAAkB,QAAQ,oBAAoB;AACha,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAO,qBAAqB,CAAC,CAAE;AAC/B,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D;AACA,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,SAASC,cAAc,IAAIC,SAAS,QAAQ,iCAAiC;AAC7E,SAASD,cAAc,IAAIE,UAAU,QAAQ,kCAAkC;AAC/E,SAASF,cAAc,IAAIG,QAAQ,QAAQ,gCAAgC;AAC3E,SAASH,cAAc,IAAII,QAAQ,QAAQ,gCAAgC;AAC3E,SAASJ,cAAc,IAAIK,QAAQ,QAAQ,sCAAsC;AACjF,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,MAAM,EAAEC,aAAa,EAAEC,YAAY,QAAQ,gBAAgB,CAAC,CAAC;AACtE,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,WAAW,MAAM,8BAA8B;AAEtD,OAAOC,YAAY,IAAIC,SAAS,QAAQ,qBAAqB;;AAE7D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,KAAK,CAACC,QAAQ,CAACiE,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EACxEnE,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAMkE,QAAQ,GAAGA,CAAA,KAAMH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5DD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAAC;IAC3C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;EAC7D,CAAC,EAAE,EAAE,CAAC;EACN,OAAOJ,QAAQ;AACjB;AAACD,EAAA,CARQD,WAAW;AAUpB,OAAO,MAAMS,mBAAmB,GAAG/D,MAAM,CAACgE,GAAG;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARWF,mBAAmB;AAUhC,OAAO,MAAMG,wBAAwB,GAAGlE,MAAM,CAACgE,GAAG;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBG,KAAK,IAAIA,KAAK,CAACC,gBAAgB,GACjD,OAAO7D,cAAc,GAAG,GACxB;AACJ;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY4D,KAAK,IAAIA,KAAK,CAACX,QAAQ,IAAIW,KAAK,CAACE,cAAc,GAAG,GAAG,GAAG,MAAM;AAC1E;AACA,eAAeF,KAAK,IAAIA,KAAK,CAACX,QAAQ,IAAI,CAACW,KAAK,CAACE,cAAc,GAAG,MAAM,GAAG,MAAM;AACjF;AACA;AACA;AACA,IAAIF,KAAK,IAAI,CAACA,KAAK,CAACC,gBAAgB,IAAI;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACE,GAAA,GArDWJ,wBAAwB;AAuDrC,MAAMK,cAAc,GAAGvE,MAAM,CAACgE,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAXID,cAAc;AAapB,MAAME,QAAQ,GAAGzE,MAAM,CAAC0E,IAAI;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,QAAQ;AAiBd,MAAMG,OAAO,GAAG5E,MAAM,CAACgE,GAAG;AAC1B,gBAAiBG,KAAK,IAAMA,KAAK,CAACU,MAAM,GAAG,SAAS,GAAG,SAAU;AACjE;AACA;AACA;AACA;AACA,wBAAyBV,KAAK,IAAMA,KAAK,CAACU,MAAM,GAAG,MAAM,GAAG,KAAM;AAClE;AACA;AACA,mBAAoBV,KAAK,IAAMA,KAAK,CAACU,MAAM,GAAG,KAAK,GAAG,MAAO;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA5BIF,OAAO;AA8Bb,OAAO,MAAMG,IAAI,GAAG/E,MAAM,CAACgE,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAbWD,IAAI;AAejB,OAAO,MAAME,UAAU,GAAGjF,MAAM,CAACkF,KAAK;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYf,KAAK,IAAIA,KAAK,CAACX,QAAQ,IAAIW,KAAK,CAACE,cAAc,GAAG,OAAO,GAAG,GAAG;AAC3E;AACA;AACA,eAAeF,KAAK,IAAIA,KAAK,CAACX,QAAQ,IAAIW,KAAK,CAACE,cAAc,GAAG,MAAM,GAAG,MAAM;AAChF;AACA,CAAC;AAACc,GAAA,GAtBWF,UAAU;AAwBvB,OAAO,MAAMG,WAAW,GAAGpF,MAAM,CAACgE,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAhBWD,WAAW;AAkBxB,OAAO,MAAME,iBAAiB,GAAGtF,MAAM,CAACuF,EAAE;AAC1C;AACA,CAAC;AAACC,GAAA,GAFWF,iBAAiB;AAI9B,OAAO,MAAMG,UAAU,GAAGzF,MAAM,CAAC0F,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBWF,UAAU;AAqBvB,OAAO,MAAMG,MAAM,GAAG5F,MAAM,CAAC6F,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXWF,MAAM;AAanB,OAAO,MAAMG,YAAY,GAAG/F,MAAM,CAACgE,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GAVWD,YAAY;AAYzB,OAAO,MAAME,WAAW,GAAGjG,MAAM,CAACkG,IAAI;AACtC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GANWF,WAAW;AAQxB,OAAO,MAAMG,gBAAgB,GAAGpG,MAAM,CAACqG,MAAM;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAfWF,gBAAgB;AAiB7B,OAAO,MAAMG,WAAW,GAAGvG,MAAM,CAACgE,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwC,IAAA,GAhBWD,WAAW;AAkBxB,OAAO,MAAME,mBAAmB,GAAGzG,MAAM,CAACgE,GAAG;AAC7C;AACA;AACA,gBAAiBG,KAAK,IAAMA,KAAK,CAACuC,UAAU,GAAG,SAAS,GAAG,OAAQ;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAlBWF,mBAAmB;AAoBhC,OAAO,MAAMG,wBAAwB,GAAG5G,MAAM,CAACgE,GAAG;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6C,IAAA,GAbWD,wBAAwB;AAerC,OAAO,MAAME,kBAAkB,GAAG9G,MAAM,CAACgE,GAAG;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+C,IAAA,GA/BWD,kBAAkB;AAiC/B,OAAO,MAAME,cAAc,GAAGhH,MAAM,CAACgE,GAAG;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiD,IAAA,GArBWD,cAAc;AAuB3B,OAAO,MAAME,kBAAkB,GAAGlH,MAAM,CAACgE,GAAG;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmD,IAAA,GAVWD,kBAAkB;AAY/B,OAAO,MAAME,iBAAiB,GAAGpH,MAAM,CAACgE,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqD,IAAA,GANWD,iBAAiB;AAQ9B,OAAO,MAAME,gBAAgB,GAAGtH,MAAM,CAAC6F,MAAM;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,IAAA,GAZWD,gBAAgB;AAc7B,OAAO,MAAME,SAAS,GAAGxH,MAAM,CAACgE,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyD,IAAA,GAdWD,SAAS;AAgBtB,OAAO,MAAME,OAAO,GAAG1H,MAAM,CAACgE,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2D,IAAA,GAnBWD,OAAO;AAqBpB,MAAME,KAAK,GAAG5H,MAAM,CAACgE,GAAG;AACxB;AACA,kBAAkB,CAAC;EAAE6D;AAAQ,CAAC,KAAMA,OAAO,GAAG,OAAO,GAAG,OAAQ;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,IAAA,GAvBMF,KAAK;AAwBX,MAAMG,mBAAmB,GAAIC,QAAQ,IAAK;EACxC,OAAOA,QAAQ,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;IACvC,MAAMC,IAAI,GAAGpG,MAAM,CAACmG,OAAO,CAACE,SAAS,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;IAC3D,IAAI,CAACJ,GAAG,CAACE,IAAI,CAAC,EAAE;MACdF,GAAG,CAACE,IAAI,CAAC,GAAG,EAAE;IAChB;IACAF,GAAG,CAACE,IAAI,CAAC,CAACG,IAAI,CAACJ,OAAO,CAAC;IACvB,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;;AAED;AACA,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,OAAOC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAACC,OAAO,CAAC,MAAM,EAAE,YAAY;IACjG,OAAOL,IAAI,CAACC,KAAK,CAACD,IAAI,CAACM,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC;EACpD,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;AAClB,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACxBrH,MAAM,CAACsH,MAAM,CAAC,OAAO,CAAC,CAAC,CAAE;EACzB,MAAMC,SAAS,GAAG,uFAAuF;EACzG,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAClD,MAAMC,IAAI,GAAG5H,QAAQ,CAAC6H,GAAG,CAACC,OAAO,CAACL,aAAa,EAAED,SAAS,CAAC,CAACV,QAAQ,CAAC9G,QAAQ,CAAC+H,GAAG,CAACC,IAAI,CAAC;EACvF,MAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACP,IAAI,CAAC;EAClC,MAAMQ,MAAM,GAAGH,SAAS,CAACI,GAAG;EAC5B,MAAMC,OAAO,GAAGZ,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;EAC/C,MAAMY,YAAY,GAAGL,IAAI,CAACC,KAAK,CAACG,OAAO,CAAC;EACxC,MAAME,SAAS,GAAGD,YAAY,CAACF,GAAG;EAClC,MAAMI,KAAK,GAAGf,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAE3C,MAAMe,QAAQ,GAAG3K,WAAW,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM4K,eAAe,GAAG,EAAAvB,eAAA,GAAAsB,QAAQ,CAACE,KAAK,cAAAxB,eAAA,uBAAdA,eAAA,CAAgByB,OAAO,KAAI,IAAI,CAAC,CAAC;;EAEzD,MAAMpH,QAAQ,GAAGF,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,cAAc,EAAEwG,iBAAiB,CAAC,GAAGpL,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqL,UAAU,EAAEC,aAAa,CAAC,GAAGtL,QAAQ,CAAC,KAAK,CAAC;EACnD;EACA,MAAM;IAAEuL,MAAM;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGvL,UAAU,CAACiB,kBAAkB,CAAC;EACtE;EACA,MAAMuK,OAAO,GAAGtL,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM,CAACsI,OAAO,EAAEiD,UAAU,CAAC,GAAG3L,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM;IAAEoI,OAAO;IAAEwD;EAAW,CAAC,GAAG1L,UAAU,CAACgB,cAAc,CAAC;EAAC,CAAC,CAAC;EAC7D,MAAM,CAAC2K,YAAY,EAAEC,eAAe,CAAC,GAAG9L,QAAQ,CAAC,KAAK,CAAC;EACvD;EACA,MAAM,CAAC+L,SAAS,EAAEC,YAAY,CAAC,GAAGhM,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiM,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlM,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmM,OAAO,EAAEC,UAAU,CAAC,GAAGpM,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACqM,aAAa,EAAEC,gBAAgB,CAAC,GAAGtM,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuM,cAAc,EAAEC,iBAAiB,CAAC,GAAGxM,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACyM,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1M,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvE,MAAM,CAAC2M,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5M,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC6M,eAAe,EAAEC,kBAAkB,CAAC,GAAG9M,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/D;EACA,MAAM,CAAC+M,KAAK,EAAEC,QAAQ,CAAC,GAAGhN,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiN,OAAO,EAAEC,UAAU,CAAC,GAAGlN,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmN,KAAK,EAAEC,QAAQ,CAAC,GAAGpN,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqN,YAAY,EAAEC,eAAe,CAAC,GAAGtN,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuN,WAAW,EAAEC,cAAc,CAAC,GAAGxN,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyN,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1N,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM2N,UAAU,GAAGvN,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EACjC,MAAMwN,YAAY,GAAGxN,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EACnC,MAAMyN,oBAAoB,GAAGzN,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;EAC5C;EACA,MAAM0N,cAAc,GAAG1N,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM2N,iBAAiB,GAAG3N,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC4N,UAAU,EAAEC,aAAa,CAAC,GAAGjO,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkO,aAAa,EAAEC,gBAAgB,CAAC,GAAGnO,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoO,aAAa,EAAEC,gBAAgB,CAAC,GAAGrO,QAAQ,CAAC,IAAIsO,GAAG,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxO,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACyO,YAAY,EAAEC,eAAe,CAAC,GAAG1O,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC2O,eAAe,EAAEC,kBAAkB,CAAC,GAAG5O,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6O,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9O,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM+O,oBAAoB,GAAG3O,MAAM,CAAC,IAAI,CAAC;EACzC,MAAM,CAAC4O,eAAe,EAAEC,kBAAkB,CAAC,GAAGjP,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkP,eAAe,EAAEC,kBAAkB,CAAC,GAAGnP,QAAQ,CAAC;IAAEoP,OAAO,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC,CAAC;EAChF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvP,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwP,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzP,QAAQ,CAAC,CAAC,CAAC;EACjE,MAAM,CAAC0P,aAAa,EAAEC,gBAAgB,CAAC,GAAG3P,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM4P,gBAAgB,GAAGxP,MAAM,CAAC,KAAK,CAAC;;EAEtC;EACA,MAAM,CAACyP,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9P,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAAC+P,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhQ,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACiQ,cAAc,EAAEC,iBAAiB,CAAC,GAAGlQ,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMmQ,kBAAkB,GAAG/P,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAACgQ,YAAY,EAAEC,eAAe,CAAC,GAAGrQ,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM;IAAEsQ,MAAM,EAAE3L,gBAAgB;IAAEwI,KAAK,EAAEoD,eAAe;IAAEC,QAAQ,EAAEC;EAAmB,CAAC,GAAGjO,iBAAiB,CAAC1B,cAAc,EAAE,IAAI,CAAC;;EAElI;EACAb,SAAS,CAAC,MAAM;IACd,IAAIsQ,eAAe,EAAE;MACnBG,OAAO,CAACvD,KAAK,CAAC,wBAAwB,EAAEoD,eAAe,CAAC;MACxDG,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;QACpChM,gBAAgB;QAChB8L,kBAAkB,EAAE,GAAGA,kBAAkB,GAAG;QAC5C3P,cAAc,EAAE,CAAAA,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8P,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAG,KAAK;QACxDC,aAAa,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;QACrDC,OAAO,EAAEhN,MAAM,CAAC+G,QAAQ,CAACkG;MAC3B,CAAC,CAAC;;MAEF;MACAR,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE,MAAMQ,OAAO,GAAG,IAAIC,KAAK,CAAC,CAAC;MAC3BD,OAAO,CAACE,MAAM,GAAG,MAAMX,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MAC7EQ,OAAO,CAACG,OAAO,GAAIC,CAAC,IAAKb,OAAO,CAACvD,KAAK,CAAC,iDAAiD,EAAEoE,CAAC,CAAC;MAC5FJ,OAAO,CAACK,GAAG,GAAG1Q,cAAc;IAC9B,CAAC,MAAM,IAAI6D,gBAAgB,IAAI8L,kBAAkB,KAAK,GAAG,EAAE;MACzDC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;MACA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI;EACF,CAAC,EAAE,CAACJ,eAAe,EAAE5L,gBAAgB,EAAE8L,kBAAkB,CAAC,CAAC;;EAE3D;EACA,MAAMgB,sBAAsB,GAAIC,IAAI,IAAK;IACvCpE,eAAe,CAACoE,IAAI,CAAC;IACrB,IAAI3N,QAAQ,EAAE;MACZqH,iBAAiB,CAAC,IAAI,CAAC;IACzB;;IAEA;IACA,IAAIgF,YAAY,CAACsB,IAAI,CAAC/G,GAAG,CAAC,IAAIyF,YAAY,CAACsB,IAAI,CAAC/G,GAAG,CAAC,GAAG,CAAC,EAAE;MACxDgH,cAAc,CAACD,IAAI,CAAC/G,GAAG,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMiH,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI7N,QAAQ,EAAE;MACZqH,iBAAiB,CAAC,KAAK,CAAC;MACxBkC,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMuE,iBAAiB,GAAIN,CAAC,IAAK;IAC/BA,CAAC,CAACO,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB3D,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACAlO,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8D,QAAQ,EAAE;MACbqH,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE,CAACrH,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMgO,cAAc,GAAG,CAAC,GAAGtE,mBAAmB,CAAC,CAACuE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACxD,IAAIhJ,IAAI,CAAC+I,CAAC,CAACrJ,SAAS,CAAC,GAAG,IAAIM,IAAI,CAACgJ,CAAC,CAACtJ,SAAS,CAC9C,CAAC;EAED,MAAMuJ,eAAe,GAAG7J,mBAAmB,CAACyJ,cAAc,CAAC;EAC3D,MAAMK,aAAa,GAAGhS,MAAM,CAAC,IAAI,CAAC;EAElCH,SAAS,CAAC,MAAM;IACd;IACA,IAAImS,aAAa,CAAChD,OAAO,IAAI,CAACP,mBAAmB,IAAIkB,oBAAoB,EAAE;MACzE;MACA,MAAMsC,SAAS,GAAGtD,oBAAoB,CAACK,OAAO;MAC9C,IAAIiD,SAAS,IAAI5E,mBAAmB,CAAC6E,MAAM,GAAG,CAAC,EAAE;QAC/C;QACAD,SAAS,CAACE,SAAS,GAAGF,SAAS,CAACG,YAAY;;QAE5C;QACAC,UAAU,CAAC,MAAM;UACf,IAAIL,aAAa,CAAChD,OAAO,IAAIW,oBAAoB,EAAE;YACjDqC,aAAa,CAAChD,OAAO,CAACsD,cAAc,CAAC;cAAEC,QAAQ,EAAE;YAAU,CAAC,CAAC;UAC/D;QACF,CAAC,EAAE,EAAE,CAAC;MACR;IACF;EACF,CAAC,EAAE,CAAClF,mBAAmB,EAAEoB,mBAAmB,EAAEkB,oBAAoB,CAAC,CAAC;EAEpE,MAAM6C,kBAAkB,GAAGC,KAAK,IAAI;IAClC,IAAInH,OAAO,CAAC0D,OAAO,IAAI,CAAC1D,OAAO,CAAC0D,OAAO,CAAC0D,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,EAC5DzH,aAAa,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAM0H,YAAY,GAAG,MAAOH,KAAK,IAAK;IACpCA,KAAK,CAACI,cAAc,CAAC,CAAC;IACtB,IAAI,CAACvK,OAAO,CAACwK,IAAI,CAAC,CAAC,IAAIrH,YAAY,EAAE;IAErC,IAAI,CAACwB,YAAY,IAAI,CAACA,YAAY,CAAC8F,aAAa,EAAE;MAChDzC,OAAO,CAACvD,KAAK,CAAC,4CAA4C,CAAC;MAC3D;IACF;IAIA,MAAMiG,aAAa,GAAG1K,OAAO,CAACwK,IAAI,CAAC,CAAC;;IAEpC;IACA,MAAMG,iBAAiB,GAAG;MACxB1I,GAAG,EAAE,QAAQzB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACzBmK,IAAI,EAAEF,aAAa;MACnBhO,MAAM,EAAE,IAAI;MACZwD,SAAS,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC,CAAC;MACnCC,WAAW,EAAE,IAAItK,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC,CAAC;MACrCE,WAAW,EAAEpG,YAAY,CAAC1C,GAAG;MAC7B+I,YAAY,EAAE,IAAI;MAAE;MACpBC,MAAM,EAAE;IACV,CAAC;;IAED;IACAjG,sBAAsB,CAACkG,WAAW,IAAI,CAACP,iBAAiB,EAAE,GAAGO,WAAW,CAAC,CAAC;;IAE1E;IACApF,kBAAkB,CAAC,KAAK,CAAC;;IAEzB;IACAiE,UAAU,CAAC,MAAM;MACf,MAAMJ,SAAS,GAAGtD,oBAAoB,CAACK,OAAO;MAC9C,IAAIiD,SAAS,EAAE;QACbA,SAAS,CAACE,SAAS,GAAGF,SAAS,CAACG,YAAY;MAC9C;IACF,CAAC,EAAE,EAAE,CAAC;;IAEN;IACAnE,gBAAgB,CAACwF,SAAS,IAAI;MAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;MACnC,MAAME,cAAc,GAAGD,QAAQ,CAACE,GAAG,CAAC3G,YAAY,CAAC1C,GAAG,CAAC,IAAI,EAAE;MAC3DmJ,QAAQ,CAACG,GAAG,CAAC5G,YAAY,CAAC1C,GAAG,EAAE,CAAC0I,iBAAiB,EAAE,GAAGU,cAAc,CAAC,CAAC;MACtE,OAAOD,QAAQ;IACjB,CAAC,CAAC;;IAEF;IACA9G,QAAQ,CAACkH,QAAQ,IAAI;MACnB,MAAMC,YAAY,GAAGD,QAAQ,CAACE,GAAG,CAAC1C,IAAI,IAAI;QACxC,IAAIA,IAAI,CAAC/G,GAAG,KAAK0C,YAAY,CAAC1C,GAAG,EAAE;UACjC,OAAO;YACL,GAAG+G,IAAI;YACPhJ,OAAO,EAAE;cACP4K,IAAI,EAAEF,aAAa;cACnB1K,OAAO,EAAE0K,aAAa;cACtBI,WAAW,EAAE,IAAItK,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC,CAAC;cACrC3K,SAAS,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC,CAAC;cACnCnO,MAAM,EAAE;YACV,CAAC;YACDiP,SAAS,EAAE,IAAInL,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC;UACpC,CAAC;QACH;QACA,OAAO7B,IAAI;MACb,CAAC,CAAC;;MAEF;MACA,MAAM4C,SAAS,GAAGH,YAAY,CAACI,SAAS,CAAC7C,IAAI,IAAIA,IAAI,CAAC/G,GAAG,KAAK0C,YAAY,CAAC1C,GAAG,CAAC;MAC/E,IAAI2J,SAAS,GAAG,CAAC,EAAE;QACjB,MAAM,CAACE,UAAU,CAAC,GAAGL,YAAY,CAACM,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;QACtDH,YAAY,CAACO,OAAO,CAACF,UAAU,CAAC;MAClC;MAEA,OAAOL,YAAY;IACrB,CAAC,CAAC;IAEF,IAAI;MACFrI,eAAe,CAAC,IAAI,CAAC;MACrBH,UAAU,CAAC,EAAE,CAAC,CAAC,CAAE;;MAEjB,MAAMgJ,QAAQ,GAAG,MAAMlT,uBAAuB,CAACqJ,SAAS,EAAEuC,YAAY,CAAC8F,aAAa,EAAEC,aAAa,CAAC;;MAEpG;MACA1F,sBAAsB,CAACkG,WAAW,IAChCA,WAAW,CAACQ,GAAG,CAACQ,GAAG,IACjBA,GAAG,CAACjK,GAAG,KAAK0I,iBAAiB,CAAC1I,GAAG,GAC7B;QAAE,GAAGiK,GAAG;QAAEjB,MAAM,EAAE;MAAO,CAAC,GAC1BiB,GACN,CACF,CAAC;;MAED;IAEF,CAAC,CAAC,OAAOzH,KAAK,EAAE;MAAA,IAAA0H,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdtE,OAAO,CAACvD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDuD,OAAO,CAACvD,KAAK,CAAC,qBAAqB,GAAA0H,eAAA,GAAE1H,KAAK,CAACwH,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBI,IAAI,CAAC;MAC1DvE,OAAO,CAACvD,KAAK,CAAC,WAAW,GAAA2H,gBAAA,GAAE3H,KAAK,CAACwH,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBnB,MAAM,CAAC;;MAElD;MACAjG,sBAAsB,CAACkG,WAAW,IAChCA,WAAW,CAACsB,MAAM,CAACN,GAAG,IAAIA,GAAG,CAACjK,GAAG,KAAK0I,iBAAiB,CAAC1I,GAAG,CAC7D,CAAC;;MAED;MACA0D,gBAAgB,CAACwF,SAAS,IAAI;QAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;QACnC,MAAME,cAAc,GAAGD,QAAQ,CAACE,GAAG,CAAC3G,YAAY,CAAC1C,GAAG,CAAC,IAAI,EAAE;QAC3DmJ,QAAQ,CAACG,GAAG,CAAC5G,YAAY,CAAC1C,GAAG,EAAEoJ,cAAc,CAACmB,MAAM,CAACN,GAAG,IAAIA,GAAG,CAACjK,GAAG,KAAK0I,iBAAiB,CAAC1I,GAAG,CAAC,CAAC;QAC/F,OAAOmJ,QAAQ;MACjB,CAAC,CAAC;;MAEF;MACAnI,UAAU,CAACyH,aAAa,CAAC;;MAEzB;MACA,MAAM+B,YAAY,GAAG,EAAAJ,gBAAA,GAAA5H,KAAK,CAACwH,QAAQ,cAAAI,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBE,IAAI,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsB7H,KAAK,KAAI,2CAA2C;MAC/F/J,KAAK,CAAC+J,KAAK,CAACgI,YAAY,CAAC;IAC3B,CAAC,SAAS;MACRrJ,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM+E,aAAa,GAAG5M,MAAM,CAAC+G,QAAQ,CAACoK,QAAQ,KAAK,WAAW;EAC9D,MAAMC,MAAM,GAAGxE,aAAa,GACxBC,OAAO,CAACC,GAAG,CAACuE,wBAAwB,GACpCxE,OAAO,CAACC,GAAG,CAACwE,yBAAyB;;EAEzC;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzV,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM2R,cAAc,GAAGxR,WAAW,CAAC,MAAOuV,MAAM,IAAK;IACnD,IAAI;MACFhF,OAAO,CAACC,GAAG,CAAC,+CAA+C+E,MAAM,EAAE,CAAC;MACpE,MAAMf,QAAQ,GAAG,MAAM3S,0BAA0B,CAAC8I,SAAS,EAAE4K,MAAM,EAAE3K,KAAK,CAAC;MAC3E,IAAI4J,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACtB,MAAM,KAAK,GAAG,EAAE;QACjDjD,OAAO,CAACC,GAAG,CAAC,0DAA0D+E,MAAM,EAAE,CAAC;;QAE/E;QACArF,eAAe,CAACsF,UAAU,IAAI;UAC5B,MAAMC,SAAS,GAAG;YAAE,GAAGD;UAAW,CAAC;UACnC,OAAOC,SAAS,CAACF,MAAM,CAAC,CAAC,CAAC;UAC1B,OAAOE,SAAS;QAClB,CAAC,CAAC;;QAEF;QACA,IAAI;UACF,MAAMjB,QAAQ,GAAG,MAAM5S,sBAAsB,CAAC+I,SAAS,EAAEC,KAAK,CAAC;UAC/D,IAAI4J,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACtB,MAAM,KAAK,GAAG,EAAE;YACjDtD,eAAe,CAACsE,QAAQ,CAACM,IAAI,CAAC7E,YAAY,CAAC;UAC7C;QACF,CAAC,CAAC,OAAOjD,KAAK,EAAE;UACduD,OAAO,CAACvD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QAC3E;MACF,CAAC,MAAM;QACLuD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEgE,QAAQ,CAACM,IAAI,CAAC;MAC5E;IACF,CAAC,CAAC,OAAO9H,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,mDAAmDuI,MAAM,GAAG,EAAEvI,KAAK,CAAC;IACpF;EACF,CAAC,EAAE,CAACrC,SAAS,EAAEC,KAAK,CAAC,CAAC;;EAEtB;EACA,MAAM8K,gBAAgB,GAAGA,CAACC,UAAU,GAAG,EAAE,KAAK;IAC5C;IACA,IAAIhI,cAAc,CAACsB,OAAO,EAAE;MAC1B2G,YAAY,CAACjI,cAAc,CAACsB,OAAO,CAAC;IACtC;IACA,IAAIrB,iBAAiB,CAACqB,OAAO,EAAE;MAC7B4G,aAAa,CAACjI,iBAAiB,CAACqB,OAAO,CAAC;IAC1C;;IAEA;IACA9C,gBAAgB,CAAC,KAAK,CAAC;IACvBM,uBAAuB,CAAC,KAAK,CAAC;IAC9BJ,iBAAiB,CAACsJ,UAAU,CAAC,CAAC,CAAC;;IAE/BpF,OAAO,CAACC,GAAG,CAAC,kCAAkCmF,UAAU,WAAW,CAAC;;IAEpE;IACA/H,iBAAiB,CAACqB,OAAO,GAAG6G,WAAW,CAAC,MAAM;MAC5CzJ,iBAAiB,CAAC0J,IAAI,IAAI;QACxB,IAAIA,IAAI,IAAI,CAAC,EAAE;UACb;UACA5J,gBAAgB,CAAC,IAAI,CAAC;UACtBM,uBAAuB,CAAC,IAAI,CAAC;UAC7BoJ,aAAa,CAACjI,iBAAiB,CAACqB,OAAO,CAAC;UAExCsB,OAAO,CAACC,GAAG,CAAC,0BAA0BmF,UAAU,WAAW,CAAC;;UAE5D;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA,OAAO,CAAC;QACV;QACA,OAAOI,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;;IAER;IACApI,cAAc,CAACsB,OAAO,GAAGqD,UAAU,CAAC,MAAM;MACxCnG,gBAAgB,CAAC,IAAI,CAAC;MACtBM,uBAAuB,CAAC,IAAI,CAAC;MAC7BoJ,aAAa,CAACjI,iBAAiB,CAACqB,OAAO,CAAC;IAC1C,CAAC,EAAE0G,UAAU,GAAG,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMK,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzCzF,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAChEjE,sBAAsB,CAAC,IAAI,CAAC;IAC5B,MAAM0J,WAAW,CAAC,qBAAqB,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAG,MAAAA,CAAOC,MAAM,GAAG,QAAQ,KAAK;IAC1D5F,OAAO,CAACC,GAAG,CAAC,wCAAwC2F,MAAM,MAAM,CAAC;;IAEjE;IACA,IAAI,CAAC1I,YAAY,CAACwB,OAAO,EAAE;MACzBsB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D;IACF;;IAEA;IACA,IAAI7C,cAAc,CAACsB,OAAO,EAAE;MAC1B2G,YAAY,CAACjI,cAAc,CAACsB,OAAO,CAAC;IACtC;IACA,IAAIrB,iBAAiB,CAACqB,OAAO,EAAE;MAC7B4G,aAAa,CAACjI,iBAAiB,CAACqB,OAAO,CAAC;IAC1C;IAEA9C,gBAAgB,CAAC,KAAK,CAAC;IACvBM,uBAAuB,CAAC,KAAK,CAAC;IAC9BV,mBAAmB,CAAC,IAAI,CAAC;IAEzB,IAAI;MACF;MACA,MAAMyI,QAAQ,GAAG,MAAMtT,mBAAmB,CAACyJ,SAAS,EAAEC,KAAK,CAAC;MAC5D,MAAMkK,IAAI,GAAGN,QAAQ,CAACM,IAAI;MAE1B,IAAI,CAACrH,YAAY,CAACwB,OAAO,EAAE;MAE3B,IAAI6F,IAAI,CAACtB,MAAM,KAAK,GAAG,EAAE;QACvB,IAAIsB,IAAI,CAACsB,WAAW,EAAE;UACpB;UACA7F,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5CvE,UAAU,CAAC,IAAI,CAAC;UAChBF,mBAAmB,CAAC,KAAK,CAAC;UAC1BF,YAAY,CAAC,EAAE,CAAC;UAChBwK,iBAAiB,CAAC,CAAC;UACnB;QACF;QAEA,IAAIvB,IAAI,CAACwB,MAAM,IAAIxB,IAAI,CAACwB,MAAM,CAACC,EAAE,EAAE;UACjC;UACAhG,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEsE,IAAI,CAACwB,MAAM,CAAC;UACvDzK,YAAY,CAACiJ,IAAI,CAACwB,MAAM,CAACC,EAAE,CAAC;UAC5BxK,mBAAmB,CAAC,KAAK,CAAC;;UAE1B;UACA,MAAMyK,GAAG,GAAG1B,IAAI,CAACwB,MAAM,CAACE,GAAG,GACzB3N,IAAI,CAAC4N,GAAG,CAAC,CAAC,EAAE3B,IAAI,CAACwB,MAAM,CAACE,GAAG,GAAG3N,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE;UAEnE0M,gBAAgB,CAACc,GAAG,CAAC;UACrB;QACF;MACF;;MAEA;MACAjG,OAAO,CAACmG,IAAI,CAAC,6CAA6C,EAAE5B,IAAI,CAAC;MACjE,MAAM,IAAI6B,KAAK,CAAC7B,IAAI,CAAC9H,KAAK,IAAI,gCAAgC,CAAC;IAEjE,CAAC,CAAC,OAAOA,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,wCAAwCmJ,MAAM,IAAI,EAAEnJ,KAAK,CAAC;MAExE,IAAI,CAACS,YAAY,CAACwB,OAAO,EAAE;MAE3BlD,mBAAmB,CAAC,KAAK,CAAC;MAC1BU,uBAAuB,CAAC,IAAI,CAAC;;MAE7B;MACA8D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD8B,UAAU,CAAC,MAAM;QACf,IAAI,CAACtG,OAAO,IAAIyB,YAAY,CAACwB,OAAO,EAAE;UACpC2H,wBAAwB,CAACT,MAAM,CAAC;QAClC;MACF,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;;EAED;EACA,MAAMS,wBAAwB,GAAG,MAAAA,CAAOT,MAAM,GAAG,QAAQ,KAAK;IAC5D5F,OAAO,CAACC,GAAG,CAAC,wCAAwC2F,MAAM,MAAM,CAAC;;IAEjE;IACA,IAAI,CAAC1I,YAAY,CAACwB,OAAO,EAAE;MACzBsB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D;IACF;IAEArE,gBAAgB,CAAC,KAAK,CAAC;IACvBM,uBAAuB,CAAC,KAAK,CAAC;IAC9BV,mBAAmB,CAAC,IAAI,CAAC;IAEzB,IAAI;MACF,MAAMkK,WAAW,CAAC,YAAYE,MAAM,EAAE,CAAC;IACzC,CAAC,CAAC,OAAOnJ,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,6CAA6CmJ,MAAM,IAAI,EAAEnJ,KAAK,CAAC;MAC7EjB,mBAAmB,CAAC,KAAK,CAAC;MAC1BU,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM4J,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI1I,cAAc,CAACsB,OAAO,EAAE;MAC1B2G,YAAY,CAACjI,cAAc,CAACsB,OAAO,CAAC;MACpCtB,cAAc,CAACsB,OAAO,GAAG,IAAI;IAC/B;IACA,IAAIrB,iBAAiB,CAACqB,OAAO,EAAE;MAC7B4G,aAAa,CAACjI,iBAAiB,CAACqB,OAAO,CAAC;MACxCrB,iBAAiB,CAACqB,OAAO,GAAG,IAAI;IAClC;EACF,CAAC;;EAED;EACA,MAAM4H,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI/S,MAAM,CAACgT,cAAc,EAAE;MACzBvG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD1M,MAAM,CAACgT,cAAc,CAACC,kBAAkB,CAAC,CAAC;MAC1CjT,MAAM,CAACgT,cAAc,CAACE,UAAU,CAAC,CAAC;MAClClT,MAAM,CAACgT,cAAc,GAAG,IAAI;IAC9B;EACF,CAAC;;EAED;EACAhX,SAAS,CAAC,MAAM;IACd,MAAMmX,eAAe,GAAG,WAAW;IACnC,MAAMC,KAAK,GAAGhC,MAAM,CAAChM,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG+N,eAAe;;IAEzD;IACA,IAAInT,MAAM,CAACgT,cAAc,IAAIhT,MAAM,CAACgT,cAAc,CAACK,SAAS,EAAE;MAC5D5G,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD8E,aAAa,CAACxR,MAAM,CAACgT,cAAc,CAAC;MACpC;IACF;IAEAvG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,MAAM4G,MAAM,GAAG7W,EAAE,CAAC2W,KAAK,EAAE;MACvBG,eAAe,EAAE,IAAI;MACrBC,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,IAAI,EAAE;QAAE3M,KAAK,EAAEf,YAAY,CAACC,OAAO,CAAC,OAAO;MAAE,CAAC;MAC9C0N,YAAY,EAAE,IAAI;MAClBC,oBAAoB,EAAE,EAAE;MACxBC,iBAAiB,EAAE;IACrB,CAAC,CAAC;;IAEF;IACA5T,MAAM,CAACgT,cAAc,GAAGM,MAAM;IAC9B9B,aAAa,CAAC8B,MAAM,CAAC;IAErB,OAAO,MAAM;MACX7G,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;MAChF;MACA;MACA8E,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxV,SAAS,CAAC,MAAM;IACd,IAAI,CAACuV,UAAU,EAAE;;IAEjB;IACAA,UAAU,CAACsC,EAAE,CAAC,SAAS,EAAE,MAAM;MAC7BpH,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C;MACA,MAAMoH,QAAQ,GAAG,SAASjN,SAAS,EAAE;MACrC4F,OAAO,CAACC,GAAG,CAAC,sCAAsCoH,QAAQ,EAAE,CAAC;IAC/D,CAAC,CAAC;IAEFvC,UAAU,CAACsC,EAAE,CAAC,YAAY,EAAGE,MAAM,IAAK;MACtCtH,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEqH,MAAM,CAAC;IACxD,CAAC,CAAC;IAEFxC,UAAU,CAACsC,EAAE,CAAC,eAAe,EAAG3K,KAAK,IAAK;MACxCuD,OAAO,CAACvD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D,CAAC,CAAC;IAEFqI,UAAU,CAACsC,EAAE,CAAC,OAAO,EAAGG,OAAO,IAAK;MAClCvH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEsH,OAAO,CAACC,IAAI,CAAC;MACnDlL,QAAQ,CAACkH,QAAQ,IAAI;QACnB;QACA,MAAMiE,aAAa,GAAGjE,QAAQ,CAACgB,MAAM,CAACxD,IAAI,IAAIA,IAAI,CAAC/G,GAAG,KAAKsN,OAAO,CAACtN,GAAG,CAAC;QACvE,OAAO,CAACsN,OAAO,EAAE,GAAGE,aAAa,CAAC;MACpC,CAAC,CAAC;;MAEF;MACA,IAAIF,OAAO,CAACvP,OAAO,IAAI,CAACuP,OAAO,CAACvP,OAAO,CAACtD,MAAM,EAAE;QAC9CiJ,gBAAgB,CAACwF,SAAS,IAAI;UAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;UACnC,MAAME,cAAc,GAAGD,QAAQ,CAACE,GAAG,CAACiE,OAAO,CAACtN,GAAG,CAAC,IAAI,EAAE;;UAEtD;UACA,MAAMyN,eAAe,GAAG;YACtBzN,GAAG,EAAEsN,OAAO,CAACvP,OAAO,CAAC2P,EAAE,IAAI,OAAOnP,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;YAC9CmK,IAAI,EAAE2E,OAAO,CAACvP,OAAO,CAAC4K,IAAI,IAAI2E,OAAO,CAACvP,OAAO,CAACA,OAAO;YACrDtD,MAAM,EAAE6S,OAAO,CAACvP,OAAO,CAACtD,MAAM;YAC9BwD,SAAS,EAAEqP,OAAO,CAACvP,OAAO,CAACE,SAAS,IAAIqP,OAAO,CAACvP,OAAO,CAAC8K,WAAW;YACnEA,WAAW,EAAEyE,OAAO,CAACvP,OAAO,CAAC8K,WAAW,IAAIyE,OAAO,CAACvP,OAAO,CAACE,SAAS;YACrE6K,WAAW,EAAEwE,OAAO,CAACtN;UACvB,CAAC;;UAED;UACA,MAAM2N,aAAa,GAAGvE,cAAc,CAACwE,IAAI,CAAC3D,GAAG,IAC3CA,GAAG,CAACtB,IAAI,KAAK8E,eAAe,CAAC9E,IAAI,IACjCsB,GAAG,CAACxP,MAAM,KAAKgT,eAAe,CAAChT,MAAM,IACrC4D,IAAI,CAACwP,GAAG,CAAC,IAAItP,IAAI,CAAC0L,GAAG,CAAChM,SAAS,CAAC,GAAG,IAAIM,IAAI,CAACkP,eAAe,CAACxP,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC;UACjF,CAAC;UAED,IAAI,CAAC0P,aAAa,EAAE;YAClBxE,QAAQ,CAACG,GAAG,CAACgE,OAAO,CAACtN,GAAG,EAAE,CAACyN,eAAe,EAAE,GAAGrE,cAAc,CAAC,CAAC;UACjE;UAEA,OAAOD,QAAQ;QACjB,CAAC,CAAC;;QAEF;QACA,IAAI,CAACzG,YAAY,IAAIA,YAAY,CAAC1C,GAAG,KAAKsN,OAAO,CAACtN,GAAG,EAAE;UACrD;UACA0F,eAAe,CAACsF,UAAU,KAAK;YAC7B,GAAGA,UAAU;YACb,CAACsC,OAAO,CAACtN,GAAG,GAAG,CAACgL,UAAU,CAACsC,OAAO,CAACtN,GAAG,CAAC,IAAI,CAAC,IAAI;UAClD,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA+F,OAAO,CAACC,GAAG,CAAC,WAAWsH,OAAO,CAACC,IAAI,gEAAgE,CAAC;UACpGzF,UAAU,CAAC,MAAM;YACfd,cAAc,CAACsG,OAAO,CAACtN,GAAG,CAAC;UAC7B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QACZ;MACF;MAEA+F,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEsH,OAAO,CAACC,IAAI,CAAC;IAChF,CAAC,CAAC;;IAEF;IACA1C,UAAU,CAACsC,EAAE,CAAC,wBAAwB,EAAG7C,IAAI,IAAK;MAChDvE,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;QACxD8H,UAAU,EAAExD,IAAI,CAACwD,UAAU;QAC3B3N,SAAS,EAAEmK,IAAI,CAACnK,SAAS;QACzB4N,WAAW,EAAEzD,IAAI,CAACyD,WAAW;QAC7BC,SAAS,EAAE,CAAC,CAAC1D,IAAI,CAACwB,MAAM;QACxBmC,OAAO,EAAE3D,IAAI,CAAC2D,OAAO;QACrBC,SAAS,EAAE5D,IAAI,CAAC4D;MAClB,CAAC,CAAC;MAEFnI,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvC5E,SAAS,EAAE,CAAC,CAACA,SAAS;QACtBM,aAAa;QACbJ,gBAAgB;QAChBE,OAAO;QACP2M,YAAY,EAAEhO;MAChB,CAAC,CAAC;;MAEF;MACA,IAAImK,IAAI,CAACnK,SAAS,IAAImK,IAAI,CAACnK,SAAS,KAAKA,SAAS,EAAE;QAClD4F,OAAO,CAACC,GAAG,CAAC,+DAA+DsE,IAAI,CAACyD,WAAW,KAAKzD,IAAI,CAACnK,SAAS,aAAaA,SAAS,EAAE,CAAC;QACvI;MACF;;MAEA;MACA,IAAIqB,OAAO,EAAE;QACXuE,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;QAC1E;MACF;;MAEA;MACA,IAAI,CAAClE,mBAAmB,EAAE;QACxBiE,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;QAC5E;MACF;MAEA,IAAIsE,IAAI,CAACwB,MAAM,IAAIxB,IAAI,CAACwD,UAAU,EAAE;QAClC;QACA,MAAMM,MAAM,GAAG9D,IAAI,CAACwB,MAAM;QAC1B,IAAIuC,WAAW,GAAG,IAAI;QAEtB,IAAI,OAAOD,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACE,UAAU,CAAC,aAAa,CAAC,EAAE;UAClED,WAAW,GAAGD,MAAM;QACtB,CAAC,MAAM,IAAIA,MAAM,CAACrC,EAAE,IAAI,OAAOqC,MAAM,CAACrC,EAAE,KAAK,QAAQ,IAAIqC,MAAM,CAACrC,EAAE,CAACuC,UAAU,CAAC,aAAa,CAAC,EAAE;UAC5FD,WAAW,GAAGD,MAAM,CAACrC,EAAE;QACzB,CAAC,MAAM,IAAIqC,MAAM,CAACG,MAAM,IAAI,OAAOH,MAAM,CAACG,MAAM,KAAK,QAAQ,IAAIH,MAAM,CAACG,MAAM,CAACD,UAAU,CAAC,aAAa,CAAC,EAAE;UACxGD,WAAW,GAAGD,MAAM,CAACG,MAAM;QAC7B;QAEA,IAAIF,WAAW,EAAE;UACftI,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D3E,YAAY,CAACgN,WAAW,CAAC;UACzB9M,mBAAmB,CAAC,KAAK,CAAC;;UAE1B;UACA,IAAIyK,GAAG,GAAG,EAAE;UACZ,IAAIoC,MAAM,CAACpC,GAAG,EAAE;YACdA,GAAG,GAAG3N,IAAI,CAAC4N,GAAG,CAAC,CAAC,EAAEmC,MAAM,CAACpC,GAAG,GAAG3N,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;UAC/D;;UAEA;UACA0M,gBAAgB,CAACc,GAAG,CAAC;;UAErB;UACA,IAAI1B,IAAI,CAAC2D,OAAO,EAAE;YAChBlI,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;UAC/D;QACF,CAAC,MAAM;UACLD,OAAO,CAACvD,KAAK,CAAC,kDAAkD,EAAE3C,IAAI,CAAC2O,SAAS,CAACJ,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACpG;MACF;IACF,CAAC,CAAC;;IAEF;IACAvD,UAAU,CAACsC,EAAE,CAAC,4BAA4B,EAAG7C,IAAI,IAAK;MACpDvE,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;QACvD8H,UAAU,EAAExD,IAAI,CAACwD,UAAU;QAC3B3N,SAAS,EAAEmK,IAAI,CAACnK,SAAS;QACzBI,KAAK,EAAE+J,IAAI,CAAC/J,KAAK;QACjBqL,WAAW,EAAEtB,IAAI,CAACsB,WAAW;QAC7B6C,UAAU,EAAEnE,IAAI,CAACmE,UAAU;QAC3BP,SAAS,EAAE5D,IAAI,CAAC4D;MAClB,CAAC,CAAC;;MAEF;MACA,IAAI5D,IAAI,CAACnK,SAAS,IAAImK,IAAI,CAACnK,SAAS,KAAKA,SAAS,EAAE;QAClD4F,OAAO,CAACC,GAAG,CAAC,yEAAyEsE,IAAI,CAACnK,SAAS,YAAYA,SAAS,EAAE,CAAC;QAC3H;MACF;MAEA,IAAImK,IAAI,CAACsB,WAAW,IAAItB,IAAI,CAAC/J,KAAK,KAAK,MAAM,EAAE;QAC7CwF,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnEvE,UAAU,CAAC,IAAI,CAAC;QAChBF,mBAAmB,CAAC,KAAK,CAAC;QAC1BF,YAAY,CAAC,EAAE,CAAC;QAChBwK,iBAAiB,CAAC,CAAC;MACrB;IACF,CAAC,CAAC;;IAEF;IACAhB,UAAU,CAACsC,EAAE,CAAC,oBAAoB,EAAG7C,IAAI,IAAK;MAC5CvE,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE;QACzD8H,UAAU,EAAExD,IAAI,CAACwD,UAAU;QAC3B3N,SAAS,EAAEmK,IAAI,CAACnK,SAAS;QACzBpC,OAAO,EAAEuM,IAAI,CAACvM,OAAO;QACrBmQ,SAAS,EAAE5D,IAAI,CAAC4D;MAClB,CAAC,CAAC;;MAEF;MACA,IAAI5D,IAAI,CAACnK,SAAS,IAAImK,IAAI,CAACnK,SAAS,KAAKA,SAAS,EAAE;QAClD4F,OAAO,CAACC,GAAG,CAAC,+DAA+DsE,IAAI,CAACnK,SAAS,YAAYA,SAAS,EAAE,CAAC;QACjH;MACF;;MAEA;MACAsB,UAAU,CAAC,IAAI,CAAC;MAChBF,mBAAmB,CAAC,KAAK,CAAC;MAC1BF,YAAY,CAAC,EAAE,CAAC;MAChBwK,iBAAiB,CAAC,CAAC;;MAEnB;MACA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;MAEM;MACA/D,UAAU,CAAC,MAAM;QACf/B,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;QACzE,IAAI,CAACjB,aAAa,IAAI3C,KAAK,CAACuF,MAAM,GAAG,CAAC,EAAE;UACtC5B,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtD3D,QAAQ,CAAC,EAAE,CAAC;UACZqM,OAAO,CAAC,CAAC,CAAC;UACVC,eAAe,CAAC,IAAI,CAAC;UACrBC,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,qBAAqB,CAAC;QAC7C,CAAC,MAAM;UACL7I,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;QAC7E;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;IAEF,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE;MACA,IAAI6E,UAAU,EAAE;QACdA,UAAU,CAACgE,GAAG,CAAC,OAAO,CAAC;QACvBhE,UAAU,CAACgE,GAAG,CAAC,SAAS,CAAC;QACzBhE,UAAU,CAACgE,GAAG,CAAC,YAAY,CAAC;QAC5BhE,UAAU,CAACgE,GAAG,CAAC,eAAe,CAAC;QAC/BhE,UAAU,CAACgE,GAAG,CAAC,WAAW,CAAC;QAC3BhE,UAAU,CAACgE,GAAG,CAAC,wBAAwB,CAAC;QACxChE,UAAU,CAACgE,GAAG,CAAC,4BAA4B,CAAC;QAC5ChE,UAAU,CAACgE,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC;;QAEtC9I,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC;MAC/F;IACF,CAAC;EACH,CAAC,EAAE,CAAC6E,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElBvV,SAAS,CAAC,MAAM;IACdwZ,QAAQ,CAACrV,gBAAgB,CAAC,OAAO,EAAEwO,kBAAkB,CAAC;IACtD,OAAO,MAAM6G,QAAQ,CAACpV,mBAAmB,CAAC,OAAO,EAAEuO,kBAAkB,CAAC;EACxE,CAAC,CAAC;EAEF3S,SAAS,CAAC,MAAM;IACd;IACA2N,YAAY,CAACwB,OAAO,GAAG,IAAI;IAC3BvB,oBAAoB,CAACuB,OAAO,GAAG,KAAK,CAAC,CAAC;;IAEtC;IACAsB,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;;IAE1E;IACA,MAAM+I,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF;QACA,MAAMnD,WAAW,GAAG,MAAMoD,qBAAqB,CAAC,iBAAiB,CAAC;QAElE,IAAI,CAACpD,WAAW,EAAE;UAChB;UACA7F,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;UAC3F;QACF,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QAC3D;MACF,CAAC,CAAC,OAAOxD,KAAK,EAAE;QACduD,OAAO,CAACvD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D;QACAuD,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;QAC9E;MACF;IACF,CAAC;IAED+I,kBAAkB,CAAC,CAAC;;IAEpB;IACA,OAAO,MAAM;MACX9L,YAAY,CAACwB,OAAO,GAAG,KAAK,CAAC,CAAC;MAC9BvB,oBAAoB,CAACuB,OAAO,GAAG,IAAI,CAAC,CAAC;MACrC,IAAIzB,UAAU,CAACyB,OAAO,EAAE;QACtB2G,YAAY,CAACpI,UAAU,CAACyB,OAAO,CAAC;QAChCsB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC9C;MACA;MACA,IAAIR,kBAAkB,CAACf,OAAO,EAAE;QAC9B2G,YAAY,CAAC5F,kBAAkB,CAACf,OAAO,CAAC;QACxCsB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD;MACA;MACA6F,iBAAiB,CAAC,CAAC;MACnB9F,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgJ,qBAAqB,GAAG,MAAAA,CAAOrD,MAAM,GAAG,SAAS,KAAK;IAC1D,IAAI,CAAC1I,YAAY,CAACwB,OAAO,EAAE,OAAO,KAAK;IAEvC,IAAI;MACFsB,OAAO,CAACC,GAAG,CAAC,yEAAyE2F,MAAM,GAAG,CAAC;MAC/F,MAAM3B,QAAQ,GAAG,MAAM1S,2BAA2B,CAAC6I,SAAS,CAAC;MAE7D4F,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEgE,QAAQ,CAACM,IAAI,CAAC;MAE1E,MAAMsB,WAAW,GAAG5B,QAAQ,CAACM,IAAI,CAACsB,WAAW;MAC7C,MAAMqD,eAAe,GAAGjF,QAAQ,CAACM,IAAI,CAAC2E,eAAe;;MAErD;MACA,IAAIrD,WAAW,IAAIqD,eAAe,KAAK,MAAM,EAAE;QAC7ClJ,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;QAC9EvE,UAAU,CAAC,IAAI,CAAC;QAChBF,mBAAmB,CAAC,KAAK,CAAC;QAC1BF,YAAY,CAAC,EAAE,CAAC;QAChBwK,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC,CAAC;MACf,CAAC,MAAM;QACL9F,OAAO,CAACC,GAAG,CAAC,uEAAuEiJ,eAAe,GAAG,CAAC;QACtGxN,UAAU,CAAC,KAAK,CAAC;QACjB,OAAO,KAAK,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,6DAA6D,EAAEA,KAAK,CAAC;MACnFuD,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtEvE,UAAU,CAAC,KAAK,CAAC;MACjB,OAAO,KAAK,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMgK,WAAW,GAAG,MAAAA,CAAOE,MAAM,GAAG,SAAS,KAAK;IAChD5F,OAAO,CAACC,GAAG,CAAC,oCAAoC2F,MAAM,EAAE,CAAC;IAEzD,IAAI,CAAC1I,YAAY,CAACwB,OAAO,EAAE;MACzBsB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpE;IACF;;IAEA;IACAD,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IAClE,MAAM4F,WAAW,GAAG,MAAMoD,qBAAqB,CAAC,eAAerD,MAAM,EAAE,CAAC;IACxE,IAAIC,WAAW,EAAE;MACf7F,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;MAC5E6F,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACrB,OAAO,CAAC;IACV;;IAEA;IACA9F,OAAO,CAACC,GAAG,CAAC,+DAA+D2F,MAAM,GAAG,CAAC;IACrFpK,mBAAmB,CAAC,IAAI,CAAC;IAEzB9K,iBAAiB,CAAC0J,SAAS,CAAC,CAAC+O,IAAI,CAAElF,QAAQ,IAAK;MAC9CjE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEgE,QAAQ,CAACM,IAAI,CAAC;MAE3D,IAAI,CAACrH,YAAY,CAACwB,OAAO,EAAE;;MAE3B;MACAsB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgE,QAAQ,CAACM,IAAI,CAAC;;MAE1D;MACA,IAAIN,QAAQ,CAACM,IAAI,CAACsB,WAAW,EAAE;QAC7B7F,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzDvE,UAAU,CAAC,IAAI,CAAC;QAChBF,mBAAmB,CAAC,KAAK,CAAC;QAC1BF,YAAY,CAAC,EAAE,CAAC;QAChBwK,iBAAiB,CAAC,CAAC;QACnB;MACF;MAEA,IAAIsD,UAAU,GAAG,IAAI;MACrB,IAAInD,GAAG,GAAG,EAAE,CAAC,CAAC;;MAEd;MACA,IAAIhC,QAAQ,CAACM,IAAI,CAACwB,MAAM,EAAE;QACxB,MAAMsC,MAAM,GAAGpE,QAAQ,CAACM,IAAI,CAACwB,MAAM;QAEnC,IAAIsC,MAAM,CAACrC,EAAE,EAAE;UACb;UACAoD,UAAU,GAAGf,MAAM,CAACrC,EAAE;UACtBhG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACnD,CAAC,MAAM,IAAIoI,MAAM,CAACG,MAAM,EAAE;UACxB;UACAY,UAAU,GAAGf,MAAM,CAACG,MAAM;UAC1BxI,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACvD,CAAC,MAAM,IAAI,OAAOoI,MAAM,KAAK,QAAQ,EAAE;UACrC;UACAe,UAAU,GAAGf,MAAM;UACnBrI,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACzD;;QAEA;QACA,IAAIoI,MAAM,CAACpC,GAAG,EAAE;UACdA,GAAG,GAAG3N,IAAI,CAAC4N,GAAG,CAAC,CAAC,EAAEmC,MAAM,CAACpC,GAAG,GAAG3N,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;UAC7DuH,OAAO,CAACC,GAAG,CAAC,+BAA+BgG,GAAG,WAAW,CAAC;QAC5D;MACF;MAEA,IAAImD,UAAU,EAAE;QACd;QACA,IAAIA,UAAU,CAACb,UAAU,CAAC,aAAa,CAAC,EAAE;UACxCjN,YAAY,CAAC8N,UAAU,CAAC;UACxBpJ,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEmJ,UAAU,CAACxH,MAAM,CAAC;UAC3EpG,mBAAmB,CAAC,KAAK,CAAC;;UAE1B;UACA2J,gBAAgB,CAACc,GAAG,CAAC;QACvB,CAAC,MAAM;UACLjG,OAAO,CAACvD,KAAK,CAAC,2CAA2C,EAAE2M,UAAU,CAAClJ,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;UACxF1E,mBAAmB,CAAC,KAAK,CAAC;QAC5B;MACF,CAAC,MAAM;QACLwE,OAAO,CAACvD,KAAK,CAAC,uCAAuC,EAAEwH,QAAQ,CAACM,IAAI,CAAC;QACrE/I,mBAAmB,CAAC,KAAK,CAAC;MAC5B;;MAEA;MACA;;MAEA;IAEF,CAAC,CAAC,CAAC6N,KAAK,CAAE5M,KAAK,IAAK;MAClBuD,OAAO,CAACvD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CjB,mBAAmB,CAAC,KAAK,CAAC;;MAE1B;MACA,IAAI0B,YAAY,CAACwB,OAAO,IAAI,CAACvB,oBAAoB,CAACuB,OAAO,EAAE;QACzDsB,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;QAC7EhD,UAAU,CAACyB,OAAO,GAAGqD,UAAU,CAAC,MAAM;UACpC,IAAI7E,YAAY,CAACwB,OAAO,IAAI,CAACvB,oBAAoB,CAACuB,OAAO,EAAE;YACzDsB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;YACjDyF,WAAW,CAAC,aAAa,CAAC;UAC5B;QACF,CAAC,EAAE,KAAK,CAAC;MACX;IACF,CAAC,CAAC,CAAC4D,OAAO,CAAC,MAAM;MACf,IAAIpM,YAAY,CAACwB,OAAO,EAAE;QACxBlD,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ,CAAC;EAEDjM,SAAS,CAAC,MAAM;IACd,IAAIoN,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE1C,GAAG,EAAE;MACrB/I,gBAAgB,CAACyL,YAAY,CAAC1C,GAAG,CAAC,CAC/BkP,IAAI,CAAClF,QAAQ,IAAI;QAChBjE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgE,QAAQ,CAACM,IAAI,CAACgF,WAAW,CAAC;QAC3DhM,aAAa,CAAC0G,QAAQ,CAACM,IAAI,CAACgF,WAAW,CAAC;MAC1C,CAAC,CAAC,CACDF,KAAK,CAAC5M,KAAK,IAAI;QACduD,OAAO,CAACvD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;;MAEJ;MACA+M,mBAAmB,CAAC7M,YAAY,CAAC;IACnC;EACF,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM6M,mBAAmB,GAAG,MAAAA,CAAOxI,IAAI,EAAEyI,WAAW,GAAG,KAAK,EAAEC,QAAQ,GAAG,QAAQ,KAAK;IAAA,IAAAC,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IACpF;IACA,MAAMC,iBAAiB,GAAG,CAAC;IAC3B,MAAMC,uBAAuB,GAAG,EAAAL,kBAAA,GAAA3I,IAAI,CAACiJ,YAAY,cAAAN,kBAAA,wBAAAC,qBAAA,GAAjBD,kBAAA,CAAmBO,QAAQ,cAAAN,qBAAA,uBAA3BA,qBAAA,CAA6BO,wBAAwB,KACnF,IAAI3R,IAAI,CAAC,CAAC,GAAG,IAAIA,IAAI,CAACwI,IAAI,CAACiJ,YAAY,CAACC,QAAQ,CAACC,wBAAwB,CAAC,GAAGJ,iBAAiB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;;IAEtH;IACA,IAAI,CAACN,WAAW,KAAAI,mBAAA,GAAI7I,IAAI,CAACiJ,YAAY,cAAAJ,mBAAA,gBAAAC,qBAAA,GAAjBD,mBAAA,CAAmBK,QAAQ,cAAAJ,qBAAA,eAA3BA,qBAAA,CAA6BM,eAAe,IAAIJ,uBAAuB,EAAE;MAE3F;IACF;;IAEA;IACA,MAAMK,UAAU,GAAG,mBAAmBrJ,IAAI,CAAC/G,GAAG,EAAE;IAChD,IAAI1G,MAAM,CAAC+W,sBAAsB,IAAI/W,MAAM,CAAC+W,sBAAsB,CAACD,UAAU,CAAC,EAAE;MAE9E;IACF;;IAEA;IACA,IAAI,CAAC9W,MAAM,CAAC+W,sBAAsB,EAAE/W,MAAM,CAAC+W,sBAAsB,GAAG,CAAC,CAAC;IACtE/W,MAAM,CAAC+W,sBAAsB,CAACD,UAAU,CAAC,GAAG,IAAI;IAEhD,IAAI;MACF,MAAMpG,QAAQ,GAAG,MAAM9S,yBAAyB,CAAC6P,IAAI,CAAC/G,GAAG,EAAEI,KAAK,CAAC;MAEjE,IAAI4J,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACtB,MAAM,KAAK,GAAG,IAAIgB,QAAQ,CAACM,IAAI,CAACgG,iBAAiB,EAAE;QAEpF;QACAjO,QAAQ,CAACkO,SAAS,IAChBA,SAAS,CAAC9G,GAAG,CAAC+G,CAAC;UAAA,IAAAC,eAAA;UAAA,OACbD,CAAC,CAACxQ,GAAG,KAAK+G,IAAI,CAAC/G,GAAG,GACd;YACA,GAAGwQ,CAAC;YACJR,YAAY,EAAE;cACZ,GAAGQ,CAAC,CAACR,YAAY;cACjBC,QAAQ,EAAE;gBACR,KAAAQ,eAAA,GAAGD,CAAC,CAACR,YAAY,cAAAS,eAAA,uBAAdA,eAAA,CAAgBR,QAAQ;gBAC3BE,eAAe,EAAEnG,QAAQ,CAACM,IAAI,CAACgG,iBAAiB;gBAChDJ,wBAAwB,EAAE,IAAI3R,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC;cACnD;YACF;UACF,CAAC,GACC4H,CAAC;QAAA,CACP,CACF,CAAC;;QAED;QACA,IAAI9N,YAAY,IAAIA,YAAY,CAAC1C,GAAG,KAAK+G,IAAI,CAAC/G,GAAG,EAAE;UACjD2C,eAAe,CAAC4I,IAAI;YAAA,IAAAmF,kBAAA;YAAA,OAAK;cACvB,GAAGnF,IAAI;cACPyE,YAAY,EAAE;gBACZ,GAAGzE,IAAI,CAACyE,YAAY;gBACpBC,QAAQ,EAAE;kBACR,KAAAS,kBAAA,GAAGnF,IAAI,CAACyE,YAAY,cAAAU,kBAAA,uBAAjBA,kBAAA,CAAmBT,QAAQ;kBAC9BE,eAAe,EAAEnG,QAAQ,CAACM,IAAI,CAACgG,iBAAiB;kBAChDJ,wBAAwB,EAAE,IAAI3R,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC;gBACnD;cACF;YACF,CAAC;UAAA,CAAC,CAAC;QACL;MACF;IACF,CAAC,CAAC,OAAOpG,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,0CAA0CuE,IAAI,CAACwG,IAAI,GAAG,EAAE/K,KAAK,CAAC;IAC9E,CAAC,SAAS;MACR;MACA,IAAIlJ,MAAM,CAAC+W,sBAAsB,EAAE;QACjC,OAAO/W,MAAM,CAAC+W,sBAAsB,CAACD,UAAU,CAAC;MAClD;IACF;EACF,CAAC;;EAED;EACA,MAAMO,yBAAyB,GAAGA,CAACvO,KAAK,EAAEwO,SAAS,GAAG,OAAO,KAAK;IAChE,MAAMC,kBAAkB,GAAGzO,KAAK,CAACmI,MAAM,CAACxD,IAAI,IAAI;MAAA,IAAA+J,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA;MAC9C,MAAMC,QAAQ,IAAAJ,mBAAA,GAAG/J,IAAI,CAACiJ,YAAY,cAAAc,mBAAA,wBAAAC,qBAAA,GAAjBD,mBAAA,CAAmBb,QAAQ,cAAAc,qBAAA,uBAA3BA,qBAAA,CAA6BZ,eAAe;MAC7D,MAAML,iBAAiB,GAAG,CAAC;MAC3B,MAAMqB,eAAe,GAAG,EAAAH,mBAAA,GAAAjK,IAAI,CAACiJ,YAAY,cAAAgB,mBAAA,wBAAAC,qBAAA,GAAjBD,mBAAA,CAAmBf,QAAQ,cAAAgB,qBAAA,uBAA3BA,qBAAA,CAA6Bf,wBAAwB,KAC3E,IAAI3R,IAAI,CAAC,CAAC,GAAG,IAAIA,IAAI,CAACwI,IAAI,CAACiJ,YAAY,CAACC,QAAQ,CAACC,wBAAwB,CAAC,GAAGJ,iBAAiB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;MAEtH,OAAO,CAACoB,QAAQ,IAAI,CAACC,eAAe;IACtC,CAAC,CAAC;IAEF,IAAIN,kBAAkB,CAAClJ,MAAM,KAAK,CAAC,EAAE;MACnC;IACF;;IAEA;IACA,MAAMyJ,aAAa,GAAGP,kBAAkB,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACpD,MAAMC,YAAY,GAAGT,kBAAkB,CAACQ,KAAK,CAAC,CAAC,CAAC;;IAEhD;IACAD,aAAa,CAACG,OAAO,CAAC,CAACxK,IAAI,EAAEyK,KAAK,KAAK;MACrC1J,UAAU,CAAC,MAAM;QACfyH,mBAAmB,CAACxI,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;MAC1C,CAAC,EAAEyK,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC;;IAEF;IACAF,YAAY,CAACC,OAAO,CAAC,CAACxK,IAAI,EAAEyK,KAAK,KAAK;MACpC1J,UAAU,CAAC,MAAM;QACfyH,mBAAmB,CAACxI,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC;MAC5C,CAAC,EAAGqK,aAAa,CAACzJ,MAAM,GAAG,GAAG,GAAK6J,KAAK,GAAG,GAAI,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMzH,QAAQ,GAAG,MAAM5S,sBAAsB,CAAC+I,SAAS,EAAEC,KAAK,CAAC;MAC/D,IAAI4J,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACtB,MAAM,KAAK,GAAG,EAAE;QACjDtD,eAAe,CAACsE,QAAQ,CAACM,IAAI,CAAC7E,YAAY,CAAC;QAC3C;MACF;IACF,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;IAC3E;EACF,CAAC;;EAID;EACA,MAAMkP,cAAc,GAAGA,CAACC,WAAW,EAAEC,YAAY,KAAK;IACpD,IAAI,CAACD,WAAW,IAAI,CAACC,YAAY,EAAE,OAAO,KAAK;;IAE/C;IACA,MAAMC,SAAS,GAAGF,WAAW,CAACpJ,IAAI,CAAC,CAAC;IACpC,MAAMuJ,WAAW,GAAGF,YAAY,CAACnT,QAAQ,CAAC,CAAC;IAE3C,MAAMsT,eAAe,GAAG,CACtBF,SAAS,KAAKC,WAAW;IAAE;IAC3BD,SAAS,CAACvD,UAAU,CAAC,UAAU,CAAC;IAAE;IAClC,WAAW,CAAC0D,IAAI,CAACH,SAAS,CAAC;IAAE;IAC7BA,SAAS,KAAK,WAAWC,WAAW,EAAE;IAAE;IACxCD,SAAS,CAAClK,MAAM,GAAG,CAAC;IAAE;IACtB,kBAAkB,CAACqK,IAAI,CAACH,SAAS,CAAC,CAAC;IAAA,CACpC;IAED,OAAOE,eAAe,CAACnE,IAAI,CAACqE,OAAO,IAAIA,OAAO,CAAC;EACjD,CAAC;;EAID;EACA,MAAMC,kBAAkB,GAAG,MAAAA,CAAOnH,MAAM,EAAEoH,QAAQ,KAAK;IACrD,IAAI;MACFpM,OAAO,CAACC,GAAG,CAAC,yCAAyCmM,QAAQ,KAAK,CAAC;MACnE,MAAMnI,QAAQ,GAAG,MAAMzS,0BAA0B,CAACwT,MAAM,EAAE3K,KAAK,CAAC;MAEhE,IAAI4J,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACtB,MAAM,KAAK,GAAG,EAAE;QACjDjD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgE,QAAQ,CAACM,IAAI,CAAC;;QAExD;QACA,IAAIN,QAAQ,CAACM,IAAI,CAACqH,WAAW,KAAK3H,QAAQ,CAACM,IAAI,CAAC8H,OAAO,IAAIpI,QAAQ,CAACM,IAAI,CAAC+H,qBAAqB,EAAE;UAC9FhQ,QAAQ,CAACkO,SAAS,IAChBA,SAAS,CAAC9G,GAAG,CAAC+G,CAAC;YAAA,IAAA8B,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;YAAA,OACblC,CAAC,CAACxQ,GAAG,KAAK+K,MAAM,GACZ;cACA,GAAGyF,CAAC;cACJjD,IAAI,EAAEvD,QAAQ,CAACM,IAAI,CAACqH,WAAW;cAC/B3B,YAAY,EAAE;gBACZ,GAAGQ,CAAC,CAACR,YAAY;gBACjBC,QAAQ,EAAE;kBACR,KAAAqC,gBAAA,GAAG9B,CAAC,CAACR,YAAY,cAAAsC,gBAAA,uBAAdA,gBAAA,CAAgBrC,QAAQ;kBAC3BE,eAAe,EAAEnG,QAAQ,CAACM,IAAI,CAACgG,iBAAiB,MAAAiC,gBAAA,GAAI/B,CAAC,CAACR,YAAY,cAAAuC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtC,QAAQ,cAAAuC,qBAAA,uBAAxBA,qBAAA,CAA0BrC,eAAe;kBAC7FD,wBAAwB,EAAElG,QAAQ,CAACM,IAAI,CAAC+H,qBAAqB,GAAG,IAAI9T,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC,CAAC,IAAA6J,gBAAA,GAAGjC,CAAC,CAACR,YAAY,cAAAyC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxC,QAAQ,cAAAyC,qBAAA,uBAAxBA,qBAAA,CAA0BxC,wBAAwB;kBAC7IyC,QAAQ,EAAE3I,QAAQ,CAACM,IAAI,CAACqH;gBAC1B;cACF;YACF,CAAC,GACCnB,CAAC;UAAA,CACP,CACF,CAAC;;UAED;UACA,IAAI9N,YAAY,IAAIA,YAAY,CAAC1C,GAAG,KAAK+K,MAAM,EAAE;YAC/CpI,eAAe,CAAC4I,IAAI;cAAA,IAAAqH,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA;cAAA,OAAK;gBACvB,GAAGzH,IAAI;gBACPgC,IAAI,EAAEvD,QAAQ,CAACM,IAAI,CAACqH,WAAW;gBAC/B3B,YAAY,EAAE;kBACZ,GAAGzE,IAAI,CAACyE,YAAY;kBACpBC,QAAQ,EAAE;oBACR,KAAA2C,mBAAA,GAAGrH,IAAI,CAACyE,YAAY,cAAA4C,mBAAA,uBAAjBA,mBAAA,CAAmB3C,QAAQ;oBAC9BE,eAAe,EAAEnG,QAAQ,CAACM,IAAI,CAACgG,iBAAiB,MAAAuC,mBAAA,GAAItH,IAAI,CAACyE,YAAY,cAAA6C,mBAAA,wBAAAC,qBAAA,GAAjBD,mBAAA,CAAmB5C,QAAQ,cAAA6C,qBAAA,uBAA3BA,qBAAA,CAA6B3C,eAAe;oBAChGD,wBAAwB,EAAElG,QAAQ,CAACM,IAAI,CAAC+H,qBAAqB,GAAG,IAAI9T,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC,CAAC,IAAAmK,mBAAA,GAAGxH,IAAI,CAACyE,YAAY,cAAA+C,mBAAA,wBAAAC,qBAAA,GAAjBD,mBAAA,CAAmB9C,QAAQ,cAAA+C,qBAAA,uBAA3BA,qBAAA,CAA6B9C,wBAAwB;oBAChJyC,QAAQ,EAAE3I,QAAQ,CAACM,IAAI,CAACqH;kBAC1B;gBACF;cACF,CAAC;YAAA,CAAC,CAAC;UACL;QACF;;QAEA;QACAlZ,KAAK,CAACwa,OAAO,CAACjJ,QAAQ,CAACM,IAAI,CAACvM,OAAO,EAAE;UACnCmV,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE,IAAI;UACfC,eAAe,EAAE,KAAK;UACtBC,YAAY,EAAE,IAAI;UAClBC,YAAY,EAAE,IAAI;UAClBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO/Q,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,8CAA8C2P,QAAQ,GAAG,EAAE3P,KAAK,CAAC;MAC/E/J,KAAK,CAAC+J,KAAK,CAAC,0CAA0C,EAAE;QACtD0Q,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMK,qBAAqB,GAAG,MAAAA,CAAOhE,WAAW,GAAG,KAAK,KAAK;IAC3D;IACA,MAAMiE,WAAW,GAAGpU,YAAY,CAACC,OAAO,CAAC,sBAAsBa,SAAS,EAAE,CAAC;IAC3E,MAAMuT,eAAe,GAAGrU,YAAY,CAACC,OAAO,CAAC,gCAAgCa,SAAS,EAAE,CAAC;IAEzF,IAAI,CAACqP,WAAW,IAAIiE,WAAW,IAAIC,eAAe,EAAE;MAClD,MAAMC,QAAQ,GAAG,IAAIpV,IAAI,CAAC,CAAC,GAAG,IAAIA,IAAI,CAACmV,eAAe,CAAC;MACvD,MAAM5D,iBAAiB,GAAG,CAAC;MAE3B,IAAI6D,QAAQ,GAAG7D,iBAAiB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;QACtD/J,OAAO,CAACC,GAAG,CAAC,iEAAiE8J,iBAAiB,QAAQ,CAAC;QACvG3K,mBAAmB,CAACsO,WAAW,CAAC;QAChC;MACF;IACF;IAEA,IAAI;MACF,MAAMzJ,QAAQ,GAAG,MAAM7S,2BAA2B,CAACgJ,SAAS,EAAEC,KAAK,CAAC;MAEpE,IAAI4J,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACtB,MAAM,KAAK,GAAG,IAAIgB,QAAQ,CAACM,IAAI,CAACgG,iBAAiB,EAAE;QAEpF;QACAnL,mBAAmB,CAAC6E,QAAQ,CAACM,IAAI,CAACgG,iBAAiB,CAAC;QACpDjR,YAAY,CAACuU,OAAO,CAAC,sBAAsBzT,SAAS,EAAE,EAAE6J,QAAQ,CAACM,IAAI,CAACgG,iBAAiB,CAAC;QACxFjR,YAAY,CAACuU,OAAO,CAAC,gCAAgCzT,SAAS,EAAE,EAAE,IAAI5B,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC,CAAC,CAAC;MAE7F,CAAC,MAAM;QACLzD,mBAAmB,CAAC,IAAI,CAAC;QACzB;QACA9F,YAAY,CAACwU,UAAU,CAAC,sBAAsB1T,SAAS,EAAE,CAAC;QAC1Dd,YAAY,CAACwU,UAAU,CAAC,gCAAgC1T,SAAS,EAAE,CAAC;MACtE;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MAAA,IAAAsR,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACdlO,OAAO,CAACvD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;;MAEhE;MACA,KAAAsR,gBAAA,GAAItR,KAAK,CAACwH,QAAQ,cAAA8J,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxJ,IAAI,cAAAyJ,qBAAA,gBAAAC,sBAAA,GAApBD,qBAAA,CAAsBvR,KAAK,cAAAwR,sBAAA,gBAAAC,sBAAA,GAA3BD,sBAAA,CAA6BE,QAAQ,cAAAD,sBAAA,eAArCA,sBAAA,CAAAE,IAAA,CAAAH,sBAAA,EAAwC,gCAAgC,CAAC,EAAE;QAC7EjO,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;QAErF,IAAI;UAAA,IAAAoO,iBAAA;UACF,MAAM;YAAEC;UAAiB,CAAC,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC;UAC/D,MAAMC,WAAW,GAAG,MAAMD,gBAAgB,CAAClU,SAAS,EAAEC,KAAK,CAAC;UAE5D,IAAI,EAAAgU,iBAAA,GAAAE,WAAW,CAAChK,IAAI,cAAA8J,iBAAA,uBAAhBA,iBAAA,CAAkBpL,MAAM,MAAK,GAAG,EAAE;YAAA,IAAAuL,mBAAA;YACpCxO,OAAO,CAACC,GAAG,CAAC,iCAAiCsO,WAAW,CAAChK,IAAI,CAACkK,GAAG,EAAE,CAAC;YACpEzO,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;;YAE7D;YACA,MAAMyO,aAAa,GAAG,MAAMtd,2BAA2B,CAACgJ,SAAS,EAAEC,KAAK,CAAC;YAEzE,IAAI,EAAAmU,mBAAA,GAAAE,aAAa,CAACnK,IAAI,cAAAiK,mBAAA,uBAAlBA,mBAAA,CAAoBvL,MAAM,MAAK,GAAG,IAAIyL,aAAa,CAACnK,IAAI,CAACgG,iBAAiB,EAAE;cAC9EnL,mBAAmB,CAACsP,aAAa,CAACnK,IAAI,CAACgG,iBAAiB,CAAC;cACzDjR,YAAY,CAACuU,OAAO,CAAC,sBAAsBzT,SAAS,EAAE,EAAEsU,aAAa,CAACnK,IAAI,CAACgG,iBAAiB,CAAC;cAC7FjR,YAAY,CAACuU,OAAO,CAAC,gCAAgCzT,SAAS,EAAE,EAAE,IAAI5B,IAAI,CAAC,CAAC,CAACqK,WAAW,CAAC,CAAC,CAAC;cAC3F7C,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;cAC1E;YACF;UACF,CAAC,MAAM;YACLD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEsO,WAAW,CAAChK,IAAI,CAAC;UACvE;QACF,CAAC,CAAC,OAAOoK,QAAQ,EAAE;UACjB3O,OAAO,CAACvD,KAAK,CAAC,iCAAiC,EAAEkS,QAAQ,CAAC;QAC5D;MACF;MAEAvP,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMwP,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACjS,YAAY,IAAI,CAACsB,eAAe,IAAIE,mBAAmB,EAAE;MAC5D;IACF;IAEAC,sBAAsB,CAAC,IAAI,CAAC;IAC5BkB,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;;IAEhC,IAAI;MACF,MAAMuP,QAAQ,GAAG9Q,YAAY,GAAG,CAAC;MACjC,MAAM+Q,QAAQ,GAAG,EAAE;MAEnB,MAAM7K,QAAQ,GAAG,MAAMpT,mBAAmB,CAACuJ,SAAS,EAAEuC,YAAY,CAAC1C,GAAG,EAAE4U,QAAQ,EAAEC,QAAQ,EAAEzU,KAAK,CAAC;MAElG,IAAI4J,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,IAAIoM,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,IAAIoM,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAAC0M,IAAI,EAAE;QAAA,IAAAwK,qBAAA;QAC1G,MAAMC,WAAW,GAAG/K,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAAC0M,IAAI;QAClD,MAAM0K,UAAU,GAAG,EAAAF,qBAAA,GAAA9K,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAACqX,QAAQ,CAAC,CAAC,CAAC,cAAAH,qBAAA,uBAArCA,qBAAA,CAAuCE,UAAU,KAAI,CAAC;QAEzE,IAAID,WAAW,CAACpN,MAAM,KAAK,CAAC,EAAE;UAC5B1D,kBAAkB,CAAC,KAAK,CAAC;QAC3B,CAAC,MAAM;UACL;UACAlB,sBAAsB,CAACmS,YAAY,IAAI;YACrC;YACA,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAACF,YAAY,CAACzL,GAAG,CAACQ,GAAG,IAAIA,GAAG,CAACjK,GAAG,CAAC,CAAC;YAC7D,MAAMqV,iBAAiB,GAAGN,WAAW,CAACxK,MAAM,CAACN,GAAG,IAAI,CAACkL,WAAW,CAACG,GAAG,CAACrL,GAAG,CAACjK,GAAG,CAAC,CAAC;;YAE9E;YACA,OAAO,CAAC,GAAGqV,iBAAiB,EAAE,GAAGH,YAAY,CAAC;UAChD,CAAC,CAAC;;UAEF;UACA,MAAMK,QAAQ,GAAG7S,YAAY,CAAC1C,GAAG;UACjC0D,gBAAgB,CAACwF,SAAS,IAAI;YAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;YACnC,MAAMsM,gBAAgB,GAAGrM,QAAQ,CAACE,GAAG,CAACkM,QAAQ,CAAC,IAAI,EAAE;YAErD,MAAMJ,WAAW,GAAG,IAAIC,GAAG,CAACI,gBAAgB,CAAC/L,GAAG,CAACQ,GAAG,IAAIA,GAAG,CAACjK,GAAG,CAAC,CAAC;YACjE,MAAMqV,iBAAiB,GAAGN,WAAW,CAACxK,MAAM,CAACN,GAAG,IAAI,CAACkL,WAAW,CAACG,GAAG,CAACrL,GAAG,CAACjK,GAAG,CAAC,CAAC;YAE9E,MAAMyV,kBAAkB,GAAG,CAAC,GAAGJ,iBAAiB,EAAE,GAAGG,gBAAgB,CAAC;YAEtErM,QAAQ,CAACG,GAAG,CAACiM,QAAQ,EAAEE,kBAAkB,CAAC;YAC1C,OAAOtM,QAAQ;UACjB,CAAC,CAAC;UAEFpF,eAAe,CAAC6Q,QAAQ,CAAC;;UAEzB;UACA7R,sBAAsB,CAAC2S,eAAe,IAAI;YACxC,MAAMC,YAAY,GAAGD,eAAe,CAAC/N,MAAM;YAE3C,IAAIgO,YAAY,IAAIX,UAAU,EAAE;cAC9B/Q,kBAAkB,CAAC,KAAK,CAAC;YAC3B;YAEA,OAAOyR,eAAe,CAAC,CAAC;UAC1B,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLzR,kBAAkB,CAAC,KAAK,CAAC;QACzB8B,OAAO,CAACC,GAAG,CAAC,wDAAwDtD,YAAY,CAAC6K,IAAI,EAAE,CAAC;MAC1F;IACF,CAAC,CAAC,OAAO/K,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,0CAA0CE,YAAY,CAAC6K,IAAI,GAAG,EAAE/K,KAAK,CAAC;IACtF,CAAC,SAAS;MACR2B,sBAAsB,CAAC,KAAK,CAAC;MAC7BkB,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMuQ,oBAAoB,GAAIhP,CAAC,IAAK;IAClC,MAAMc,SAAS,GAAGd,CAAC,CAACwB,MAAM;IAC1B,MAAMR,SAAS,GAAGF,SAAS,CAACE,SAAS;IACrC,MAAMiO,eAAe,GAAG,GAAG,CAAC,CAAC;;IAE7B;IACA,IAAIvQ,cAAc,EAAE;MAClBS,OAAO,CAACC,GAAG,CAAC,8DAA8DtD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6K,IAAI,EAAE,CAAC;MAC/F;IACF;;IAEA;IACA,IAAI3J,eAAe,EAAE;MACnB;IACF;;IAEA;IACA,IAAIgE,SAAS,IAAIiO,eAAe,IAAI7R,eAAe,IAAI,CAACE,mBAAmB,EAAE;MAC3E6B,OAAO,CAACC,GAAG,CAAC,gEAAgEtD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6K,IAAI,EAAE,CAAC;;MAEjG;MACA,MAAMuI,mBAAmB,GAAGpO,SAAS,CAACG,YAAY;MAClD,MAAMkO,gBAAgB,GAAGnO,SAAS;MAElC+M,gBAAgB,CAAC,CAAC,CAACzF,IAAI,CAAC,MAAM;QAC5B;QACA,MAAM8G,aAAa,GAAGA,CAAA,KAAM;UAC1B,MAAMC,eAAe,GAAGvO,SAAS,CAACG,YAAY;UAC9C,MAAMqO,gBAAgB,GAAGD,eAAe,GAAGH,mBAAmB;UAC9D,MAAMK,YAAY,GAAGJ,gBAAgB,GAAGG,gBAAgB;;UAExD;UACAxO,SAAS,CAACE,SAAS,GAAGuO,YAAY;;UAElC;UACAC,qBAAqB,CAAC,MAAM;YAC1B,IAAI/X,IAAI,CAACwP,GAAG,CAACnG,SAAS,CAACE,SAAS,GAAGuO,YAAY,CAAC,GAAG,CAAC,EAAE;cACpDzO,SAAS,CAACE,SAAS,GAAGuO,YAAY;YACpC;UACF,CAAC,CAAC;QACJ,CAAC;;QAED;QACAH,aAAa,CAAC,CAAC;QACflO,UAAU,CAACkO,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAIE,MAAM,CAACK,IAAI,EAAE3H,OAAO,CAAC,GAAGrZ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAACihB,YAAY,EAAE3H,eAAe,CAAC,GAAGtZ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACxD,MAAMkhB,iBAAiB,GAAG9gB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC+gB,WAAW,EAAEC,cAAc,CAAC,GAAGphB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEvD,MAAMuZ,UAAU,GAAG,MAAAA,CAAO8H,OAAO,EAAEC,MAAM,GAAG,KAAK,EAAEhL,MAAM,GAAG,SAAS,KAAK;IACxE5F,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE0Q,OAAO,EAAE,SAAS,EAAEC,MAAM,EAAE,SAAS,EAAEhL,MAAM,CAAC;IAC/F5F,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE7F,SAAS,EAAE,QAAQ,EAAEC,KAAK,GAAG,UAAU,GAAG,SAAS,CAAC;;IAE9F;IACA,IAAI6E,gBAAgB,CAACR,OAAO,IAAI,CAACkS,MAAM,EAAE;MACvC5Q,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAE2F,MAAM,CAAC;MACpF;IACF;IAEA,IAAI,CAACgL,MAAM,EAAE;MACX1R,gBAAgB,CAACR,OAAO,GAAG,IAAI;IACjC;IAEA,IAAI;MACF,MAAMoQ,QAAQ,GAAG,EAAE;MACnB,MAAM+B,KAAK,GAAGhU,WAAW;MAEzBmD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0Q,OAAO,CAAC;MAC7C3Q,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4Q,KAAK,CAAC;MAExC,MAAM5M,QAAQ,GAAG,MAAMrT,gBAAgB,CAACwJ,SAAS,EAAEuW,OAAO,EAAE7B,QAAQ,EAAE+B,KAAK,EAAExW,KAAK,CAAC;MAEnF,MAAMyW,QAAQ,GAAG7M,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAAC0M,IAAI;MAE/C,IAAIuM,QAAQ,CAAClP,MAAM,KAAK,CAAC,EAAE;QACzBgH,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;MAC1B;MAEAtM,QAAQ,CAAEkO,SAAS,IAAK;QACtB,IAAIuG,UAAU;QACd,IAAIH,MAAM,EAAE;UACV;UACA,MAAMxB,WAAW,GAAG,IAAIC,GAAG,CAAC7E,SAAS,CAAC9G,GAAG,CAAC1C,IAAI,IAAIA,IAAI,CAAC/G,GAAG,CAAC,CAAC;UAC5D,MAAM+W,cAAc,GAAGF,QAAQ,CAACtM,MAAM,CAACxD,IAAI,IAAI,CAACoO,WAAW,CAACG,GAAG,CAACvO,IAAI,CAAC/G,GAAG,CAAC,CAAC;UAC1E8W,UAAU,GAAG,CAAC,GAAGvG,SAAS,EAAE,GAAGwG,cAAc,CAAC;QAChD,CAAC,MAAM;UACL;UACA,MAAMC,WAAW,GAAGH,QAAQ,CAACtM,MAAM,CAAC,CAACxD,IAAI,EAAEyK,KAAK,EAAEyF,IAAI,KACpDzF,KAAK,KAAKyF,IAAI,CAACrN,SAAS,CAAC4G,CAAC,IAAIA,CAAC,CAACxQ,GAAG,KAAK+G,IAAI,CAAC/G,GAAG,CAClD,CAAC;UACD8W,UAAU,GAAGE,WAAW;;UAExB;UACA,IAAIA,WAAW,CAACrP,MAAM,GAAG,CAAC,IAAI,CAACtD,eAAe,IAAI,CAACM,YAAY,IAAI/B,WAAW,KAAK,EAAE,IAAImC,aAAa,EAAE;YACtGgB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;YACrDpB,eAAe,CAAC,IAAI,CAAC;YACrBI,gBAAgB,CAAC,KAAK,CAAC;YACvB8C,UAAU,CAAC,MAAM;cACfoP,eAAe,CAACF,WAAW,CAAC;YAC9B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;UACZ;QACF;QACA;QACA,IAAI,CAACL,MAAM,EAAE;UACX;UACA7O,UAAU,CAAC,MAAM;YACf6I,yBAAyB,CAACmG,UAAU,EAAE,sBAAsB,CAAC;UAC/D,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACX;;QAEA;QACA,IAAIH,MAAM,IAAIG,UAAU,CAACnP,MAAM,GAAG,CAAC,EAAE;UACnCG,UAAU,CAAC,MAAM;YACf6I,yBAAyB,CAACmG,UAAU,EAAE,WAAW,CAAC;UACpD,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACX;;QAEA;QACA,IAAI,CAACH,MAAM,IAAIG,UAAU,CAACnP,MAAM,GAAG,CAAC,EAAE;UACpCG,UAAU,CAAC,MAAM;YACf2J,iBAAiB,CAAC,CAAC;UACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACX;QAEA,OAAOqF,UAAU;MACnB,CAAC,CAAC;MACFvU,UAAU,CAAC,KAAK,CAAC;MACjBkU,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOU,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,cAAA;MACZtR,OAAO,CAACvD,KAAK,CAAC,2BAA2B,EAAE2U,GAAG,CAAC;MAC/CpR,OAAO,CAACvD,KAAK,CAAC,sBAAsB,EAAE;QACpCzE,OAAO,EAAEoZ,GAAG,CAACpZ,OAAO;QACpBiL,MAAM,GAAAoO,aAAA,GAAED,GAAG,CAACnN,QAAQ,cAAAoN,aAAA,uBAAZA,aAAA,CAAcpO,MAAM;QAC5BsB,IAAI,GAAA+M,cAAA,GAAEF,GAAG,CAACnN,QAAQ,cAAAqN,cAAA,uBAAZA,cAAA,CAAc/M,IAAI;QACxBgN,MAAM,EAAEH,GAAG,CAACG;MACd,CAAC,CAAC;MACF/U,UAAU,CAAC,KAAK,CAAC;MACjBkU,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,SAAS;MACR;MACA,IAAI,CAACE,MAAM,EAAE;QACX1R,gBAAgB,CAACR,OAAO,GAAG,KAAK;MAClC;IACF;EACF,CAAC;;EAID;EACA,MAAM8S,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACjB,YAAY,IAAIE,WAAW,EAAE;IAElC,MAAM9O,SAAS,GAAG6O,iBAAiB,CAAC9R,OAAO;IAC3C,IAAI,CAACiD,SAAS,EAAE;IAEhB,MAAM8P,MAAM,GAAG9P,SAAS,CAACG,YAAY,GAAGH,SAAS,CAACE,SAAS,IAAIF,SAAS,CAAC+P,YAAY,GAAG,EAAE;IAC1F,IAAID,MAAM,EAAE;MACVzR,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3DyQ,cAAc,CAAC,IAAI,CAAC;MACpB/H,OAAO,CAAEgJ,QAAQ,IAAK;QACpB,MAAM9C,QAAQ,GAAG8C,QAAQ,GAAG,CAAC;QAC7B9I,UAAU,CAACgG,QAAQ,EAAE,IAAI,EAAE,YAAY,CAAC;QACxC,OAAOA,QAAQ;MACjB,CAAC,CAAC;IACJ;EACF,CAAC;EAOD,MAAM+C,UAAU,GAAI3Z,IAAI,IAAK;IAC3B,MAAMQ,GAAG,GAAG5G,MAAM,CAAC,CAAC,CAACggB,EAAE,CAAC,mBAAmB,CAAC;IAC5C,MAAMC,SAAS,GAAGjgB,MAAM,CAACoG,IAAI,CAAC,CAAC4Z,EAAE,CAAC,mBAAmB,CAAC;IAEtD,IAAIpZ,GAAG,CAACsZ,MAAM,CAACD,SAAS,EAAE,KAAK,CAAC,EAAE;MAChC,OAAOA,SAAS,CAAC3Z,MAAM,CAAC,OAAO,CAAC;IAClC,CAAC,MAAM,IAAIM,GAAG,CAACuZ,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAACD,MAAM,CAACD,SAAS,EAAE,KAAK,CAAC,EAAE;MAC3D,OAAO,OAAO;IAChB,CAAC,MAAM,IAAIrZ,GAAG,CAACuZ,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACH,SAAS,CAAC,EAAE;MACtD,OAAOA,SAAS,CAAC3Z,MAAM,CAAC,MAAM,CAAC;IACjC,CAAC,MAAM;MACL,OAAO2Z,SAAS,CAAC3Z,MAAM,CAAC,YAAY,CAAC;IACvC;EACF,CAAC;EAGD,MAAM+Z,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,GAAG,GAAG,MAAMnhB,qBAAqB,CAACoJ,SAAS,CAAC,CAAC,CAAC;;MAEpD;MACA1H,KAAK,CAACyf,GAAG,CAAC5N,IAAI,CAACL,GAAG,EAAE;QAClBkJ,SAAS,EAAE,IAAI;QACfgF,IAAI,EAAE;MACR,CAAC,CAAC;;MAEF;MACA;MACApW,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC/BV,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;MAClBI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;MACnB;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MAAA,IAAA4V,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdvS,OAAO,CAACvD,KAAK,CAAC,0BAA0B,EAAE,EAAA4V,gBAAA,GAAA5V,KAAK,CAACwH,QAAQ,cAAAoO,gBAAA,uBAAdA,gBAAA,CAAgB9N,IAAI,KAAI9H,KAAK,CAACzE,OAAO,CAAC;;MAEhF;MACAtF,KAAK,CAAC,EAAA4f,gBAAA,GAAA7V,KAAK,CAACwH,QAAQ,cAAAqO,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/N,IAAI,cAAAgO,qBAAA,uBAApBA,qBAAA,CAAsBrO,GAAG,KAAI,0BAA0B,EAAE;QAC7DkJ,SAAS,EAAE,IAAI;QACfgF,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMI,eAAe,GAAG,MAAO/X,OAAO,IAAK;IACzC,IAAI;MACF,MAAMwJ,QAAQ,GAAG,MAAMhT,eAAe,CAACwJ,OAAO,CAAC;MAC/C8C,aAAa,CAAC0G,QAAQ,CAACM,IAAI,CAACgF,WAAW,CAAC;MACxC7W,KAAK,CAACuR,QAAQ,CAACM,IAAI,CAACL,GAAG,EAAE;QACvBkJ,SAAS,EAAE,IAAI;QACfgF,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO3V,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF,CAAC;;EAED;EACA,MAAM0U,eAAe,GAAG,MAAOsB,QAAQ,IAAK;IAC1C,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAAC7Q,MAAM,KAAK,CAAC,EAAE;IAGxCrD,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAMmU,WAAW,GAAG,CAAC,GAAGD,QAAQ,CAAC,CAACnR,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAAA,IAAAmR,UAAA,EAAAC,WAAA,EAAAC,UAAA,EAAAC,WAAA;MAC/C,MAAMC,KAAK,GAAG,IAAIva,IAAI,CAAC,EAAAma,UAAA,GAAApR,CAAC,CAACvJ,OAAO,cAAA2a,UAAA,uBAATA,UAAA,CAAW7P,WAAW,OAAA8P,WAAA,GAAIrR,CAAC,CAACvJ,OAAO,cAAA4a,WAAA,uBAATA,WAAA,CAAW1a,SAAS,KAAI,CAAC,CAAC;MAC3E,MAAM8a,KAAK,GAAG,IAAIxa,IAAI,CAAC,EAAAqa,UAAA,GAAArR,CAAC,CAACxJ,OAAO,cAAA6a,UAAA,uBAATA,UAAA,CAAW/P,WAAW,OAAAgQ,WAAA,GAAItR,CAAC,CAACxJ,OAAO,cAAA8a,WAAA,uBAATA,WAAA,CAAW5a,SAAS,KAAI,CAAC,CAAC;MAC3E,OAAO8a,KAAK,GAAGD,KAAK;IACtB,CAAC,CAAC;;IAEF;IACA,MAAME,cAAc,GAAGP,WAAW,CAACpH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAC/C7M,kBAAkB,CAAC;MAAEC,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAEsU,cAAc,CAACrR;IAAO,CAAC,CAAC;IAEhE,MAAMsR,SAAS,GAAG,CAAC,CAAC,CAAC;;IAErB,IAAI;MACF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,CAACrR,MAAM,EAAEuR,CAAC,IAAID,SAAS,EAAE;QACzD,MAAME,KAAK,GAAGH,cAAc,CAAC3H,KAAK,CAAC6H,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAAC;;QAEpD;QACA,MAAMG,aAAa,GAAGD,KAAK,CAAC1P,GAAG,CAAC,MAAO1C,IAAI,IAAK;UAC9C,IAAI;YACF;YACA,IAAItD,aAAa,CAAC6R,GAAG,CAACvO,IAAI,CAAC/G,GAAG,CAAC,EAAE;cAC/B;YACF;YACA,MAAMgK,QAAQ,GAAG,MAAMpT,mBAAmB,CAACuJ,SAAS,EAAE4G,IAAI,CAAC/G,GAAG,EAAE,CAAC,EAAE,EAAE,EAAEI,KAAK,CAAC;YAE7E,IAAI4J,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,IAAIoM,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,IAAIoM,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAAC0M,IAAI,EAAE;cAC1G,MAAM1M,QAAQ,GAAGoM,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAAC0M,IAAI;;cAE/C;cACA5G,gBAAgB,CAACwF,SAAS,IAAI;gBAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;gBACnCC,QAAQ,CAACG,GAAG,CAACvC,IAAI,CAAC/G,GAAG,EAAEpC,QAAQ,CAAC;gBAChC,OAAOuL,QAAQ;cACjB,CAAC,CAAC;;cAEF;YACF,CAAC,MAAM;cACL;cACAzF,gBAAgB,CAACwF,SAAS,IAAI;gBAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;gBACnCC,QAAQ,CAACG,GAAG,CAACvC,IAAI,CAAC/G,GAAG,EAAE,EAAE,CAAC;gBAC1B,OAAOmJ,QAAQ;cACjB,CAAC,CAAC;cACF;YACF;UACF,CAAC,CAAC,OAAO3G,KAAK,EAAE;YACduD,OAAO,CAACvD,KAAK,CAAC,0BAA0BuE,IAAI,CAACwG,IAAI,GAAG,EAAE/K,KAAK,CAAC;YAC5D;YACAkB,gBAAgB,CAACwF,SAAS,IAAI;cAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;cACnCC,QAAQ,CAACG,GAAG,CAACvC,IAAI,CAAC/G,GAAG,EAAE,EAAE,CAAC;cAC1B,OAAOmJ,QAAQ;YACjB,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;;QAEF;QACA,MAAMkQ,OAAO,CAACC,GAAG,CAACF,aAAa,CAAC;;QAEhC;QACA,MAAMG,SAAS,GAAGlb,IAAI,CAACmb,GAAG,CAACN,CAAC,GAAGD,SAAS,EAAED,cAAc,CAACrR,MAAM,CAAC;QAChEnD,kBAAkB,CAAC;UAAEC,OAAO,EAAE8U,SAAS;UAAE7U,KAAK,EAAEsU,cAAc,CAACrR;QAAO,CAAC,CAAC;;QAExE;QACA,IAAIuR,CAAC,GAAGD,SAAS,GAAGD,cAAc,CAACrR,MAAM,EAAE;UACzC,MAAM8R,KAAK,GAAGT,cAAc,CAACrR,MAAM,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;UACtD,MAAM,IAAI0R,OAAO,CAACK,OAAO,IAAI5R,UAAU,CAAC4R,OAAO,EAAED,KAAK,CAAC,CAAC;QAC1D;MACF;MAEA1T,OAAO,CAACC,GAAG,CAAC,kCAAkCgT,cAAc,CAACrR,MAAM,kCAAkC,CAAC;MACtG5B,OAAO,CAACC,GAAG,CAAC,yBAAyBvC,aAAa,CAACkW,IAAI,aAAa,CAAC;;MAErE;MACA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAEI,CAAC,CAAC,OAAOnX,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD/J,KAAK,CAAC+J,KAAK,CAAC,mCAAmC,EAAE;QAC/C0Q,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,SAAS;MACR7O,kBAAkB,CAAC,KAAK,CAAC;MACzBE,kBAAkB,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACApP,SAAS,CAAC,MAAM;IACdyQ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEpD,WAAW,CAAC;IACjEmD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE7F,SAAS,EAAE,QAAQ,EAAEC,KAAK,GAAG,UAAU,GAAG,SAAS,CAAC;IAEhGiC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACdqM,OAAO,CAAC,CAAC,CAAC;IACVC,eAAe,CAAC,IAAI,CAAC;IACrB1J,gBAAgB,CAACR,OAAO,GAAG,KAAK,CAAC,CAAC;;IAElC,MAAMkH,MAAM,GAAG/I,WAAW,GAAG,QAAQ,GAAG,cAAc;IACtDmD,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE2F,MAAM,CAAC;IACzDiD,UAAU,CAAC,CAAC,EAAE,KAAK,EAAEjD,MAAM,CAAC;EAC9B,CAAC,EAAE,CAAC/I,WAAW,CAAC,CAAC;;EAEjB;EACAtN,SAAS,CAAC,MAAM;IACdsP,eAAe,CAAC,KAAK,CAAC;IACtBlB,gBAAgB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;IAC3BW,kBAAkB,CAAC,KAAK,CAAC;IACzBE,kBAAkB,CAAC;MAAEC,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC,CAAC;IAC5CI,sBAAsB,CAAC,CAAC,CAAC;IACzBE,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;IACxBC,gBAAgB,CAACR,OAAO,GAAG,KAAK,CAAC,CAAC;;IAElC;IACA,IAAItE,SAAS,IAAIC,KAAK,EAAE;MACtBoT,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACrT,SAAS,EAAEC,KAAK,CAAC,CAAC;;EAEtB;EACA9K,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6K,SAAS,IAAI,CAACC,KAAK,EAAE;;IAE1B;IACA,MAAMwZ,QAAQ,GAAGtO,WAAW,CAAC,MAAM;MACjCmG,iBAAiB,CAAC,CAAC;IACrB,CAAC,EAAE,KAAK,CAAC;IAET,OAAO,MAAMpG,aAAa,CAACuO,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACzZ,SAAS,EAAEC,KAAK,CAAC,CAAC;;EAItB;EACA9K,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6K,SAAS,IAAI,CAACC,KAAK,IAAIoB,OAAO,EAAE;IAErC,IAAIqY,kBAAkB,GAAG,IAAI;IAE7B,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B;MACAD,kBAAkB,GAAGvO,WAAW,CAAC,YAAY;QAC3C,IAAI9J,OAAO,IAAI,CAACyB,YAAY,CAACwB,OAAO,EAAE;UACpC4G,aAAa,CAACwO,kBAAkB,CAAC;UACjC;QACF;QAEA,IAAI;UACF;UACA,MAAMjO,WAAW,GAAG,MAAMoD,qBAAqB,CAAC,YAAY,CAAC;UAE7D,IAAIpD,WAAW,EAAE;YACf;YACAP,aAAa,CAACwO,kBAAkB,CAAC;YACjC9T,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;UAC/D;QACF,CAAC,CAAC,OAAOxD,KAAK,EAAE;UACd;UACAuD,OAAO,CAACgU,KAAK,CAAC,mCAAmC,EAAEvX,KAAK,CAACzE,OAAO,CAAC;QACnE;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACb,CAAC;;IAED;IACA,MAAMic,UAAU,GAAGlS,UAAU,CAAC,MAAM;MAClC,IAAI,CAACtG,OAAO,IAAIyB,YAAY,CAACwB,OAAO,EAAE;QACpCsB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAC/D8T,eAAe,CAAC,CAAC;MACnB;IACF,CAAC,EAAE,KAAK,CAAC;IAET,OAAO,MAAM;MACX1O,YAAY,CAAC4O,UAAU,CAAC;MACxB,IAAIH,kBAAkB,EAAE;QACtBxO,aAAa,CAACwO,kBAAkB,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,CAAC1Z,SAAS,EAAEC,KAAK,EAAEoB,OAAO,CAAC,CAAC;;EAE/B;EACAlM,SAAS,CAAC,MAAM;IACd,IAAIuP,mBAAmB,GAAG,CAAC,IAAIA,mBAAmB,GAAG,EAAE,KAAK,CAAC,EAAE;MAC7DkB,OAAO,CAACC,GAAG,CAAC,iCAAiCnB,mBAAmB,EAAE,CAAC;MACnEpM,KAAK,CAACwa,OAAO,CAAC,qCAAqCpO,mBAAmB,qBAAqB,EAAE;QAC3FqO,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,eAAe,EAAE,KAAK;QACtBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC1O,mBAAmB,CAAC,CAAC;;EAEzB;EACAvP,SAAS,CAAC,MAAM;IACd,MAAMoS,SAAS,GAAG6O,iBAAiB,CAAC9R,OAAO;IAC3C,IAAIiD,SAAS,EAAE;MACbA,SAAS,CAACjO,gBAAgB,CAAC,QAAQ,EAAE8d,YAAY,CAAC;IACpD;IACA,OAAO,MAAM;MACX,IAAI7P,SAAS,EAAE;QACbA,SAAS,CAAChO,mBAAmB,CAAC,QAAQ,EAAE6d,YAAY,CAAC;MACvD;IACF,CAAC;EACH,CAAC,EAAE,CAACjB,YAAY,EAAEE,WAAW,CAAC,CAAC;;EAE/B;;EAEA;EACAlhB,SAAS,CAAC,MAAM;IACd,IAAI,CAACuV,UAAU,IAAI,CAACnI,YAAY,EAAE;MAChC;IACF;IAEA,IAAIA,YAAY,EAAE;MAChB;MACA,IAAIuX,qBAAqB,GAAG,IAAI;MAChC,MAAMC,aAAa,GAAGxX,YAAY,CAAC1C,GAAG;MACtC,MAAMma,eAAe,GAAGzX,YAAY,CAAC6K,IAAI;;MAEzC;MACAxK,sBAAsB,CAAC,EAAE,CAAC;MAC1Bc,kBAAkB,CAAC,IAAI,CAAC;MACxBwB,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;;MAE/B;MACAE,iBAAiB,CAAC,IAAI,CAAC;MACvB,IAAIC,kBAAkB,CAACf,OAAO,EAAE;QAC9B2G,YAAY,CAAC5F,kBAAkB,CAACf,OAAO,CAAC;MAC1C;;MAEA;MACAe,kBAAkB,CAACf,OAAO,GAAGqD,UAAU,CAAC,MAAM;QAC5CvC,iBAAiB,CAAC,KAAK,CAAC;QACxBQ,OAAO,CAACC,GAAG,CAAC,cAAcmU,eAAe,0DAA0D,CAAC;MACtG,CAAC,EAAE,IAAI,CAAC;MAERtP,UAAU,CAACuP,IAAI,CAAC,oBAAoB,EAAE;QAAEC,MAAM,EAAE3X,YAAY,CAAC1C;MAAI,CAAC,CAAC;MAEnE,MAAMsa,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC;QACA,MAAM,IAAIjB,OAAO,CAACK,OAAO,IAAI5R,UAAU,CAAC4R,OAAO,EAAE,EAAE,CAAC,CAAC;;QAErD;QACA,IAAI,CAACO,qBAAqB,EAAE;UAC1B;UACAnV,sBAAsB,CAACyG,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;UACxC;QACF;QAEA,IAAI;UAEF;UACA,IAAI,CAAC0O,qBAAqB,EAAE;YAC1B;YACA;UACF;;UAEA;UACA,MAAM7Q,cAAc,GAAG3F,aAAa,CAAC4F,GAAG,CAAC6Q,aAAa,CAAC;UACvD,IAAI9Q,cAAc,IAAIA,cAAc,CAACzB,MAAM,GAAG,CAAC,EAAE;YAE/C;YACA,IAAIsS,qBAAqB,IAAI,CAAAvX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1C,GAAG,MAAKka,aAAa,EAAE;cAChEnX,sBAAsB,CAACqG,cAAc,CAAC;;cAEtC;cACA;cACArF,eAAe,CAAC,CAAC,CAAC;cAClBE,kBAAkB,CAACmF,cAAc,CAACzB,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC;;cAEjD9D,kBAAkB,CAAC,KAAK,CAAC;;cAEzB;cACAiE,UAAU,CAAC,MAAM;gBACf,MAAMJ,SAAS,GAAGtD,oBAAoB,CAACK,OAAO;gBAC9C,IAAIiD,SAAS,IAAI0B,cAAc,CAACzB,MAAM,GAAG,CAAC,EAAE;kBAC1CD,SAAS,CAACE,SAAS,GAAGF,SAAS,CAACG,YAAY;gBAC9C;cACF,CAAC,EAAE,GAAG,CAAC;YACT,CAAC,MAAM;cACL;cACA/C,sBAAsB,CAACyG,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;YAC1C;;YAEA;YACA;YACAzD,UAAU,CAAC,YAAY;cACrB,IAAI;gBACF,IAAI,CAACmS,qBAAqB,EAAE,OAAO,CAAC;;gBAEpC,MAAM5D,IAAI,GAAG,CAAC;gBACd,MAAMxB,QAAQ,GAAG,EAAE;gBACnB,MAAM7K,QAAQ,GAAG,MAAMpT,mBAAmB,CAACuJ,SAAS,EAAE+Z,aAAa,EAAE7D,IAAI,EAAExB,QAAQ,EAAEzU,KAAK,CAAC;gBAE3F,IAAI4J,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,IAAIoM,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,IAAIoM,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAAC0M,IAAI,EAAE;kBAC1G,MAAMiQ,aAAa,GAAGvQ,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAAC0M,IAAI;;kBAEpD;kBACA5G,gBAAgB,CAACwF,SAAS,IAAI;oBAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;oBACnCC,QAAQ,CAACG,GAAG,CAAC4Q,aAAa,EAAEK,aAAa,CAAC;oBAC1C,OAAOpR,QAAQ;kBACjB,CAAC,CAAC;;kBAEF;kBACA,IAAI8Q,qBAAqB,IAAI,CAAAvX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1C,GAAG,MAAKka,aAAa,EAAE;oBAChEnX,sBAAsB,CAACwX,aAAa,CAAC;kBACvC,CAAC,MAAM;oBACL;kBAAA;gBAEJ;cACF,CAAC,CAAC,OAAO/X,KAAK,EAAE;gBACd,IAAI,CAACyX,qBAAqB,EAAE;kBAC1B;gBAAA,CACD,MAAM;kBACL;gBAAA;cAEJ;YACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;YAEV,OAAO,CAAC;UACV;;UAEA;;UAEA;UACA,IAAIA,qBAAqB,EAAE;YACzB;YACApW,kBAAkB,CAAC,IAAI,CAAC;UAC1B;UAEA,MAAMwS,IAAI,GAAG,CAAC;UACd,MAAMxB,QAAQ,GAAG,EAAE;UAEnB,MAAM7K,QAAQ,GAAG,MAAMpT,mBAAmB,CAACuJ,SAAS,EAAE+Z,aAAa,EAAE7D,IAAI,EAAExB,QAAQ,EAAEzU,KAAK,CAAC;;UAE3F;UACA,IAAI,CAAC6Z,qBAAqB,EAAE;YAC1BnV,sBAAsB,CAACyG,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;YACxC;UACF;UAEA,IAAIvB,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,IAAIoM,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,IAAIoM,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAAC0M,IAAI,EAAE;YAC1G,MAAM1M,QAAQ,GAAGoM,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAAC0M,IAAI;;YAE/C;YACA,IAAI2P,qBAAqB,IAAI,CAAAvX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1C,GAAG,MAAKka,aAAa,EAAE;cAAA,IAAAM,sBAAA;cAChEzX,sBAAsB,CAACnF,QAAQ,CAAC;;cAEhC;cACA,MAAMoX,UAAU,GAAG,EAAAwF,sBAAA,GAAAxQ,QAAQ,CAACM,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAACqX,QAAQ,CAAC,CAAC,CAAC,cAAAuF,sBAAA,uBAArCA,sBAAA,CAAuCxF,UAAU,KAAIpX,QAAQ,CAAC+J,MAAM;cACvF5D,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;cACpBE,kBAAkB,CAACrG,QAAQ,CAAC+J,MAAM,GAAGqN,UAAU,CAAC,CAAC,CAAC;;cAElD;cACAtR,gBAAgB,CAACwF,SAAS,IAAI;gBAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;gBACnCC,QAAQ,CAACG,GAAG,CAAC4Q,aAAa,EAAEtc,QAAQ,CAAC;gBACrC,OAAOuL,QAAQ;cACjB,CAAC,CAAC;;cAEF;cACArB,UAAU,CAAC,MAAM;gBACf,MAAMJ,SAAS,GAAGtD,oBAAoB,CAACK,OAAO;gBAC9C,IAAIiD,SAAS,IAAI9J,QAAQ,CAAC+J,MAAM,GAAG,CAAC,EAAE;kBACpCD,SAAS,CAACE,SAAS,GAAGF,SAAS,CAACG,YAAY;gBAC9C;cACF,CAAC,EAAE,GAAG,CAAC;YACT,CAAC,MAAM;cACL;YAAA;UAEJ,CAAC,MAAM;YAEL;YACA,IAAIoS,qBAAqB,IAAI,CAAAvX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1C,GAAG,MAAKka,aAAa,EAAE;cAChEnU,OAAO,CAACC,GAAG,CAAC,iDAAiDmU,eAAe,EAAE,CAAC;cAC/EpX,sBAAsB,CAAC,EAAE,CAAC;;cAE1B;cACAgB,eAAe,CAAC,CAAC,CAAC;cAClBE,kBAAkB,CAAC,KAAK,CAAC;;cAEzB;cACAP,gBAAgB,CAACwF,SAAS,IAAI;gBAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;gBACnCC,QAAQ,CAACG,GAAG,CAAC4Q,aAAa,EAAE,EAAE,CAAC;gBAC/B,OAAO/Q,QAAQ;cACjB,CAAC,CAAC;YACJ,CAAC,MAAM;cACLpD,OAAO,CAACC,GAAG,CAAC,wCAAwCmU,eAAe,mBAAmB,CAAC;YACzF;UACF;QACF,CAAC,CAAC,OAAOhD,GAAG,EAAE;UACZ,IAAI,CAAC8C,qBAAqB,EAAE;YAC1BlU,OAAO,CAACC,GAAG,CAAC,gCAAgCmU,eAAe,EAAE,CAAC;UAChE,CAAC,MAAM;YACLpU,OAAO,CAACvD,KAAK,CAAC,qCAAqC2X,eAAe,GAAG,EAAEhD,GAAG,CAAC;;YAE3E;YACA,IAAI8C,qBAAqB,IAAI,CAAAvX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1C,GAAG,MAAKka,aAAa,EAAE;cAChEnU,OAAO,CAACC,GAAG,CAAC,0DAA0DmU,eAAe,EAAE,CAAC;cACxFpX,sBAAsB,CAAC,EAAE,CAAC;YAC5B,CAAC,MAAM;cACLgD,OAAO,CAACC,GAAG,CAAC,iDAAiDmU,eAAe,mBAAmB,CAAC;YAClG;UACF;QACF,CAAC,SAAS;UACR;UACA,IAAIF,qBAAqB,IAAI,CAAAvX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1C,GAAG,MAAKka,aAAa,EAAE;YAChErW,kBAAkB,CAAC,KAAK,CAAC;UAC3B;QACF;MACF,CAAC;;MAED;MACAgH,UAAU,CAACuP,IAAI,CAAC,oBAAoB,EAAE;QAAEC,MAAM,EAAEH;MAAc,CAAC,CAAC;;MAEhE;MACArP,UAAU,CAACsC,EAAE,CAAC,UAAU,EAAGsN,UAAU,IAAK;QACxC;QACA,IAAI,CAACR,qBAAqB,IAAI,CAAAvX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1C,GAAG,MAAKka,aAAa,EAAE;UACjEpV,sBAAsB,CAACyG,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;UACxC;QACF;;QAEA;QACA1H,kBAAkB,CAAC,KAAK,CAAC;;QAEzB;QACAd,sBAAsB,CAACkG,WAAW,IAAI;UACpC;UACA,MAAMyR,gBAAgB,GAAGzR,WAAW,CAACsB,MAAM,CAACxM,OAAO,IAAI;YACrD;YACA,IAAIA,OAAO,CAACiC,GAAG,KAAKya,UAAU,CAACza,GAAG,EAAE,OAAO,KAAK;YAChD;YACA,IAAIjC,OAAO,CAACgL,YAAY,IAAIhL,OAAO,CAAC4K,IAAI,KAAK8R,UAAU,CAAC9R,IAAI,IAAI5K,OAAO,CAACtD,MAAM,KAAKggB,UAAU,CAAChgB,MAAM,EAAE;cACpG,OAAO,KAAK;YACd;YACA,OAAO,IAAI;UACb,CAAC,CAAC;UACF,OAAO,CAACggB,UAAU,EAAE,GAAGC,gBAAgB,CAAC;QAC1C,CAAC,CAAC;;QAEF;QACAhX,gBAAgB,CAACwF,SAAS,IAAI;UAC5B,MAAMC,QAAQ,GAAG,IAAIxF,GAAG,CAACuF,SAAS,CAAC;UACnC,MAAME,cAAc,GAAGD,QAAQ,CAACE,GAAG,CAAC6Q,aAAa,CAAC,IAAI,EAAE;;UAExD;UACA,MAAMS,sBAAsB,GAAGvR,cAAc,CAACmB,MAAM,CAACxM,OAAO,IAAI;YAC9D,IAAIA,OAAO,CAACiC,GAAG,KAAKya,UAAU,CAACza,GAAG,EAAE,OAAO,KAAK;YAChD,IAAIjC,OAAO,CAACgL,YAAY,IAAIhL,OAAO,CAAC4K,IAAI,KAAK8R,UAAU,CAAC9R,IAAI,IAAI5K,OAAO,CAACtD,MAAM,KAAKggB,UAAU,CAAChgB,MAAM,EAAE;cACpG,OAAO,KAAK;YACd;YACA,OAAO,IAAI;UACb,CAAC,CAAC;UAEF0O,QAAQ,CAACG,GAAG,CAAC4Q,aAAa,EAAE,CAACO,UAAU,EAAE,GAAGE,sBAAsB,CAAC,CAAC;UACpE,OAAOxR,QAAQ;QACjB,CAAC,CAAC;;QAEF;QACA,IAAI,CAACsR,UAAU,CAAChgB,MAAM,EAAE;UACtBsL,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;UAC3F8B,UAAU,CAAC,MAAM;YACfd,cAAc,CAACkT,aAAa,CAAC;UAC/B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QACZ;;QAEA;QACApS,UAAU,CAAC,MAAM;UACf,MAAMJ,SAAS,GAAGtD,oBAAoB,CAACK,OAAO;UAC9C,IAAIiD,SAAS,IAAItC,oBAAoB,EAAE;YACrCsC,SAAS,CAACE,SAAS,GAAGF,SAAS,CAACG,YAAY;UAC9C;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC;MAEFyS,aAAa,CAAC,CAAC;;MAEf;MACA,MAAMM,cAAc,GAAG9S,UAAU,CAAC,MAAM;QACtC,IAAImS,qBAAqB,IAAI,CAAAvX,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1C,GAAG,MAAKka,aAAa,EAAE;UAChErW,kBAAkB,CAAC,KAAK,CAAC;QAC3B;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEX,OAAO,MAAM;QACX;QACAoW,qBAAqB,GAAG,KAAK;QAC7B7O,YAAY,CAACwP,cAAc,CAAC;;QAE5B;QACA,IAAIpV,kBAAkB,CAACf,OAAO,EAAE;UAC9B2G,YAAY,CAAC5F,kBAAkB,CAACf,OAAO,CAAC;QAC1C;QACAc,iBAAiB,CAAC,KAAK,CAAC;QAExBsF,UAAU,CAACuP,IAAI,CAAC,qBAAqB,EAAE;UAAEC,MAAM,EAAEH;QAAc,CAAC,CAAC;QACjErP,UAAU,CAACgE,GAAG,CAAC,UAAU,CAAC;QAC1B;MACF,CAAC;IACH,CAAC,MAAM;MACL;MACAhL,kBAAkB,CAAC,KAAK,CAAC;MACzB;IACF;EACF,CAAC,EAAE,CAACnB,YAAY,EAAEmI,UAAU,CAAC,CAAC;;EAE9B;EACA,MAAMgQ,sBAAsB,GAAGrlB,WAAW,CAAC,MAAOslB,MAAM,IAAK;IAC3D,IAAI,CAACA,MAAM,EAAE;IAEb/U,OAAO,CAACC,GAAG,CAAC,6DAA6D8U,MAAM,EAAE,CAAC;IAClF/U,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;MACjD+U,eAAe,EAAE3Y,KAAK,CAACuF,MAAM;MAC7BxH,SAAS;MACTC,KAAK,EAAEA,KAAK,GAAG,UAAU,GAAG,SAAS;MACrChH,QAAQ;MACRa;IACF,CAAC,CAAC;IAEF,IAAI;MACF;MACA,MAAM+gB,WAAW,GAAG5Y,KAAK,CAAC6Y,IAAI,CAAClU,IAAI,IAAIA,IAAI,CAAC/G,GAAG,KAAK8a,MAAM,CAAC;MAE3D,IAAIE,WAAW,EAAE;QACfjV,OAAO,CAACC,GAAG,CAAC,qCAAqCgV,WAAW,CAACzN,IAAI,EAAE,CAAC;QACpE5K,eAAe,CAACqY,WAAW,CAAC;QAC5B,IAAI5hB,QAAQ,EAAE;UACZqH,iBAAiB,CAAC,IAAI,CAAC;QACzB;QACA;QACA,IAAIgF,YAAY,CAACqV,MAAM,CAAC,IAAIrV,YAAY,CAACqV,MAAM,CAAC,GAAG,CAAC,EAAE;UACpD9T,cAAc,CAAC8T,MAAM,CAAC;QACxB;QACA;MACF;;MAEA;MACA/U,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAElE,IAAI;QACF,MAAMgE,QAAQ,GAAG,MAAMxS,mBAAmB,CAAC2I,SAAS,EAAE2a,MAAM,EAAE1a,KAAK,CAAC;QAEpE,IAAI4J,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACtB,MAAM,KAAK,GAAG,IAAIgB,QAAQ,CAACM,IAAI,CAACvD,IAAI,EAAE;UACvE,MAAMmU,cAAc,GAAGlR,QAAQ,CAACM,IAAI,CAACvD,IAAI;UACzChB,OAAO,CAACC,GAAG,CAAC,6BAA6BkV,cAAc,CAAC3N,IAAI,EAAE,CAAC;;UAE/D;UACAlL,QAAQ,CAACkO,SAAS,IAAI;YACpB,MAAM4K,YAAY,GAAG5K,SAAS,CAAC3C,IAAI,CAAC7G,IAAI,IAAIA,IAAI,CAAC/G,GAAG,KAAK8a,MAAM,CAAC;YAChE,IAAI,CAACK,YAAY,EAAE;cACjB,OAAO,CAACD,cAAc,EAAE,GAAG3K,SAAS,CAAC;YACvC;YACA,OAAOA,SAAS;UAClB,CAAC,CAAC;;UAEF;UACA5N,eAAe,CAACuY,cAAc,CAAC;UAC/B,IAAI9hB,QAAQ,EAAE;YACZqH,iBAAiB,CAAC,IAAI,CAAC;UACzB;;UAEA;UACAhI,KAAK,CAACwa,OAAO,CAAC,2BAA2BiI,cAAc,CAAC3N,IAAI,EAAE,EAAE;YAC9D2F,QAAQ,EAAE,WAAW;YACrBC,SAAS,EAAE,IAAI;YACfC,eAAe,EAAE,KAAK;YACtBC,YAAY,EAAE,IAAI;YAClBC,YAAY,EAAE,IAAI;YAClBC,SAAS,EAAE;UACb,CAAC,CAAC;UAEF;QACF;MACF,CAAC,CAAC,OAAO6H,QAAQ,EAAE;QAAA,IAAAC,kBAAA;QACjBtV,OAAO,CAACmG,IAAI,CAAC,gCAAgC,EAAEkP,QAAQ,CAAC;;QAExD;QACA,IAAI,EAAAC,kBAAA,GAAAD,QAAQ,CAACpR,QAAQ,cAAAqR,kBAAA,uBAAjBA,kBAAA,CAAmBrS,MAAM,MAAK,GAAG,EAAE;UACrCjD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACnD;MACF;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MAEjE,IAAI;QAAA,IAAAsV,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA;QACF;QACA,MAAMC,cAAc,GAAG,MAAM9kB,gBAAgB,CAACwJ,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAEC,KAAK,CAAC;QAE3E,KAAAkb,oBAAA,GAAIG,cAAc,CAACnR,IAAI,cAAAgR,oBAAA,gBAAAC,qBAAA,GAAnBD,oBAAA,CAAqB1d,QAAQ,cAAA2d,qBAAA,gBAAAC,sBAAA,GAA7BD,qBAAA,CAAgC,CAAC,CAAC,cAAAC,sBAAA,eAAlCA,sBAAA,CAAoClR,IAAI,EAAE;UAC5C,MAAMoR,cAAc,GAAGD,cAAc,CAACnR,IAAI,CAAC1M,QAAQ,CAAC,CAAC,CAAC,CAAC0M,IAAI;UAC3D,MAAMqR,sBAAsB,GAAGD,cAAc,CAACT,IAAI,CAAClU,IAAI,IAAIA,IAAI,CAAC/G,GAAG,KAAK8a,MAAM,CAAC;UAE/E,IAAIa,sBAAsB,EAAE;YAC1B5V,OAAO,CAACC,GAAG,CAAC,sCAAsC2V,sBAAsB,CAACpO,IAAI,EAAE,CAAC;;YAEhF;YACAlL,QAAQ,CAACkO,SAAS,IAAI;cACpB,MAAM4K,YAAY,GAAG5K,SAAS,CAAC3C,IAAI,CAAC7G,IAAI,IAAIA,IAAI,CAAC/G,GAAG,KAAK8a,MAAM,CAAC;cAChE,IAAI,CAACK,YAAY,EAAE;gBACjB,OAAO,CAACQ,sBAAsB,EAAE,GAAGpL,SAAS,CAAC;cAC/C;cACA,OAAOA,SAAS;YAClB,CAAC,CAAC;;YAEF;YACA5N,eAAe,CAACgZ,sBAAsB,CAAC;YACvC,IAAIviB,QAAQ,EAAE;cACZqH,iBAAiB,CAAC,IAAI,CAAC;YACzB;YAEAhI,KAAK,CAACwa,OAAO,CAAC,2BAA2B0I,sBAAsB,CAACpO,IAAI,EAAE,EAAE;cACtE2F,QAAQ,EAAE,WAAW;cACrBC,SAAS,EAAE;YACb,CAAC,CAAC;YAEF;UACF;QACF;MACF,CAAC,CAAC,OAAOyI,WAAW,EAAE;QACpB7V,OAAO,CAACmG,IAAI,CAAC,yBAAyB,EAAE0P,WAAW,CAAC;MACtD;;MAEA;MACA7V,OAAO,CAACC,GAAG,CAAC,uDAAuD8U,MAAM,EAAE,CAAC;;MAE5E;MACA,MAAMe,qBAAqB,GAAGviB,MAAM,CAACwiB,OAAO,CAC1C,gCAAgC,GAChC,+BAA+B,GAC/B,+BAA+B,GAC/B,wCAAwC,GACxC,6CAA6C,GAC7C,gEACF,CAAC;MAED,IAAID,qBAAqB,EAAE;QACzB;QACA,MAAME,SAAS,GAAG1b,QAAQ,CAACE,KAAK;QAChC,IAAIwb,SAAS,KAAKA,SAAS,CAACC,IAAI,IAAID,SAAS,CAACE,OAAO,CAAC,EAAE;UACtDlW,OAAO,CAACC,GAAG,CAAC,2DAA2D,EAAE+V,SAAS,CAAC;;UAEnF;UACA,IAAIG,eAAe,GAAGH,SAAS,CAACE,OAAO;UACvC,IAAIC,eAAe,IAAI,CAACA,eAAe,CAAC5N,UAAU,CAAC,IAAI,CAAC,EAAE;YACxD4N,eAAe,GAAG,IAAI,GAAGA,eAAe;UAC1C;UAEA,IAAI;YAAA,IAAAC,oBAAA;YACF,MAAMC,cAAc,GAAG,MAAM3kB,kBAAkB,CAC7C0I,SAAS,EACT+b,eAAe,EACfH,SAAS,CAACC,IAAI,EACd5b,KACF,CAAC;YAED,IAAI,EAAA+b,oBAAA,GAAAC,cAAc,CAAC9R,IAAI,cAAA6R,oBAAA,uBAAnBA,oBAAA,CAAqBnT,MAAM,MAAK,GAAG,IAAIoT,cAAc,CAAC9R,IAAI,CAACvD,IAAI,EAAE;cACnE,MAAMsV,YAAY,GAAGD,cAAc,CAAC9R,IAAI,CAACvD,IAAI;cAC7ChB,OAAO,CAACC,GAAG,CAAC,2BAA2BqW,YAAY,CAAC9O,IAAI,EAAE,CAAC;;cAE3D;cACAlL,QAAQ,CAACkO,SAAS,IAAI,CAAC8L,YAAY,EAAE,GAAG9L,SAAS,CAAC,CAAC;;cAEnD;cACA5N,eAAe,CAAC0Z,YAAY,CAAC;cAC7B,IAAIjjB,QAAQ,EAAE;gBACZqH,iBAAiB,CAAC,IAAI,CAAC;cACzB;cAEAhI,KAAK,CAACwa,OAAO,CAAC,iCAAiCoJ,YAAY,CAAC9O,IAAI,GAAG,EAAE;gBACnE2F,QAAQ,EAAE,WAAW;gBACrBC,SAAS,EAAE;cACb,CAAC,CAAC;cAEF;YACF;UACF,CAAC,CAAC,OAAOmJ,WAAW,EAAE;YACpBvW,OAAO,CAACvD,KAAK,CAAC,gCAAgC,EAAE8Z,WAAW,CAAC;YAC5D7jB,KAAK,CAAC+J,KAAK,CAAC,6EAA6E,EAAE;cACzF0Q,QAAQ,EAAE,WAAW;cACrBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL1a,KAAK,CAAC8jB,IAAI,CAAC,kGAAkG,EAAE;YAC7GrJ,QAAQ,EAAE,WAAW;YACrBC,SAAS,EAAE;UACb,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAI,CAAC0I,qBAAqB,EAAE;QAC1BpjB,KAAK,CAAC+jB,OAAO,CAAC,8FAA8F,EAAE;UAC5GtJ,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE,IAAI;UACfC,eAAe,EAAE,KAAK;UACtBC,YAAY,EAAE,IAAI;UAClBC,YAAY,EAAE,IAAI;UAClBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAO/Q,KAAK,EAAE;MACduD,OAAO,CAACvD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D/J,KAAK,CAAC+J,KAAK,CAAC,4CAA4C,EAAE;QACxD0Q,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC/Q,KAAK,EAAEjC,SAAS,EAAEC,KAAK,EAAEhH,QAAQ,EAAEqM,YAAY,EAAEuB,cAAc,EAAE3G,QAAQ,CAACE,KAAK,CAAC,CAAC;;EAErF;EACAjL,SAAS,CAAC,MAAM;IACd,IAAIgL,eAAe,IAAI8B,KAAK,CAACuF,MAAM,GAAG,CAAC,EAAE;MACvC;MACAkT,sBAAsB,CAACva,eAAe,CAAC;IACzC;EACF,CAAC,EAAE,CAACA,eAAe,EAAE8B,KAAK,CAACuF,MAAM,EAAEkT,sBAAsB,CAAC,CAAC;EAE3D,oBACE9hB,OAAA,CAAAE,SAAA;IAAAwjB,QAAA,eAEE1jB,OAAA,CAACyE,KAAK;MAACC,OAAO,EAAEA,OAAQ;MAAAgf,QAAA,GACrB,CAACjb,OAAO,gBACPzI,OAAA;QAAK2jB,SAAS,EAAC,qBAAqB;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAH,QAAA,eAC5D1jB,OAAA;UAAK2jB,SAAS,EAAC,8DAA8D;UAAAD,QAAA,eAC3E1jB,OAAA;YAAK2jB,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3B1jB,OAAA;cAAK4jB,KAAK,EAAE;gBACVE,eAAe,EAAE,OAAO;gBACxBC,OAAO,EAAE1jB,QAAQ,GAAG,MAAM,GAAG,MAAM;gBACnC2jB,OAAO,EAAE,MAAM;gBACfC,aAAa,EAAE,QAAQ;gBACvBC,GAAG,EAAE7jB,QAAQ,GAAG,MAAM,GAAG,MAAM;gBAC/B8jB,MAAM,EAAE,MAAM;gBACdC,QAAQ,EAAE/jB,QAAQ,GAAG,MAAM,GAAG;cAChC,CAAE;cAAAqjB,QAAA,gBACA1jB,OAAA;gBAAI4jB,KAAK,EAAE;kBACTS,QAAQ,EAAEhkB,QAAQ,GAAG,QAAQ,GAAG,QAAQ;kBACxCikB,SAAS,EAAEjkB,QAAQ,GAAG,QAAQ,GAAG,MAAM;kBACvCkkB,YAAY,EAAElkB,QAAQ,GAAG,MAAM,GAAG;gBACpC,CAAE;gBAAAqjB,QAAA,EAAC;cAAoD;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5D3kB,OAAA;gBAAI4jB,KAAK,EAAE;kBACTgB,WAAW,EAAEvkB,QAAQ,GAAG,MAAM,GAAG,MAAM;kBACvCgkB,QAAQ,EAAEhkB,QAAQ,GAAG,MAAM,GAAG,MAAM;kBACpCwkB,UAAU,EAAE;gBACd,CAAE;gBAAAnB,QAAA,gBACA1jB,OAAA;kBAAI4jB,KAAK,EAAE;oBAAEW,YAAY,EAAElkB,QAAQ,GAAG,KAAK,GAAG;kBAAM,CAAE;kBAAAqjB,QAAA,EAAC;gBAA+B;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3F3kB,OAAA;kBAAI4jB,KAAK,EAAE;oBAAEW,YAAY,EAAElkB,QAAQ,GAAG,KAAK,GAAG;kBAAM,CAAE;kBAAAqjB,QAAA,EAAC;gBAA6D;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzH3kB,OAAA;kBAAI4jB,KAAK,EAAE;oBAAEW,YAAY,EAAElkB,QAAQ,GAAG,KAAK,GAAG;kBAAM,CAAE;kBAAAqjB,QAAA,EAAC;gBAAwE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpI3kB,OAAA;kBAAI4jB,KAAK,EAAE;oBAAEW,YAAY,EAAElkB,QAAQ,GAAG,KAAK,GAAG;kBAAM,CAAE;kBAAAqjB,QAAA,EAAC;gBAA0D;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CAAC,eACL3kB,OAAA;gBAAK4jB,KAAK,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEc,cAAc,EAAE,QAAQ;kBAAEX,MAAM,EAAE;gBAAS,CAAE;gBAAAT,QAAA,eAC1E1jB,OAAA;kBAAK4jB,KAAK,EAAE;oBACVG,OAAO,EAAE1jB,QAAQ,GAAG,MAAM,GAAG,MAAM;oBACnCyjB,eAAe,EAAE,OAAO;oBACxBiB,MAAM,EAAE,mBAAmB;oBAC3BC,YAAY,EAAE,MAAM;oBACpBZ,QAAQ,EAAE,MAAM;oBAChBa,SAAS,EAAE;kBACb,CAAE;kBAAAvB,QAAA,eACA1jB,OAAA;oBAAK4jB,KAAK,EAAE;sBACVC,KAAK,EAAExjB,QAAQ,GAAG,OAAO,GAAG,OAAO;sBACnC6kB,MAAM,EAAE7kB,QAAQ,GAAG,OAAO,GAAG,OAAO;sBACpC2jB,OAAO,EAAE,MAAM;sBACfmB,UAAU,EAAE,QAAQ;sBACpBL,cAAc,EAAE,QAAQ;sBACxBE,YAAY,EAAE,KAAK;sBACnBZ,QAAQ,EAAE,MAAM;sBAChBN,eAAe,EAAE,SAAS;sBAC1BiB,MAAM,EAAE;oBACV,CAAE;oBAAArB,QAAA,EAEEnb,gBAAgB,gBAAGvI,OAAA,CAACrB,OAAO;sBAACygB,IAAI,EAAE,iBAAkB;sBAACuE,SAAS,EAAC;oBAAyB;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,GAEzFtc,SAAS,gBACPrI,OAAA;sBAAK4jB,KAAK,EAAE;wBAAEU,SAAS,EAAE;sBAAS,CAAE;sBAAAZ,QAAA,gBAClC1jB,OAAA;wBACE8N,GAAG,EAAE,GAAGzF,SAAS,EAAG;wBACpB+c,GAAG,EAAC,SAAS;wBACbxB,KAAK,EAAE;0BACLQ,QAAQ,EAAE,MAAM;0BAChBiB,SAAS,EAAE,MAAM;0BACjBxB,KAAK,EAAE,MAAM;0BACbqB,MAAM,EAAE,MAAM;0BACdI,OAAO,EAAE3c,aAAa,GAAG,GAAG,GAAG,CAAC;0BAChC4c,UAAU,EAAE;wBACd,CAAE;wBACFC,MAAM,EAAEA,CAAA,KAAMxY,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAE;wBAC3DwY,OAAO,EAAG5X,CAAC,IAAK;0BACdb,OAAO,CAACvD,KAAK,CAAC,2BAA2B,EAAEoE,CAAC,CAAC;0BAC7Cb,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE5E,SAAS,CAAC;wBAC1C;sBAAE;wBAAAmc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGF3kB,OAAA;wBAAK4jB,KAAK,EAAE;0BAAE8B,SAAS,EAAE;wBAAO,CAAE;wBAAAhC,QAAA,GAC/B,CAAC/a,aAAa,IAAIE,cAAc,GAAG,CAAC,iBACnC7I,OAAA;0BAAK4jB,KAAK,EAAE;4BACV+B,KAAK,EAAE9c,cAAc,IAAI,EAAE,GAAG,SAAS,GAAG,MAAM;4BAChDwb,QAAQ,EAAE,MAAM;4BAChBE,YAAY,EAAE,MAAM;4BACpBP,OAAO,EAAE,MAAM;4BACfmB,UAAU,EAAE,QAAQ;4BACpBL,cAAc,EAAE,QAAQ;4BACxBZ,GAAG,EAAE,KAAK;4BACV0B,UAAU,EAAE/c,cAAc,IAAI,EAAE,GAAG,KAAK,GAAG;0BAC7C,CAAE;0BAAA6a,QAAA,gBACA1jB,OAAA;4BAAA0jB,QAAA,EAAM;0BAAE;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACf3kB,OAAA;4BAAA0jB,QAAA,GAAM,aACO,EAACpe,IAAI,CAACC,KAAK,CAACsD,cAAc,GAAG,EAAE,CAAC,EAAC,GAAC,EAAC,CAACA,cAAc,GAAG,EAAE,EAAEnD,QAAQ,CAAC,CAAC,CAACmgB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAC9Fhd,cAAc,IAAI,EAAE,IAAI,KAAK;0BAAA;4BAAA2b,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CACN,EAEAhc,aAAa,IAAI,CAACJ,gBAAgB,iBACjCvI,OAAA;0BAAK4jB,KAAK,EAAE;4BACV+B,KAAK,EAAE,SAAS;4BAChBtB,QAAQ,EAAE,MAAM;4BAChBE,YAAY,EAAE,MAAM;4BACpBqB,UAAU,EAAE,KAAK;4BACjBtB,SAAS,EAAE;0BACb,CAAE;0BAAAZ,QAAA,GAAC,+BAED,eAAA1jB,OAAA;4BAAK4jB,KAAK,EAAE;8BACVS,QAAQ,EAAE,MAAM;8BAChBsB,KAAK,EAAE,MAAM;8BACbD,SAAS,EAAE,KAAK;8BAChBE,UAAU,EAAE;4BACd,CAAE;4BAAAlC,QAAA,EAAC;0BAEH;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CACN,EAEApc,gBAAgB,iBACfvI,OAAA;0BAAK4jB,KAAK,EAAE;4BACV+B,KAAK,EAAE,SAAS;4BAChBtB,QAAQ,EAAE,MAAM;4BAChBE,YAAY,EAAE,MAAM;4BACpBqB,UAAU,EAAE,KAAK;4BACjBtB,SAAS,EAAE,QAAQ;4BACnBN,OAAO,EAAE,MAAM;4BACfmB,UAAU,EAAE,QAAQ;4BACpBL,cAAc,EAAE,QAAQ;4BACxBZ,GAAG,EAAE;0BACP,CAAE;0BAAAR,QAAA,gBACA1jB,OAAA,CAACrB,OAAO;4BAACygB,IAAI,EAAC,MAAM;4BAACuG,KAAK,EAAC,SAAS;4BAACT,MAAM,EAAE,EAAG;4BAACrB,KAAK,EAAE;0BAAG;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC9D3kB,OAAA;4BAAA0jB,QAAA,EAAM;0BAAuB;4BAAAc,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjC,CACN,EAEA,CAAChc,aAAa,IAAIM,oBAAoB,kBACrCjJ,OAAA;0BACE8lB,OAAO,EAAEA,CAAA,KAAMnT,sBAAsB,CAAC,QAAQ,CAAE;0BAChDoT,QAAQ,EAAExd,gBAAiB;0BAC3Bqb,KAAK,EAAE;4BACLE,eAAe,EAAEvb,gBAAgB,GAAG,SAAS,GAAG,SAAS;4BACzDod,KAAK,EAAE,OAAO;4BACdZ,MAAM,EAAE,MAAM;4BACdC,YAAY,EAAE,KAAK;4BACnBjB,OAAO,EAAE,WAAW;4BACpBiC,MAAM,EAAEzd,gBAAgB,GAAG,aAAa,GAAG,SAAS;4BACpD8b,QAAQ,EAAE,MAAM;4BAChBuB,UAAU,EAAE,KAAK;4BACjBL,UAAU,EAAE,eAAe;4BAC3BvB,OAAO,EAAE,MAAM;4BACfmB,UAAU,EAAE,QAAQ;4BACpBL,cAAc,EAAE,QAAQ;4BACxBZ,GAAG,EAAE,KAAK;4BACVC,MAAM,EAAE;0BACV,CAAE;0BAAAT,QAAA,EAEDnb,gBAAgB,gBACfvI,OAAA,CAAAE,SAAA;4BAAAwjB,QAAA,gBACE1jB,OAAA;8BAAA0jB,QAAA,EAAM;4BAAC;8BAAAc,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACd3kB,OAAA;8BAAA0jB,QAAA,EAAM;4BAAU;8BAAAc,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA,eACvB,CAAC,gBAEH3kB,OAAA,CAAAE,SAAA;4BAAAwjB,QAAA,gBACE1jB,OAAA;8BAAA0jB,QAAA,EAAM;4BAAE;8BAAAc,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eACf3kB,OAAA;8BAAA0jB,QAAA,EAAM;4BAAkB;8BAAAc,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA,eAC/B;wBACH;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACK,CACT;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,GACJ5b,mBAAmB;oBAAA;oBACrB;oBACA/I,OAAA;sBAAK4jB,KAAK,EAAE;wBACVI,OAAO,EAAE,MAAM;wBACfmB,UAAU,EAAE,QAAQ;wBACpBL,cAAc,EAAE,QAAQ;wBACxBb,aAAa,EAAE,QAAQ;wBACvBC,GAAG,EAAE,MAAM;wBACXyB,KAAK,EAAE,MAAM;wBACbrB,SAAS,EAAE,QAAQ;wBACnBP,OAAO,EAAE;sBACX,CAAE;sBAAAL,QAAA,gBACA1jB,OAAA,CAACrB,OAAO;wBAACygB,IAAI,EAAC,MAAM;wBAACuG,KAAK,EAAC,SAAS;wBAACT,MAAM,EAAE,EAAG;wBAACrB,KAAK,EAAE;sBAAG;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC9D3kB,OAAA;wBAAK4jB,KAAK,EAAE;0BAAES,QAAQ,EAAEhkB,QAAQ,GAAG,MAAM,GAAG,MAAM;0BAAEulB,UAAU,EAAE;wBAAM,CAAE;wBAAAlC,QAAA,EAAC;sBAEzE;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN3kB,OAAA;wBAAK4jB,KAAK,EAAE;0BAAES,QAAQ,EAAEhkB,QAAQ,GAAG,MAAM,GAAG,MAAM;0BAAEslB,KAAK,EAAE;wBAAO,CAAE;wBAAAjC,QAAA,EAAC;sBAErE;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;oBAAA;oBAEN;oBACA3kB,OAAA;sBAAK4jB,KAAK,EAAE;wBACVI,OAAO,EAAE,MAAM;wBACfmB,UAAU,EAAE,QAAQ;wBACpBL,cAAc,EAAE,QAAQ;wBACxBb,aAAa,EAAE,QAAQ;wBACvBC,GAAG,EAAE,MAAM;wBACXyB,KAAK,EAAE,MAAM;wBACbrB,SAAS,EAAE,QAAQ;wBACnBP,OAAO,EAAE;sBACX,CAAE;sBAAAL,QAAA,gBACA1jB,OAAA;wBAAK4jB,KAAK,EAAE;0BACVS,QAAQ,EAAE,MAAM;0BAChBsB,KAAK,EAAE,SAAS;0BAChBpB,YAAY,EAAE;wBAChB,CAAE;wBAAAb,QAAA,EAAC;sBAEH;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN3kB,OAAA;wBAAK4jB,KAAK,EAAE;0BAAES,QAAQ,EAAEhkB,QAAQ,GAAG,MAAM,GAAG,MAAM;0BAAEulB,UAAU,EAAE,KAAK;0BAAED,KAAK,EAAE;wBAAO,CAAE;wBAAAjC,QAAA,EAAC;sBAExF;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN3kB,OAAA;wBAAK4jB,KAAK,EAAE;0BACVS,QAAQ,EAAEhkB,QAAQ,GAAG,MAAM,GAAG,MAAM;0BACpCslB,KAAK,EAAE,MAAM;0BACbd,UAAU,EAAE,KAAK;0BACjBN,YAAY,EAAE;wBAChB,CAAE;wBAAAb,QAAA,EAAC;sBAEH;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN3kB,OAAA;wBACE8lB,OAAO,EAAErT,sBAAuB;wBAChCmR,KAAK,EAAE;0BACLE,eAAe,EAAE,SAAS;0BAC1B6B,KAAK,EAAE,OAAO;0BACdZ,MAAM,EAAE,MAAM;0BACdC,YAAY,EAAE,MAAM;0BACpBjB,OAAO,EAAE,WAAW;0BACpBiC,MAAM,EAAE,SAAS;0BACjB3B,QAAQ,EAAE,MAAM;0BAChBuB,UAAU,EAAE,KAAK;0BACjBL,UAAU,EAAE,eAAe;0BAC3BvB,OAAO,EAAE,MAAM;0BACfmB,UAAU,EAAE,QAAQ;0BACpBL,cAAc,EAAE,QAAQ;0BACxBZ,GAAG,EAAE,MAAM;0BACXe,SAAS,EAAE,oCAAoC;0BAC/CgB,QAAQ,EAAE;wBACZ,CAAE;wBACFC,WAAW,EAAGrY,CAAC,IAAK;0BAClBA,CAAC,CAACwB,MAAM,CAACuU,KAAK,CAACE,eAAe,GAAG,SAAS;0BAC1CjW,CAAC,CAACwB,MAAM,CAACuU,KAAK,CAACuC,SAAS,GAAG,kBAAkB;wBAC/C,CAAE;wBACFC,UAAU,EAAGvY,CAAC,IAAK;0BACjBA,CAAC,CAACwB,MAAM,CAACuU,KAAK,CAACE,eAAe,GAAG,SAAS;0BAC1CjW,CAAC,CAACwB,MAAM,CAACuU,KAAK,CAACuC,SAAS,GAAG,eAAe;wBAC5C,CAAE;wBAAAzC,QAAA,gBAEF1jB,OAAA;0BAAM4jB,KAAK,EAAE;4BAAES,QAAQ,EAAE;0BAAO,CAAE;0BAAAX,QAAA,EAAC;wBAAE;0BAAAc,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC5C3kB,OAAA;0BAAA0jB,QAAA,EAAM;wBAAa;0BAAAc,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN3kB,OAAA,CAACF,SAAS;QAAA4jB,QAAA,gBACR1jB,OAAA,CAACY,mBAAmB;UAACgjB,KAAK,EAAE;YAAE8B,SAAS,EAAEnlB,MAAM,CAACC,UAAU,GAAG,IAAI,GAAG,MAAM,GAAG;UAAM,CAAE;UAAAkjB,QAAA,gBAEnF1jB,OAAA,CAAC8B,UAAU;YAACzB,QAAQ,EAAEA,QAAS;YAACa,cAAc,EAAEA,cAAe;YAAAwiB,QAAA,gBAE7D1jB,OAAA,CAACiE,iBAAiB;cAAAyf,QAAA,eAChB1jB,OAAA,CAACmE,gBAAgB;gBAAAuf,QAAA,gBACf1jB,OAAA,CAACqE,SAAS;kBAACyhB,OAAO,EAAEhe,QAAS;kBAAA4b,QAAA,eAC3B1jB,OAAA;oBACE8N,GAAG,EAAE3B,gBAAgB,KAAItF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEwf,QAAQ,CAAC,CAAC,CAAC,KAAItnB,QAAS;oBAC5D8kB,KAAK,EAAC,IAAI;oBACVqB,MAAM,EAAC,IAAI;oBACXE,GAAG,EAAC,8BAA2B;oBAC/BK,OAAO,EAAG5X,CAAC,IAAK;sBACd;sBACA,IAAIA,CAAC,CAACwB,MAAM,CAACvB,GAAG,KAAK/O,QAAQ,EAAE;wBAC7BiO,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;wBAC1EY,CAAC,CAACwB,MAAM,CAACvB,GAAG,GAAG/O,QAAQ;wBACvB0b,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;sBAC/B;oBACF,CAAE;oBACF6L,aAAa,EAAEA,CAAA,KAAM;sBACnB;sBACAtZ,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;sBAChEwN,qBAAqB,CAAC,IAAI,CAAC;oBAC7B;kBAAE;oBAAA+J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ3kB,OAAA,CAACuE,OAAO;kBAACgiB,GAAG,EAAEve,OAAQ;kBAAA0b,QAAA,gBACpB1jB,OAAA,CAACd,UAAU;oBAACsnB,IAAI,EAAC,KAAK;oBAAC,cAAW;kBAAuB;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5D3kB,OAAA,CAACb,QAAQ;oBAACqnB,IAAI,EAAC,KAAK;oBAAC,cAAW;kBAAgC;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnE3kB,OAAA,CAACZ,QAAQ;oBACPonB,IAAI,EAAC,KAAK;oBACV,cAAW,2BAAwB;oBACnCV,OAAO,EAAEA,CAAA,KAAMle,aAAa,CAAC,IAAI;kBAAE;oBAAA4c,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEpB3kB,OAAA,CAACiC,WAAW;cAAC0hB,SAAS,EAAEhc,UAAU,GAAG,QAAQ,GAAG,EAAG;cAAA+b,QAAA,eACjD1jB,OAAA,CAACmC,iBAAiB;gBAAAuhB,QAAA,EACfxmB,WAAW,CAACwT,GAAG,CAAC,CAAC;kBAAEiE,EAAE;kBAAE8R;gBAAO,CAAC,kBAC9BzmB,OAAA,CAACsC,UAAU;kBAETwjB,OAAO,EAAEW,MAAM,KAAK,aAAa,GAAGvH,gBAAgB,GAAGpX,QAAS;kBAChE6b,SAAS,EAAE8C,MAAM,KAAK,aAAa,GAAG,EAAE,GAAG,EAAG,CAAC;kBAAA;kBAAA/C,QAAA,EAE9C+C;gBAAM,GAJF9R,EAAE;kBAAA6P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKG,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACe;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEd3kB,OAAA,CAAC3C,SAAS;cAACwM,WAAW,EAAEA,WAAY;cAACC,cAAc,EAAEA;YAAe;cAAA0a,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAGtErZ,eAAe,iBACdtL,OAAA;cAAK4jB,KAAK,EAAE;gBACVG,OAAO,EAAE,UAAU;gBACnBD,eAAe,EAAE,SAAS;gBAC1B4C,UAAU,EAAE,mBAAmB;gBAC/BrC,QAAQ,EAAE,MAAM;gBAChBsB,KAAK,EAAE,MAAM;gBACb3B,OAAO,EAAE,MAAM;gBACfmB,UAAU,EAAE,QAAQ;gBACpBjB,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBACA1jB,OAAA,CAACrB,OAAO;gBAACygB,IAAI,EAAC,MAAM;gBAACuG,KAAK,EAAC,SAAS;gBAACT,MAAM,EAAE,EAAG;gBAACrB,KAAK,EAAE;cAAG;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9D3kB,OAAA;gBAAA0jB,QAAA,GAAM,kCACyB,EAAClY,eAAe,CAACE,OAAO,EAAC,GAAC,EAACF,eAAe,CAACG,KAAK,EAAC,GAChF;cAAA;gBAAA6Y,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,eAGD3kB,OAAA;cAAK2jB,SAAS,EAAC,wBAAwB;cAAC4C,GAAG,EAAE/I,iBAAkB;cAAAkG,QAAA,GAE5Dna,OAAO,IAAIF,KAAK,CAACuF,MAAM,KAAK,CAAC,iBAC5B5O,OAAA;gBAAK4jB,KAAK,EAAE;kBACVI,OAAO,EAAE,MAAM;kBACfC,aAAa,EAAE,QAAQ;kBACvBkB,UAAU,EAAE,QAAQ;kBACpBL,cAAc,EAAE,QAAQ;kBACxBf,OAAO,EAAE,WAAW;kBACpB4B,KAAK,EAAE;gBACT,CAAE;gBAAAjC,QAAA,gBACA1jB,OAAA,CAACrB,OAAO;kBAACygB,IAAI,EAAC,MAAM;kBAACuG,KAAK,EAAC,SAAS;kBAACT,MAAM,EAAE,EAAG;kBAACrB,KAAK,EAAE;gBAAG;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9D3kB,OAAA;kBAAG4jB,KAAK,EAAE;oBAAE8B,SAAS,EAAE,MAAM;oBAAErB,QAAQ,EAAE;kBAAO,CAAE;kBAAAX,QAAA,EAAC;gBAAuB;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CACN,EAEAtb,KAAK,CAACqH,GAAG,CAAE1C,IAAI;gBAAA,IAAA2Y,mBAAA,EAAAC,qBAAA;gBAAA,oBACd5mB,OAAA,CAACsD,mBAAmB;kBAClBC,UAAU,EAAEyK,IAAI,CAAC/G,GAAG,MAAK0C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1C,GAAG,CAAC;kBAC3C6e,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIzlB,QAAQ,EAAE;sBACZ0N,sBAAsB,CAACC,IAAI,CAAC;oBAC9B,CAAC,MAAM;sBACLpE,eAAe,CAACoE,IAAI,CAAC;sBACrB;sBACA,IAAItB,YAAY,CAACsB,IAAI,CAAC/G,GAAG,CAAC,IAAIyF,YAAY,CAACsB,IAAI,CAAC/G,GAAG,CAAC,GAAG,CAAC,EAAE;wBACxDgH,cAAc,CAACD,IAAI,CAAC/G,GAAG,CAAC;sBAC1B;oBACF;kBACF,CAAE;kBAAAyc,QAAA,gBACF1jB,OAAA,CAACyD,wBAAwB;oBAAAigB,QAAA,eACvB1jB,OAAA,CAACJ,WAAW;sBAACinB,KAAK,EAAEna,YAAY,CAACsB,IAAI,CAAC/G,GAAG,CAAC,IAAI,CAAE;sBAAAyc,QAAA,eAC9C1jB,OAAA;wBACE8N,GAAG,EAAE,EAAA6Y,mBAAA,GAAA3Y,IAAI,CAACiJ,YAAY,cAAA0P,mBAAA,wBAAAC,qBAAA,GAAjBD,mBAAA,CAAmBzP,QAAQ,cAAA0P,qBAAA,uBAA3BA,qBAAA,CAA6BxP,eAAe,KAAIrY,QAAS;wBAC9D8kB,KAAK,EAAC,IAAI;wBACVuB,GAAG,EAAC,8BAA2B;wBAC/BK,OAAO,EAAG5X,CAAC,IAAK;0BACd;0BACA,IAAIA,CAAC,CAACwB,MAAM,CAACvB,GAAG,KAAK/O,QAAQ,EAAE;4BAC7BiO,OAAO,CAACC,GAAG,CAAC,4CAA4Ce,IAAI,CAACwG,IAAI,oBAAoB,CAAC;4BACtF3G,CAAC,CAACwB,MAAM,CAACvB,GAAG,GAAG/O,QAAQ;4BACvByX,mBAAmB,CAACxI,IAAI,CAAC;0BAC3B;wBACF,CAAE;wBACFsY,aAAa,EAAEA,CAAA,KAAM;0BACnB;0BACAtZ,OAAO,CAACC,GAAG,CAAC,oDAAoDe,IAAI,CAACwG,IAAI,EAAE,CAAC;0BAC5EgC,mBAAmB,CAACxI,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;wBACnC,CAAE;wBACF8Y,KAAK,EAAC;sBAA4C;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACU,CAAC,eAC3B3kB,OAAA,CAAC+D,kBAAkB;oBAAA2f,QAAA,gBACjB1jB,OAAA,CAAC2D,kBAAkB;sBAAA+f,QAAA,gBACjB1jB,OAAA;wBACEsmB,aAAa,EAAEA,CAAA,KAAM;0BACnBtZ,OAAO,CAACC,GAAG,CAAC,yCAAyCe,IAAI,CAACwG,IAAI,mBAAmB,CAAC;0BAClF2E,kBAAkB,CAACnL,IAAI,CAAC/G,GAAG,EAAE+G,IAAI,CAACwG,IAAI,CAAC;wBACzC,CAAE;wBACFsS,KAAK,EAAC,0DAAoD;wBAC1DlD,KAAK,EAAE;0BAAEoC,MAAM,EAAE,SAAS;0BAAEhC,OAAO,EAAE,MAAM;0BAAEmB,UAAU,EAAE,QAAQ;0BAAEjB,GAAG,EAAE;wBAAM,CAAE;wBAAAR,QAAA,GAE/E1V,IAAI,CAACwG,IAAI,EACTmE,cAAc,CAAC3K,IAAI,CAACwG,IAAI,EAAExG,IAAI,CAACyB,aAAa,CAAC,iBAC5CzP,OAAA;0BACE8lB,OAAO,EAAGjY,CAAC,IAAK;4BACdA,CAAC,CAACO,eAAe,CAAC,CAAC;4BACnB+K,kBAAkB,CAACnL,IAAI,CAAC/G,GAAG,EAAE+G,IAAI,CAACwG,IAAI,CAAC;0BACzC,CAAE;0BACFoP,KAAK,EAAE;4BACLmD,UAAU,EAAE,aAAa;4BACzBhC,MAAM,EAAE,MAAM;4BACdiB,MAAM,EAAE,SAAS;4BACjBL,KAAK,EAAE,SAAS;4BAChBtB,QAAQ,EAAE,QAAQ;4BAClBN,OAAO,EAAE,GAAG;4BACZI,MAAM,EAAE,GAAG;4BACXmB,OAAO,EAAE,GAAG;4BACZC,UAAU,EAAE;0BACd,CAAE;0BACFW,WAAW,EAAGrY,CAAC,IAAKA,CAAC,CAACwB,MAAM,CAACuU,KAAK,CAAC0B,OAAO,GAAG,CAAE;0BAC/Cc,UAAU,EAAGvY,CAAC,IAAKA,CAAC,CAACwB,MAAM,CAACuU,KAAK,CAAC0B,OAAO,GAAG,GAAI;0BAChDwB,KAAK,EAAC,uEAAoE;0BAAApD,QAAA,EAC3E;wBAED;0BAAAc,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CACT;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC,eACP3kB,OAAA;wBAAA0jB,QAAA,EAAI1V,IAAI,CAAChJ,OAAO,GAAG4Z,UAAU,CAAC5Q,IAAI,CAAChJ,OAAO,CAAC8K,WAAW,IAAI9B,IAAI,CAAChJ,OAAO,CAACE,SAAS,CAAC,GAAG;sBAAE;wBAAAsf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CAAC,eACrB3kB,OAAA,CAAC6D,cAAc;sBAAA6f,QAAA,GACZ1V,IAAI,CAAChJ,OAAO,IAAIgJ,IAAI,CAAChJ,OAAO,CAACtD,MAAM,iBAAI1B,OAAA,CAACf,SAAS;wBAAAulB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACrD3kB,OAAA;wBAAA0jB,QAAA,EAAI1V,IAAI,CAAChJ,OAAO,GAAIgJ,IAAI,CAAChJ,OAAO,CAACA,OAAO,IAAIgJ,IAAI,CAAChJ,OAAO,CAAC4K,IAAI,IAAI,eAAe,GAAI;sBAAe;wBAAA4U,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GA9EG3W,IAAI,CAAC/G,GAAG;kBAAAud,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+Eb,CAAC;cAAA,CACvB,CAAC,EAGDlH,WAAW,iBAAIzd,OAAA;gBAAG2jB,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,EAAC;cAA+B;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEI,CAAC,EAEXhb,YAAY,KAAK,CAACtJ,QAAQ,IAAIa,cAAc,CAAC,gBAC7ClB,OAAA,CAACe,wBAAwB;YACvBV,QAAQ,EAAEA,QAAS;YACnBa,cAAc,EAAEA,cAAe;YAC/BD,gBAAgB,EAAEA,gBAAiB;YAAAyiB,QAAA,gBAGnC1jB,OAAA,CAACyC,MAAM;cAAAihB,QAAA,gBACL1jB,OAAA,CAACiD,gBAAgB;gBAAC6iB,OAAO,EAAE5X,gBAAiB;gBAAAwV,QAAA,eAC1C1jB,OAAA,CAACX,QAAQ;kBAAAmlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACnB3kB,OAAA,CAAC4C,YAAY;gBAACkjB,OAAO,EAAE3X,iBAAkB;gBAACyV,KAAK,EAAE;kBAAEoC,MAAM,EAAE;gBAAU,CAAE;gBAAAtC,QAAA,eACrE1jB,OAAA;kBACE8N,GAAG,EAAE,EAAA7H,qBAAA,GAAA0D,YAAY,CAACsN,YAAY,cAAAhR,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BiR,QAAQ,cAAAhR,sBAAA,uBAAnCA,sBAAA,CAAqCkR,eAAe,KAAIrY,QAAS;kBACtE8kB,KAAK,EAAC,IAAI;kBACVuB,GAAG,EAAC,8BAA2B;kBAC/BK,OAAO,EAAG5X,CAAC,IAAK;oBACd;oBACA,IAAIA,CAAC,CAACwB,MAAM,CAACvB,GAAG,KAAK/O,QAAQ,EAAE;sBAC7BiO,OAAO,CAACC,GAAG,CAAC,4CAA4CtD,YAAY,CAAC6K,IAAI,oBAAoB,CAAC;sBAC9F3G,CAAC,CAACwB,MAAM,CAACvB,GAAG,GAAG/O,QAAQ;sBACvByX,mBAAmB,CAAC7M,YAAY,CAAC;oBACnC;kBACF,CAAE;kBACF2c,aAAa,EAAGzY,CAAC,IAAK;oBACpB;oBACAA,CAAC,CAACO,eAAe,CAAC,CAAC;oBACnBpB,OAAO,CAACC,GAAG,CAAC,oDAAoDtD,YAAY,CAAC6K,IAAI,EAAE,CAAC;oBACpFgC,mBAAmB,CAAC7M,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;kBAC3C,CAAE;kBACFmd,KAAK,EAAC;gBAA4C;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eACf3kB,OAAA,CAAC8C,WAAW;gBAAA4gB,QAAA,GACT/Z,YAAY,CAAC6K,IAAI,eAClBxU,OAAA;kBACE8lB,OAAO,EAAEA,CAAA,KAAM3M,kBAAkB,CAACxP,YAAY,CAAC1C,GAAG,EAAE0C,YAAY,CAAC6K,IAAI,CAAE;kBACvEoP,KAAK,EAAE;oBACLmD,UAAU,EAAE,aAAa;oBACzBhC,MAAM,EAAE,MAAM;oBACdiC,UAAU,EAAE,MAAM;oBAClBhB,MAAM,EAAE,SAAS;oBACjBL,KAAK,EAAE,MAAM;oBACbtB,QAAQ,EAAE,QAAQ;oBAClBN,OAAO,EAAE,KAAK;oBACdiB,YAAY,EAAE,KAAK;oBACnBO,UAAU,EAAE;kBACd,CAAE;kBACFW,WAAW,EAAGrY,CAAC,IAAKA,CAAC,CAACwB,MAAM,CAACuU,KAAK,CAAC+B,KAAK,GAAG,SAAU;kBACrDS,UAAU,EAAGvY,CAAC,IAAKA,CAAC,CAACwB,MAAM,CAACuU,KAAK,CAAC+B,KAAK,GAAG,MAAO;kBACjDmB,KAAK,EAAC,wCAAkC;kBAAApD,QAAA,EACzC;gBAED;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAET3kB,OAAA,CAACoB,cAAc;cACbmlB,GAAG,EAAElb,oBAAqB;cAC1B4b,QAAQ,EAAEpK,oBAAqB;cAAA6G,QAAA,EAE9B7Y,eAAe,gBACd7K,OAAA;gBAAK4jB,KAAK,EAAE;kBACVI,OAAO,EAAE,MAAM;kBACfC,aAAa,EAAE,QAAQ;kBACvBkB,UAAU,EAAE,QAAQ;kBACpBL,cAAc,EAAE,QAAQ;kBACxBI,MAAM,EAAE,MAAM;kBACdS,KAAK,EAAE,MAAM;kBACbzB,GAAG,EAAE;gBACP,CAAE;gBAAAR,QAAA,gBACA1jB,OAAA,CAACrB,OAAO;kBAACygB,IAAI,EAAC,MAAM;kBAACuG,KAAK,EAAC,SAAS;kBAACT,MAAM,EAAE,EAAG;kBAACrB,KAAK,EAAE;gBAAG;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9D3kB,OAAA;kBAAG4jB,KAAK,EAAE;oBAAES,QAAQ,EAAE,MAAM;oBAAEC,SAAS,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,GAAC,0BAC3B,EAAC/Z,YAAY,CAAC6K,IAAI,EAAC,KAC7C;gBAAA;kBAAAgQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,GACJuC,MAAM,CAACC,IAAI,CAAC1Y,eAAe,CAAC,CAACG,MAAM,GAAG,CAAC,gBACzC5O,OAAA,CAAAE,SAAA;gBAAAwjB,QAAA,GAEGvY,mBAAmB,iBAClBnL,OAAA;kBAAK4jB,KAAK,EAAE;oBACVI,OAAO,EAAE,MAAM;oBACfc,cAAc,EAAE,QAAQ;oBACxBK,UAAU,EAAE,QAAQ;oBACpBpB,OAAO,EAAE,MAAM;oBACf4B,KAAK,EAAE,MAAM;oBACbtB,QAAQ,EAAE,MAAM;oBAChBH,GAAG,EAAE;kBACP,CAAE;kBAAAR,QAAA,gBACA1jB,OAAA,CAACrB,OAAO;oBAACygB,IAAI,EAAC,MAAM;oBAACuG,KAAK,EAAC,SAAS;oBAACT,MAAM,EAAE,EAAG;oBAACrB,KAAK,EAAE;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9D3kB,OAAA;oBAAA0jB,QAAA,EAAM;kBAAkC;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CACN,EAGA,CAAC1Z,eAAe,IAAIlB,mBAAmB,CAAC6E,MAAM,GAAG,EAAE,iBAClD5O,OAAA;kBAAK4jB,KAAK,EAAE;oBACVI,OAAO,EAAE,MAAM;oBACfc,cAAc,EAAE,QAAQ;oBACxBK,UAAU,EAAE,QAAQ;oBACpBpB,OAAO,EAAE,MAAM;oBACf4B,KAAK,EAAE,MAAM;oBACbtB,QAAQ,EAAE,MAAM;oBAChB+C,SAAS,EAAE;kBACb,CAAE;kBAAA1D,QAAA,EAAC;gBAEH;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,EAEAuC,MAAM,CAACC,IAAI,CAAC1Y,eAAe,CAAC,CAACiC,GAAG,CAAEzL,IAAI,iBACrCjF,OAAA,CAAC3D,KAAK,CAAC4D,QAAQ;kBAAAyjB,QAAA,gBACb1jB,OAAA,CAACsB,QAAQ;oBAAAoiB,QAAA,EAAE7kB,MAAM,CAACoG,IAAI,CAAC,CAACoiB,QAAQ,CAAC,IAAI,EAAE;sBACrCC,OAAO,EAAE,QAAQ;sBACjBC,OAAO,EAAE,SAAS;sBAClBC,QAAQ,EAAE,MAAM;sBAChBC,QAAQ,EAAE;oBACZ,CAAC;kBAAC;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,EACblW,eAAe,CAACxJ,IAAI,CAAC,CAACyL,GAAG,CAAE1L,OAAO,IAAK;oBACtC,MAAM;sBAAEiC,GAAG;sBAAE2I,IAAI;sBAAE1K,SAAS;sBAAExD,MAAM;sBAAEsO,YAAY;sBAAEC;oBAAO,CAAC,GAAGjL,OAAO;oBACtE,oBACEhF,OAAA,CAACyB,OAAO;sBAAWC,MAAM,EAAEA,MAAO;sBAAAgiB,QAAA,gBAChC1jB,OAAA;wBAAA0jB,QAAA,EAAI9T;sBAAI;wBAAA4U,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACb3kB,OAAA;wBAAM4jB,KAAK,EAAE;0BAAEI,OAAO,EAAE,MAAM;0BAAEmB,UAAU,EAAE,QAAQ;0BAAEjB,GAAG,EAAE;wBAAM,CAAE;wBAAAR,QAAA,GAChE7kB,MAAM,CAACqG,SAAS,CAAC,CAACC,MAAM,CAAC,OAAO,CAAC,EACjCzD,MAAM,IAAIsO,YAAY,IAAIC,MAAM,KAAK,SAAS,iBAC7CjQ,OAAA;0BAAM8mB,KAAK,EAAC,aAAa;0BAAClD,KAAK,EAAE;4BAAE+B,KAAK,EAAE,MAAM;4BAAEtB,QAAQ,EAAE;0BAAO,CAAE;0BAAAX,QAAA,EAAC;wBAAC;0BAAAc,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAC9E,EACAjjB,MAAM,IAAIsO,YAAY,IAAIC,MAAM,KAAK,MAAM,iBAC1CjQ,OAAA;0BAAM8mB,KAAK,EAAC,SAAS;0BAAClD,KAAK,EAAE;4BAAE+B,KAAK,EAAE,SAAS;4BAAEtB,QAAQ,EAAE;0BAAO,CAAE;0BAAAX,QAAA,EAAC;wBAAC;0BAAAc,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAC7E,EACAjjB,MAAM,IAAI,CAACsO,YAAY,iBACtBhQ,OAAA;0BAAM8mB,KAAK,EAAC,UAAU;0BAAClD,KAAK,EAAE;4BAAE+B,KAAK,EAAE,SAAS;4BAAEtB,QAAQ,EAAE;0BAAO,CAAE;0BAAAX,QAAA,EAAC;wBAAE;0BAAAc,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAC/E;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA,GAbK1d,GAAG;sBAAAud,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAcR,CAAC;kBAEd,CAAC,CAAC;gBAAA,GA1BiB1f,IAAI;kBAAAuf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2BT,CACjB,CAAC,eACF3kB,OAAA;kBAAKumB,GAAG,EAAE7X;gBAAc;kBAAA8V,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,eAC3B,CAAC,gBAEH3kB,OAAA;gBAAK4jB,KAAK,EAAE;kBACVI,OAAO,EAAE,MAAM;kBACfC,aAAa,EAAE,QAAQ;kBACvBkB,UAAU,EAAE,QAAQ;kBACpBL,cAAc,EAAE,QAAQ;kBACxBI,MAAM,EAAE,MAAM;kBACdS,KAAK,EAAE,MAAM;kBACbrB,SAAS,EAAE,QAAQ;kBACnBP,OAAO,EAAE;gBACX,CAAE;gBAAAL,QAAA,gBACA1jB,OAAA;kBAAK4jB,KAAK,EAAE;oBACVS,QAAQ,EAAE,MAAM;oBAChBE,YAAY,EAAE,MAAM;oBACpBe,OAAO,EAAE;kBACX,CAAE;kBAAA5B,QAAA,EAAC;gBAEH;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN3kB,OAAA;kBAAG4jB,KAAK,EAAE;oBACRS,QAAQ,EAAE,MAAM;oBAChBE,YAAY,EAAE,KAAK;oBACnBqB,UAAU,EAAE,KAAK;oBACjBD,KAAK,EAAE;kBACT,CAAE;kBAAAjC,QAAA,EAAC;gBAEH;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ3kB,OAAA;kBAAG4jB,KAAK,EAAE;oBACRS,QAAQ,EAAE,MAAM;oBAChBQ,UAAU,EAAE,KAAK;oBACjBT,QAAQ,EAAE;kBACZ,CAAE;kBAAAV,QAAA,GAAC,kCAC4B,eAAA1jB,OAAA;oBAAA0jB,QAAA,EAAS/Z,YAAY,CAAC6K;kBAAI;oBAAAgQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,yCAEnE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACa,CAAC,eAEjB3kB,OAAA,CAACoD,WAAW;cAAAsgB,QAAA,eACV1jB,OAAA;gBAAM0nB,QAAQ,EAAEpY,YAAa;gBAACsU,KAAK,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEmB,UAAU,EAAE,QAAQ;kBAAEjB,GAAG,EAAE;gBAAG,CAAE;gBAAAR,QAAA,gBACtF1jB,OAAA,CAACL,KAAK;kBACJyf,IAAI,EAAC,MAAM;kBACXuI,WAAW,EAAC,qBAAqB;kBACjCC,QAAQ,EAAEA,CAAC;oBAAEvY;kBAAO,CAAC,KAAKpH,UAAU,CAACoH,MAAM,CAACwY,KAAK,CAAE;kBACnDA,KAAK,EAAE7iB,OAAQ;kBACf4e,KAAK,EAAE;oBAAES,QAAQ,EAAEhkB,QAAQ,GAAG,MAAM,GAAG;kBAAO;gBAAE;kBAAAmkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eAEF3kB,OAAA;kBACEof,IAAI,EAAC,QAAQ;kBACb2G,QAAQ,EAAE5d,YAAY,IAAI,CAACnD,OAAO,CAACwK,IAAI,CAAC,CAAE;kBAC1CoU,KAAK,EAAE;oBACLmD,UAAU,EAAE,aAAa;oBACzBhC,MAAM,EAAE,MAAM;oBACdiB,MAAM,EAAE7d,YAAY,IAAI,CAACnD,OAAO,CAACwK,IAAI,CAAC,CAAC,GAAG,aAAa,GAAG,SAAS;oBACnEmW,KAAK,EAAExd,YAAY,IAAI,CAACnD,OAAO,CAACwK,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,SAAS;oBAAE;oBAC7D6U,QAAQ,EAAEhkB,QAAQ,GAAG,QAAQ,GAAG,MAAM;oBACtCilB,OAAO,EAAEnd,YAAY,IAAI,CAACnD,OAAO,CAACwK,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG;kBACnD,CAAE;kBAAAkU,QAAA,EAEDvb,YAAY,gBACXnI,OAAA,CAACrB,OAAO;oBAACygB,IAAI,EAAC,MAAM;oBAACuG,KAAK,EAAC,SAAS;oBAACT,MAAM,EAAE,EAAG;oBAACrB,KAAK,EAAE;kBAAG;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE9D3kB,OAAA,CAACT,MAAM;oBAAAilB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACV;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGT3kB,OAAA;kBACEof,IAAI,EAAC,QAAQ;kBACb0G,OAAO,EAAEA,CAAA,KAAMtG,eAAe,CAAC7V,YAAY,CAAC1C,GAAG,CAAE;kBACjD2c,KAAK,EAAE;oBACLmD,UAAU,EAAE,aAAa;oBACzBhC,MAAM,EAAEza,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;oBAC9D0b,MAAM,EAAE,SAAS;oBACjBhC,OAAO,EAAE,MAAM;oBACfmB,UAAU,EAAE,QAAQ;oBACpBjB,GAAG,EAAE,CAAC;oBACNyB,KAAK,EAAErb,UAAU,GAAG,SAAS,GAAG,SAAS;oBAAE;oBAC3C+Z,QAAQ,EAAEhkB,QAAQ,GAAG,MAAM,GAAG,MAAM;oBACpCulB,UAAU,EAAE,KAAK;oBACjBK,QAAQ,EAAE5lB,QAAQ,GAAG,GAAG,GAAG,GAAG;oBAC9BykB,cAAc,EAAE,QAAQ;oBACxBE,YAAY,EAAE,CAAC;oBACfjB,OAAO,EAAE1jB,QAAQ,GAAG,KAAK,GAAG;kBAC9B,CAAE;kBAAAqjB,QAAA,GAEDpZ,UAAU,gBAAGtK,OAAA,CAACR,aAAa;oBAACokB,KAAK,EAAE;sBAAES,QAAQ,EAAE;oBAAG;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG3kB,OAAA,CAACP,YAAY;oBAACmkB,KAAK,EAAE;sBAAES,QAAQ,EAAE;oBAAG;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpG3kB,OAAA;oBAAA0jB,QAAA,EAAOpZ,UAAU,GAAG,cAAc,GAAG;kBAAc;oBAAAka,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAEd3kB,OAAA,CAACzC,WAAW;cAACsK,MAAM,EAAE2C,aAAc;cAACsd,OAAO,EAAEA,CAAA,KAAMrd,gBAAgB,CAAC,KAAK,CAAE;cAACjE,IAAI,EAAEmD;YAAa;cAAA6a,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,GAE3B,CAAC,CAACtkB,QAAQ,IAAI,CAACa,cAAc,kBAAKlB,OAAA,CAACe,wBAAwB;YACzDE,gBAAgB,EAAEA,gBAAiB;YACnC2iB,KAAK,EAAE;cACLmD,UAAU,EAAE,OAAO;cACnBjC,cAAc,EAAE,QAAQ;cACxBd,OAAO,EAAE3jB,QAAQ,GAAG,MAAM,GAAG,MAAM;cACnC4jB,aAAa,EAAE,QAAQ;cACvBkB,UAAU,EAAE;YACd,CAAE;YAAAzB,QAAA,gBAEF1jB,OAAA,CAACV,UAAU;cAACskB,KAAK,EAAE;gBAAES,QAAQ,EAAE,OAAO;gBAAEsB,KAAK,EAAE;cAAsB;YAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1E3kB,OAAA,CAAC4B,IAAI;cAACgiB,KAAK,EAAE;gBAAEmD,UAAU,EAAE;cAAQ,CAAE;cAAArD,QAAA,EAAC;YAA+C;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGV,CAAC,EAErB9c,MAAM,iBACL7H,OAAA,CAAC1C,CAAC,CAACyqB,OAAO;UAAArE,QAAA,gBACR1jB,OAAA,CAAC1C,CAAC,CAACmF,MAAM;YAAAihB,QAAA,eACP1jB,OAAA,CAAC1C,CAAC,CAAC0qB,IAAI;cAAAtE,QAAA,gBACL1jB,OAAA,CAACX,QAAQ;gBAACymB,OAAO,EAAE/d;cAAU;gBAAAyc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC3kB,OAAA;gBAAA0jB,QAAA,EAAI;cAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEX3kB,OAAA,CAAC1C,CAAC,CAAC+G,SAAS;YAAAqf,QAAA,eACV1jB,OAAA;cACE8N,GAAG,EAAE3B,gBAAgB,KAAItF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEwf,QAAQ,CAAC,CAAC,CAAC,KAAItnB,QAAS;cAC5D8kB,KAAK,EAAC,KAAK;cACXqB,MAAM,EAAC,KAAK;cACZE,GAAG,EAAC,8BAA2B;cAC/BK,OAAO,EAAG5X,CAAC,IAAK;gBACd;gBACA,IAAIA,CAAC,CAACwB,MAAM,CAACvB,GAAG,KAAK/O,QAAQ,EAAE;kBAC7BiO,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;kBACnFY,CAAC,CAACwB,MAAM,CAACvB,GAAG,GAAG/O,QAAQ;kBACvB0b,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/B;cACF,CAAE;cACF6L,aAAa,EAAEA,CAAA,KAAM;gBACnB;gBACAtZ,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;gBACzEwN,qBAAqB,CAAC,IAAI,CAAC;cAC7B,CAAE;cACFmJ,KAAK,EAAE;gBAAEoC,MAAM,EAAE;cAAU;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eAEd3kB,OAAA,CAAC1C,CAAC,CAAC2qB,QAAQ;YAAAvE,QAAA,gBACT1jB,OAAA;cAAA0jB,QAAA,EAAM;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjB3kB,OAAA;cAAA0jB,QAAA,EAAIvc,YAAY,CAACqN;YAAI;cAAAgQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEb3kB,OAAA,CAAC1C,CAAC,CAAC2qB,QAAQ;YAAAvE,QAAA,gBACT1jB,OAAA;cAAA0jB,QAAA,EAAM;YAAM;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnB3kB,OAAA;cAAA0jB,QAAA,EAAG;YAAgC;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGd3kB,OAAA,CAACH,YAAY;QAAA2kB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEX;EAAC,gBACR,CAAC;AAEP,CAAC;AAAA5e,GAAA,CAnjGKD,WAAW;EAAA,QAYEnJ,WAAW,EAGXwD,WAAW,EA+D+DrB,iBAAiB;AAAA;AAAAopB,IAAA,GA9ExGpiB,WAAW;AAqjGjB,eAAeA,WAAW;AAAC,IAAAhF,EAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAujB,IAAA;AAAAC,YAAA,CAAArnB,EAAA;AAAAqnB,YAAA,CAAAhnB,GAAA;AAAAgnB,YAAA,CAAA9mB,GAAA;AAAA8mB,YAAA,CAAA3mB,GAAA;AAAA2mB,YAAA,CAAAxmB,GAAA;AAAAwmB,YAAA,CAAAtmB,GAAA;AAAAsmB,YAAA,CAAAnmB,GAAA;AAAAmmB,YAAA,CAAAjmB,GAAA;AAAAimB,YAAA,CAAA9lB,GAAA;AAAA8lB,YAAA,CAAA3lB,GAAA;AAAA2lB,YAAA,CAAAxlB,GAAA;AAAAwlB,YAAA,CAAAtlB,IAAA;AAAAslB,YAAA,CAAAnlB,IAAA;AAAAmlB,YAAA,CAAAhlB,IAAA;AAAAglB,YAAA,CAAA9kB,IAAA;AAAA8kB,YAAA,CAAA3kB,IAAA;AAAA2kB,YAAA,CAAAzkB,IAAA;AAAAykB,YAAA,CAAAvkB,IAAA;AAAAukB,YAAA,CAAArkB,IAAA;AAAAqkB,YAAA,CAAAnkB,IAAA;AAAAmkB,YAAA,CAAAjkB,IAAA;AAAAikB,YAAA,CAAA/jB,IAAA;AAAA+jB,YAAA,CAAA7jB,IAAA;AAAA6jB,YAAA,CAAA3jB,IAAA;AAAA2jB,YAAA,CAAAxjB,IAAA;AAAAwjB,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}