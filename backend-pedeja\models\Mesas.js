const mongoose = require('mongoose');

const MesasSchema = new mongoose.Schema(
    {
        empresa_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Empresa',
            required: true,
        },
        status: String,
        name: String,
        number: Number,
        total: Number,
        total_payed: Number,
        qr_code: String,
        deletedAt: Date,
        pedidos: [
            {
                id_pedido: {
                    type: mongoose.Schema.Types.ObjectId,
                    ref: 'Pedidos',
                    index: true
                },
                id_garcom: {
                    type: mongoose.Schema.Types.ObjectId,
                    ref: 'User', // Assuming a 'Garcom' model exists
                },
                name_garcom: String,
                closed: {
                    type: Boolean,
                    default: false,
                },
                updatedAt: Date,
            },
        ],
    },
    { timestamps: true }
);

const Mesas = mongoose.model('Mesas', MesasSchema);

module.exports = Mesas;
