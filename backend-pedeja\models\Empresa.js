const mongoose = require('mongoose')

const EmpresaSchema = new mongoose.Schema({
id_empresa: Number,
createdBy: String,
cnpj: String,
name: String,
razao: String,
email: String,
cep: String,
estado: String,
municipio: String,
bairro: String,
logradouro: String,
address_number: String,
complemento: String,
latitude: String,
longitude: String,
telefone: String,
celular: String,
status_loja: String,
horario_funcionamento: Object,
timezone: String,
tempoBalcaoMin: String,
tempoBalcaoMax: String,
tempoEntregaMin: String,
tempoEntregaMax: String,
tipo_impressao: String,
id_grupo: Number,
type:String,
region_type_delivery: String,
status_printer: String,
entrega_disabled: Boolean,
retirada_disabled: Boolean,
raios_entrega: [
    {
      raio_entrega: Number,
      valor_entrega: Number,
    },
],
bairros_entrega: [
    {
      bairro_entrega: String,
      valor_entrega: Number,
    },
],
status_bot: Boolean,
call_atendente: Boolean,
whatsapp: {
  ativo: Boolean,
  id: String,
  name: String, // Nome normalizado da instância para operações de delete
  token: String,
  endpoint: String,
  instance_status: Number,
  baterry: Number,
  jid: String
},
customer_asaas: {
  type: Object,
  default: {}
},
caixa_aberto: {
  type: Boolean,
  default: false
},
caixa_aberto_id: {
  type: mongoose.Schema.Types.ObjectId,
  ref: 'Caixa',
  default: null
},
last_caixa_id: {
  type: mongoose.Schema.Types.ObjectId,
  ref: 'Caixa',
  default: null
},
fechamento_temporario: Boolean,
formas_pagamento: { // 🔹 Novo campo adicionado
  type: [String],
  enum: ["Cartão", "Pix", "Dinheiro"],
  default: []
},
importacao_finalizada: { type: Boolean, default: false },
status_importacao: {
  type: String,
  enum: ['idle', 'iniciando', 'em_andamento', 'finalizando', 'concluida', 'erro'],
  default: 'idle'
},
progresso_importacao: {
  em_andamento: { type: Boolean, default: false },
  porcentagem: { type: Number, default: 0 },
  etapa_atual: { type: String, default: '' },
  total_categorias: { type: Number, default: 0 },
  categorias_processadas: { type: Number, default: 0 },
  total_itens: { type: Number, default: 0 },
  itens_processados: { type: Number, default: 0 },
  total_adicionais: { type: Number, default: 0 },
  adicionais_processados: { type: Number, default: 0 },
  data_inicio: { type: Date, default: null },
  data_fim: { type: Date, default: null },
  tipo_importacao: { type: String, default: '' }, // 'ifood' ou 'anotaai'
  processo_id: { type: String, default: null }, // ID do processo para controle
  ultima_atualizacao: { type: Date, default: null }
},
last_reset_date: {
  type: Date,
  default: null
},
dias_para_reset: {
  type: Number,
  default: null
},
cancel_password: { type: String, select: false }, // Armazena a senha criptografada
has_cancel_password: { type: Boolean, default: false },
customResponses: [{
  id: {
    type: String,
    required: true
  },
  question: {
    type: String,
    required: true,
    maxlength: 100
  },
  response: {
    type: String,
    required: true,
    maxlength: 300
  },
  active: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}],
inativo:Boolean,
bloqueado:Boolean,
fila_impressao_sub: String,

// 🔥 NOVO: Campo para armazenar fila de atendimento humano
fila_atendimento: [{
  lead_id: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  nome: {
    type: String,
    required: true
  },
  celular: {
    type: String,
    required: true
  },
  mensagem: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['pendente', 'em_atendimento', 'finalizado', 'cancelado'],
    default: 'pendente'
  },
  atendido_por: {
    user_id: String,
    user_name: String
  },
  data_atendimento: {
    type: Date,
    default: null
  },
  data_finalizacao: {
    type: Date,
    default: null
  },
  observacoes: {
    type: String,
    default: ''
  }
}],

// Campo para armazenar progresso da configuração inicial
configuracao_inicial: {
  etapas_completas: {
    type: [Number],
    default: [1] // Etapa 1 (informações pessoais) sempre completa no início
  },
  etapa_atual: {
    type: Number,
    default: 2 // Começa na etapa 2 (endereço)
  },
  finalizada: {
    type: Boolean,
    default: false
  }
},

// Campo para armazenar solicitações de cardápio para análise da equipe
solicitacoes_cardapio: [{
  id_solicitacao: {
    type: mongoose.Schema.Types.ObjectId,
    default: () => new mongoose.Types.ObjectId()
  },
  link_cardapio: {
    type: String,
    default: ''
  },
  arquivo_pdf: {
    nome_arquivo: String,
    nome_arquivo_gcs: String,
    url_gcs: String,
    caminho_gcs: String,
    tamanho_arquivo: Number,
    // Campos antigos mantidos para compatibilidade
    caminho_arquivo: String
  },
  observacoes: {
    type: String,
    default: ''
  },
  data_solicitacao: {
    type: Date,
    default: Date.now
  },
  status_analise: {
    type: String,
    enum: ['pendente', 'em_andamento', 'concluido', 'rejeitado'],
    default: 'pendente'
  },
  data_conclusao: {
    type: Date,
    default: null
  },
  usuario_responsavel: {
    type: String,
    default: null
  },
  observacoes_equipe: {
    type: String,
    default: ''
  },
  solicitado_por: {
    user_id: String,
    user_name: String
  }
}],

createdAt: Date,
updatedAt: Date,
deletedAt: Date

})

const Empresa = mongoose.model('Empresa', EmpresaSchema)

module.exports = Empresa