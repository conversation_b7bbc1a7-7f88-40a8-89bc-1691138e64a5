# @aws-crypto/ie11-detection

Functions for interact with IE11 browsers Crypto. The IE11 `window.subtle` functions are unique.
This library is used to identify an IE11 `window` and then offering types for crypto functions.
For example see @aws-crypto/random-source-browser

## Usage

```
import {isMsWindow} from '@aws-crypto/ie11-detection'

if (isMsWindow(window)) {
  // use `window.subtle.mscrypto`
}

```

## Test

`npm test`
