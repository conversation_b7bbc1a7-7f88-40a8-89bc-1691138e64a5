{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\PEDEJA\\\\pedeja-front\\\\src\\\\components\\\\UnreadBadge\\\\index.jsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BadgeContainer = styled.div`\n  position: relative;\n  display: inline-block;\n`;\n_c = BadgeContainer;\nconst Badge = styled.div`\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  background-color: #25d366;\n  color: white;\n  border-radius: 50%;\n  min-width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n  z-index: 1;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  \n  /* Para números maiores que 99, mostrar 99+ */\n  ${props => props.count > 99 && `\n    font-size: 10px;\n    min-width: 24px;\n    height: 20px;\n  `}\n  \n  @media (max-width: 768px) {\n    top: -6px;\n    right: -6px;\n    min-width: 18px;\n    height: 18px;\n    font-size: 11px;\n    \n    ${props => props.count > 99 && `\n      font-size: 9px;\n      min-width: 22px;\n      height: 18px;\n    `}\n  }\n`;\n_c2 = Badge;\nconst UnreadBadge = ({\n  count,\n  children\n}) => {\n  if (!count || count === 0) {\n    return children;\n  }\n  const displayCount = count > 99 ? '99+' : count.toString();\n  return /*#__PURE__*/_jsxDEV(BadgeContainer, {\n    children: [children, /*#__PURE__*/_jsxDEV(Badge, {\n      count: count,\n      children: displayCount\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_c3 = UnreadBadge;\nexport default UnreadBadge;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"BadgeContainer\");\n$RefreshReg$(_c2, \"Badge\");\n$RefreshReg$(_c3, \"UnreadBadge\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "BadgeContainer", "div", "_c", "Badge", "props", "count", "_c2", "UnreadBadge", "children", "displayCount", "toString", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/PEDEJA/pedeja-front/src/components/UnreadBadge/index.jsx"], "sourcesContent": ["import React from 'react';\r\nimport styled from 'styled-components';\r\n\r\nconst BadgeContainer = styled.div`\r\n  position: relative;\r\n  display: inline-block;\r\n`;\r\n\r\nconst Badge = styled.div`\r\n  position: absolute;\r\n  top: -8px;\r\n  right: -8px;\r\n  background-color: #25d366;\r\n  color: white;\r\n  border-radius: 50%;\r\n  min-width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n  z-index: 1;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  \r\n  /* Para números maiores que 99, mostrar 99+ */\r\n  ${props => props.count > 99 && `\r\n    font-size: 10px;\r\n    min-width: 24px;\r\n    height: 20px;\r\n  `}\r\n  \r\n  @media (max-width: 768px) {\r\n    top: -6px;\r\n    right: -6px;\r\n    min-width: 18px;\r\n    height: 18px;\r\n    font-size: 11px;\r\n    \r\n    ${props => props.count > 99 && `\r\n      font-size: 9px;\r\n      min-width: 22px;\r\n      height: 18px;\r\n    `}\r\n  }\r\n`;\r\n\r\nconst UnreadBadge = ({ count, children }) => {\r\n  if (!count || count === 0) {\r\n    return children;\r\n  }\r\n\r\n  const displayCount = count > 99 ? '99+' : count.toString();\r\n\r\n  return (\r\n    <BadgeContainer>\r\n      {children}\r\n      <Badge count={count}>\r\n        {displayCount}\r\n      </Badge>\r\n    </BadgeContainer>\r\n  );\r\n};\r\n\r\nexport default UnreadBadge;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,cAAc,GAAGH,MAAM,CAACI,GAAG;AACjC;AACA;AACA,CAAC;AAACC,EAAA,GAHIF,cAAc;AAKpB,MAAMG,KAAK,GAAGN,MAAM,CAACI,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,EAAE,IAAI;AACjC;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,EAAE,IAAI;AACnC;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AAACC,GAAA,GArCIH,KAAK;AAuCX,MAAMI,WAAW,GAAGA,CAAC;EAAEF,KAAK;EAAEG;AAAS,CAAC,KAAK;EAC3C,IAAI,CAACH,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE;IACzB,OAAOG,QAAQ;EACjB;EAEA,MAAMC,YAAY,GAAGJ,KAAK,GAAG,EAAE,GAAG,KAAK,GAAGA,KAAK,CAACK,QAAQ,CAAC,CAAC;EAE1D,oBACEX,OAAA,CAACC,cAAc;IAAAQ,QAAA,GACZA,QAAQ,eACTT,OAAA,CAACI,KAAK;MAACE,KAAK,EAAEA,KAAM;MAAAG,QAAA,EACjBC;IAAY;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAErB,CAAC;AAACC,GAAA,GAfIR,WAAW;AAiBjB,eAAeA,WAAW;AAAC,IAAAL,EAAA,EAAAI,GAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}