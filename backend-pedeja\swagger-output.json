{"openapi": "3.0.0", "info": {"version": "1.0.0", "title": "PedeJa API", "description": "PedeJa.chat Documentation"}, "host": "", "paths": {"/webhook": {"post": {"description": "", "parameters": [{"name": "appname", "in": "header", "schema": {"type": "string"}}, {"name": "user-agent", "in": "header", "schema": {"type": "string"}}, {"name": "content-type", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"instance": {"example": "any"}, "instanceId": {"example": "any"}, "data": {"example": "any"}, "owner": {"example": "any"}, "event": {"example": "any"}, "type": {"example": "any"}, "messages": {"example": "any"}, "state": {"example": "any"}, "qrcode": {"example": "any"}}}}}}}}, "/scanQR/{empresaID}": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/instanceStatus/{empresaID}": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/removeSession/{empresaID}": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/qrcode/{empresaID}": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/qrcode/renew/{empresaID}": {"post": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/connectionStatus/{empresaID}": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/sendTextMessage/{empresaID}": {"post": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"number": {"example": "any"}, "message": {"example": "any"}}}}}}}}, "/chats/{empresaID}": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "pageSize", "in": "query", "schema": {"type": "string"}}, {"name": "query", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/chats/{empresaID}/messages/{leadID}": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "leadID", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "pageSize", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/chats/{empresaID}/messages/{leadID}/simple": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "leadID", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "string"}}, {"name": "pageSize", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/chats/{empresaID}/messages/{leadID}/mark-as-read": {"put": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "leadID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/chats/{empresaID}/unread-count": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/get-profile-picture/{leadChannelID}": {"post": {"description": "", "parameters": [{"name": "leadChannelID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/get-my-profile-picture/{empresaID}": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error"}}}}, "/wp-jobs": {"post": {"description": "", "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"example": "any"}, "queue": {"example": "any"}}}}}}}}, "/bot-llm": {"post": {"description": "", "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"company_id": {"example": "any"}, "message": {"example": "any"}, "lead_id": {"example": "any"}}}}}}}}, "/webhook/configure/{empresaID}": {"post": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/refresh-contact-info/{leadChannelID}": {"post": {"description": "", "parameters": [{"name": "leadChannelID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/refresh-profile-pictures/{empresaID}": {"post": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}}}}, "/webhook/status/{empresaID}": {"get": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/webhook/messages-upsert": {"post": {"description": "", "parameters": [{"name": "appname", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"instance": {"example": "any"}, "instanceId": {"example": "any"}, "data": {"example": "any"}, "owner": {"example": "any"}, "messages": {"example": "any"}, "message": {"example": "any"}}}}}}}}, "/webhook/messages-update": {"post": {"description": "", "parameters": [{"name": "appname", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"instance": {"example": "any"}, "instanceId": {"example": "any"}, "data": {"example": "any"}, "owner": {"example": "any"}, "messages": {"example": "any"}}}}}}}}, "/webhook/connection-update": {"post": {"description": "", "parameters": [{"name": "appname", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"instance": {"example": "any"}, "instanceId": {"example": "any"}, "data": {"example": "any"}, "owner": {"example": "any"}}}}}}}}, "/webhook/send-message": {"post": {"description": "", "parameters": [{"name": "appname", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"instance": {"example": "any"}, "instanceId": {"example": "any"}, "data": {"example": "any"}, "owner": {"example": "any"}, "messages": {"example": "any"}}}}}}}}, "/webhook/qrcode-updated": {"post": {"description": "", "parameters": [{"name": "appname", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"instance": {"example": "any"}, "instanceId": {"example": "any"}, "data": {"example": "any"}, "owner": {"example": "any"}}}}}}}}, "/fetch-jid/{empresaID}": {"post": {"description": "", "parameters": [{"name": "empresaID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/fila-atendimento/{empresaId}": {"get": {"description": "", "parameters": [{"name": "empresaId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/fila-atendimento/{empresaId}/iniciar/{atendimentoId}": {"post": {"description": "", "parameters": [{"name": "empresaId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "atendimentoId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"example": "any"}, "user_name": {"example": "any"}}}}}}}}, "/fila-atendimento/{empresaId}/finalizar/{atendimentoId}": {"post": {"description": "", "parameters": [{"name": "empresaId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "atendimentoId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"observacoes": {"example": "any"}}}}}}}}, "/fila-atendimento/{empresaId}/cancelar/{atendimentoId}": {"delete": {"description": "", "parameters": [{"name": "empresaId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "atendimentoId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"motivo": {"example": "any"}}}}}}}}, "/fila-atendimento/{empresaId}/limpar-finalizados": {"post": {"description": "", "parameters": [{"name": "empresaId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/api/v1/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "<PERSON><PERSON> de usuário", "description": "<PERSON><PERSON> de usuário", "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error"}}, "security": [{}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"example": "any"}, "password": {"example": "any"}}}}}}}}, "/api/v1/auth/garcom/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "<PERSON>gin de garçom", "description": "<PERSON>gin de garçom", "responses": {"401": {"description": "Unauthorized"}}, "security": [{}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"example": "any"}, "password": {"example": "any"}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer"}}}, "security": [{"bearerAuth": []}]}