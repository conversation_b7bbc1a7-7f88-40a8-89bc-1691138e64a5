class TransferMesaUseCase {
    constructor(mesaDAO){
        this.mesaDAO = mesaDAO
    }

    async execute({ current_mesa_id, next_mesa_id }) {
        try {

            const currentMesa = await this.mesaDAO.findByIdSimple({ mesa_id: current_mesa_id });

            if(!currentMesa){
                return {
                    status: false,
                    message: "Mesa atual não encontrada"
                }
            }

            const nextMesa = await this.mesaDAO.findByIdSimple({ mesa_id: next_mesa_id });

            if(!nextMesa){
                return {
                    status: false,
                    message: "Mesa destino não encontrada"
                }
            }

            if(nextMesa.status === "occupied"){
                return {
                    status: false,
                    message: "Mesa atualmente ocupada, desocupe ela para prosseguir com a transferência."
                }
            }
            
            nextMesa.status = "occupied";
            nextMesa.pedidos = currentMesa.pedidos;
            nextMesa.total = currentMesa.total || 0;
            nextMesa.total_payed = currentMesa.total_payed || 0;

            await nextMesa.save();
            await currentMesa.save();


            await this.mesaDAO.resetMesa({ mesa_id: current_mesa_id });

            return {
                status: true,
                message: "Mesa transferida com sucesso."
            }


        } catch(error){
            console.error(error);
            return {
                status: false,
                msg: "Error ao registrar pedido"
            }
        }
    }
}

module.exports = TransferMesaUseCase;