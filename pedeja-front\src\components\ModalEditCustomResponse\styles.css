.modal-edit-custom-response {
    width: 100%;
    max-width: 600px;
    background: white;
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #318CD5 0%, #4facfe 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.modal-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.modal-header p {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.5;
    margin: 0;
}

.modal-form {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: all 0.3s ease;
    background: #f8f9fa;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.form-textarea:focus {
    outline: none;
    border-color: #318CD5;
    background: white;
    box-shadow: 0 0 0 3px rgba(49, 140, 213, 0.1);
}

.question-field {
    min-height: 80px;
}

/* Container para o input com prefixo */
.question-input-container {
    position: relative;
    display: flex;
    align-items: flex-start;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background-color: #f8f9fa;
    padding: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.question-input-container:focus-within {
    border-color: #318CD5;
    background: white;
    box-shadow: 0 0 0 3px rgba(49, 140, 213, 0.1);
}

/* Prefixo fixo */
.question-prefix {
    background-color: #e9ecef;
    color: #495057;
    font-weight: 600;
    padding: 1rem 0.75rem;
    border-right: 1px solid #dee2e6;
    white-space: nowrap;
    font-size: 1rem;
    line-height: 1.5;
    display: flex;
    align-items: center;
    min-height: 100px;
    box-sizing: border-box;
    font-family: inherit;
}

/* Campo de input sem prefixo visível */
.question-field-with-prefix {
    flex: 1;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    padding: 1rem !important;
    font-size: 1rem;
    line-height: 1.5;
    resize: vertical;
    min-height: 80px;
    background-color: transparent;
    font-family: inherit;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.question-field-with-prefix:focus {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

.response-field {
    min-height: 120px;
    position: relative;
}

.char-counter {
    text-align: right;
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-top: 0.25rem;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.error-message::before {
    content: "⚠️";
    font-size: 0.8rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.btn-cancel,
.btn-submit {
    padding: 0rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-cancel {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn-cancel:hover:not(:disabled) {
    background: #e9ecef;
    color: #495057;
}

.btn-submit {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-submit:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.btn-submit:disabled,
.btn-cancel:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Responsividade */
@media (max-width: 768px) {
    .modal-edit-custom-response {
        max-width: 95vw;
        margin: 1rem;
    }
    
    .modal-header {
        padding: 1.5rem;
    }
    
    .modal-header h2 {
        font-size: 1.5rem;
    }
    
    .modal-form {
        padding: 1.5rem;
    }
    
    .form-actions {
        flex-direction: column-reverse;
    }
    
    .btn-cancel,
    .btn-submit {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .modal-header {
        padding: 1rem;
    }
    
    .modal-header h2 {
        font-size: 1.3rem;
    }
    
    .modal-header p {
        font-size: 0.9rem;
    }
    
    .modal-form {
        padding: 1rem;
    }
}
