class MesasDAO {
    constructor(mesasModel){
        this.model = mesasModel;
    }

    async getMesas(empresa_id){
        try {
            const mesas = await this.model.find({
                empresa_id: empresa_id
            })
            .populate({
                path: 'pedidos.id_pedido', // Populates the `id_pedido` reference from `Pedidos`
                model: 'Pedidos', // Optional if `Pedidos` is defined in `ref`
            })
            .populate({
                path: 'pedidos.id_garcom', // Populates the `id_garcom` reference from `User`
                model: 'User', // Optional if `User` is defined in `ref`
                select: 'name email', // Specify fields to include from the User collection
            })
            .exec();

        return mesas;
            return mesas;
        } catch(error){
            console.log('error', error);
        }
    }

    async createMesas({ qr_code, empresa_id, name, status, number }){
        try {
            const mesa = new this.model();
            mesa.qr_code = qr_code
            mesa.empresa_id = empresa_id
            mesa.status = status
            mesa.name = name,
            mesa.number = number

            console.log('mesa', mesa);
            await mesa.save()

            return mesa;
        } catch(error){
            console.log('error', error);
        }
    }

    async findMesasByNumber({ number }){
        try {
            const mesa = await this.model.findOne({ number});
            return mesa;
        } catch(error){
            console.log('error', error);
        }
    }

    async findLastNumber({ empresa_id }){
        try {
            const mesa = await this.model.findOne({ empresa_id }).sort({ number: -1 });
            return mesa;
        } catch(error){
            console.log('error', error);
        }
    }

    async updateMesa({ mesa_id, status, pedidos, total }){
        try {

            const mesa = await this.model.update({ _id: mesa_id }, { status, pedidos, total, updated_at: new Date() });
            return mesa;
        } catch(error){
            console.log('error', error);
            return {
                status: false,
                message: "Erro ao atualizar status da mesa"
            }
        }
    }

    async getWithPedidos({ id_empresa, mesaId }){
        try {
            const mesas = await this.model.findOne({ empresa_id: id_empresa, _id: mesaId }).populate('pedidos');
            return mesas;
        } catch(error){
            console.log('error', error);
        }
    }

    async findByIdSimple({ mesa_id }){
        try {
            const mesa = await this.model.findOne({ _id: mesa_id });
            return mesa;
        } catch(error){
            console.log('error', error);
        }
    }

    async findMesaById({ mesa_id }){
        try {           
            const mesa = await this.model.findOne({ _id: mesa_id })
            .populate({
                path: 'pedidos.id_pedido', // Populates the `id_pedido` reference from `Pedidos`
                model: 'Pedidos', // Optional if `Pedidos` is defined in `ref`
            })
            .populate({
                path: 'pedidos.id_garcom', // Populates the `id_garcom` reference from `User`
                model: 'User', // Optional if `User` is defined in `ref`
                select: 'name email', // Specify fields to include from the User collection
            })
            .exec();
            return mesa;
        } catch(error){
            console.log('error', error);
        }
    }

    async resetMesa({ mesa_id }){
        try {
            const mesa = await this.model.findOneAndUpdate({ _id: mesa_id }, { pedidos: [], total: 0, total_payed: 0, status: 'free', updated_at: new Date() }, { new: true });
            console.log('mesa updated', mesa);

            return mesa;
        } catch(error){
            console.log('error', error);
            return {
                status: false,
                message: "Erro ao resetar mesa"
            }
        }
    }

    async deleteMesa({ mesa_id }){
        try {
            const mesa = await this.model.deleteOne({ _id: mesa_id });
            return mesa;
        } catch(error){
            console.log('error', error);
            return {
                status: false,
                message: "Erro ao deletar mesa"
            }
        }
    }
}

module.exports = MesasDAO;