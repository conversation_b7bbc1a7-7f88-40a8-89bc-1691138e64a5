

class GetMesasUseCase {

    constructor(mesasDAO, empresaDAO){
        this.mesasDAO = mesasDAO
        this.empresaDAO = empresaDAO
    }

    async execute(vinculo_empresa) {
        try {

            console.log('empresa_id', vinculo_empresa);
            const empresa = await this.empresaDAO.getEmpresaById(vinculo_empresa);
            //const empresa_id = await this.empresaDAO.getEmpresaById(vinculo_empresa);
            const mesas = await this.mesasDAO.getMesas(empresa._id);
            return {
                status: true,
                data: mesas
            }
        } catch(error){
            console.error(error);
            return {
                status: false,
                message: "Error on get mesas"
            }
        }

    }
}

module.exports = GetMesasUseCase;